// 获取首页基础信息
// 定制化图形*1、 0
// 定制化图形*2、 1
// 饼图-空心环形图、2
// 柱状图-基础柱状图、3
// 柱状图-条形图-行、4
// 折线图-基础平滑折线图、5
// 折线图-基础面积图、6
// 雷达图、7
// 定制化Map、8
import { request, vsocPath } from '../../util/request'

export const getVehiclePlatformList = function (data, token) {
  return request({
    url: `${vsocPath}/vehicleType/vehicleTypes`,
    method: 'post',
    loading: true,
    data,
    headers: {
      Authorization: token,
    },
  })
}

// 分享画布
export const generateLink = function (data) {
  return request({
    url: `${vsocPath}/canvas/generateLink`,
    method: 'post',
    data,
  })
}
// 撤销画布
export const revokeLink = function (data) {
  return request({
    url: `${vsocPath}/canvas/revokeLink`,
    method: 'post',
    data,
  })
}
// 查询所有统计配置
export const getlayoutItems = function (data) {
  return request({
    url: `${vsocPath}/layoutItem/layoutItems`,
    method: 'post',
    data,
  })
}

// 更改统计配置
export const updateConfig = function (data) {
  return request({
    url: `${vsocPath}/layoutItem/updateConfig`,
    method: 'post',
    data,
  })
}

//查询统计指标关联的画布
export const queryDashboardsByItemKey = function (data) {
  return request({
    url: `${vsocPath}/layoutItem/queryDashboardsByItemKey`,
    method: 'post',
    data,
  })
}

//更新关联的画布实例
export const updateDashboardsByItemKey = function (data) {
  return request({
    url: `${vsocPath}/layoutItem/updateDashboardsByItemKey`,
    method: 'post',
    data,
  })
}

//获取画布
export const getCanvas = function (data) {
  return request({
    url: `${vsocPath}/canvas/canvases`,
    method: 'post',
    data,
    loading: true,
  })
}

//新增画布
export const addCanvas = function (data) {
  return request({
    url: `${vsocPath}/canvas/addCanvas`,
    method: 'post',
    data,
  })
}

//编辑画布
export const editCanvas = function (data) {
  return request({
    url: `${vsocPath}/canvas/updateCanvas`,
    method: 'post',
    data,
  })
}

//删除画布
export const delCanvas = function (data) {
  return request({
    url: `${vsocPath}/canvas/deleteCanvas`,
    method: 'post',
    data,
  })
}

//获取所有有效统计指标
export const validLayoutItems = function (data, token) {
  return request({
    url: `${vsocPath}/layoutItem/validLayoutItems`,
    method: 'post',
    data,
    headers: {
      Authorization: token,
    },
  })
}

//获取画布详情
export const canvasDetail = function (data, token) {
  return request({
    url: `${vsocPath}/canvas/canvasDetail`,
    method: 'post',
    data,
    headers: {
      Authorization: token,
    },
  })
}

//查询统计指标的数据详情接口
export const layoutItemData = function (data, token, loading) {
  return request({
    url: `${vsocPath}/canvas/layoutItemData`,
    method: 'post',
    data,
    headers: {
      Authorization: token,
    },
    loading: loading === 'false' ? false : true,
  })
}

//新增统计指标的数据详情接口
export const addLayoutData = function (data) {
  return request({
    url: `${vsocPath}/canvas/addLayoutData`,
    method: 'post',
    data,
  })
}

export const getCloudList = function (params) {
  return {
    code: 200,
    data: [
      {
        contentSourceType: '0',
        contentSourceTypeName: 'Static Data',
        createDate: '2023-06-30 14:03:02',
        createUser: 'lianglin',
        id: 1,
        itemEnName: 'Attacks in 7 Days',
        itemKey: 'C001',
        itemName: '安全态势',
        chartValue: '0',
        chartName: '定制化图形*1',
        chartList: [{ value: '0', text: '定制化图形*1' }],
        startWidth: 4,
        startHeight: 7,
        minWidth: 3,
        minHidth: 3,
        isBorder: '1',
        updateDate: '2023-07-19 17:05:04',
        updateUser: 'admin',
        value: {
          posture: '1',
          time: '2023-12-22 14:06:00',
        },
        valueExample: '0',
        interfaceUrl: '',
        isActive: '0',
      },
      {
        contentSourceType: '0',
        contentSourceTypeName: 'Static Data',
        createDate: '2023-06-30 14:03:02',
        createUser: 'lianglin',
        id: 2,
        itemEnName: 'Attacks in 7 Days',
        itemKey: 'C002',
        itemName: '车辆总数',
        chartValue: '1',
        chartName: '定制化图形*2',
        chartList: [{ value: '1', text: '定制化图形*2' }],
        startWidth: 3,
        startHeight: 4,
        minWidth: 3,
        minHidth: 4,
        isBorder: '0',
        updateDate: '2023-07-19 17:05:04',
        updateUser: 'admin',
        value: 500,
        valueExample: 50,
        interfaceUrl: '/detector/detectors',
        isActive: '0',
      },
      {
        contentSourceType: '0',
        contentSourceTypeName: 'Static Data',
        createDate: '2023-06-30 14:03:02',
        createUser: 'lianglin',
        id: 3,
        itemEnName: 'Attacks in 7 Days',
        itemKey: 'C003',
        itemName: '车型事件占比分布',
        chartValue: '2',
        chartName: '饼图-空心环形图',
        chartList: [{ value: '2', text: '饼图-空心环形图' }],
        startWidth: 3,
        startHeight: 4,
        minWidth: 3,
        minHidth: 4,
        isBorder: '0',
        updateDate: '2023-07-19 17:05:04',
        updateUser: 'admin',
        value: {
          total: 10,
          list: [
            { name: '未知车型', value: 5, percentage: '50%' },
            { name: 'Beetle', value: 5, percentage: '50%' },
          ],
        },

        valueExample: 50,
        interfaceUrl: '/detector/detectors',
        isActive: '0',
      },
      {
        contentSourceType: '0',
        contentSourceTypeName: 'Static Data',
        createDate: '2023-06-30 14:03:02',
        createUser: 'lianglin',
        id: 4,
        itemEnName: 'Attacks in 7 Days',
        itemKey: 'C003',
        itemName: '车型事件占比分布',
        chartValue: '3',
        chartName: '柱状图-基础柱状图',
        chartList: [{ value: '3', text: '柱状图-基础柱状图' }],
        startWidth: 3,
        startHeight: 4,
        minWidth: 3,
        minHidth: 4,
        isBorder: '0',
        updateDate: '2023-07-19 17:05:04',
        updateUser: 'admin',
        value: {
          total: 20,
          list: [
            {
              arrays: [
                {
                  number: 5,
                  time: '2023-12-24',
                },
                {
                  number: 5,
                  time: '2023-12-25',
                },
              ],
              name: '未知车型',
              level: '0',
            },
            {
              arrays: [
                {
                  number: 5,
                  time: '2023-12-24',
                },
                {
                  number: 5,
                  time: '2023-12-25',
                },
              ],
              name: 'Beetle',
              level: '1',
            },
          ],
        },
        valueExample: 50,
        interfaceUrl: '/detector/detectors',
        isActive: '0',
      },
      {
        contentSourceType: '0',
        contentSourceTypeName: 'Static Data',
        createDate: '2023-06-30 14:03:02',
        createUser: 'lianglin',
        id: 5,
        itemEnName: 'Attacks in 7 Days',
        itemKey: 'C003',
        itemName: '车型事件占比分布',
        chartValue: '4',
        chartName: '柱状图-条形图-行',
        chartList: [{ value: '4', text: '柱状图-条形图-行' }],
        startWidth: 3,
        startHeight: 4,
        minWidth: 3,
        minHidth: 4,
        isBorder: '0',
        updateDate: '2023-07-19 17:05:04',
        updateUser: 'admin',
        value: {
          total: 20,
          list: [
            {
              name: '未知车型',
              value: 5,
              percentage: '50%',
            },
            {
              name: 'Beetle',
              value: 1,
              percentage: '50%',
            },
          ],
        },
        valueExample: 50,
        interfaceUrl: '/detector/detectors',
        isActive: '0',
      },
      {
        contentSourceType: '0',
        contentSourceTypeName: 'Static Data',
        createDate: '2023-06-30 14:03:02',
        createUser: 'lianglin',
        id: 6,
        itemEnName: 'Attacks in 7 Days',
        itemKey: 'C003',
        itemName: '风险告警Top10',
        chartValue: '5',
        chartName: '折线图-基础平滑折线图',
        chartList: [{ value: '5', text: '折线图-基础平滑折线图' }],
        startWidth: 3,
        startHeight: 4,
        minWidth: 3,
        minHidth: 4,
        isBorder: '0',
        updateDate: '2023-07-19 17:05:04',
        updateUser: 'admin',
        value: {
          total: 20,
          list: [
            {
              arrays: [
                {
                  number: 10,
                  time: '2023-12-24',
                },
                {
                  number: 5,
                  time: '2023-12-25',
                },
              ],
              name: '未知车型',
              level: '0',
            },
            {
              arrays: [
                {
                  number: 5,
                  time: '2023-12-24',
                },
                {
                  number: 5,
                  time: '2023-12-25',
                },
              ],
              name: 'Beetle',
              level: '1',
            },
          ],
        },
        valueExample: 50,
        interfaceUrl: '/detector/detectors',
        isActive: '0',
      },
      {
        contentSourceType: '0',
        contentSourceTypeName: 'Static Data',
        createDate: '2023-06-30 14:03:02',
        createUser: 'lianglin',
        id: 7,
        itemEnName: 'Attacks in 7 Days',
        itemKey: 'C003',
        itemName: '风险告警Top10',
        chartValue: '6',
        chartName: '折线图-基础面积图',
        chartList: [{ value: '6', text: '折线图-基础面积图' }],
        startWidth: 3,
        startHeight: 4,
        minWidth: 3,
        minHidth: 4,
        isBorder: '0',
        updateDate: '2023-07-19 17:05:04',
        updateUser: 'admin',
        value: {
          total: 20,
          list: [
            {
              arrays: [
                {
                  number: 10,
                  time: '2023-12-24',
                },
                {
                  number: 5,
                  time: '2023-12-25',
                },
              ],
              name: '未知车型',
              level: '0',
              color: '#F0DA4C',
            },
            {
              arrays: [
                {
                  number: 5,
                  time: '2023-12-24',
                },
                {
                  number: 5,
                  time: '2023-12-25',
                },
              ],
              name: 'Beetle',
              level: '1',
              color: '#32FDB8',
            },
          ],
        },
        valueExample: 50,
        interfaceUrl: '/detector/detectors',
        isActive: '0',
      },
      {
        contentSourceType: '0',
        contentSourceTypeName: 'Static Data',
        createDate: '2023-06-30 14:03:02',
        createUser: 'lianglin',
        id: 8,
        itemEnName: 'Attacks in 7 Days',
        itemKey: 'C003',
        itemName: '风险告警Top10',
        chartValue: '7',
        chartName: '雷达图',
        chartList: [{ value: '7', text: '雷达图' }],
        startWidth: 3,
        startHeight: 4,
        minWidth: 3,
        minHidth: 4,
        isBorder: '0',
        updateDate: '2023-07-19 17:05:04',
        updateUser: 'admin',
        value: {
          total: 20,
          list: [
            {
              name: '疑似远程入侵',
              percent: 46.7,
              count: 336,
              color: '#533DF1',
            },
            {
              name: '入口流量异常',
              percent: 43.02,
              count: 248,
              color: '#40CD6E',
            },
            {
              name: '未知用户',
              percent: 49.88,
              count: 178,
              color: '#FBD82C',
            },
            {
              name: '未知进程',
              percent: 36.75,
              count: 89,
              color: '#44E2FE',
            },
            {
              name: '未知端口',
              percent: 36.14,
              count: 16,
              color: '#1B84FF',
            },
            {
              name: '特征值异常',
              percent: 36.14,
              count: 16,
              color: '#1B84FF',
            },
          ],
        },
        valueExample: 50,
        interfaceUrl: '/detector/detectors',
        isActive: '0',
      },
      {
        contentSourceType: '0',
        contentSourceTypeName: 'Static Data',
        createDate: '2023-06-30 14:03:02',
        createUser: 'lianglin',
        id: 9,
        itemEnName: 'Attacks in 7 Days',
        itemKey: 'C003',
        itemName: '告警事件位置分布',
        chartValue: '8',
        chartName: '定制化Map',
        chartList: [{ value: '8', text: '定制化Map' }],
        startWidth: 6,
        startHeight: 6,
        minWidth: 3,
        minHidth: 4,
        isBorder: '1',
        updateDate: '2023-07-19 17:05:04',
        updateUser: 'admin',
        value: [
          [
            {
              ip: '**********',
              name: 'China,ChangSha',
              location: [112.936271, 28.235399],
            },
            {
              ip: '**********',
              name: 'China,ChangSha',
              location: [120.936271, 25.235399],
            },
            {
              ip: '**********',
              name: 'Albania',
            },
          ],
        ],
        valueExample: 50,
        interfaceUrl: '/detector/detectors',
        isActive: '0',
      },
    ],
  }
}

export const getGridlist = function (params) {
  return {
    code: 200,
    data: [
      {
        id: 1,
        name: '测试',
        title: '车辆安全监测与运营平台',
        titleEnName: 'Vehicle safety monitoring and operation platform',
        backgroudType: '1',
      },
      {
        id: 2,
        name: 'test',
        title: '测试',
        titleEnName: 'Test',
        backgroudType: '2',
      },
    ],
  }
}

export const getGridInfo = function (params) {
  let info = {}
  if (params.id === '1') {
    info = {
      id: 1, //画布id
      data: [
        {
          x: 0,
          y: 0,
          w: 3,
          h: 4,
          i: 2,
          minW: 3,
          minH: 4,
          type: '1',
          itemName: '车辆总数',
          value: 500,
          isBorder: '0',
          moved: false,
        },
        {
          x: 0,
          y: 8,
          w: 3,
          h: 4,
          i: 3,
          minW: 3,
          minH: 4,
          type: '2',
          itemName: '车型事件占比分布',
          value: {
            total: 10,
            list: [
              { name: '未知车型', value: 5, percentage: '50%' },
              { name: 'Beetle', value: 5, percentage: '50%' },
            ],
          },
          isBorder: '0',
          moved: false,
        },
        {
          x: 3,
          y: 8,
          w: 6,
          h: 4,
          i: 4,
          minW: 3,
          minH: 4,
          type: '3',
          itemName: '车型事件占比分布',
          value: {
            total: 20,
            list: [
              {
                arrays: [
                  { number: 5, time: '2023-12-24' },
                  { number: 5, time: '2023-12-25' },
                ],
                name: '未知车型',
                level: '0',
              },
              {
                arrays: [
                  { number: 5, time: '2023-12-24' },
                  { number: 5, time: '2023-12-25' },
                ],
                name: 'Beetle',
                level: '1',
              },
            ],
          },
          isBorder: '0',
          moved: false,
        },
        {
          x: 9,
          y: 0,
          w: 3,
          h: 4,
          i: 5,
          minW: 3,
          minH: 4,
          type: '4',
          itemName: '车型事件占比分布',
          value: {
            total: 20,
            list: [
              { name: '未知车型', value: 5, percentage: '50%' },
              { name: 'Beetle', value: 1, percentage: '50%' },
            ],
          },
          isBorder: '0',
          moved: false,
        },
        {
          x: 0,
          y: 4,
          w: 3,
          h: 4,
          i: 6,
          minW: 3,
          minH: 4,
          type: '5',
          itemName: '风险告警Top10',
          value: {
            total: 20,
            list: [
              {
                arrays: [
                  { number: 10, time: '2023-12-24' },
                  { number: 5, time: '2023-12-25' },
                ],
                name: '未知车型',
                level: '0',
              },
              {
                arrays: [
                  { number: 5, time: '2023-12-24' },
                  { number: 5, time: '2023-12-25' },
                ],
                name: 'Beetle',
                level: '1',
              },
            ],
          },
          isBorder: '0',
          moved: false,
        },
        {
          x: 9,
          y: 8,
          w: 3,
          h: 4,
          i: 7,
          minW: 3,
          minH: 4,
          type: '6',
          itemName: '风险告警Top10',
          value: {
            total: 20,
            list: [
              {
                arrays: [
                  { number: 10, time: '2023-12-24' },
                  { number: 5, time: '2023-12-25' },
                ],
                name: '未知车型',
                level: '0',
                color: '#F0DA4C',
              },
              {
                arrays: [
                  { number: 5, time: '2023-12-24' },
                  { number: 5, time: '2023-12-25' },
                ],
                name: 'Beetle',
                level: '1',
                color: '#32FDB8',
              },
            ],
          },
          isBorder: '0',
          moved: false,
        },
        {
          x: 9,
          y: 4,
          w: 3,
          h: 4,
          i: 8,
          minW: 3,
          minH: 4,
          type: '7',
          itemName: '风险告警Top10',
          value: {
            total: 20,
            list: [
              {
                name: '疑似远程入侵',
                percent: 46.7,
                count: 336,
                color: '#533DF1',
              },
              {
                name: '入口流量异常',
                percent: 43.02,
                count: 248,
                color: '#40CD6E',
              },
              {
                name: '未知用户',
                percent: 49.88,
                count: 178,
                color: '#FBD82C',
              },
              {
                name: '未知进程',
                percent: 36.75,
                count: 89,
                color: '#44E2FE',
              },
              {
                name: '未知端口',
                percent: 36.14,
                count: 16,
                color: '#1B84FF',
              },
              {
                name: '特征值异常',
                percent: 36.14,
                count: 16,
                color: '#1B84FF',
              },
            ],
          },
          isBorder: '0',
          moved: false,
        },
        {
          x: 3,
          y: 0,
          w: 6,
          h: 8,
          i: 9,
          minW: 3,
          minH: 4,
          type: '8',
          itemName: '告警事件位置分布',
          value: [
            [
              {
                ip: '**********',
                name: 'China,ChangSha',
                location: [112.936271, 28.235399],
              },
              {
                ip: '**********',
                name: 'China,ChangSha',
                location: [120.936271, 25.235399],
              },
              { ip: '**********', name: 'Albania' },
            ],
          ],
          isBorder: '1',
          moved: false,
        },
      ], //画布内每个统计指标的数据
      title: '车辆安全监测与运营平台', //画布的标题
      titleEnName: '', //画布的英文标题
      type: '1', //画布的背景
    }
  } else {
    info = {
      id: 2, //画布id
      data: [
        {
          x: 0,
          y: 0,
          w: 3,
          h: 4,
          i: 2,
          minW: 3,
          minH: 4,
          type: '1',
          itemName: '车辆总数',
          value: 500,
          isBorder: '0',
          moved: false,
        },
      ], //画布内每个统计指标的数据
      title: '测试', //画布的标题
      titleEnName: '', //画布的英文标题
      type: '2', //画布的背景
    }
  }
  return {
    code: 200,
    info: info,
  }
}

export const getGardInfo = function (params) {
  let data = []
  if (params.id === '1') {
    data = [
      {
        chartId: 2,
        data: {
          total: 500,
          percentage: '50%',
        },
      },
      {
        chartId: 3,
        data: {
          total: 10,
          list: [
            { name: '未知车型', value: 5, percentage: '50%' },
            { name: 'Beetle', value: 5, percentage: '50%' },
          ],
        },
      },
      {
        chartId: 4,
        data: {
          total: 20,
          list: [
            {
              arrays: [
                { number: 5, time: '2023-12-24' },
                { number: 5, time: '2023-12-25' },
              ],
              name: '未知车型',
              level: '0',
            },
            {
              arrays: [
                { number: 5, time: '2023-12-24' },
                { number: 5, time: '2023-12-25' },
              ],
              name: 'Beetle',
              level: '1',
            },
          ],
        },
      },
      {
        chartId: 5,
        data: {
          total: 20,
          list: [
            { name: '未知车型', value: 5, percentage: '50%' },
            { name: 'Beetle', value: 1, percentage: '50%' },
          ],
        },
      },
      {
        chartId: 6,
        data: {
          total: 20,
          list: [
            {
              arrays: [
                { number: 10, time: '2023-12-24' },
                { number: 5, time: '2023-12-25' },
              ],
              name: '未知车型',
              level: '0',
            },
            {
              arrays: [
                { number: 5, time: '2023-12-24' },
                { number: 5, time: '2023-12-25' },
              ],
              name: 'Beetle',
              level: '1',
            },
          ],
        },
      },
      {
        chartId: 7,
        data: {
          total: 20,
          list: [
            {
              arrays: [
                { number: 10, time: '2023-12-24' },
                { number: 5, time: '2023-12-25' },
              ],
              name: '未知车型',
              level: '0',
              color: '#F0DA4C',
            },
            {
              arrays: [
                { number: 5, time: '2023-12-24' },
                { number: 5, time: '2023-12-25' },
              ],
              name: 'Beetle',
              level: '1',
              color: '#32FDB8',
            },
          ],
        },
      },
      {
        chartId: 8,
        data: {
          total: 20,
          list: [
            {
              name: '疑似远程入侵',
              percent: 46.7,
              count: 336,
              color: '#533DF1',
            },
            {
              name: '入口流量异常',
              percent: 43.02,
              count: 248,
              color: '#40CD6E',
            },
            {
              name: '未知用户',
              percent: 49.88,
              count: 178,
              color: '#FBD82C',
            },
            {
              name: '未知进程',
              percent: 36.75,
              count: 89,
              color: '#44E2FE',
            },
            {
              name: '未知端口',
              percent: 36.14,
              count: 16,
              color: '#1B84FF',
            },
            {
              name: '特征值异常',
              percent: 36.14,
              count: 16,
              color: '#1B84FF',
            },
          ],
        },
      },
      {
        chartId: 9,
        data: [
          [
            {
              ip: '**********',
              name: 'China,ChangSha',
              location: [112.936271, 28.235399],
            },
            {
              ip: '**********',
              name: 'China,ChangSha',
              location: [120.936271, 25.235399],
            },
            { ip: '**********', name: 'Albania' },
          ],
        ],
      },
    ]
  } else {
    data = [
      {
        chartId: 2,
        data: {
          total: 1000,
          percentage: '50%',
        },
      },
    ]
  }
  return {
    code: 200,
    data: data,
  }
}
