<template>
  <edit-page
    class="list-edit"
    :popsName="
      editType === 'new'
        ? $t('vulnerability.edit.title4')
        : editType === 'edit'
        ? $t('vulnerability.edit.title5')
        : $t('vulnerability.edit.title6')
    "
  >
    <div slot="BreadBtn">
      <v-btn
        color="primary"
        width="76"
        min-width="76"
        elevation="0"
        @click="addTicket"
        v-if="editType !== 'new'"
      >
        转工单
      </v-btn>
      <v-btn
        v-if="editType !== 'detail'"
        color="primary"
        width="76"
        min-width="76"
        elevation="0"
        :loading="confirmLoading"
        @click="onSave"
        class="ml-3"
      >
        {{ $t('action.submit') }}
      </v-btn>
    </div>
    <v-form ref="form" class="color-base overflow-y py-6 px-4">
      <div class="d-flex align-center justify-space-between pb-6 pt-2">
        <span class="text-title font-weight-medium">{{
          $t('global.drawer.baseInfo')
        }}</span>
      </div>
      <v-row dense class="px-9">
        <v-col cols="6">
          <v-autocomplete
            :label="$t('vulnerability.headers.findUser')"
            v-model="form.findUser"
            :items="userList"
            :rules="[v => required(v, $t('vulnerability.headers.findUser'))]"
            :menu-props="{ offsetY: true }"
            :readonly="editType === 'detail'"
            class="is-required"
          ></v-autocomplete>
        </v-col>

        <v-col cols="6">
          <!-- <vsoc-date-picker
                    :value="form.findDate"
                    @change="date => (form.findDate = date)"
                  ></vsoc-date-picker> -->
          <v-menu
            ref="menu1"
            v-model="isShowDate"
            :close-on-content-click="false"
            transition="scale-transition"
            offset-y
            min-width="auto"
            :disabled="editType === 'detail'"
          >
            <template v-slot:activator="{ on, attrs }">
              <v-text-field
                v-model="form.findDate"
                :label="$t('vulnerability.headers.findDate')"
                persistent-hint
                append-icon="mdi-calendar-range-outline"
                v-bind="attrs"
                v-on="on"
                @blur="date = formatDate(form.findDate)"
                :rules="[
                  v => required(v, $t('vulnerability.headers.findDate')),
                ]"
              ></v-text-field>
            </template>
            <v-date-picker
              v-model="form.findDate"
              no-title
              :locale="$i18n.locale"
              @input="isShowDate = false"
            ></v-date-picker>
          </v-menu>
        </v-col>

        <v-col cols="6">
          <v-text-field
            :label="$t('vulnerability.headers.vulnerId')"
            v-model="form.vulneraNumber"
            :readonly="editType === 'detail'"
          ></v-text-field>
        </v-col>
        <v-col cols="6">
          <!-- <v-select
                      label="漏洞级别"
                      v-model="form.vulnerabilityLevel"
                      :items="vulnerabilityLevelEnum"
                      :menu-props="{ offsetY: true }"
                    ></v-select> -->

          <v-radio-group
            :readonly="editType === 'detail'"
            v-model="form.vulnerabilityLevel"
            row
            mandatory
          >
            <span
              class="mr-4 ml-2 text--secondary text-root-base d-flex justify-center align-center"
              >{{ $t('vulnerability.headers.alarmLevel') }}</span
            >
            <v-radio
              v-for="level in vulnerabilityLevelEnum"
              :key="level.value"
              :value="level.value"
              :color="level.color"
            >
              <template v-slot:label>
                <div class="d-flex" :style="`color:${level.color}`">
                  <vsoc-icon
                    class="mr-1"
                    type="fill"
                    size="middle"
                    icon="icon-loudongdengjibiaozhi"
                  ></vsoc-icon>
                  <span class="font-weight-medium text-root-base">{{
                    level.text
                  }}</span>
                </div>
              </template>
            </v-radio>
          </v-radio-group>
        </v-col>
      </v-row>

      <div class="d-flex align-center justify-space-between py-6">
        <span class="text-title font-weight-medium">{{
          $t('vulnerability.edit.title2')
        }}</span>
      </div>
      <v-row dense class="px-9">
        <v-col cols="6">
          <v-text-field
            :readonly="editType === 'detail'"
            :label="$t('vulnerability.headers.affectManufacturers')"
            v-model="form.impactManufacturer"
            :rules="[
              v => required(v, $t('vulnerability.headers.affectManufacturers')),
            ]"
            class="is-required"
          ></v-text-field>
        </v-col>

        <v-col cols="6">
          <v-text-field
            :readonly="editType === 'detail'"
            :label="$t('vulnerability.edit.title2')"
            v-model="form.impactProduct"
            :rules="[v => required(v, $t('vulnerability.edit.title2'))]"
            class="is-required"
          ></v-text-field>
        </v-col>
      </v-row>

      <div class="d-flex align-center justify-space-between py-6">
        <span class="text-title font-weight-medium">{{
          $t('vulnerability.edit.title3')
        }}</span>
      </div>
      <v-row dense class="px-9">
        <v-col cols="6">
          <v-text-field
            :readonly="editType === 'detail'"
            :label="$t('vulnerability.headers.vulnerabilityName')"
            v-model="form.vulnerabilityName"
            :placeholder="$t('vulnerability.edit.hint')"
            :rules="[
              v => required(v, $t('vulnerability.headers.vulnerabilityName')),
            ]"
            class="is-required"
          ></v-text-field>
        </v-col>

        <v-col cols="6">
          <v-select
            :readonly="editType === 'detail'"
            :label="$t('vulnerability.headers.loopholeType')"
            v-model="form.loopholeType"
            :items="vulnerabilityTypeEnum"
            :menu-props="{ offsetY: true }"
            :rules="[
              v => required(v, $t('vulnerability.headers.loopholeType')),
            ]"
            class="is-required"
          ></v-select>
        </v-col>

        <v-col cols="12">
          <v-text-field
            :readonly="editType === 'detail'"
            :label="$t('vulnerability.edit.url')"
            v-model="form.vulneraUrl"
            placeholder="eg: http://baidu.com,https://www.google.com/"
            :rules="[v => urlsValidator(v)]"
          ></v-text-field>
        </v-col>
        <v-col cols="12">
          <v-textarea
            :readonly="editType === 'detail'"
            :label="$t('vulnerability.edit.desc')"
            v-model="form.vulneraContent"
            :rows="$AREA_ROWS"
          ></v-textarea>
        </v-col>
        <v-col cols="12">
          <v-textarea
            :readonly="editType === 'detail'"
            :label="$t('vulnerability.edit.solution')"
            v-model="form.processingPlan"
            :rows="$AREA_ROWS"
          ></v-textarea>
        </v-col>
        <v-col cols="12"
          ><vsoc-file
            :title="$t('vulnerability.edit.file')"
            :hint="fileHint"
            :accept="fileAccept"
            :limit="fileLimit"
            :fileList="attachmentList"
            :actions="[editType]"
            :disabled="editType === 'detail'"
            @input="arr => (form.attachmentList = arr)"
          ></vsoc-file>
        </v-col>
      </v-row>
    </v-form>
  </edit-page>
</template>

<script>
import { max, required, urlsValidator } from '@/@core/utils/validation'
import {
  addSelfVulnerability,
  editSelfVulnerability,
  getSelfVulnerabilityDetail,
} from '@/api/vulnerability/index'
import EditPage from '@/components/EditPage.vue'
import VsocFile from '@/components/VsocFile.vue'
import breadCrumb from '@/components/bread-crumb/index'
import { toDate } from '@/util/filters'
import { setLocalStorage } from '@/util/localStorage'
import { deepClone } from '@/util/utils'
export default {
  name: 'assetEdit',
  components: { breadCrumb, EditPage, VsocFile },
  data() {
    return {
      userList: [],
      attachmentList: [],
      fileLimit: 5,
      fileAccept: '.doc, .docx, .zip, .py',
      isShowDate: false,
      required,
      max,
      urlsValidator,
      tarvalue: 0,

      value: [0, 1, 2],

      confirmLoading: false,
      error: false,
      errorText: '',
      form: {
        findUser: '',
        findDate: this.formatDate(new Date()),
        vulneraNumber: '',
        vulnerabilityLevel: '',
        impactManufacturer: '',
        impactProduct: '',
        vulnerabilityName: '',
        loopholeType: '',
        vulneraUrl: '',
        vulneraContent: '',
        processingPlan: '',
        attachmentList: [],
      },
      infoData: {},
      editType: 'new',
      platList: [],
      vendorList: [],
      platformList: [],
      parentProductList: [],
    }
  },
  computed: {
    fileHint() {
      return `${this.$i18n.t('ticket.hint.attachmentsTip', ['5', '20M'])} ： ${
        this.fileAccept
      }`
    },
    vulnerabilityLevelEnum() {
      return this.$store.getters['enums/getLaboratoryVulnerabilityLevel']
    },
    vulnerabilityTypeEnum() {
      return Object.assign([], this.$store.state.enums.enums.VulnerabilityType)
    },
  },
  mounted() {
    this.editType = this.$route.params.mode || 'new'
    if (this.editType !== 'new') {
      this.loadAssetDetail()
    }
    this.loadUserList()
  },
  methods: {
    //转工单
    addTicket() {
      //0超1高2中3低 0严重1高2中3低
      const params = {
        priority: this.infoData.vulnerabilityLevel,
        title:
          this.infoData.vulneraNumber +
          '_' +
          this.vulnerabilityLevelEnum[this.infoData.vulnerabilityLevel].text +
          '_' +
          this.infoData.vulnerabilityName, //漏洞编号_漏洞等级_漏洞名称
        ticketContent: this.infoData.vulneraContent,
        dataSource: '1',
        relationId: this.infoData.vulneraNumber,
      }
      setLocalStorage('alertTicket', JSON.stringify(params))
      // localStorage.setItem('alertTicket', JSON.stringify(params))
      this.$router.push('/ticket/addTicket?type=1')
    },
    async loadUserList() {
      this.userList = await this.$store.dispatch('global/loadUserList')
    },
    formatDate(date) {
      return toDate(date, 'yyyy-MM-dd')
    },
    async loadAssetDetail() {
      try {
        const params = {
          id: this.$route.query.id,
        }
        const { data } = await getSelfVulnerabilityDetail(params)
        this.attachmentList = data.attachment
        this.form = data?.vulneraData
        this.infoData = deepClone(data?.vulneraData)
      } catch (err) {
        console.log('获取自建漏洞详情报错', err)
      }
    },
    onAssociatePlatform(id) {
      const currentItem = this.platList.find(v => v.id === id)
      if (currentItem) {
        this.form.supplierId = currentItem.supplierId
        this.form.vehiclePlatformId = currentItem.vehiclePlatformId
      }
      // const currentItem = this.platformList.find(
      //   v =>
      //     v.partId === this.form.partId &&
      //     v.supplierId === this.form.supplierId,
      // )
      // if (currentItem) {
      //   this.form.vehiclePlatformId = currentItem.id
      // }
    },

    async onSave() {
      try {
        this.confirmLoading = true
        const bool = this.$refs.form.validate()
        if (!bool) {
          return
        }
        if (this.editType === 'new') {
          await addSelfVulnerability(this.form)
          this.$notify.info('success', this.$t('global.hint.add', ['']))
        } else {
          await editSelfVulnerability(this.form)
          this.$notify.info('success', this.$t('global.hint.edit', ['']))
        }

        this.$router.go(-1)
      } catch (err) {
        console.log('自建漏洞编辑报错', err)
      } finally {
        this.confirmLoading = false
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.col {
  padding: 0px 12px;
}
</style>
