<template>
  <v-form ref="form" v-model="valid" lazy-validation>
    <v-row class="pl-2 pr-2 mx-0 my-0">
      <v-col cols="12" class="px-0 py-0">
        <el-tree
          ref="elTree"
          :labelText="$t('menu.headers.name')"
          class="mt-n2"
          :organizationFlag="false"
          :showCheckbox="true"
          :treeData="permissionList"
          :default-props="defaultProps"
          :expandFlag="false"
          :isIcon="true"
        >
        </el-tree>
        <!-- <div>
          <v-text-field
            v-model="filterName"
            hide-details
            label="请输入菜单名称"
            placeholder="eg. admin"
            outlined
            dense
            required
            color="primary"
            autocomplete="off"
            class="my-2"
            append-icon="mdi-magnify"
          >
          </v-text-field>
          <v-treeview
            v-model="selectUiIds"
            :search="filterName"
            color="primary"
            item-key="id"
            item-text="name"
            selectable
            :items="permissionList"
            expand-icon="icon-tree"
          ></v-treeview>
        </div> -->
      </v-col>
    </v-row>
  </v-form>
</template>

<script>
// import { getUiIdsForRole, setUisForRole } from '@/api/auth/auth-role-ui'
import { querySysMenuList } from '@/api/system/menu'
import { findRoleMenuList, saveOrUpdateRoleMenu } from '@/api/system/role'
import elTree from '@/components/el-tree/index'
import { deepClone } from '@/util/utils'

export default {
  name: 'Grant',
  props: {
    roleId: {
      type: String,
      required: true,
    },
  },
  components: {
    elTree,
  },
  data() {
    return {
      valid: true,
      filterName: '',
      uiTreeData: [],
      selectUiIds: [],
      permissionList: [],
      defaultProps: {
        children: 'children',
        label: 'name',
      },
    }
  },

  // computed: {
  //   permissionList() {
  //     return store.state.permission.permissions
  //   },
  // },
  mounted() {
    const _this = this

    // this.getSelectUiIds()

    Promise.all([this.loadSysMenuList(), this.getSelectUiIds()]).then(
      values => {
        _this.permissionList = _this.deepFilter(deepClone(values[0]))

        // _this.selectUiIds = values[1].menuIds
        this.$nextTick(() => {
          this.$refs['elTree'].$refs['tree'].setCheckedKeys(values[1].menuIds)
        })
      },
    )
  },
  methods: {
    deepFilter(list) {
      return list.filter(item => {
        if (item.children) {
          item.children = this.deepFilter(item.children)
        }
        return item.isShow
      })
    },
    loadSysMenuList() {
      return new Promise((resolve, reject) =>
        querySysMenuList({ name: '' })
          .then(resp => {
            if (resp.code === 200 && resp.data) {
              resolve(resp.data)
            }
          })
          .catch(err => {
            reject(err)
          }),
      )
    },

    // 获取选中的菜单id数组
    getSelectUiIds() {
      const _this = this

      return new Promise((resolve, reject) => {
        findRoleMenuList({ roleId: _this.roleId })
          .then(resp => {
            if (resp.code === 200 && resp.data) {
              // this.selectUiIds = resp.data.menuIds

              // resp.data.filter(v => v.isExist).map(item => item.id)
              resolve(resp.data)
            }
          })
          .catch(e => {
            reject(e)
          })
      })
    },

    // 重新授权
    save(callback) {
      const bool = this.$refs.form.validate()
      if (!bool) return callback(false, true)
      let menuIds =
        this.$refs['elTree'].$refs['tree'].getCheckedKeys(true) || []
      // let checkedNodes = this.$refs['elTree'].$refs['tree'].getCheckedNodes()
      // menuIds = menuIds.concat(
      //   checkedNodes.filter(v => !!v.boundMenuId).map(v => v.boundMenuId),
      // )
      saveOrUpdateRoleMenu({ roleId: this.roleId, menuIds: menuIds })
        .then(resp => {
          if (resp.code === 200) {
            this.$notify.info('success', this.$t('role.grant'))
            this.$emit('save')
            callback()
          }
        })
        .catch(e => {
          console.log(e)
          callback(false, true)
        })
    },
  },
}
</script>

<style lang="scss" scoped></style>
