<template>
  <div class="h-100 w-50">
    <vsoc-chart
      class="box-chart d-flex align-center"
      :title="title"
      :echartId="echartId"
      :option="chartOption"
      @highlight="onHighlight"
    ></vsoc-chart>
  </div>
</template>

<script>
import VsocChart from '@/components/VsocChart.vue'
import { numberToFormat } from '@/util/filters'
import { breakWords, deepClone } from '@/util/utils'
import {
  cloudPieLegendFn,
  cloudPieSeriesFn,
  cloudTooltip,
  getRoundSize,
} from './chart'
export default {
  name: 'CardRing',
  components: {
    VsocChart,
  },
  props: {
    title: {
      type: String,
      default: '',
    },
    echartId: {
      type: String,
      default: '',
    },
    list: {
      type: Array,
      default: () => {
        return []
      },
    },
    total: {
      type: [Number, String],
      default: () => {
        return 0
      },
    },
  },
  created() {},
  computed: {
    chartOption() {
      const formatter = () => {
        const totalObj = numberToFormat(this.total, 'Object')
        // return `\n{value|${totalObj.num}}{unit|${totalObj.unit}}`
        return `{name|${breakWords(this.title, 5)}}\n{value|${totalObj.num}${
          totalObj.unit
        }}{unit|Gbps}`
      }
      let list = deepClone(this.list)
      return {
        color: ['#01eedc', '#0773ff'],
        tooltip: cloudTooltip,
        legend: {
          ...cloudPieLegendFn(list),
          bottom: getRoundSize(30),
          show: false,
        },
        series: [
          {
            ...cloudPieSeriesFn(list, formatter)[0],
            bottom: -getRoundSize(70),
          },
        ],
      }
    },
  },
  methods: {
    onHighlight(obj, myChart) {
      const option = this.chartOption
      option.tooltip.backgroundColor = obj.color
      myChart.setOption(option)
    },
  },
}
</script>
