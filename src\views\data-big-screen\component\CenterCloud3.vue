<template>
  <div class="c-box pb-0">
    <div class="c-box-header">{{ title || $t('screen.cloud.center3') }}</div>

    <vsoc-chart
      echartId="center-cloud-3"
      class="box-chart"
      :option="chartOption"
      @highlight="onHighlight"
    ></vsoc-chart>
  </div>
</template>

<script>
import VsocChart from '@/components/VsocChart.vue'
import { numberToFormat } from '@/util/filters'
import {
  cloudPieColor,
  cloudPieLegendFn,
  cloudPieSeriesFn,
  cloudTooltip,
} from './chart'
export default {
  name: 'CenterCloud3',
  components: {
    VsocChart,
  },
  props: {
    list: {
      type: Array,
      default: () => {
        return [
          { name: 'Normal', value: 12 },
          { name: 'Moderate Risk', value: 8 },
          { name: 'Low Risk', value: 6 },
          { name: 'High Risk', value: 2 },
        ]
      },
    },
    total: {
      type: [Number, String],
      default: () => {
        return 1.56 * Math.pow(10, 6)
      },
    },
    title: {
      type: String,
      default: '',
    },
  },
  computed: {
    chartOption() {
      const formatter = () => {
        const totalObj = numberToFormat(this.total, 'Object')
        return `\n{value|${totalObj.num}}{unit|${totalObj.unit}}`
      }
      return {
        color: cloudPieColor,
        tooltip: cloudTooltip,
        legend: cloudPieLegendFn(),
        series: cloudPieSeriesFn(this.list, formatter),
      }
    },
  },
  methods: {
    onHighlight(obj, myChart) {
      const option = this.chartOption
      option.tooltip.backgroundColor = obj.color
      myChart.setOption(option)
    },
  },
}
</script>
