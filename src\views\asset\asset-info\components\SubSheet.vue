<template>
  <v-bottom-sheet
    ref="subMap"
    persistent
    v-model="value"
    :fullscreen="isFullScreen"
  >
    <v-sheet v-if="value" :height="isFullScreen ? '100vh' : '50vh'">
      <div class="h-100 pa-2">
        <div class="d-flex justify-space-between map-title mb-2">
          <div class="d-flex align-center">
            <vsoc-icon
              v-if="lastReceiveDate && assetActivityLevelEnum.length"
              type="fill"
              :style="{
                color:
                  days <= assetActivityLevelEnum[0]
                    ? '#12e055'
                    : days > assetActivityLevelEnum[0] &&
                      days <= assetActivityLevelEnum[1]
                    ? '#f5d018'
                    : '#b4bbcc',
              }"
              icon="icon-huoyuedu"
              class="mr-2"
            ></vsoc-icon>
            <span class="mr-6 text--primary text-content">{{ vin }}</span>
            <span class="text--primary text-content">{{ subTitle }}</span>
            <v-btn
              @click.stop="copy(subList)"
              style="margin-top: -2px"
              v-show-tips="$t('action.copy')"
              icon
            >
              <vsoc-icon
                size="1.2rem"
                type="fill"
                icon="icon-fuzhi"
              ></vsoc-icon>
            </v-btn>
          </div>
          <div class="w-60 d-flex align-center justify-end">
            <v-text-field
              v-model="subSearch"
              background-color="transparent"
              :label="$t('asset.axis.queryField')"
              hide-details
              clearable
              outlined
              dense
              type="text"
              class="text-width"
            >
            </v-text-field>
            <v-btn icon small @click.native="toggleChangeFullScreen">
              <v-icon
                v-show-tips="
                  isFullScreen ? $t('action.reback') : $t('action.fullScreen')
                "
                size="1.5rem"
              >
                {{ isFullScreen ? 'mdi-fullscreen-exit' : 'mdi-fullscreen' }}
              </v-icon>
            </v-btn>
            <v-btn icon small @click.native="close">
              <v-icon v-show-tips="$t('action.close')" size="1.5rem">
                mdi-close
              </v-icon>
            </v-btn>
          </div>
        </div>
        <div class="overflow-y-auto" style="height: calc(100% - 46px)">
          <v-data-table
            :headers="subHeaders"
            :items="subList"
            :items-per-page="subList.length"
            hide-default-footer
            :search="subSearch"
          >
            <template v-slot:header.name="{ header }">
              {{ subSignalValueType === '7' ? $t('global.name') : header.text }}
            </template>
            <template v-slot:item="props">
              <tr style="width: 100%">
                <template v-for="(v, key) in props.headers">
                  <td :key="key" class="text-start">
                    <div
                      v-if="
                        props.item[v.text] &&
                        (typeof props.item[v.text] === 'object' ||
                        Array.isArray(props.item[v.text])
                          ? JSON.stringify(props.item[v.text]).length > 70
                          : String(props.item[v.text]).length > 70)
                      "
                      class="d-flex align-center"
                    >
                      <div style="max-width: 26vw" v-show-tips>
                        <template
                          v-if="
                            v.text === '车辆警报类型' ||
                            v.text === '车辆警报源ECU'
                          "
                        >
                          <span style="font-family: monospace !important">
                            {{ props.item[v.text] }}</span
                          >
                        </template>
                        <template v-else>
                          {{ props.item[v.text] }}
                        </template>
                      </div>
                       <v-btn
                        v-show-tips="$t('action.copy')"
                        icon
                        @click.stop="copy(props.item[v.text])"
                      >
                                       
                        <vsoc-icon
                          type="fill"
                          size="16px"
                          icon="icon-fuzhi"
                        ></vsoc-icon>
                                     
                      </v-btn>
                    </div>
                    <div v-else>
                      <template
                        v-if="
                          v.text === '车辆警报类型' ||
                          v.text === '车辆警报源ECU'
                        "
                      >
                        <span style="font-family: monospace !important">
                          {{ props.item[v.text] }}</span
                        >
                      </template>
                      <template v-else>
                        {{ props.item[v.text] }}
                      </template>
                    </div>
                  </td>
                </template>
              </tr>
            </template>
            <!-- <template v-slot:item.车辆警报类型="{ item }">
              <span style="font-family: monospace !important">{{
                item.车辆警报类型
              }}</span>
            </template>
            <template v-slot:item.车辆警报源ECU="{ item }">
              <span style="font-family: monospace">{{
                item.车辆警报源ECU
              }}</span>
            </template>
            <template v-slot:item.tag="{ item }">
              <span>{{ item }}</span>
            </template> -->
          </v-data-table>
        </div>
      </div>
    </v-sheet>
  </v-bottom-sheet>
</template>

<script>
import { exitFullscreen } from '@/util/utils'
import moment from 'moment'

export default {
  name: 'SubSheet',
  props: {
    value: Boolean,
    subSignalValueType: String,
    subTitle: String,
    vin: String,
    lastReceiveDate: String,
    subHeaders: Array,
    subList: Array,
  },
  components: {},
  computed: {
    days() {
      return (
        (new Date().getTime() - new Date(this.lastReceiveDate).getTime()) /
        86400000
      )
    },
    assetActivityLevelEnum() {
      return this.$store.getters['enums/getAssetActivityLevel']
    },
  },
  data() {
    return {
      subSearch: '',
      // 是否全屏
      isFullScreen: false,
    }
  },
  watch: {},
  methods: {
    moment,
    async copy(params) {
      let val = JSON.stringify(params)
      if (navigator.clipboard && window.isSecureContext) {
        // navigator clipboard 向剪贴板写文本
        this.$notify.info('success', this.$t('global.hint.copy'))
        return navigator.clipboard.writeText(val)
      } else {
        // 创建text area
        const element = document.createElement('SPAN')
        element.textContent = val
        document.body.appendChild(element)
        if (document.selection) {
          var range = document.body.createTextRange()
          range.moveToElementText(element)
          range.select()
        } else if (window.getSelection) {
          var range = document.createRange()
          range.selectNode(element)
          window.getSelection().removeAllRanges()
          window.getSelection().addRange(range)
        }
        document.execCommand('copy')
        element.remove ? element.remove() : element.removeNode(true)
        this.$notify.info('success', this.$t('global.hint.copy'))
      }
    },
    toggleChangeFullScreen() {
      this.isFullScreen = !this.isFullScreen
    },
    close() {
      if (this.isFullScreen) {
        exitFullscreen()
      }
      this.$emit('input', false)
    },
  },
}
</script>

<style lang="scss" scoped>
.darwer {
  height: 40vh;
  background: #fff;
}
.full-screen {
  height: 100vh;
  background: #fff;
}
.pa-lb-10 {
  padding: 0 0 3.75rem 0.625rem;
}
.pad-tb-16 {
  padding: 1rem 0;
}
.bm-view {
  height: 100%;
  width: 100%;
}
</style>
