<!-- 漏洞详情 -->
<template>
  <div>
    <bread-crumb>
      <template slot="title">
        <div class="d-flex color-base text-title">
          <vsoc-icon
            size="x-large"
            icon="icon-loudongdengjibiaozhi"
          ></vsoc-icon>
          <span class="ml-2">{{ form.cveId }}</span>
          <span class="ml-1">漏洞详情</span>
        </div>
      </template>
      <v-btn
        color="primary"
        width="76"
        min-width="76"
        elevation="0"
        @click="addTicket"
      >
        {{ $t('alert.btn.ticket') }}
      </v-btn>
    </bread-crumb>

    <v-card tile class="h-100 overflow-y-auto" elevation="0">
      <v-card-text class="pa-0">
        <!-- <hr class="horizontal dark" /> -->
        <div
          class="overflow-y px-10"
          :style="{ height: `calc(100vh - ${topHeaderHeight}px)` }"
        >
          <h6
            class="d-flex align-center text-sm-h6 text-typo font-weight-bolder mb-2 my-6"
          >
            <span class="text-title font-weight-medium">{{
              $t('global.drawer.baseInfo')
            }}</span>
          </h6>

          <div class="mx-6 mt-4">
            <v-form>
              <v-row>
                <v-col cols="4">
                  <!-- <v-text-field
                    v-model="form.cveId"
                    :label="$t('vulnerability.headers.cveId')"
                    hide-details
                    disabled
                  ></v-text-field> -->
                  <div class="text-root-base accent--text">
                    {{ $t('vulnerability.headers.cveId') }}
                  </div>
                  <div class="text-content font-weight-medium color-base mt-1">
                    {{ form.cveId }}
                  </div>
                </v-col>
                <v-col cols="4">
                  <!-- <v-text-field
                    :value="form.state | dataFilter"
                    :label="$t('vulnerability.cve.detail.state')"
                    hide-details
                    disabled
                  ></v-text-field> -->
                  <div class="text-root-base accent--text">
                    {{ $t('vulnerability.cve.detail.state') }}
                  </div>
                  <div class="text-content font-weight-medium color-base mt-1">
                    {{ form.state | dataFilter }}
                  </div>
                </v-col>
                <v-col cols="4">
                  <!-- <v-text-field
                    v-model="form.dataSourceName"
                    :label="$t('vulnerability.headers.dataSource')"
                    hide-details
                    disabled
                  ></v-text-field> -->
                  <div class="text-root-base accent--text">
                    {{ $t('vulnerability.headers.dataSource') }}
                  </div>
                  <div class="text-content font-weight-medium color-base mt-1">
                    {{ form.dataSourceName }}
                  </div>
                </v-col>

                <v-col cols="4">
                  <!-- <v-text-field
                    :value="form.publishedDate | toDate | dataFilter"
                    :label="$t('vulnerability.cve.headers.publishedDate')"
                    hide-details
                    disabled
                  ></v-text-field> -->
                  <div class="text-root-base accent--text">
                    {{ $t('vulnerability.cve.headers.publishedDate') }}
                  </div>
                  <div class="text-content font-weight-medium color-base mt-1">
                    {{ form.publishedDate | toDate | dataFilter }}
                  </div>
                </v-col>
                <v-col cols="4">
                  <!-- <v-text-field
                    :value="form.lastUpdateDate | toDate | dataFilter"
                    :label="$t('vulnerability.cve.drawer.lastUpdateDate')"
                    hide-details
                    disabled
                  ></v-text-field> -->
                  <div class="text-root-base accent--text">
                    {{ $t('vulnerability.cve.drawer.lastUpdateDate') }}
                  </div>
                  <div class="text-content font-weight-medium color-base mt-1">
                    {{ form.lastUpdateDate | toDate | dataFilter }}
                  </div>
                </v-col>
              </v-row>
            </v-form>
            <v-divider class="mt-4 divider--dashed"></v-divider>

            <h6
              class="d-flex align-center text-sm-h6 text-typo font-weight-bolder mb-2 mt-6"
            >
              <span class="text-root-base accent--text">{{
                $t('vulnerability.cve.headers.desc')
              }}</span>
            </h6>
            <p
              class="text-content color-base font-weight-medium"
              v-for="(desc, index) in form.descriptions"
              :key="index"
            >
              {{ desc.value }}
            </p>
            <v-divider class="mt-6 divider--dashed"></v-divider>

            <h6
              class="d-flex align-center text-sm-h6 text-typo font-weight-bolder mb-2 mt-6"
            >
              <div class="text-root-base accent--text">
                {{ $t('vulnerability.cve.detail.metric') }}
              </div>
            </h6>

            <div>
              <v-tabs
                active-class="rounded primary--text opacity-b1"
                class="w-80 mb-4"
                v-model="currentVersion"
                hide-slider
                height="46"
              >
                <v-tab class="text-content" tab-value="v3"
                  >CVSS Version 3.x</v-tab
                >
                <v-tab class="text-content ml-6" tab-value="v2"
                  >CVSS Version 2.x</v-tab
                >
              </v-tabs>
              <template
                v-if="
                  form.metricObj &&
                  form.metricObj[currentVersion] &&
                  form.metricObj[currentVersion].length > 0
                "
              >
                <v-row
                  v-for="(record, index) in form.metricObj[currentVersion]"
                  :key="index + 'version'"
                  class="color-base"
                >
                  <v-col>
                    <span class="text-root-base mr-3">Base Score:</span>
                    <v-chip
                      label
                      small
                      :color="toColor(cvss3Enum, record.baseSeverity)"
                      >{{ record.baseScore }}&nbsp{{
                        record.baseSeverity
                      }}</v-chip
                    >
                  </v-col>
                  <v-col>
                    <span class="text-root-base mr-2">Vector:</span>
                    <span>{{ record.vectorString }}</span>
                  </v-col>
                </v-row>
              </template>
              <template v-else>
                <div class="color-base text-content font-weight-medium">
                  N/A
                </div>
              </template>
            </div>
            <v-divider class="mt-6 divider--dashed"></v-divider>

            <h6
              class="d-flex align-center text-sm-h6 text-typo font-weight-bolder mb-2 mt-6"
            >
              <span class="text-root-base accent--text">{{
                $t('vulnerability.cve.detail.affectedRange')
              }}</span>

              <v-icon
                class="ml-2"
                v-show-tips="$t('vulnerability.cve.detail.hint')"
                size="16"
                >mdi-help-circle-outline</v-icon
              >
            </h6>
            <!-- 影响范围 -->
            <template v-if="form.affected && form.affected.length > 0">
              <v-data-table
                :headers="affectedObj.headers"
                :items="form.affected"
                :items-per-page="form.affected.length"
                hide-default-footer
                @click:row="$_clickRow"
                show-expand
              >
                <template v-slot:item.data-table-expand="{ item, isExpanded }">
                  <v-btn icon small>
                    <v-icon v-if="isExpanded" size="1.75rem">
                      mdi-chevron-up
                    </v-icon>
                    <v-icon v-else size="1.75rem"> mdi-chevron-down </v-icon>
                  </v-btn>
                </template>
                <template v-slot:expanded-item="{ headers, item }">
                  <td :colspan="headers.length + 1" class="py-6">
                    <v-data-table
                      class="son-table pl-6"
                      :headers="affectedObj.sonHeaders"
                      :items="item.versions"
                      :items-per-page="item.versions.length"
                      hide-default-footer
                    >
                    </v-data-table>
                  </td>
                </template>
              </v-data-table>
              <!-- <v-chip-group
                v-for="(item, index) in form.affected"
                :key="index + 'affected'"
                class="text-sm text-body"
              >
                <v-chip class="text-sm">
                  {{ $t('vulnerability.cve.detail.vendor') }}：{{ item.vendor }}
                </v-chip>
                <v-chip class="text-sm">
                  {{ $t('vulnerability.cve.detail.product') }}：{{
                    item.product
                  }}
                </v-chip>
                <v-chip
                  v-for="(v, i) in item.versions"
                  :key="i + 'versions'"
                  class="text-sm"
                >
                  {{ $t('vulnerability.cve.detail.version') }}：{{
                    v.version
                  }}({{ v.status }})
                </v-chip>
              </v-chip-group> -->
            </template>
            <template v-else>
              <div class="color-base text-content font-weight-medium">N/A</div>
            </template>

            <v-divider class="mt-3 divider--dashed"></v-divider>

            <h6
              class="d-flex align-center text-sm-h6 text-typo font-weight-bolder mb-2 mt-6"
            >
              <span class="text-root-base accent--text">{{
                $t('vulnerability.link')
              }}</span>
            </h6>
            <!-- <p class="text-sm text-body">
              {{ form.solution }}
            </p> -->
            <v-data-table
              :headers="referenceHeaders"
              :items="form.reference"
              :items-per-page="form.reference.length || 0"
              hide-default-footer
            >
              <template v-slot:item.url="{ item }">
                <a class="text-sm d-block" :href="item.url" target="_blank">{{
                  item.url
                }}</a>
              </template>

              <template v-slot:item.tags="{ item }">
                <v-chip-group v-if="item.tags && item.tags.length > 0">
                  <v-chip
                    small
                    v-for="(tag, j) in item.tags"
                    :key="j + 'tag'"
                    class="text-sm"
                  >
                    {{ tag }}
                  </v-chip>
                </v-chip-group>
                <div v-else class="color-base">N/A</div>
              </template>
            </v-data-table>
            <v-divider class="mt-6 divider--dashed"></v-divider>

            <h6
              class="d-flex align-center text-sm-h6 text-typo font-weight-bolder mb-2 mt-6"
            >
              <span class="text-root-base accent--text">{{
                $t('vulnerability.cve.detail.type')
              }}</span>
            </h6>

            <p
              v-for="(descriptions, i) in form.problemtypes"
              :key="i + 'problemtypes'"
            >
              <template
                v-if="
                  descriptions &&
                  descriptions.descriptions &&
                  descriptions.descriptions.length > 0
                "
              >
                <span
                  v-for="(desc, j) in descriptions.descriptions"
                  :key="j + 'desc'"
                  class="text-content font-weight-medium mr-3"
                  target="_blank"
                  >{{ desc.cweId | dataFilter }}</span
                >
              </template>
              <template v-else
                ><span class="color-base text-content font-weight-medium"
                  >N/A</span
                ></template
              >
            </p>
          </div>
        </div>
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
import { getCveDetail } from '@/api/vulnerability/index'
import breadCrumb from '@/components/bread-crumb/index'
import { setLocalStorage } from '@/util/localStorage'
export default {
  name: 'VulnerabilityDetail',
  components: {
    breadCrumb,
  },
  data() {
    return {
      referenceHeaders: [
        { text: 'Hyperlink', value: 'url' },
        { text: 'Resource', value: 'tags' },
      ],
      currentVersion: 'v3',
      form: {
        id: '1749358050566733824',
        cveId: 'CVE-2006-0941',
        publishedDate: '2006-03-01 02:00:00',
        cveSource: '"mitre"',
        dataSource: '4',
        lastUpdateDate: '2018-10-18 14:57:01',
        descriptions: [
          {
            lang: 'en',
            value:
              'Multiple cross-site scripting (XSS) vulnerabilities in post.php in ShoutLIVE 1.1.0 allow remote attackers to inject arbitrary web script or HTML via certain variables when posting new messages.',
          },
        ],
        tags: [],
        affected: [
          {
            product: 'n/a',
            vendor: 'n/a',
            versions: [
              {
                status: 'affected',
                version: 'n/a',
              },
            ],
          },
        ],
        reference: [
          {
            name: '20060307 [eVuln] ShoutLIVE PHP Code Execution & Multiple XSS Vulnerabilities',
            tags: ['mailing-list', 'x_refsource_BUGTRAQ'],
            url: 'http://www.securityfocus.com/archive/1/426985/100/0/threaded',
          },
          {
            name: '16857',
            tags: ['vdb-entry', 'x_refsource_BID'],
            url: 'http://www.securityfocus.com/bid/16857',
          },
          {
            name: 'shoutlive-post-xss(24901)',
            tags: ['vdb-entry', 'x_refsource_XF'],
            url: 'https://exchange.xforce.ibmcloud.com/vulnerabilities/24901',
          },
          {
            name: '19047',
            tags: ['third-party-advisory', 'x_refsource_SECUNIA'],
            url: 'http://secunia.com/advisories/19047',
          },
          {
            name: '23483',
            tags: ['vdb-entry', 'x_refsource_OSVDB'],
            url: 'http://www.osvdb.org/23483',
          },
          {
            name: '557',
            tags: ['third-party-advisory', 'x_refsource_SREASON'],
            url: 'http://securityreason.com/securityalert/557',
          },
          {
            name: 'ADV-2006-0755',
            tags: ['vdb-entry', 'x_refsource_VUPEN'],
            url: 'http://www.vupen.com/english/advisories/2006/0755',
          },
          {
            tags: ['x_refsource_MISC'],
            url: 'http://evuln.com/vulns/87/summary.html',
          },
        ],
        metrics: [],
        problemtypes: [
          {
            descriptions: [
              {
                description: 'n/a',
                lang: 'en',
                type: 'text',
              },
            ],
          },
        ],
        createUser: 'admin',
        createTime: '2023-08-11 15:42:10',
        isNotReference: '1',
        isNotReferenceName: '不存在',
        startPublishedDate: null,
        endPublishedDate: null,
        cweId: null,
        startLastUpdateDate: null,
        endLastUpdateDate: null,
        vendor: null,
        product: null,
        dataSourceName: 'cve',
      },
      query: {
        page: 1,
        size: 10,
        loading: false,
      },
      affectedObj: {
        headers: [
          {
            text: '',
            value: 'data-table-expand',
            width: 50,
          },
          {
            text: 'id',
            value: 'id',
          },
        ],
        sonHeaders: [
          {
            text: 'id',
            value: 'id',
          },
        ],
      },
    }
  },
  computed: {
    topHeaderHeight() {
      return this.$store.getters['global/getTopHeaderHeight']
    },
    cvss3Enum() {
      return this.$store.getters['enums/getCvss3']
    },
    vulnerabilityLevelEnums() {
      return this.$store.state.enums.enums.VulnerabilityLevel
    },
  },
  created() {
    this.loadDetail()
  },
  methods: {
    //转工单
    addTicket() {
      let description = ''
      if (this.form.descriptions.length) {
        this.form.descriptions.forEach(v => {
          description += v.value
        })
      }
      // [0,1,2,3] 严重 高 中 低
      let levelList = [
        { value: '0', text: 'critical' },
        { value: '1', text: 'high' },
        { value: '2', text: 'medium' },
        { value: '3', text: 'low' },
        { value: '3', text: 'none' },
      ]
      let priority = '3'
      if (this.form.metricObj['v3'].length) {
        priority = levelList.find(
          v =>
            v.text.toLocaleUpperCase() ===
            this.form.metricObj['v3'][0].baseSeverity.toLocaleUpperCase(),
        ).value
      }
      const params = {
        priority: priority,
        title: this.form.cveId,
        ticketContent: description,
        dataSource: '1',
        relationId: this.form.cveId,
      }
      setLocalStorage('alertTicket', JSON.stringify(params))
      // localStorage.setItem('alertTicket', JSON.stringify(params))
      this.$router.push('/ticket/addTicket?type=1')
    },
    // 评分色
    toColor(list, text) {
      // 忽略大小写
      return list.find(v => new RegExp(text, 'i').test(v.text))?.color
    },
    showUrl(url) {
      return url && url.trim().startsWith('http')
    },
    async loadDetail() {
      try {
        const params = {
          id: this.$route.query.id,
        }
        const { data } = await getCveDetail(params)
        this.form = data
        this.form.metricObj = this.handleVersionGroup(data.metrics)
        let reg = /^(n\/a)$/
        this.form.descriptions.forEach(desc => {
          if (reg.test(desc.value)) {
            desc.value = desc.value.toUpperCase()
          }
        })
        this.form.affected.forEach(v => {
          if (reg.test(v.product)) {
            v.product = v.product.toUpperCase()
          }
          if (reg.test(v.vendor)) {
            v.vendor = v.vendor.toUpperCase()
          }
          v.versions.forEach(ver => {
            if (reg.test(ver.version)) {
              ver.version = ver.version.toUpperCase()
            }
          })
        })
        this.handledAffected()
      } catch (err) {
        console.log('cve漏洞详情报错', err)
      }
    },
    // 单击表格行
    $_clickRow(item, slot) {
      slot.expand(!slot.isExpanded)
    },
    handledAffected() {
      let keyList = this.form.affected
        .map(item => {
          return Object.keys(item)
        })
        .flat()
      let keys = [...new Set(keyList)].filter(k => k !== 'versions')

      keys.forEach(key => {
        let obj = {
          text: key,
          value: key,
          sortable: false,
        }
        this.affectedObj.headers.push(obj)
      })

      let versionList = this.form.affected.flatMap(v => v.versions)
      let versionKeyList = versionList.flatMap(v => {
        return Object.keys(v)
      })
      let versionKeys = [...new Set(versionKeyList)]

      versionKeys.forEach(key => {
        let obj = {
          text: key,
          value: key,
          sortable: false,
        }
        this.affectedObj.sonHeaders.push(obj)
      })

      this.form.affected.forEach((x, i) => {
        x.id = i + 1
        x.versions.forEach((y, j) => {
          y.id = `${i + 1}.${j + 1}`
          Object.keys(y).forEach(x => {
            if (
              Array.isArray(y[x]) ||
              Object.prototype.toString.call(y[x]) === '[object Object]'
            ) {
              y[x] = JSON.stringify(y[x])
            }
          })
        })
      })
    },
    handleVersionGroup(arr) {
      // metrics: []

      // CVE-2017-11427: [
      //   {
      //     "cvssV3_0": {
      //       "baseSeverity": "HIGH",
      //       "confidentialityImpact": "HIGH",
      //       "attackComplexity": "LOW",
      //       "scope": "CHANGED",
      //       "attackVector": "NETWORK",
      //       "availabilityImpact": "NONE",
      //       "integrityImpact": "NONE",
      //       "baseScore": 7.7,
      //       "privilegesRequired": "LOW",
      //       "vectorString": "CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:C/C:H/I:N/A:N",
      //       "userInteraction": "NONE",
      //       "version": "3.0"
      //     }
      //   }
      // ]

      // CVE-2023-32040: [
      //   {
      //     "format": "CVSS",
      //     "scenarios": [
      //       {
      //         "lang": "en-US",
      //         "value": "GENERAL"
      //       }
      //     ],
      //     "cvssV3_1": {
      //       "baseSeverity": "HIGH",
      //       "baseScore": 5.5,
      //       "vectorString": "CVSS:3.1/AV:L/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N/E:U/RL:O/RC:C",
      //       "version": "3.1"
      //     }
      //   }
      // ]

      // CVE-2023-5063: {
      //     "exploitabilityScore": 3.1,
      //     "cvssV3": {
      //         "baseSeverity": "MEDIUM",
      //         "confidentialityImpact": "LOW",
      //         "attackComplexity": "LOW",
      //         "scope": "CHANGED",
      //         "attackVector": "NETWORK",
      //         "availabilityImpact": "NONE",
      //         "integrityImpact": "LOW",
      //         "privilegesRequired": "LOW",
      //         "baseScore": 6.4,
      //         "vectorString": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:C/C:L/I:L/A:N",
      //         "version": "3.1",
      //         "userInteraction": "NONE"
      //     },
      //     "impactScore": 2.7
      // }
      let newArr = []
      if (!Array.isArray(arr)) {
        let tempKeys = Object.keys(arr).filter(v => v.startsWith('cvssV'))
        tempKeys.forEach(key => {
          newArr.push(arr[key])
        })
      } else {
        arr.forEach(item => {
          let tempKeys = Object.keys(item).filter(v => v.startsWith('cvssV'))
          tempKeys.forEach(key => {
            newArr.push(item[key])
          })
        })
      }
      if (newArr.length === 0) {
        return {
          v3: [],
          v2: [],
        }
      }
      // let list = newArr
      //   .map(item => {
      //     return Object.values(item)
      //   })
      //   .flat()
      let obj = {
        v3: newArr.filter(v => Number(v.version) >= 3.0),
        v2: newArr.filter(v => Number(v.version) < 3.0),
      }
      return obj
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .v-text-field > .v-input__control > .v-input__slot:before {
  width: 0;
}
::v-deep .v-input--is-disabled input {
  color: var(--v-color-base);
}
.col-6 {
  padding: 0 1rem 1rem;
}

::v-deep
  .son-table.v-data-table
  > .v-data-table__wrapper
  > table
  > thead
  > tr:last-child
  > th,
::v-deep
  .son-table.v-data-table
  > .v-data-table__wrapper
  > table
  > tbody
  > tr:not(:last-child)
  > td:not(.v-data-table__mobile-row),
::v-deep
  .son-table.v-data-table
  > .v-data-table__wrapper
  > table
  > tbody
  > tr:not(:last-child)
  > th:not(.v-data-table__mobile-row) {
  border-width: 0 !important;
}
</style>
