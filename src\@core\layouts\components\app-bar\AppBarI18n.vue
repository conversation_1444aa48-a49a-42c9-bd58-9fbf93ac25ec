<template>
  <!-- nudge-bottom="22" -->
  <v-menu
    offset-y
    min-width="175"
    left
    :elevation="$vuetify.theme.dark ? 9 : 8"
  >
    <!-- Activator -->
    <!-- <template v-slot:activator="{ on, attrs }">
      <div
        v-bind="attrs"
        class="d-flex align-center"
        v-on="on"
        v-if="mode === 'login'"
      >
        <template>
          <span class="text-ml">{{
            locales.find(l => l.locale === $i18n.locale).title
          }}</span>
          <v-icon class="ml-1">mdi-chevron-down</v-icon>
        </template>
      </div>
      <v-btn small color="white" v-bind="attrs" v-on="on" icon v-else>
        <vsoc-icon type="fill" icon="icon-duoyuyan" />
      </v-btn>
    </template> -->

    <!-- Options List -->
    <v-list :class="{ 'login-bg-white': mode === 'login' }" class="pa-0">
      <v-list-item-group :value="$i18n.locale" @change="updateActiveLocale">
        <v-list-item
          v-for="locale in locales"
          :key="locale.locale"
          :value="locale.locale"
          :active-class="mode === 'login' ? 'primary white--text' : 'bg-btn'"
          style="height: 3.8333rem"
        >
          <vsoc-icon type="fill" class="mr-2" :icon="locale.icon"></vsoc-icon>
          <v-list-item-title class="text-ml">{{
            locale.title
          }}</v-list-item-title>
        </v-list-item>
      </v-list-item-group>
    </v-list>
  </v-menu>
</template>

<script>
import { loadLanguageAsync } from '@/plugins/i18n'
import store from '@/store'
import { getToken } from '@/util/token'
import useAppConfig from '@core/@app-config/useAppConfig'
import { getCurrentInstance, onMounted } from '@vue/composition-api'
export default {
  props: {
    mode: {
      type: String,
      default: 'layout',
    },
  },
  setup() {
    const vm = getCurrentInstance().proxy
    const { isRtl } = useAppConfig()

    const locales = [
      {
        title: '中文',
        locale: 'zh-CN',
        icon: 'icon-zhongwen',
      },
      {
        title: 'English',
        locale: 'en',
        icon: 'icon-yingwen',
      },
    ]

    onMounted(() => {
      // console.log('$i18n', vm.$i18n)
    })

    const updateActiveLocale = locale => {
      // Set to RTL mode if locale is arabic
      // isRtl.value = locale === 'ar'

      loadLanguageAsync(locale)
      if (getToken()) {
        store.dispatch('enums/getEnums')
      }
    }

    return {
      locales,
      updateActiveLocale,
    }
  },
}
</script>
<style lang="scss" scoped>
.login-bg-white {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(12px);
  .theme--light.v-list-item:not(.v-list-item--active):not(
      .v-list-item--disabled
    ) {
    color: $white;
  }
}
.theme--light.v-list-item--active::before {
  opacity: 0;
}
</style>
