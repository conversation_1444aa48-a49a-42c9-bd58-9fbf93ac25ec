const simulator = {
  currentTitle: '场景',
  headers: {
    name: '场景名称',
    desc: '描述',
    messages: '消息数量',
    updateDate: '最后更新时间',
  },
  copySimulator: '复制场景',
  simulation: '模拟场景',
  edit: {
    simulateMessage: '模拟消息',
    messageType: {
      online: '在线编辑',
      upload: '文件上传',
    },
    signalType: '信号类型',
    addSubkey: '添加子项',
    date: '日期',
    time: '时间',
    add: '添加',
  },
  hint: {
    formatError: '数据格式错误！',
    jsonError: '模拟消息不是有效的JSON格式，请检查！',
    assetTypeEmpty: '请先选择资产类型',
    assetTypeRequired: '资产类型是必选的',
    messageEmpty: '模拟消息不能为空',
  },
  swal: {
    title: '删除@:simulator.simulation',
    text: '确认删除@:simulator.simulation{name}？',
  },
  // 场景模拟
  exec: {
    select: '选择场景',
    selected: '已选场景',
    channel: '数据通道',
    assetMode: '资产模式',
    dateModel: '时间模式',
    execution: '执行次数',
    assetOption: {
      new: '新建',
      newText: '新建：每次运行都会生成一个新的资产',
      original: '指定资产',
      originalText: '指定资产：每次运行都将使用文件中指定的资产',
    },
    dateOption: {
      now: '现在',
      nowText: '现在：此模式将生成当前日期的信号',
      original: '指定时间',
      originalText: '指定时间：此模式将生成以文件中指定日期的信号',
    },
    message: '{num} 条消息',
    hint1: '请选择场景！',
    hint2: '未查询到结果！',
    search: '搜索场景',
    result: '执行结果',
    success: '场景仿真执行成功！',
    error: '场景仿真执行失败！',
    executing: '执行中...',
    name: '场景',
    totalMessage: '总消息',
  },
  custom: '自定义场景',
  json: '上传JSON文件',
}

export default simulator
