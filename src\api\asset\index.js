import { request, vsocPath } from '../../util/request'

export const getVin = function (data) {
  return request({
    url: `${vsocPath}/vehicleAsset/getEncryptVin`,
    method: 'post',
    data,
    params: data,
  })
}

export const delDigitalTwin = function (data) {
  return request({
    url: `${vsocPath}/vehicleAsset/deleteVehicle `,
    method: 'post',
    data,
  })
}

// 根据资产轴id查询资产轴的详情信息
export const getEventAxisDetails = function (data) {
  return request({
    url: `${vsocPath}/vehicleAsset/findEventAxisDetails`,
    method: 'post',
    data,
  })
}

// 资产实例基础详情
export const getVehicleDetails = function (data) {
  return request({
    url: `${vsocPath}/vehicleAsset/vehicleDetails`,
    method: 'post',
    // loading: true,
    data,
  })
}

// 获取资产实例信息
export const vehicles = function (data) {
  return request({
    url: `${vsocPath}/vehicleAsset/vehicles`,
    method: 'post',
    data,
  })
}

// 获取全量资产实例信息
export const queryVehicleByCondition = function (data) {
  return request({
    url: `${vsocPath}/vehicleAsset/queryVehicleByCondition`,
    method: 'post',
    data,
  })
}

// 根据资产id添加资产组
export const addRelationByAssetId = function (data) {
  return request({
    url: `${vsocPath}/assetGroupRelation/addRelationByAssetId`,
    method: 'post',
    data,
  })
}

export const downloadVehicleTemplate = function (data) {
  return request({
    url: `${vsocPath}/vehicleAsset/downloadVehicleTemplate`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-download',
    },
    responseType: 'blob',
  })
}
