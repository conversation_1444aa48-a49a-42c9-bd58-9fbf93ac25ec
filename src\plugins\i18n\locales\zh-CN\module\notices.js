const notices = {
  currentTitle: '公告',
  headers: {
    title: '标题',
    receiverType: '收件人',
    sendStatus: '发送状态',
    sendCount: '发送人数',
    readCount: '阅读人数',
    readingPercentage: '打开率',
    createDate: '创建时间',
    sendDate: '发送时间',
    content: '消息内容',
  },
  sendTitle: {
    title: '公告标题',
    content: '公告内容',
    object: '通知对象',
    time: '发布时间',
    user: '选择用户',
    timeRange: '选择时间范围',
  },
  readInfo: {
    title: '公告阅读情况',
    user: '用户',
    status: '阅读状态',
  },
  btn: {
    publish: '发布公告',
    more: '查看更多消息',
    previous: '查看历史消息',
  },
  hint: {
    nodata: '暂无消息',
  },
}

export default notices
