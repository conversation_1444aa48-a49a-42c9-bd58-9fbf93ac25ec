const grid = {
  currentTitle: '画布',
  headers: {
    name: '画布名称',
    title: '画布标题',
    titleEnName: '画布英文标题',
    time: '有效期至',
    url: '分享链接',
  },
  share: {
    copyBtn: '复制分享链接',
    btn: '开启分享',
    tip: '将此画布通过下面的链接分享给没有VSOC账号的人',
    delTitle: '禁用这个分享链接？',
    delTip:
      '会取消已存在的链接。可以重新生效，但是链接会变更。是否确认执行此操作？',
  },

  screen: {
    edit: '你正在编辑看板',
    view: '你正在预览看板',
    btn: '设计看板',
    btn1: '选择模板',
    select: '可选图形',
    grid: '选择默认模板',
  },

  tip: '请选择画布背景',
  bg1: '背景一',
  bg2: '背景二',
  bg3: '背景三',
  edit1: '编辑画布',
  edit2: '设计画布',
  add: '新增画布',
  delete: {
    title: '删除画布',
    text: '确定删除画布：{0}？',
  },
  hint: 'Token不能为空',
  link: '生成链接',
  hint1: '分享链接生成成功',
}

export default grid
