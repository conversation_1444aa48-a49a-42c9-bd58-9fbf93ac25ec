<!-- 信号管理高级查询 -->
<template>
  <vsoc-drawer
    :title="$t('action.advanced')"
    :value="value"
    temporary
    @click:confirm="doQuery"
    @input="close"
    @click:close="close"
    @click:cancel="close"
    class="color-base"
  >
    <!-- <template #title>
      <v-btn
        class="no-hb ml-4"
        text
        color="#fff"
        @click="clearAdvanceQuery"
      >
        重置
      </v-btn>
    </template> -->
    <template #right-title>
      <v-btn
        icon
        class="no-hb ml-4"
        text
        color="#fff"
        @click="clearAdvanceQuery"
      >
        <v-icon>mdi-filter-variant-remove</v-icon>
      </v-btn>
    </template>
    <!-- <div class="text-lg font-weight-semibold mb-4">
      {{ $t('signal.headers.name') }}
    </div> -->
    <v-text-field
      v-model="advanceQuery.queryField"
      dense
      class="mt-6"
      color="primary"
      hide-details
      :label="`Id | ${$t('signal.headers.name')}`"
    ></v-text-field>
    <!-- <div class="text-lg font-weight-semibold my-4">
      {{ $t('signal.headers.valueType') }}
    </div> -->
    <v-autocomplete
      v-model="advanceQuery.signalValueTypes"
      item-value="key"
      multiple
      color="primary"
      :menu-props="{ offsetY: true, maxHeight: 300 }"
      append-icon="mdi-chevron-down"
      hide-details
      :items="signalValueTypeList"
      :label="$t('signal.headers.valueType')"
      chips
      class="mt-6"
    >
      <template v-slot:selection="{ item, index }">
        <v-chip
          label
          close
          color="primary"
          class="text-white"
          @click:close="removeTags(index)"
        >
          <span class="text-caption ls-0">{{ item.text }}</span>
        </v-chip>
      </template>
    </v-autocomplete>
  </vsoc-drawer>
</template>

<script>
// import { signalValueTypeList } from '@/components/logic-engine/lib/tools'ss
import VsocDrawer from '@/components/VsocDrawer.vue'

export default {
  name: 'SignalDrawer',
  components: {
    VsocDrawer,
  },
  props: {
    value: Boolean,
  },
  data() {
    return {
      // 信号值类型可选
      signalValueTypeList: [
        {
          text: '字符型(String)',
          oldText: 'Text',
          value: 'STRING',
          key: '0',
        },
        {
          text: '布尔型(Boolean)',
          oldText: 'Boolean',
          value: 'BOOLEAN',
          key: '1',
        },
        {
          text: '小数型(Double)',
          oldText: 'Numeric',
          value: 'DOUBLE',
          key: '2',
        },
        {
          text: '整数型(Long)',
          oldText: 'Whole number',
          value: 'LONG',
          key: '3',
        },
        {
          text: '时间戳(Timestamp)',
          oldText: 'Timestamp',
          value: 'TIMESTAMP',
          key: '4',
        },
        {
          text: '字典型(Dictionary)',
          oldText: 'Structured',
          value: 'DICTIONARY',
          key: '5',
        },
        {
          text: '对象数组(Object array)',
          oldText: 'Structured list',
          value: 'OBJECT_ARRAY',
          key: '6',
        },
        {
          text: '数组型(Array)',
          oldText: 'Array',
          value: 'ARRAY',
          key: '7',
        },
        {
          text: '柱状图(Histogram)',
          oldText: 'Histogram',
          value: 'Histogram',
          key: '8',
        },
      ],
      advanceQuery: {
        name: '',
        dataType: '',
        assetType: ['Vehicle'],
        active: [],
        isEncrypt: [],
        signalValueTypes: [],
        signalType: [],
        page: 1,
        size: 10,
        sort: 'updateDate,desc',
      },
    }
  },
  computed: {
    encryptionEnum() {
      return this.$store.state.enums?.configs?.['Encryption_Mode_Flag']?.[
        'Encryption_Mode_Flag'
      ].propertyValue
    },
    signalDataTypeEnum() {
      return this.$store.state.enums.enums.SignalDataType
    },
    encrypt() {
      // return this.$store.state.enums.enums.Encrypt
      return this.$store.getters['enums/getEncrypt']
    },
    activeEnum() {
      return this.$store.getters['enums/getActiveStatus']
    },
    signalTypeList() {
      const list = []

      for (const key in this.$store.state.enums.enums.SignalType) {
        list.push(this.$store.state.enums.enums.SignalType[key])
      }

      return list
    },

    assetTypeEnum() {
      return this.$store.state.enums.enums.AssetType
    },
  },
  created() {},
  methods: {
    close(bool) {
      if (!bool) {
        this.$emit('input', false)
      }
    },

    clearAdvanceQuery() {
      this.advanceQuery = {
        name: '',
        assetType: [],
        isEncrypt: [],
        signalValueTypes: [],
        signalType: [],
        active: [],
        page: 1,
        size: 10,
        sort: 'updateDate,desc',
      }
    },

    setModel(val) {
      this.advanceQuery = val
    },

    doQuery(cb) {
      this.$emit('do-query', this.advanceQuery)
      cb()
    },

    removeTags(i) {
      this.advanceQuery.signalValueTypes.splice(i, 1)
    },
  },
}
</script>

<style lang="scss" scoped></style>
