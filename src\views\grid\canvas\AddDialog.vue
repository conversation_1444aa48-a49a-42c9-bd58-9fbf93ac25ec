<template>
  <vsoc-drawer
    v-model="isDrawerShow"
    :title="
      mode === 'new'
        ? $t('global.drawer.addTitle', { cur: $generateMenuTitle($route.meta) })
        : $t('global.drawer.editTitle', {
            cur: $generateMenuTitle($route.meta),
          })
    "
    @click:confirm="onSave"
  >
    <v-form ref="form" v-model="valid" lazy-validation>
      <div class="mt-4">
        <v-row class="pl-6 pr-6">
          <v-text-field
            v-model="editForm.name"
            :label="$t('grid.headers.name')"
            required
            :rules="[required(editForm.name, $t('grid.headers.name'))]"
            color="primary"
          >
          </v-text-field>
        </v-row>
        <v-row class="pl-6 pr-6">
          <v-text-field
            v-model="editForm.title"
            :label="$t('grid.headers.title')"
            required
            :rules="[required(editForm.title, $t('grid.headers.title'))]"
            color="primary"
          >
          </v-text-field>
        </v-row>
        <!-- <v-row class="pl-6 pr-6">
          <v-text-field
            v-model="editForm.titleEnName"
            :label="$t('grid.headers.titleEnName')"
            required
            :rules="[
              required(editForm.titleEnName, $t('grid.headers.titleEnName')),
            ]"
            color="primary"
          >
          </v-text-field>
        </v-row> -->
        <!-- <div class="mt-3 text--primary font-weight-semibold-light">
          {{ $t('grid.tip') }}
        </div>
        <v-list class="pa-0">
          <v-list-item-group v-model="editForm.backGroundImg" mandatory>
            <v-list-item
              class="my-3 pa-0"
              v-for="(item, i) in imgItems"
              :key="i"
              :value="item.value"
            >
              <v-list-item-content class="pa-0">
                <v-img
                  height="100%"
                  max-width="100%"
                  :src="require(`@/assets/images/bg5.png`)"
                ></v-img>
                <div class="name-box">{{ item.name }}</div>
              </v-list-item-content>
            </v-list-item>
          </v-list-item-group>
        </v-list> -->
      </div>
    </v-form>
  </vsoc-drawer>
</template>

<script>
import { required } from '@/@core/utils/validation'
import { addCanvas, editCanvas } from '@/api/grid/index'

import VsocDrawer from '@/components/VsocDrawer.vue'

import { deepClone } from '@/util/utils'
export default {
  name: 'editCanvas',
  components: {
    VsocDrawer,
  },
  props: {
    item: {
      type: Object,
      required: false,
    },
    mode: {
      type: String,
      required: false,
      default: () => 'new',
    },
  },
  computed: {
    imgItems() {
      return [
        { value: '1', type: '5', name: this.$t('grid.bg1') },
        { value: '2', type: '4', name: this.$t('grid.bg2') },
        { value: '3', type: '6', name: this.$t('grid.bg3') },
      ]
    },
  },
  data() {
    return {
      required,
      editForm: {},
      valid: true,
      isDrawerShow: false,
    }
  },
  watch: {
    isDrawerShow: {
      handler(newVal) {
        if (newVal) {
          this.$refs.form.resetValidation()
          if (this.mode === 'new') {
            this.editForm = {
              name: '', //画布名称
              title: '', //画布标题
              titleEnName: '', //画布的英文标题
              backGroundImg: '1', //画布的背景
            }
          } else {
            this.editForm = { ...this.item }
            // this.editForm.backGroundImg = Number(this.editForm.backGroundImg)
          }
        }
      },
      immediate: true,
    },
  },
  methods: {
    async onSave(callback) {
      try {
        const bool = this.$refs.form.validate()
        if (!bool) {
          callback(false, true)
          return
        }
        const params = deepClone(this.editForm)
        // params.backGroundImg = params.backGroundImg.toString()
        if (this.mode === 'new') {
          await addCanvas(params)
          this.$notify.info(
            'success',
            this.$t('global.hint.add', [this.$t('grid.currentTitle')]),
          )
        }
        if (this.mode === 'edit') {
          await editCanvas(params)
          this.$notify.info(
            'success',
            this.$t('global.hint.edit', [this.$t('grid.currentTitle')]),
          )
        }
        this.$emit('refresh')
        callback()
      } catch (err) {
        callback(false, true)
      }
    },
  },
}
</script>
<style scoped lang="scss">
.v-list-item {
  width: 180px !important;
  height: 100px !important;
  border-radius: 8px !important;
  .v-list-item__content {
    border-radius: 8px !important;
    width: 100% !important;
    height: 100% !important;
    position: relative;
    .name-box {
      position: absolute;
      left: 12px;
      bottom: 10px;
      color: #fff;
    }
  }
}
.v-list-item--active::before {
  opacity: 0 !important;
}
.v-list-item:hover::before {
  opacity: 0 !important;
}
.v-item--active .v-list-item__content {
  border: 2px solid #ff910f;
}
</style>
