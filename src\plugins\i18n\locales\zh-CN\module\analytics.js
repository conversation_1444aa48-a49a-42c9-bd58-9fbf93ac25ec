const analytics = {
  tab1: '危险资产分析',
  tab2: '告警事件分析',
  totalAssets: '资产总数',
  totalAlerts: '告警总数',
  alertLevel: '告警级别',
  alertStatus: '告警状态',
  affectedModel: '影响车企',
  safeEventTop5: '安全事件Top 5',
  assetLocation: '告警资产位置',
  totalModels: '车企总数',
  affectedAssets: '唯一影响资产总数',
  alertType: '唯一告警类型总数',
  alertLevelRadio: '告警严重级别占比',
  alertTrend: '告警严重级别趋势',
  alertRecord: '告警记录',
  importAssets: '影响资产',
  drawer: {
    detectionRange: '检测范围',
    engineType: '引擎类型',
    vehicleYear: '车辆年份',
    assetId: '资产ID',
    alertId: '告警ID',
    alertType: '告警类型',
    alertModel: '告警车企',
    otherYear: '其他年份',
  },
}

export default analytics
