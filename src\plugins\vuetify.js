import preset from '@/@core/preset/preset'
import Vue from 'vue'
import { <PERSON><PERSON><PERSON>, Scroll } from 'vuetify/lib/directives'
import Vuetify from 'vuetify/lib/framework'
import { i18n } from './i18n'

Vue.use(Vuetify, { directives: { <PERSON><PERSON><PERSON>, Scroll } })

export default new Vuetify({
  preset,
  icons: {
    iconfont: 'mdiSvg',
  },
  theme: {
    options: {
      customProperties: true,
      variations: false,
    },
  },
  // lang: {
  //   locales: { zhHans },
  //   current: 'zhHans',
  // },
  lang: {
    t: (key, ...params) => i18n.t(key, params),
  },
})
