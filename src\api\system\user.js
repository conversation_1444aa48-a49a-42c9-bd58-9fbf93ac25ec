import { request, vsocPath } from '../../util/request'

//查询所有用户
export const findAllUsers = function (params) {
  return request({
    url: `${vsocPath}/user/users`,
    method: 'post',
    data: params,
  })
}

export const unlockUser = function (data) {
  return request({
    url: `${vsocPath}/user/unlockUser`,
    method: 'post',
    data,
  })
}
// 修改密码
export const resetPassword = function (params) {
  return request({
    url: `${vsocPath}/user/resetPassword`,
    method: 'post',
    data: params,
  })
}

// 无参数查询用户信息
export const userInfo = function (params) {
  return request({
    url: `${vsocPath}/user/userInfo`,
    method: 'post',
    data: params,
  })
}

// 获取用户信息
export const findUsers = function (params) {
  return request({
    url: `${vsocPath}/user/findUsers`,
    method: 'post',
    data: params,
  })
}

// 新增用户
export const addUser = function (params) {
  return request({
    url: `${vsocPath}/user/addUser`,
    method: 'post',
    data: params,

    // {
    //   userId: '', // 用户名
    //   userName: '', // 用户名
    //   email: '', // 邮箱
    //   phone: '', // 手机号
    //   fullname: '', // 目前未用到
    //   password: '',
    //   rePassword: '',
    //   roleId: '',
    //   groupId: '',
    // }
  })
}

// 获取角色信息
export const userRole = function (params) {
  return request({
    url: `${vsocPath}/sysRole/userRole`,
    method: 'post',
    data: params,

    // {
    //     "pageNum":1,
    //     "pageSize":5
    // }
  })
}

// 删除用户信息
export const delUser = function (params) {
  return request({
    url: `${vsocPath}/user/delUser`,
    method: 'post',
    params,

    //  http://localhost:8090/user/delUser?id=22
  })
}

// 用户【修改】接口
export const updateUser = function (params) {
  return request({
    url: `${vsocPath}/user/updateUser`,
    method: 'post',
    data: params,
  })
}
