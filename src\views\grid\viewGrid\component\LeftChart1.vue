<template>
  <div class="box" :class="{ 'pb-0': currentTab === 1 }">
    <div class="mh-tab d-flex">
      <!-- 图片格式 -->
      <!-- <div
        :class="
          currentTab === 1
            ? 'tab-border--active'
            : 'tab-border--disabled opacity-4'
        "
        @click="currentTab = 1"
      >
        告警状态分布统计
      </div>
      <div
        :class="
          currentTab === 2
            ? 'tab-border--active'
            : 'tab-border--disabled opacity-4'
        "
        @click="currentTab = 2"
      >
        告警事件类型 Top5
      </div> -->
      <!-- 组件模式 -->
      <dv-border-box-14 :disabled="currentTab !== 1" @click="currentTab = 1">{{
        $t('screen.alertStatus')
      }}</dv-border-box-14>
      <dv-border-box-14 :disabled="currentTab !== 2" @click="currentTab = 2">{{
        $t('screen.alertEvent')
      }}</dv-border-box-14>
    </div>

    <!-- <dv-border-box-12 class="tab-border-box"
      ></dv-border-box-12
    > -->
    <!-- <div class="box-header">影响车型</div> -->
    <!-- <dv-charts :option="option" class="box-chart" /> -->
    <!-- <div
      v-if="currentTab === 1"
      id="car-pie"
      class="box-chart d-flex align-center"
      v-resize="onResize"
    ></div> -->
    <div class="tab-content">
      <tab-item-1
        v-if="currentTab === 1"
        :status="status"
        :total="total"
      ></tab-item-1>
      <tab-item-2 v-if="currentTab === 2" :events="events"> </tab-item-2>
    </div>
  </div>
</template>

<script>
import TabItem1 from '@/views/data-big-screen/component/Tab/TabItem1'
import TabItem2 from '@/views/data-big-screen/component/Tab/TabItem2'
import { getRoundSize } from './chart'
import DvBorderBox14 from './dv-border-box14/Index.vue'
const data = [
  { name: '迈腾', value: 254 },
  { name: '帕萨特', value: 133 },
  { name: '宝来', value: 360 },
  { name: '高尔夫', value: 459 },
]
export default {
  name: 'LeftChart1',
  components: {
    DvBorderBox14,
    TabItem1,
    TabItem2,
  },
  props: {
    status: Array,
    events: Array,
    total: [Number, String],
  },

  data() {
    return {
      currentTab: 1,
      myChart: undefined,
    }
  },
  mounted() {
    // this.onDraw()
  },
  computed: {
    option() {
      return {
        backgroundColor: 'transparent',
        color: ['#035766', '#38487c', '#62799c', '#446377'],
        textStyle: {
          fontFamily: 'Fontquan-XinYiGuanHeiTi',
          fontStyle: 'normal',
          fontWeight: 400,
          color: '#ffffffcc',
        },
        title: {
          text: `${this.$t('screen.model')}\n${this.total}`,
          top: '36%',
          left: '49%',
          // top: 'center',
          // left: 'center',
          textAlign: 'center',
          textStyle: {
            fontSize: getRoundSize(22),
            lineHeight: getRoundSize(26),
            letterSpacing: 0.08,
            color: '#FFFFFF',
            opacity: 0.8,
          },
        },
        series: [
          {
            name: '里面的环',
            type: 'pie',
            // radius: [40, 45],
            radius: ['39%', '45%'],
            emphasis: {
              scale: false,
            },
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
            color: '#103d60',
            data: [{ value: 1548, name: '填充的值' }],
          },
          {
            name: '中间的环',
            type: 'pie',
            percentPrecision: 0,
            radius: ['50%', '67%'],
            // radius: [50, 70],
            // height: "33.33%",
            left: 'center',
            width: '90%',
            emphasis: {
              scale: false,
            },
            label: {
              show: true,
              alignTo: 'edge',
              formatter: '{name|{b}}\n{value|{c}\t({d}%)}',
              minMargin: 5,
              edgeDistance: 10,
              lineHeight: 15,
              rich: {
                value: {
                  fontSize: getRoundSize(10),
                  lineHeight: getRoundSize(18),
                  fontFamily: 'PingFang HK',
                  fontStyle: 'normal',
                  fontWeight: 400,
                  color: '#FFFFFF',
                  opacity: 0.8,
                },
                name: {
                  fontSize: getRoundSize(14),
                  lineHeight: getRoundSize(18),
                  fontFamily: 'PingFang HK',
                  fontStyle: 'normal',
                  fontWeight: 400,
                  color: '#FFFFFF',
                  opacity: 0.8,
                },
              },
            },
            labelLine: {
              show: true,
              length: 15,
              length2: 0,
              // maxSurfaceAngle: 80,
              // minTurnAngle: 90,
              lineStyle: {
                width: 3,
              },
            },
            data: this.alertData.status,
          },
          // {
          //   name: '外面的环',
          //   type: 'pie',
          //   percentPrecision: 0,
          //   radius: ['70%', '85%'],
          //   // radius: [50, 70],
          //   // height: "33.33%",
          //   left: 'center',
          //   width: '90%',
          //   emphasis: {
          //     scale: false,
          //   },
          //   itemStyle: {
          //     color: 'transparent',
          //   },

          //   label: {
          //     show: true,
          //     alignTo: 'edge',
          //     formatter: '{name|{b}}\n{value|{c}\t({d}%)}',
          //     minMargin: 5,
          //     edgeDistance: 10,
          //     lineHeight: 15,
          //     rich: {
          //       value: {
          //         fontSize: getRoundSize(10),
          //         lineHeight: getRoundSize(18),
          //         fontFamily: 'PingFang HK',
          //         fontStyle: 'normal',
          //         fontWeight: 400,
          //         color: '#FFFFFF',
          //         opacity: 0.8,
          //       },
          //       name: {
          //         fontSize: getRoundSize(14),
          //         lineHeight: getRoundSize(18),
          //         fontFamily: 'PingFang HK',
          //         fontStyle: 'normal',
          //         fontWeight: 400,
          //         color: '#FFFFFF',
          //         opacity: 0.8,
          //       },
          //     },
          //   },
          //   labelLine: {
          //     show: true,
          //     length: 15,
          //     length2: 0,
          //     maxSurfaceAngle: 80,
          //     minTurnAngle: 90,
          //     lineStyle: {
          //       color: '#fff',
          //       width: 1,
          //     },
          //   },
          //   data: data,
          // },
        ],
      }
    },
    clientWidth() {
      return this.$store.state.global.clientWidth
    },
  },
  methods: {
    onResize() {
      this.$nextTick(() => {
        this.myChart.setOption(this.option)
        this.myChart.resize()
      })
    },
    onDraw() {
      const ele = document.getElementById('car-pie')
      this.myChart = this.$echarts.init(ele, null, {
        height: 'auto',
        width: 'auto',
        renderer: 'svg',
      })

      // const labelLayout = params => {
      //   const isLeft = params.labelRect.x < this.myChart.getWidth() / 2
      //   const points = params.labelLinePoints
      //   points[2][0] = isLeft
      //     ? params.labelRect.x
      //     : params.labelRect.x + params.labelRect.width
      //   return {
      //     labelLinePoints: points,
      //   }
      // }
      // Object.assign(this.option.series[1], { labelLayout })
      this.myChart.setOption(this.option)
    },
  },
}
</script>
<style lang="scss" scoped>
// .box-chart {
//   // margin-top: 5%;
//   // margin-left: -4px;
//   background: url('../images/box-pie.png') no-repeat;
//   background-size: cover;
//   background-position: center;
// }
</style>
