<template>
  <edit-page :popsName="mode === 'add' ? '新增车企' : '编辑车企'">
    <div slot="BreadBtn">
      <v-btn
        color="primary"
        elevation="0"
        class="me-2"
        :loading="confirmLoading"
        @click="onConfirm"
      >
        保存
      </v-btn>
      <v-btn color="grey" elevation="0" outlined @click="onCancel">
        取消
      </v-btn>
    </div>

    <div class="edit-center-box pb-9">
      <v-form ref="form" v-model="valid">
        <!-- 基础信息 -->
        <div class="text-title color-base font-weight-semibold-light mb-4 mt-6">
          基础信息
          <span
            class="pl-2 text-caption text--secondary"
            v-if="mode === 'edit'"
            >{{ formData.id }}</span
          >
        </div>
        <div class="px-9">
          <v-row>
            <!-- 第一行：品牌、车企名称 -->
            <v-col class="mr-6">
              <v-text-field
                v-model="formData.brand"
                label="品牌"
                color="primary"
                :rules="[rules.required]"
                required
              ></v-text-field>
            </v-col>
            <v-col>
              <v-text-field
                v-model="formData.automaker_name"
                label="车企名称"
                color="primary"
                :rules="[rules.required]"
                required
              ></v-text-field>
            </v-col>
          </v-row>
          <v-row>
            <!-- 第二行：车企编码、已监管 -->
            <v-col class="mr-6">
              <v-text-field
                v-model="formData.automaker_code"
                label="车企编码"
                color="primary"
                :rules="[rules.required]"
                required
              ></v-text-field>
            </v-col>
            <v-col>
              <v-select
                v-model="formData.is_supervise"
                label="已监管"
                color="primary"
                :items="superviseOptions"
                item-text="text"
                item-value="value"
                :rules="[rules.required]"
                required
              ></v-select>
            </v-col>
          </v-row>
          <v-row>
            <!-- 第三行：所在地、详细地址 -->
            <v-col class="mr-6">
              <v-text-field
                v-model="formData.headquarters_location"
                label="所在地"
                color="primary"
              ></v-text-field>
            </v-col>
            <v-col>
              <v-text-field
                v-model="formData.address"
                label="详细地址"
                color="primary"
              ></v-text-field>
            </v-col>
          </v-row>

          <!-- 描述（单独一行，因为是多行文本） -->
          <v-textarea
            v-model="formData.description"
            label="描述"
            :rows="3"
            color="primary"
            outlined
          ></v-textarea>
        </div>

        <!-- LOGO上传 -->
        <div class="text-title color-base font-weight-semibold-light mb-5 mt-8">
          车企LOGO
          <v-btn
            icon
            small
            class="ml-2"
            @click="showLogoTipsDialog = true"
            v-show-tips="'查看上传要求'"
          >
            <v-icon size="18" color="grey">mdi-information-outline</v-icon>
          </v-btn>
        </div>
        <div class="px-9">
          <div class="d-flex justify-start">
            <el-upload
              ref="logoUpload"
              class="logo-uploader"
              :action="vsocPath + '/file/upload'"
              :headers="headers"
              :show-file-list="false"
              :before-upload="beforeLogoUpload"
              accept=".jpg,.jpeg,.png,.svg"
            >
              <div v-if="formData.picture_code" class="logo-preview">
                <img
                  :src="formData.picture_code"
                  alt="LOGO"
                  class="logo-image"
                />
                <div class="logo-overlay">
                  <v-icon color="white" size="24">mdi-camera</v-icon>
                </div>
              </div>
              <div v-else class="logo-placeholder">
                <v-icon size="28" color="primary">mdi-plus</v-icon>
                <div
                  class="mt-1 text-caption text--secondary"
                  style="font-size: 10px"
                >
                  点击上传
                </div>
              </div>
            </el-upload>
          </div>
        </div>

        <!-- LOGO上传要求Dialog -->
        <v-dialog v-model="showLogoTipsDialog" max-width="400">
          <v-card>
            <v-card-title class="text-h6">
              LOGO上传要求
              <v-spacer></v-spacer>
              <v-btn icon @click="showLogoTipsDialog = false">
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </v-card-title>
            <v-card-text>
              <div class="upload-requirements">
                <div class="requirement-item mb-3">
                  <v-icon size="20" color="success" class="mr-2"
                    >mdi-check-circle</v-icon
                  >
                  <span>支持 JPG、JPEG、PNG 格式</span>
                </div>
                <div class="requirement-item mb-3">
                  <v-icon size="20" color="success" class="mr-2"
                    >mdi-check-circle</v-icon
                  >
                  <span>文件大小不超过 150KB</span>
                </div>
                <div class="requirement-item mb-3">
                  <v-icon size="20" color="success" class="mr-2"
                    >mdi-check-circle</v-icon
                  >
                  <span>建议尺寸：200x200像素</span>
                </div>
                <div class="requirement-item">
                  <v-icon size="20" color="info" class="mr-2"
                    >mdi-lightbulb-outline</v-icon
                  >
                  <span>建议使用透明背景的PNG格式</span>
                </div>
              </div>
            </v-card-text>
          </v-card>
        </v-dialog>

        <!-- 车企预览图上传 -->
        <div class="text-title color-base font-weight-semibold-light mb-5 mt-8">
          车企预览图（最多3张）
          <v-btn
            icon
            small
            class="ml-2"
            @click="showImagesTipsDialog = true"
            v-show-tips="'查看上传要求'"
          >
            <v-icon size="18" color="grey">mdi-information-outline</v-icon>
          </v-btn>
        </div>
        <div class="px-9">
          <div class="images-upload-section">
            <el-upload
              ref="imagesUpload"
              class="images-uploader"
              :action="vsocPath + '/file/upload'"
              :headers="headers"
              :file-list="previewImages"
              :on-success="handleImagesSuccess"
              :on-remove="handleImagesRemove"
              :before-upload="beforeImagesUpload"
              :limit="3"
              :on-exceed="handleImagesExceed"
              list-type="picture-card"
              accept=".jpg,.jpeg,.png"
              :data="{ moduleName: 'automaker' }"
            >
              <div class="upload-btn">
                <v-icon size="32" color="primary">mdi-plus</v-icon>
                <div class="text-caption text--secondary mt-1">上传图片</div>
              </div>
            </el-upload>
          </div>
        </div>

        <!-- 车企预览图上传要求Dialog -->
        <v-dialog v-model="showImagesTipsDialog" max-width="400">
          <v-card>
            <v-card-title class="text-h6">
              车企预览图上传要求
              <v-spacer></v-spacer>
              <v-btn icon @click="showImagesTipsDialog = false">
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </v-card-title>
            <v-card-text>
              <div class="upload-requirements">
                <div class="requirement-item mb-3">
                  <v-icon size="20" color="success" class="mr-2"
                    >mdi-check-circle</v-icon
                  >
                  <span>支持 JPG、JPEG、PNG 格式</span>
                </div>
                <div class="requirement-item mb-3">
                  <v-icon size="20" color="success" class="mr-2"
                    >mdi-check-circle</v-icon
                  >
                  <span>文件大小不超过 250KB</span>
                </div>
                <div class="requirement-item mb-3">
                  <v-icon size="20" color="success" class="mr-2"
                    >mdi-check-circle</v-icon
                  >
                  <span>建议尺寸：800x600像素（4:3比例）</span>
                </div>
                <div class="requirement-item mb-3">
                  <v-icon size="20" color="success" class="mr-2"
                    >mdi-check-circle</v-icon
                  >
                  <span>最多上传3张图片</span>
                </div>
                <div class="requirement-item">
                  <v-icon size="20" color="info" class="mr-2"
                    >mdi-lightbulb-outline</v-icon
                  >
                  <span>用于展示车企场景和特色</span>
                </div>
              </div>
            </v-card-text>
          </v-card>
        </v-dialog>
      </v-form>
    </div>
  </edit-page>
</template>

<script>
import {
  addAutomaker,
  editAutomaker,
  getAutomakerDetail,
} from '@/api/asset/automaker'
import editPage from '@/components/EditPage.vue'
import { vsocPath } from '@/util/request'
import { uploadImgToBase64 } from '@/util/utils'

export default {
  name: 'AutomakerEdit',
  components: {
    editPage,
  },
  data() {
    return {
      vsocPath,
      valid: false,
      confirmLoading: false,
      mode: 'add', // add 或 edit
      formData: {
        id: '',
        brand: '',
        automaker_name: '',
        automaker_code: '',
        picture_code: '',
        description: '',
        is_supervise: '',
        headquarters_location: '',
        address: '',
      },
      previewImages: [],
      rules: {
        required: value => !!value || '此字段为必填项',
      },
      superviseOptions: [
        { text: '是', value: '0' },
        { text: '否', value: '1' },
      ],
      showLogoTipsDialog: false,
      showImagesTipsDialog: false,
      fileIds: [], // 存储上传文件的ID数组
      logoUploading: false, // LOGO上传loading状态
      imageUploading: false, // 预览图上传loading状态
    }
  },
  computed: {
    headers() {
      return this.$store.getters['global/getFileHeaders']
    },
  },
  created() {
    this.initPage()
  },
  methods: {
    // 重试机制工具函数
    async retryRequest(requestFn, maxRetries = 3, delay = 1000) {
      for (let i = 0; i < maxRetries; i++) {
        try {
          return await requestFn()
        } catch (error) {
          if (i === maxRetries - 1) {
            throw error
          }
          // 等待指定时间后重试
          await new Promise(resolve => setTimeout(resolve, delay * (i + 1)))
        }
      }
    },
    initPage() {
      // 根据路由判断是新增还是编辑模式
      if (this.$route.path.includes('/add')) {
        this.mode = 'add'
        this.resetForm()
      } else if (this.$route.path.includes('/edit')) {
        this.mode = 'edit'
        const id = this.$route.query.id
        if (id) {
          this.loadAutomakerData(id)
        }
      }
    },

    async loadAutomakerData(id) {
      try {
        // 调用真实的后台接口
        const res = await getAutomakerDetail({ id })

        // 数据字段映射：后台字段 -> 前端字段
        this.formData = {
          id: res.data.id,
          brand: res.data.brand,
          automaker_name: res.data.automakerName,
          automaker_code: res.data.automakerCode,
          picture_code: res.data.pictureCode,
          description: res.data.description,
          is_supervise: res.data.isSupervise,
          headquarters_location: res.data.headquartersLocation,
          address: res.data.address,
        }

        // 处理预览图数据和fileIds
        if (res.data.fileIds && Array.isArray(res.data.fileIds)) {
          // fileIds数组包含文件路径（用于提交）
          this.fileIds = [...res.data.fileIds]
        }

        // 处理预览图显示（使用previewAttachments中的完整URL）
        if (
          res.data.previewAttachments &&
          Array.isArray(res.data.previewAttachments)
        ) {
          this.previewImages = res.data.previewAttachments.map(
            (imageUrl, index) => ({
              name: `preview_${index}.jpg`,
              url: imageUrl, // 直接使用后台返回的完整URL
              uid: `existing_${index}`,
              status: 'done',
            }),
          )
        }
      } catch (error) {
        this.$notify.info('error', '获取车企信息失败')
        console.error('获取车企信息失败：', error)
      }
    },

    resetForm() {
      this.formData = {
        id: '',
        brand: '',
        automaker_name: '',
        automaker_code: '',
        picture_code: '',
        description: '',
        is_supervise: '',
        headquarters_location: '',
        address: '',
      }
      this.previewImages = []
      this.fileIds = []
      this.$refs.form && this.$refs.form.resetValidation()
    },

    // LOGO上传相关方法
    async beforeLogoUpload(file) {
      const isValidType = [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/svg+xml',
      ].includes(file.type)
      const isValidSize = file.size / 1024 < 150 // 150KB限制

      if (!isValidType) {
        this.$notify.info('error', '只能上传 JPG、JPEG、PNG、SVG 格式的图片')
        return false
      }
      if (!isValidSize) {
        this.$notify.info('error', '图片大小不能超过 150KB')
        return false
      }

      try {
        this.logoUploading = true

        // 车企LOGO直接转换为Base64编码，保存在pictureCode字段中
        let base64 = await uploadImgToBase64(file)

        // 如果是SVG格式，检测是否包含白色并询问用户
        if (file.type === 'image/svg+xml') {
          const hasWhiteColor = await this.detectWhiteInSvg(base64)
          if (hasWhiteColor) {
            // 暂停处理，询问用户意见
            const userChoice = await this.showColorConversionDialog(base64)
            if (userChoice === 'convert') {
              base64 = await this.processSvgColor(base64)
            }
            // 如果选择 'keep'，则保持原始base64不变
          }
        }

        this.formData.picture_code = base64
        // 移除成功提示，只在失败时提示

        return false // 阻止Element UI的自动上传
      } catch (error) {
        this.$notify.info('error', 'LOGO上传失败')
        console.error('LOGO上传失败：', error)
        return false
      } finally {
        this.logoUploading = false
      }
    },

    // 检测SVG中是否包含白色元素
    async detectWhiteInSvg(base64) {
      try {
        const svgContent = atob(base64.split(',')[1])
        const parser = new DOMParser()
        const svgDoc = parser.parseFromString(svgContent, 'image/svg+xml')
        const svgElement = svgDoc.documentElement

        return this.hasWhiteColorInElement(svgElement)
      } catch (error) {
        console.warn('SVG白色检测失败：', error)
        return false
      }
    },

    // 递归检测元素中是否包含白色
    hasWhiteColorInElement(element) {
      if (element.getAttribute) {
        const fill = element.getAttribute('fill')
        const stroke = element.getAttribute('stroke')

        // 检查属性中的白色
        if (this.isWhiteColor(fill) || this.isWhiteColor(stroke)) {
          return true
        }

        // 检查style属性中的白色
        const style = element.getAttribute('style')
        if (style && this.hasWhiteInStyle(style)) {
          return true
        }
      }

      // 递归检查子元素
      if (element.children) {
        for (let i = 0; i < element.children.length; i++) {
          if (this.hasWhiteColorInElement(element.children[i])) {
            return true
          }
        }
      }

      return false
    },

    // 检查样式字符串中是否包含白色
    hasWhiteInStyle(style) {
      const whitePatterns = [
        /fill:\s*#fff(fff)?/i,
        /fill:\s*white/i,
        /fill:\s*rgb\(255,\s*255,\s*255\)/i,
        /stroke:\s*#fff(fff)?/i,
        /stroke:\s*white/i,
        /stroke:\s*rgb\(255,\s*255,\s*255\)/i,
      ]

      return whitePatterns.some(pattern => pattern.test(style))
    },

    // 显示颜色转换确认对话框
    async showColorConversionDialog(originalBase64) {
      return new Promise(resolve => {
        // 生成转换后的预览（用于对比显示）
        this.processSvgColor(originalBase64)
          .then(convertedBase64 => {
            this.$swal({
              title: '颜色转换确认',
              html: `
                <div style="margin: 20px 0;">
                  <p style="margin-bottom: 20px; color: #666;">检测到您上传的SVG LOGO包含白色元素，在白色背景下可能无法清晰显示。是否将白色部分转换为黑色以提高可见性？</p>
                  <div style="display: flex; justify-content: space-around; align-items: center;">
                    <div style="text-align: center;">
                      <div style="margin-bottom: 8px; font-weight: bold;">原始效果</div>
                      <div style="width: 80px; height: 80px; border: 1px solid #ddd; background: #f5f5f5; display: flex; align-items: center; justify-content: center; border-radius: 4px;">
                        <img src="${originalBase64}" style="max-width: 70px; max-height: 70px;" />
                      </div>
                    </div>
                    <div style="text-align: center;">
                      <div style="margin-bottom: 8px; font-weight: bold;">转换后效果</div>
                      <div style="width: 80px; height: 80px; border: 1px solid #ddd; background: #f5f5f5; display: flex; align-items: center; justify-content: center; border-radius: 4px;">
                        <img src="${convertedBase64}" style="max-width: 70px; max-height: 70px;" />
                      </div>
                    </div>
                  </div>
                  <p style="margin-top: 15px; color: #666; font-size: 14px;">建议选择"转换为黑色"以确保LOGO在各种背景下都能清晰显示。</p>
                </div>
              `,
              icon: 'warning',
              reverseButtons: true,
              showCancelButton: true,
              confirmButtonText: '转换为黑色',
              cancelButtonText: '保持原色',
              customClass: {
                confirmButton: 'sweet-btn-primary',
              },
            }).then(result => {
              if (result.isConfirmed) {
                resolve('convert')
              } else {
                resolve('keep')
              }
            })
          })
          .catch(() => {
            // 如果预览生成失败，使用简化对话框
            this.$swal({
              title: '颜色转换确认',
              text: '检测到您上传的SVG LOGO包含白色元素，在白色背景下可能无法清晰显示。是否将白色部分转换为黑色以提高可见性？',
              icon: 'warning',
              reverseButtons: true,
              showCancelButton: true,
              confirmButtonText: '转换为黑色',
              cancelButtonText: '保持原色',
              customClass: {
                confirmButton: 'sweet-btn-primary',
              },
            }).then(result => {
              if (result.isConfirmed) {
                resolve('convert')
              } else {
                resolve('keep')
              }
            })
          })
      })
    },

    // 处理SVG颜色，将白色转换为黑色
    async processSvgColor(base64) {
      try {
        // 从base64中提取SVG内容
        const svgContent = atob(base64.split(',')[1])

        // 创建一个临时的DOM解析器
        const parser = new DOMParser()
        const svgDoc = parser.parseFromString(svgContent, 'image/svg+xml')
        const svgElement = svgDoc.documentElement

        // 处理各种白色填充情况
        this.replaceSvgColors(svgElement)

        // 将修改后的SVG转换回base64
        const serializer = new XMLSerializer()
        const modifiedSvgString = serializer.serializeToString(svgElement)
        const modifiedBase64 =
          'data:image/svg+xml;base64,' + btoa(modifiedSvgString)

        return modifiedBase64
      } catch (error) {
        console.warn('SVG颜色处理失败，使用原始文件：', error)
        return base64
      }
    },

    // 递归替换SVG中的白色
    replaceSvgColors(element) {
      // 处理当前元素的填充和描边
      if (element.getAttribute) {
        const fill = element.getAttribute('fill')
        const stroke = element.getAttribute('stroke')

        // 替换各种白色表示方式
        if (this.isWhiteColor(fill)) {
          element.setAttribute('fill', '#000000')
        }
        if (this.isWhiteColor(stroke)) {
          element.setAttribute('stroke', '#000000')
        }

        // 处理style属性中的颜色
        const style = element.getAttribute('style')
        if (style) {
          let newStyle = style
          newStyle = newStyle.replace(/fill:\s*#fff(fff)?/gi, 'fill:#000000')
          newStyle = newStyle.replace(/fill:\s*white/gi, 'fill:#000000')
          newStyle = newStyle.replace(
            /fill:\s*rgb\(255,\s*255,\s*255\)/gi,
            'fill:#000000',
          )
          newStyle = newStyle.replace(
            /stroke:\s*#fff(fff)?/gi,
            'stroke:#000000',
          )
          newStyle = newStyle.replace(/stroke:\s*white/gi, 'stroke:#000000')
          newStyle = newStyle.replace(
            /stroke:\s*rgb\(255,\s*255,\s*255\)/gi,
            'stroke:#000000',
          )
          element.setAttribute('style', newStyle)
        }
      }

      // 递归处理子元素
      if (element.children) {
        for (let i = 0; i < element.children.length; i++) {
          this.replaceSvgColors(element.children[i])
        }
      }
    },

    // 判断是否为白色
    isWhiteColor(color) {
      if (!color) return false
      const normalizedColor = color.toLowerCase().trim()
      return (
        normalizedColor === '#ffffff' ||
        normalizedColor === '#fff' ||
        normalizedColor === 'white' ||
        normalizedColor === 'rgb(255,255,255)' ||
        normalizedColor === 'rgb(255, 255, 255)'
      )
    },

    // 预览图上传前验证
    beforeImagesUpload(file) {
      const isValidType = ['image/jpeg', 'image/jpg', 'image/png'].includes(
        file.type,
      )
      const isValidSize = file.size / 1024 < 250 // 250KB限制

      if (!isValidType) {
        this.$notify.info('error', '只能上传 JPG、JPEG、PNG 格式的图片')
        return false
      }
      if (!isValidSize) {
        this.$notify.info('error', '图片大小不能超过 250KB')
        return false
      }

      return true // 允许Element UI上传
    },

    // 预览图上传成功回调
    handleImagesSuccess(response, file, fileList) {
      console.log('文件上传响应：', response) // 调试信息

      if (response.code === 200) {
        // 根据协议文档，response.data 直接就是文件路径字符串
        const filePath = response.data

        console.log('提取的文件路径：', filePath) // 调试信息

        // 将文件路径放入fileIds数组中（这是关键！）
        if (filePath) {
          this.fileIds = this.fileIds || []
          this.fileIds.push(filePath)
          console.log('当前fileIds数组：', this.fileIds) // 调试信息
        } else {
          console.warn('未能获取到文件路径，响应结构：', response)
        }

        // 移除成功提示，只在失败时提示
      } else {
        this.$notify.info('error', response.message || '图片上传失败')
      }
    },

    handleImagesRemove(file, fileList) {
      const index = this.previewImages.findIndex(img => img.uid === file.uid)
      if (index > -1) {
        // 释放URL对象内存
        if (
          this.previewImages[index].url &&
          this.previewImages[index].url.startsWith('blob:')
        ) {
          URL.revokeObjectURL(this.previewImages[index].url)
        }

        // 从fileIds数组中移除对应的文件路径
        if (this.fileIds && this.fileIds[index]) {
          this.fileIds.splice(index, 1)
        }

        this.previewImages.splice(index, 1)
      }
    },

    handleImagesExceed(files, fileList) {
      this.$notify.info('warning', '最多只能上传3张预览图')
    },

    async onConfirm() {
      if (!this.$refs.form.validate()) {
        return
      }

      this.confirmLoading = true
      try {
        const submitData = {
          ...this.formData,
          fileIds: this.fileIds, // 文件ID数组
        }

        console.log('提交的数据：', submitData) // 调试信息
        console.log('fileIds数组：', this.fileIds) // 调试信息

        if (this.mode === 'add') {
          // 调用新增接口
          await addAutomaker(submitData)
          this.$notify.info('success', '新增车企成功')
        } else {
          // 调用编辑接口
          await editAutomaker(submitData)
          this.$notify.info('success', '编辑车企成功')
        }

        // 返回列表页面
        this.$router.push('/automaker')
      } catch (error) {
        this.$notify.info(
          'error',
          this.mode === 'add' ? '新增车企失败' : '编辑车企失败',
        )
        console.error('操作失败：', error)
      } finally {
        this.confirmLoading = false
      }
    },

    onCancel() {
      // 返回列表页面
      this.$router.push('/automaker')
    },
  },
}
</script>

<style scoped>
/* 整体布局样式 */
.automaker-center-box {
  max-width: 1200px;
  margin: 0 auto;
}

/* 标题样式 */
.text-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  border-left: 4px solid #1976d2;
  padding-left: 12px;
  margin-top: 32px;
}

/* 上传区域样式 */
.upload-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 120px;
}

.upload-tips {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #1976d2;
}

/* LOGO上传样式 */
.logo-uploader {
  display: inline-block;
}

.logo-preview {
  position: relative;
  width: 80px;
  height: 80px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
}

.logo-preview:hover {
  border-color: #1976d2;
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.2);
}

.logo-preview:hover .logo-overlay {
  opacity: 1;
}

.logo-image {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
}

.logo-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.logo-placeholder {
  width: 80px;
  height: 80px;
  border: 2px dashed #d0d0d0;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #fafafa;
}

.logo-placeholder:hover {
  border-color: #1976d2;
  background-color: #f0f7ff;
}

/* 特色预览图上传样式 */
.images-upload-section {
  background-color: #fafafa;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #e0e0e0;
}

.images-uploader {
  width: 100%;
}

.upload-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

/* 覆盖Element UI的样式 */
.images-uploader ::v-deep .el-upload--picture-card {
  width: 160px;
  height: 120px;
  border: 2px dashed #d0d0d0;
  border-radius: 12px;
  transition: all 0.3s ease;
  background-color: #ffffff;
}

.images-uploader ::v-deep .el-upload--picture-card:hover {
  border-color: #1976d2;
  background-color: #f0f7ff;
}

.images-uploader ::v-deep .el-upload-list--picture-card .el-upload-list__item {
  width: 160px;
  height: 120px;
  border-radius: 12px;
  border: 2px solid #e0e0e0;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.images-uploader
  ::v-deep
  .el-upload-list--picture-card
  .el-upload-list__item:hover {
  border-color: #1976d2;
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.2);
}

.images-uploader
  ::v-deep
  .el-upload-list--picture-card
  .el-upload-list__item-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
}

/* 删除按钮样式优化 */
.images-uploader
  ::v-deep
  .el-upload-list--picture-card
  .el-upload-list__item-actions {
  background: rgba(0, 0, 0, 0.6);
  border-radius: 10px;
}

/* 进度条样式 */
.images-uploader ::v-deep .el-progress {
  width: 90% !important;
}

/* Dialog样式优化 */
.upload-requirements {
  padding: 8px 0;
}

.requirement-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  line-height: 1.5;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .automaker-center-box {
    padding: 0 16px;
  }

  .upload-tips {
    margin-top: 16px;
  }

  .logo-preview,
  .logo-placeholder {
    width: 70px;
    height: 70px;
  }

  .images-uploader ::v-deep .el-upload--picture-card,
  .images-uploader
    ::v-deep
    .el-upload-list--picture-card
    .el-upload-list__item {
    width: 140px;
    height: 100px;
  }
}

/* SVG颜色转换对话框样式 */
::v-deep .svg-color-dialog .el-message-box {
  width: 480px;
}

::v-deep .svg-color-dialog .el-message-box__message {
  text-align: left;
}

::v-deep .svg-color-dialog .el-message-box__btns {
  text-align: center;
  padding: 20px 20px 30px;
}

::v-deep .svg-color-dialog .el-button--primary {
  background-color: #1976d2;
  border-color: #1976d2;
}

::v-deep .svg-color-dialog .el-button--primary:hover {
  background-color: #1565c0;
  border-color: #1565c0;
}
</style>
