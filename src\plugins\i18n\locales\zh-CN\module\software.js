const software = {
  currentTitle: '软件资产',
  headers: {
    id: 'ID',
    type: '分类',
    customName: '自定义名称',
    productName: '产品名称',
    parentProductName: '上级产品名称',
    version: '版本号',
    supplier: '供应商',
    sourceLink: '源链接',
    cpe: '通用平台枚举(CPE)',
    recommendedVersion: '推荐版本',
    hashType: '哈希类型',
    hashValue: '哈希值',
    partName: '所属零部件',
    supplierName: '所属供应商',
    vehiclePlatformName: '所属平台',
    status: '资产状态',
    notes: '备注',
  },
  card: {
    platformCount: '平台数量',
    platCount: '零部件数量',
    supplierCount: '供应商数量',
    componentCount: '组件数量',
  },
  swal: {
    del: {
      title: '删除软件资产',
      text: '是否确认删除软件资产：{0}？',
    },
    batchDel: {
      title: '批量删除软件资产',
      text: '是否确认删除软件资产共{0}条？',
    },
    export: '是否确认导出软件资产共{0}条？',
  },
  hint: {
    del: '请先选择需要删除的软件资产！',
    batchDel: '批量删除软件资产成功！',
    allDel: '全部删除软件资产成功！',
    change: '请先选择需要变更的软件资产！',
    batchChange: '批量变更软件资产成功！',
  },
  allText: '软件资产数据',
  title: {
    add: '新增软件资产',
    edit: '编辑软件资产',
    detail: '软件资产详情',
  },
  template: {
    hint: '下载软件资产导入模板',
    text: '软件资产导入模板',
  },
  fileHint: '单个附件大小不超过20M；附件格式要求：xls,xlsx',
}

export default software
