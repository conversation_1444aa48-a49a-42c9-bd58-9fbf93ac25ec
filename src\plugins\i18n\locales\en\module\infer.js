const infer = {
  currentTitle: 'Infer',
  headers: {
    order: 'Order',
    name: 'Name',
    signal: 'Updated signal',
    event: 'Updated event',
  },
  priority: 'Priority',
  group: {
    title: 'Asset Group',
    applicableType: 'Applicable Type',
    hint: 'Please select an asset group',
    hint1: 'Please select at least an asset group!',
    allGroups: 'All asset',
    selectGroups: 'Include selected groups',
    excludeGroups: 'Exclude selected groups',
  },
  inferLogic: 'Inference Logic',
  conditionalInference: 'Conditional Inference',
  stateSignal: 'State Signal',
  event: 'Event',
  hintEvent1: 'Please select a behavioral event.',
  hintEvent2:
    'When the inference logic is established, behavioral event instances (up to 1 event) will be added to the selected events.',
  hintEvent3:
    'When the inference logic is established, behavioral event instances (up to 1 event) will be added to the selected events.',
  selectEvent: 'Select behavioral events',
  hintSignal:
    'When the inference logic is established, the selected signal information will be updated. (Max 10 signals)',
  hintSignal1:
    'When the inference logic is established, the selected signal information will be updated. (Max 1 signal)',
  select: 'Select',
  method: 'Assignment Method',
  input: 'please enter',
  signalSurvey: 'Signal Survey',
  recommended: 'Recommended signals',
  all: 'All signals',
  search: 'search signal',
  selectedSiganl: 'Selected signal',
  most: 'Select at most {count} items',
  itemSelect: '{count} items selected',
  inferRule:
    'Please specify at least one conditional inference (event or status signal)',
  logicalError:
    'This logical condition is incomplete, please fill in or delete',
  improveLogic: 'Please improve the signal state logic',
  swal: {
    title: 'Delete Infer',
    hint: 'Confirm deletion inference: {name}?',
  },
  include: 'include',
  add: 'Add Infer',
  edit: 'Edit Infer',
  expression: {
    title: 'Timed expression',
    express: 'Expression',
    old: 'Old expression',
    new: 'New expression',
    everyDay: 'Every day',
    hour: 'Hour',
    minute: 'Minute',
    second: 'Second',
    every: 'Every',
    everyMonth: 'Every month',
    day: 'Day',
    week: 'Week',
    month: 'Month',
    custom: 'Custom',
  },
}

export default infer
