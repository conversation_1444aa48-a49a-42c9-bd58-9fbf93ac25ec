<template>
  <component
    :is="resolveLayoutVariant"
    :class="`skin-variant--${appSkinVariant}`"
  >
    <transition :name="appRouteTransition" mode="out-in" appear>
      <router-view></router-view>
    </transition>
  </component>
</template>

<script>
// eslint-disable-next-line object-curly-newline
import { computed, onUnmounted } from '@vue/composition-api'
// eslint-disable-next-line import/no-unresolved
import useAppConfig from '@core/@app-config/useAppConfig'
import { useRouter } from '@core/utils'
import { useLayout } from '@core/layouts/composable/useLayout'

// Layouts
import LayoutContentVerticalNav from '@/layouts/variants/content/vertical-nav/LayoutContentVerticalNav.vue'
import LayoutContentHorizontalNav from '@/layouts/variants/content/horizontal-nav/LayoutContentHorizontalNav.vue'
import LayoutBlank from '@/layouts/variants/blank/LayoutBlank.vue'

// Dynamic vh
import useDynamicVh from '@core/utils/useDynamicVh'

export default {
  components: {
    LayoutContentVerticalNav,
    LayoutContentHorizontalNav,
    LayoutBlank,
  },
  setup() {
    const { route } = useRouter()
    const { appContentLayoutNav, appSkinVariant, appRouteTransition } =
      useAppConfig()

    const { handleBreakpointLayoutSwitch } = useLayout()
    handleBreakpointLayoutSwitch()

    const resolveLayoutVariant = computed(() => {
      if (route.value.meta.layout === 'blank') return 'layout-blank'
      if (route.value.meta.layout === 'content')
        return `layout-content-${appContentLayoutNav.value}-nav`

      return null
    })

    useDynamicVh()

    onUnmounted(() => {
      window.onresize = null
    })

    return {
      resolveLayoutVariant,
      appSkinVariant,
      appRouteTransition,
    }
  },
}
</script>
<style lang="scss">
@font-face {
  font-family: 'Fontquan-XinYiGuanHeiTi';
  src: url('./assets/font/xinyiguangheiti/xinyiguangheiti/ZiTiQuanXinYiGuanHeiTi3.0-2.ttf');
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'HarmonyOS Sans SC';
  src: url('./assets/font/HarmonyOS_Sans_SC/HarmonyOS_Sans_SC_Regular.ttf');
}
@font-face {
  font-family: 'BlinkMacSystemFont';
  src: url('./assets/font/BlinkMacSystemFont/blinkmacsystemfont-regular.ttf');
}
@font-face {
  font-family: 'Segoe UI';
  src: url('./assets/font/Segoe UI/Segoe UI.ttf');
}
//@font-face {
//  font-family: 'Roboto';
//  src: url('./assets/font/Roboto/Roboto Serif Regular.ttf');
//}
@font-face {
  font-family: 'Oxygen';
  src: url('./assets/font/Oxygen/Oxygen-Regular.ttf');
}
@font-face {
  font-family: 'Ubuntu';
  src: url('./assets/font/Ubuntu/Ubuntu-R.ttf');
}
@font-face {
  font-family: 'Cantarell';
  src: url('./assets/font/Cantarell/Cantarell Regular.ttf');
}
@font-face {
  font-family: 'Fira Sans';
  src: url('./assets/font/Fira Sans/FiraSans-Regular-33.ttf');
}
@font-face {
  font-family: 'Droid Sans';
  src: url('./assets/font/Droid Sans/Droid-Sans-Fallback-Regular.ttf');
}
@font-face {
  font-family: 'Helvetica Neue';
  src: url('./assets/font/Helvetica Neue/Helvetica-Neue.ttf');
}
@font-face {
  font-family: 'sans-serif';
  src: url('./assets/font/sans-serif/Times-Sans-Serif.ttf');
}
</style>
