<!-- 资产时间线 -->
<template>
  <div>
    <div ref="timeLine" class="assets-time-line d-flex text-ml">
      <template v-for="(item, i) in timeLineData">
        <div
          :key="i"
          class="assets-time-line__item text-center"
          @click="$_check(i)"
        >
          <!-- 最近一次活动 -->
          <template v-if="item.type === 'RecentActivity'">
            <div class="text--black primary--text font-weight-medium">
              {{ item.eventName | generateEventName(item) }}
            </div>
            <v-btn class="my-4" fab color="primary" width="52" height="52">
              <v-icon size="2rem"> mdi-car </v-icon>
            </v-btn>
            <div>
              {{ item.lastEventDate | getDateDiff }}
            </div>
            <div class="text--primary">
              {{ item.lastEventDate | toDate }}
            </div>
          </template>
          <template v-else>
            <v-tooltip bottom>
              <template v-slot:activator="{ on, attrs }">
                <div
                  class="text--black px-2 text-overflow-hide text--primary"
                  v-bind="attrs"
                  v-on="on"
                >
                  {{ item.eventName | generateEventName(item) }}
                </div>
              </template>
              <span>{{ item.eventName | generateEventName(item) }}</span>
            </v-tooltip>
            <v-avatar
              v-if="['Statistics', 'Alarm'].includes(item.type)"
              width="52px"
              height="52px"
              class="assets-time-line__icon mt-4 mb-5"
              :color="item.color || 'primary'"
            >
              <v-icon size="24" color="white" class="rounded-0">
                mdi-flash
              </v-icon>
            </v-avatar>

            <!-- <v-btn
              v-else
              class="my-4 mb-3"
              fab
              color="secondary"
              width="50"
              height="50"
              style="z-index: 9"
            >
              <v-icon size="2rem"> mdi-car </v-icon>
            </v-btn> -->
            <img
              v-else
              class="assets-time-line__icon my-4"
              src="@/assets/images/svg/timeline-event.svg"
              alt=""
            />
            <div>
              {{ item.lastEventDate | getDateDiff }}
            </div>
            <div
              v-if="item.type === 'Statistics'"
              class="text--black font-weight-semibold"
            >
              <!-- 最近六个月 -->
              {{ $tc('enums.datePresets.lastxm', 2, { num: 6 }) }}
            </div>

            <div v-else class="text--black text--primary">
              {{ item.lastEventDate | toDate }}
            </div>
            <!-- <div
              class="text--primary font-weight-medium"
              :class="{ 'opacity-0': item.type === 'Statistics' }"
              style="height: 1.28rem"
            >
              {{ item.lastEventDate | toDate('yyyy-MM-dd') }}
            </div> -->
          </template>
          <template
            v-if="
              timeLineType === 'alert' &&
              (i === 0 || i === timeLineData.length - 2)
            "
          >
            <span class="assets-time-line__next"></span>
            <!-- <img
              class="assets-time-line__next"
              src="@/assets/images/svg/timeline-next.svg"
              alt=""
              width="100%"
            /> -->
          </template>
          <!-- 最后一个步骤，虚箭头 -->
          <div
            v-else-if="
              timeLineType === 'last-dash-arrow' &&
              i === timeLineData.length - 2
            "
          >
            <span class="assets-time-line__next"></span>
          </div>
          <div
            v-else-if="i < timeLineData.length - 1"
            class="assets-time-line__item__line text--primary"
          >
            {{
              formatDuring(
                timeLineData[i + 1].lastEventDate,
                item.lastEventDate,
              )
            }}
          </div>
          <!-- <img
            class="current-tile-line"
            :class="{ 'check-tile-line': checkTimeLine === i }"
            src="@/assets/images/svg/timeline-check.svg"
            alt=""
          /> -->
          <vsoc-icon
            class="current-tile-line opacity-0 primary--text"
            :class="{ 'check-tile-line': checkTimeLine === i }"
            type="fill"
            size="2.5rem"
            icon="icon-xiangshangjiantou"
          ></vsoc-icon>
        </div>
      </template>
    </div>
    <div
      v-if="isNoData && timeLineData.length === 0"
      class="d-flex flex-column justify-center align-center"
    >
      <v-icon size="2rem" class="text-secondary mb-3">
        mdi-apple-finder
      </v-icon>
      <h6 class="text-h6 text-secondary">{{ $t('global.noData') }}</h6>
    </div>
  </div>
</template>

<script>
import { differenceInMilliseconds } from 'date-fns'
import moment from 'moment'
export default {
  name: 'AssetsTimeLine',
  components: {},
  props: {
    timeLineData: Array,
    timeLineType: {
      type: String,
      default: 'assets',
    },
    isNoData: Boolean,
  },
  data() {
    return {
      checkTimeLine: 0,
      data: [],
      // 事件类型
      typeOption: [
        {
          text: '最近六个月',
          value: 'Statistics',
        },
        {
          text: '正常事件',
          value: 'Event',
        },
        {
          text: '告警事件',
          value: 'Alarm',
        },
        {
          text: 'RecentActivity',
          value: '最近活动',
        },
      ],
    }
  },
  watch: {
    timeLineData() {
      this.setCheckTimeLine()
    },
  },
  created() {},
  methods: {
    $_check(i) {
      this.checkTimeLine = i
      this.$emit('change', i)
    },

    setCheckTimeLine() {
      this.checkTimeLine = this.timeLineData.length - 1
      this.$nextTick(() => {
        this.$refs.timeLine.scrollLeft = 9999
      })
    },

    formatDuring(end, start) {
      if (!end || !start || end === start) {
        return '00:00:00'
      }
      // 去除时间秒后的小数进行取整
      end = end.split('.')[0]
      start = start.split('.')[0]
      // end = new Date()
      // start = new Date('2023-02-17 00:00:00')
      // 计算毫秒数
      const mss = differenceInMilliseconds(new Date(end), new Date(start))
      if (mss < 0) {
        return this.$t('global.diff.error')
      }
      // const days =moment.duration(mss).asDays();
      // console.log(day, 888)
      // const days = parseInt(
      //   (mss % (1000 * 60 * 60 * 24 * 365)) / (1000 * 60 * 60 * 24),
      // )
      // const days = parseInt(mss / (1000 * 60 * 60 * 24))
      // const hours = parseInt((mss % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
      // const minutes = parseInt((mss % (1000 * 60 * 60)) / (1000 * 60))
      // const seconds = parseInt((mss % (1000 * 60)) / 1000)
      // conso
      const duration = moment.duration(mss)
      const days = duration.days()
      const hours = duration.hours()
      const minutes = duration.minutes()
      const seconds = duration.seconds()
      return `${days > 0 ? `${days}Day ` : ''}${
        hours >= 10 ? hours : `0${hours}`
      }:${minutes >= 10 ? minutes : `0${minutes}`}:${
        seconds >= 10 ? seconds : `0${seconds}`
      }`
    },
  },
}
</script>

<style lang="scss" scoped>
.assets-time-line {
  // justify-content: center;
  overflow: auto hidden;
  width: 100%;
  .assets-time-line__item {
    flex: 1;
    min-width: 20rem;
    cursor: pointer;
    position: relative;
    padding-bottom: 1.25rem;
    &:hover {
      .current-tile-line {
        display: inline-block;
        opacity: 0.5;
      }
    }
    .assets-time-line__icon {
      position: relative;
      z-index: 1;
      width: 50px;
      height: 50px;
    }

    .assets-time-line__item__line {
      position: absolute;
      left: 50%;
      width: 100%;
      // min-width: 240px;
      background: var(--v-secondary-base);
      height: 1.5rem;
      top: 32%;
      line-height: 1.5rem;
    }

    @at-root .theme--light .assets-time-line__item__line {
      background: #f5f5f5 !important;
    }

    .assets-time-line__next {
      position: absolute;
      left: 70%;
      width: 60%;
      height: 1.3rem;
      top: 33%;
      // line-height: 1.5rem;
      // background: url('../../../../assets/images/svg/timeline-next.svg') repeat
      //   center;

      background: url('./../../../../assets/images/pages/<EMAIL>');
      background-position: 60% 100%;
      background-size: contain;
      background-repeat: space repeat;
    }

    @at-root .theme--light .assets-time-line__next {
      background-image: url('./../../../../assets/images/pages/<EMAIL>') !important;
    }

    .v-avatar .v-icon {
      height: 30px;
      width: 30px;
    }
    .current-tile-line {
      position: absolute;
      left: 50%;
      bottom: -2%;
      transform: translateX(-50%);
      display: none;
      &.check-tile-line {
        display: inline-block;
        opacity: 1;
      }
    }
  }
}
</style>
