<template>
  <vsoc-drawer
    :title="$t('action.advanced')"
    :value="value"
    @click:confirm="doQuery"
    @input="close"
    @click:close="close"
    @click:cancel="close"
    class="color-base"
  >
    <template #right-title>
      <v-btn icon class="no-hb ml-4" text @click="clearAdvanceQuery">
        <v-icon size="16">mdi-filter-variant-remove</v-icon>
      </v-btn>
    </template>
    <template v-if="currentTab === 0">
      <p class="mt-4 mb-1 text-content">
        {{ $t('vulnerability.headers.alarmLevel') }}
      </p>
      <v-row dense class="ma-0">
        <v-col v-for="item in vulnerabilityLevelEnum" :key="item.value">
          <v-checkbox
            v-model="advanceQuery.vulnerabilityLevelList"
            :value="item.value"
            dense
            hide-details
            class="pt-0 mt-0"
          >
            <template v-slot:label>
              <div class="d-flex">
                <vsoc-icon
                  type="fill"
                  :style="{ color: item.color }"
                  icon="icon-loudongdengjibiaozhi"
                ></vsoc-icon>
                <span class="ml-2">{{ item.text }}</span>
              </div>
            </template>
          </v-checkbox>
        </v-col>
      </v-row>
      <v-text-field
        class="mt-3"
        v-model="advanceQuery.vulnerabilityName"
        :label="$t('vulnerability.headers.vulnerabilityName')"
      >
      </v-text-field>
      <v-select
        v-model="selected"
        return-object
        class="mt-2"
        :items="vulnerabilityDateList"
        :label="$t('vulnerability.vulnerabilityDate')"
        :multiple="false"
        :menu-props="{ offsetY: true }"
      >
        <template v-slot:item="{ item }">
          {{ $t(item.text) }}
        </template>
        <template v-slot:selection="{ item }"> {{ $t(item.text) }}</template>
      </v-select>
      <vsoc-date-range ref="dateInput" v-model="range" @input="onChangeDate">
        <template v-slot:text="{ on, attrs }">
          <v-text-field
            clearable
            readonly
            large
            :label="$t(selected.text)"
            append-icon="mdi-calendar-range-outline"
            :value="RANGE_STR(range.start, range.end)"
            v-bind="attrs"
            @click:clear="onChangeDate({ start: '', end: '' })"
            v-on="on"
          />
        </template>
      </vsoc-date-range>
      <v-text-field
        v-model="advanceQuery.cnvdId"
        :label="$t('vulnerability.headers.cnvdId')"
      >
      </v-text-field>
    </template>

    <template v-if="currentTab === -0.5">
      <p class="mt-4 mb-1 text-content">
        {{ $t('vulnerability.headers.alarmLevel') }}
      </p>
      <v-row dense>
        <v-col
          v-for="item in cnnvdVulnerabilityLevelEnum"
          :key="item.value"
          cols="4"
        >
          <v-checkbox
            v-model="advanceQuery.cnnvdLevelList"
            :value="item.value"
            dense
            class="pt-0 mt-0"
          >
            <template v-slot:label>
              <div class="d-flex">
                <vsoc-icon
                  type="fill"
                  :style="{ color: item.color }"
                  icon="icon-loudongdengjibiaozhi"
                ></vsoc-icon>
                <span class="ml-2">{{ item.text }}</span>
              </div>
            </template>
          </v-checkbox>
        </v-col>
      </v-row>

      <v-text-field
        v-model="advanceQuery.cnnvdName"
        :label="$t('vulnerability.headers.vulnerabilityName')"
      >
      </v-text-field>

      <v-select
        v-model="selected"
        return-object
        class="mt-3"
        :items="cnnvdDateList"
        :label="$t('vulnerability.vulnerabilityDate')"
        :multiple="false"
        :menu-props="{ offsetY: true }"
      >
        <template v-slot:item="{ item }">
          {{ $t(item.text) }}
        </template>
        <template v-slot:selection="{ item }"> {{ $t(item.text) }}</template>
      </v-select>

      <vsoc-date-range ref="dateCnnvd" v-model="range" @input="onChangeDate">
        <template v-slot:text="{ on, attrs }">
          <v-text-field
            clearable
            readonly
            large
            :label="$t(selected.text)"
            append-icon="mdi-calendar-range-outline"
            :value="RANGE_STR(range.start, range.end)"
            v-bind="attrs"
            @click:clear="onChangeDate({ start: '', end: '' })"
            v-on="on"
          />
        </template>
      </vsoc-date-range>
      <v-text-field
        v-model="advanceQuery.cnnvdId"
        :label="$t('vulnerability.headers.cnnvdId')"
      >
      </v-text-field>
    </template>

    <template v-if="[-1, -0.5, 0].includes(this.currentTab)">
      <v-text-field
        v-model="advanceQuery.cveId"
        :label="$t('vulnerability.headers.cveId')"
      >
      </v-text-field>
    </template>

    <template v-if="currentTab === 1">
      <!-- <v-select
        v-model="advanceQuery.vulnerabilityLevelList"
        :items="laboratoryVulnerabilityLevelEnum"
        :label="$t('vulnerability.headers.alarmLevel')"
        multiple
        clearable
        :menu-props="{ offsetY: true }"
      >
      </v-select> -->
      <p class="mt-4 mb-0 text-content">
        {{ $t('vulnerability.headers.alarmLevel') }}
      </p>
      <v-row dense>
        <v-col
          cols="6"
          v-for="item in laboratoryVulnerabilityLevelEnum"
          :key="item.value"
        >
          <v-checkbox
            v-model="advanceQuery.vulnerabilityLevelList"
            :value="item.value"
            dense
            hide-details
            class="mt-0 pt-1"
          >
            <template v-slot:label>
              <div class="d-flex">
                <vsoc-icon
                  type="fill"
                  :style="{ color: item.color }"
                  icon="icon-loudongdengjibiaozhi"
                ></vsoc-icon>
                <span class="ml-2">{{ item.text }}</span>
              </div>
            </template>
          </v-checkbox>
        </v-col>
      </v-row>

      <v-text-field
        class="mt-4"
        v-model="advanceQuery.vulnerabilityName"
        :label="$t('vulnerability.headers.vulnerabilityName')"
      >
      </v-text-field>
      <v-text-field
        v-model="advanceQuery.vulneraNumber"
        :label="$t('vulnerability.headers.vulnerId')"
      />
      <v-autocomplete
        :label="$t('vulnerability.headers.findUser')"
        v-model="advanceQuery.findUser"
        :items="userList"
        clearable
        :menu-props="{ offsetY: true }"
      ></v-autocomplete>
      <vsoc-date-range
        ref="dateInput"
        v-model="range"
        @input="onChangeFindDate"
        :inputLabel="$t('vulnerability.headers.findDate')"
        class="mb-5"
      >
      </vsoc-date-range>
      <v-text-field
        v-model="advanceQuery.impactManufacturer"
        :label="$t('vulnerability.headers.affectManufacturers')"
      />
      <v-select
        :label="$t('vulnerability.headers.loopholeType')"
        v-model="advanceQuery.loopholeTypeList"
        multiple
        :items="vulnerabilityTypeEnum"
        :menu-props="{ offsetY: true }"
      ></v-select>
    </template>

    <template v-if="currentTab === -1">
      <v-text-field
        v-model="advanceQuery.cweId"
        :label="$t('vulnerability.cve.drawer.cweId')"
      >
      </v-text-field>
      <v-text-field
        v-model="advanceQuery.descriptions"
        :label="$t('vulnerability.cve.headers.desc')"
      >
      </v-text-field>
      <v-text-field
        v-model="advanceQuery.product"
        :label="$t('vulnerability.edit.title2')"
      >
      </v-text-field>
      <v-text-field
        v-model="advanceQuery.vendor"
        :label="$t('vulnerability.headers.affectManufacturers')"
      >
      </v-text-field>

      <div class="text-content">
        {{ $t('vulnerability.cve.drawer.isLink') }}
      </div>
      <v-radio-group
        v-model="advanceQuery.isNotreference"
        row
        class="pt-0 mt-2"
      >
        <v-radio
          v-for="reference in referenceOption"
          :key="reference.value"
          :value="reference.value"
          :label="reference.text"
        ></v-radio>
      </v-radio-group>

      <!-- <v-select
        :label="$t('vulnerability.cve.drawer.version')"
        v-model="advanceQuery.metrics"
        :items="versionOption"
        :menu-props="{ offsetY: true }"
        @change="() => (advanceQuery.baseSeverityList = [])"
      >
      </v-select> -->
      <div class="text-content">
        {{ $t('vulnerability.cve.drawer.version') }}
      </div>
      <v-radio-group
        v-model="advanceQuery.metrics"
        row
        @change="() => (advanceQuery.baseSeverityList = [])"
        class="pt-0 mt-2"
      >
        <v-radio
          v-for="(version, index) in versionOption"
          :key="index"
          :label="version.text"
          :value="version.value"
        ></v-radio>
      </v-radio-group>
      <template v-if="advanceQuery.metrics">
        <div class="text-content mb-n2">
          {{ $t('vulnerability.cve.drawer.range') }}
        </div>
        <v-checkbox
          v-model="advanceQuery.baseSeverityList"
          v-for="(metric, index) in advanceQuery.metrics === '2'
            ? cvss2Enum
            : cvss3Enum"
          :key="index + 'metric'"
          :value="metric.value"
          hide-details
          class="pt-0"
        >
          <template v-slot:label>
            <div>
              <v-icon :color="metric.color">mdi-circle-medium</v-icon>
              <span>{{ metric.label }}</span>
            </div>
          </template>
        </v-checkbox>
      </template>

      <!-- <v-select
        v-if="advanceQuery.metrics"
        :label="$t('vulnerability.cve.drawer.range')"
        multiple
        v-model="advanceQuery.baseSeverityList"
        :items="advanceQuery.metrics === '2' ? cvss2Enum : cvss3Enum"
        :menu-props="{ offsetY: true }"
        item-text="label"
        item-color="color"
      >
        <template v-slot:selection="{ item, index }">
          <span v-if="index === 0">
            {{ $t('global.pagination.selected') }}：{{
              advanceQuery.baseSeverityList.length
            }}
          </span>
        </template>
      </v-select> -->
      <vsoc-date-range
        v-model="publishedRange"
        class="mt-2"
        :inputLabel="$t('vulnerability.cve.headers.publishedDate')"
      />
      <vsoc-date-range
        class="my-7"
        v-model="updateRange"
        :inputLabel="$t('vulnerability.cve.drawer.lastUpdateDate')"
      />
    </template>

    <template v-if="currentTab === -2">
      <p class="mt-4 mb-1 text-content">
        {{ $t('vulnerability.headers.alarmLevel') }}
      </p>
      <v-row dense class="ma-0">
        <v-col
          v-for="item in cavdVulnerabilityLevelEnum"
          :key="item.value"
          cols="6"
        >
          <v-checkbox
            v-model="advanceQuery.vulLevelList"
            :value="item.value"
            dense
            hide-details
            class="pt-0 mt-0"
          >
            <template v-slot:label>
              <div class="d-flex">
                <vsoc-icon
                  type="fill"
                  :style="{ color: item.color }"
                  icon="icon-loudongdengjibiaozhi"
                ></vsoc-icon>
                <span class="ml-2">{{ item.text }}</span>
              </div>
            </template>
          </v-checkbox>
        </v-col>
      </v-row>
      <v-text-field
        v-model="advanceQuery.vulName"
        :label="$t('vulnerability.headers.vulnerabilityName')"
      ></v-text-field>
      <v-select
        v-model="selected"
        return-object
        class="mt-3"
        :items="cavdDateList"
        :label="$t('vulnerability.vulnerabilityDate')"
        :multiple="false"
        :menu-props="{ offsetY: true }"
      >
        <template v-slot:item="{ item }">
          {{ $t(item.text) }}
        </template>
        <template v-slot:selection="{ item }"> {{ $t(item.text) }}</template>
      </v-select>

      <vsoc-date-range ref="dateCnnvd" v-model="range" @input="onChangeDate">
        <template v-slot:text="{ on, attrs }">
          <v-text-field
            clearable
            readonly
            large
            :label="$t(selected.text)"
            append-icon="mdi-calendar-range-outline"
            :value="RANGE_STR(range.start, range.end)"
            v-bind="attrs"
            @click:clear="onChangeDate({ start: '', end: '' })"
            v-on="on"
          />
        </template>
      </vsoc-date-range>

      <v-select
        :items="cavdIdList"
        :label="$t('vulnerability.headers.vulnerId')"
        :multiple="false"
        :menu-props="{ offsetY: true }"
        return-object
        @change="any => (selectedId = any)"
      >
        <template v-slot:item="{ item }">
          {{ $t(item.text) }}
        </template>
        <template v-slot:selection="{ item }"> {{ $t(item.text) }}</template>
      </v-select>
      <v-text-field
        v-model="advanceQuery[selectedId.value]"
        :label="selectedId.text || 'Id'"
      ></v-text-field>
    </template>
  </vsoc-drawer>
</template>

<script>
import VsocDrawer from '@/components/VsocDrawer.vue'
import VsocDateRange from '@/components/vsoc-date-range/VsocDateRange.vue'
import { RANGE_STR } from '@/components/vsoc-date-range/constants'
export default {
  name: 'VulnerabilityDrawer',
  components: {
    VsocDrawer,
    VsocDateRange,
  },
  props: {
    value: Boolean,
    currentTab: Number,
  },
  data() {
    return {
      versionOption: [
        { text: 'Version 3.x', value: '3' },
        { text: 'Version 2', value: '2' },
        { text: 'All', value: '' },
      ],
      userList: [],
      range: {
        start: '',
        end: '',
      },
      selected: {},
      advanceQuery: {
        vulnerabilityLevelList: [],
        loopholeTypeList: [],
        cveId: '',
        cnvdId: '',
        vulnerabilityName: '',
        attachment: null, //附件
        dataSource: '', //漏洞来源
        publicTimeStart: null, //公开日期开始时间（用于条件查询）
        publicTimeEnd: null, //公开日期结束时间（用于条件查询）
        recordTimeStart: null, //收录时间开始时间（用于条件查询）
        recordTimeEnd: null, //收录时间结束时间（用于条件查询）
        updateTimeStart: null, //更新世家开始时间（用于条件查询）
        updateTimeEnd: null, //更新时间结束时间（用于条件查询）
        createTimeStart: null, //录入时间开始时间（用于条件查询）
        createTimeEnd: null, //录入时间结束时间（用于条件查询）

        // 实验室漏洞
        vulnerabilityLevelList: [],
        vulneraNumber: '',
        findUser: '',
        startDate: null,
        endDate: null,
        vulnerabilityName: '',
        impactManufacturer: '',
        loopholeTypeList: [],

        // cve
        startPublishedDate: null, //公开时间开始时间
        endPublishedDate: null, //公开时间结束时间
        startLastupdateDate: null, //最后更新时间开始时间
        endLastupdateDate: null, //最后更新时间结束时间
        descriptions: '', //描述
        isNotreference: null, //是否包含超链接
        cweId: '', //cweID
        vendor: '', //供应商
        product: '', //产品
        metrics: '', //cvss评分
        baseSeverityList: [], //评分范围

        // cnnvd
        cnnvdId: '',
        cnnvdName: '',
        cveId: '',
        publishedTimeStart: '',
        publishedTimeEnd: '',
        cnnvdLevelList: [],
        updateTimeStart: '',
        updateTimeEnd: '',
        createTimeStart: '',
        createTimeEnd: '',

        // cavd
        cavdNo: '',
        vulName: '',
        cveContent: '',
        publishDateStart: null,
        publishDateEnd: null,
        vulLevelList: [],
        createTimeStart: null,
        createTimeEnd: null,
        syncTimeStart: null,
        syncTimeEnd: null,
      },
      selectedId: {},
    }
  },
  computed: {
    referenceOption() {
      return [
        {
          text: this.$t('enums.reference.yes'),
          value: 1,
        },
        {
          text: this.$t('enums.reference.no'),
          value: 0,
        },
      ]
    },
    publishedRange: {
      get() {
        return {
          start: this.advanceQuery.startPublishedDate,
          end: this.advanceQuery.endPublishedDate,
        }
      },
      set(newVal) {
        this.advanceQuery.startPublishedDate = newVal.start
        this.advanceQuery.endPublishedDate = newVal.end
        return newVal
      },
    },
    updateRange: {
      get() {
        return {
          start: this.advanceQuery.startLastupdateDate,
          end: this.advanceQuery.endLastupdateDate,
        }
      },
      set(newVal) {
        this.advanceQuery.startLastupdateDate = newVal.start
        this.advanceQuery.endLastupdateDate = newVal.end
        return newVal
      },
    },
    cvss2Enum() {
      let list = this.$store.getters['enums/getCvss2']
      return list.map(item => {
        return {
          label: `${item.text}(${item.value})`,
          value: item.text,
          color: item.color,
        }
      })
    },
    cvss3Enum() {
      let list = this.$store.getters['enums/getCvss3']
      return list.map(item => {
        return {
          label: `${item.text}(${item.value})`,
          value: item.text,
          color: item.color,
        }
      })
    },
    vulnerabilityTypeEnum() {
      return Object.assign([], this.$store.state.enums.enums.VulnerabilityType)
    },
    laboratoryVulnerabilityLevelEnum() {
      return this.$store.getters['enums/getLaboratoryVulnerabilityLevel']
    },
    vulnerabilityDateList() {
      return [
        {
          text: 'vulnerability.headers.recordTime',
          value: 'recordTime',
        },
        {
          text: 'vulnerability.headers.publicTime',
          value: 'publicTime',
        },
        // {
        //   text: 'vulnerability.headers.updateTime',
        //   value: 'updateTime',
        // },
        {
          text: 'vulnerability.headers.createTime',
          value: 'createTime',
        },
      ]
    },
    cnnvdDateList() {
      return [
        {
          text: 'vulnerability.cnnvd.updateDate',
          value: 'updateTime',
        },
        {
          text: 'vulnerability.cnnvd.publicDate',
          value: 'publishedTime',
        },
        {
          text: 'vulnerability.headers.createTime',
          value: 'createTime',
        },
      ]
    },
    cavdDateList() {
      return [
        {
          text: this.$t('vulnerability.cavd.publicTime'),
          value: 'publishDate',
        },
        {
          text: this.$t('global.createDate'),
          value: 'createTime',
        },
        {
          text: this.$t('vulnerability.headers.createTime'),
          value: 'syncTime',
        },
      ]
    },
    cavdIdList() {
      return [
        {
          text: this.$t('vulnerability.cavd.id'),
          value: 'cavdNo',
        },
        {
          text: this.$t('vulnerability.headers.cveId'),
          value: 'cveContent',
        },
      ]
    },
    cnnvdVulnerabilityLevelEnum() {
      return this.$store.getters['enums/getCnnvdVulnerabilityLevel']
    },
    cavdVulnerabilityLevelEnum() {
      return this.$store.getters['enums/getCavdVulnerabilityLevel']
    },
    vulnerabilityLevelEnum() {
      return this.$store.getters['enums/getVulnerabilityLevel']
    },
    vulnerabilitySourceEnum() {
      return this.$store.getters['enums/getVulnerabilitySource']
    },
  },
  mounted() {
    this.selected =
      this.currentTab === 0
        ? this.vulnerabilityDateList[0]
        : this.cnnvdDateList[0]

    this.loadUserList()
  },
  methods: {
    async loadUserList() {
      this.userList = await this.$store.dispatch('global/loadUserList')
    },
    RANGE_STR,
    onChangeFindDate(range) {
      this.advanceQuery.startDate = range.start
      this.advanceQuery.endDate = range.end
    },
    // 时间范围改变
    onChangeDate(range) {
      this.range = range
    },
    close(bool) {
      if (!bool) {
        this.$emit('input', false)
      }
    },

    setModel(obj) {
      Object.keys(this.advanceQuery).forEach(key => {
        this.advanceQuery[key] = obj[key]
      })
      if (
        this.currentTab === 0 ||
        this.currentTab === -0.5 ||
        this.currentTab === -2
      ) {
        const startDate = obj[`${this.selected.value}Start`]
        const endDate = obj[`${this.selected.value}End`]
        this.range = {
          start: startDate,
          end: endDate,
        }
      } else if (this.currentTab === 1) {
        this.range = {
          start: obj.startDate,
          end: obj.endDate,
        }
      } else {
        this.range = {
          start: '',
          end: '',
        }
      }
    },

    clearAdvanceQuery() {
      Object.assign(
        this.$data.advanceQuery,
        this.$options.data.call(this).advanceQuery,
      )
      Object.assign(this.$data.range, this.$options.data.call(this).range)
    },
    setFilterList(query) {
      let filterList = []
      for (const [key, value] of Object.entries(query)) {
        if (value instanceof Array && value.length === 0) {
          continue
        }
        if (
          (value || value === 0) &&
          !key.includes('Time') &&
          !key.includes('Date')
        ) {
          let text = ''
          let name = ''
          let type = 'String'
          switch (key) {
            case 'vulName':
              text = 'vulnerability.headers.vulnerabilityName'
              name = value
              break
            case 'cavdNo':
              text = 'vulnerability.cavd.id'
              name = value
              break
            case 'vulneraNumber':
              text = 'vulnerability.headers.vulnerId'
              name = value
              break
            case 'findUser':
              text = 'vulnerability.headers.findUser'
              name = this.userList.find(v => v.userId === value)?.userName
              break
            case 'vulnerabilityLevelList':
            case 'cnnvdLevelList':
            case 'vulLevelList':
              text = 'vulnerability.headers.alarmLevel'
              let currentEnum = this.vulnerabilityLevelEnum
              if (this.currentTab == 1) {
                currentEnum = this.laboratoryVulnerabilityLevelEnum
              } else if (this.currentTab == -0.5) {
                currentEnum = thivulnerabilitys.cnnvdVulnerabilityLevelEnum
              } else if (this.currentTab == -2) {
                currentEnum = this.cavdVulnerabilityLevelEnum
              }
              // this.currentTab === 0 || this.currentTab === -0.5
              //   ? this.vulnerabilityLevelEnum
              //   : this.laboratoryVulnerabilityLevelEnum
              let arr = currentEnum
                .filter(v => value.includes(v.value))
                .map(y => y.text)
              if (value.length > 1) {
                name = `+${arr.length}`
              } else {
                name = arr[0]
              }
              type = 'Array'
              break
            case 'loopholeTypeList':
              text = 'vulnerability.headers.loopholeType'
              let list = this.vulnerabilityTypeEnum
                .filter(v => value.includes(v.value))
                .map(y => y.text)
              if (value.length > 1) {
                name = `+${list.length}`
              } else {
                name = list[0]
              }
              type = 'Array'
              break
            case 'cnvdId':
              text = 'vulnerability.headers.cnvdId'
              name = value
              break
            case 'vulnerabilityName':
            case 'cnnvdName':
              text = 'vulnerability.headers.vulnerabilityName'
              name = value
              break
            case 'cveId':
            case 'cveContent':
              text = 'vulnerability.headers.cveId'
              name = value
              break
            case 'cnnvdId':
              text = 'vulnerability.headers.cnnvdId'
              name = value
              break
            case 'dataSource':
              text = 'vulnerability.headers.dataSource'
              name = this.vulnerabilitySourceEnum[value]?.text
              break
            case 'cweId':
              text = 'cweId'
              name = value
              break
            case 'vendor':
              text = 'vulnerability.headers.affectManufacturers'
              name = value
              break
            case 'product':
              text = 'vulnerability.edit.title2'
              name = value
              break
            case 'descriptions':
              text = 'vulnerability.cve.headers.desc'
              name = value
              break
            case 'isNotreference':
              text = 'vulnerability.cve.drawer.isLink'
              name = this.referenceOption.find(v => v.value === value)?.text
              break
            case 'metrics':
              text = 'vulnerability.cve.drawer.version'
              name = this.versionOption.find(v => v.value === value)?.text
              break
            case 'baseSeverityList':
              text = 'vulnerability.cve.drawer.range'
              const currentCvss =
                this.advanceQuery.metrics == 3 ? this.cvss3Enum : this.cvss2Enum
              let cvssList = currentCvss
                .filter(v => value.includes(v.value))
                .map(y => y.label)
              if (value.length > 1) {
                name = `+${cvssList.length}`
              } else {
                name = cvssList[0]
              }
              type = 'Array'
              break
          }
          filterList.push({
            text: `${this.$t(text)}:${name}`,
            value,
            key,
            type,
          })
        }
      }

      if (query.startDate && query.endDate && this.currentTab === 1) {
        filterList.push({
          text: `${this.$t('vulnerability.headers.findDate')}:${this.RANGE_STR(
            query.startDate,
            query.endDate,
          )}`,
          value: this.RANGE_STR(query.startDate, query.endDate),
          key: 'findDate',
          type: 'Date',
        })
      }

      const startDate = query[`${this.selected.value}Start`]
      const endDate = query[`${this.selected.value}End`]
      if (
        startDate &&
        endDate &&
        (this.currentTab === 0 ||
          this.currentTab === -0.5 ||
          this.currentTab == -2)
      ) {
        let value = this.RANGE_STR(startDate, endDate)
        filterList.push({
          text: `${this.$t(this.selected.text)}:${value}`,
          value,
          key: this.selected.value,
          type: 'Date',
        })
      }

      // cve
      if (this.currentTab === -1) {
        if (query.startPublishedDate && query.endPublishedDate) {
          filterList.push({
            text: `${this.$t(
              'vulnerability.cve.headers.publishedDate',
            )}:${this.RANGE_STR(
              query.startPublishedDate,
              query.endPublishedDate,
            )}`,
            value: this.RANGE_STR(
              query.startPublishedDate,
              query.endPublishedDate,
            ),
            key: 'PublishedDate',
            type: 'Date',
          })
        }

        if (query.startLastupdateDate && query.endLastupdateDate) {
          filterList.push({
            text: `${this.$t(
              'vulnerability.cve.drawer.lastUpdateDate',
            )}:${this.RANGE_STR(
              query.startLastupdateDate,
              query.endLastupdateDate,
            )}`,
            value: this.RANGE_STR(
              query.startLastupdateDate,
              query.endLastupdateDate,
            ),
            key: 'LastupdateDate',
            type: 'Date',
          })
        }
      }

      return filterList
    },

    //  高级查询
    doQuery(callback) {
      let dateList = this.vulnerabilityDateList
      if (this.currentTab === -0.5) {
        dateList = this.cnnvdDateList
      }
      // 时间处理
      // 排他
      dateList.forEach(item => {
        this.advanceQuery[`${item.value}Start`] = null
        this.advanceQuery[`${item.value}End`] = null
      })
      // 赋值
      this.advanceQuery[`${this.selected.value}Start`] = this.range.start
      this.advanceQuery[`${this.selected.value}End`] = this.range.end
      this.$emit('do-query', this.advanceQuery)
      callback()
    },
  },
}
</script>
