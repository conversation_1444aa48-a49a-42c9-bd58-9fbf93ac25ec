const notices = {
  currentTitle: 'Notices',
  headers: {
    title: 'Notice Title',
    receiverType: 'Receiver',
    sendStatus: 'Status',
    sendCount: 'Send Count',
    readCount: 'Read Count',
    readingPercentage: 'Read Percentage',
    createDate: 'Create Time',
    sendDate: 'Send Time',
    content: 'Notice Content',
  },
  sendTitle: {
    title: 'Notice Title',
    content: 'Notice Content',
    object: 'Notification object',
    time: 'Send Type',
    user: 'Receivers',
    timeRange: 'Send Time',
  },
  readInfo: {
    title: 'Reading Situation',
    user: 'User',
    status: 'Status',
  },
  btn: {
    publish: 'New Notice',
    more: 'View more messages',
    previous: 'View previous messages',
  },
  hint: {
    nodata: 'No Message',
  },
}

export default notices
