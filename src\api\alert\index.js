import { request, vsocPath } from '../../util/request'

// 获取告警列表
export const getAlerts = function (data) {
  return request({
    url: `${vsocPath}/assetAlarm/assetAlarms`,
    method: 'post',
    data,
  })
}

// 导出告警
export const exportAlarm = function (data) {
  return request({
    url: `${vsocPath}/assetAlarm/export`,
    method: 'post',
    data,
    responseType: 'blob',
  })
}

// 告警类型统计数据
export const alarmTypeStatistics = function (data) {
  return request({
    url: `${vsocPath}/assetAlarm/alarmTypeStatistics`,
    method: 'post',
    data,
  })
}

// 多资产详情
export const multiAssetDetail = function (data) {
  return request({
    url: `${vsocPath}/assetAlarm/multiAssetDetail`,
    method: 'post',
    data,
  })
}

// // 获取告警属性列表
// export const getAlertFields = function (params) {
//   return request({
//     url: `${cmdbPath}/asset-alarm`,
//     method: 'get',
//     params,
//   })
// }

// 修改告警状态
export const updateStatus = function (data) {
  return request({
    url: `${vsocPath}/assetAlarm/updateAssetAlarm`,
    method: 'post',
    data,
  })
}

// // 获取告警详情
// export const getAlertDetails = function (id) {
//   return request({
//     url: `${cmdbPath}/asset-alarm/${id}/detail`,
//     method: 'get',
//   })
// }

// // 获取全部告警标签
// export const getAllAlertTags = function () {
//   return request({
//     url: `${cmdbPath}/alarm-tag/selectBox`,
//     method: 'get',
//   })
// }

// // 新增告警标签
// export const addAlertTag = function (name) {
//   return request({
//     url: `${cmdbPath}/alarm-tag/add`,
//     method: 'post',
//     data: {
//       description: '',
//       name,
//     },
//   })
// }
