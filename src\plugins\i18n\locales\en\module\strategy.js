const strategy = {
  project: {
    currentTitle: 'Project',
    headers: {
      projectName: 'Project Name',
      groupType: 'Group Type',
      vehicleModel: 'Vehicle Model',
      vehicleYear: 'Model Year',
      validRulesetCount: 'Valid Strategy',
      ruleSetId: 'Pending Review Strategy',
    },
    swal: {
      del: {
        title: 'Delete Project',
        text: 'Are you sure you want to delete the project: {0}?',
      },
      del1: {
        title: 'Delete interception',
        text: 'There are valid Rulesets under this project that cannot be directly deleted. Please remove all associated Rulesets first and try again.',
        btn: 'Got it',
      },
    },
  },
  ruleSet: {
    currentTitle: 'RuleSet',
    headers: {
      rulesetName: 'Ruleset Name',
      modelCode: 'Model Code(Hexadecimal system)',
      mainVersion: 'Version(Hexadecimal system)',
      revisionNumber: 'Revision Number',
      enableStatus: 'Rule status',
      filterStatus: 'Algorithm filter',
      rulesetState: 'Status',
      reason: 'Reason',
      verificationContent: 'Verification content',
    },
    securityPolicy: 'Security Strategy',
    pending: 'Pending Review Strategy',
    review: 'Review Strategy',
    edit: 'Edit Strategy',
    upload: {
      rulesetUpload: 'Strategy upload',
      tip: 'The value needs to be in hexadecimal format',
      tip1: 'The file content is not in a valid JSON format!',
      tip2: 'Please upload the strategy!',
    },
    del: {
      title: 'Delete RuleSet',
      text: 'Are you sure to delete the RuleSet:{0}?',
    },
    info: {
      content: 'Edit Content',
      current: 'Disable Rule Details (Current Version)',
      new: 'Disable Rule Details (New Version)',
      old: 'Disable Rule Details (Old Version)',
      newStatus: 'Rule status (New Version)',
      oldStatus: 'Rule status (Old Version)',
      tip: 'Submitted successfully',
      tip1: 'Disabled successfully',
      tip2: 'Enabled successfully ',
      audit: 'Strategy review',
      history: 'Strategy History Details',
      detail: 'Strategy Detail',
      reviewfailed: 'Review failed',
      reviewSuccess: 'Review successfully',
      verificationContent: 'Verification content',
    },
    historicalVersion: 'Historical Version',
  },
  decryptionTool: {
    form: {
      modelCode: 'Model Code(Hexadecimal system)',
      mainVersion: 'Main Version(Hexadecimal system)',
      indexId: 'Index ID',
      messageTime: 'Message Time',
      rules: 'Data visualization rules',
      encryptedData: 'Original encrypted data',
    },
    results: 'Analysis results',
    tip: 'The original encrypted data must be 544 characters long',
    tip1: 'The format of the data visualization rules is incorrect. Please check the JSON format',
  },
}
export default strategy
