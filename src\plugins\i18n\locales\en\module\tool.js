const tool = {
  event: 'Event',
  previousState: 'Previous state',
  value: 'Value',
  list: 'List',
  formula: 'Formula',
  abnormal: 'Abnormal',
  currentMessage: 'Current message',
  currentState: 'Current state',
  abnormal: 'Abnormal value',
  anomaly: {
    tip: 'Unit testing',
    tip1: 'Maximum limit',
  },
  createFormula: {
    name: 'Formula Name',
    type: 'Expression Type',
    expression: 'Expression',
    tip: 'Select existing formulas',
    tip1: 'Create an expression. Use the "Add Variable" button to reference the dynamic values of signals from digital twins. You can use the following characters+, -, *,/, (,) ',
    tip2: 'Use the Verify Expression button to verify the return type and legality of the expression',
    tip3: 'Verification passed',
    tip4: 'Formula name is duplicated',
    tip5: 'Formula name consist of letters, numbers, and underscores starting with a letter',
    tip6: 'The number of variables has reached the upper limit',
    tip7: 'Please select sources and signals for all variables used in the formula',
    tip8: 'The calculated value is of type {0} and cannot be compared with {1}',
  },
  hint: {
    tip: 'Apply only when triggered by the current message',
    tip1: 'The logical condition is incomplete, please fill in or delete it',
    tip2: 'Fill in at least one complete logic',
    tip3: 'Please enter a value of a numeric type',
    tip4: 'Please enter a value of integer type',
    tip5: 'The valid range of values is between {0} and {1}',
    tip6: 'The valid range of values cannot be less than {0}',
    tip7: 'The valid range of values cannot be greater than {0}',
    tip8: 'Time interval exceeds maximum limit',
  },
}
export default tool
