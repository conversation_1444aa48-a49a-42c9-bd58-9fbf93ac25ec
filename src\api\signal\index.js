import { request, vsocPath } from '../../util/request'

// 根据id查询资产模型
export const digitalTwinDetails = function (data) {
  return request({
    url: `${vsocPath}/digitalTwin/digitalTwinDetails`,
    method: 'post',
    data,
  })
}

// 根据条件查询信号或事件
export const digitalTwinsByCondition = function (data) {
  return request({
    url: `${vsocPath}/digitalTwin/digitalTwinsByCondition`,
    method: 'post',
    data,
  })
}

// 修改资产模型
export const updateDigitalWin = function (data) {
  return request({
    url: `${vsocPath}/digitalTwin/updateDigitalTwin`,
    method: 'post',
    data,
  })
}

// 删除资产模型
export const deleteDigitalTwin = function (data) {
  return request({
    url: `${vsocPath}/digitalTwin/deleteDigitalTwin`,
    method: 'post',
    data,
  })
}

// 分页查询资产模型
export const queryPage = function (data) {
  return request({
    url: `${vsocPath}/digitalTwin/digitalTwins`,
    method: 'post',
    data,
  })
}

// 新增资产模型
export const saveDigitalTwin = function (data) {
  return request({
    url: `${vsocPath}/digitalTwin/saveDigitalTwin`,
    method: 'post',
    data,
  })
}

// 动态更新历史信号表字段
export const alterTableField = function (data) {
  return request({
    url: `${vsocPath}/digitalTwin/alterTableField`,
    method: 'post',
    data,
  })
}
