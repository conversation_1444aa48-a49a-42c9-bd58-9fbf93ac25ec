<!-- 处理管理新增、编辑 -->
<template>
  <edit-page
    :popsName="
      editType === 'add'
        ? $t('alertAction.btn.add')
        : editType === 'edit'
        ? $t('alertAction.btn.edit')
        : $t('alertAction.btn.detail')
    "
  >
    <div v-if="editType !== 'detail'" slot="BreadBtn">
      <v-btn color="primary" elevation="0" @click="confirmEdit">
        {{ $t('action.save') }}
      </v-btn>
    </div>
    <div class="edit-center-box response-center-box">
      <v-form ref="form" v-model="valid">
        <div
          class="text-title color-base font-weight-semibold-light mb-4 mt-6 d-flex align-center"
        >
          <div>
            {{ $t('global.drawer.baseInfo') }}
          </div>
          <div
            v-if="actionInfo.id"
            class="ml-6 primary--text d-flex align-center"
          >
            <span>{{ actionInfo.id }}</span>
            <v-btn v-show-tips="$t('action.copy')" icon small>
              <i
                v-copy="actionInfo.id"
                class="iconfont icon-fuzhi text-root primary--text"
              ></i>
            </v-btn>
          </div>
        </div>
        <div class="px-9">
          <v-text-field
            v-model="actionInfo.name"
            color="primary"
            :label="$t('alertAction.headers.name')"
            :rules="rules"
            :disabled="editType === 'detail'"
            class="is-required"
          ></v-text-field>
          <v-textarea
            v-model="actionInfo.description"
            :label="$t('global.desc')"
            :rows="$AREA_ROWS"
            color="primary"
            :disabled="editType === 'detail'"
            outlined
            class="mt-1"
          ></v-textarea>
        </div>

        <div class="text-title color-base font-weight-semibold-light mb-5 mt-1">
          {{ $t('alertAction.headers.severityList') }}
        </div>
        <div class="px-9">
          <v-row>
            <v-col v-for="item in alertLevel" :key="item.value">
              <v-checkbox
                :key="item.value"
                v-model="actionInfo.severityList"
                color="primary"
                :value="item.value"
                hide-details
                :disabled="editType === 'detail'"
              >
                <template v-slot:label>
                  <vsoc-icon
                    :style="{ color: item.color }"
                    type="fill"
                    icon="icon-dunpai"
                    class="mr-2"
                  ></vsoc-icon>
                  <!-- <v-icon :style="{ color: item.color }" width="10" height="14">
                    mdi-shield
                  </v-icon> -->
                  <span>{{ item.text }}</span>
                </template>
              </v-checkbox>
            </v-col>
          </v-row>
        </div>
        <div
          class="alert-box text-title color-base font-weight-semibold-light mb-5 mt-8"
        >
          {{ $t('alertAction.headers.warnClassifyTypeName') }}
        </div>
        <div class="px-9">
          <v-row>
            <v-col class="ma-0 mr-6">
              <v-autocomplete
                v-model="actionInfo.warnClassifyType"
                append-icon="mdi-chevron-down"
                :items="alertGroupTypes"
                :label="$t('detector.detail.type')"
                hide-details
                class="select-router-transition"
                :menu-props="{ offsetY: true }"
                :disabled="editType === 'detail'"
              ></v-autocomplete>
            </v-col>
            <v-col class="ma-0 asset-box">
              <v-select
                v-show="actionInfo.warnClassifyType !== '0'"
                v-model="actionInfo.warnClassifyList"
                append-icon="mdi-chevron-down"
                multiple
                chips
                :items="alertClassifyList"
                item-value="id"
                item-text="name"
                :label="$t('alertAction.headers.warnClassifyTypeName')"
                :menu-props="{ offsetY: true }"
                hide-details
                :disabled="editType === 'detail'"
              >
                <template v-slot:selection="{ item, index }">
                  <v-chip
                    v-if="index <= 3"
                    color="primary"
                    close
                    small
                    :disabled="editType === 'detail'"
                    @click:close="actionInfo.warnClassifyList.splice(index, 1)"
                  >
                    {{ item.name }}
                  </v-chip>
                  <span v-if="index === 4" class="grey--text text-caption">
                    (+{{ actionInfo.warnClassifyList.length - 4 }})
                  </span>
                </template>
              </v-select>
            </v-col>
          </v-row>
        </div>
        <div class="text-title color-base font-weight-semibold-light mb-5 mt-8">
          {{ $t('alertAction.headers.disposeTypeName') }}
        </div>
        <div class="px-9">
          <v-radio-group
            v-model="actionInfo.disposeType"
            row
            hide-details
            @change="changeType()"
          >
            <v-row>
              <v-col v-for="(item, index) in actionTypeEnum" :key="index">
                <v-radio
                  :label="item.text"
                  :value="item.value"
                  :disabled="editType === 'detail'"
                ></v-radio>
              </v-col>
            </v-row>
          </v-radio-group>
          <!--API集成-->
          <!-- <template v-if="actionInfo.disposeType == 0">
            <v-select
              v-model="ExternalApi.ticketType"
              color="primary"
              :items="ticketTypes"
              label="工单系统"
              hide-details
              :menu-props="{ offsetY: true }"
            >
            </v-select>
          </template> -->
          <template
            v-if="
              actionInfo.disposeType == 1 ||
              actionInfo.disposeType == 2 ||
              actionInfo.disposeType == 4
            "
          >
            <v-row>
              <v-col cols="12">
                <v-autocomplete
                  v-model="actionInfo.recipientsList"
                  multiple
                  :menu-props="{ offsetY: true, maxHeight: 300 }"
                  append-icon="mdi-chevron-down"
                  :items="actionsList"
                  chips
                  item-text="text"
                  item-value="value"
                  :label="$t('alertAction.headers.receiveUser')"
                  :disabled="editType === 'detail'"
                >
                  <template v-slot:selection="{ item, index }">
                    <v-chip
                      color="primary"
                      close
                      small
                      :disabled="editType === 'detail'"
                      @click:close="actionInfo.recipientsList.splice(index, 1)"
                    >
                      <span>{{ item.text }}</span>
                    </v-chip>
                  </template>
                </v-autocomplete>
              </v-col>
              <v-col cols="12">
                <v-text-field
                  v-model="actionInfo.noticeTitle"
                  :label="$t('response.swal.notice2')"
                  :rules="rules"
                  :disabled="editType === 'detail'"
                ></v-text-field>
              </v-col>
              <v-col cols="12" class="mt-2">
                <quill-editor
                  :key="editorKey"
                  ref="myQuillEditor"
                  v-model="actionInfo.noticeContent"
                  class="editor-box editor"
                  :disabled="editType === 'detail'"
                  :options="editorOption"
                >
                </quill-editor>
              </v-col>
            </v-row>
            <div
              v-if="editType !== 'detail'"
              class="d-inline-flex primary--text mt-4 cursor-pointer"
              @click="checkTag"
            >
              <vsoc-icon
                type="fill"
                size="16px"
                icon="icon-quanjusousuo"
              ></vsoc-icon>
              <span class="ml-2">
                {{ $t('response.swal.tip4') }}
              </span>
            </div>
            <!-- <v-row class="align-end mt-4">
                <v-col cols="2">
                  <div class="text-secondary mb-2">参数类型</div>
                  <v-select
                    v-model="type"
                    dense
                    outlined
                    :items="typeList"
                    placeholder="请选择"
                    persistent-hint
                    :menu-props="{ offsetY: true }"
                    append-icon="mdi-chevron-down"
                    hide-details
                  ></v-select>
                </v-col>
                <v-col v-if="type">
                  <div class="text-secondary mb-2">动态参数</div>
                  <el-select
                    v-model="signal"
                    filterable
                    class="flex-1"
                    placeholder="请选择"
                  >
                    <template v-for="(op, index) in alertList">
                      <el-option
                        :key="index"
                        :label="op.text"
                        :value="op.value"
                      ></el-option>
                    </template>
                  </el-select>
                </v-col>
                <v-col v-if="signal" cols="1">
                  <v-btn
                    class="bg-btn ml-4"
                    elevation="0"
                    width="76"
                    min-width="76"
                    style="margin-top: 26px"
                    @click="appendAlertDescription"
                  >
                    添加
                  </v-btn>
                </v-col>
                <div
                  class="d-flex align-center ml-6 mt-7"
                  style="width: 180px; color: #a1a6b1"
                >
                  <vsoc-icon type="fill" icon="icon-zhushi"></vsoc-icon>
                  <span class="ml-2">在通知内容中添加动态信号</span>
                </div>
              </v-row>
            </v-row> -->
          </template>
          <!-- Kafka集成 -->
          <template v-if="actionInfo.disposeType == 3">
            <v-select
              v-model="Kafka.kafkaType"
              color="primary"
              :items="kafkaTypes"
              label="是否内置系统"
              autocomplete="new-password"
              :disabled="editType === 'detail'"
            >
            </v-select>
            <template v-if="Kafka.kafkaType === '1'">
              <v-text-field
                v-model="Kafka.kafkaHost"
                label="IP地址和端口号"
                color="primary"
                type="text"
                autocomplete="new-password"
                :rules="rules"
                :disabled="editType === 'detail'"
              ></v-text-field>
              <v-text-field
                v-model="Kafka.kafkaUsername"
                label="用户名"
                color="primary"
                type="text"
                autocomplete="new-password"
                :rules="rules"
                :disabled="editType === 'detail'"
              ></v-text-field>
              <v-text-field
                v-model="Kafka.kafkaPassword"
                :type="showPassword ? 'text' : 'password'"
                label="密码"
                color="primary"
                :class="!showPassword ? 'input-password' : ''"
                autocomplete="new-password"
                :rules="rules"
                :append-icon="!showPassword ? 'mdi-eye-off' : 'mdi-eye'"
                @click:append="showPassword = !showPassword"
                :disabled="editType === 'detail'"
              ></v-text-field>
            </template>
            <v-text-field
              v-model="Kafka.kafkaTopic"
              label="主题名"
              color="primary"
              class="mb-4"
              :rules="rules"
              :disabled="editType === 'detail'"
            ></v-text-field>
          </template>
          <!-- 系统消息配置 -->
          <!-- <v-row v-if="actionInfo.disposeType == 2">
            <v-col cols="12">
              <div class="d-flex align-center">
                <div class="text-sm mr-4">
                  <span class="text--primary font-weight-semibold"
                    >接收人 {{ `(${SystemMessage.length})` }}</span
                  >
                </div>
                <v-btn class="no-hb" text color="primary" @click="editEmail">
                  <v-icon size="1rem" left> mdi-pencil </v-icon>
                  选择接收人
                </v-btn>
              </div>
              <v-chip
                v-for="item in SystemMessage"
                :key="item.id"
                label
                color="primary"
                class="text-body text-sm my-2 d-block"
                style="width: fit-content"
                close
                @click:close="closeUser(item, 'SystemMessage')"
              >
                <span class="text-caption ls-0">{{ item.name }}</span>
              </v-chip>
            </v-col>
          </v-row> -->
          <!-- 邮箱通知配置 -->
          <!-- <v-row v-if="actionInfo.disposeType == 1">
            <v-col cols="12">
              <div class="d-flex align-center">
                <div class="text-sm mr-4">
                  <span class="text--primary font-weight-semibold"
                    >接收人 {{ `(${Email.length})` }}</span
                  >
                </div>
                <v-btn class="no-hb" text color="primary" @click="editEmail">
                  <v-icon size="1rem" left> mdi-pencil </v-icon>
                  选择接收人
                </v-btn>
              </div>

              <v-chip
                v-for="item in Email"
                :key="item.id"
                label
                color="primary"
                class="text-body text-sm my-2 d-block"
                style="width: fit-content"
                close
                @click:close="closeUser(item, 'Email')"
              >
                <span class="text-caption ls-0">{{ item.email }}</span>
              </v-chip>
            </v-col>
          </v-row> -->
        </div>
        <!-- <div
          class="text-title font-weight-semibold-light mb-5 mt-8 d-flex align-center"
        >
          <div>动态参数信息</div>
          <v-tooltip right transition="slide-x-transition">
            <template v-slot:activator="{ on, attrs }">
              <v-icon
                v-bind="attrs"
                size="14px"
                v-on="on"
                color="#686E7C"
                class="ml-4 tip-color"
              >
                mdi-information
              </v-icon>
            </template>
            <span
              >可以选择自定义附加字段信息针对所有的告警，选择的附加字段会添加到处理流程中。</span
            >
          </v-tooltip>
        </div>

        <div class="px-9">
          <v-radio-group v-model="actionInfo.assetType" row hide-details>
            <v-row>
              <v-col v-for="(item, index) in assetTypeEnum" cols="3">
                <v-radio
                  :key="index"
                  :label="item.dictName"
                  :value="item.dictId"
                  :disabled="editType === 'detail'"
                ></v-radio>
              </v-col>
            </v-row>
          </v-radio-group>
          <v-autocomplete
            v-model="actionInfo.digitalId"
            append-icon="mdi-chevron-down"
            persistent-hint
            color="primary"
            chips
            :items="signals"
            item-text="displayName"
            item-value="id"
            label="选择信号"
            multiple
            :rules="digitalIdRules"
            @change="valChange"
          >
            <template v-slot:selection="{ item, index }">
              <v-chip
                label
                color="primary text-sm"
                class="text-body text-sm my-2 d-block"
                close
                small
                @click:close="clearChip(actionInfo.digitalId, index)"
              >
                <span>{{ item.displayName ? item.displayName : item }}</span>
              </v-chip>
            </template>
            <template #no-data>
              <p class="text-sm text-body py-1 text-center">无匹配数据</p>
            </template>
          </v-autocomplete>
        </div> -->
      </v-form>
    </div>
    <vsoc-drawer
      width="500px"
      v-model="isTagShow"
      :title="$t('response.swal.tip4')"
      hideFooter
    >
      <div class="pt-3">
        {{ $t('response.swal.tip5') }}
      </div>

      <v-tabs v-model="typeValue" class="my-3" grow height="30">
        <div v-for="item in typeList" :key="item.value">
          <v-tab
            class="h-100 mr-4"
            :tab-value="item.value"
            @click.stop="changeTag(item)"
          >
            {{ $t(item.text) }}
          </v-tab>
        </div>
      </v-tabs>

      <div class="d-flex justify-end">
        <v-text-field
          v-model="search"
          color="primary"
          hide-details
          :label="$t('response.swal.parameter')"
          outlined
          dense
        ></v-text-field>
      </div>
      <v-data-table
        :headers="headers"
        hide-default-footer
        fixed-header
        :items-per-page="999"
        :items="tagTable"
        :search="search"
        :height="tableHeight"
        item-key="value"
        class="table border-radius-xl mt-3 thead-light"
      >
        <template v-slot:item.value="{ item }">
          <div class="d-flex" style="width: 200px">
            <div v-show-tips>
              {{ item.value | dataFilter }}
            </div>
            <vsoc-icon
              type="fill"
              class="action-btn ml-2 cursor-pointer"
              icon="icon-fuzhi"
              v-copy="item.value"
            />
          </div>
        </template>
        <template v-slot:item.text="{ item }">
          <div v-show-tips style="width: 140px">
            {{ item.text | dataFilter }}
          </div>
        </template>
      </v-data-table>
    </vsoc-drawer>
  </edit-page>
</template>

<script>
import { addAction, disposeActionDetail, updateAction } from '@/api/action'

import { querySysGroupList } from '@/api/system/organization'
import { findAllUsers } from '@/api/system/user'
import { flatTree } from '@/views/handle/response/tools.js'

import { getAllClassify } from '@/api/classify/index'
import editPage from '@/components/EditPage.vue'
import { deepClone, setRemainingHeight } from '@/util/utils'

import VsocDrawer from '@/components/VsocDrawer.vue'
import 'quill/dist/quill.bubble.css'
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import { quillEditor } from 'vue-quill-editor'
export default {
  name: 'ActionEdit',
  components: {
    editPage,
    quillEditor,
    VsocDrawer,
  },
  data() {
    return {
      tagTable: [],
      editorKey: 0,

      tableHeight: '34.5rem',
      search: '',
      isTagShow: false,

      typeValue: '1',
      showPassword: false,
      showSelectEmail: false,
      actionInfo: {
        id: '',
        name: '',
        description: '',
        severityList: ['3'], //严重级别数组 0：严重，1：高，2：中，3：低，4：信息
        disposeType: '0', //处置类型: 0：工单集成；1：邮件通知；2：系统消息； 4钉钉通知
        assetType: '0', //资产类型 0:vehicle 1:app
        kafkaTopic: '',
        warnClassifyType: '0', //告警分类类型 0：适用所有 1：适用指定告警分类 2：排除选择的告警分类
        warnClassifyList: [],
        noticeTitle: '',
        noticeContent: '',
        recipientsList: [],
      },
      alertClassifyList: [],
      actionsList: [],
      userList: [],
      groupList: [],
      isFlag: false,
      ExternalApi: {
        ticketType: '0',
      },
      Kafka: {
        kafkaType: '0',
        kafkaHost: '',
        kafkaUsername: '',
        kafkaPassword: '',
        kafkaTopic: '',
      },
      kafkaTypes: [
        {
          text: '是',
          value: '0',
        },
        {
          text: '否',
          value: '1',
        },
      ],
      ticketTypes: [
        {
          text: '内置工单系统',
          value: '0',
        },
        {
          text: '第三方工单系统',
          value: '1',
        },
      ],
      signals: [],
      Email: [],
      SystemMessage: [],
      signalType: '0', // 0 vehicle 1 app
      userInfos: [],
      editType: 'add',
      valid: true,
      rules: [v => !!v || '必填'],
      alarmFields: [],
      digitalIdRules: [
        v => (v && v.length <= 10) || `最多选择10条(${v.length}/10)`,
      ],
    }
  },
  watch: {
    '$i18n.locale': {
      handler(newVal) {
        this.editorKey++
      },
      deep: true,
    },
  },
  computed: {
    headers() {
      return [
        {
          text: this.$t('response.infoHeaders.value'),
          value: 'value',
          width: '200px',
        },
        {
          text: this.$t('response.infoHeaders.name'),
          value: 'text',
          width: '140px',
        },
      ]
    },
    editorOption() {
      return {
        placeholder: this.$t('alertAction.headers.noticeContent'),
        modules: {
          toolbar: false,
        },
      }
    },
    alertTable() {
      return [
        { text: this.$t('alert.headers.id'), value: '${alert.id}' },
        { text: this.$t('alert.headers.name'), value: '${alert.title}' },
        { text: this.$t('alertAction.detail.link'), value: '${alert.url}' },
        { text: this.$t('global.desc'), value: '${alert.content}' },
        { text: this.$t('alert.headers.severity'), value: ' ${alert.level}' },
        { text: this.$t('alert.headers.tag'), value: '${alert.type}' },
        { text: this.$t('global.createDate'), value: '${alert.createDate}' },
        {
          text: this.$t('global.updateDate'),
          value: '${alert.updateDate}',
        },
        { text: this.$t('global.updater'), value: '${alert.updater}' },
        { text: this.$t('alert.headers.status'), value: '${alert.status}' },
        { text: this.$t('alert.headers.assertId'), value: '${alert.assetId}' },
        {
          text: this.$t('alert.headers.deviceType'),
          value: '${alert.deviceType}',
        },
        { text: this.$t('alert.headers.model'), value: '${alert.model}' },
      ]
    },
    typeList() {
      return [
        { value: '1', text: this.$t('alertAction.btn.alert') },
        { value: '2', text: this.$t('alertAction.btn.app') },
      ]
    },
    // 获取告警等级颜色
    alertLevel() {
      const levels = this.$store.state.enums.enums.AlarmLevel
      let levellist = {}
      for (let i in levels) {
        if (i !== '5') {
          levellist[i] = levels[i]
        }
      }
      return levellist
    },
    alertGroupTypes() {
      return this.$store.getters['enums/getAlarmClassifyType']
    },
    actionTypeEnum() {
      const allTypes = this.$store.getters['enums/getDisposeType'].filter(
        v => v.value !== '3',
      )
      return this.$store.getters['enums/getDingType'] === 'true'
        ? allTypes
        : allTypes.filter(v => v.value !== '4')
    },
  },
  beforeDestroy() {
    this.$bus.$off('resize', this.$_setTableHeight)
  },
  created() {
    if (this.$route.query.id) {
      if (this.$route.query.isDetail === '1') {
        this.editType = 'detail'
      } else {
        this.editType = 'edit'
      }
      this.getActionById(this.$route.query.id)
    } else {
      this.editType = 'add'
      this.getSignals()
      this.getUsers()
      this.getGroup()
      this.getClassify()
    }
    this.$_setTableHeight()
    this.$bus.$on('resize', this.$_setTableHeight)
  },
  methods: {
    $_setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = setRemainingHeight() - 20
      })
    },
    // 获取信号
    async getSignals() {
      await this.$store.dispatch('global/searchSignal')
    },
    //获取分类
    async getClassify() {
      const res = await getAllClassify({ type: '0' })
      this.alertClassifyList = res.data || []
    },
    // 获取用户信息
    async getUsers() {
      try {
        const res = await findAllUsers({ status: '1' })
        this.userList = res.data.map(v => {
          return {
            ...v,
            value: v.userId,
            text: v.userName,
          }
        })
      } catch (e) {
        console.error(`获取用户信息：${e}`)
      }
    },
    async getGroup() {
      this.isFlag = false
      const res = await querySysGroupList({ state: '0' })
      this.groupList = flatTree(res.data)
      this.groupList = this.groupList.map(v => {
        return {
          ...v,
          value: v.id,
          text: v.name,
        }
      })
      this.isFlag = true
    },
    changeType(recipientsList) {
      if (
        this.actionInfo.disposeType === '1' ||
        this.actionInfo.disposeType === '2'
      ) {
        this.actionsList = this.userList
      } else if (this.actionInfo.disposeType === '4') {
        this.actionsList = this.groupList
      } else if (this.actionInfo.disposeType === '0') {
        this.actionInfo.noticeTitle = ''
        this.actionInfo.noticeContent = ''
      }
      this.actionInfo.recipientsList =
        recipientsList && recipientsList.length ? recipientsList : []
    },
    checkTag() {
      this.isTagShow = true
      this.tagTable = this.alertTable
      this.typeValue = '1'
      this.search = ''
    },
    changeTag(item) {
      this.typeValue = item.value
      if (item.value === '2') {
        this.tagTable = this.$store.getters['global/getSignals']('0')
          .filter(
            v => !['5', '6'].includes(v.signalValueType) && v.dataType !== '2',
          )
          .map(v => {
            return {
              ...v,
              text: v.displayName,
              value: `\${currentMessage.${v.id}}`,
            }
          })
      } else {
        this.tagTable = this.alertTable
      }
    },

    // 根据id查询
    async getActionById(id) {
      await Promise.all([
        this.getSignals(),
        this.getUsers(),
        this.getGroup(),
        this.getClassify(),
      ]).then(async e => {
        const res = await disposeActionDetail({ id: id })
        for (const key in this.actionInfo) {
          this.actionInfo[key] = res.data[key]
        }
        this.changeType(this.actionInfo.recipientsList)
      })
    },

    handleExtendAttributes(e) {
      this.Email = []
      this.SystemMessage = []
      if (e.disposeType === '0') {
        this.ExternalApi.ticketType = e.ticketType
      } else if (e.disposeType === '1') {
        if (e.receiveUser) {
          this.Email =
            this.userInfos.filter(v => e.receiveUser.indexOf(v.id) !== -1) || []
        }
      } else if (e.disposeType === '2') {
        if (e.receiveUser) {
          this.SystemMessage =
            this.userInfos.filter(v => e.receiveUser.indexOf(v.id) !== -1) || []
        }
      } else if (e.disposeType === '3') {
        const obj = this.Kafka
        for (const key in obj) {
          obj[key] = e[key]
        }
      }
    },
    getExtendAttributes() {
      // 0：工单集成；1：邮件通知；2：系统消息；3：kafka集成
      const actionInfo = {
        receiveUser: '',
        ticketType: '',
        kafkaTopic: '',
      }
      if (this.actionInfo.disposeType === '1') {
        actionInfo.receiveUser = this.Email.length
          ? this.Email.map(v => v.id).toString()
          : ''
      } else if (this.actionInfo.disposeType === '2') {
        actionInfo.receiveUser = this.SystemMessage.length
          ? this.SystemMessage.map(v => v.id).toString()
          : ''
      } else if (this.actionInfo.disposeType === '0') {
        actionInfo.ticketType = this.ExternalApi.ticketType
      } else if (this.actionInfo.disposeType === '3') {
        const obj = this.Kafka
        for (const key in obj) {
          actionInfo[key] = obj[key]
        }
      }

      return actionInfo
    },
    // 点击确认按钮
    async confirmEdit() {
      if (!this.$refs.form.validate()) return
      if (this.actionInfo.severityList.length === 0) {
        return this.$notify.info(
          'error',
          this.$t('alertAction.hint.require', [
            this.$t('alertAction.headers.severity'),
          ]),
        )
      }
      if (
        ['1', '2'].indexOf(this.actionInfo.warnClassifyType) !== -1 &&
        this.actionInfo.warnClassifyList.length === 0
      ) {
        this.$nextTick(() => {
          let isError = document.getElementsByClassName('alert-box')
          isError[0].scrollIntoView({
            block: 'start',
            behavior: 'smooth',
          })
        })
        return this.$notify.info(
          'error',
          this.$t('alertAction.hint.require', [
            this.$t('alertAction.headers.warnClassifyTypeName'),
          ]),
        )
      }
      if (['1', '2', '4'].indexOf(this.actionInfo.disposeType) !== -1) {
        if (this.actionInfo.recipientsList.length === 0) {
          return this.$notify.info(
            'error',
            this.$t('alertAction.hint.require', [
              this.$t('alertAction.headers.receiveUser'),
            ]),
          )
        }
        // recipientsList 数据过筛，避免修改时将已删除用户id传给后台
        if (this.editType === 'edit') {
          this.actionInfo.recipientsList =
            this.actionInfo.recipientsList.filter(a =>
              this.actionsList.some(b => b.userId === a),
            )
        }
        if (!this.actionInfo.noticeContent) {
          return this.$notify.info(
            'error',
            this.$t('alertAction.hint.require', [
              this.$t('alertAction.headers.noticeContent'),
            ]),
          )
        }
      }

      try {
        const params = deepClone(this.actionInfo)
        if (params.warnClassifyType === '0') {
          params.warnClassifyList = []
        }
        if (this.editType === 'add') {
          const res = await addAction(params)
          if (res.code === 200) {
            this.$notify.info(
              'success',
              this.$t('global.hint.add', [this.$t('alertAction.currentTitle')]),
            )
            this.$router.go(-1)
          }
        } else {
          const res = await updateAction(params)
          if (res.code === 200) {
            this.$notify.info(
              'success',
              this.$t('global.hint.edit', [
                this.$t('alertAction.currentTitle'),
              ]),
            )
            this.$router.go(-1)
          }
        }
      } catch (e) {
        console.error(`操作错误：${e}`)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.response-center-box {
  font-size: 14px;

  .logic-engine__error {
    font-size: 12px;
    color: var(--v-error-base) !important;
  }
  ::v-deep .asset-box .v-select.v-select--chips .v-select__selections {
    min-height: auto !important;
  }
  ::v-deep .v-slide-group__prev,
  .v-slide-group__next {
    flex: 1 !important;
    min-width: 16px !important;
  }
  ::v-deep .v-slide-group__prev {
    margin-right: 24px !important;
  }
  ::v-deep .v-slide-group__next {
    margin-left: 24px !important;
  }
  ::v-deep .v-input--selection-controls {
    margin-top: 0 !important;
    padding-top: 0 !important;
  }
  ::v-deep .v-slide-group__wrapper {
    box-shadow: none !important;
  }

  ::v-deep .v-input--checkbox .v-input__slot {
    margin-bottom: 0;
    height: 36px !important;
  }
  .editor {
    height: 200px !important;
  }
  // .editor {
  //   height: 200px !important;
  //   ::v-deep .ql-editor {
  //     padding: 8px 12px !important;
  //     color: var(--v-color-base) !important;
  //     font-size: 14px !important;
  //   }
  //   ::v-deep .ql-container.ql-snow {
  //     border-color: #e0dede !important;
  //     border-radius: 5px !important;
  //   }
  //   ::v-deep .ql-editor.ql-blank::before {
  //     font-style: normal !important;
  //     color: rgba(94, 86, 105, 0.68) !important;
  //     font-size: 1rem !important;
  //   }
  //   ::v-deep .ql-container.ql-snow.ql-disabled {
  //     .ql-editor {
  //       color: rgba(94, 86, 105, 0.38) !important;
  //     }
  //   }
  // }
}
</style>
