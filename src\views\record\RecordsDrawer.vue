<!-- 告警抽屉 width="24rem" -->
<template>
  <vsoc-drawer
    :title="$t('action.advanced')"
    :value="value"
    @click:confirm="doQuery"
    @input="close"
    @click:close="close"
    @click:cancel="close"
    class="record-drawer"
  >
    <template #right-title>
      <v-btn icon class="no-hb ml-4" text @click="clearAdvanceQuery">
        <v-icon size="16">mdi-filter-variant-remove</v-icon>
      </v-btn>
    </template>
    <v-form ref="form" v-model="valid">
      <vsoc-date-range
        ref="dateInput"
        v-model="dateRange.range"
        no-title
        :menu-props="dateRange.menuProps"
        @input="onChangeDate"
        class="date-input mt-2"
      >
        <template v-slot:text="{ on, attrs }">
          <v-text-field
            clearable
            class="append-icon-max me-3"
            readonly
            hide-details
            color="primary"
            large
            :label="$t('global.createDate')"
            append-icon="mdi-calendar-range-outline"
            v-bind="attrs"
            v-on="on"
            :value="RANGE_STR(advanceQuery.startDate, advanceQuery.endDate)"
            @click:clear="onChangeDate({ start: '', end: '' })"
          ></v-text-field>
        </template>
      </vsoc-date-range>

      <div class="text-base color-base font-weight-semibold-light mt-6 mb-2">
        {{ $t('record.headers.status') }}
      </div>
      <v-radio-group
        v-model="advanceQuery.status"
        row
        hide-details
        color="primary"
        class="ma-0 pa-0"
      >
        <v-row class="ma-0">
          <v-col
            v-for="item in disposeResultStatusEum"
            :key="item.value"
            class="pa-0"
          >
            <v-radio :value="item.value" class="mr-0">
              <template #label>
                <v-badge
                  dot
                  inline
                  offset-x="10"
                  :offset-y="-18"
                  :color="item.color"
                  class="mr-1"
                ></v-badge>
                <span class="text-base color-base">{{ item.text }}</span>
              </template>
            </v-radio>
          </v-col>
        </v-row>
      </v-radio-group>

      <v-text-field
        v-model="advanceQuery.alarmId"
        color="primary"
        :label="$t('record.headers.alarmId')"
        type="number"
        hide-spin-buttons
        :rules="idRules"
        class="mt-6"
      ></v-text-field>

      <v-text-field
        v-model="advanceQuery.disposeName"
        color="primary"
        :label="$t('record.headers.disposeName')"
        class="drawer-2"
      ></v-text-field>

      <v-autocomplete
        v-model="advanceQuery.disposeTypeList"
        :menu-props="{ offsetY: true, maxHeight: 300 }"
        :items="disposeTypeList"
        :label="$t('record.headers.disposeTypeName')"
        item-value="value"
        item-text="text"
        clearable
        multiple
        class="drawer-2"
      >
        <template v-slot:selection="{ index }">
          <span v-if="index === 0" class="text-caption">
            {{ $t('global.selected') }}{{ advanceQuery.disposeTypeList.length }}
          </span>
        </template>
      </v-autocomplete>

      <v-combobox
        v-model="advanceQuery.alarmTypeList"
        :menu-props="{ offsetY: true, maxHeight: 300 }"
        :items="alertTypes"
        :label="$t('alert.headers.type')"
        multiple
        class="mt-2"
        :search-input.sync="searchTag"
        :return-object="false"
      >
        <template v-slot:selection="{ item, index }">
          <v-chip
            small
            pill
            color="primary"
            close
            @click:close="removeTypes(index)"
          >
            {{ alertTypeMap[item] ? alertTypeMap[item].text : item }}
          </v-chip>
        </template>
        <template v-slot:no-data>
          <p
            class="pa-2 mb-0 text-body list-hover"
            @click="createType(searchTag)"
          >
            {{ $t('alert.hint.tip') }}：<strong>{{ searchTag }}</strong>
          </p>
        </template>
      </v-combobox>
      <!-- <v-autocomplete
        v-model="advanceQuery.alarmTypeList"
        :menu-props="{ offsetY: true, maxHeight: 300 }"
        :items="alertTypes"
        :label="$t('record.headers.alarmType')"
        item-value="id"
        item-text="name"
        :loading="isLoading"
        :search-input.sync="search"
        no-filter
        clearable
        multiple
        class="drawer-2"
      >
        <template v-slot:selection="{ index }">
          <span v-if="index === 0" class="text-caption">
            {{ $t('global.selected') }}{{ advanceQuery.alarmTypeList.length }}
          </span>
        </template>
      </v-autocomplete> -->
    </v-form>
  </vsoc-drawer>
</template>

<script>
import { max } from '@/@core/utils/validation'
import { RANGE_STR } from '@/components/vsoc-date-range/constants'
import VsocDateRange from '@/components/vsoc-date-range/VsocDateRange.vue'

// import { getAllData } from '@/api/detector'
import VsocDrawer from '@/components/VsocDrawer.vue'
export default {
  components: {
    VsocDrawer,
    VsocDateRange,
  },
  props: {
    value: Boolean,
    alertTags: Array,
  },
  data() {
    return {
      createing: false,
      searchTag: null,
      idRules: [v => max(v, 10)],
      valid: true,
      isLoading: false,
      search: null,
      dateRange: {
        range: {
          start: '',
          end: '',
        },
        menuProps: { offsetY: true, closeOnContentClick: false },
      },
      advanceQuery: {
        alarmId: '',
        alarmTypeList: [],
        disposeName: '',
        disposeTypeList: [],
        status: '',
        startDate: '',
        endDate: '',
      },

      // 告警类型
      alertTypes: [],
      alertTypeMap: {},
    }
  },
  watch: {
    search(val) {
      // if (this.isLoading) return
      // this.getSelectBox(val)
    },
  },
  computed: {
    disposeResultStatusEum() {
      return this.$store.state.enums.enums.DisposeResultStatus
    },
    disposeTypeList() {
      return this.$store.getters['enums/getDisposeType'].filter(
        v => v.value !== '3',
      )
    },
  },
  created() {
    this.getSelectBox()
  },
  methods: {
    removeTypes(index) {
      this.advanceQuery.alarmTypeList.splice(index, 1)
    },
    async createType(tagName) {
      if (this.createing) return
      this.createing = true
      // this.alertTypes.unshift(tagName)
      this.advanceQuery.alarmTypeList.push(tagName)
      this.searchTag = ''
      this.createing = false
    },
    async getSelectBox() {
      // const parms = {
      //   pageNum: 1,
      //   pageSize: 10,
      //   name: val || '',
      // }
      try {
        this.alertTypes = []
        const obj = {}

        this.alertTypeMap = obj
      } catch (e) {
        console.error(`获取告警类型数据：${e}`)
      } finally {
        // this.isLoading = false
      }
    },

    RANGE_STR,

    // 时间范围改变
    onChangeDate(range) {
      this.dateRange.range = range
      this.advanceQuery.startDate = range.start
      this.advanceQuery.endDate = range.end
    },
    close(bool) {
      if (!bool) {
        this.$emit('input', false)
      }
    },

    setModel(val) {
      this.dateRange.range = {
        start: val.startDate,
        end: val.endDate,
      }
      this.advanceQuery = val
      this.getSelectBox()
    },

    clearAdvanceQuery() {
      this.advanceQuery = {
        pageNum: 1,
        pageSize: 10,
        alarmId: '',
        alarmTypeList: [],
        disposeName: '',
        disposeTypeList: [],
        status: '',
        startDate: '',
        endDate: '',
      }
    },

    //  高级查询
    doQuery(callback) {
      const bool = this.$refs.form.validate()
      if (!bool) {
        return callback(false, true)
      }
      const params = {
        advanceQuery: this.advanceQuery,
        alertTypeMap: this.alertTypeMap,
      }
      this.$emit('do-query', params)
      callback()
    },
  },
}
</script>

<style lang="scss" scoped>
.date-input {
  ::v-deep .v-text-field.v-input--dense {
    margin-right: 0 !important;
  }
}
// .record-drawer {
//   ::v-deep .v-text-field .v-label {
//     color: var(--v-color-base) !important;
//   }
// }
</style>
