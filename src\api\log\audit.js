import { request, vsocPath } from '../../util/request'

export const getOperation = function (data) {
  return request({
    url: `${vsocPath}/operationController/selectOperation`,
    method: 'post',
    data,
  })
}

// 最大保留时长
export const editRetention = function (data) {
  return request({
    url: `${vsocPath}/operationController/updateRetention`,
    method: 'post',
    data,
  })
}

// 修改配置
export const editOperation = function (data) {
  return request({
    url: `${vsocPath}/operationController/updateOperation`,
    method: 'post',
    data,
  })
}
