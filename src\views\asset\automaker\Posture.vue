<template>
  <div>
    <bread-crumb>
      <template v-slot:left>
        <v-chip
          small
          label
          style="font-size: 12px"
          class="color-base ml-3 bg-btn"
        >
          {{ $t('global.pagination.total') }} {{ tableData.length }}
        </v-chip>
      </template>
      <template>
        <table-search
          :searchList="searchList"
          :searchQuery="query"
          @search="$_search"
        ></table-search>
      </template>
    </bread-crumb>
    <div
      v-if="tableData.length > 0"
      :style="{ height: `calc(100vh - ${topHeaderHeight}px)` }"
      class="overflow-auto scroll-bar-bg"
    >
      <div class="pt-0 pa-3 mt-n1 cards-container ma-auto">
        <div class="pa-1" v-for="item in tableData" :key="item.id">
          <v-card class="pa-4" @click="goAlert(item)" ripple>
            <v-card-text class="pa-0">
              <!-- 车企基本信息区域 -->
              <div class="d-flex align-center company-info-section">
                <!-- 车企LOGO -->
                <div class="logo-container mr-5">
                  <div class="logo-wrapper">
                    <v-img
                      v-if="item.pictureCode"
                      :src="item.pictureCode"
                      width="68"
                      height="68"
                      contain
                      class="logo-image"
                    ></v-img>
                    <div
                      v-else
                      class="logo-placeholder d-flex align-center justify-center"
                    >
                      <v-icon size="34" color="grey lighten-1"
                        >mdi-domain</v-icon
                      >
                    </div>
                  </div>
                </div>

                <!-- 车企名称 -->
                <div class="flex-grow-1 info-section">
                  <div class="d-flex align-center mb-3">
                    <h3
                      class="company-name text-h5 font-weight-bold color-base mb-0"
                      v-show-tips="item.name"
                    >
                      {{ item.name }}
                    </h3>
                  </div>
                  <div class="text-body-2 grey--text darken-1 update-time">
                    <v-icon small class="mr-1" color="grey darken-1"
                      >mdi-clock-outline</v-icon
                    >
                    更新时间：{{ item.time | toDate }}
                  </div>
                </div>
              </div>

              <!-- 风险评分和统计区域 -->
              <div
                class="d-flex justify-space-between align-center stats-section mx-1"
              >
                <!-- 安全等级 -->
                <div class="text-center level-section">
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <div
                        class="security-shield-wrapper mb-2"
                        v-bind="attrs"
                        v-on="on"
                      >
                        <v-icon
                          size="36"
                          :color="item.securityLevel.color"
                          class="security-shield"
                        >
                          mdi-shield
                        </v-icon>
                      </div>
                    </template>
                    <span>{{ item.securityLevel.text }}</span>
                  </v-tooltip>
                  <div class="text-body-2 grey--text">安全态势</div>
                </div>

                <!-- 风险评分 -->
                <div class="text-center score-section">
                  <div class="score-display">
                    <div
                      class="risk-score text-h4 font-weight-bold mb-1"
                      :style="{
                        color: item.securityLevel.color,
                      }"
                    >
                      {{ item.score || 0 }}
                    </div>
                    <div class="text-body-2 grey--text">风险评分</div>
                  </div>
                </div>

                <!-- 风险统计详情 -->
                <div class="text-center count-section">
                  <div class="count-display">
                    <div
                      class="risk-count text-h4 font-weight-bold mb-1 primary--text"
                    >
                      {{ item.count | toNumber }}
                    </div>
                    <div class="text-body-2 grey--text">风险统计</div>
                  </div>
                </div>
              </div>

              <!-- 风险分布区域 -->
              <div class="risk-distribution-section mx-1">
                <div
                  class="w-100 d-flex align-center overflow-hidden risk-distribution-container"
                >
                  <template v-if="item.totalNum > 0">
                    <v-tooltip
                      v-for="(v, index) in item.riskList"
                      :key="index"
                      bottom
                    >
                      <template v-slot:activator="{ on, attrs }">
                        <v-progress-linear
                          :value="100"
                          :color="v.color"
                          height="20"
                          :style="{
                            width: (v.displayPercent || v.percent) + '%',
                          }"
                          class="risk-progress-bar"
                          v-bind="attrs"
                          v-on="on"
                        >
                          <span
                            class="text-body-2 font-weight-bold"
                            style="color: #fff"
                            >{{ v.value }}</span
                          >
                        </v-progress-linear>
                      </template>
                      <span>
                        {{ getRiskLevelName(v.color) }}: {{ v.value }}
                        <span
                          v-if="
                            v.displayPercent && v.displayPercent !== v.percent
                          "
                        >
                          ( {{ parseFloat(v.percent).toFixed(2) }}%)
                        </span>
                      </span>
                    </v-tooltip>
                  </template>
                  <template v-else>
                    <div class="risk-empty-container">
                      <div
                        class="risk-empty-state d-flex align-center justify-center"
                      >
                        <span class="text-caption grey--text text--lighten-1"
                          >暂无风险</span
                        >
                      </div>
                    </div>
                  </template>
                </div>
              </div>
            </v-card-text>
          </v-card>
        </div>
      </div>
    </div>

    <div
      v-if="tableLoading === false && tableData.length === 0"
      style="height: 80vh"
      class="d-flex flex-column justify-center align-center"
    >
      <v-img
        max-width="22rem"
        max-height="22rem"
        contain
        :src="require('@/assets/images/tree-empty.png')"
      ></v-img>
      <div class="text--primary text-xxl">暂无已监管的车企数据</div>
      <div class="text-ml mt-1">请检查搜索条件或联系管理员</div>
    </div>
  </div>
</template>

<script>
import { getAutomakerList } from '@/api/asset/automaker'
import breadCrumb from '@/components/bread-crumb/index'
import TableSearch from '@/components/TableSearch/index.vue'
import { getAutomakerSecurityLevelUnified } from '@/util/algorithm'

export default {
  name: 'AutomakerPosture',
  components: {
    breadCrumb,
    TableSearch,
  },
  data() {
    return {
      query: {
        automaker_name: '',
      },
      tableData: [],
      tableLoading: false,
      // groupList: [],
    }
  },
  filters: {
    toNumber(num) {
      // 小数
      // return num.toLocaleString('en-US')
      // 整数
      return String(num).replace(/(\d)(?=(\d{3})+$)/g, '$1,')
    },
  },
  computed: {
    searchList() {
      return [
        {
          type: 'input',
          value: 'automaker_name',
          text: '车企名称',
        },
      ]
    },
    healthStatus() {
      return this.$store.state.enums.enums.HealthStatus
    },
    topHeaderHeight() {
      return this.$store.getters['global/getTopHeaderHeight']
    },
  },

  mounted() {
    this.$_getTableData()
  },
  methods: {
    // 根据风险评分计算安全等级（统一版）
    calculateSecurityLevel(riskDetails = null, riskScore = 0) {
      // 使用统一的智能算法
      return getAutomakerSecurityLevelUnified({
        riskDetails: riskDetails,
        riskScore: riskScore,
        scoreType: 'auto',
      })
    },

    // 计算风险详情总数
    calculateRiskTotal(riskDetails) {
      if (!riskDetails) return 0
      return (
        (riskDetails.critical || 0) +
        (riskDetails.high || 0) +
        (riskDetails.medium || 0) +
        (riskDetails.low || 0) +
        (riskDetails.unassigned || 0)
      )
    },

    // 根据颜色获取风险等级名称
    getRiskLevelName(color) {
      const colorMap = {
        '#DA1F1F': '严重',
        '#FA9114': '高',
        '#F5D018': '中',
        '#12B2A7': '低',
        '#B4BBCC': '未分级',
      }
      return colorMap[color] || '未知'
    },

    goAlert(record) {
      // 跳转到告警详情页面或相关页面
      this.$router.push({
        path: '/alerts',
        query: {
          id: record.automakerCode,
        },
      })
    },

    onClear() {
      this.query.automaker_name = ''
      this.$_search()
    },
    // 搜索方法
    $_search() {
      this.query.pageNum = 1
      this.$_getTableData()
    },
    async $_getTableData() {
      try {
        this.tableLoading = true
        this.tableData = []

        // 调用车企列表接口
        const res = await getAutomakerList(this.query)

        // 筛选已监管的车企并进行数据映射
        const supervisedAutomakers = (res.data || [])
          .filter(item => item.isSupervise === '0') // 只显示已监管的车企
          .map(item => {
            const securityLevel = this.calculateSecurityLevel(
              item.riskDetails,
              item.riskScore,
            )
            const riskTotal = this.calculateRiskTotal(item.riskDetails)

            // 生成风险分布列表
            let riskList = []
            if (riskTotal > 0 && item.riskDetails) {
              const allRisks = [
                {
                  percent: (
                    (item.riskDetails.critical / riskTotal) *
                    100
                  ).toFixed(4),
                  value: item.riskDetails.critical || 0,
                  color: '#DA1F1F',
                },
                {
                  percent: ((item.riskDetails.high / riskTotal) * 100).toFixed(
                    4,
                  ),
                  value: item.riskDetails.high || 0,
                  color: '#FA9114',
                },
                {
                  percent: (
                    (item.riskDetails.medium / riskTotal) *
                    100
                  ).toFixed(4),
                  value: item.riskDetails.medium || 0,
                  color: '#F5D018',
                },
                {
                  percent: ((item.riskDetails.low / riskTotal) * 100).toFixed(
                    4,
                  ),
                  value: item.riskDetails.low || 0,
                  color: '#12B2A7',
                },
                {
                  percent: (
                    (item.riskDetails.unassigned / riskTotal) *
                    100
                  ).toFixed(4),
                  value: item.riskDetails.unassigned || 0,
                  color: '#B4BBCC',
                },
              ]

              // 只过滤掉百分比为0的项，保留所有有意义的风险等级
              riskList = allRisks.filter(risk => parseFloat(risk.percent) > 0)

              // 处理极小占比的显示问题，确保每个有数据的级别都能显示
              if (riskList.length > 0) {
                const minDisplayPercent = 3 // 最小显示占比3%
                let totalAdjustedPercent = 0

                // 第一步：标记需要调整的项目并计算调整后的总占比
                riskList.forEach(risk => {
                  const originalPercent = parseFloat(risk.percent)
                  if (
                    originalPercent > 0 &&
                    originalPercent < minDisplayPercent
                  ) {
                    risk.displayPercent = minDisplayPercent
                  } else {
                    risk.displayPercent = originalPercent
                  }
                  totalAdjustedPercent += risk.displayPercent
                })

                // 第二步：如果调整后总占比超过100%，按比例缩放
                if (totalAdjustedPercent > 100) {
                  const scaleFactor = 100 / totalAdjustedPercent
                  riskList.forEach(risk => {
                    risk.displayPercent = (
                      risk.displayPercent * scaleFactor
                    ).toFixed(2)
                  })
                } else {
                  // 确保显示占比为字符串格式，保留两位小数
                  riskList.forEach(risk => {
                    risk.displayPercent = risk.displayPercent.toFixed(2)
                  })
                }
              }
            }

            return {
              id: item.id,
              name: item.automakerName, // 车企名称
              score: item.riskScore || 0, // 风险评分
              securityLevel: securityLevel, // 安全等级信息
              level: securityLevel.level, // 安全等级
              description: `${securityLevel.text} - 风险评分: ${
                item.riskScore || 0
              }`,
              pictureCode: item.pictureCode || '', // 车企LOGO
              time: item.updateDate, // 更新时间
              count: riskTotal, // 风险统计总数
              totalNum: riskTotal, // 总数量（用于模板兼容）
              riskDetails: item.riskDetails, // 风险详情
              riskList: riskList, // 风险分布列表
              // 保持原有字段以兼容模板
              automakerName: item.automakerName,
              automakerCode: item.automakerCode, // 车企编码，用于跳转
              riskScore: item.riskScore || 0,
              updateDate: item.updateDate,
            }
          })

        this.tableData = supervisedAutomakers
        this.tableDataTotal = supervisedAutomakers.length
      } catch (e) {
        console.error(`获取车企安全态势失败：${e}`)
      }
      this.tableLoading = false
    },
  },
}
</script>
<style scoped lang="scss">
.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  grid-gap: 12px;
  width: auto;
  padding: 12px;
}

@media (min-width: 1600px) {
  .cards-container {
    grid-template-columns: repeat(auto-fill, minmax(420px, 1fr));
    grid-gap: 16px;
    padding: 16px;
  }
}

@media (max-width: 768px) {
  .cards-container {
    grid-template-columns: 1fr;
    padding: 8px;
    grid-gap: 8px;
  }
}
.v-card:hover {
  box-shadow: 0 -3px 0 0 $primary inset, 3px 3px 10px 0px rgba(0, 0, 0, 10%) !important;
}

.primary--hover:hover {
  color: $primary !important;
}

/* 车企态势卡片样式优化 */
.logo-container {
  flex-shrink: 0;

  .logo-wrapper {
    padding: 4px;
    border-radius: 12px;
    background: linear-gradient(145deg, #f8f9fa 0%, #e9ecef 100%);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transform: translateY(-1px);
    }
  }

  .logo-image {
    border-radius: 8px;
    border: 2px solid #e0e0e0;
    background-color: #fafafa;
  }

  .logo-placeholder {
    width: 68px;
    height: 68px;
    border-radius: 8px;
    border: 2px dashed #d0d0d0;
    background: linear-gradient(145deg, #f8f9fa 0%, #e9ecef 100%);
  }
}

/* Card内部区域间距优化 */
.company-info-section {
  margin-bottom: 12px;
  // padding-bottom: 20px;
  // border-bottom: 1px solid #f0f0f0;
}

/* 安全盾牌样式 */
.security-shield-wrapper {
  display: inline-block;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.1);
  }
}

.security-shield {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
  transition: all 0.3s ease;

  &:hover {
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
  }
}

.info-section {
  min-height: 64px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.company-name {
  line-height: 1.2;
  letter-spacing: 0.5px;
}

.security-level-chip-enhanced {
  font-size: 11px !important;
  height: 24px !important;
  border-width: 1.5px !important;
}

.update-time {
  opacity: 0.8;
}

.stats-section {
  padding: 20px 0;
  border-top: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
  background: rgba(248, 249, 250, 0.5);
  border-radius: 8px;
  margin: 0 -20px 20px -20px;
  padding-left: 20px;
  padding-right: 20px;
}

.level-section,
.score-section,
.count-section {
  flex: 1;
  padding: 0 16px;

  &:first-child {
    padding-left: 8px;
  }

  &:last-child {
    padding-right: 8px;
  }
}

/* 移除原来的芯片样式，现在使用盾牌 */

.score-display,
.count-display {
  padding: 8px;
  border-radius: 8px;
}

.risk-score {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.risk-count {
  text-shadow: 0 1px 2px rgba(25, 118, 210, 0.2);
}

/* 安全等级芯片样式 */
.security-level-chip {
  border-width: 1px !important;
  background-color: transparent !important;
  font-size: 11px;
}

/* 圆环图容器样式 */
.risk-chart-container {
  min-width: 160px;
}

.risk-donut-chart {
  position: relative;
  display: inline-block;

  .donut-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #666;
  }

  .risk-segment {
    transition: stroke-width 0.3s ease;

    &:hover {
      stroke-width: 8;
    }
  }
}

.risk-legend {
  .legend-item {
    font-size: 11px;
    line-height: 1.2;

    .legend-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      flex-shrink: 0;

      &.critical {
        background-color: #da1f1f;
      }

      &.high {
        background-color: #ff910f;
      }

      &.medium {
        background-color: #44e2fe;
      }

      &.low {
        background-color: #2ee56a;
      }
    }
  }
}

/* 车企名称文本溢出处理 */
.text-h6 {
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 卡片质感优化 */
.v-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-bottom: 3px solid transparent;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
  border-radius: 12px !important;
  background: linear-gradient(145deg, #ffffff 0%, #fafafa 100%);

  &:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15) !important;
    border-bottom-color: var(--v-primary-base) !important;
    background: #ffffff;
  }

  &:active {
    transform: translateY(-2px);
    transition-duration: 0.1s;
  }
}

/* 风险分布进度条样式 */
.risk-progress-bar {
  border-radius: 0 !important;
  min-width: 1px; /* 确保极小占比也能显示 */

  &:first-child {
    border-radius: 4px 0 0 4px !important;
  }

  &:last-child {
    border-radius: 0 4px 4px 0 !important;
  }

  &:only-child {
    border-radius: 4px !important;
  }

  .v-progress-linear__background {
    opacity: 0 !important;
  }

  .v-progress-linear__buffer {
    opacity: 0 !important;
  }

  /* 当宽度很小时，确保数字仍然可见 */
  .v-progress-linear__content {
    min-width: 20px;
    text-align: center;

    span {
      font-size: 11px;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    }
  }
}

/* 风险分布容器 */
.risk-distribution {
  border-radius: 2px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 风险分布区域样式 */
.risk-distribution-section {
  margin-top: 16px;
}

.risk-distribution-container {
  height: 20px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 风险分布空状态样式 */
.risk-empty-container {
  width: 100%;
  height: 20px;
  border-radius: 4px;
}

.risk-empty-state {
  height: 100%;
  background: #fafafa;
  border: 1px dashed #e0e0e0;
  border-radius: 4px;
  opacity: 0.7;
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 0.8;
  }
}
</style>
