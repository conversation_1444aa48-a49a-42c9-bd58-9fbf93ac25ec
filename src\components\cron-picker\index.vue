<template>
  <vsoc-dialog
    :title="$t('infer.expression.title') + '(Quartz)'"
    :value="show"
    @input="$emit('update:show', $event)"
    @click:confirm="save"
    width="900"
  >
    <cron-picker
      :interval="interval"
      :cron="cron"
      class="mb-1"
      @change="onChange"
    />
    <!--  (${cronMessage})v-if="interval != 'advanced'" -->
    <div class="pt-4">
      <span class="text-sm"
        >{{ $t('infer.expression.old') }}：<span class="font-weight-semibold"
          >{{ `${value}` }}
        </span></span
      >
      <span class="text-sm primary--text pl-4"
        >{{ $t('infer.expression.new') }}：<span class="font-weight-semibold">{{
          `${cron}`
        }}</span>
      </span>
    </div>
  </vsoc-dialog>
</template>

<script>
import VsocDialog from '@/components/VsocDialog.vue'
import CronPicker from './components/index'
import { getExpressionType, validExpression } from './utils'

export default {
  name: 'CronPickerDialog',
  components: {
    VsocDialog,
    CronPicker,
  },
  props: {
    value: {
      type: [Number, String],
      default: '0 0 0/1 * * ?',
    },
    show: Boolean,
  },
  data() {
    return {
      cronKey: '',
      editCronValue: this.value,
      cron: this.value,
      interval: 'minute', // 调度周期
      debounceFilter: null,
      cronMessage: '',
    }
  },
  watch: {
    value(newVal) {
      if (newVal === this.editCronValue) {
        return
      }
      this.editCronValue = newVal
      this.cron = newVal
    },

    editCronValue(newVal) {
      this.$emit('change', newVal)
    },

    show(newVal) {
      newVal && this.initCronValue(this.value)
    },

    // cron() {
    //     this.parseCron()
    // }
  },

  created() {
    // this.parseCron()
  },

  methods: {
    initCronValue(cronValue) {
      const data = validExpression(cronValue, 'noTips')
      if (data.isChecked) {
        this.interval = getExpressionType(cronValue).type
      } else {
        this.interval = 'advanced'
      }
      this.cron = cronValue
    },

    // change 事件会返回新的 interval 和 cron
    onChange(res) {
      this.interval = res.interval
      this.cron = res.cron
    },

    save(cb) {
      const data = validExpression(this.cron)
      if (data.isChecked) {
        this.editCronValue = this.cron
        this.$emit('update:show', false)
        cb()
      } else {
        this.$notify('error', data.message)
        cb(false, true)
      }
    },

    parseCron() {
      const data = validExpression(this.cron)
      this.cronMessage = data.message
    },
  },
}
</script>

<style lang="scss" scoped></style>
