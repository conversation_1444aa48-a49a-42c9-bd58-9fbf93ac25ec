<template>
  <vsoc-drawer
    :title="$t('action.advanced')"
    :value="value"
    @click:confirm="doQuery"
    @input="close"
    @click:close="close"
    @click:cancel="close"
    class="ticket-draw-box"
  >
    <template #right-title>
      <v-btn icon class="no-hb ml-4" text @click="clearAdvanceQuery">
        <v-icon size="16">mdi-filter-variant-remove</v-icon>
      </v-btn>
    </template>
    <div>
      <vsoc-date-range
        ref="dateInput"
        v-model="dateRange.range"
        no-title
        :menu-props="dateRange.menuProps"
        @input="onChangeDate"
        class="date-input mt-2"
      >
        <template v-slot:text="{ on, attrs }">
          <v-text-field
            clearable
            class="append-icon-max me-3"
            readonly
            hide-details
            color="primary"
            large
            :label="$t('ticket.headers.createDate')"
            append-icon="mdi-calendar-range-outline"
            :value="RANGE_STR(advanceQuery.startDate, advanceQuery.endDate)"
            v-bind="attrs"
            @click:clear="onChangeDate({ start: '', end: '' })"
            v-on="on"
          ></v-text-field>
        </template>
      </vsoc-date-range>

      <vsoc-date-range
        ref="dateInput"
        v-model="dateRange1.range"
        no-title
        :menu-props="dateRange1.menuProps"
        @input="onChangeDate1"
        class="date-input mt-6"
      >
        <template v-slot:text="{ on, attrs }">
          <v-text-field
            clearable
            class="append-icon-max me-3"
            readonly
            hide-details
            color="primary"
            large
            :label="$t('ticket.headers.eta')"
            append-icon="mdi-calendar-range-outline"
            :value="
              RANGE_STR(advanceQuery.etaStartDate, advanceQuery.etaEndDate)
            "
            v-bind="attrs"
            @click:clear="onChangeDate1({ start: '', end: '' })"
            v-on="on"
          ></v-text-field>
        </template>
      </vsoc-date-range>

      <div class="text-base color-base font-weight-semibold-light mt-6 mb-2">
        {{ $t('ticket.headers.priority') }}
      </div>
      <div class="d-flex flex-wrap justify-lg-space-between">
        <v-checkbox
          v-for="(item, index) in levelList"
          :key="item.text"
          v-model="advanceQuery.priorities"
          color="primary"
          :value="item.dictId"
          hide-details
          class="mt-0 w-50 mb-4 pa-0"
          multiple
        >
          <template v-slot:label>
            <div class="d-flex align-center text-center">
              <v-icon
                :color="
                  ticketLevel[item.dictId] && ticketLevel[item.dictId].color
                "
                size="16"
                class="mr-3"
              >
                mdi-alert-circle
              </v-icon>
              <span class="text-base color-base">
                {{ item.dictName }}
              </span>
            </div>
          </template>
        </v-checkbox>
      </div>
      <div class="text-base color-base font-weight-semibold-light mt-2 mb-2">
        {{ $t('ticket.headers.status') }}
      </div>
      <div class="d-flex flex-wrap justify-lg-space-between">
        <v-checkbox
          v-for="(item, i) in ticketList"
          :key="i"
          v-model="advanceQuery.statuses"
          color="primary"
          :value="item.dictId"
          column
          hide-details
          class="mt-0 w-50 pa-0 mb-4"
          multiple
          :disabled="advanceQuery.showSelectFlag"
        >
          <template v-slot:label>
            <div class="d-flex align-center text-center">
              <v-icon
                size="18"
                class="mr-3"
                :color="
                  ticketStatus[item.dictId] && ticketStatus[item.dictId].color
                "
              >
                mdi-circle-medium
              </v-icon>
              <span class="text-base color-base">
                {{ item.dictName }}
              </span>
            </div>
          </template>
        </v-checkbox>
      </div>

      <!-- 车型 -->
      <!-- <v-combobox
        v-model="advanceQuery.affectModel"
        dense
        :items="modelList"
        class="drawer-2 mt-3"
        color="primary"
        :label="$t('asset.headers.model')"
      ></v-combobox> -->
      <v-select
        v-model="advanceQuery.affectModel"
        color="primary"
        :items="modelList"
        :label="$t('ticket.headers.models')"
        class="drawer-2"
        :menu-props="{ offsetY: true }"
      />
      <v-select
        v-model="advanceQuery.classifies"
        color="primary"
        :items="ticketClassify"
        :label="$t('ticket.headers.classifyName')"
        multiple
        class="drawer-2"
        item-text="name"
        item-value="id"
        :menu-props="{ offsetY: true }"
      >
        <template v-slot:selection="{ index }">
          <span v-if="index === 0">
            {{ $t('global.pagination.selected') }}：{{
              advanceQuery.classifies.length
            }}
          </span>
        </template>
      </v-select>

      <v-text-field
        v-model="advanceQuery.ticketId"
        color="primary"
        :label="$t('ticket.headers.ticketId')"
        class="drawer-2"
      ></v-text-field>

      <v-text-field
        v-model="advanceQuery.title"
        color="primary"
        :label="$t('ticket.headers.title')"
        class="drawer-2"
      ></v-text-field>

      <v-autocomplete
        v-model="advanceQuery.createUsers"
        multiple
        :menu-props="{ offsetY: true, maxHeight: 300 }"
        append-icon="mdi-chevron-down"
        :items="userList"
        :label="$t('ticket.headers.createUser')"
        class="drawer-2"
      >
        <template v-slot:selection="{ index }">
          <span v-if="index === 0">
            {{ $t('global.pagination.selected') }}：{{
              advanceQuery.createUsers.length
            }}
          </span>
        </template>
      </v-autocomplete>
      <!-- 来源 -->
      <v-autocomplete
        v-model="advanceQuery.dataSourceList"
        multiple
        :menu-props="{ offsetY: true, maxHeight: 300 }"
        append-icon="mdi-chevron-down"
        :items="dataList"
        item-text="dictName"
        item-value="dictId"
        :label="$t('ticket.headers.dataSourceName')"
        class="drawer-2"
      >
        <template v-slot:selection="{ index }">
          <span v-if="index === 0">
            {{ $t('global.pagination.selected') }}：{{
              advanceQuery.dataSourceList.length
            }}
          </span>
        </template>
      </v-autocomplete>
      <v-autocomplete
        v-model="advanceQuery.assignedGroups"
        multiple
        :menu-props="{ offsetY: true, maxHeight: 300 }"
        append-icon="mdi-chevron-down"
        :items="allUserList"
        item-text="name"
        item-value="id"
        class="drawer-2"
        :label="$t('ticket.headers.assignedGroupName')"
      >
        <template v-slot:selection="{ index }">
          <span v-if="index === 0">
            {{ $t('global.pagination.selected') }}：{{
              advanceQuery.assignedGroups.length
            }}
          </span>
        </template>
      </v-autocomplete>

      <!-- 处理人 -->
      <v-autocomplete
        v-model="advanceQuery.assignedTos"
        multiple
        :menu-props="{ offsetY: true, maxHeight: 300 }"
        append-icon="mdi-chevron-down"
        :items="userList"
        :label="$t('ticket.headers.assignedToName')"
        class="drawer-2"
      >
        <template v-slot:selection="{ index }">
          <span v-if="index === 0">
            {{ $t('global.pagination.selected') }}：{{
              advanceQuery.assignedTos.length
            }}
          </span>
        </template>
      </v-autocomplete>

      <!-- 关注人 -->
      <v-autocomplete
        v-model="advanceQuery.watchList"
        :menu-props="{ offsetY: true, maxHeight: 300 }"
        append-icon="mdi-chevron-down"
        :items="userList"
        :label="$t('ticket.headers.WatchList')"
      >
      </v-autocomplete>
    </div>
  </vsoc-drawer>
</template>

<script>
import elTree from '@/components/search-tree/index'
import { RANGE_STR } from '@/components/vsoc-date-range/constants'
import VsocDateRange from '@/components/vsoc-date-range/VsocDateRange.vue'

import VsocDrawer from '@/components/VsocDrawer.vue'
import { endOfDay, format, startOfDay, subDays } from 'date-fns'
export default {
  components: {
    VsocDrawer,
    VsocDateRange,
    elTree,
  },
  props: {
    value: Boolean,
    userList: Array,
    groupList: Array,
    ticketClassify: Array,
    ticketList: Array,
    levelList: Array,
    dataList: Array,
    modelList: Array,
  },
  data() {
    return {
      defaultProps: {
        children: 'children',
        label: 'name',
      },
      // show: false,
      dateRange: {
        range: {
          start: '',
          end: '',
        },
        menuProps: { offsetY: true, closeOnContentClick: false },
      },
      dateRange1: {
        range: {
          start: '',
          end: '',
        },
        menuProps: { offsetY: true, closeOnContentClick: false },
      },
      advanceQuery: {
        ticketId: '', // 工单编号
        title: '',
        startDate: '', // 起始时间
        endDate: '', // 结束时间
        affectModel: '',
        classifies: [],
        priorities: [],
        statuses: [],
        createUsers: [],
        assignedGroups: [],
        assignedTos: [],
        dataSourceList: [],
        pageShowType: '0', // pageShowType  0-待认领 1-我的代办 2-我的申请单  3-全部工单
        pageNum: 1, // 当前页码
        pageSize: 10,
        showSelectFlag: false,
        etaStartDate: '',
        etaEndDate: '',
        watchList: '',
      },
      allUserList: [],
    }
  },
  computed: {
    ticketLevel() {
      return this.$store.state.enums.enums.TicketLevel
    },
    ticketStatus() {
      return this.$store.state.enums.enums.TicketStatus
    },
  },
  created() {},

  methods: {
    // changeUser() {
    //   let allUserList = []
    //   this.advanceQuery.dealUserList = []
    //   if (this.advanceQuery.dealType === '1') {
    //     allUserList = this.groupList
    //   } else {
    //     allUserList = this.userList
    //   }
    //   this.allUserList = allUserList
    // },
    RANGE_STR,

    // 时间范围改变
    onChangeDate(range) {
      this.dateRange.range = range
      this.advanceQuery.startDate = range.start
      this.advanceQuery.endDate = range.end
    },
    onChangeDate1(range) {
      this.dateRange1.range = range
      this.advanceQuery.etaStartDate = range.start
      this.advanceQuery.etaEndDate = range.end
    },
    close(bool) {
      if (!bool) {
        this.$emit('input', false)
      }
    },
    // changeTree(data, e, assignedTos) {
    //   if (e && e.checkedKeys.length) {
    //     this.$refs['elTree'].$refs.tree.setCheckedKeys([data.id])
    //     this.$refs['elTree'].filterText = data.name
    //     this.advanceQuery.assignedGroups[0] = data.id
    //     this.advanceQuery.assignedTos = assignedTos || []
    //     this.allUserList = this.userList.filter(
    //       v => (v.departments.length && v.departments[0].id) === data.id,
    //     )
    //     // this.show = true
    //   } else {
    //     this.$refs['elTree'].$refs.tree.setCheckedKeys([])
    //     this.$refs['elTree'].filterText = ''
    //     this.advanceQuery.assignedGroups = []
    //     // this.show = false
    //   }
    // },
    setModel(val) {
      this.dateRange.range = {
        start: val.startDate,
        end: val.endDate,
      }
      this.allUserList = this.flat(this.groupList)
      this.advanceQuery = val
      // this.$nextTick(() => {
      //   if (this.advanceQuery.assignedGroups.length) {
      //     const data = {
      //       id: this.advanceQuery.assignedGroups[0],
      //       name:
      //         this.flat(this.groupList).find(
      //           v => v.id === this.advanceQuery.assignedGroups[0],
      //         ).name || '',
      //     }
      //     this.changeTree(
      //       data,
      //       {
      //         checkedKeys: this.advanceQuery.assignedGroups,
      //       },
      //       this.advanceQuery.assignedTos,
      //     )
      //   } else {
      //     this.changeTree()
      //   }
      // })
    },
    flat(arr) {
      let newArr = []
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].children) {
          newArr.push(...this.flat(arr[i].children))
        }
        newArr.push({ ...arr[i] })
      }
      return newArr
    },
    clearAdvanceQuery() {
      this.advanceQuery = {
        ticketId: '', // 工单编号
        title: '',
        startDate: format(
          startOfDay(subDays(new Date(), 30)),
          'yyyy-MM-dd HH:mm:ss',
        ), // 起始时间
        endDate: format(endOfDay(new Date()), 'yyyy-MM-dd HH:mm:ss'), // 结束时间
        affectModel: '',
        classifies: [],
        priorities: [],
        statuses: this.advanceQuery.showSelectFlag
          ? this.advanceQuery.statuses
          : [],
        createUsers: [],
        assignedGroups: [],
        assignedTos: [],
        dataSourceList: [],
        pageShowType: this.advanceQuery.pageShowType, // pageShowType  0-待认领 1-我的代办 2-我的申请单  3-全部工单
        pageNum: 1, // 当前页码
        pageSize: 10,
        showSelectFlag: this.advanceQuery.showSelectFlag,
        etaStartDate: '',
        etaEndDate: '',
        watchList: '',
      }
      // this.changeTree()
    },

    //  高级查询
    doQuery(callback) {
      setTimeout(() => {
        this.$emit('do-query', this.advanceQuery)
        callback()
      }, 180)
    },
  },
}
</script>

<style lang="scss" scoped>
.date-input {
  ::v-deep .v-text-field.v-input--dense {
    margin-right: 0 !important;
  }
}
</style>
