<template>
  <v-dialog v-model="isDialogVisible" width="360" persistent no-click-animation>
    <v-card>
      <v-card-title
        class="d-flex align-center justify-space-between py-0 px-4 font-weight-semibold py-3"
      >
        <div>
          {{ $t('login.verify.hint') }}
        </div>
        <v-icon
          size="1.5rem"
          class="cursor-pointer"
          @click="isDialogVisible = false"
        >
          mdi-close-circle
        </v-icon>
      </v-card-title>
      <v-divider></v-divider>
      <v-card-text class="px-4 py-4">
        <!-- <slide-verify
          ref="slideverify"
          :w="325"
          :h="180"
          slider-text="向右拖动滑块填充拼图"
          :imgs="imgs"
          @success="onSuccess"
        ></slide-verify> -->
        <solide
          ref="slideverify"
          :w="325"
          :h="180"
          :l="42"
          :r="10"
          :slider-text="$t('login.verify.sliderText')"
          :imgs="imgs"
          @success="onSuccess"
          :accuracy="6"
        >
        </solide>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
import solide from '@/components/Solide.vue'
export default {
  name: 'SlideBox',
  components: {
    solide,
  },
  props: ['successFun'],
  data() {
    return {
      isDialogVisible: false,
      imgs: [
        require('@/assets/images/loginBg/1.png'),
        require('@/assets/images/loginBg/2.png'),
        require('@/assets/images/loginBg/3.png'),
        require('@/assets/images/loginBg/4.png'),
        require('@/assets/images/loginBg/5.png'),
        require('@/assets/images/loginBg/6.png'),
        require('@/assets/images/loginBg/7.png'),
        require('@/assets/images/loginBg/8.png'),
        require('@/assets/images/loginBg/9.png'),
        require('@/assets/images/loginBg/10.png'),
      ],
    }
  },
  mounted() {},
  methods: {
    onSuccess() {
      this.successFun()
    },
    onReset() {
      //重置图片验证组件
      this.$nextTick(() => {
        this.$refs.slideverify.reset()
      })
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .slide-verify-slider {
  font-size: 14px !important;
}
// ::v-deep .slide-verify-slider-mask-item:hover {
//   background: $primary !important;
// }
// ::v-deep .slide-verify-slider-mask-item {
//   height: 38px !important;
// }
// ::v-deep .container-active .slide-verify-slider-mask-item {
//   border: 1px solid $primary !important;
// }
</style>
