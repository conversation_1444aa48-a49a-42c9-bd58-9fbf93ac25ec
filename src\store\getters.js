import { getLocalStorage } from '@/util/localStorage'
export default {
  // appBarTitle(state) {
  //     return state.layout.appbarTitle
  // },
  // menus: state => state.permission.menus,
  // buttons: state => state.permission.buttons,
  // permissions: state => state.permission.permissions,
  // userInfo: state => state.permission.userInfo,
  token: state => state.user.token,
  permissions: state => state.permission.permissions, // 接口返回的菜单信息
  menus: state => state.permission.menus, // 左侧导航菜单信息
  buttons: state => state.permission.buttons,
  userInfo: state => {
    // 页面刷新时，vuex会被清空
    if (Object.values(state.user.userInfo).filter(v => !!v).length === 0) {
      return JSON.parse(getLocalStorage('userInfo'))
    }
    return state.user.userInfo
  },
}
