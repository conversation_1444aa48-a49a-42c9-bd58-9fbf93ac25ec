const signal = {
  currentTitle: '信号',
  headers: {
    name: '名称',
    desc: '描述',
    encryption: '加密',
    valueType: '值类型',
    modelType: '模型类型',
    intervalSize: '间隔大小',
    intervalCount: '间隔计数',
    lastUpdate: '最后更新时间',
    dataType: '数据类型',
  },
  ttl: {
    maximum:
      '最大保留时长(该值从资产的数字孪生表中删除的最大保留时长) | 最大保留时长(从资产的数字孪生中删除值之前的最长保留时间)',
    value0: '0 - 保存到下一条消息',
    value1: '-1 - 保持到相同信号的下一次更新',
  },
  id: '信号唯一标识',
  monitoringPeriod: '监测周期',
  period1: '事件直方图跟踪每个间隔的发生次数',
  period2: '选择事件直方图的间隔大小和间隔计数，有效支持检测',
  size1: '选择间隔大小以最好地支持检测所需的粒度。',
  size2: '选择小于检测所需时间范围的间隔。',
  count1: '选择可用于检测的历史间隔计数',
  count2: '在告警事件资产时间轴中显示',
  category: '分类',
  trueEscape: 'True转义值:',
  falseEscape: 'False转义值:',
  setOptionalValue: '设置可选值',
  optionalValue: '可选值',
  selectSub: '选择子信号',
  noData: '无匹配数据',
  unit: '信号单位',
  max: '最大值',
  min: '最小值',
  customize: '自定义',
  selectUnit: '选择单位',
  valueAlready: '该值已存在',
  subHint: '至少选择一个子信号',
  new: '新增@:signal.currentTitle',
  edit: '修改@:signal.currentTitle',
  swal: {
    title: '删除信号',
    hint: '确认删除信号{name}？',
  },
  enum: '枚举值',
  enumMap: '枚举值的映射关系',
  ttlTip: 'TTL不能超过15天，请重新选择时间期限！',
}

export default signal
