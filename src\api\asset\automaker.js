import { request, vsocPath } from '../../util/request'

// 车企分页列表查询
export const getAutomakerList = function (data) {
  return request({
    url: `${vsocPath}/automaker/automakers`,
    method: 'post',
    loading: true,
    data: {
      automakerName: data.automaker_name || '',
      isSupervise: data.is_supervise || '',
    },
  })
}

// 查询车企列表（不分页）
export const getAutomakerListAll = function (data) {
  return request({
    url: `${vsocPath}/automaker/automakerList`,
    method: 'post',
    loading: true,
    data: {
      automakerName: data.automaker_name || '',
      isSupervise: data.is_supervise || '',
    },
  })
}

// 新增车企
export const addAutomaker = function (data) {
  return request({
    url: `${vsocPath}/automaker/addAutomaker`,
    method: 'post',
    data: {
      automakerName: data.automaker_name,
      automakerCode: data.automaker_code,
      description: data.description,
      pictureCode: data.picture_code,
      isSupervise: data.is_supervise,
      headquartersLocation: data.headquarters_location,
      address: data.address,
      brand: data.brand,
      fileIds: data.fileIds || [],
    },
  })
}

// 更新车企
export const editAutomaker = function (data) {
  return request({
    url: `${vsocPath}/automaker/updateAutomaker`,
    method: 'post',
    data: {
      id: data.id,
      automakerName: data.automaker_name,
      automakerCode: data.automaker_code,
      description: data.description,
      pictureCode: data.picture_code,
      isSupervise: data.is_supervise,
      headquartersLocation: data.headquarters_location,
      address: data.address,
      brand: data.brand,
      fileIds: data.fileIds || [],
    },
  })
}

// 获取车企详情
export const getAutomakerDetail = function (data) {
  return request({
    url: `${vsocPath}/automaker/automakerDetails`,
    method: 'post',
    data: {
      id: data.id || '',
      automakerCode: data.automakerCode || '',
    },
  })
}

// 删除车企
export const deleteAutomaker = function (data) {
  return request({
    url: `${vsocPath}/automaker/deleteAutomaker`,
    method: 'post',
    data: {
      id: data.id,
    },
  })
}

// 通用文件上传接口（用于车企LOGO和预览图）
export const uploadFile = function (file, moduleName = 'automaker') {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('moduleName', moduleName)

  return request({
    url: `${vsocPath}/file/upload`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

// 批量上传文件接口（用于车企预览图）
export const uploadFiles = function (files, folder = 'automaker') {
  const formData = new FormData()
  files.forEach((file, index) => {
    formData.append('files', file)
  })
  formData.append('folder', folder)

  return request({
    url: `${vsocPath}/file/uploadMultiple`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

// 根据文件路径获取文件访问URL
export const getFileUrl = function (filePath) {
  return request({
    url: `${vsocPath}/file/getUrl`,
    method: 'post',
    data: {
      filePath: filePath,
    },
  })
}

// 删除文件
export const deleteFile = function (filePath) {
  return request({
    url: `${vsocPath}/file/delete`,
    method: 'post',
    data: {
      filePath: filePath,
    },
  })
}
