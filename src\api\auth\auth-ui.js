import { deleteRequest, getRequest, postRequest, putRequest, vsocPath } from '../../util/request'

const MODULE_PATH = vsocPath

export function getUiLike(authUi, page, size) {
  const param = { page, size }
  Object.assign(param, authUi)

  return getRequest(`${MODULE_PATH}/auth-ui/like`, param)
}

export function addUi(authUi) {
  return postRequest(`${MODULE_PATH}/auth-ui`, authUi)
}

export function updateUi(id, authUi) {
  return putRequest(`${MODULE_PATH}/auth-ui`, authUi)
}

export function deleteUis(ids) {
  return deleteRequest(`${MODULE_PATH}/auth-ui`, ids)
}

export function getSimpleUiTree() {
  return getRequest(`${MODULE_PATH}/auth-ui/tree/simple`)
}

export function getUiTree() {
  return getRequest(`${MODULE_PATH}/auth-ui/tree`)
}
