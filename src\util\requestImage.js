import { getLocalStorage } from '@/util/localStorage'
export function requestImg(src, callback) {
  return new Promise((resolve, reject) => {
    Object.defineProperty(Image.prototype, 'authSrc', {
      writable: true,
      enumerable: true,
      configurable: true,
    })
    let request = new XMLHttpRequest()
    request.responseType = 'blob'
    request.open('get', src, true)
    request.setRequestHeader('Authorization', getLocalStorage('token') || '')
    request.setRequestHeader('Token', getLocalStorage('vsoc_appToken') || '')
    request.setRequestHeader('Appcode', 'vsoc')
    request.onreadystatechange = e => {
      if (request.readyState == XMLHttpRequest.DONE && request.status == 200) {
        // callback && callback(URL.createObjectURL(request.response))
        resolve(URL.createObjectURL(request.response))
      }
    }
    request.send(null)
  })
}
function delImageSrc(htmlString) {
  let src = ''
  let imgTagAuthSrc = /<img [^>]*authsrc=['"]([^'"]+)[^>]*>/gi
  let imgTagSrc = /<img [^>]*src=['"]([^'"]+)[^>]*>/gi
  if (htmlString.split(imgTagAuthSrc).length === 1) {
    src = htmlString.split(imgTagSrc)[1]
  } else {
    src = htmlString.split(imgTagAuthSrc)[1]
  }
  return src
  // htmlString.matchres.forEach(v => {
  //   if (v.split(imgTagAuthSrc).length === 1) {
  //     src.push(v.split(imgTagSrc)[1])
  //   } else {
  //     src.push(v.split(imgTagAuthSrc)[1])
  //   }
  // })
}

// async function extractImageSrc(htmlString, content) {
//   let arr = delImageSrc(htmlString)
//   let list = arr.map(async item => {
//     return {
//       item,
//       src: await requestImg(item),
//     }
//   })
//   Promise.all(list)
//     .then(res => {
//       matchres.forEach((v,index)=>{
//         content=content.replace(v, res[index])
//       })
//       callback&&
//     })
//     .catch(error => {
//       console.log(error)
//     })
// }

//删除 src
export function dealImg(content) {
  return new Promise(async (resolve, reject) => {
    let imgReg = RegExp(/<img [^>]*src=['"]([^'"]+)[^>]*>/gi) //定义正则，筛选出img元素
    let matchres = content.match(imgReg)
    if (matchres && matchres.length) {
      let matchresImg = matchres.map((item, index) => {
        return {
          src: delImageSrc(item),
          authsrc: item,
        }
      })
      let list = []
      matchresImg.forEach(item => {
        list.push(requestImg(item.src))
      })
      Promise.all(list)
        .then(res => {
          matchresImg.forEach((v, index) => {
            content = content.replace(
              v.authsrc,
              `<img src=${res[index]} authsrc=${v.src} />`,
            )
          })
        })
        .catch(error => {
          console.log(error)
        })
        .finally(() => {
          resolve(content)
        })
    } else {
      resolve(content)
    }
  })
}
