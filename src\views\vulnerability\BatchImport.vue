<template>
  <vsoc-dialog
    v-model="isShow"
    title="导入"
    dense
    width="600"
    :hideFooter="true"
    @click:confirm="onConfirm"
  >
    <div
      slot="title"
      class="w-100 d-flex justify-lg-space-between align-center"
    >
      <span class="font-weight-semibold text-h5"> 导入 </span>
      <v-btn icon @click="isShow = false">
        <vsoc-icon icon="mdi-close"></vsoc-icon>
      </v-btn>
    </div>
    <v-form ref="form" class="py-4">
      <v-row class="px-6 pb-4">
        <!-- :href="`${vsocPath}/vehicleAsset/downloadTemplate?token=${token}`" -->
        <!-- @click="onDownload" -->
        <a class="text-decoration-underline" @click="onDownload">
          {{ $t('vulnerability.template.hint') }}
        </a>
      </v-row>
      <v-row class="px-6">
        <el-upload
          ref="upload"
          :headers="headers"
          :action="vsocPath + '/vulnerability/importVehicleAsset'"
          :on-change="handleChange"
          :on-success="handleSuccess"
          :on-error="handleError"
          :on-exceed="handleExceed"
          :before-upload="handleBefore"
          :file-list="fileList"
          :show-file-list="false"
          accept=".xls,.xlsx"
        >
          <!-- :auto-upload="false" -->
          <v-btn
            color="primary--text bg-btn"
            elevation="0"
            :loading="isUploading"
          >
            <v-icon dense style="transform: rotate(45deg)" class="text-ml">
              mdi-paperclip
            </v-icon>
            {{ $t('action.upload') }}
          </v-btn>
        </el-upload>
        <div class="w-100">
          <v-chip
            class="mt-3 text-ml py-1 primary-hover-chip"
            label
            v-for="(item, index) in fileList"
            :key="index"
            @click.stop="onDownloadFile(item.raw)"
          >
            <vsoc-icon type="fill" icon="icon-xiangqing"></vsoc-icon>
            <span class="ml-2 mr-3">{{ item.name }}</span>
            <span class="action-btn">{{ item.size | formatFileSize }}</span>
            <vsoc-icon class="ml-3" type="fill" icon="icon-xiazai"></vsoc-icon>
          </v-chip>
        </div>
      </v-row>
    </v-form>
  </vsoc-dialog>
</template>

<script>
import { downloadVulnerabilityTemplate } from '@/api/vulnerability'
import VsocDialog from '@/components/VsocDialog.vue'

import { vsocPath } from '@/util/request'
import { getToken } from '@/util/token'
export default {
  name: 'BatchImport',
  props: {
    selectedList: Array,
  },
  components: {
    VsocDialog,
  },
  data() {
    return {
      base64File: '',
      isUploading: false,
      isShow: false,
      token: getToken(),

      vsocPath,
      importFile: null,
      fileList: [],
    }
  },
  computed: {
    headers() {
      return this.$store.getters['global/getFileHeaders']
    },
  },
  watch: {
    isShow(newVal) {
      if (newVal) {
        this.fileList = []
      }
    },
  },

  methods: {
    onDownloadFile(downfile) {
      let objectUrl = undefined
      if (this.base64File) {
        objectUrl =
          'data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,' +
          this.base64File
      } else {
        objectUrl = URL.createObjectURL(downfile)
      }
      const tmpLink = document.createElement('a')

      tmpLink.href = objectUrl
      tmpLink.download = downfile.name
      document.body.appendChild(tmpLink)
      tmpLink.click()

      document.body.removeChild(tmpLink)
      URL.revokeObjectURL(objectUrl)
    },
    handleBefore(file) {
      this.isUploading = true
    },
    handleExceed(files, fileList) {
      console.log('超出文件限制数了')
      // this.fileList = files
    },
    handleError(err, file, fileList) {
      this.$notify.info('error', err)
    },
    handleSuccess(res, file, fileList) {
      const { code, msg, data } = res
      if (code === 200) {
        this.$notify.info('success', msg)
        this.base64File = data
        this.$emit('refresh')
        // this.isShow = false
      } else {
        this.$notify.info('error', msg)
        this.base64File = ''
      }
      this.isUploading = false
    },
    handleChange(file, fileList) {
      const lastItem = fileList[fileList.length - 1]
      this.fileList = [lastItem]
    },
    async onDownload() {
      const file = await downloadVulnerabilityTemplate()
      const xlsx =
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'
      const blob = new Blob([file], { type: xlsx })
      //转换数据类型
      const a = document.createElement('a') // 转换完成，创建一个a标签用于下载

      a.download = `${this.$t('vulnerability.template.text')}.xlsx`
      a.href = URL.createObjectURL(blob)
      document.body.appendChild(a)
      a.click()
      a.remove()
      // 直接打开下载文件的链接
      // window.location.href = res.request.responseURL
    },
    async onConfirm(cb) {
      const res = await this.$refs.upload.submit()
      // try {
      //   this.$notify.info('success', '批量导入软件资产成功！')
      //   this.$emit('refresh')
      //   cb()
      // } catch (err) {
      //   console.log('批量导入软件资产报错', err)
      //   cb(false, true)
      // }
    },
  },
}
</script>
