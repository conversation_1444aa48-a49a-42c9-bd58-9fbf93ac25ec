<template>
  <div class="app-customizer">
    <!-- <v-btn
      icon
      class="app-customizer-toggler rounded-0"
      :class="$vuetify.rtl ? 'rounded-r-lg' : 'rounded-l-lg'"
      color="white"
      large
      @click="isCustomizerOpen = !isCustomizerOpen"
      v-if="
        $store.getters.userInfo && $store.getters.userInfo.roleId === 'admin'
      "
    >
      <v-icon size="25">
        {{ icons.mdiCog }}
      </v-icon>
    </v-btn> -->
    <v-navigation-drawer
      v-model="isCustomizerOpen"
      :right="!$vuetify.rtl"
      temporary
      fixed
      width="400"
      class="app-customizer-drawer"
    >
      <!-- Heading -->
      <div class="app-customizer-header customizer-section py-3">
        <h6 class="font-weight-semibold text-xl text--primary">
          SYSTEM CUSTOMIZER
        </h6>
        <span class="text--secondary">Customize and preview in real time</span>
        <v-btn
          icon
          class="icon-customizer-close"
          @click="isCustomizerOpen = false"
        >
          <v-icon>
            {{ icons.mdiClose }}
          </v-icon>
        </v-btn>
      </div>

      <v-divider></v-divider>

      <perfect-scrollbar
        :options="perfectScrollbarOptions"
        class="ps-customizer"
      >
        <!-- Section: Themeing -->
        <div class="customizer-section">
          <p class="text-xs text--disabled">THEMING</p>
          <!-- Skin -->
          <!-- <span class="text--primary">Skin</span>
          <v-radio-group v-model="appSkinVariant" class="mt-1" hide-details row>
            <v-radio
              v-for="option in skinOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            ></v-radio>
          </v-radio-group> -->
          <!-- Mode -->
          <!-- <span class="mt-6 d-inline-block mb-2 text--primary">Mode</span> -->
          <!-- <span class="text--primary">Theme</span> -->
          <v-radio-group
            class="mt-1"
            v-model="appMode"
            hide-details
            row
            @change="onChangeTheme"
          >
            <v-radio
              v-for="theme in themeOption"
              :key="theme.value"
              :label="theme.label"
              :value="theme.value"
            ></v-radio>
          </v-radio-group>
          <!-- <div class="d-flex align-center">
            <span class="text--secondary text-sm">Light</span>
            <v-switch
              v-model="isDark"
              hide-details
              class="mt-0 mx-2"
            ></v-switch>
            <span class="text--secondary text-sm">Dark</span>
          </div> -->
          <template>
            <div class="text--primary pt-4">Primary Change</div>
            <v-radio-group
              :disabled="$store.state.appConfig.globalThemeMode !== 'semi-dark'"
              class="mt-1"
              v-model="appPrimary"
              hide-details
              row
              @change="toggleLotus"
            >
              <v-radio
                v-for="primary in primaryChangeList"
                :key="primary.value"
                :label="primary.label"
                :value="primary.value"
              ></v-radio>
            </v-radio-group>
          </template>
        </div>

        <v-divider></v-divider>

        <!-- Section: Primary Color -->
        <!-- <div class="customizer-section">
          <p class="text-xs text--disabled">PRIMARY COLOR</p>
          <div class="d-flex gap-x-4 mt-2">
            <v-chip-group mandatory column @change="onChangePrimary">
              <v-chip
                filter
                ripple
                small
                v-for="themePrimary in themePrimaryColor"
                :key="themePrimary.key"
                :color="themePrimary.color"
                class="rounded"
              >
                {{ themePrimary.key }}
              </v-chip>
              <v-menu offset-y>
                <template v-slot:activator="{ on, attrs }">
                  <v-chip
                    filter
                    ripple
                    small
                    v-bind="attrs"
                    v-on="on"
                    class="rounded"
                    :color="colorPicker ? colorPicker.hex : '#ccc'"
                  >
                    Palette
                  </v-chip>
                </template>
                <v-color-picker
                  v-model="colorPicker"
                  swatches-max-height="200"
                  @update:color="obj => onChangePrimary(-1, obj)"
                ></v-color-picker>
              </v-menu>
            </v-chip-group>
          </div>
        </div>

        <v-divider></v-divider> -->

        <!-- Section: layout -->
        <div class="customizer-section">
          <p class="text-xs text--disabled">LAYOUT</p>

          <!-- Content Width -->
          <span class="text--primary">Content Width</span>
          <v-radio-group
            v-model="appContentWidth"
            class="mt-1"
            hide-details
            row
          >
            <v-radio
              v-for="option in appContentWidthOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            ></v-radio>
          </v-radio-group>

          <!-- AppBar Type -->
          <!-- <span class="mt-6 d-inline-block text--primary">AppBar Type</span>
          <v-radio-group v-model="appBarType" class="mt-1" hide-details row>
            <v-radio
              v-for="option in appBarTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            ></v-radio>
          </v-radio-group> -->

          <!-- Footer Type -->
          <!-- <span class="mt-6 d-inline-block text--primary">Footer Type</span>
          <v-radio-group v-model="footerType" class="mt-1" hide-details row>
            <v-radio
              v-for="option in footerTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            ></v-radio>
          </v-radio-group> -->

          <!-- Blur -->
          <!-- <div class="d-flex align-center justify-space-between mt-6">
            <span class="text--primary">AppBar Blur</span>
            <v-switch
              v-model="appBarIsBlurred"
              hide-details
              class="mt-0 ms-2"
            ></v-switch>
          </div> -->
        </div>

        <v-divider></v-divider>

        <!-- Section: Menu -->
        <div class="customizer-section">
          <p class="text-xs text--disabled">MENU</p>
          <template v-if="$vuetify.breakpoint.lgAndUp">
            <!-- Menu Layout -->
            <!-- <span class="text--primary">Menu Layout</span>
            <v-radio-group
              v-model="appContentLayoutNav"
              class="mt-1"
              hide-details
              row
            >
              <v-radio
                v-for="option in appContentLayoutNavOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              ></v-radio>
            </v-radio-group> -->
          </template>

          <!-- Menu Collapsed -->
          <div
            v-if="appContentLayoutNav === 'vertical'"
            class="d-flex align-center justify-space-between mt-6"
          >
            <span class="text--primary">Menu Collapsed</span>
            <v-switch
              v-model="menuIsVerticalNavMini"
              hide-details
              class="mt-0 ms-2"
            ></v-switch>
          </div>

          <!-- Menu Hidden -->
          <!-- <div class="d-flex align-center justify-space-between mt-6">
            <span class="text--primary">Menu Hidden</span>
            <v-switch
              v-model="menuIsMenuHidden"
              hide-details
              class="mt-0 ms-2"
            ></v-switch>
          </div> -->
        </div>
        <v-divider></v-divider>
        <!-- Section: Misc -->
        <!--        <div class="customizer-section">-->
        <!--          <p class="text-xs text&#45;&#45;disabled">MISC</p>-->
        <!--          &lt;!&ndash; RTL &ndash;&gt;-->
        <!--          <div class="d-flex align-center justify-space-between mt-6">-->
        <!--            <span class="text&#45;&#45;primary">RTL</span>-->
        <!--            <v-switch v-model="isRtl" hide-details class="mt-0 ms-2"></v-switch>-->
        <!--          </div>-->
        <!--          &lt;!&ndash; Router Transition &ndash;&gt;-->
        <!--          <v-row class="mt-6 align-center">-->
        <!--            <v-col><span class="text&#45;&#45;primary">Router Transition</span></v-col>-->
        <!--            <v-col col="4">-->
        <!--              <v-select-->
        <!--                v-model="appRouteTransition"-->
        <!--                :items="routerTransitionOptions"-->
        <!--                outlined-->
        <!--                hide-details-->
        <!--                dense-->
        <!--                class="select-router-transition"-->
        <!--              ></v-select>-->
        <!--            </v-col>-->
        <!--          </v-row>-->
        <!--        </div>-->
        <!--        <v-divider></v-divider>-->
        <!--        <div class="customizer-section">-->
        <!--          <p class="text-xs text&#45;&#45;disabled">MAP</p>-->
        <!--          <v-radio-group class="mt-1" v-model="appMap" hide-details row>-->
        <!--            <v-radio-->
        <!--              v-for="map in mapOption"-->
        <!--              :key="map.value"-->
        <!--              :label="map.label"-->
        <!--              :value="map.value"-->
        <!--              class="text-capitalize"-->
        <!--            ></v-radio>-->
        <!--          </v-radio-group>-->
        <!--        </div>-->
      </perfect-scrollbar>
    </v-navigation-drawer>
  </div>
</template>

<script>
import { toggleLotus } from '@/util/utils.js'
import useAppConfig from '@core/@app-config/useAppConfig'
import { computed, getCurrentInstance, ref } from '@vue/composition-api'
import Vue from 'vue'

import { mdiClose, mdiCog } from '@mdi/js'

import { PerfectScrollbar } from 'vue2-perfect-scrollbar'
import themeVariants from './themeVariants'

// 3rd Party
import themeMixin from '@/@core/layouts/components/theme-mixin'
import store from '@/store'
export default {
  components: {
    PerfectScrollbar,
  },
  mixins: [themeMixin],
  setup() {
    const vm = getCurrentInstance().proxy
    const isCustomizerOpen = computed({
      get() {
        return store.state.global.isCustomizerOpen
      },
      set(newVal) {
        store.commit('global/setIsCustomizerOpen', newVal)
      },
    })
    // eslint-disable-next-line object-curly-newline
    const {
      appPrimary,
      appMap,
      appContentLayoutNav,
      appSkinVariant,
      appContentWidth,
      appRouteTransition,
      menuIsVerticalNavMini,
      menuIsMenuHidden,
      appBarType,
      appBarIsBlurred,
      footerType,
      isDark,
      isRtl,
      themes,
    } = useAppConfig()

    const selectedTheme = ref('vuexy')

    const routerTransitionOptions = [
      { text: 'Scroll X', value: 'scroll-x-transition' },
      { text: 'Scroll X Reverse', value: 'scroll-x-reverse-transition' },
      { text: 'Scroll Y', value: 'scroll-y-transition' },
      { text: 'Scroll Y Reverse', value: 'scroll-y-reverse-transition' },
      { text: 'Slide X', value: 'slide-x-transition' },
      { text: 'Slide X Reverse', value: 'slide-x-reverse-transition' },
      { text: 'Slide Y', value: 'slide-y-transition' },
      { text: 'Slide Y Reverse', value: 'slide-y-reverse-transition' },
      { text: 'Fade', value: 'fade-transition' },
    ]

    const skinOptions = computed(() => {
      const options = [
        { label: 'Default', value: 'default' },
        { label: 'Bordered', value: 'bordered' },
        { label: 'Semi Dark', value: 'semi-dark' },
      ]
      if (appContentLayoutNav.value === 'horizontal') options.splice(-1, 1)

      return options
    })

    // const themeOption = computed(() => {
    //   return [
    //     {
    //       label: 'Light',
    //       value: false,
    //     },
    //     {
    //       label: 'Dark',
    //       value: true,
    //     },
    //     {
    //       label: 'Semi Dark',
    //       value: 'semi-dark',
    //     },
    //   ]
    // })
    // const appMode = computed({
    //   get() {
    //     return vm.$route.meta.isDark
    //       ? vm.$route.meta.isDark
    //       : appSkinVariant.value === 'semi-dark'
    //       ? appSkinVariant.value
    //       : isDark.value
    //   },
    //   set(newVal) {
    //     return newVal
    //   },
    // })
    // const onChangeTheme = value => {
    //   if (typeof value === 'boolean') {
    //     isDark.value = value
    //     appSkinVariant.value = 'default'
    //   } else {
    //     appSkinVariant.value = value
    //     isDark.value = vm.$route.meta.isDark
    //   }
    // }

    const appContentWidthOptions = [
      { label: 'Boxed', value: 'boxed' },
      { label: 'Full Width', value: 'full' },
    ]

    const appBarTypeOptions = computed(() => {
      const types = [
        { label: 'Fixed', value: 'fixed' },
        { label: 'Static', value: 'static' },
        { label: 'Hidden', value: 'hidden' },
      ]
      if (appContentLayoutNav.value === 'horizontal') types.splice(-1, 1)

      return types
    })

    const footerTypeOptions = [
      { label: 'Fixed', value: 'fixed' },
      { label: 'Static', value: 'static' },
      { label: 'Hidden', value: 'hidden' },
    ]

    const appContentLayoutNavOptions = [
      { label: 'Vertical', value: 'vertical' },
      { label: 'Horizontal', value: 'horizontal' },
    ]

    const perfectScrollbarOptions = {
      maxScrollbarLength: 60,
      wheelPropagation: false,
    }

    const mapOption = ref([
      {
        label: 'baidu',
        value: 'baidu',
      },
      {
        label: 'mapbox',
        value: 'mapbox',
      },
    ])

    const themePrimaryColor = [
      {
        // key: '浅蓝',
        key: 'Light',
        color: '#214EA8',
      },
      {
        // key: '暗黑',
        key: 'Dark',
        color: '#0F7EFF',
      },
      {
        // key: '薄暮',
        key: 'Dust',
        color: '#F5222D',
      },
      {
        // key: '火山',
        key: 'Volcano',
        color: '#FA541C',
      },
      {
        // key: '日暮',
        key: 'Sunset Orange',
        color: '#FAAD14',
      },
      {
        // key: '明青',
        key: 'Cyan',
        color: '#13C2C2',
      },
      {
        // key: '极光绿',
        key: 'Polar Green',
        color: '#52C41A',
      },
      {
        // key: '拂晓蓝',
        key: 'Daybreak Blue',
        color: '#1890FF',
      },
      {
        // key: '极客蓝',
        key: 'Geek Blue',
        color: '#2F54EB',
      },
      {
        // key: '酱紫',
        key: 'Geek Purple',
        color: '#722ED1',
      },
    ]

    const colorPicker = ref(undefined)
    const onChangePrimary = (index, obj = {}) => {
      const color = index !== -1 ? themePrimaryColor[index].color : obj.hex
      if (vm.$vuetify.theme.isDark) {
        vm.$vuetify.theme.themes.dark.primary = color
      } else {
        vm.$vuetify.theme.themes.light.primary = color
      }
      Vue.prototype.$primaryColor = color
    }

    const primaryChangeList = ref([
      {
        label: 'Default',
        value: 'default',
      },
      {
        label: 'Dark_Yellow',
        value: 'lotus',
      },
    ])

    return {
      // primary color
      themePrimaryColor,
      onChangePrimary,
      colorPicker,
      toggleLotus,

      isCustomizerOpen,
      selectedTheme,
      appRouteTransition,
      themeVariants,
      primaryChangeList,
      appPrimary,

      // App Content Layout Nav
      appContentLayoutNavOptions,
      appContentLayoutNav,

      // Skin
      appSkinVariant,
      skinOptions,

      // Theme
      // themeOption,
      // onChangeTheme,
      // appMode,

      // App Content Width
      appMap,
      mapOption,
      appContentWidth,
      appContentWidthOptions,

      menuIsMenuHidden,
      menuIsVerticalNavMini,

      // AppBar Type
      appBarType,
      appBarIsBlurred,
      appBarTypeOptions,

      // Footer Type
      footerType,
      footerTypeOptions,

      isDark,
      isRtl,
      themes,
      routerTransitionOptions,

      // Perfect scrollbar options
      perfectScrollbarOptions,

      // Icons
      icons: {
        mdiCog,
        mdiClose,
      },
    }
  },
}
</script>

<style lang="scss">
@import '~vuetify/src/styles/styles.sass';

.chip--outlined {
  outline: 1px solid #f00;
  outline-offset: 3px;
  border-radius: 1px;
}

.ps-customizer {
  height: calc(100% - 81px) !important;
}

.app-customizer-toggler {
  position: fixed;
  top: 50%;
  transform: translateX(-50%);
  background: var(--v-primary-base);
  @include ltr() {
    right: -22px;
  }
  @include rtl() {
    left: 20px;
  }
}

@include theme(app-customizer-drawer) using ($material) {
  background-color: map-deep-get($material, 'cards');
}

.app-customizer {
  z-index: 7;

  .v-label {
    font-size: 0.875rem;
  }

  .app-customizer-header {
    position: relative;
    .icon-customizer-close {
      position: absolute;
      @include ltr() {
        right: 20px;
      }
      @include rtl() {
        left: 20px;
      }
      top: 50%;
      transform: translateY(-50%);
    }
  }

  .customizer-section {
    padding: 24px;
  }

  // Fixes Overlay is shown below SystemBar
  @at-root {
    .v-overlay {
      & + .v-application--wrap > .v-system-bar {
        z-index: 6 !important;
      }
    }
  }
}
</style>
