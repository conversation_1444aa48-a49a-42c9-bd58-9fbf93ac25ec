<template>
  <!-- 定制化图形2 -->
  <div class="box">
    <div class="box-header">{{ title }}</div>
    <!-- style="padding: 0 6%" -->
    <div
      :style="{ marginTop: isFull ? '0' : '4%' }"
      class="box-chart d-flex justify-space-between align-center w-100"
    >
      <div v-for="(item, index) in list" :key="index">
        <div class="font-weight-medium fs-14-1">
          {{ $t('global.' + item.name) }}
        </div>
        <div
          v-show-tips="item.value"
          class="card-item-title-text"
          :style="{
            color:
              Number(item.type) === 1
                ? '#FF385D'
                : Number(item.type) === 2
                ? '#F0DA4C'
                : '#FF9229',
          }"
        >
          {{ numberToFormat(item.value) }}
          <!-- <count-to
            :ref="'count' + index"
            class="c-center2-num-1 font-weight-semibold"
            :style="{
              color:
                Number(item.type) === 1
                  ? '#FF385D'
                  : Number(item.type) === 2
                  ? '#F0DA4C'
                  : '#FF9229',
            }"
            :startVal="0"
            :endVal="
              Number(item.value) ? Number(numberToFormat(item.value)) : 0
            "
            :duration="2500"
          ></count-to> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { numberToFormat } from '@/util/filters'
import countTo from 'vue-count-to'

export default {
  name: 'CardItem2',
  props: {
    title: {
      type: String,
      default: '',
    },
    list: {
      type: Array,
      default: () => {
        return []
      },
    },
    isFull: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    countTo,
  },
  computed: {},
  watch: {
    list: {
      handler(val) {
        if (val.length === 0) return
        // val.forEach((item, index) => {
        //   this.$nextTick(() => {
        //     this.$refs['count' + index][0] &&
        //       this.$refs['count' + index][0].start()
        //   })
        // })
      },
      immediate: true,
    },
  },
  data() {
    return {
      numberToFormat,
    }
  },
}
</script>
