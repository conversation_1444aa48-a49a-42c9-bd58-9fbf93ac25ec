<template>
  <v-card class="h-full">
    <v-card-title
      class="w-100 float-left d-flex align-center justify-space-between"
    >
      <v-row>
        <v-col>
          <span class="text-xxl font-weight-semibold text--primary">{{
            $t('posture.dataCollection')
          }}</span>

          <span class="text-base"
            ><span class="mx-2">/</span>{{ collectionData.total
            }}<span class="ml-4">
              {{ $t('global.updateDiff', [collectionData.updateDate]) }}
            </span></span
          >
        </v-col>
        <v-col class="pa-0 d-flex align-center" cols="3">
          <v-autocomplete
            v-model="collectionList"
            style="z-index: 7"
            :items="collectionData.yData"
            item-value="type"
            item-text="name"
            multiple
            class="pt-0 px-3"
            @change="changeAutocomplete"
          >
            <template v-slot:selection="{ index }">
              <span v-if="index === 0" class="text-caption">
                {{ $t('global.selected') }}{{ collectionList.length }}
              </span>
            </template>
          </v-autocomplete>
        </v-col>
      </v-row>
    </v-card-title>
    <v-card-text class="h-full">
      <vsoc-chart
        class="theme--dark text--secondary"
        ref="areaLine"
        echart-id="area-line"
        :option="option"
        :is-y-dashed="true"
      ></vsoc-chart>
    </v-card-text>
  </v-card>
</template>

<script>
import VsocChart from '@/components/VsocChart'
import { format, startOfDay, subHours } from 'date-fns'
import {
  grid,
  legend,
  linearGradient,
  tooltip,
} from '../../../assets/echarts-theme/constant'
let xData = []
for (let i = 24; i >= 0; i--) {
  i = i - 3
  let time = format(subHours(startOfDay(new Date()), i), 'HH:mm')
  xData.push(time)
}
const yData = [
  {
    name: '网联车辆消息 1.87T (39%)',
    data: [100, 120, 90, 390, 130, 160, 280, 240, 220, 180, 270, 280, 375],
  },
  {
    name: '网联服务指令 1.58T (32%)',
    data: [60, 80, 360, 110, 80, 100, 90, 180, 160, 140, 200, 220, 275],
  },
  {
    name: '移动APP 678G (21%)',
    data: [20, 40, 30, 70, 380, 60, 50, 140, 120, 100, 140, 180, 220],
  },
]
export default {
  props: {
    collectionData: {
      type: Object,
      default() {
        return {
          xData: xData,
          yData: yData,
          total: 0,
          updateDate: '',
        }
      },
    },
  },
  components: {
    VsocChart,
  },
  data() {
    return {
      collectionList: [],
      option: {},
    }
  },
  computed: {},
  mounted() {
    // 数据处理默认类型
    this.collectionData.yData.map((item, index) => {
      this.collectionList.push(item.type)
    })
    this.gradientChart(this.collectionData.yData)
  },
  methods: {
    // 构建图表数据对象
    gradientChart(yDataList) {
      let colorList = ['#298CFF', '#533DF1', '#44E2FE', '#0eaa9f']
      // const collectTypeEnum = this.$store.state.enums.enums.CollectionType
      // 如果数据超过4种就随机从固定的4中颜色中抽取颜色
      if (yDataList.length > colorList.length) {
        let count = yDataList.length - colorList.length
        let arr = []
        for (let i = 0; i < count; i++) {
          let i = Math.floor(Math.random() * arr.length)
          arr.push(colorList[i] || '#298CFF')
        }
        colorList = colorList.concat(arr)
      }
      this.option = {
        color: colorList,
        title: {
          show: false,
          text: `{text|${this.$t(
            'posture.dataCollection',
          )}}\t {subtext|/ \t${this.toNumber(
            this.collectionData.total,
          )}（${this.$t('enums.datePresets.last24')}）}`,
        },
        series: yDataList.map((item, index) => {
          let name = item.name
          return linearGradient.seriesCommonFn(
            name,
            item.data,
            colorList[index],
          )
        }),
        tooltip,
        legend: {
          ...legend,
          top: '6%',
          show: false,
        },
        grid: {
          ...grid,
          top: '25%',
          // top: 60,
        },
        xAxis: linearGradient.xAxisFn(this.collectionData.xData),
        yAxis: [
          {
            type: 'value',
          },
        ],
      }
    },
    changeAutocomplete(types) {
      let arr = this.collectionData.yData.filter(obj =>
        types.includes(obj.type),
      )
      this.gradientChart(arr)
    },
    toNumber(num) {
      // 小数
      // return num.toLocaleString('en-US')
      // 整数
      return String(num).replace(/(\d)(?=(\d{3})+$)/g, '$1,')
    },
  },
}
</script>
<style scoped lang="scss"></style>
