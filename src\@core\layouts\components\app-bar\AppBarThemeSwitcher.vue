<template>
  <v-fade-transition mode="out-in">
    <!-- <v-icon
      :key="isDark"
      @click="isDark = !isDark"
    >
      {{ isDark ? icons.mdiWeatherSunny : icons.mdiWeatherNight }}
    </v-icon> -->
    <!-- <v-btn small icon @click="isDark = !isDark" color="white">
      <vsoc-icon
        type="fill"
        size="x-large"
        :icon="!isDark ? 'icon-xitongzhutiseW' : 'icon-xitongzhutiseB'"
      ></vsoc-icon>
    </v-btn> -->
    <v-menu offset-y>
      <template v-slot:activator="{ on, attrs }">
        <v-btn v-bind="attrs" v-on="on" small icon color="white">
          <vsoc-icon
            type="fill"
            size="x-large"
            :icon="!isDark ? 'icon-xitongzhutiseW' : 'icon-xitongzhutiseB'"
          ></vsoc-icon>
        </v-btn>
      </template>
      <v-card>
        <v-list-item-group
          v-model="appMode"
          mandatory
          color="primary"
          @change="onChangeTheme"
          class="overflow-hidden"
        >
          <v-list-item
            v-for="(item, i) in themeOption"
            :key="i"
            :value="item.value"
          >
            <!-- <v-list-item-icon>
              <vsoc-icon
                type="fill"
                size="x-large"
                :icon="item.icon"
              ></vsoc-icon>
            </v-list-item-icon> -->
            <v-list-item-content>
              <v-list-item-title v-text="item.label"></v-list-item-title>
            </v-list-item-content>
          </v-list-item>
        </v-list-item-group>
      </v-card>
    </v-menu>
  </v-fade-transition>
</template>

<script>
import useAppConfig from '@core/@app-config/useAppConfig'
import { mdiWeatherNight, mdiWeatherSunny } from '@mdi/js'
import themeMixin from '../theme-mixin'
export default {
  mixins: [themeMixin],
  setup() {
    const { isDark, appSkinVariant } = useAppConfig()

    return {
      isDark,

      // Icons
      icons: {
        mdiWeatherNight,
        mdiWeatherSunny,
      },
    }
  },
}
</script>

<style></style>
