<template>
  <vsoc-drawer
    v-model="isDrawerShow"
    :title="
      mode === 'new'
        ? $t('global.drawer.addTitle', { cur: $t('dict.currentTitle1') })
        : $t('global.drawer.editTitle', {
            cur: $t('dict.currentTitle1'),
          })
    "
    @click:confirm="onSave"
  >
    <v-form ref="form" v-model="valid" lazy-validation>
      <div class="mt-2">
        <v-row class="pl-6 pr-6">
          <v-select
            v-model="advanceQuery.dictType"
            color="primary"
            :items="allTypeList"
            item-text="text"
            item-value="value"
            :label="$t('dict.headers1.dictType')"
            disabled
          >
          </v-select>
        </v-row>
        <v-row class="pl-6 pr-6">
          <v-text-field
            v-model="advanceQuery.dictName"
            :label="$t('dict.headers1.dictName')"
            required
            color="primary"
            class="is-required"
            :rules="[
              v =>
                !!v ||
                $t('validation.required', [$t('dict.headers1.dictName')]),
            ]"
          >
          </v-text-field>
        </v-row>
        <v-row class="pl-6 pr-6">
          <v-text-field
            v-model="advanceQuery.dictEnName"
            :label="$t('dict.headers1.dictEnName')"
            required
            color="primary"
            class="is-required"
            :rules="[
              v =>
                !!v ||
                $t('validation.required', [$t('dict.headers1.dictEnName')]),
            ]"
          >
          </v-text-field>
        </v-row>
        <v-row class="pl-6 pr-6">
          <v-text-field
            v-model="advanceQuery.dictId"
            :label="$t('dict.headers1.dictId')"
            required
            color="primary"
            class="is-required"
            :rules="[
              v =>
                !!v || $t('validation.required', [$t('dict.headers1.dictId')]),
            ]"
          >
          </v-text-field>
        </v-row>

        <v-row class="pl-6 pr-6">
          <v-text-field
            v-model.number="advanceQuery.sort"
            :label="$t('dict.headers1.sort')"
            required
            color="primary"
            type="number"
          >
          </v-text-field>
        </v-row>

        <v-row class="pl-6 pr-6">
          <v-row class="pl-6 pr-6">
            <span class="text-base font-weight-medium color-base">
              {{ $t('dict.headers.status') }}
            </span>
          </v-row>
          <v-btn-toggle
            class="w-100 mt-2"
            v-model="advanceQuery.status"
            color="primary"
          >
            <v-btn
              v-for="item in dictStatusList"
              :key="item.value"
              :value="item.value"
              elevation="0"
              class="w-50"
            >
              <v-badge
                dot
                inline
                offset-x="10"
                :offset-y="-18"
                :color="item.color"
                class="mr-2"
              ></v-badge>
              <span>{{ item.text }}</span>
            </v-btn>
          </v-btn-toggle>
        </v-row>
        <div class="mt-5">
          <v-row class="pl-6 pr-6">
            <v-row class="pl-6 pr-6">
              <span class="text-base font-weight-medium color-base">
                {{ $t('dict.headers.isDefault') }}
              </span>
            </v-row>
            <v-btn-toggle
              class="w-100 mt-2"
              v-model="advanceQuery.isDefault"
              color="primary"
            >
              <v-btn
                v-for="item in isDefaultList"
                :key="item.value"
                :value="item.value"
                elevation="0"
                class="w-50"
              >
                <v-badge
                  dot
                  inline
                  offset-x="10"
                  :offset-y="-18"
                  :color="item.color"
                  class="mr-2"
                ></v-badge>
                <span>{{ item.text }}</span>
              </v-btn>
            </v-btn-toggle>
          </v-row>
        </div>

        <v-row class="pl-6 pr-6">
          <v-textarea
            class="mt-5"
            v-model="advanceQuery.remark"
            :label="$t('dict.headers.remark')"
            :rows="$AREA_ROWS"
            color="primary"
          ></v-textarea>
        </v-row>
      </div>
    </v-form>
  </vsoc-drawer>
</template>

<script>
import { addDictData, updateDictData } from '@/api/system/dict'
import VsocDrawer from '@/components/VsocDrawer.vue'
import { activeColor, inactiveColor } from '@/plugins/systemColor'

export default {
  name: 'dictDataEdit',
  props: {
    mode: {
      type: String,
      default: () => 'new',
    },
    allTypeList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      valid: true,
      isDrawerShow: false,
      dictStatusList: [
        {
          text: '正常',
          value: '0',
          color: activeColor,
        },
        {
          text: '停用',
          value: '1',
          color: inactiveColor,
        },
      ],
      isDefaultList: [
        {
          text: '是',
          value: '0',
          color: activeColor,
        },
        {
          text: '否',
          value: '1',
          color: inactiveColor,
        },
      ],
      advanceQuery: {
        dictType: '', //字典类型id
        dictName: '', //字典标签(中文)
        dictEnName: '', //字典标签(英文)
        dictId: '', //字典键值
        sort: 0, //排序
        status: '0', //状态（0正常 1停用）
        isDefault: '0', //系统默认（0是 1否）
        remark: '', //备注
      },
      dictTypeName: '',
    }
  },
  watch: {
    'advanceQuery.dictName': {
      handler(newVal) {
        this.advanceQuery.remark = this.dictTypeName + newVal
      },
      deep: true,
    },
  },
  components: {
    VsocDrawer,
  },
  mounted() {},
  methods: {
    open(item) {
      this.$refs.form.resetValidation()
      this.$nextTick(() => {
        if (this.mode === 'new') {
          this.advanceQuery = {
            dictType: item.value, //字典类型id
            dictName: '', //字典标签(中文)
            dictEnName: '', //字典标签(英文)
            dictId: '', //字典键值
            sort: 0, //排序
            status: '0', //状态（0正常 1停用）
            isDefault: '0', //系统默认（0是 1否）
            remark: item.text, //备注
          }
          this.dictTypeName = item.text
        } else {
          this.advanceQuery = { ...item }
        }
        this.isDrawerShow = true
      })
    },
    onSave(callback) {
      const bool = this.$refs.form.validate()
      if (!bool) return callback(false, true)

      if (this.mode === 'edit') {
        updateDictData(this.advanceQuery)
          .then(resp => {
            if (resp.code === 200) {
              this.$notify.info(
                'success',
                this.$t('global.hint.edit', [this.advanceQuery.dictName]),
              )
              this.$emit('save')
              callback()
            } else {
              throw new Error(resp.msg)
            }
          })
          .catch(e => {
            this.$notify.info('error', e)
            callback(false, true)
          })
      } else {
        addDictData(this.advanceQuery)
          .then(resp => {
            if (resp.code === 200) {
              this.$notify.info(
                'success',
                this.$t('global.hint.add', [this.advanceQuery.dictName]),
              )
              this.$emit('save')
              callback()
            } else {
              throw new Error(resp.msg)
            }
          })
          .catch(e => {
            callback(false, true)
          })
      }
    },
  },
}
</script>

<style scoped></style>
