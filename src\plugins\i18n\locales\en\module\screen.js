let screen = {
  week: {
    sun: 'Sun',
    mon: 'Mon',
    tue: 'Tue',
    wed: 'Wed',
    thu: 'Thu',
    fri: 'Fri',
    sat: 'Sat',
  },
  alertStatus: 'Alert Distribution By Status',
  alertEvent: 'Top Alert Types',
  alerting: 'Pending Alerts',
  alertTrend: 'Trending of Alerts',

  dataHandle: 'Data Processing',
  ticketStatus: 'Ticket Distribution By Status',
  ticketType: 'Top Ticket Types',
  ticketing: 'Pending Tickets',
  ticketTrend: 'Trending of Tickets',

  alertTotal: 'Alerts',
  ticketTotal: 'Tickets',
  vulnerabilityTotal: 'Vulnerabilities',
  assetTotal: 'Assert',
  model: 'Model',

  fullHint:
    'Please click the full-screen button in the upper right corner for the best display experience!',

  bar: {
    cloud: 'Cloud Posture',
    vehicle: 'Vehicle Posture',
  },
}

let cloud = {
  title: 'Vehicle Connectivity Cloud Security Dashboad',
  left1: {
    title: 'Security Risks Overview',
  },
  left2: '',

  center1: '',
  center2: '',
  center3: '',

  right1: '',
  right2: '',

  times: 'Times',
  days: 'Days',
}

export let loadCloudMenu = list => {
  // let list = store.state.global.cloudConfigList
  // if (store.state.global.cloudConfigList.length === 0) {
  //   list = await store.dispatch('global/loadCloudConfigList')
  // }
  cloud.left1.attacksToday = list.find(v => v.itemKey === 'C002')?.itemEnName
  cloud.left1.attacksDays7 = list.find(v => v.itemKey === 'C003')?.itemEnName
  cloud.left1.internetDomain = list.find(v => v.itemKey === 'C004')?.itemEnName
  cloud.left1.baselineItem = list.find(v => v.itemKey === 'C005')?.itemEnName
  cloud.left1.coutinousDays = list.find(v => v.itemKey === 'C006')?.itemEnName

  cloud.left2 = list.find(v => v.itemKey === 'C009')?.itemEnName

  cloud.center1 = list.find(v => v.itemKey === 'C007')?.itemEnName
  cloud.center2 = list.find(v => v.itemKey === 'C010')?.itemEnName
  cloud.center3 = list.find(v => v.itemKey === 'C011')?.itemEnName

  cloud.right1 = list.find(v => v.itemKey === 'C008')?.itemEnName
  cloud.right2 = list.find(v => v.itemKey === 'C012')?.itemEnName
}

// loadCloudMenu()

export default Object.assign(screen, { cloud })
