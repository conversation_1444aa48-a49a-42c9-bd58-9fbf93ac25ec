<template>
  <!-- 定制化地图-->
  <div class="box box-chart">
    <div class="box-header">
      {{ title }}
    </div>
    <!-- style="margin-top: 2%" -->
    <div ref="echartBox" class="tab-content">
      <vsoc-chart
        :echartId="echartId"
        :option="option"
        :isShowEmpty="false"
      ></vsoc-chart>

      <!-- <template v-if="toData.length !== 0">
        <vsoc-chart :echartId="echartId" :option="option"></vsoc-chart>
      </template>
      <template v-else>
        <div
          class="box-chart d-flex align-center color--primary justify-center fs-16"
        >
          None
        </div>
      </template> -->
    </div>
  </div>
</template>

<script>
import VsocChart from '@/components/VsocChart.vue'
import { getRoundSize, tooltip } from './chart'
import china from './map/china'

export default {
  name: 'CardItem11',
  props: {
    title: {
      type: String,
      default: '',
    },
    toData: {
      type: Array,
      default: () => {
        return []
      },
    },
    echartId: {
      type: String,
      default: '',
    },
    height: {
      type: Number,
      default: 0,
    },
    width: {
      type: Number,
      default: 0,
    },
  },
  components: {
    VsocChart,
  },
  created() {
    this.$echarts.registerMap('china', china)
  },
  data() {
    return {}
  },
  computed: {
    option() {
      let dataValue = []
      this.toData.forEach(v => {
        if (v.longitude && v.latitude) {
          dataValue.push({
            name: china.features.find(x =>
              v.cityName.includes(x.properties && x.properties.name),
            )?.properties.name,
            value: [v.longitude, v.latitude, v.count],
          })
        }
      })
      return {
        visualMap: {
          // 视觉映射组件
          type: 'piecewise',
          show: true,
          hoverLink: false,
          bottom: 0,
          left: 0,
          textStyle: {
            color: 'rgba(255, 255, 255, 0.8)',
            fontsize: getRoundSize(14),
          },
          itemSymbol: 'circle',
          itemWidth: 8,
          itemHeight: 8,
          itemGap: getRoundSize(14),
          min: 1,
          max: 500,
          //gt最小 lte最大
          pieces: [
            {
              gt: 500,
              label: this.$t('global.count') + ' >500',
            },
            {
              gt: 301,
              lte: 500,
              label: this.$t('global.count') + ' 301-500',
            },
            {
              gt: 101,
              lte: 300,
              label: this.$t('global.count') + ' 101-300',
            },
            {
              gt: 1,
              lte: 100,
              label: this.$t('global.count') + ' 1-100',
            },
          ],
          color: ['#FF385D', '#FF9229', '#F0DA4C', '#21CBFF'], //自定义范围的颜色
        },
        // 底图样式
        geo: {
          show: true,
          roam: false,
          map: 'china',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          label: {
            show: true,
            textStyle: {
              color: '#fff',
              fontSize: getRoundSize(14),
              fontWeight: 400,
            },
          },
          itemStyle: {
            borderColor: '#0385D3',
            borderWidth: 0.7,
            areaColor: 'rgba(255, 255, 255, 0.05)',
          },

          emphasis: {
            itemStyle: {
              areaColor: 'rgba(0,162,248, .6)',
            },
            label: {
              show: true,
              color: '#fff',
              fontSize: getRoundSize(14),
            },
          },
        },
        tooltip: {
          ...tooltip(),
          trigger: 'item',
          formatter(val) {
            if (!(val.data && val.data.value)) return
            return val.marker + '\t' + val.data.name + ':\t' + val.data.value[2]
          },
        },
        series: [
          {
            zlevel: 1,
            selectedMode: false,
            type: 'map',
            geoIndex: 0,
            data: dataValue,
            mapType: 'china',
            showLegendSymbol: false,
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
          },
          {
            coordinateSystem: 'geo',
            type: 'scatter',
            data: dataValue,
            symbolSize: getRoundSize(20),
            symbol(val) {
              return 'image://' + require('../images/P1.png')
              // if (val.length && val[2]) {
              //   if (Number(val[2]) > 500) {
              //     return 'image://' + require('../images/map1.png')
              //   } else if (Number(val[2]) > 301 && Number(val[2]) < 500) {
              //     return 'image://' + require('../images/map2.png')
              //   } else if (Number(val[2]) > 101 && Number(val[2]) < 300) {
              //     return 'image://' + require('../images/map3.png')
              //   } else if (Number(val[2]) > 1 && Number(val[2]) < 100) {
              //     return 'image://' + require('../images/map4.png')
              //   }
              // }
            },
            zlevel: 2,
          },
          // {
          //   type: 'effectScatter', // 带有涟漪特效动画的散点（气泡）图
          //   coordinateSystem: 'geo',
          //   markPoint: {
          //     symbol:
          //       'path://M9 20C7.75 20 0 13.9705 0 9C0 4.0295 4.0295 0 9 0C13.9705 0 18 4.0295 18 9C18 13.9705 10.25 20 9 20ZM9 11.75C10.5188 11.75 11.75 10.5187 11.75 9C11.75 7.48125 10.5188 6.25 9 6.25C7.48125 6.25 6.25 7.48125 6.25 9C6.25 10.5187 7.48125 11.75 9 11.75Z',
          //   },
          //   zlevel: 10,
          //   symbol: 'circle',
          //   symbolSize: 10,
          //   // 涟漪特效
          //   rippleEffect: {
          //     period: 4,
          //     scale: 4,
          //     brushType: 'stroke',
          //   },
          //   itemStyle: {
          //     color: '#FF385D',
          //   },
          //   label: {
          //     show: true,
          //     color: '#fff',
          //     position: 'bottom',
          //     fontSize: getRoundSize(16),
          //     formatter: function (item) {
          //       return ''
          //     },
          //   },
          //   // 这里用来组装自定义数据，以便在tooltip中取得。
          //   data: dataValue,
          // },
        ],
      }
    },
  },
}
</script>
