const list = {
  currentTitle: '列表',
  headers: {
    name: '列表名称',
    description: '描述',
    valueTypeName: '类型',
    itemCount: '项',
    isEncrypt: '加密',
    isEncrypt1: '加密模式',
    Longitude: '经度',
    latitude: '纬度',
  },
  delete: {
    title: '删除列表',
    text: '是否确认列表：{0}？',
  },
  tip: '包含字母数字值或{},-_./:&*@]"',
  detail: '列表详情',
  btn: {
    new: '新增列表',
    edit: '编辑列表',
    detail: '列表详情',
    del: '删除项',
    export: '导出项',
    add: '新增项',
    import: '导入项',
    stop: '停止绘制',
    begin: '开始绘制',
    addPath: '添加坐标点',
  },
  hint: {
    tip: '请注意，此列表中的项目是加密的，因此只能用于加密信号。',
    operation: '暂存操作',
    tip1: '格式有误',
    tip2: '项是必填项',
    tip3: '重复项',
    tip4: '错误信息',
    tip5: '项的数量不能超过1000',
    tip6: '经度、纬度是必填项',
    tip7: '鼠标左键开始绘制，右键终止绘制',
    tip8: '导入须知:',
    tip9: '文件类型：Excel',
    tip10: '只支持单列数据',
    tip11: '首行标题：必须为“项”',
  },
  del: {
    title: '删除项（暂存操作）',
    info: '选择的项将暂时从列表中删除',
    tip: '已从暂存表中删除',
    text: '是否确认删除项：{0}？',
  },
}

export default list
