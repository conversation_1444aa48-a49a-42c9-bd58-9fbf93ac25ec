<template>
  <v-form ref="form" v-model="valid" lazy-validation>
    <div class="mt-4">
      <v-row class="pl-6 pr-6 mb-6">
        <span class="text-base font-weight-medium color-base">
          {{ $t('global.drawer.baseInfo') }}
        </span>
      </v-row>
      <v-row class="pl-6 pr-6">
        <v-select
          v-model="classifyEt.type"
          color="primary"
          :items="classifyTypes"
          :disabled="mode !== 'new'"
          :label="$t('classify.headers.typeName')"
          :rules="[
            v =>
              !!v ||
              $t('validation.required', [$t('classify.headers.typeName')]),
          ]"
          class="is-required"
          item-disabled="disabled"
          :menu-props="{ offsetY: true }"
        >
        </v-select>
      </v-row>
      <v-row class="pl-6 pr-6">
        <v-text-field
          v-model="classifyEt.name"
          :label="$t('classify.headers.name')"
          required
          color="primary"
          class="is-required"
          :rules="[
            v =>
              !!v || $t('validation.required', [$t('classify.headers.name')]),
          ]"
        >
        </v-text-field>
      </v-row>

      <v-row class="pl-6 pr-6" v-if="classifyEt.type == 3">
        <v-autocomplete
          v-model="classifyEt.classifySuperiorList"
          color="primary"
          :items="classifySuperiors"
          :label="$t('classify.classifySuperior')"
          multiple
          item-text="text"
          item-value="name"
          :menu-props="{ offsetY: true }"
        >
          <template v-slot:selection="{ index }">
            <span v-if="index === 0">
              {{ $t('global.pagination.selected') }}：{{
                classifyEt.classifySuperiorList.length
              }}
            </span>
          </template>
        </v-autocomplete>
      </v-row>

      <v-row class="pl-6 pr-6">
        <v-textarea
          v-model="classifyEt.description"
          :label="$t('classify.headers.description')"
          :rows="$AREA_ROWS"
          color="primary"
        ></v-textarea>
      </v-row>
    </div>
  </v-form>
</template>

<script>
import {
  addAlarmType,
  updateAlarmType,
  getClassifySuperiorNullData,
} from '@/api/classify/index'

export default {
  name: 'Edit',
  props: {
    item: {
      type: Object,
      required: false,
    },
    mode: {
      type: String,
      required: false,
      default: () => 'new',
    },
    classifyTypes: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      classifyEt: this.item,

      valid: true,
      classifySuperiors: [],
    }
  },
  mounted() {
    this.loadClassifySuperiorNullData()
  },
  methods: {
    async loadClassifySuperiorNullData() {
      try {
        const { data } = await getClassifySuperiorNullData({
          classifySuperiorList: this.classifyEt.classifySuperiorList,
        })
        this.classifySuperiors = data.map(item => {
          return {
            ...item,
            text: `${item.name}--${item.description}`,
          }
        })
      } catch (err) {
        console.log('获取平台分类报错', err)
      }
    },
    save(callback) {
      const bool = this.$refs.form.validate()
      if (!bool) return callback(false, true)

      if (this.mode === 'edit') {
        updateAlarmType(this.classifyEt)
          .then(resp => {
            if (resp.code === 200) {
              this.$notify.info(
                'success',
                this.$t('global.hint.edit', [this.classifyEt.name]),
              )
              this.$emit('save')
              callback()
            } else {
              throw new Error(resp.msg)
            }
          })
          .catch(e => {
            this.$notify.info('error', e)
            callback(false, true)
          })
      } else {
        addAlarmType(this.classifyEt)
          .then(resp => {
            if (resp.code === 200) {
              this.$notify.info(
                'success',
                this.$t('global.hint.add', [this.classifyEt.name]),
              )
              this.$emit('save')
              callback()
            } else {
              throw new Error(resp.msg)
            }
          })
          .catch(e => {
            console.log(e)
            callback(false, true)
          })
      }
    },
  },
}
</script>

<style scoped></style>
