import Vue from 'vue'
import vuetify from '../../plugins/vuetify'
import Snackbar from './Snackbar.vue'

const Notify = function () {
  const SnackbarConstructor = Vue.extend(Snackbar)
  const instance = new SnackbarConstructor()
  instance.$vuetify = vuetify.framework
  instance.$mount()
  document.body.appendChild(instance.$el)
  instance.$el.style.zIndex = 10000

  return instance
}

Notify.info = function (level, message, timeout) {
  Notify().info(level, message, timeout)
}

export default Notify
