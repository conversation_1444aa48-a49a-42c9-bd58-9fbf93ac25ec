<template>
  <div class="w-100 h-100 d-flex align-center flex-column">
    <img
      style="width: 625px; margin-top: 109px"
      src="@/assets/images/404.png"
    />
    <div
      class="color-base"
      style="font-size: 18px; line-height: 26px; margin-top: 75px"
    >
      {{ $t('global.request.notFound') }}
    </div>
    <v-btn
      color="primary"
      elevation="0"
      style="margin-top: 32px"
      @click="goBack"
    >
      {{ $t('global.hint.back1') }}
    </v-btn>
  </div>
</template>

<script>
import { isExternal } from '@/@core/utils/validation'
import { getToken } from '@/util/token'

export default {
  methods: {
    async goBack() {
      const token = getToken()
      if (token) {
        await this.$store
          .dispatch('permission/generateRoutes')
          .then(myRouters => {
            let currentPath =
              myRouters.find(v => !isExternal(v.path))?.path || '/'
            this.$router.push({
              path: currentPath,
            })
          })
          .catch(err => {
            this.$notify.info('error', err)
          })
      } else {
        this.$router.push({
          path: '/login',
        })
      }
    },
  },
  // setup() {
  //   // if (this.$store.state.permission.menus.length) {
  //   //     localStorage.setItem('oldToken', '')
  //   //     await this.$store
  //   //       .dispatch('permission/generateRoutes')
  //   //       .then(res => {
  //   //         this.$router.push(res[0].path)
  //   //       })
  //   //       .catch(err => {
  //   //         this.$notify.info('error', err)
  //   //       })
  //   //   } else {
  //   //     this.$router.push('/')
  //   //   }
  //   const vm = getCurrentInstance().proxy
  //   const { router } = useRouter()
  //   const goBack = () => {
  //     let myRouters = vm.$store.state.permission.routes
  //     let currentPath = myRouters.find(v => !isExternal(v.path))?.path || '/'
  //     router.push({
  //       path: currentPath,
  //     })
  //   }
  //   return {
  //     goBack,
  //     icons: {
  //       mdiAlert,
  //     },
  //   }
  // },
}
</script>

<style lang="scss"></style>
