const enums = {
  active: {
    valid: 'valid',
    invalid: 'invalid',
  },
  encrypt: {
    encryption: 'encryption',
    unencryption: 'no encryption',
  },
  datePresets: {
    today: 'Today',
    last1: 'Last 1 hour',
    last24: 'Last 24 hours',
    last7: 'Last 7 days',
    last30: 'Last 30 days',
    last15: 'Last 15 days',
    lastxm: 'Last month | Last {num} months',
    lastxh: 'Last {num} hour | Last {num} hours',
    lastxd: 'Last day | Last {num} days',
    all: 'All time periods',
    highActivity: 'High activity',
    moderateActivity: 'Moderate activity',
    lowActivity: 'Low activity',
  },
  operateStatus: {
    success: 'Success',
    error: 'Error',
  },
  softwareType: {
    application: 'Application',
    system: 'Operating System',
    equipment: 'Hardware Equipment',
  },
  reference: {
    yes: 'Yes',
    no: 'No',
  },
}

export default enums
