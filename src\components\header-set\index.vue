<template>
  <div class="set-box">
    <vsoc-drawer
      :title="$t('action.setHeader')"
      v-model="isDrawerShow"
      @click:confirm="onChange"
    >
      <v-list>
        <v-list-item class="select-all-box">
          <v-list-item-action class="mr-2">
            <v-checkbox v-model="allIn" @change="onSelectAll" />
          </v-list-item-action>
          <v-list-item-title>{{ $t('action.selectAll') }}</v-list-item-title>
        </v-list-item>
        <draggable
          class="move-box"
          :list="newHeaderList"
          :handle="'.mover'"
          @end="onDragEnd"
        >
          <transition-group>
            <v-list-item
              v-for="item in newHeaderList"
              :key="item.value"
              class="mover"
            >
              <vsoc-icon
                type="fill"
                icon="icon-move-full"
                class="action-btn mr-2"
                size="16"
              ></vsoc-icon>
              <v-list-item-action class="mr-2">
                <v-checkbox
                  :disabled="item.disabled"
                  v-model="item.checked"
                  color="primary"
                  @change="handleCheck(item)"
                ></v-checkbox>
              </v-list-item-action>
              <v-list-item-title>{{
                $t(item.text1 || item.text)
              }}</v-list-item-title>
            </v-list-item>
          </transition-group>
        </draggable>
      </v-list>
    </vsoc-drawer>
  </div>
</template>
<script>
import VsocDrawer from '@/components/VsocDrawer.vue'
import { setLocalStorage } from '@/util/localStorage'
import draggable from 'vuedraggable'
export default {
  components: {
    VsocDrawer,
    draggable,
  },

  computed: {},
  data() {
    return {
      isDrawerShow: false,
      header: {},
      actionItem: undefined,
      headerListList: [],
      newHeaderList: [],
      allIn: false,
    }
  },
  created() {},
  methods: {
    open(params, callback) {
      this.isDrawerShow = true
      // this.headerListList = params
      this.callback = callback
      // const jsonList = localStorage.getItem(this.$route.path + '_headerList')
      // const list = JSON.parse(jsonList)
      // if (list && list.length > 0) {
      //   callback(list)
      //   this.actionItem = list.find(v => v.value === 'actions')
      //   this.newHeaderList = list.filter(v => v.value !== 'actions')
      //   this.handleCheck()
      // } else {
      // }
      this.actionItem = params.find(
        v => v.value === 'actions' || v.value === 'actions1',
      )
      this.newHeaderList = params
        .filter(v => v.value !== 'actions' && v.value !== 'actions1')
        .map(t => {
          return {
            checked: t.checked || true,
            ...t,
          }
        })
      this.handleCheck()
    },
    //是否全选
    handleCheck() {
      let arrTrue = this.newHeaderList.filter(v => v.checked)
      if (arrTrue.length === this.newHeaderList.length) {
        this.allIn = true
      } else {
        this.allIn = false
      }
    },
    onSelectAll(bool) {
      if (bool) {
        this.newHeaderList.forEach(item => {
          item.checked = true
        })
      } else {
        this.newHeaderList.forEach(item => {
          item.checked = item?.disabled || false
        })
      }
    },
    onDragEnd(e) {
      this.$forceUpdate()
    },
    onChange(callback) {
      let filterList = this.newHeaderList.map(item => {
        if (item.checked) {
          delete item.class
          delete item.cellClass
        } else {
          item.class = 'display-none'
          item.cellClass = 'display-none'
        }
        return item
      })
      // let newList = deepClone(filterList)
      if (this.actionItem) {
        if (this.actionItem.value === 'actions1') {
          filterList.unshift(this.actionItem)
        } else {
          filterList.push(this.actionItem)
        }
      }
      // const newList = this.actionItem
      //   ? this.actionItem.value === 'action1'
      //     ? filterList.unshift(this.actionItem)
      //     : filterList.push(this.actionItem)
      //   : filterList
      // localStorage.setItem(
      //   this.$route.path + '_headerList',
      //   JSON.stringify(filterList),
      // )
      setLocalStorage(
        this.$route.path + '_headerList',
        JSON.stringify(filterList),
      )
      callback()
      this.callback(filterList)
    },
  },
}
</script>

<style lang="scss" scoped>
.theme--light.select-all-box {
  background: #fafafa !important;
}
.theme--dark.select-all-box {
  background: #2a3867 !important;
}
.set-box {
  position: relative;
  .select-all-box {
    // background: #fafafa !important;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 111;
    .v-list-item__action {
      margin-left: -4px !important;
    }
  }
  .v-list-item {
    min-height: 50px !important;
    padding: 0 24px !important;
  }
  .move-box {
    padding-top: 50px !important;
  }
  .v-list {
    padding: 0 !important;
  }
  .v-list-item__title {
    font-size: 14px !important;
    color: var(--v-color-base) !important;
  }

  ::v-deep .vsoc-drawer__content {
    padding-top: 0 !important;
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
}
</style>
