<template>
  <div id="data-view" v-resize="getScreenSize">
    <dv-loading v-if="isLoading">Loading...</dv-loading>
    <div id="dv-full-screen-container" v-else>
      <!-- <div class="main-header main-light">
        <div class="mh-logo" @click="goIndex">
        </div>
        <div class="mh-title">{{ title }}</div>
        <div class="mh-date">
          {{ new Date() | toDate('yyyy年MM月dd日 hh:mm:ss') }}
        </div>
      </div> -->
      <div class="main-header">
        <!-- <v-img
          @click="goIndex"
          contain
          :height="$getCeilSize(28)"
          :min-width="$getCeilSize(282)"
          :aspect-ratio="282 / 28"
          src="./images/yq-logo.svg"
          alt="logo"
          class="mh-logo"
        /> -->
        <dv-decoration-8
          @click="goIndex"
          class="header-left-decoration mh-high"
          :color="[primary, primary]"
        />

        <dv-decoration-5
          class="header-center-decoration"
          :color="[primary, primary]"
        />
        <dv-decoration-8
          class="header-right-decoration"
          :reverse="true"
          :color="[primary, primary]"
        />
        <div class="mh-title" @click="goIndex">
          {{ title }}
        </div>
        <div class="mh-date d-flex justify-lg-space-between align-center">
          <span class="text-center"
            >{{ dateYear }} <v-spacer></v-spacer> {{ dateDay }}
            {{ dateWeek }}</span
          >
          <v-menu offset-y left nudge-bottom="5">
            <template v-slot:activator="{ on, attrs }">
              <v-btn
                elevation="0"
                small
                v-bind="attrs"
                class="d-flex align-center text-base date-option"
                v-on="on"
              >
                <template>
                  <span>{{ currentText }}</span>
                  <v-icon class="ml-1">mdi-chevron-down</v-icon>
                </template>
              </v-btn>
            </template>
            <v-list class="pa-0 date-list">
              <v-list-item-group :value="currentDate" @change="onSelectData">
                <v-list-item
                  v-for="item in dateOption"
                  :key="item.value"
                  :value="item.value"
                >
                  <v-list-item-title class="fs-16">{{
                    item.text
                  }}</v-list-item-title>
                </v-list-item>
              </v-list-item-group>
            </v-list>
          </v-menu>

          <!-- <v-btn icon @click="toggleFullScreen">
            <v-icon
              :size="Math.round((this.screenWidth / 1920) * 32)"
              :color="primary"
              >mdi-power</v-icon
            >
            <vsoc-icon
              :size="$getCeilSize(30)"
              :icon="
                isFull ? 'icon-daxiaopingqiehuan' : 'icon-daxiaopingqiehuan-1'
              "
            ></vsoc-icon>
          </v-btn> -->
          <v-btn icon @click="toggleFullScreen">
            <v-icon v-if="isFull" :size="$getCeilSize(30)" :color="primary"
              >mdi-power</v-icon
            >
            <vsoc-icon
              :size="$getCeilSize(30)"
              v-else
              :icon="'icon-daxiaopingqiehuan-1'"
            ></vsoc-icon>
          </v-btn>
        </div>
      </div>

      <div class="main-container">
        <div class="item1">
          <dv-border-box-12>
            <!-- :events="alertData.events"
              :status="alertData.status"
              :total="alertData.total" -->
            <left-chart-1
              :events="alertData.events"
              :status="alertData.status"
              :total="alertData.total"
            ></left-chart-1>
          </dv-border-box-12>
        </div>
        <div class="item2">
          <dv-border-box-12>
            <right-chart-1
              :events="ticketData.events"
              :status="ticketData.status"
              :total="ticketData.total"
            ></right-chart-1>
          </dv-border-box-12>
        </div>
        <div class="item3">
          <dv-border-box-12>
            <left-chart-2
              :alarmPending="screenData.alarmPending"
            ></left-chart-2>
          </dv-border-box-12>
        </div>
        <div class="item4">
          <dv-border-box-12>
            <right-chart-2
              :ticketPending="screenData.ticketPending"
            ></right-chart-2>
          </dv-border-box-12>
        </div>
        <div class="item5">
          <dv-border-box-12>
            <left-chart-3
              :alarmTrend="screenData.alarmTrend"
              :xList="xList"
              :total="screenData.alarmTrendSum"
            ></left-chart-3>
          </dv-border-box-12>
        </div>
        <div class="item6">
          <dv-border-box-12>
            <center-chart-2
              :xList="xList"
              :dataProcessing="screenData.dataProcessing"
              :total="screenData.dataProcessingSum"
            ></center-chart-2>
          </dv-border-box-12>
        </div>
        <div class="item7">
          <dv-border-box-12>
            <right-chart-3
              :xList="xList"
              :ticketTrend="screenData.ticketTrend"
              :total="screenData.ticketTrendSum"
            ></right-chart-3>
          </dv-border-box-12>
        </div>
        <div class="item8">
          <center-chart-1
            :refreshDate="screenData.posture.time"
            :posture="screenData.posture.posture"
          ></center-chart-1>
        </div>
        <!-- <div class="mc-left"></div>
        <div class="mc-middle"></div>
        <div class="mc-right"></div> -->
      </div>
    </div>
  </div>
</template>

<script>
import { getVehicleList } from '@/api/screen/index.js'
import { getDataProcessing } from '@/api/workbench/index'
import LeftChart1 from './component/LeftChart1.vue'
import LeftChart2 from './component/LeftChart2.vue'
import LeftChart3 from './component/LeftChart3.vue'

import RightChart1 from './component/RightChart1.vue'
import RightChart2 from './component/RightChart2.vue'
import RightChart3 from './component/RightChart3.vue'

import CenterChart1 from './component/CenterChart1.vue'
import CenterChart2 from './component/CenterChart2.vue'

import { toDate } from '@/util/filters'
import {
  exitFullscreen,
  firstPath,
  isFullScreen,
  requestFullScreen,
} from '@/util/utils'
import { endOfDay, format, startOfDay, subDays } from 'date-fns'
import DvDecoration8 from './component/dv-decoration8/Index.vue'

export const ISO_FORMAT = 'yyyy-MM-dd HH:mm:ss'

//判断是否是全屏状态
let isFull =
  Math.abs(
    window.screen.height - window.document.documentElement.clientHeight,
  ) <= 17

// 阻止F11键默认事件，用HTML5全屏API代替
window.addEventListener('keydown', function (e) {
  e = e || window.event
  if (e.keyCode === 122 && !isFull) {
    e.preventDefault()
    // enterFullScreen()
    requestFullScreen(document.documentElement)
  }
})

// // 打开浏览器全屏模式
// function enterFullScreen() {
//   let el = document.documentElement
//   let rfs =
//     el.requestFullScreen ||
//     el.webkitRequestFullScreen ||
//     el.mozRequestFullScreen ||
//     el.msRequestFullscreen
//   if (rfs) {
//     // typeof rfs != "undefined" && rfs
//     rfs.call(el)
//   } else if (typeof window.ActiveXObject !== 'undefined') {
//     // for IE，这里其实就是模拟了按下键盘的F11，使浏览器全屏
//     let wscript = new ActiveXObject('WScript.Shell')
//     if (wscript != null) {
//       wscript.SendKeys('{F11}')
//     }
//   }
// }

// // 退出全屏
// function exitFullScreen() {
//   let el = document
//   let cfs =
//     el.cancelFullScreen ||
//     el.mozCancelFullScreen ||
//     el.msExitFullscreen ||
//     el.webkitExitFullscreen ||
//     el.exitFullscreen
//   if (cfs) {
//     // typeof cfs != "undefined" && cfs
//     cfs.call(el)
//   } else if (typeof window.ActiveXObject !== 'undefined') {
//     // for IE，这里和fullScreen相同，模拟按下F11键退出全屏
//     let wscript = new ActiveXObject('WScript.Shell')
//     if (wscript != null) {
//       wscript.SendKeys('{F11}')
//     }
//   }
// }
export default {
  name: 'data-big-screen',
  components: {
    LeftChart1,
    LeftChart2,
    LeftChart3,
    RightChart1,
    RightChart2,
    RightChart3,
    CenterChart1,
    CenterChart2,
    DvDecoration8,
  },
  data() {
    return {
      refreshDate: null,
      timer2: null,
      isFull: isFull,
      currentDate: 7,
      primary: '#44e2fe',
      dateDay: null,
      dateYear: null,
      dateWeek: null,

      timer: null,

      isLoading: false,
      // 获取浏览器可视区域高度（包含滚动条）、
      // 获取浏览器可视区域高度（不包含工具栏高度）、
      // 获取body的实际高度  (三个都是相同，兼容性不同的浏览器而设置的)
      screenHeight:
        window.innerHeight ||
        document.documentElement.clientHeight ||
        document.body.clientHeight,
      screenWidth:
        window.innerWidth ||
        document.documentElement.clientWidth ||
        document.body.clientWidth,

      alertData: {
        status: [],
        events: [],
        total: 0,
      },
      ticketData: {
        status: [],
        events: [],
        total: 0,
      },
      screenData: {},
      xList: [],
    }
  },
  computed: {
    title() {
      return this.$t('projectName')
    },
    weekday() {
      return [
        this.$t('screen.week.sun'),
        this.$t('screen.week.mon'),
        this.$t('screen.week.tue'),
        this.$t('screen.week.wed'),
        this.$t('screen.week.thu'),
        this.$t('screen.week.fri'),
        this.$t('screen.week.sat'),
      ]
    },
    currentText() {
      return this.dateOption.find(v => v.value === this.currentDate)?.text
    },
    dateOption() {
      return [
        {
          text: this.$t('enums.datePresets.last7'),
          value: 7,
        },
        {
          text: this.$t('enums.datePresets.last15'),
          value: 15,
        },
      ]
    },
  },
  watch: {
    '$i18n.locale': {
      handler(lang) {
        // document.getElementById('data-view').setAttribute('data-lang', lang)
        window.document.documentElement.setAttribute('data-lang', lang)
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    this.loadData(true)
  },
  mounted() {
    this.$nextTick(() => {
      if (!isFull || !isFullScreen()) {
        // enterFullScreen()
        requestFullScreen(document.documentElement)
        // document.documentElement.requestFullscreen({ navigationUI: 'hide' })
      }
    })
    this.timeInterval()
    let time =
      this.$store.getters['enums/getPollingInterval'](
        this.$generateMenuTitle(this.$route.meta),
      ) || 20
    this.timer2 = setInterval(this.loadData, time * 1000)
  },
  beforeDestroy() {
    clearInterval(this.timer)
    clearInterval(this.timer2)
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      // 通过 `vm` 访问组件实例
      if (!isFull || !isFullScreen()) {
        // enterFullScreen()
        requestFullScreen(document.documentElement)
        // document.documentElement.requestFullscreen({ navigationUI: 'hide' })
      }
    })
  },
  beforeRouteLeave(to, from, next) {
    if (isFull || isFullScreen()) {
      exitFullscreen()
      // exitFullScreen()
    }
    next()
  },
  methods: {
    async loadDataCollection(params) {
      const { data } = await getDataProcessing(params)
      this.screenData.dataProcessingSum = data.data
        .map(v => Number(v.number))
        .reduce((x, y) => x + y)
      this.screenData.dataProcessing = data
    },
    async loadData(mode = false) {
      try {
        this.refreshDate = new Date()
        this.isLoading = mode
        const params = {
          startDate: format(
            startOfDay(subDays(new Date(), this.currentDate - 1)),
            ISO_FORMAT,
          ),
          endDate: format(endOfDay(new Date()), ISO_FORMAT),
        }
        let { data } = await getVehicleList(params)
        this.handleAlertData(data)
        this.handleTicketData(data)
        this.screenData.assetCount = 0
        this.screenData = data
        this.screenData.dataProcessing = {}
        this.screenData.dataProcessingSum = 0
        this.xList = []
        for (let i = this.currentDate - 1; i >= 0; i--) {
          this.xList.push(format(subDays(new Date(), i), 'MM/dd'))
        }
        if (!this.isFull && mode) {
          this.$notify.info('success', this.$t('screen.fullHint'))
        }
        await this.loadDataCollection(params)
      } catch (err) {
        console.log('数据大屏数据报错', err)
      } finally {
        this.isLoading = false
      }
    },
    handleAlertData(obj) {
      this.alertData.status = obj.alarmStatusList.map(s => {
        return {
          name: s.statusName,
          value: s.count,
          percent: s.format,
        }
      })
      this.alertData.events = obj.alarmEventTypeTopFive
      this.alertData.total = obj.alarmCount
    },
    handleTicketData(obj) {
      this.ticketData.status = obj.ticketStatusList.map(s => {
        return {
          name: s.statusName,
          value: s.count,
          percent: s.format,
        }
      })
      this.ticketData.events = obj.ticketTypeTopFive
      this.ticketData.total = obj.ticketCount
    },
    onSelectData(value) {
      if (!value) return
      this.currentDate = value
      this.loadData()
    },
    toggleFullScreen() {
      if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen()
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen()
        }
        if (document.referrer) {
          this.$router.go(-1)
        } else {
          this.$router.push(firstPath())
        }
      }
      // document.documentElement.onfullscreenchange = () => {
      //   console.log('进来了')
      //   location.reload()
      // }
    },
    timeInterval() {
      this.timer = setInterval(() => {
        this.dateDay = toDate(new Date(), 'hh:mm:ss')
        this.dateYear = toDate(new Date(), 'yyyy/MM/dd')
        this.dateWeek = this.weekday[new Date().getDay()]
      }, 1000)
    },
    goIndex() {
      this.$router.push(firstPath())
      // this.$router.push('/')
    },
    getScreenSize() {
      this.getScreenHeight()
      this.getScreenWidth()
      isFull =
        Math.abs(
          window.screen.height - window.document.documentElement.clientHeight,
        ) <= 17
      this.isFull = isFull
    },
    // 获取浏览器高度进行自适应
    getScreenHeight() {
      this.screenHeight =
        window.innerHeight ||
        document.documentElement.innerHeight ||
        document.body.clientHeight
    },
    // 字体大小根据宽度自适应
    getScreenWidth() {
      this.screenWidth =
        window.innerWidth ||
        document.documentElement.clientWidth ||
        document.body.clientWidth
      this.$store.commit('global/setClientWidth', this.screenWidth)
    },
  },
}
</script>

<style lang="scss">
@import './index.scss';
</style>
