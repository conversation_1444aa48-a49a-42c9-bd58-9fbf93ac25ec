<template>
  <!-- min-width="230" -->
  <div>
    <v-menu
      offset-y
      left
      min-width="160"
      nudge-bottom="14"
      content-class="user-profile-menu-content"
      z-index="99"
    >
      <template v-slot:activator="{ on, attrs }">
        <div v-bind="attrs" v-on="on" class="d-flex align-center">
          <!-- <v-badge bottom color="success" overlap offset-x="12" offset-y="12" dot>
          <v-avatar
            size="24"
            v-bind="attrs"
            color="primary"
            class="v-avatar-light-bg primary--text"
            v-on="on"
          >
            <v-img :src="require('@/assets/images/avatars/3.png')"></v-img>
          </v-avatar>
        </v-badge> -->
          <v-avatar
            size="28"
            color="primary"
            class="v-avatar-light-bg primary--text"
          >
            <v-img :src="require('@/assets/images/avatars/3.png')"></v-img>
          </v-avatar>
          <div
            class="d-inline-flex flex-column justify-center ms-3"
            style="vertical-align: middle"
          >
            <span class="text-ml" style="user-select: none">
              <span class="text-no-wrap">{{
                $store.getters.userInfo && $store.getters.userInfo.userName
              }}</span>
              <v-icon class="ml-1">mdi-chevron-down</v-icon>
            </span>
            <!-- <span class="text-xxs">{{
            $store.getters.userInfo && $store.getters.userInfo.roleName
          }}</span> -->
          </div>
        </div>
      </template>
      <v-list>
        <div class="pb-3 pt-2 list-avatar">
          <!-- <v-badge
          color="success"
          overlap
          offset-x="12"
          offset-y="12"
          class="ms-4"
          dot
        >
          <v-avatar
            size="40px"
            color="primary"
            class="v-avatar-light-bg primary--text"
          >
            <v-img :src="require('@/assets/images/avatars/3.png')"></v-img>
          </v-avatar>
        </v-badge> -->
          <v-avatar
            size="40"
            color="primary"
            class="v-avatar-light-bg primary--text"
          >
            <v-img :src="require('@/assets/images/avatars/3.png')"></v-img>
          </v-avatar>
          <div
            class="d-inline-flex flex-column justify-center"
            style="vertical-align: middle"
          >
            <span class="text--primary font-weight-semibold text-ml">
              {{ $store.getters.userInfo && $store.getters.userInfo.userName }}
            </span>
            <small
              class="text--disabled text-capitalize text-xxs text-no-wrap"
              v-if="$store.getters.userInfo"
            >
              {{ $store.getters.userInfo.roleName }}</small
            >
          </div>
        </div>

        <!-- <v-divider class="mb-2"></v-divider> -->

        <!-- Profile -->
        <v-list-item @click="onShowUser" class="list-item">
          <v-list-item-icon>
            <!-- <v-icon size="16">
            {{ icons.mdiAccountOutline }}
          </v-icon> -->
            <vsoc-icon
              size="x-large"
              type="fill"
              icon="icon-yonghuxinxi"
            ></vsoc-icon>
          </v-list-item-icon>
          <v-list-item-content>
            <v-list-item-title class="list-item-title text-ml">{{
              $t('global.navbar.profile')
            }}</v-list-item-title>
          </v-list-item-content>
        </v-list-item>

        <!-- <v-divider class="my-2"></v-divider> -->

        <!-- Settings -->
        <v-list-item
          v-if="
            $store.state.appConfig.isThemeAndTimeZone &&
            $store.state.appConfig.themeFlag === 'true'
          "
          class="list-item"
          @click="onShowSetting"
        >
          <v-list-item-icon>
            <!-- <v-icon size="x-large">
            {{ icons.mdiCogOutline }}
          </v-icon> -->
            <vsoc-icon
              size="x-large"
              type="fill"
              icon="icon-xitongshezhi"
            ></vsoc-icon>
          </v-list-item-icon>
          <v-list-item-content>
            <v-list-item-title class="list-item-title text-ml">{{
              $t('global.navbar.settings')
            }}</v-list-item-title>
          </v-list-item-content>
        </v-list-item>

        <!-- FAQ -->
        <v-list-item @click="downUserGuide" class="list-item">
          <v-list-item-icon>
            <!-- <v-icon size="x-large">
            {{ icons.mdiHelpCircleOutline }}
          </v-icon> -->
            <vsoc-icon
              size="x-large"
              type="fill"
              icon="icon-yonghushouce"
            ></vsoc-icon>
          </v-list-item-icon>
          <v-list-item-content>
            <v-list-item-title class="list-item-title text-ml">{{
              $t('global.navbar.manual')
            }}</v-list-item-title>
          </v-list-item-content>
        </v-list-item>

        <!-- <v-divider class="my-2"></v-divider> -->

        <!-- Logout -->
        <v-list-item @click="logoutUser" class="list-item">
          <v-list-item-icon>
            <!-- <v-icon size="x-large">
            {{ icons.mdiLogoutVariant }}
          </v-icon> -->
            <vsoc-icon
              size="x-large"
              type="fill"
              icon="icon-tuichudenglu"
            ></vsoc-icon>
          </v-list-item-icon>
          <v-list-item-content>
            <v-list-item-title class="list-item-title text-ml">{{
              $t('global.navbar.logOut')
            }}</v-list-item-title>
          </v-list-item-content>
        </v-list-item>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
import { getPropertiesList } from '@/api/system/parameter'
import { ticketDownload } from '@/api/ticket'
import store from '@/store'
import { getLocalStorage } from '@/util/localStorage'
import { removeToken } from '@/util/token'
import { useRouter } from '@core/utils'
import {
  mdiAccountOutline,
  mdiChatOutline,
  mdiCheckboxMarkedOutline,
  mdiCogOutline,
  mdiCurrencyUsd,
  mdiEmailOutline,
  mdiHelpCircleOutline,
  mdiLogoutVariant,
} from '@mdi/js'
import { getCurrentInstance, ref } from '@vue/composition-api'
import AppCustomizer from '../app-customizer/AppCustomizer.vue'
export default {
  components: {
    AppCustomizer,
  },
  setup() {
    const vm = getCurrentInstance().proxy
    const { router } = useRouter()

    // 退出系统
    const logoutUser = async () => {
      vm.$store
        .dispatch('user/logOut')
        .then(res => {
          vm.$store.dispatch('permission/clearPermission')
          vm.$store.commit('global/clearPollingST')
          removeToken()
          router.push({
            path: '/login',
            query: { redirect: vm.$route.fullPath },
          })
        })
        .catch(err => {
          vm.$notify.info('error', err)
        })

      // const res = await logout()
      // if (res.code === 200) {
      //   localStorage.removeItem('token')
      //   localStorage.removeItem('userInfo')
      //   vm.$store.commit('user/setToken', '')
      //   vm.$store.commit('user/setUserInfo', {})

      //   // 清空菜单
      //   vm.$store.commit('permission/setMenus', [])
      // }

      // // Remove userData from localStorage
      // // ? We just removed token from localStorage. If you like, you can also make API call to backend to blacklist used token
      // // localStorage.removeItem('accessToken')

      // // Remove userData & Ability from localStorage
      // // localStorage.removeItem('userData')
      // // localStorage.removeItem('userAbility')

      // // Reset ability
      // // vm.$ability.update(initialAbility)

      // // Redirect to login page
      // // router.push({ name: 'auth-login' })
      // // router.push({ name: 'login' })

      // router.push('/login')
    }

    //用户手册
    const downUserGuide = async () => {
      try {
        const res = await getPropertiesList({ propertyKey: 'User_Guide_URL' })
        if (res.data.records.length) {
          let filePath = res.data.records[0].propertyValue
          if (filePath) {
            ticketDownload(filePath).then(res => {
              let url = window.URL.createObjectURL(new Blob([res]))
              let link = document.createElement('a')
              link.style.display = 'none'
              link.href = url
              link.setAttribute('download', '用户手册_' + filePath)
              document.body.appendChild(link)
              link.click()
              document.body.removeChild(link)
              window.URL.revokeObjectURL(url)
            })
          }
        }
      } catch (e) {
        console.error(`获取参数设置错误：${e}`)
      } finally {
      }
    }
    const userInfo = ref(JSON.parse(getLocalStorage('userInfo')))

    // 用户信息
    const onShowUser = () => {
      //修复用户信息 名称跳转BUG，改为url,因为名称可能在前台发生变更
      router.push({ path: '/user/detail' }).catch(err => {
        console.log('err', err)
      })
    }

    const onShowSetting = () => {
      store.commit('global/setIsCustomizerOpen', true)
    }

    return {
      logoutUser,
      onShowUser,
      onShowSetting,
      downUserGuide,
      icons: {
        mdiAccountOutline,
        mdiEmailOutline,
        mdiCheckboxMarkedOutline,
        mdiChatOutline,
        mdiCogOutline,
        mdiCurrencyUsd,
        mdiHelpCircleOutline,
        mdiLogoutVariant,
      },
      userInfo,
    }
  },
}
</script>
<style lang="scss" scoped>
::v-deep .v-list-item__icon {
  min-width: 10px !important;
  padding-right: 0 !important;
  margin: 0 !important;
  height: 100%;
}
.user-profile-menu-content {
  .v-list-item {
    min-height: 2.5rem !important;
    // color: var(--v-color-base) !important;
  }
}

.list-avatar {
  /* Frame 137 */

  /* Auto layout */

  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 16px 24px 8px;
  gap: 8px;

  // width: 160px;
  height: 54px;

  // background: #ffffff;
  border-radius: 4px 4px 0px 0px;
}
.list-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0 24px;
  gap: 8px;
  height: 42px;
}
.v-list-item--link:hover {
  background: rgba($primary, 0.1);
  color: $primary !important;
  caret-color: $primary !important;
}
.v-list-item:hover::before {
  opacity: 0.1;
}
.list-item-title {
  // width: 48px;
  // height: 18px;
  height: 100%;
  display: flex;
  align-items: center;

  // font-size: 14px;
  // line-height: 22px !important;;
}
</style>
