<template>
  <v-card tile class="main-content">
    <v-card-text class="pa-0">
      <div class="d-flex justify-space-between align-center flex-row-reverse">
        <div class="d-flex align-end">
          <table-search
            :searchList="searchList"
            :searchQuery="query"
            @search="$_search"
          ></table-search>
          <!-- <v-select
            v-model="queryKey"
            small
            color="primary"
            :menu-props="{ auto: true, offsetY: true }"
            append-icon="mdi-chevron-down"
            hide-details
            dense
            outlined
            class="mr-3 select-width"
            :items="searchConditions"
            :label="$t('action.queryConditions')"
          ></v-select>

          <vsoc-date-range
            v-if="queryKey === 'startPublishedDate'"
            ref="dateInput"
            v-model="range"
            @input="onChangeDate"
          >
            <template v-slot:text="{ inputValue }">
              <v-text-field
                class="date-width"
                clearable
                readonly
                color="primary"
                outlined
                dense
                hide-details
                :label="$t('vulnerability.cve.headers.publishedDate')"
                append-icon="mdi-calendar-range-outline"
                :value="inputValue"
                @click:clear="onChangeDate({ start: '', end: '' })"
              />
            </template>
          </vsoc-date-range>

          <v-radio-group
            v-else-if="queryKey === 'isNotreference'"
            v-model="query[queryKey]"
            row
            small
            dense
            hide-details
            class="ml-4 mt-0 text-width"
          >
            <v-radio
              v-for="reference in referenceOption"
              :key="reference.value"
              :value="reference.value"
              :label="reference.text"
            ></v-radio>
          </v-radio-group>
          <v-text-field
            v-else
            v-model="query[queryKey]"
            hide-details
            dense
            outlined
            class="text-width"
            :label="$t('action.queryContent')"
          ></v-text-field>

          <v-btn
            color="primary--text bg-btn ml-3"
            elevation="0"
            @click="$_search"
          >
            <span>{{ $t('action.search') }}</span>
          </v-btn> -->
        </div>
      </div>

      <v-data-table
        fixed-header
        :items-per-page="query.pageSize"
        item-key="id"
        :height="tableHeight"
        hide-default-footer
        :headers="headers"
        :items="tableData"
        class="table border-radius-xl mt-3 thead-light"
        :loading="tableLoading"
        @click:row="$_viewDetails"
      >
        <template v-slot:item.affectedVendors="{ item }">
          <v-row dense>
            <v-col cols="12" class="d-flex align-center">
              <span class="mr-1"
                >{{ $t('vulnerability.cve.detail.vendor') }}:</span
              >
              <span
                v-show-tips="item.affectedVendors"
                style="max-width: 10rem"
                class="text-overflow-hide"
              >
                {{ item.affectedVendors | dataFilter }}
              </span>
            </v-col>
            <v-col cols="12" class="accent--text d-flex align-center">
              <span class="mr-1"
                >{{ $t('vulnerability.cve.detail.product') }}:</span
              >
              <span
                v-show-tips="item.affectedProducts"
                style="max-width: 10rem"
                class="text-overflow-hide"
              >
                {{ item.affectedProducts | dataFilter }}
              </span>
            </v-col>
          </v-row>
        </template>

        <template v-slot:item.metrics="{ item }">
          <v-row dense class="py-2">
            <v-col cols="12" class="d-flex align-center">
              <span class="mr-1">V3.x:</span>
              <v-chip-group v-if="item.v3Metrics.length !== 0">
                <v-chip
                  v-for="(v3, i) in item.v3Metrics"
                  :key="i + 'v3'"
                  label
                  small
                  :color="toColor(cvss3Enum, v3.baseSeverity)"
                  >{{ v3.baseScore }}&nbsp{{ v3.baseSeverity }}</v-chip
                >
              </v-chip-group>
              <span v-else>N/A</span>
            </v-col>
            <v-col cols="12" class="d-flex align-center accent--text">
              <span class="mr-1">V2.0:</span>
              <v-chip-group v-if="item.v2Metrics.length !== 0">
                <v-chip
                  v-for="(v2, i) in item.v2Metrics"
                  :key="i + 'v2'"
                  label
                  small
                  :color="toColor(cvss2Enum, v2.baseSeverity)"
                  >{{ v2.baseScore }}&nbsp{{ v2.baseSeverity }}</v-chip
                >
              </v-chip-group>
              <span v-else>N/A</span>
            </v-col>
          </v-row>
        </template>

        <template v-slot:item.descriptions="{ item }">
          <v-row dense>
            <v-col
              cols="12"
              v-if="item.descriptions && item.descriptions.length > 0"
              v-show-tips="$t('global.clickDetail')"
              style="max-width: 40rem"
              class="text-overflow-hide cursor-pointer"
            >
              {{ item.descriptions[0].value }}
            </v-col>
            <v-col cols="12" v-else>N/A</v-col>
            <v-col cols="12" class="accent--text">
              <span class="mr-1"
                >{{ $t('vulnerability.cve.headers.publishedDate') }}:</span
              >{{ item.publishedDate | toDate }}
            </v-col>
          </v-row>
        </template>

        <template v-slot:item.createUser="{ item }">
          <div>
            <div class="mb-1">{{ item.createUser | dataFilter }}</div>
            <div class="accent--text">{{ item.createTime }}</div>
          </div>
        </template>
      </v-data-table>
      <!-- 分页器 -->
      <vsoc-pagination
        :page.sync="query.pageNum"
        :size.sync="query.pageSize"
        :total="tableDataTotal"
        @change-size="$_search"
        @change-page="getTableData"
      />
    </v-card-text>
  </v-card>
</template>

<script>
import { getCveList } from '@/api/vulnerability'
import TableSearch from '@/components/TableSearch/index.vue'
import VsocPagination from '@/components/VsocPagination.vue'
import VsocDateRange from '@/components/vsoc-date-range/VsocDateRange.vue'
import { uniqBy } from 'lodash'

export default {
  name: 'CveIndex',
  props: {
    tableHeight: [String, Number],
  },
  components: {
    VsocPagination,
    VsocDateRange,
    TableSearch,
  },
  data() {
    return {
      range: {
        start: '',
        end: '',
      },

      // 查询内容
      queryKey: 'cveId',
      tableLoading: false,

      // 查询条件下拉选择
      query: {
        cveId: '',
        startPublishedDate: undefined, //公开时间开始时间
        endPublishedDate: undefined, //公开时间结束时间
        startLastupdateDate: undefined, //最后更新时间开始时间
        endLastupdateDate: undefined, //最后更新时间结束时间
        descriptions: '', //描述
        isNotreference: null, //是否包含超链接
        cweId: '', //cweID
        vendor: '', //供应商
        product: '', //产品
        metrics: '', //cvss评分
        baseSeverityList: [], //评分范围
        pageNum: 1,
        pageSize: 200,
      },

      tableData: [],
      tableDataTotal: 0,
    }
  },
  computed: {
    searchList() {
      return [
        {
          type: 'multiSearch',
          value: 'cveId',
          conditions: [
            {
              type: 'input',
              value: 'cveId',
              text: this.$t('vulnerability.headers.cveId'),
            },
            {
              type: 'input',
              value: 'descriptions',
              text: this.$t('vulnerability.cve.headers.desc'),
            },
            {
              type: 'date',
              value: ['startPublishedDate', 'endPublishedDate'],
              text: this.$t('vulnerability.cve.headers.publishedDate'),
              dateRange: {
                range: {
                  start: '',
                  end: '',
                },
                menuProps: { offsetY: true, closeOnContentClick: false },
              },
            },
            {
              type: 'radio',
              value: 'isNotreference',
              text: this.$t('vulnerability.cve.drawer.isLink'),
              itemList: this.referenceOption,
            },
          ],
        },
      ]
    },
    referenceOption() {
      return [
        {
          text: this.$t('enums.reference.yes'),
          value: 1,
        },
        {
          text: this.$t('enums.reference.no'),
          value: 0,
        },
      ]
    },
    cvss2Enum() {
      return this.$store.getters['enums/getCvss2']
    },
    cvss3Enum() {
      return this.$store.getters['enums/getCvss3']
    },
    // 查询条件列表
    searchConditions() {
      return [
        {
          text: this.$t('vulnerability.headers.cveId'),
          value: 'cveId',
        },
        {
          text: this.$t('vulnerability.cve.headers.desc'),
          value: 'descriptions',
        },
        {
          text: this.$t('vulnerability.cve.headers.publishedDate'),
          value: 'startPublishedDate',
        },
        {
          text: this.$t('vulnerability.cve.drawer.isLink'),
          value: 'isNotreference',
        },
      ]
    },
    headers() {
      return [
        {
          text: this.$t('vulnerability.headers.cveId'),
          value: 'cveId',
          width: 160,
        },
        {
          text: this.$t('vulnerability.cve.headers.desc'),
          value: 'descriptions',
          width: 360,
          sortable: false,
        },
        {
          text: this.$t('vulnerability.cve.detail.metric'),
          value: 'metrics',
          width: 150,
          sortable: false,
        },
        {
          text: this.$t('vulnerability.cve.detail.affectedRange'),
          value: 'affectedVendors',
          width: 180,
          sortable: false,
        },
        {
          text: this.$t('vulnerability.headers.entryInfo'),
          value: 'createUser',
          width: 260,
          sortable: false,
        },
      ]
    },
  },
  // mounted() {
  //   this.$_search()
  // },
  methods: {
    onReset() {
      Object.assign(this.$data.query, this.$options.data.call(this).query)
      this.$_search()
    },
    // 评分色
    toColor(list, text) {
      // 忽略大小写
      return list.find(v => new RegExp(text, 'i').test(v.text))?.color
    },
    $_viewDetails(record) {
      if (this.$route.meta.buttons.includes('cve-detail')) {
        this.$router.push(`/bugManagement/cve-details?id=${record.id}`)
      }
    },
    onChangeDate(range) {
      this.range = range
      this.query.startPublishedDate = range.start
      this.query.endPublishedDate = range.end
    },

    $_search() {
      this.query.pageNum = 1
      this.getTableData()
      this.$emit('filter', this.query)
    },

    // 获取表格
    async getTableData() {
      try {
        this.tableLoading = true
        const { data } = await getCveList(this.query)
        this.tableData = data.records.map(item => {
          let metricObj = this.handleVersionGroup(item.metrics)
          item.descriptions.forEach(desc => {
            let bool = /^(n\/a)$/.test(desc.value)
            if (bool) {
              desc.value = desc.value.toUpperCase()
            }
          })
          return {
            ...item,
            affectedProducts: item.affected
              ?.map(v => {
                let bool = /^(n\/a)$/.test(v.product)
                return bool ? v.product.toUpperCase() : v.product
              })
              .join(','),
            affectedVendors: item.affected
              ?.map(v => {
                let bool = /^(n\/a)$/.test(v.vendor)
                return bool ? v.vendor.toUpperCase() : v.vendor
              })
              .join(','),
            // 根据评分等级去重
            v3Metrics: metricObj.v3 && uniqBy(metricObj.v3, 'baseSeverity'),
            v2Metrics: metricObj.v2 && uniqBy(metricObj.v2, 'baseSeverity'),
          }
        })
        this.tableDataTotal = data.total
        this.$forceUpdate()
      } catch (e) {
        console.error(`获取漏洞管理：${e}`)
      }
      this.tableLoading = false
    },
    handleVersionGroup(arr) {
      // metrics: []

      // CVE-2017-11427: [
      //   {
      //     "cvssV3_0": {
      //       "baseSeverity": "HIGH",
      //       "confidentialityImpact": "HIGH",
      //       "attackComplexity": "LOW",
      //       "scope": "CHANGED",
      //       "attackVector": "NETWORK",
      //       "availabilityImpact": "NONE",
      //       "integrityImpact": "NONE",
      //       "baseScore": 7.7,
      //       "privilegesRequired": "LOW",
      //       "vectorString": "CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:C/C:H/I:N/A:N",
      //       "userInteraction": "NONE",
      //       "version": "3.0"
      //     }
      //   }
      // ]

      // CVE-2023-32040: [
      //   {
      //     "format": "CVSS",
      //     "scenarios": [
      //       {
      //         "lang": "en-US",
      //         "value": "GENERAL"
      //       }
      //     ],
      //     "cvssV3_1": {
      //       "baseSeverity": "HIGH",
      //       "baseScore": 5.5,
      //       "vectorString": "CVSS:3.1/AV:L/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N/E:U/RL:O/RC:C",
      //       "version": "3.1"
      //     }
      //   }
      // ]

      // CVE-2023-5063: {
      //     "exploitabilityScore": 3.1,
      //     "cvssV3": {
      //         "baseSeverity": "MEDIUM",
      //         "confidentialityImpact": "LOW",
      //         "attackComplexity": "LOW",
      //         "scope": "CHANGED",
      //         "attackVector": "NETWORK",
      //         "availabilityImpact": "NONE",
      //         "integrityImpact": "LOW",
      //         "privilegesRequired": "LOW",
      //         "baseScore": 6.4,
      //         "vectorString": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:C/C:L/I:L/A:N",
      //         "version": "3.1",
      //         "userInteraction": "NONE"
      //     },
      //     "impactScore": 2.7
      // }
      let newArr = []
      if (arr && !Array.isArray(arr)) {
        let tempKeys = Object.keys(arr).filter(v => v.startsWith('cvssV'))
        if (tempKeys && tempKeys.length) {
          tempKeys.forEach(key => {
            newArr.push(arr[key])
          })
        }
      } else {
        arr &&
          arr.length &&
          arr.forEach(item => {
            let tempKeys = Object.keys(item).filter(v => v.startsWith('cvssV'))
            if (tempKeys && tempKeys.length) {
              tempKeys.forEach(key => {
                newArr.push(arr[key])
              })
            }
          })
      }
      if (newArr.length === 0) {
        return {
          v3: [],
          v2: [],
        }
      }
      let obj = {
        v3: newArr.filter(v => Number(v.version) >= 3.0),
        v2: newArr.filter(v => Number(v.version) < 3.0),
      }
      return obj
    },
  },
}
</script>
<style scoped lang="scss">
.v-chip.v-size--x-small {
  display: inline-block;
  width: 3.6667rem;
  height: 1.8333rem;
  padding: 0;
  margin-right: 4px;
  text-align: center;
}
</style>
