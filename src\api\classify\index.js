import { request, vsocPath } from '../../util/request'

// 分类分页
export const getAlarmTypes = function (data) {
  return request({
    url: `${vsocPath}/classifyManage/queryPage`,
    method: 'post',
    data,
  })
}
// 获取分类
export const getAllClassify = function (data) {
  return request({
    url: `${vsocPath}/classifyManage/classifies`,
    method: 'post',
    data,
  })
}
// 新增分类
export const addAlarmType = function (data) {
  return request({
    url: `${vsocPath}/classifyManage/addClassify`,
    method: 'post',
    data,
  })
}

// 编辑分类
export const updateAlarmType = function (data) {
  return request({
    url: `${vsocPath}/classifyManage/updateClassify`,
    method: 'post',
    data,
  })
}

//删除分类
export const deleteAlarmType = function (data) {
  return request({
    url: `${vsocPath}/classifyManage/deleteClassify`,
    method: 'post',
    data,
  })
}

//查询分类类型
export const classifyType = function (data) {
  return request({
    url: `${vsocPath}/classifyManage/classifyType`,
    method: 'post',
    data,
  })
}
export const updateActive = function (data) {
  return request({
    url: `${vsocPath}/classifyManage/updateClassify`,
    method: 'post',
    data,
  })
}

export const getClassifySuperiorNullData = function (data) {
  return request({
    url: `${vsocPath}/classifyManage/classifySuperiorNullData`,
    method: 'post',
    data,
  })
}
