import { login, logout } from '@/api/login/login'
import { userInfo } from '@/api/system/user'
import router, { resetRouter } from '@/router'
import store from '@/store'
import {
  clearLocalStorage,
  removeLocalStorage,
  setLocalStorage,
} from '@/util/localStorage'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import {
  removeApiKey,
  removeToken,
  setApiKey,
  setToken,
} from '../../util/token'
export default {
  namespaced: true,
  name: 'user',
  state: {
    // 用户信息
    userInfo: {
      userName: '',
    },
    token: '', // token值
  },
  actions: {
    // 用户登录
    logIn({ commit, dispatch }, formData) {
      return new Promise((resolve, reject) => {
        login(formData)
          .then(res => {
            if (res.success) {
              NProgress.start()
              // localStorage.setItem('token', res.data.token)
              setToken(res.data.token)
              setApiKey(res.data.apiKey)
              // localStorage.setItem('apiKey', res.data.apiKey)
              commit('setToken', res.data.token)
              dispatch('getUserInfo')
              resolve(res)
            } else {
              throw new Error(res.msg)
            }
          })
          .catch(err => {
            reject(err)
          })
      })
    },

    // 用户退出
    logOut({ commit }) {
      return new Promise((resolve, reject) => {
        logout()
          .then(res => {
            if (res.code === 200) {
              // 用户退出成功操作：
              //   清空token
              //   重置为静态路由
              //   重置菜单
              //   清空缓存
              //   清空vuex
              // localStorage.removeItem('token')
              removeToken()
              removeApiKey()
              removeLocalStorage('userInfo')
              removeLocalStorage('messageList')
              // localStorage.removeItem('apiKey')
              commit('setToken', '')
              commit('setUserInfo', {})

              // 清空菜单信息
              // dispatch('clearPermission')

              // 重置路由
              resetRouter()

              store.commit('appConfig/UPDATE_GLOBAL_THEME_MODE', '')
              store.commit('appConfig/UPDATE_APP_PRIMARY', '')
              window.sessionStorage.removeItem(
                process.env.BASE_URL + '_params',
              ),
                clearLocalStorage()
              // localStorage.clear()

              // 删除Vuex
              // window.location.reload()
              resolve(res)
            } else {
              throw new Error()
            }
          })
          .catch(err => {
            reject(err)
          })
      })
    },

    // 获取用户信息
    getUserInfo({ commit, dispatch }) {
      return new Promise((resolve, reject) => {
        userInfo()
          .then(res => {
            if (res.code === 200) {
              // 单角色
              res.data.roleId = res.data?.roles.map(r => r.roleId).join(',')
              res.data.roleName = res.data?.roles.map(r => r.roleName).join(',')
              commit('setUserInfo', res.data)
              setLocalStorage('userInfo', JSON.stringify(res.data))
              // localStorage.setItem(
              //   process.env.BASE_URL + 'userInfo',
              //   JSON.stringify(res.data),
              // )
              const vm = router.app
              router.push(vm.$route.query.redirect || '/')

              resolve(res)
            } else {
              throw new Error(res.msg)
            }
          })
          .catch(err => {
            dispatch('logOut')
            router.push('/login')
            reject(err)
          })
      })
    },
  },
  mutations: {
    setToken(state, val) {
      state.token = val
    },
    setUserInfo(state, val) {
      state.userInfo = val
    },
  },
}
