<template>
  <!-- <transition name="fade">
    <v-btn
      v-show="backTopShow"
      class="goTop"
      color="primary"
      fab
      dark
      elevation="6"
      @click="$vuetify.goTo(0, 'duration')"
    >
      <v-icon>mdi-chevron-up</v-icon>
    </v-btn>
  </transition> -->
  <v-fab-transition>
    <v-btn
      v-show="isScroll"
      color="primary"
      fab
      large
      bottom
      right
      fixed
      elevation="3"
      @click="onScroll"
    >
      <v-icon> mdi-chevron-up</v-icon>
    </v-btn>
  </v-fab-transition>
</template>

<script>
export default {
  data() {
    return {
      backTopShow: false,
    }
  },
  // mounted() {
  //   window.addEventListener('scroll', this.handleScroll)
  // },
  computed: {
    isScroll() {
      return this.$store.state.global.isScroll
    },
  },
  methods: {
    handleScroll() {
      if (document.documentElement.scrollTop + document.body.scrollTop > 90) {
        this.backTopShow = true
      } else {
        this.backTopShow = false
      }
    },
    onScroll() {
      const ele = document.querySelector('#app-content-container')
      // this.$vuetify.goTo(0, 'duration')
      this.$vuetify.goTo('#app-content-container', { container: ele })
    },
  },
}
</script>

<style lang="scss" scoped>
// .goTop {
//   position: fixed;
//   z-index: 6;
//   right: 20px;
//   bottom: 20px;
// }
// .fade-enter-active,
// .fade-leave-active {
//   transition: opacity 0.5s;
// }
// .fade-enter,
// .fade-leave-to {
//   opacity: 0;
// }
</style>
