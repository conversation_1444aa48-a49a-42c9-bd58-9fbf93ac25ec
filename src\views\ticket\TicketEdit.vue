<!-- 工单管理新增、编辑 -->
<template>
  <edit-page
    class="ticket-box"
    :ticketId="ticketInfo.ticketId"
    :popsName="
      editType === 'add'
        ? $t('ticket.btn.add')
        : editType === 'edit'
        ? $t('ticket.btn.edit')
        : $t('ticket.btn.detail')
    "
  >
    <div v-if="editType !== 'detail'" slot="BreadBtn">
      <div v-if="editType === 'add'">
        <v-btn color="primary" elevation="0" @click="confirmEdit('add', '10')">
          {{ $t('action.save') }}
        </v-btn>
      </div>
      <div v-else>
        <template class="d-flex align-center" v-for="(item, index) in btnList">
          <v-btn
            v-if="item.dictId === '5' && jumpClose()"
            :key="index"
            class="ml-3"
            elevation="0"
            color="primary"
            @click="
              confirmEdit(ticketAction[item.dictId].dictName, item.dictId)
            "
          >
            {{
              ticketAction[item.dictId]
                ? ticketAction[item.dictId].dictName
                : item.dictName
            }}
          </v-btn>
          <v-btn
            v-if="
              (item.dictId !== '5' && item.dictId !== '6') ||
              (item.dictId === '6' && oldAssignedTo)
            "
            :key="index"
            :color="
              ['0', '3', '4', '6'].indexOf(item.dictId) !== -1
                ? 'bg-btn'
                : 'primary'
            "
            elevation="0"
            @click="
              confirmEdit(ticketAction[item.dictId].dictName, item.dictId)
            "
            class="ml-3"
            v-has="item.code"
          >
            {{
              ticketAction[item.dictId]
                ? ticketAction[item.dictId].dictName
                : item.dictName
            }}
          </v-btn>
          <!-- <v-btn
            :key="index"
            :color="
              ['6', '3', '4', '0'].indexOf(item.dictId) !== -1
                ? 'bg-btn'
                : 'primary'
            "
            elevation="0"
            @click="
              confirmEdit(ticketAction[item.dictId].dictName, item.dictId)
            "
            class="ml-3"
            v-has="item.code"
          >
            {{
              ticketAction[item.dictId]
                ? ticketAction[item.dictId].dictName
                : item.dictName
            }}
          </v-btn> -->
        </template>
        <v-btn
          v-if="
            editFlag && ticketInfo.status !== '4' && ticketInfo.status !== '5'
          "
          elevation="0"
          class="ml-3 bg-btn"
          @click="confirmEdit($t('action.update'), '9')"
        >
          {{ $t('action.update') }}
        </v-btn>
      </div>
    </div>
    <div class="ticket-center-box">
      <v-form ref="form" v-model="valid">
        <div class="d-flex align-center justify-space-between">
          <div
            class="ticket-title mt-6 font-weight-semibold-light mb-4 d-flex align-center"
          >
            <div>{{ $t('global.drawer.baseInfo') }}</div>
          </div>
          <div
            v-if="editType === 'edit' && ticketInfo.ticketId"
            class="pr-9 d-flex align-center cursor-pointer export-btn"
            @click="exportTicket"
            v-has:Export
          >
            <vsoc-icon
              type="fill"
              icon="icon-tiaozhuan"
              size="x-large"
              class="action-btn"
            ></vsoc-icon>
            <span class="ml-2 export-text">
              {{ $t('action.export') }}
            </span>
          </div>
        </div>
        <div class="px-9">
          <el-upload
            style="height: 0"
            ref="refUpload"
            :action="vsocPath + '/file/ticketUpload'"
            :headers="headers"
            :on-success="handleSuccess"
            :on-change="handleChangeFile"
            :on-error="handleError"
            :on-exceed="handleExceed"
            :file-list="fileList"
            :limit="12"
            multiple
            :show-file-list="false"
          >
          </el-upload>
          <v-row>
            <v-col class="mr-6">
              <v-text-field
                v-model="ticketInfo.ticketType"
                color="primary"
                disabled
                :label="$t('ticket.headers.ticketType')"
              ></v-text-field>
            </v-col>
            <v-col class="mr-6">
              <v-text-field
                v-model="ticketInfo.createUserName"
                color="primary"
                disabled
                :label="$t('ticket.headers.createUser')"
              ></v-text-field>
            </v-col>
            <v-col>
              <v-text-field
                v-model="ticketInfo.createDate"
                color="primary"
                disabled
                :label="$t('ticket.headers.createDate')"
                append-icon="mdi-calendar-range-outline"
              ></v-text-field>
            </v-col>
          </v-row>
          <v-row>
            <v-col class="mr-6">
              <v-select
                v-model="ticketInfo.status"
                :items="ticketList"
                disabled
                :label="$t('ticket.headers.status')"
              >
                <template #prepend-inner>
                  <v-icon
                    :style="{
                      color: ticketStatus[ticketInfo.status]
                        ? ticketStatus[ticketInfo.status].color
                        : '',
                    }"
                    size="14"
                    class="mt-1"
                  >
                    mdi-circle-medium
                  </v-icon>
                </template>
              </v-select>
            </v-col>
            <v-col class="mr-6">
              <v-autocomplete
                v-model="ticketInfo.dataSource"
                :menu-props="{ offsetY: true, maxHeight: 300 }"
                append-icon="mdi-chevron-down"
                :items="dataSourceData"
                item-text="text"
                item-value="value"
                :label="$t('ticket.headers.dataSourceName')"
                @change="changeSource"
                clearable
                :disabled="editType === 'detail'"
              >
              </v-autocomplete>
            </v-col>
            <v-col class="d-flex align-center">
              <v-autocomplete
                v-if="ticketInfo.dataSource === '2'"
                v-model="ticketInfo.relationId"
                :menu-props="{ offsetY: true, maxHeight: 300 }"
                append-icon="mdi-chevron-down"
                :items="dataCenterList"
                :label="$t('ticket.headers.relationId')"
                clearable
                :loading="isLoading"
                :search-input.sync="search"
                no-filter
                :disabled="!ticketInfo.dataSource || editType === 'detail'"
              >
                <template #item="{ item }">
                  <div>{{ item.id }}-{{ item.name }}</div>
                </template>
              </v-autocomplete>
              <v-text-field
                v-else
                v-model="ticketInfo.relationId"
                color="primary"
                :label="$t('ticket.headers.relationId')"
                :disabled="!ticketInfo.dataSource || editType === 'detail'"
              ></v-text-field>
              <v-btn
                v-if="
                  editType !== 'add' &&
                  (isJumpVul ||
                    (ticketInfo.dataSource === '2' && ticketInfo.relationId))
                "
                icon
                @click="checkDetail"
              >
                <vsoc-icon
                  v-show-tips="$t('action.preview')"
                  type="fill"
                  icon="icon-tiaozhuan"
                  class="action-btn primary--text"
                  size="x-large"
                ></vsoc-icon>
              </v-btn>
            </v-col>
          </v-row>
          <v-row class="row-3">
            <v-col class="mr-6">
              <v-autocomplete
                v-model="ticketInfo.classify"
                :menu-props="{ offsetY: true, maxHeight: 300 }"
                append-icon="mdi-chevron-down"
                :items="ticketClassify"
                item-text="name"
                item-value="id"
                item-disabled="disabled"
                :label="$t('ticket.headers.classifyName')"
                clearable
                :disabled="editType === 'detail'"
              >
              </v-autocomplete>
            </v-col>
            <v-col class="mr-6">
              <el-tree
                :labelText="$t('ticket.headers.assignedGroupName')"
                ref="elTree"
                :organizationFlag="true"
                :outlinedFlag="false"
                :showCheckbox="true"
                :expandFlag="true"
                :treeData="groupList"
                :default-props="defaultProps"
                :bottom="true"
                :isDense="false"
                @change="changeTree"
                :ruleFlag="true"
                :clearable="false"
                :disabled="editType === 'detail'"
              />
            </v-col>
            <v-col>
              <v-autocomplete
                v-model="ticketInfo.assignedTo"
                :menu-props="{ offsetY: true, maxHeight: 300 }"
                append-icon="mdi-chevron-down"
                :items="allUserList"
                item-text="userName"
                item-value="userId"
                :label="$t('ticket.headers.assignedToName')"
                :disabled="!userShow || editType === 'detail'"
                clearable
              >
              </v-autocomplete>
            </v-col>
          </v-row>
          <v-row class="row-4">
            <v-col class="mr-6">
              <v-select
                v-model="ticketInfo.priority"
                :items="levelList"
                :label="$t('ticket.headers.priority')"
                :menu-props="{ offsetY: true }"
                :disabled="editType === 'detail'"
              >
                <template #prepend-inner>
                  <v-icon
                    :style="{
                      color: ticketLevel[ticketInfo.priority]
                        ? ticketLevel[ticketInfo.priority].color
                        : '',
                    }"
                    size="14"
                    class="mt-1"
                  >
                    mdi-alert-circle
                  </v-icon>
                </template>
                <template v-slot:item="{ item }">
                  <div class="d-flex align-center">
                    <v-icon
                      :style="{
                        color: ticketLevel[item.value]
                          ? ticketLevel[item.value].color
                          : '',
                      }"
                      size="14"
                      class="mr-3"
                    >
                      mdi-alert-circle
                    </v-icon>
                    <span class="text-base color-base">
                      {{ item.text }}
                    </span>
                  </div>
                </template>
              </v-select>
            </v-col>
            <v-col class="mr-6">
              <v-select
                v-model="ticketInfo.impact"
                :items="impactList"
                :label="$t('ticket.headers.impact')"
                item-text="text"
                item-value="value"
                :menu-props="{ offsetY: true }"
                clearable
                :disabled="editType === 'detail'"
              >
              </v-select>
            </v-col>
            <v-col class="time-box">
              <v-text-field
                v-model="ticketInfo.eta1"
                ref="refTime"
                :label="$t('ticket.headers.eta')"
                append-icon="mdi-calendar-range-outline"
                :rules="etaRules"
                :disabled="editType === 'detail'"
              ></v-text-field>
              <el-date-picker
                popper-class="elDatePicker"
                :picker-options="options"
                default-time="18:00:00"
                v-model="ticketInfo.eta"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder=""
                @focus="handleEta"
                :disabled="editType === 'detail'"
              >
              </el-date-picker>
            </v-col>
          </v-row>
          <v-row class="d-flex align-end">
            <div style="width: 31%" class="mr-6">
              <v-autocomplete
                v-model="ticketInfo.affectModelList"
                multiple
                :menu-props="{ offsetY: true, maxHeight: 300 }"
                append-icon="mdi-chevron-down"
                :items="modelList"
                :label="$t('ticket.headers.models')"
                clearable
                :disabled="editType === 'detail'"
              >
                <template v-slot:selection="{ index }">
                  <span v-if="index === 0">
                    {{ $t('global.pagination.selected') }}：{{
                      ticketInfo.affectModelList.length
                    }}
                  </span>
                </template>
              </v-autocomplete>
            </div>

            <div class="flex-1">
              <v-autocomplete
                v-model="ticketInfo.watchPeopleList"
                multiple
                :menu-props="{ offsetY: true, maxHeight: 300 }"
                append-icon="mdi-chevron-down"
                :items="watchUserList"
                :label="$t('ticket.headers.WatchList')"
                item-value="userId"
                item-text="userName"
                :disabled="!editFlag || !isEditWatch"
                chips
              >
                <template v-slot:selection="{ item, index }">
                  <v-chip
                    :disabled="!editFlag || !isEditWatch"
                    v-if="index <= 6"
                    color="primary"
                    :close="!item.disabled"
                    small
                    @click:close="ticketInfo.watchPeopleList.splice(index, 1)"
                  >
                    {{ item.userName }}
                  </v-chip>
                  <span v-if="index === 6" class="grey--text text-caption">
                    (+{{ ticketInfo.watchPeopleList.length - 6 }})
                  </span>
                </template>
              </v-autocomplete>
            </div>
          </v-row>
          <v-row class="title-box">
            <v-text-field
              v-model="ticketInfo.title"
              color="primary"
              :label="$t('ticket.headers.name')"
              :rules="nameRules"
              :disabled="!editFlag"
            ></v-text-field>
            <div v-if="editType === 'edit'">
              <v-btn
                v-has:ticket-edit
                v-if="editFlag"
                icon
                @click.stop="editFlag = false"
              >
                <vsoc-icon
                  v-show-tips="$t('action.cancel')"
                  type="fill"
                  icon="icon-guanbi"
                  class="primary--text"
                  size="x-large"
                ></vsoc-icon>
              </v-btn>
              <v-btn v-else icon @click.stop="editFlag = true">
                <vsoc-icon
                  v-show-tips="$t('ticket.hint.updateTips')"
                  type="fill"
                  icon="icon-bianji"
                  class="primary--text"
                  size="x-large"
                ></vsoc-icon>
              </v-btn>
            </div>
          </v-row>
          <v-row class="v-text-field pt-0 mt-2">
            <div class="content-box v-label mb-2">
              {{ $t('ticket.headers.content') }}
            </div>
            <quill-editor
              ref="myQuillEditor"
              v-model="ticketInfo.ticketContent"
              :options="editorOption"
              class="editor-box editor w-100"
              :disabled="!editFlag"
            >
            </quill-editor>
          </v-row>

          <v-row
            v-if="editType === 'edit' || editType === 'detail'"
            class="d-block v-text-field pt-0 mt-6"
          >
            <div class="content-box v-label">
              {{ $t('ticket.hint.attachments') }} ({{ oldAttachments.length }})
            </div>
            <div
              v-if="oldAttachments.length"
              class="d-flex justify-space-between"
            >
              <div
                ref="refUploadBox"
                class="d-flex flex-wrap"
                :style="{
                  height: showUpload ? 'auto' : '68px',
                  overflow: showUpload ? 'visible' : 'hidden',
                }"
              >
                <v-chip
                  v-for="(item, index) in oldAttachments"
                  :key="index"
                  label
                  class="text-ml active-chip active-chip-box mt-3 mr-6"
                >
                  <div class="d-flex align-center justify-space-between">
                    <div style="max-width: 400px" v-show-tips>
                      <i
                        style="color: #06309b"
                        class="iconfont icon-fujianicon mr-3 text-xl"
                      ></i>
                      <span class="text-ml">{{ item.name }}</span>
                    </div>

                    <div class="ml-4">
                      <span class="file-size">
                        {{ Math.ceil(item.response.data.fileSize / 1024) }}KB
                      </span>
                      <i
                        class="iconfont icon-xiazai text-xl ml-3 cursor-pointer"
                        @click.stop="downLoad(item)"
                      ></i>
                    </div>
                  </div>
                </v-chip>
                <v-chip
                  v-if="isUploadFlag && showUpload"
                  label
                  class="active-chip active-chip-box cursor-pointer mt-3 px-2 py-0"
                  @click="showUpload = false"
                >
                  <i
                    style="font-size: 20px"
                    class="iconfont icon-shouqi"
                    color="#686E7C"
                  ></i>
                </v-chip>
              </div>

              <v-chip
                v-if="isUploadFlag && !showUpload"
                label
                class="active-chip active-chip-box cursor-pointer mt-3"
                style="padding: 0 14px"
                @click="showUpload = true"
              >
                <i
                  style="font-size: 24px"
                  class="iconfont icon-gengduo ml-n2"
                  color="#686E7C"
                ></i>
              </v-chip>
            </div>
          </v-row>
        </div>

        <div v-if="editType === 'add'">
          <div
            class="ticket-title mt-6 font-weight-semibold-light mb-4 d-flex align-center"
          >
            <span class="mr-6"> {{ $t('ticket.hint.attachments') }} </span>
            <v-btn
              width="34px"
              min-width="34px"
              elevation="0"
              @click="handleFile"
              class="upload-btn primary--text bg-btn"
            >
              <v-icon dense class="text-xl">mdi-paperclip</v-icon>
            </v-btn>
          </div>
          <div class="upload-note px-9 d-flex align-center">
            <v-icon dense class="text-xl" color="#A1A6B1">mdi-paperclip</v-icon>
            <span class="ml-2">
              {{ $t('ticket.hint.attachmentsTip', ['12', '20M']) }}： 'jpg',
              'jpeg', 'bmp', 'png', 'gif', 'txt', 'rar', 'zip', 'doc', 'docx',
              'xlsx', 'xls', 'pdf', 'ppt', 'mp4'；
            </span>
          </div>
          <div v-if="attachments.length" class="chip-file px-9">
            <v-chip
              v-for="(item, index) in attachments"
              :key="index"
              label
              class="mt-3 text-ml active-chip w-100"
            >
              <div class="w-100 d-flex align-center justify-space-between">
                <div class="w-90" v-show-tips>
                  <i
                    style="color: #06309b"
                    class="iconfont icon-fujianicon mr-3 text-xl"
                  ></i>
                  <span class="text-ml">{{ item.name }}</span>
                </div>

                <div>
                  <span class="file-size">
                    {{ Math.ceil(item.response.data.fileSize / 1024) }}KB
                  </span>
                  <i
                    class="iconfont icon-shanchu text-xl ml-3"
                    @click.stop="handleRemove(item, index)"
                  ></i>
                </div>
              </div>
            </v-chip>
          </div>
        </div>

        <div v-else>
          <div class="mt-6" v-if="editType !== 'detail'">
            <div class="d-flex align-center mb-3 pr-9">
              <v-tabs
                v-model="tab"
                height="56"
                class="remark-box title font-weight-semibold-light"
              >
                <template v-for="item in remarkList">
                  <v-tab
                    :key="item.value"
                    :tab-value="item.value"
                    class="h-100 mr-6"
                    @click="changeTab(item)"
                  >
                    {{ $t(item.text) }}
                  </v-tab>
                </template>
              </v-tabs>
              <div class="d-flex align-center">
                <vsoc-icon
                  type="fill"
                  icon="icon-xiangqing2"
                  class="action-btn"
                  size="16px"
                ></vsoc-icon>
                <span class="ml-2 upload-note">{{
                  $t('ticket.remarkHint')
                }}</span>
              </div>
            </div>
            <v-tabs-items class="px-9" v-model="tab">
              <v-tab-item
                v-for="item in remarkList"
                :key="item.value"
                :value="item.value"
              >
                <template v-if="item.value === '0'">
                  <v-row>
                    <v-col class="mr-6">
                      <v-autocomplete
                        v-model="ticketInfo.vulnerabilityAssessment"
                        :menu-props="{ offsetY: true, maxHeight: 300 }"
                        append-icon="mdi-chevron-down"
                        :items="vulnerabilityAssessmentList"
                        item-text="text"
                        item-value="value"
                        :label="
                          $t('ticket.dashboardVulner.vulnerabilityAssessment')
                        "
                        :disabled="editType === 'detail'"
                        @change="changeVulnerabilityAssessment"
                      >
                      </v-autocomplete>
                    </v-col>
                    <v-col class="mr-6">
                      <v-autocomplete
                        v-model="ticketInfo.attackCharacter"
                        :menu-props="{ offsetY: true, maxHeight: 300 }"
                        append-icon="mdi-chevron-down"
                        :items="attackCharacterList"
                        item-text="text"
                        item-value="value"
                        :label="$t('ticket.dashboardVulner.attackCharacter')"
                        :disabled="editType === 'detail'"
                      >
                      </v-autocomplete>
                    </v-col>
                    <v-col>
                      <v-autocomplete
                        v-model="ticketInfo.vulnerabilityAttackVector"
                        :menu-props="{ offsetY: true, maxHeight: 300 }"
                        append-icon="mdi-chevron-down"
                        :items="vulnerabilityAttackVectorList"
                        item-text="name"
                        item-value="id"
                        item-disabled="disabled"
                        :label="$t('ticket.dashboardVulner.attackVector')"
                        clearable
                        :disabled="editType === 'detail'"
                      >
                      </v-autocomplete>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col class="mr-6">
                      <v-autocomplete
                        v-model="ticketInfo.vulnerabilityImpactScope"
                        :menu-props="{ offsetY: true, maxHeight: 300 }"
                        append-icon="mdi-chevron-down"
                        :items="vulnerabilityImpactScopeList"
                        item-text="name"
                        item-value="id"
                        item-disabled="disabled"
                        :label="$t('ticket.dashboardVulner.importProport')"
                        clearable
                        :disabled="editType === 'detail'"
                      >
                      </v-autocomplete>
                    </v-col>
                    <v-col class="mr-6">
                      <v-autocomplete
                        v-model="ticketInfo.accessType"
                        :menu-props="{ offsetY: true, maxHeight: 300 }"
                        append-icon="mdi-chevron-down"
                        :items="accessTypeList"
                        item-text="text"
                        item-value="value"
                        :label="$t('ticket.dashboardVulner.accessType')"
                        :disabled="editType === 'detail'"
                      >
                        <template #prepend-inner>
                          <vsoc-icon
                            type="fill"
                            :icon="
                              ticketInfo.accessType === '0'
                                ? 'icon-yuanchengfangwen'
                                : 'icon-wulifangwen'
                            "
                            class="action-btn mt-1"
                            size="14"
                          ></vsoc-icon>
                        </template>
                        <template v-slot:item="{ item }">
                          <div class="d-flex align-center">
                            <vsoc-icon
                              type="fill"
                              :icon="
                                item.value === '0'
                                  ? 'icon-yuanchengfangwen'
                                  : 'icon-wulifangwen'
                              "
                              class="action-btn"
                              size="14"
                            ></vsoc-icon>
                            <span class="ml-3 text-base color-base">
                              {{ item.text }}
                            </span>
                          </div>
                        </template>
                      </v-autocomplete>
                    </v-col>
                    <v-col>
                      <v-autocomplete
                        v-model="ticketInfo.attackDistance"
                        :menu-props="{ offsetY: true, maxHeight: 300 }"
                        append-icon="mdi-chevron-down"
                        :items="attackDistanceList"
                        item-text="text"
                        item-value="value"
                        :label="$t('ticket.dashboardVulner.attackDistance')"
                        :disabled="editType === 'detail'"
                      >
                        <template #prepend-inner>
                          <vsoc-icon
                            type="fill"
                            :icon="
                              ticketInfo.attackDistance === '0'
                                ? 'icon-yuanchenggongji'
                                : 'icon-duanchenggongji'
                            "
                            class="action-btn mt-1"
                            size="14"
                          ></vsoc-icon>
                        </template>
                        <template v-slot:item="{ item }">
                          <div class="d-flex align-center">
                            <vsoc-icon
                              type="fill"
                              :icon="
                                item.value === '0'
                                  ? 'icon-yuanchenggongji'
                                  : 'icon-duanchenggongji'
                              "
                              class="action-btn"
                              size="14"
                            ></vsoc-icon>
                            <span class="ml-3 text-base color-base">
                              {{ item.text }}
                            </span>
                          </div>
                        </template>
                      </v-autocomplete>
                    </v-col>
                  </v-row>
                </template>
                <template v-if="item.value === '1'">
                  <quill-editor
                    ref="myQuillEditor1"
                    v-model="ticketInfo.remark"
                    :options="editorOption"
                    class="editor-box editor1"
                  >
                  </quill-editor>
                  <div
                    class="editor-bottom py-4 px-4 d-flex align-center justify-end"
                  >
                    <v-btn
                      width="34px"
                      min-width="34px"
                      elevation="0"
                      @click="handleFile"
                      class="upload-btn primary--text bg-btn mr-3"
                    >
                      <v-icon dense class="text-xl">mdi-paperclip</v-icon>
                    </v-btn>
                    <v-btn
                      class="reply-btn primary--text bg-btn mr-3"
                      width="64"
                      min-width="64"
                      elevation="0"
                      @click="updateRemark"
                      v-has:Reply
                      :disabled="!ticketInfo.remark"
                    >
                      <span> {{ $t('action.reply') }} </span>
                    </v-btn>
                    <v-btn
                      class="reply-btn"
                      width="64"
                      min-width="64"
                      outlined
                      depressed
                      @click="ticketInfo.remark = ''"
                    >
                      <span> {{ $t('action.clear') }} </span>
                    </v-btn>
                  </div>
                  <div class="upload-note mt-2">
                    <v-icon dense class="text-xl" color="#A1A6B1"
                      >mdi-paperclip</v-icon
                    >
                    <span class="ml-2">
                      {{ $t('ticket.hint.attachmentsTip', ['12', '20M']) }}：
                      'jpg', 'jpeg', 'bmp', 'png', 'gif', 'txt', 'rar', 'zip',
                      'doc', 'docx', 'xlsx', 'xls', 'pdf', 'ppt', 'mp4'；
                    </span>
                  </div>
                </template>
                <template v-if="item.value === '2'">
                  <quill-editor
                    ref="myQuillEditor2"
                    v-model="ticketInfo.processingPlan"
                    :options="editorOption"
                    class="editor-box editor1"
                  >
                  </quill-editor>
                  <div
                    class="editor-bottom py-4 px-4 d-flex align-center justify-end"
                  >
                    <v-btn
                      width="34px"
                      min-width="34px"
                      elevation="0"
                      @click="handleFile"
                      class="upload-btn primary--text bg-btn mr-3"
                    >
                      <v-icon dense class="text-xl">mdi-paperclip</v-icon>
                    </v-btn>
                    <v-btn
                      class="primary--text bg-btn mr-3"
                      min-width="64"
                      elevation="0"
                      @click="updateRemark"
                      v-has:Reply
                      :disabled="!ticketInfo.processingPlan"
                    >
                      <span> {{ $t('action.summarize') }} </span>
                    </v-btn>
                    <v-btn
                      class="reply-btn"
                      width="64"
                      min-width="64"
                      outlined
                      depressed
                      @click="ticketInfo.processingPlan = ''"
                    >
                      <span> {{ $t('action.clear') }} </span>
                    </v-btn>
                  </div>
                  <div class="upload-note mt-2">
                    <v-icon dense class="text-xl" color="#A1A6B1"
                      >mdi-paperclip</v-icon
                    >
                    <span class="ml-2">
                      {{ $t('ticket.hint.attachmentsTip', ['12', '20M']) }}：
                      'jpg', 'jpeg', 'bmp', 'png', 'gif', 'txt', 'rar', 'zip',
                      'doc', 'docx', 'xlsx', 'xls', 'pdf', 'ppt', 'mp4'；
                    </span>
                  </div>
                </template>
              </v-tab-item>
            </v-tabs-items>

            <div v-if="attachments.length" class="chip-file px-9">
              <v-chip
                v-for="(item, index) in attachments"
                :key="index"
                label
                class="mt-3 text-ml active-chip w-100"
              >
                <div class="w-100 d-flex align-center justify-space-between">
                  <div class="w-90" v-show-tips>
                    <i
                      style="color: #06309b"
                      class="iconfont icon-fujianicon mr-3 text-xl"
                    ></i>
                    <span class="text-ml">{{ item.name }}</span>
                  </div>

                  <div>
                    <span class="file-size">
                      {{ Math.ceil(item.response.data.fileSize / 1024) }}KB
                    </span>
                    <i
                      class="iconfont icon-shanchu text-xl ml-3"
                      @click.stop="handleRemove(item, index)"
                    ></i>
                  </div>
                </div>
              </v-chip>
            </div>
          </div>

          <div
            class="d-flex align-center justify-space-between ticket-title mt-6 font-weight-semibold-light pr-9"
          >
            <div>
              {{ $t('ticket.hint.tracks') }}（{{ operationHistories.length }}）
            </div>
            <div style="width: 220px">
              <v-select
                v-model="selectedOperationTypeList"
                multiple
                :menu-props="{ offsetY: true }"
                append-icon="mdi-chevron-down"
                :items="operationList"
                :label="$t('ticket.headers.Operation')"
                hide-details
                dense
                outlined
                @change="searchOperaList"
              >
                <template v-slot:selection="{ index }">
                  <span v-if="index === 0">
                    {{ $t('global.pagination.selected') }}：{{
                      selectedOperationTypeList.length
                    }}
                  </span>
                </template>
                <template v-slot:prepend-item>
                  <v-list-item
                    class="pl-5"
                    ripple
                    @mousedown.prevent
                    @click="toggle"
                  >
                    <v-list-item-action>
                      <v-icon
                        :color="
                          selectedOperationTypeList.length > 0 ? 'primary' : ''
                        "
                      >
                        {{ icon }}
                      </v-icon>
                    </v-list-item-action>
                    <v-list-item-content class="ml-n2">
                      <v-list-item-title> 全选 </v-list-item-title>
                    </v-list-item-content>
                  </v-list-item>
                  <v-divider class="mt-1"></v-divider>
                </template>
              </v-select>
            </div>
          </div>
          <dv-loading class="mt-14" v-if="operaLoading">
            Loading...
          </dv-loading>
          <div v-else>
            <div v-if="operationHistories.length" class="px-9">
              <div
                v-for="(item, index) in operationHistories"
                :key="index"
                :class="[
                  'active-box',
                  'px-6',
                  'pt-4',
                  'mt-7',
                  item.isOverHeight ? 'pb-8' : 'pb-4',
                ]"
              >
                <div
                  :ref="'operationHistories' + index"
                  class="d-flex justify-space-between align-center"
                >
                  <div class="d-flex align-center">
                    <v-avatar color="primary" size="36px">
                      <span
                        :class="
                          store.state.appConfig.appPrimary === 'lotus'
                            ? ''
                            : 'white--text'
                        "
                        >{{ item.operationUserName | firstAvatar }}</span
                      >
                    </v-avatar>
                    <span class="mx-3 op-name"
                      >{{ item.operationUserName }}
                    </span>
                  </div>
                  <div class="d-flex align-center">
                    <span class="primary--text">{{ item.operationType }}</span>
                    <v-icon size="18px" color="#A1A6B1"
                      >mdi-circle-small</v-icon
                    >
                    <span>{{ item.operationDate }}</span>
                  </div>
                </div>
                <div
                  class="mt-3 show-more-false"
                  :style="{
                    maxHeight: item.isShowMore ? 'fit-content' : '300px',
                  }"
                  :id="'contentOption-box' + index"
                >
                  <div>
                    <div v-if="item.content">
                      <quill-editor
                        v-model="item.content"
                        :options="editorOption3"
                        class="editor2"
                        disabled
                      >
                      </quill-editor>
                    </div>
                    <div v-if="item.historyAttachment.length">
                      <div class="history-box text-root mt-3">
                        {{ $t('ticket.hint.attachments') }}：
                      </div>
                      <v-chip
                        v-for="(item, index) in item.historyAttachment"
                        :key="index"
                        label
                        class="text-ml active-chip active-chip-box mt-2 mr-6"
                      >
                        <div class="d-flex align-center justify-space-between">
                          <div style="max-width: 400px" v-show-tips>
                            <i
                              style="color: #06309b"
                              class="iconfont icon-fujianicon mr-3 text-xl"
                            ></i>
                            <span class="text-ml">{{ item.originalName }}</span>
                          </div>

                          <div class="ml-4">
                            <span class="file-size">
                              {{ Math.ceil(item.fileSize / 1024) }}KB
                            </span>
                            <i
                              class="iconfont icon-xiazai text-xl ml-3 cursor-pointer"
                              @click.stop="downLoad(item, 2)"
                            ></i>
                          </div>
                        </div>
                      </v-chip>
                    </div>
                  </div>
                </div>
                <v-chip
                  v-if="item.isOverHeight"
                  label
                  class="more-chip-box active-chip cursor-pointer"
                  @click="showMore(item)"
                >
                  <vsoc-icon
                    type="fill"
                    :icon="item.isShowMore ? 'icon-shouqi' : 'icon-zhankai'"
                    size="16px"
                  ></vsoc-icon>
                </v-chip>
              </div>
            </div>
            <div
              v-else
              class="mt-14 d-flex flex-column align-center justify-center"
            >
              <v-img
                contain
                max-width="150px"
                src="@/assets/images/chart-empty.svg"
              ></v-img>
              <div class="mt-8 upload-note">
                {{ $t('global.noData') }}
              </div>
            </div>
          </div>
        </div>
      </v-form>
    </div>
  </edit-page>
</template>
<script>
import editPage from '@/components/EditPage.vue'
import elTree from '@/components/search-tree/index'

import { getAlerts } from '@/api/alert'
// import { getVulnerabilityList } from '@/api/analyse'
// import { getList } from '@/api/data-center'
import { getAllClassify } from '@/api/classify/index'
import { querySysGroupList } from '@/api/system/organization'
import {
  addTicket,
  editTicket,
  exportBatch,
  getTicketById,
  makdwnImg,
  ticketDownload,
  ticketOperationType,
  updateRemark,
} from '@/api/ticket'

import { max, required } from '@/@core/utils/validation'
import { findAllUsers } from '@/api/system/user'
import DatePicker from '@/components/VsocDateRange.vue'
import { getLocalStorage } from '@/util/localStorage'
import { dealImg, requestImg } from '@/util/requestImage'
import { deepClone } from '@/util/utils'
import { addDays, format } from 'date-fns'
import 'quill/dist/quill.bubble.css'
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import { quillEditor } from 'vue-quill-editor'
import { vsocPath } from '../../util/request'
//引入Qill插件
import store from '@/store'
import Quill from 'quill'
let BlockEmbed = Quill.imports['blots/embed']
// 自定义img标签
class ImageBlot extends BlockEmbed {
  static create(value) {
    let node = super.create()
    node.setAttribute('src', value.src)
    node.setAttribute('authsrc', value.authsrc)
    return node
  }
  static value(node) {
    return {
      src: node.getAttribute('src'),
      authsrc: node.getAttribute('authsrc'),
    }
  }
}
ImageBlot.blotName = 'image'
ImageBlot.tagName = 'img'
Quill.register(ImageBlot)
export default {
  name: 'TicketEdit',
  components: {
    quillEditor,
    editPage,
    elTree,
    DatePicker,
  },
  data() {
    return {
      isJumpVul: false,
      options: {
        // 时间不能大于当前时间
        disabledDate: time => {
          return time.getTime() < new Date().getTime() - 24 * 60 * 60 * 1000
        },
      },
      time: '',
      isEditWatch: true,
      isUploadFlag: false,
      showUpload: true,
      editFlag: true,
      ruleFlag: false,
      userShow: false,
      fileList: [],
      allUserList: [],
      groupList: [],
      dataCenterList: [],
      alertList: [],
      defaultProps: {
        children: 'children',
        label: 'name',
      },
      // dataSourceData: [],
      tab: null,
      remarkList: [
        { value: '1', text: 'ticket.hint.follow' },
        { value: '2', text: 'ticket.hint.solution' },
        // { value: '0', text: 'ticket.hint.analy' },
      ],

      vsocPath: vsocPath,
      editType: 'add',
      btnList: [],
      editorOption: {
        placeholder: '',
        modules: {
          toolbar: false,
        },
      },
      editorOption3: {
        placeholder: '',
        modules: {
          toolbar: false,
        },
      },
      modelList: [],
      ticketInfo: {
        ticketId: '',
        ticketType: 'Incident',
        title: '',
        ticketContent: '',
        createDate: '',
        createUser: '',
        createUserName: '',
        status: '0', //0待处理 1处理中 2已处理 3已驳回 4已取消 5已关闭
        priority: '3', //P0-Critical (严重)、P1-High (高)、P2-Moderate (中)、P3-Low (低)
        classify: '',
        assignedTo: '',
        assignedGroup: null,
        remark: '',
        processingPlan: '',
        relationId: '',
        dataSource: '',
        impact: '',
        affectModelList: [],
        eta: '',
        eta1: '',
        watchPeopleList: [],
        vulnerabilityAttackVector: '',
        vulnerabilityImpactScope: '',
        accessType: '',
        attackDistance: '',
        attackCharacter: '',
        vulnerabilityAssessment: '',
      },
      //附件
      attachments: [],
      oldAttachments: [],
      //轨迹
      operationHistories: [],
      selectedOperationTypeList: [],
      nameRules: [
        v => required(v, this.$t('ticket.headers.name')),
        v => max(v, 200),
      ],
      etaRules: [v => required(v, this.$t('ticket.headers.eta'))],
      ticketClassify: [],
      vulnerabilityAttackVectorList: [],
      vulnerabilityImpactScopeList: [],
      userList: [],
      watchUserList: [],
      permissionBtn: [],
      // ticketList: [],
      // levelList: [],
      valid: true,
      isLoading: false,
      operaLoading: false,
      search: null,
      oldAssignedTo: '',
      oldTitle: '',
      oldTicketContent: '',
      userInfo: JSON.parse(getLocalStorage('userInfo')),
      operationList: [],
      isPaste: false,
    }
  },

  computed: {
    vulnerabilityAssessmentList() {
      return Object.assign(
        [],
        this.$store.state.enums.enums['Vulnerability Assessment'],
      )
    },
    store() {
      return store
    },
    searchAll() {
      return this.selectedOperationTypeList.length === this.operationList.length
    },
    searchSome() {
      return this.selectedOperationTypeList.length > 0 && !this.searchAll
    },
    icon() {
      if (this.searchAll) return 'mdi-checkbox-marked'
      if (this.searchSome) return 'mdi-minus-box'
      return 'mdi-checkbox-blank-outline'
    },
    headers() {
      return this.$store.getters['global/getFileHeaders']
    },
    impactList() {
      return this.$store.getters['enums/getTicketImpact']
    },
    ticketLevel() {
      return this.$store.state.enums.enums.TicketLevel
    },
    ticketStatus() {
      return this.$store.state.enums.enums.TicketStatus
    },
    ticketAction() {
      return this.$store.state.enums.enums.TicketAction
    },
    ticketList() {
      return this.$store.getters['enums/getTicketStatus']
    },
    levelList() {
      return this.$store.getters['enums/getTicketLevel']
    },
    dataSourceData() {
      return this.$store.getters['enums/getTicketDataSource']
    },
    accessTypeList() {
      return this.$store.getters['enums/getAccessType']
    },
    attackDistanceList() {
      return this.$store.getters['enums/getAttackDistance']
    },
    attackCharacterList() {
      return this.$store.getters['enums/getAttackCharacter']
    },
  },
  created() {
    if (this.$route.query.id) {
      if (this.$route.params.mode === 'detail') {
        this.editType = 'detail'
      } else {
        this.editType = 'edit'
      }
      this.editFlag = false
      this.isPaste = false
      this.getInfo(1)
    } else {
      this.editType = 'add'
      this.editFlag = true
      this.$nextTick(() => {
        this.ticketInfo.createDate = format(new Date(), 'yyyy-MM-dd HH:mm:ss')
        this.ticketInfo.createUser = this.userInfo.userId
        this.ticketInfo.createUserName = this.userInfo.userName
        this.ticketInfo.watchPeopleList = [this.ticketInfo.createUser]
        this.init()
      })
    }
  },
  watch: {
    operationHistories: {
      handler() {
        this.onResize()
      },
      deep: true,
    },
    search(val) {
      if (this.ticketInfo.dataSource === '2') {
        this.getAlertList(val)
      }
    },
    'ticketInfo.relationId'(val) {
      if (
        this.ticketInfo.dataSource === '1' &&
        val &&
        (val.substring(0, 3) === 'CVE' || val.substring(0, 3) === 'INT')
      ) {
        this.isJumpVul = true
      } else {
        this.isJumpVul = false
      }
    },
    'ticketInfo.eta'(val) {
      this.ticketInfo.eta1 = val
        ? format(new Date(val), 'yyyy-MM-dd HH:mm:ss')
        : ''
    },
    'ticketInfo.priority'(val) {
      if (this.editType === 'add') {
        if (val === '0') {
          this.ticketInfo.eta = format(
            addDays(new Date(), 3),
            'yyyy-MM-dd 18:00:00',
          )
        } else {
          this.ticketInfo.eta = ''
        }
      }
    },
  },
  mounted() {
    //  自定义粘贴图片功能
    let quill = this.$refs.myQuillEditor.quill
    this.$forceUpdate()
    this.pasteImg(quill)
  },
  beforeDestroy() {
    this.$bus.$off('resize', this.onResize)
  },
  methods: {
    jumpClose() {
      // 管理员和创建人有关单权限即可
      let isClose = false
      const userInfo = JSON.parse(getLocalStorage('userInfo') || '')
      if (userInfo) {
        if (
          (userInfo.roleId &&
            userInfo.roleId.toLowerCase().includes('admin')) ||
          userInfo.userId === this.ticketInfo.createUser
        ) {
          isClose = true
        }
      }
      return isClose
    },
    checkDetail() {
      if (this.ticketInfo.dataSource === '1') {
        const source =
          this.ticketInfo.relationId.substring(0, 3) === 'CVE'
            ? 'NVD'
            : 'INTERNAL'
        this.$router.push(
          `/analyse/vulnerabilityDetail/index?vulnId=${this.ticketInfo.relationId}&source=${source}`,
        )
      } else {
        if (this.dataCenterList.length) {
          const params = this.dataCenterList[0]
          this.$router.push(`/alert/detail?id=${this.ticketInfo.relationId}`)
          // this.$router.push(
          //   `/alert/detail?id=${this.ticketInfo.relationId}&vehicleId=${params.vehicleId}&name=${params.name}`,
          // )
        }
      }
    },
    onResize() {
      setTimeout(() => {
        this.operationHistories.forEach((v, index) => {
          let div = document.querySelector(`#contentOption-box${index} div`)
          v.isOverHeight = div && div.offsetHeight > 300 ? true : false
        })
      }, 500)
    },
    pasteImg(quill) {
      if (!quill) return
      quill.root.addEventListener(
        'paste',
        evt => {
          if (
            evt.clipboardData &&
            evt.clipboardData.files &&
            evt.clipboardData.files.length
          ) {
            evt.preventDefault()
            ;[].forEach.call(evt.clipboardData.files, file => {
              if (!file.type.match(/^image\/(gif|jpe?g|a?png|bmp)/i)) {
                return
              }
              const formData = new FormData()
              formData.append('file', file)
              makdwnImg(formData)
                .then(async res => {
                  if (res.code == 200) {
                    let length = quill.getSelection().index //光标位置
                    let src =
                      this.vsocPath +
                      '/attachment/seeFile?fileId=' +
                      res.data.filePath
                    requestImg(src).then(blob => {
                      quill.insertEmbed(length, 'image', {
                        src: blob,
                        authsrc: src,
                      })
                      // 调整光标到最后
                      quill.setSelection(length + 1) //光标后移一位
                    })
                  }
                })
                .catch(err => {
                  console.error(err)
                })
            })
          }
        },
        false,
      )
    },
    showMore(item) {
      item.isShowMore = !item.isShowMore
      this.operationHistories = [...this.operationHistories]
    },
    toggle() {
      this.$nextTick(() => {
        if (this.searchAll) {
          this.selectedOperationTypeList = []
        } else {
          this.selectedOperationTypeList = this.operationList
        }
        this.searchOperaList()
      })
    },
    handleEta() {
      this.$refs['refTime'].focus()
    },
    dealEditorOption(type) {
      return this.$i18n.locale === 'en' ? this.editorOption : this.editorOption1
    },
    //富文本光标
    getPosition() {
      let quill = this.$refs['myQuillEditor'].quill
      //禁用编辑器，防止页面自动滚动到编辑器位置
      quill.enable(false)
      // 编辑器绑定内容，如修改的时候原有的内容
      quill.pasteHTML(this.ticketInfo.ticketContent)
      this.$nextTick(function () {
        //丢掉编辑器焦点并重新启用编辑器
        quill.blur()
        quill.enable(true)
      })
    },
    changeTab(item) {
      //  自定义粘贴图片功能
      if (item.value === '1') return
      setTimeout(() => {
        if (this.isPaste) return
        let quill =
          this.$refs['myQuillEditor2'] && this.$refs['myQuillEditor2'][0].quill
        this.$forceUpdate()
        this.pasteImg(quill)
        this.isPaste = true
      })
    },
    //改变来源
    changeSource(val) {
      this.ticketInfo.relationId = ''
      this.dataCenterList = []
      this.search = null
      // if (this.editType === 'edit' && val === '1') {
      //   this.$notify.info(
      //     'warning',
      //     this.$t('validation.required', [this.$t('ticket.hint.analy')]),
      //   )
      // }
      if (val === '2') {
        this.getAlertList()
      }
    },
    changeVulnerabilityAssessment(val) {
      if (val === '1') {
        this.$notify.info(
          'warning',
          this.$t('validation.required', [this.$t('ticket.hint.analy')]),
        )
      }
    },
    //获取车型
    async getAllVehicle() {
      const data = await this.$store.dispatch('global/loadAllAutomaker', {
        is_supervise: '0',
      })
      this.modelList = data || []
    },
    init() {
      Promise.all([
        this.fetchAllClassify(),
        this.getUserGroup(),
        this.getAllUser(),
        this.getAllVehicle(),
      ]).then(e => {
        if (Number(this.$route.query.type) === 1) {
          const params = JSON.parse(getLocalStorage('alertTicket'))

          let modelInfo = {}
          const modelName = params.affectModelList
          if (
            modelName != undefined &&
            modelName != null &&
            modelName.trim() !== ''
          ) {
            modelInfo = this.modelList.find(
              item => item.name === params.affectModelList,
            )
          }
          for (const key of Object.keys(params)) {
            // 特别处理 affectModelList 字段
            if (key === 'affectModelList') {
              // 尝试将字符串解析为数组，如果失败则保持原样或设置为空数组
              try {
                this.ticketInfo[key] = [modelInfo.id]
              } catch (error) {
                console.warn(
                  'Failed to parse affectModelList as an array:',
                  error,
                )
                this.ticketInfo[key] = [] // 或者保持原样：this.ticketInfo[key] = params[key];
              }
            } else {
              // 直接赋值其他字段
              this.ticketInfo[key] = params[key]
            }
          }

          if (this.ticketInfo.dataSource === '2') {
            this.getAlertList(this.ticketInfo.relationId)
          }
        }
        this.dealWatchUser()
      })
    },

    async searchOperaList() {
      const params = {
        ticketId: this.$route.query.id,
        operationTypeList: this.selectedOperationTypeList,
      }
      const res = await getTicketById(params)
      this.dealOperation(res.data)
    },
    //获取所有操作类型
    async getOperaList() {
      const res = await ticketOperationType()
      this.operationList = res.data || []
      this.selectedOperationTypeList = [
        '重开',
        '指派',
        '完结',
        '驳回',
        '取消',
        '关单',
        '催单',
        '回复',
        '更新',
        '新建',
        '总结',
        '挂起',
        '恢复',
      ]
    },
    //获取详情
    async getInfo(type) {
      await this.getOperaList()
      await this.fetchAllClassify()
      await this.getUserGroup()
      await this.getAllUser()
      await this.getAllVehicle()
      const res = await getTicketById({
        ticketId: this.$route.query.id,
        operationTypeList: this.selectedOperationTypeList,
      })
      this.dealInfoData(res.data)

      const btnList = await this.$store.dispatch('global/getTicketBtn')
      this.$nextTick(() => {
        const routerList = this.$router.getRoutes()
        this.permissionBtn =
          routerList.find(v => v.meta.id === this.$route.meta.parentId)?.meta
            .buttons || []
        if (
          this.ticketInfo.createUser === this.userInfo.userId ||
          this.permissionBtn.indexOf('update-watch') !== -1
        ) {
          this.isEditWatch = true
        } else {
          this.isEditWatch = false
        }
        this.btnList = btnList.find(
          v => v.status === this.ticketInfo.status,
        ).button
        // 调整按钮生成顺序
        let obj = ''
        for (const i in this.btnList) {
          let item = this.btnList[i]
          if (item.dictId === '13' || item.dictId === '14') {
            obj = item
            this.btnList.splice(i, 1)
            break
          }
        }
        // 如果有恢复\挂起按钮对象则添加至数组第一位
        if (obj !== '') this.btnList.unshift(obj)

        this.dealWatchUser()
        if (type === 1) {
          let quill =
            this.$refs['myQuillEditor1'] &&
            this.$refs['myQuillEditor1'][0].quill
          this.$forceUpdate()
          this.pasteImg(quill)
        }
      })
    },
    //处理关注人
    dealWatchUser() {
      let watchUserList = deepClone(this.userList)
      watchUserList.forEach((item, index) => {
        if (item.userId == this.ticketInfo.createUser) {
          item.disabled = true
          watchUserList.unshift(watchUserList.splice(index, 1)[0])
        } else {
          item.disabled = false
        }
      })
      this.watchUserList = watchUserList

      let watchPeopleList = deepClone(this.ticketInfo.watchPeopleList)
      watchPeopleList.forEach((item, index) => {
        if (item === this.ticketInfo.createUser) {
          watchPeopleList.unshift(watchPeopleList.splice(index, 1)[0])
        }
      })
      this.ticketInfo.watchPeopleList = watchPeopleList
    },
    //处理详情数据
    async dealInfoData(list) {
      this.showUpload = true
      this.attachments = []
      // if (list.ticket.classify) {
      //   list.ticket.classify = Number(list.ticket.classify)
      // }
      this.ticketInfo = deepClone(list.ticket)
      // if (!this.ticketInfo.vulnerabilityAssessment) {
      //   this.ticketInfo.vulnerabilityAssessment = '0'
      // }
      this.$set(this.ticketInfo, 'eta1', this.ticketInfo.eta)
      // this.ticketInfo.eta = ''
      // this.ticketInfo.eta1 = list.ticket.eta
      this.oldAttachments = list.attachments || []
      this.oldAttachments = this.oldAttachments.map(v => {
        return {
          ...v,
          name: v.originalName,
          response: {
            data: {
              filePath: v.generateName,
              fileSize: v.fileSize,
            },
            code: 200,
          },
        }
      })
      this.$nextTick(() => {
        this.$refs['refUpload'].clearFiles()
        this.isUploadFlag =
          this.$refs['refUploadBox'] &&
          this.$refs['refUploadBox'].offsetHeight > 68
            ? true
            : false
        this.showUpload = false
      })
      // this.operationHistories = list.operationHistories || []
      // if (this.operationHistories.length) {
      //   this.operationHistories.forEach(v => {
      //     v.content = this.dealImg(v.content)
      //     v.OperationUserName = v.OperationUserName || ''
      //     v.OpertionUser = v.OpertionUser || ''
      //   })
      // }
      if (this.ticketInfo.dataSource === '2') {
        this.ticketInfo.relationId = Number(this.ticketInfo.relationId)
        await this.getAlertList(this.ticketInfo.relationId)
      }
      // if (this.ticketInfo.relationId) {
      //   this.ticketInfo.relationId = Number(this.ticketInfo.relationId)
      // }

      if (this.ticketInfo.assignedGroup) {
        const data = {
          id: this.ticketInfo.assignedGroup,
          name: this.ticketInfo.assignedGroupName,
        }
        this.changeTree(data, {
          checkedKeys: [this.ticketInfo.assignedGroup],
        })
      } else {
        this.changeTree()
      }
      this.ticketInfo.assignedTo = list.ticket.assignedTo || ''
      this.ticketInfo.ticketContent = await dealImg(list.ticketDetail)

      this.oldAssignedTo = deepClone(this.ticketInfo.assignedTo)
      this.oldTitle = deepClone(this.ticketInfo.title)
      this.dealOperation(list)

      this.ticketInfo.processingPlan = await dealImg(
        this.ticketInfo.processingPlan || '',
      )
      this.ticketInfo.remark = await dealImg(this.ticketInfo.remark || '')

      this.$nextTick(() => {
        const quill = this.$refs['myQuillEditor'].quill
        this.oldTicketContent =
          quill.container.querySelector('.ql-editor').innerHTML
      })
    },

    dealImg(content) {
      if (!content) return ''
      let imgReg = RegExp(/<img [^>]*src=['"]([^'"]+)[^>]*>/gi) //定义正则，筛选出img元素
      let matchres = content.match(imgReg)
      if (matchres && matchres.length) {
        matchres.forEach((item, index) => {
          let _tempStr = item.slice(0, item.length - 2)

          let _index = _tempStr.indexOf('/')

          let _str =
            _tempStr.substring(0, _index) +
            this.vsocPath +
            _tempStr.substring(_index, _tempStr.length) +
            '"/>'
          content = content.replace(item, _str)
        })
      }
      return content
    },
    changeTree(data, e) {
      if (!this.$refs['elTree']) return
      if (e && e.checkedKeys.length) {
        this.$refs['elTree'].$refs.tree.setCheckedKeys([data.id])
        this.$refs['elTree'].filterText = data.name
        this.ticketInfo.assignedGroup = data.id
        this.ticketInfo.assignedTo = ''
        this.allUserList = this.userList.filter(
          v => (v.departments.length && v.departments[0].id) === data.id,
        )
        this.userShow = true
      } else {
        this.$refs['elTree'].$refs.tree.setCheckedKeys([])
        this.$refs['elTree'].filterText = ''
        this.ticketInfo.assignedGroup = ''
        this.ticketInfo.assignedTo = ''
        this.userShow = false
      }
    },
    async getAlertList(val) {
      const params = {
        pageNum: 1,
        pageSize: 10,
        queryField: val || '',
      }
      try {
        this.isLoading = true
        const data = await getAlerts(params)
        this.dataCenterList = data.data.records.map(v => {
          return {
            ...v,
            text: String(v.id),
            value: v.id,
          }
        })
      } catch (e) {
        console.error(`获取告警数据：${e}`)
      } finally {
        this.isLoading = false
      }
    },
    // async getDataCenter(val) {
    //   const params = {
    //     pageNum: 1,
    //     pageSize: 50,
    //     queryField: val || '',
    //   }
    //   try {
    //     this.isLoading = true
    //     const data = await getList(params)
    //     this.dataCenterList = data.data.records.map(v => {
    //       return {
    //         ...v,
    //         text: String(v.id),
    //         value: v.id,
    //         name: v.dataName,
    //       }
    //     })
    //   } catch (e) {
    //     console.error(`获取数据中心数据：${e}`)
    //   } finally {
    //     this.isLoading = false
    //   }
    // },

    handleFile() {
      this.$nextTick(() => {
        this.$refs['refUpload'].$refs['upload-inner'].handleClick()
      })
    },
    handleChangeFile(file, fileList) {
      if (!file) return
      const testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
      const acceptTypes = [
        'jpg',
        'jpeg',
        'bmp',
        'png',
        'gif',
        'txt',
        'rar',
        'zip',
        'doc',
        'docx',
        'xlsx',
        'xls',
        'pdf',
        'ppt',
        'mp4',
      ]
      if (acceptTypes.indexOf(testmsg) === -1) {
        this.$refs.refUpload.clearFiles()
        this.$notify.info('info', this.$t('ticket.hint.upLoad'))
        return false
      }
      const isLt2M = file.size / 1024 / 1024 < 20
      if (!isLt2M) {
        this.$refs.refUpload.clearFiles()
        this.$notify.info('info', this.$t('ticket.hint.upLoad1', ['20M']))
        return false
      }
    },
    handleRemove(item, index) {
      this.$nextTick(() => {
        this.attachments.splice(index, 1)
        this.$refs.refUpload.uploadFiles.forEach((v, index) => {
          if (item.name === v.name && item.uid === v.uid) {
            this.$refs.refUpload.uploadFiles.splice(index, 1)
          }
        })
      })
    },
    handleSuccess(response, file, fileList) {
      if (response.code === 500) {
        return this.$notify.info('error', response.msg)
      }
      if (file.response && file.response.code === 200) {
        this.attachments.push(file)
      }
    },
    handleError() {
      this.$notify.info('error', this.$t('ticket.hint.upLoad2'))
    },
    handleExceed() {
      this.$notify.info('error', this.$t('ticket.hint.upLoad3', ['12']))
    },
    async downLoad(file, type) {
      const filePath =
        type === 2 ? file.generateName : file.response.data.filePath
      const name = type === 2 ? file.originalName : file.name
      ticketDownload(filePath).then(res => {
        let url = window.URL.createObjectURL(new Blob([res]))
        let link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', name)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      })
    },
    //获取组织架构
    async getUserGroup() {
      const res = await querySysGroupList({ state: '0' })
      this.groupList = res.data || []
    },
    //获取用户
    async getAllUser() {
      const res = await findAllUsers({ status: '1' })
      this.userList = res.data || []
    },
    //获取分类
    async fetchAllClassify() {
      const res = await getAllClassify({ type: '' })
      this.ticketClassify = res.data.filter(v => v.type === '1') || []
      this.ticketClassify = this.ticketClassify.map(v => {
        return {
          ...v,
          disabled: v.status === '1' ? true : false,
        }
      })

      this.vulnerabilityAttackVectorList =
        res.data.filter(v => v.type === '5') || []
      this.vulnerabilityAttackVectorList =
        this.vulnerabilityAttackVectorList.map(v => {
          return {
            ...v,
            disabled: v.status === '1' ? true : false,
          }
        })

      this.vulnerabilityImpactScopeList =
        res.data.filter(v => v.type === '6') || []
      this.vulnerabilityImpactScopeList = this.vulnerabilityImpactScopeList.map(
        v => {
          return {
            ...v,
            disabled: v.status === '1' ? true : false,
          }
        },
      )
    },
    //获取数据来源
    // async getTicketDataSource() {
    //   const res = await ticketDataSource()
    //   this.dataSourceData = res.data || []
    // },
    //更新备注
    updateRemark() {
      const params = {
        ticketId: this.ticketInfo.ticketId,
      }
      if (Number(this.tab) === 1) {
        params.remark = this.ticketInfo.remark
        if (params.remark.length > 60000) {
          return this.$notify.info('error', this.$t('ticket.remarkHint1'))
        }
      }
      if (Number(this.tab) === 2) {
        params.processingPlan = this.ticketInfo.processingPlan
        if (params.processingPlan.length > 60000) {
          return this.$notify.info('error', this.$t('ticket.remarkHint1'))
        }
      }
      if (this.attachments.length) {
        params.attachments = this.attachments.map(v => {
          return {
            originalName: v.name,
            generateName: v.response.data.filePath,
          }
        })
      }
      updateRemark(params).then(async res => {
        if (res.code === 200) {
          // this.getInfo()
          const res = await getTicketById({
            ticketId: this.$route.query.id,
            operationTypeList: this.selectedOperationTypeList,
          })
          let data = res.data
          this.dealOperation(data)
          this.ticketInfo.remark = ''
          this.ticketInfo.processingPlan = await dealImg(
            data.ticket.processingPlan,
          )
        }
      })
    },
    async dealOperation(list) {
      this.operaLoading = true
      for (let i = 0; i < list.operationHistories.length; i++) {
        let v = list.operationHistories[i]
        v.operationUserName = v.operationUserName || ''
        v.operationUser = v.operationUser || ''
        v.isShowMore = false
        v.isOverHeight = false
        if (v.content) {
          v.content = await dealImg(v.content)
        }
      }
      this.operationHistories = list.operationHistories || []
      this.operaLoading = false
    },
    // 导出
    exportTicket() {
      exportBatch({ ticketId: this.ticketInfo.ticketId }).then(res => {
        let url = window.URL.createObjectURL(new Blob([res]))
        let link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', this.ticketInfo.ticketId + '.xls')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      })
    },
    async confirmEdit(name, actionType) {
      if (!this.$refs.form.validate()) return
      if (!this.ticketInfo.ticketContent) {
        this.$nextTick(() => {
          let isError = document.getElementsByClassName('content-box')
          isError[0].scrollIntoView({
            block: 'start',
            behavior: 'smooth',
          })
        })
        this.$notify.info(
          'error',
          this.$t('validation.required', [this.$t('ticket.headers.content')]),
        )
        return
      }
      // 0重新打开 1指派 2完成 3驳回 4取消 5关闭 6催单 9更新 10新建
      // 新建/重开/指派
      if (['0', '1', '10'].includes(actionType)) {
        if (new Date(this.ticketInfo.eta).getTime() < new Date().getTime()) {
          this.$nextTick(() => {
            let isError = document.getElementsByClassName('row-4')
            isError[0].scrollIntoView({
              block: 'start',
              behavior: 'smooth',
            })
          })
          this.$notify.info('error', this.$t('ticket.hint.timeTip'))
          return
        }
      }
      if (this.attachments.length > 12) {
        this.$nextTick(() => {
          let isError = document.getElementsByClassName('attach-box')
          isError[0].scrollIntoView({
            block: 'start',
            behavior: 'smooth',
          })
        })
        this.$notify.info('error', this.$t('ticket.hint.upLoad3', ['12']))
        return
      }
      try {
        const params = deepClone(this.ticketInfo)
        if (this.attachments.length) {
          params.attachments = this.attachments.map(v => {
            return {
              originalName: v.name,
              generateName: v.response.data.filePath,
            }
          })
        }
        if (this.editType === 'add') {
          delete params.createDate
          const res = await addTicket(params)
          if (res.code === 200) {
            this.$notify.info(
              'success',
              this.$t('global.hint.add', [this.$t('ticket.currentTitle')]) +
                ' ' +
                this.$t('ticket.headers.ticketId') +
                '：' +
                res.data +
                '!',
            )
            this.$router.push('/ticket/lists')
          }
        } else {
          let resObj = ''
          if (actionType === '2' && params.relationId) {
            if (params.dataSource === '1') {
              // const res = await getVulnerabilityList({
              //   pageNum: 1,
              //   pageSize: 1,
              //   searchText: params.relationId,
              // })
              // if (res.data.body && res.data.body.length) {
              //   resObj = res.data.body[0]
              // } else {
              //   return this.$notify.info(
              //     'error',
              //     this.$t('ticket.completeVulTip1'),
              //   )
              // }
            } else if (params.dataSource === '2') {
              const res = await getAlerts({ alarmId: params.relationId })
              if (res.data && res.data.records.length) {
                resObj = res.data.records[0]
              } else {
                return this.$notify.info(
                  'error',
                  this.$t('ticket.completeAlertTip1'),
                )
              }
            }
          }
          //0重新打开 1指派 2完成 3驳回 4取消 5关闭 6催单 9更新
          if (
            actionType === '1' ||
            actionType === '2' ||
            actionType === '3' ||
            actionType === '5' ||
            actionType === '6'
          ) {
            if (!this.ticketInfo.assignedGroup) {
              return this.$notify.info(
                'error',
                this.$t('validation.required', [
                  this.$t('ticket.headers.assignedGroupName'),
                ]),
              )
            }
            if (!this.ticketInfo.assignedTo) {
              this.$nextTick(() => {
                let isError = document.getElementsByClassName('row-3')
                isError[0].scrollIntoView({
                  block: 'start',
                  behavior: 'smooth',
                })
              })
              this.$notify.info(
                'error',
                this.$t('validation.required', [
                  this.$t('ticket.headers.assignedToName'),
                ]),
              )
              return
            }
          }
          if (actionType === '1' && this.oldAssignedTo === params.assignedTo) {
            return this.$notify.info(
              'error',
              this.$t('ticket.hint.comfirmInfo'),
            )
          }
          if (!this.ticketInfo.remark) {
            this.$nextTick(() => {
              let isError = document.getElementsByClassName('remark-box')
              isError[0].scrollIntoView({
                block: 'start',
                behavior: 'smooth',
              })
            })
            this.$notify.info(
              'error',
              this.$t('validation.required', [this.$t('ticket.hint.follow')]),
            )
            return
          }
          if (
            (this.ticketInfo.remark && this.ticketInfo.remark.length > 60000) ||
            (this.ticketInfo.processingPlan &&
              this.ticketInfo.processingPlan.length > 60000)
          ) {
            this.$nextTick(() => {
              let isError = document.getElementsByClassName('remark-box')
              isError[0].scrollIntoView({
                block: 'start',
                behavior: 'smooth',
              })
            })
            return this.$notify.info('error', this.$t('ticket.remarkHint1'))
          }

          if (actionType === '9') {
            if (this.oldAssignedTo && !params.assignedTo) {
              return this.$notify.info(
                'error',
                this.$t('validation.required', [
                  this.$t('ticket.headers.assignedToName'),
                ]),
              )
            }
            if (params.assignedTo && params.assignedTo !== this.oldAssignedTo) {
              params.actionType = '1'
            } else {
              params.actionType = ''
            }
          } else {
            params.actionType = actionType
          }
          if (actionType === '0' && params.assignedTo) {
            params.actionType = '1'
          }
          //漏洞管理
          if (this.ticketInfo.vulnerabilityAssessment === '1') {
            if (actionType === '2') {
              this.$nextTick(() => {
                let isError = document.getElementsByClassName('remark-box')
                isError[0].scrollIntoView({
                  block: 'start',
                  behavior: 'smooth',
                })
              })
              if (
                !this.ticketInfo.attackCharacter ||
                !this.ticketInfo.attackCharacter.trim()
              ) {
                return this.$notify.info(
                  'error',
                  this.$t('validation.required', [
                    this.$t('ticket.dashboardVulner.attackCharacter'),
                  ]),
                )
              }
              if (
                !this.ticketInfo.accessType ||
                !this.ticketInfo.accessType.trim()
              ) {
                return this.$notify.info(
                  'error',
                  this.$t('validation.required', [
                    this.$t('ticket.dashboardVulner.accessType'),
                  ]),
                )
              }
              if (
                !this.ticketInfo.attackDistance ||
                !this.ticketInfo.attackDistance.trim()
              ) {
                return this.$notify.info(
                  'error',
                  this.$t('validation.required', [
                    this.$t('ticket.dashboardVulner.attackDistance'),
                  ]),
                )
              }
            }
          }
          // else {
          //   params.vulnerabilityAttackVector = ''
          //   params.vulnerabilityImpactScope = ''
          //   params.accessType = ''
          //   params.attackDistance = ''
          //   params.attackCharacter = ''
          // }
          if (
            resObj &&
            ((params.dataSource === '1' &&
              Number(resObj.affectedProjectCount) > 0) ||
              (params.dataSource === '2' && ['0', '1'].includes(resObj.status)))
          ) {
            this.completeComfirm(params, name)
            return
          }
          if (
            this.oldTitle !== params.title ||
            this.oldTicketContent !== params.ticketContent
          ) {
            this.againComfirm(params, name, actionType)
            return
          }

          const res = await editTicket(params)
          if (res.code === 200) {
            this.$notify.info(
              'success',
              name + ' ' + this.$t('ticket.hint.successfully'),
            )
            if (actionType === '6') {
              this.editFlag = false
              this.getInfo()
            } else {
              this.$router.push('/ticket/lists')
            }
          }
        }
      } catch (e) {
        const text = this.editType === 'add' ? '新增工单' : '处理工单'
        console.error(`${text}错误：${e}`)
      }
    },

    againComfirm(data, name, actionType) {
      this.$swal({
        title: name,
        text: this.$t('ticket.hint.tips'),
        icon: 'warning',
        reverseButtons: true,
        showCancelButton: true,
        confirmButtonText: this.$t('action.confirm'),
        cancelButtonText: this.$t('action.cancel'),
        customClass: {
          confirmButton: 'sweet-btn-primary',
        },
      }).then(async result => {
        if (result.isConfirmed) {
          if (actionType == 0) {
            data.actionType = '0'
          }
          const res = await editTicket(data)
          if (res.code === 200) {
            this.$notify.info(
              'success',
              this.$t('global.hint.edit', [this.$t('ticket.currentTitle')]),
            )
            if (actionType === '6') {
              this.editFlag = false
              this.getInfo()
            } else {
              this.$router.push('/ticket/lists')
            }
          }
        }
      })
    },

    completeComfirm(data, name) {
      this.$swal({
        title: name,
        text:
          data.dataSource === '1'
            ? this.$t('ticket.completeVulTip2')
            : this.$t('ticket.completeAlertTip2'),
        icon: 'warning',
        reverseButtons: true,
        showCancelButton: true,
        confirmButtonText: this.$t('action.confirm'),
        cancelButtonText: this.$t('action.cancel'),
        customClass: {
          confirmButton: 'sweet-btn-primary',
        },
      }).then(async result => {
        if (result.isConfirmed) {
          const res = await editTicket(data)
          if (res.code === 200) {
            this.$notify.info(
              'success',
              this.$t('global.hint.edit', [this.$t('ticket.currentTitle')]),
            )
            this.$router.push('/ticket/lists')
          }
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.remark-box {
  width: auto !important;
}
::v-deep .v-list-item.v-list-item--disabled {
  color: #c0c3cc !important;
  caret-color: #c0c3cc !important;
  .v-icon.v-icon {
    color: #c0c3cc !important;
    caret-color: #c0c3cc !important;
  }
}
.ticket-box {
  color: var(--v-color-base);
  .show-more-false {
    max-height: 300px;
    overflow: hidden;
  }
  .export-btn {
    font-size: 14px;
    .export-text {
      color: $action-btn-color;
    }
  }
  .export-btn:hover {
    .action-btn,
    .export-text {
      color: $primary;
    }
  }
  .time-box {
    position: relative;

    .el-date-editor {
      position: absolute;
      top: 8px;
      left: 0;
      width: 90%;
      ::v-deep .el-input__icon {
        line-height: 48px !important;
      }
      ::v-deep .el-input__inner {
        border: 0px solid #dcdfe6 !important;
        padding: 0 !important;
        opacity: 0 !important;
      }
      ::v-deep .el-input__prefix {
        display: none;
      }
    }
  }
  .title-box {
    position: relative;

    ::v-deep .v-size--default {
      width: 18px !important;
      height: 18px !important;
      position: absolute;
      right: 0;
      bottom: 30px;
    }
  }
  .upload-note {
    color: #a1a6b1;
  }
  .editor-bottom {
    border-left: 1px solid var(--v-driver-base) !important;
    border-right: 1px solid var(--v-driver-base) !important;
    border-bottom: 1px solid var(--v-driver-base) !important;
    border-radius: 0px 0px 5px 5px !important;

    .reply-btn {
      height: 34px !important;
    }
  }
  // .content-box {
  //   color: rgba(94, 86, 105, 0.68);
  // }
  .ticket-center-box {
    padding-bottom: 64px;
  }
  .active-box {
    width: 100%;
    font-size: 14px;
    border: 1px solid var(--v-bgDivider-base) !important;
    box-shadow: inset 3px 0px 0px $primary;
    border-radius: 0px 4px 4px 0px;
    color: #686e7c;
    position: relative;

    .more-chip-box {
      position: absolute;
      bottom: -12px;
      left: 0;
      right: 0;
      margin: auto;
      width: 48px !important;
      height: 24px !important;
      border-radius: 4px !important;
      border: 1px solid #e6eaf2;
      background: #fff;
      color: #686e7c;

      ::v-deep .v-chip__content {
        justify-content: center !important;
      }
    }
    .more-chip-box:hover {
      color: var(--v-color-base) !important;
      border: 1px solid $primary;
    }
    .history-box {
      color: var(--v-color-base) !important;
    }
    .op-name {
      color: var(--v-color-base) !important;

      font-weight: $font-weight-semibold;
    }
  }
  .v-tab {
    padding: 0 8px !important;
    min-width: 48px;
  }
  .editor {
    height: 400px !important;
    // ::v-deep .ql-editor {
    //   padding: 8px 12px !important;
    //   color: #1f2533 !important;
    //   font-size: 14px !important;
    // }
    // ::v-deep .ql-container.ql-snow {
    //   border-color: #e0dede !important;
    //   border-radius: 5px !important;
    //   // border-radius: 0px 4px 4px 0px;
    // }

    // ::v-deep .ql-editor.ql-blank::before {
    //   font-style: normal !important;
    //   color: rgba(94, 86, 105, 0.68) !important;
    //   font-size: 1rem !important;
    // }
    // ::v-deep .ql-container.ql-snow.ql-disabled {
    //   .ql-editor {
    //     color: rgba(94, 86, 105, 0.38) !important;
    //   }
    // }
  }
  .editor1 {
    height: 200px !important;
    // ::v-deep .ql-container.ql-snow {
    //   border-color: #e0dede !important;
    //   border-radius: 5px 5px 0px 0px !important;
    // }
  }

  .editor2 {
    font-size: 14px !important;
    color: var(--v-color-base) !important;
    ::v-deep .ql-editor {
      padding: 0 !important;
    }
    ::v-deep .ql-container.ql-snow {
      border: 0 solid #ccc !important;
    }
    ::v-deep .ql-editor.ql-blank::before {
      font-style: normal !important;
    }
  }
  ::v-deep .v-slide-group__wrapper {
    box-shadow: 0px 0px 0px 0px var(--v-bgDivider-base) inset !important;
  }
  .ticket-title {
    color: var(--v-color-base);
    font-size: 16px !important;
  }
  .upload-btn {
    width: 34px !important;
    height: 34px !important;
    padding: 0 !important;
  }
  .btn {
    background: var(--v-bgColor-base);
    border: 1px dashed #b4bbcc;
    border-radius: 2px;
    color: #1f2533;
    height: 36px !important;
  }
  .btn:hover {
    background: rgba(1, 76, 241, 0.1);
    border: 1px dashed $primary;
    color: $primary;
  }
  .chip-file {
    .v-chip {
      background: var(--v-bgColor-base) !important;
      padding: 5px 14px !important;
      border-radius: 2px !important;
      cursor: pointer;
      .file-size {
        color: var(--v-secondary-base) !important;
      }
    }
  }
  .active-chip {
    ::v-deep .v-chip__content {
      width: 100% !important;
    }
  }
  .active-chip-box {
    height: 56px !important;
  }
  .active-chip:hover::before {
    border: 0px solid $primary;
  }
  .active-chip:hover .v-chip__content .file-size {
    color: $primary !important;
  }
  .row {
    margin: 0;
  }
  .col {
    padding: 0;
  }
}
</style>
