import { mdiCheckboxBlankCircleOutline } from '@mdi/js'

const themeConfig = {
  app: {
    name: 'VSMP',
    title: '车辆安全监测平台',
    enTitle: 'Vehicle Security Monitoring Platform',
    logo: require('@/assets/images/svg/logo.svg'),

    // name: 'VSAC',
    // title: '车辆安全监测与分析平台',
    // enTitle: 'Vehicle Security Analysis Center',
    // logo: require('@/assets/images/svg/logo2.svg'),

    isDark: false,
    isRtl: false,
    contentLayoutNav: 'vertical', // vertical, horizontal
    routeTransition: 'slide-x-transition',
    // ! `semi-dark` isn't available for horizontal nav menu
    skinVariant: 'default', // default, bordered, semi-dark
    contentWidth: 'boxed', // full,boxed
    map: 'baidu', // baidu,mapbox
    isThemeAndTimeZone: true,
  },
  menu: {
    isMenuHidden: false,
    isVerticalNavMini: false,
    verticalMenuAccordion: true,
    groupChildIcon: mdiCheckboxBlankCircleOutline,
    horizontalNavMenuGroupOpenOnHover: true,
  },
  appBar: {
    /*
    ! In Content Layout Horizontal Nav type `hidden` value won't work
    ! In Content Layout Horizontal Nav type value of `type` will affect both Appbar and Horizontal Navigation Menu
    */
    type: 'fixed', // fixed, static, hidden
    isBlurred: true,
  },

  footer: {
    type: 'hidden', // fixed, static, hidden
  },
  themes: {
    light: {
      // primary: '#2940CB',
      // secondary: '#8A8D93',
      // accent: '#0d6efd',
      primary: '#214EA8',
      accent: '#A1A6B1', // 占位文字
      secondary: '#686E7C', // 次要文字
      success: '#56CA00',
      info: '#16B1FF',
      low: '#3caea3', // 低
      warning: '#FF910F', // O
      danger: '#FB8C00', // 高
      error: '#DA1F1F', // R
      backgroundColor: '#ffffff',
      color: '#1f2533',
      bgColor: '#f5f6fa',
      bgCard: '#eceef3',
      driver: '#e0dede',
      bgDivider: '#e6eaf2',
    },
    dark: {
      color: '#FFFFFF', // 字体颜色
      backgroundColor: '#171B34', // 背景色
      // primary: '#2940CB',
      // primary: '#9155FD',
      // accent: '#0d6efd',
      // secondary: '#8A8D93',
      // primary: '#038CD6',
      // primary: '#44E2FE',
      primary: '#0F7EFF',
      accent: '#2A3867',
      secondary: '#7A8599',
      success: '#56CA00',
      info: '#16B1FF',
      low: '#3caea3', // 低
      warning: '#FF910F', // O
      danger: '#FB8C00', // 高
      error: '#DA1F1F', // R
      bgColor: '#222742',
      bgCard: '#7a8599',
      driver: '#33354f',
      bgDivider: '#33354f',
    },
  },
  // 全局loading配置
  pageLoading: ['/save', '/add', '/update'],
}

export default themeConfig
