import { request, vsocPath } from '../../util/request'

// 获取漏洞列表
export const getList = function (data) {
  return request({
    url: `${vsocPath}/vulnerability/vulnerability`,
    method: 'post',
    data,
  })
}
export const getVulnerabilityDetail = function (data) {
  return request({
    url: `${vsocPath}/vulnerability/selectDetails`,
    method: 'post',
    data,
    loading: true,
  })
}

export const importVulnerability = function (data) {
  return request({
    url: `${vsocPath}/vulnerability/importVehicleAsset`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-download',
    },
    responseType: 'blob',
  })
}

export const downloadVulnerabilityTemplate = function (data) {
  return request({
    url: `${vsocPath}/vulnerability/downloadTemplate`,
    method: 'post',
    data,
    // headers: {
    //   'Content-Type': 'application/x-download',
    // },
    responseType: 'blob',
  })
}

// ========================自建漏洞=========================

export const getSelfVulnerabilityList = function (data) {
  return request({
    url: `${vsocPath}/selfBuiltVulnera/queryPage`,
    method: 'post',
    data,
  })
}

export const addSelfVulnerability = function (data) {
  return request({
    url: `${vsocPath}/selfBuiltVulnera/addSelfBuiltVulnera`,
    method: 'post',
    data,
  })
}

export const getSelfVulnerabilityDetail = function (data) {
  return request({
    url: `${vsocPath}/selfBuiltVulnera/selfBuiltVulneraDetail`,
    method: 'post',
    loading: true,
    data,
  })
}

export const editSelfVulnerability = function (data) {
  return request({
    url: `${vsocPath}/selfBuiltVulnera/updateSelfBuiltVulnera`,
    method: 'post',
    data,
  })
}

export const delSelfVulnerability = function (data) {
  return request({
    url: `${vsocPath}/selfBuiltVulnera/deleteSelfBuiltVulnera`,
    method: 'post',
    data,
  })
}

// =========================cve==============================

export const getCveList = function (data) {
  return request({
    url: `${vsocPath}/cve/selectPage`,
    method: 'post',
    data,
  })
}

export const getCveDetail = function (data) {
  return request({
    url: `${vsocPath}/cve/selectDetails`,
    method: 'post',
    loading: true,
    data,
  })
}

// ===================================cnnvd===========================

export const getCnnvdList = function (data) {
  return request({
    url: `${vsocPath}/cnnvd/findVulnerabilityCnnvd`,
    method: 'post',
    data,
  })
}

export const getCnnvdDetail = function (data) {
  return request({
    url: `${vsocPath}/cnnvd/selectDetails`,
    method: 'post',
    data,
  })
}

// ================================cavd================================
export const getCavdList = function (data) {
  return request({
    url: `${vsocPath}/cavd/findVulnerabilityCavd`,
    method: 'post',
    data,
  })
}

export const getCavdDetail = function (data) {
  return request({
    url: `${vsocPath}/cavd/selectDetails`,
    method: 'post',
    loading: true,
    data,
  })
}
