<template>
  <div>
    <v-card tile elevation="0" class="position-relative border-bottom">
      <v-tabs centered height="54" v-model="currentTab" @change="onReset">
        <v-tab
          class="px-16"
          v-for="item in dataTitleList"
          :key="item.value"
          :tab-value="item.value"
        >
          {{ item.text }}
        </v-tab>
      </v-tabs>
      <div
        class="advance d-flex align-center tip-color cursor-pointer"
        @click="showDoQuery"
      >
        <vsoc-icon
          class="mr-2"
          size="x-large"
          type="fill"
          icon="icon-gaojichaxun"
        />
        <span>{{ $t('action.advanced') }}</span>
      </div>
    </v-card>
    <vsoc-filter
      :filterList="filterList"
      ref="filterList"
      mode="multiple"
      @clear="$_clearFilter"
      @reset="onReset"
    >
      <template v-slot:text="{ item }"> {{ item.text }}</template>
    </vsoc-filter>
    <!-- <div
      ref="filterList"
      class="rounded-0 overflow-x d-flex align-center px-4 bg-base"
    >
      <span
        v-show="filterList.length !== 0"
        class="color-base text-base font-weight-semibold"
        >{{ $t('action.queryConditions') }}：</span
      >
      <v-chip-group v-if="filterList.length" class="pa-0 ma-0">
        <v-chip
          v-for="(item, index) in filterList"
          :key="index"
          small
          pill
          dense
          class="pa-0 active-chip rounded-pill color-base bg-gray px-4"
          close
          close-icon="mdi-close"
          @click:close="$_clearFilter(item, index)"
        >
          {{ item.text }}
        </v-chip>
      </v-chip-group>
      <v-spacer></v-spacer>
      <v-btn
        v-if="filterList.length"
        color="primary"
        x-small
        rounded
        class="px-4 py-1 rounded-pill text-base"
        @click="onReset"
      >
        <v-icon left> mdi-eraser </v-icon>
        {{ $t('action.clear') }}
      </v-btn>
    </div>
    <v-divider></v-divider> -->
    <v-tabs-items v-model="currentTab">
      <v-tab-item :value="0" eager>
        <v-card tile class="main-content">
          <v-card-text class="pa-0">
            <div
              class="d-flex justify-space-between align-center flex-row-reverse"
            >
              <div class="d-flex align-end">
                <table-search
                  :searchList="searchList"
                  :searchQuery="query"
                  @search="$_search"
                ></table-search>
                <!-- <v-select
                  v-model="queryKey"
                  small
                  outlined
                  color="primary"
                  :menu-props="{ auto: true, offsetY: true }"
                  append-icon="mdi-chevron-down"
                  hide-details
                  dense
                  class="mr-3 select-width"
                  :items="searchConditions"
                  :label="$t('action.queryConditions')"
                ></v-select>
                <div class="text-width">
                  <v-text-field
                    v-if="queryKey !== 'vulnerabilityLevelList'"
                    v-model="query[queryKey]"
                    color="primary"
                    hide-details
                    dense
                    outlined
                    :label="$t('action.queryContent')"
                  ></v-text-field>
                  <v-select
                    v-else
                    v-model="query[queryKey]"
                    clearable
                    small
                    color="primary"
                    :menu-props="{ auto: true, offsetY: true }"
                    append-icon="mdi-chevron-down"
                    hide-details
                    outlined
                    multiple
                    dense
                    :items="vulnerabilityLevelEnum"
                    :label="$t('action.queryContent')"
                  ></v-select>
                </div>
                <v-btn
                  color="primary--text bg-btn ml-3"
                  elevation="0"
                  @click="$_search"
                >
                  <span>{{ $t('action.search') }}</span>
                </v-btn> -->
              </div>

              <!-- <div class="d-flex justify-end align-center">
                <v-btn
                  width="76"
                  min-width="76"
                  elevation="0"
                  color="primary"
                  class="me-1"
                  @click="$refs.batchImport.isShow = true"
                  v-has:self-vulner-import
                >
                  <span>导入</span>
                </v-btn>
              </div> -->
            </div>

            <v-data-table
              ref="bugTable"
              fixed-header
              :items-per-page="query.pageSize"
              item-key="id"
              :height="tableHeight"
              hide-default-footer
              :headers="headers"
              :items="tableData"
              class="table border-radius-xl mt-3 thead-light"
              :loading="tableLoading"
              @click:row="$_viewDetails"
            >
              <template v-slot:item.id="{ item }">
                <div class="py-2">
                  <div>
                    <v-chip label x-small class="mb-1" color="secondary"
                      >CNVD</v-chip
                    >{{ item.cnvdId | dataFilter }}
                  </div>
                  <div>
                    <v-chip label x-small text-color="accent">CVE</v-chip
                    ><span class="accent--text">{{
                      item.cveId | dataFilter
                    }}</span>
                  </div>
                </div>
              </template>

              <template v-slot:item.vulnerabilityName="{ item }">
                <div>
                  <div
                    class="mb-1 text-overflow-hide"
                    style="max-width: 25rem"
                    v-show-tips
                  >
                    {{ item.vulnerabilityName | dataFilter }}
                  </div>
                  <div>
                    <div
                      v-if="getLevelColor[item.vulnerabilityLevel]"
                      class="d-flex"
                      :style="`color:${
                        getLevelColor[item.vulnerabilityLevel].color
                      }`"
                    >
                      <vsoc-icon
                        class="mr-1"
                        type="fill"
                        size="middle"
                        icon="icon-loudongdengjibiaozhi"
                      ></vsoc-icon>
                      <span class="font-weight-medium">{{
                        getLevelColor[item.vulnerabilityLevel].text
                      }}</span>
                    </div>
                    <span v-else>N/A</span>
                  </div>
                </div>
              </template>

              <template v-slot:item.loopholeType="{ item }">
                <div
                  style="max-width: 12rem"
                  class="text-overflow-hide"
                  v-show-tips
                  v-if="vulnerabilityTypeEnum[item.loopholeType]"
                >
                  {{ vulnerabilityTypeEnum[item.loopholeType].text }}
                </div>
              </template>

              <template v-slot:item.recordTime="{ item }">
                <div>
                  <div class="mb-1">{{ item.recordTime | dataFilter }}</div>
                  <div class="accent--text">
                    {{ item.publicTime | dataFilter }}
                  </div>
                </div>
              </template>

              <template v-slot:item.createUser="{ item }">
                <div>
                  <div class="mb-1">{{ item.createUser | dataFilter }}</div>
                  <div class="accent--text">{{ item.createTime }}</div>
                </div>
              </template>

              <template v-slot:item.updateTime="{ item }">
                <div>
                  <div class="mb-1">{{ item.updateTime | dataFilter }}</div>
                  <div class="accent--text">
                    {{ item.dataSourceName | dataFilter }}
                  </div>
                </div>
              </template>
            </v-data-table>
            <!-- 分页器 -->
            <vsoc-pagination
              :page.sync="query.pageNum"
              :size.sync="query.pageSize"
              :total="tableDataTotal"
              @change-size="$_search"
              @change-page="getTableData"
            />
          </v-card-text>
        </v-card>
      </v-tab-item>
      <v-tab-item :value="1" eager>
        <self-vulnerability
          ref="selfVulnerability"
          :tableHeight="tableHeight"
          :vulnerabilityDrawer="$refs.vulnerabilityDrawer"
          @filter="$_appendFilterListItem"
        ></self-vulnerability>
      </v-tab-item>
      <v-tab-item :value="-1" eager>
        <cve-index
          ref="cveVulnerability"
          :tableHeight="tableHeight"
          :vulnerabilityDrawer="$refs.vulnerabilityDrawer"
          @filter="$_appendFilterListItem"
        ></cve-index>
      </v-tab-item>
      <v-tab-item :value="-0.5" eager>
        <cnnvd-index
          ref="cnnvdVulnerability"
          :tableHeight="tableHeight"
          :vulnerabilityDrawer="$refs.vulnerabilityDrawer"
          @filter="$_appendFilterListItem"
        ></cnnvd-index>
      </v-tab-item>
      <v-tab-item :value="-2" eager>
        <cavd-index
          ref="cavdVulnerability"
          :tableHeight="tableHeight"
          :vulnerabilityDrawer="$refs.vulnerabilityDrawer"
          @filter="$_appendFilterListItem"
        ></cavd-index>
      </v-tab-item>
    </v-tabs-items>

    <vulnerability-drawer
      ref="vulnerabilityDrawer"
      v-model="isDrawerShow"
      @do-query="doQuery"
      :currentTab="currentTab"
    ></vulnerability-drawer>

    <batch-import ref="batchImport" @refresh="$_search"></batch-import>
  </div>
</template>

<script>
import { getList } from '@/api/vulnerability'
import TableSearch from '@/components/TableSearch/index.vue'
import VsocDialog from '@/components/VsocDialog.vue'
import VsocFilter from '@/components/VsocFilter.vue'
import VsocPagination from '@/components/VsocPagination.vue'
import breadCrumb from '@/components/bread-crumb/index'
import { getLevelColor } from '@/plugins/systemColor'
import { dataFilter } from '@/util/filters'
import { clearFilterItem, setRemainingHeight } from '@/util/utils'
import CavdIndex from '@/views/vulnerability/cavd/CavdIndex'
import CnnvdIndex from '@/views/vulnerability/cnnvd/CnnvdIndex'
import CveIndex from '@/views/vulnerability/cve/CveIndex'
import { cloneDeep } from 'lodash'
import BatchImport from './BatchImport'
import SelfVulnerability from './SelfVulnerability.vue'
import VulnerabilityDrawer from './VulnerabilityDrawer.vue'

export default {
  name: 'Vulnerability',
  components: {
    VsocPagination,
    VsocDialog,
    breadCrumb,
    VulnerabilityDrawer,
    BatchImport,
    SelfVulnerability,
    CveIndex,
    VsocFilter,
    CnnvdIndex,
    TableSearch,
    CavdIndex,
  },
  data() {
    return {
      currentTab: -2,
      isDrawerShow: false,

      // 漏洞详情弹框
      showBugDetails: false,

      // 漏洞详情弹框标题
      bugDetailsTitile: '',

      // 查询内容
      queryKey: 'vulnerabilityLevelList',
      tableLoading: false,

      // 查询条件下拉选择
      query: {
        vulnerabilityLevelList: [],
        loopholeTypeList: [],
        cveId: '',
        cnvdId: '',
        vulnerabilityName: '',
        dataSource: '', //漏洞来源
        publicTimeStart: null, //公开日期开始时间（用于条件查询）
        publicTimeEnd: null, //公开日期结束时间（用于条件查询）
        recordTimeStart: null, //收录时间开始时间（用于条件查询）
        recordTimeEnd: null, //收录时间结束时间（用于条件查询）
        updateTimeStart: null, //更新世家开始时间（用于条件查询）
        updateTimeEnd: null, //更新时间结束时间（用于条件查询）
        createTimeStart: null, //录入时间开始时间（用于条件查询）
        createTimeEnd: null, //录入时间结束时间（用于条件查询）
        pageNum: 1,
        pageSize: 200,

        cavdNo: '',
        vulName: '',
        cveContent: '',
        publishDateStart: null,
        publishDateEnd: null,
        vulLevelList: [],
        createTimeStart: null,
        createTimeEnd: null,
        syncTimeStart: null,
        syncTimeEnd: null,
      },

      // 查询条件展示列表
      filterList: [],

      // 漏洞管理表格信息
      tableData: [],
      tableHeight: '34.5rem',
      tableDataTotal: 0,
    }
  },
  watch: {
    '$store.state.enums.enums': {
      handler() {
        if (this.currentTab == 0) {
          this.$_appendFilterListItem()
        } else if (this.currentTab == 1) {
          this.$_appendFilterListItem(this.$refs.selfVulnerability.query)
        } else if (this.currentTab == -1) {
          this.$_appendFilterListItem(this.$refs.cveVulnerability.query)
        } else if (this.currentTab == -2) {
          this.$_appendFilterListItem(this.$refs.cavdVulnerability.query)
        } else {
          this.$_appendFilterListItem(this.$refs.cnnvdVulnerability.query)
        }
      },
      deep: true,
    },
  },
  computed: {
    searchList() {
      return [
        {
          type: 'multiSearch',
          value: 'vulnerabilityLevelList',
          conditions: [
            {
              type: 'autocomplete',
              value: 'vulnerabilityLevelList',
              text: this.$t('vulnerability.headers.alarmLevel'),
              itemList: this.$store.getters['enums/getVulnerabilityLevel'],
            },
            {
              type: 'input',
              value: 'vulnerabilityName',
              text: this.$t('vulnerability.headers.vulnerabilityName'),
            },
            {
              type: 'input',
              value: 'cveId',
              text: this.$t('vulnerability.headers.cveId'),
            },
            {
              type: 'input',
              value: 'cnvdId',
              text: this.$t('vulnerability.headers.cnvdId'),
            },
          ],
        },
      ]
    },
    // 查询条件列表
    searchConditions() {
      return [
        {
          text: this.$t('vulnerability.headers.alarmLevel'),
          value: 'vulnerabilityLevelList',
        },
        {
          text: this.$t('vulnerability.headers.vulnerabilityName'),
          value: 'vulnerabilityName',
        },
        {
          text: this.$t('vulnerability.headers.cveId'),
          value: 'cveId',
        },
        {
          text: this.$t('vulnerability.headers.cnvdId'),
          value: 'cnvdId',
        },
      ]
    },
    dataTitleList() {
      return [
        {
          text: 'CAVD',
          value: -2,
        },
        {
          text: 'CVE',
          value: -1,
        },
        {
          text: 'CNNVD',
          value: -0.5,
        },
        {
          text: 'CNVD',
          value: 0,
        },
        {
          text: this.$t('vulnerability.tab2'),
          value: 1,
        },
      ]
    },
    headers() {
      return [
        {
          text: this.$t('vulnerability.headers.idInfo'),
          value: 'id',
          width: 220,
          sortable: false,
        },
        // {
        //   text: this.$t('vulnerability.headers.cvss'),
        //   value: 'cvss',
        //   width: '200px',
        // },
        {
          text: this.$t('vulnerability.headers.vulnerInofo'),
          value: 'vulnerabilityName',
          width: 200,
          sortable: false,
        },
        // {
        //   text: this.$t('vulnerability.headers.vulnerabilityLevel'),
        //   value: 'vulnerabilityLevel',
        //   width: '130px',
        // },
        {
          text: this.$t('vulnerability.headers.collectionTime'),
          value: 'recordTime',
          width: 160,
          sortable: false,
        },
        {
          text: this.$t('vulnerability.headers.entryInfo'),
          value: 'createUser',
          width: 260,
          sortable: false,
        },
        // {
        //   text: this.$t('vulnerability.headers.updateInfo'),
        //   value: 'updateTime',
        //   width: 160,
        //   sortable: false,
        // },
        {
          text: this.$t('vulnerability.headers.loopholeType'),
          value: 'loopholeType',
          width: 140,
          sortable: false,
        },
        // {
        //   text: this.$t('vulnerability.headers.publicTime'),
        //   value: 'publicTime',
        //   width: '200px',
        // },

        // {
        //   text: this.$t('vulnerability.headers.dataSource'),
        //   value: 'dataSource',
        //   width: '140px',
        // },
        // {
        //   text: this.$t('vulnerability.headers.createTime'),
        //   value: 'createTime',
        //   width: '200px',
        // },

        // {
        //   text: '受影响厂商',
        //   value: 'createTime',
        //   width: '100px',
        // },
      ]
    },
    getLevelColor() {
      return this.$store.state.enums.enums.VulnerabilityLevel
    },
    vulnerabilityLevelEnum() {
      return this.$store.getters['enums/getVulnerabilityLevel']
    },
    vulnerabilityTypeEnum() {
      return Object.assign([], this.$store.state.enums.enums.VulnerabilityType)
    },
  },
  mounted() {
    this.$_setTableHeight()
    this.$bus.$on('resize', this.$_setTableHeight)

    this.$_search()
  },
  beforeDestroy() {
    this.$bus.$off('resize', this.$_setTableHeight)
  },
  methods: {
    // 清除某个查询条件
    $_clearFilter(item, index) {
      let bool = clearFilterItem.call(this, item)
      if (item.type === 'Date') {
        this.query[`${item.key}Start`] = null
        this.query[`${item.key}End`] = null
        this.query.startDate = null
        this.query.endDate = null
        this.query[`start${item.key}`] = null
        this.query[`end${item.key}`] = null
        this.filterList.splice(index, 1)
        this.$forceUpdate()
        bool = false
      }
      if (!bool) {
        this.$_search()
      }
    },
    doQuery(advanceQuery) {
      this.query = Object.assign(this.query, cloneDeep(advanceQuery))
      this.$_search()
    },
    onReset() {
      if (this.currentTab === 0) {
        Object.assign(this.$data.query, this.$options.data.call(this).query)
        this.$_search()
      } else if (this.currentTab === 1) {
        this.$refs.selfVulnerability.onReset()
      } else if (this.currentTab === -1) {
        this.$refs.cveVulnerability.onReset()
      } else if (this.currentTab === -2) {
        this.$refs.cavdVulnerability.onReset()
      } else {
        this.$refs.cnnvdVulnerability.onReset()
      }
    },
    $_showBugDetails(item) {
      this.showBugDetails = true
      this.bugDetailsTitile = item.name
    },

    $_search() {
      this.query.pageNum = 1

      if (this.currentTab === 0) {
        this.getTableData()
        const params = {
          vulnerabilityLevelList: this.query.vulnerabilityLevelList,
          loopholeTypeList: this.query.loopholeTypeList,
          cveId: this.query.cveId,
          cnvdId: this.query.cnvdId,
          vulnerabilityName: this.query.vulnerabilityName,
          dataSource: this.query.dataSource,
          publicTimeStart: this.query.publicTimeStart,
          publicTimeEnd: this.query.publicTimeEnd,
          recordTimeStart: this.query.recordTimeStart,
          recordTimeEnd: this.query.recordTimeEnd,
          updateTimeStart: this.query.updateTimeStart,
          updateTimeEnd: this.query.updateTimeEnd,
          createTimeStart: this.query.createTimeStart,
          createTimeEnd: this.query.createTimeEnd,
          pageNum: this.query.pageNum,
          pageSize: this.query.pageSize,
        }
        this.$_appendFilterListItem(params)
      } else if (this.currentTab === 1) {
        for (const key in this.$refs.selfVulnerability.query) {
          this.$refs.selfVulnerability.query[key] = this.query[key]
        }
        this.$refs.selfVulnerability.getTableData()
        this.$_appendFilterListItem(this.$refs.selfVulnerability.query)
      } else if (this.currentTab == -1) {
        for (const key in this.$refs.cveVulnerability.query) {
          this.$refs.cveVulnerability.query[key] = this.query[key]
        }
        this.$refs.cveVulnerability.getTableData()
        this.$_appendFilterListItem(this.$refs.cveVulnerability.query)
      } else if (this.currentTab == -2) {
        for (const key in this.$refs.cavdVulnerability.query) {
          this.$refs.cavdVulnerability.query[key] = this.query[key]
        }
        this.$refs.cavdVulnerability.getTableData()
        this.$_appendFilterListItem(this.$refs.cavdVulnerability.query)
      } else {
        for (const key in this.$refs.cnnvdVulnerability.query) {
          this.$refs.cnnvdVulnerability.query[key] = this.query[key]
        }
        this.$refs.cnnvdVulnerability.getTableData()
        this.$_appendFilterListItem(this.$refs.cnnvdVulnerability.query)
      }
    },

    // 添加查询条件
    $_appendFilterListItem(query = this.query) {
      const searchList = cloneDeep(query)
      delete searchList.pageNum
      delete searchList.pageSize
      this.filterList =
        (this.$refs.vulnerabilityDrawer &&
          this.$refs.vulnerabilityDrawer.setFilterList(searchList)) ||
        []
      this.$_setTableHeight()
    },

    // 获取表格
    async getTableData() {
      try {
        this.tableLoading = true
        const { data } = await getList(this.query)
        this.tableData = data.records
        this.tableDataTotal = data.total
      } catch (e) {
        console.error(`获取漏洞管理：${e}`)
      }
      this.tableLoading = false
    },

    showDoQuery() {
      this.isDrawerShow = true
      let data = {}
      if (this.currentTab === 1) {
        data = this.$refs.selfVulnerability.query
      } else if (this.currentTab === 0) {
        data = cloneDeep(this.query)
      } else if (this.currentTab == -1) {
        data = this.$refs.cveVulnerability.query
      } else if (this.currentTab == -2) {
        data = this.$refs.cavdVulnerability.query
      } else {
        data = this.$refs.cnnvdVulnerability.query
      }
      this.$refs.vulnerabilityDrawer.setModel(data)
    },

    $_setTableHeight() {
      this.$nextTick(() => {
        const filterFn = () => {
          return -this.$refs.filterList.offsetHeight - 2
        }
        this.tableHeight = setRemainingHeight(filterFn)
      })
    },

    $_viewDetails(record) {
      if (this.$route.meta.buttons.includes('vulner-detail')) {
        this.$router.push(`/bugManagement/details?id=${record.id}`)
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.v-chip.v-size--x-small {
  display: inline-block;
  width: 3.6667rem;
  height: 1.8333rem;
  padding: 0;
  margin-right: 4px;
  text-align: center;
}
.advance {
  z-index: 1;
  top: 50%;
  transform: translateY(-50%);
  position: absolute;
  right: 1.5rem;
  vertical-align: middle;
}
</style>
