import { i18n } from '@/plugins/i18n'
import store from '@/store'
import { getLocalStorage } from '@/util/localStorage'
import { debounce } from 'lodash'
import Vue from 'vue'
import { emptyChartOptionFn as optionFn } from './utils'
// tips延时显示时间
const timeDelay = 200

const setShowTips = (el, params) => {
  // 给当前元素设置超出隐藏
  el.style.overflow = 'hidden'
  el.style.textOverflow = 'ellipsis'
  el.style.whiteSpace = 'nowrap'
  const elId = 'vc-tooltip'
  let time = null
  let leaveTime = null

  // 鼠标移入
  el.onmouseenter = function () {
    time = setTimeout(() => {
      let { top } = el.getBoundingClientRect()
      let { left } = el.getBoundingClientRect()
      const { width: elWidth, height: elHeight } = getElementStyle(
        el,
        params.value,
      )
      const yN = (el.offsetHeight || elHeight) + 10
      const y = top + elHeight + yN - window.innerHeight
      const x = left + elWidth - window.innerWidth

      // debugger
      if (y > 0) {
        top -= yN
      } else {
        top += yN
      }
      if (x > 0) {
        left = left - x - 24
      }

      // 创建浮层元素并设置样式
      const vcTooltipDom = document.createElement('div')
      vcTooltipDom.style.cssText = `
                position:fixed;
                top:${top}px;
                left:${left}px;
                background: rgba(97, 97, 97, 0.81);
                color:#fff;
                border-radius:5px;
                padding:4px 10px;
                font-size:12px;
                z-index:19999;
                max-width: 800px;
                word-wrap: break-word;
              `

      // 设置id方便寻找
      vcTooltipDom.setAttribute('id', elId)
      vcTooltipDom.setAttribute('class', 'tips-ani')

      // 将浮层插入到body中
      document.body.appendChild(vcTooltipDom)

      // 浮层中的文字
      vcTooltipDom.innerHTML = params.value || el.innerText
      vcTooltipDom.onmouseenter = function () {
        if (leaveTime) clearTimeout(leaveTime)
      }
      vcTooltipDom.onmouseleave = removeEle
      el.vcTooltipDom = vcTooltipDom
      clearTimeout(time)
      time = null
    }, timeDelay)
  }

  // 鼠标移出
  el.onmouseleave = removeEle

  // el.onclick = removeEle

  function removeEle() {
    if (time) {
      clearTimeout(time)
    } else {
      leaveTime = setTimeout(() => {
        // 找到浮层元素并移出
        const vcTooltipDom = document.getElementById(elId)
        vcTooltipDom && document.body.removeChild(vcTooltipDom)
        leaveTime = null
        clearTimeout(leaveTime)
      }, timeDelay)
    }
  }
}

const showTips = {
  bind(el, params) {
    setShowTips(el, params)
  },
  update(el, params) {
    setShowTips(el, params)
  },

  // 指令与元素解绑的时候，移除事件绑定
  unbind() {
    const vcTooltipDom = document.getElementById('vc-tooltip')
    vcTooltipDom && document.body.removeChild(vcTooltipDom)
  },
}

// 模拟tips元素，获取宽高用来计算位置
function getElementStyle(el, bool) {
  // if (bool) {
  //     return {
  //         width: el.offsetWidth || el.clinetWidth,
  //         height: el.offsetHeight || el.clientHeight
  //     }
  // }
  const curStyle = window.getComputedStyle(el, '') // 获取当前元素的style
  const textSpan = document.createElement('div') // 创建一个容器来记录文字的width
  // 设置新容器的字体样式，确保与当前需要隐藏的样式相同
  textSpan.style.fontSize = '12px'
  textSpan.style.fontWeight = curStyle.fontWeight
  bool ? '' : (textSpan.style.fontFamily = curStyle.fontFamily)
  textSpan.style.padding = '4px 10px'
  textSpan.style.maxWidth = '200px'
  textSpan.style.display = 'inline-block'
  textSpan.style.wordWrap = 'break-word'

  // 设置新容器的文字
  textSpan.innerHTML = bool || el.innerText
  document.body.appendChild(textSpan)
  const width = textSpan.offsetWidth
  const height = textSpan.offsetHeight
  document.body.removeChild(textSpan)

  return {
    width,
    height: bool ? el.offsetHeight || el.clientHeight : height,
  }
}

// 全局copy指令
const copy = {
  bind(el, { value }) {
    el.$value = value
    el.handler = () => {
      Vue.prototype.$copyText(`${el.$value}`).then(
        () => {
          Vue.prototype.$notify.info('success', i18n.t('global.hint.copy'))
        },
        () => {},
      )
    }
    el.addEventListener('click', el.handler) // 绑定点击事件
  },

  // 当传进来的值更新的时候触发
  componentUpdated(el, { value }) {
    el.$value = value
  },

  // 指令与元素解绑的时候，移除事件绑定
  unbind(el) {
    el.removeEventListener('click', el.handler)
  },
}

// 按钮权限
// v-has:search
const has = {
  inserted(el, binding, vnode) {
    // const { value } = binding

    // const permissions = store.getters && store.getters.buttons

    // let hasPermissions = true
    // if (value) {
    //   if (value instanceof Array) {
    //     hasPermissions = permissions.some(permission => value.includes(permission))
    //   } else {
    //     hasPermissions = permissions.includes(value)
    //   }
    // } else {
    //   console.warn('v-has标签为配置值{}', el)
    // }

    // if (!hasPermissions) {
    //   el.parentNode && el.parentNode.removeChild(el)
    // }
    let isForceDel = false
    let { userId, level: userLevel } = JSON.parse(getLocalStorage('userInfo'))
    if (binding.value) {
      let record = binding.value
      // store中的userIdByAllList，页面刷新为空，ownership权限失效
      const userIdByAllList =
        store.state.global.userIdByAllList ||
        JSON.parse(getLocalStorage('userIdByAllList'))

      let ownership =
        userIdByAllList && userIdByAllList.includes(record.createUser)

      if (ownership) {
        isForceDel = !userIdByAllList.includes(userId)
      }
    }

    if (binding.modifiers && binding.modifiers.lite && userLevel == '0') {
      isForceDel = true
    }

    const value = binding.arg || binding.value || binding.value.arg

    const permissions = vnode.context.$route.meta.buttons || []
    if (value) {
      if (!permissions.includes(value) || isForceDel) {
        const routerList = vnode.context.$router.getRoutes()
        const parentId = vnode.context.$route.meta.parentId
        const parentPermissions =
          routerList.find(v => v.meta.id === parentId)?.meta.buttons || []
        if (!parentPermissions.includes(value) || isForceDel) {
          ;(el.parentNode && el.parentNode.removeChild(el)) ||
            (el.style.display = 'none')
        }
      }
    }
  },
}

const resize = {
  inserted(el, binding, vnode) {
    let callback = binding.value
    const observer = new ResizeObserver(
      debounce(entries => {
        const entry = entries[0]
        callback(entry.contentRect)
      }, 100),
    )
    observer.observe(el)
    // 解除绑定时要用
    el._observer = observer
  },
  unbind(el) {
    const observer = el._observer
    if (el) {
      observer.unobserve(el)
    } else {
      observer.disconnect()
    }
    delete el._observer
  },
}

const getEmptyWH = el => {
  let width = 150
  let height = 120

  // 宽度不够
  if (el.clientWidth < 150) {
    let radio = 120 / 150
    width = el.clientWidth
    height = width * radio
  }
  // 高度不够
  if (el.clientHeight < 120) {
    let radio = 150 / 120
    height = el.clientHeight - 20
    width = height * radio
  }
  return {
    width,
    height,
  }
}

const emptyChartFn = el => {
  //初始化echart
  let myChart = Vue.prototype.$echarts.init(el, 'light', {
    height: 'auto',
    width: 'auto',
  })

  const { width, height } = getEmptyWH(el)
  myChart.setOption(optionFn(width, height))

  // myChart绑定到el上，方便后续调用
  el.myChart = myChart
  el.showLoading = myChart.showLoading
  el.hideLoading = myChart.hideLoading
  // 监听屏幕变化
  el.debounceResize = debounce(() => {
    const { width, height } = getEmptyWH(el)
    // 清空当前实例
    el.myChart.clear()
    // 重新绘制
    el.myChart.setOption(optionFn(width, height))
    // 必须加上resize，否则不起作用
    el.myChart.resize({
      animation: {
        duration: 200,
      },
    })
  }, 500)
  // window.addEventListener('resize', el.debounceResize)
  const observer = new ResizeObserver(el.debounceResize)
  observer.observe(el)
  el._observer = observer
}

// 暂无数据指令
const emptyChart = {
  inserted(el, binding, vnode) {
    const isEmpty = binding.value
    if (!isEmpty) {
      return
    }

    Vue.nextTick(() => {
      emptyChartFn(el)
    })
  },
  update(el, binding, vnode) {
    const isEmpty = binding.value
    if (isEmpty) {
      let myChart = el?.myChart
      if (!myChart) {
        emptyChartFn(el)
      } else {
        myChart.clear()
        emptyChartFn(el)
      }
    }
  },
  //指令卸载的时候去除window事件监听
  unbind(el, binding, vnode) {
    // window.removeEventListener('resize', el.debounceResize)
    const observer = el._observer
    if (observer) {
      observer.unobserve(el)
      delete el._observer
    }
  },
}

export { copy, emptyChart, has, resize, showTips }
