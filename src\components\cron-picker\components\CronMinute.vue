<template>
  <div class="d-flex align-center">
    <div>{{ $t('infer.expression.every') }}</div>
    <div style="width: 100px">
      <v-select
        v-model="perMinute"
        class="mx-2"
        color="primary"
        :items="minuteOptions"
        item-text="item"
        item-value="item"
        outlined
        dense
        solo
        hide-details
        :height="10"
        @change="emitChange()"
        :menu-props="{ offsetY: true }"
      >
      </v-select>
    </div>
    <div>{{ $t('infer.expression.minute') }}</div>
  </div>
</template>

<script>
export default {
  name: 'CronMinute',
  data() {
    return {
      perMinute: 5,
    }
  },
  computed: {
    minuteOptions() {
      return [1, 2, 3, 4, 5, 7, 8, 9, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55]
    },
    cronExp() {
      return `0 0/${this.perMinute} * * * ?`
    },
  },
  methods: {
    init(value) {
      const tempArr = value.split(' ')
      const minuteArr = tempArr[1].split('/')
      this.perMinute = Number(minuteArr[1])
    },
    emitChange() {
      this.$emit('change', this.cronExp)
    },
  },
}
</script>
