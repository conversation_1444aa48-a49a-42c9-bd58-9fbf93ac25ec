const groupRelation = {
  currentTitle: '组关系',
  assetCount: '包含资产数量',
  dangerCount: '存在风险数量',
  groupName: '资产组',
  selected: '已选资产（{count}）',
  unSelected: '未选资产（{count}）',
  unbind: '解除绑定',
  binding: '绑定资产',
  selectedTotal: '已选择/总数：',
  selectedHint: '确认将{count}个资产从资产组【{name}】中解绑？',
  unSelectedHint: '确认将{count}个资产加入资产组【{name}】?',
  cancel: '已取消：',
  hint: {
    unbind: '资产数量为0，不可解绑！',
    success: '解绑成功！',
  },
}

export default groupRelation
