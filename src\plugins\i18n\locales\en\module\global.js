const global = {
  diff: {
    justNow: 'just now',
    second: '{num} second ago | {num} seconds ago',
    minute: '{num} minute ago | {num} minutes ago',
    hour: '{num} hour ago | {num} hours ago',
    day: '{num} day ago | {num} days ago',
    week: '{num} week ago | {num} weeks ago',
    month: '{num} month ago | {num} months ago',
    year: '{num} year ago | {num} years ago',
    error: 'timing error',
  },
  updateDiff: 'Updated {0}',

  refreshDiff: 'Refreshed {diff}',

  time: {
    seconds: '{0} Second | {0} Seconds',
    min: '{0} Minute | {0} Minutes',
    hours: '{0} Hour | {0} Hours',
    day: '{0} Day | {0} Days',
    day1: 'Days',
  },
  navbar: {
    settings: 'Settings',
    logOut: 'Logout',
    profile: 'Profile',
    manual: 'FAQ',
  },

  drawer: {
    addTitle: 'Add {cur}',
    editTitle: 'Edit {cur}',
    delTitle: 'Delete {cur}',
    detailTitle: '{cur} Details',
    searchTitle: 'Advanced Search',
    grantTitle: 'Assign Permissions',
    baseInfo: 'Basic Information',
  },

  pagination: {
    total: 'Total',
    perPage: '',
    items: 'items per page | items',
    selected: 'selected',
    item: 'items',
  },

  hint: {
    add: '{0} added successfully!',
    edit: '{0} edited successfully!',
    del: '{0} deleted successfully!',
    copy: 'copy successfully!',
    operate: 'Operation successfully!',
    exportSuccess: 'uploaded successfully',
    exportFail: 'uploaded failed',
    successfully: 'successfully!',
    monthMore1: 'The time span is not allowed to exceed 1 month!',
    maxExportCount: 'A maximum of 9999 records can be exported at one time',
    fileUploadSuccess:
      'Attachment verification passed, upload after submission!',
    back: 'Back to Home',
    back1: 'Back',
    downEmpty: 'The download content is empty!',
    illEgal: 'Illegal file format',
    uploadEmpty: 'The file content is empty and the upload failed!',
  },

  request: {
    code302: 'Authentication failed, please log in again',
    code400: 'Parameter exception',
    code401: 'Authorization authentication exception',
    code403: 'Insufficient permissions',
    code404: 'Interface does not exist',
    code405: 'The interface request is abnormal, please check and try again',
    code500: 'System exception',
    codeDefault: 'The interface responds abnormally',
    err401: 'Username or password is wrong, please re-enter!',
    err403: 'No permission to access!',
    networkErr: 'Abnormal backend interface connection',
    timeout: 'System interface request timed out',
    fail: 'System interface {name} exception',
    notFound: "Sorry,we couldn't find the page you are looking for",
  },
  createDate: 'Create Time',
  updateDate: 'Update Time',
  createUser: 'Create User',
  updateUser: 'Update User',

  // lastUpdateDate: 'Last update time',
  updater: 'Update User',

  status: 'Status',
  null: 'Null',
  assetType: 'Asset Type',
  owner: 'Ownership',
  desc: 'Description',

  grant:
    'Insufficient permissions, please contact the administrator for authorization',
  clickDetail: 'Click for details',
  noData: 'No data available',
  noData1: 'No data available',

  selected: 'Selected:',

  count: 'Count',
  攻击事件: 'Attack',
  预警事件: 'Warning',
  活跃资产: 'Active',
  风险资产: 'Risk',

  告警总数: 'Total Alerts',
  未处理告警: 'Unhandled',
  车辆总数: 'Total Assets',
  在线车辆总数: 'Online Assets',
  风险车辆总数: 'Risk Assets',

  file: {
    uploadFile: 'Please upload attachments!',
  },

  point: 'point | points',

  encryption: 'Encryption Mode',

  name: 'name',

  select: 'Please select',
}

export default global
