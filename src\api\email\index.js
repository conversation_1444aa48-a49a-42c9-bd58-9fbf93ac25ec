import { request, vsocPath } from '../../util/request'

// 获取邮箱
export const getData = function (data) {
  return request({
    url: `${vsocPath}/emailConfig/queryEmailServerConfig`,
    method: 'post',
    data,
    loading: true,
  })
}
//编辑邮箱服务器配置
export const updateEmailServerConfig = function (data) {
  return request({
    url: `${vsocPath}/emailConfig/updateEmailServerConfig`,
    method: 'post',
    data,
  })
}
//邮箱服务器配置测试邮件发送
export const testSendEmail = function (data) {
  return request({
    url: `${vsocPath}/emailConfig/testSendEmail`,
    method: 'post',
    data,
  })
}
