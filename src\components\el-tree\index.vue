<template>
  <div class="h-100">
    <v-text-field
      v-model.trim="filterText"
      :label="labelText"
      hide-details
      class="mt-0 pt-6 mb-4"
      color="primary"
      outlined
      dense
      append-icon="mdi-magnify"
    ></v-text-field>

    <el-tree
      ref="tree"
      class="bg-card"
      :class="['tree-line', organizationFlag ? 'org-box' : 'menu-box']"
      :empty-text="$t('el.tree.emptyText')"
      :data="treeData"
      :props="defaultProps"
      :default-expand-all="expandFlag"
      :node-key="nodeKey"
      icon-class="icon-tree"
      :indent="0"
      :expand-on-click-node="false"
      @node-click="clickNode"
      :filter-node-method="filterNode"
      :show-checkbox="showCheckbox"
      v-bind="$attrs"
      v-on="$listeners"
    >
      <template slot-scope="{ node, data }">
        <div
          v-if="organizationFlag"
          class="custom-tree-node d-flex align-center"
        >
          <vsoc-icon
            v-if="data.type === '0'"
            icon="icon-shujiegouyiji"
            size="16px"
          ></vsoc-icon>
          <vsoc-icon v-else icon="icon-shujiegousanji" size="16px"></vsoc-icon>
          <!-- <i
            v-if="data.type === '0'"
            class="iconfont icon-shujiegouyiji"
            style="color: #686e7c"
          ></i>
          <i
            v-else
            class="iconfont icon-shujiegousanji"
            style="color: #686e7c"
          ></i> -->
          <!-- :style="{ color: data.state === '1' ? '#bcbdc0' : '#1F2533' }" -->

          <div
            :class="[
              'ml-2',
              'lable-text',
              data.isShow ? 'is-click' : '',
              data.state === '1' ? 'disabled-text' : 'color-base',
            ]"
          >
            {{ node.label }}
          </div>
          <v-menu
            v-if="data.isShow && data.state === '0'"
            offset-x
            right
            nudge-bottom="4"
            transition="scale-transition"
            content-class="menu-list"
          >
            <template v-slot:activator="{ on, attrs }">
              <v-btn
                min-width="0"
                min-height="0"
                class="px-0 py-0 ml-2"
                small
                dark
                text
                v-bind="attrs"
                v-on="on"
              >
                <vsoc-icon
                  icon="icon-xinzeng"
                  class="primary--text"
                  type="fill"
                  size="16px"
                ></vsoc-icon>
                <!-- <i class="el-icon-circle-plus iconfont"></i> -->
              </v-btn>
            </template>

            <v-list>
              <v-list-item
                v-for="(item, i) in menuItems"
                :key="i"
                class="list-hover"
                @click="addItem(item)"
              >
                <v-list-item-title>
                  {{ $t(item.title) }}
                </v-list-item-title>
              </v-list-item>
            </v-list>
          </v-menu>
        </div>
        <div v-else-if="isIcon" class="d-flex align-center">
          <vsoc-icon
            type="fill"
            :icon="
              data.uiType === 'Catalogue'
                ? 'icon-mulu'
                : data.uiType === 'Menu'
                ? 'icon-caidan'
                : 'icon-anniu'
            "
            class="mr-2"
            size="16"
          ></vsoc-icon>
          <div>
            {{ $generateName(node.label, data.englishName || data.enTitle) }}
          </div>
        </div>
        <div v-else-if="isApiIcon" class="d-flex align-center">
          <vsoc-icon
            type="fill"
            :icon="
              data.apiType === '0'
                ? 'icon-mulu'
                : data.apiType === '1'
                ? 'icon-bianji'
                : data.apiType === '2'
                ? 'icon-caidan'
                : ''
            "
            class="mr-2"
            size="16"
          ></vsoc-icon>
          <div>
            {{ data.name }}
          </div>
        </div>
        <div v-else>
          {{ $generateName(node.label, data.englishName || data.enTitle) }}
        </div>
      </template>
    </el-tree>
  </div>
</template>

<script>
export default {
  props: {
    treeData: Array,
    defaultProps: Object,
    menuItems: Array,
    organizationFlag: Boolean,
    showCheckbox: Boolean,
    expandFlag: Boolean,
    labelText: String,
    isIcon: {
      default: false,
      type: Boolean,
    },
    isApiIcon: {
      default: false,
      type: Boolean,
    },
    nodeKey: {
      default: 'id',
      type: String,
    },
  },
  data() {
    return {
      filterText: '',
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    },
  },
  created() {},
  beforeDestroy() {},
  methods: {
    filterNode(value, data) {
      if (!value) return true
      return data[this.defaultProps.label].indexOf(value) !== -1
    },
    clickNode(item) {
      this.$emit('click', [item])
    },
    addItem(item) {
      this.$emit('addItem', item)
    },
  },
}
</script>
<style lang="scss" scoped>
.v-menu__content.menu-list {
  margin-left: 12px !important;
  .v-list {
    padding: 0 !important;
  }
  .v-list-item {
    width: auto !important;
    height: 34px !important;
    color: var(--v-color-base) !important;
    text-align: left;
  }
  .list-hover:hover {
    color: $primary !important;
    background-color: rgba(1, 76, 241, 0.1) !important;
  }
}
::v-deep .el-tree-node__content:hover {
  background-color: rgba($color: #fff, $alpha: 0.1);
}
::v-deep .el-tree-node:focus > .el-tree-node__content {
  // background-color: #fff;
  background-color: rgba($color: #fff, $alpha: 0.1);
}
::v-deep .custom-tree-node .theme--dark.v-btn {
  color: var(--v-backgroundColor-base);
}
::v-deep .custom-tree-node {
  color: var(--v-color-base) !important;
  height: 34px;
}
::v-deep .custom-tree-node .lable-text {
  color: var(--v-color-base);
}
// ::v-deep .custom-tree-node .action-btn {
//   color: var(--v-color-base) !important;
// }
::v-deep .el-tree-node__content .lable-text.is-click {
  color: $primary;
  border-bottom: 1px solid $primary;
}

::v-deep .tree-line {
  .el-tree-node {
    position: relative;
    // padding-left: 16px; // 缩进量
  }
  .el-tree-node__content {
    // margin-bottom: 12px;
    height: 34px !important;
    font-size: $font-size-content;
    // color: #1f2533;
    color: var(--v-color-base);
  }
  // .lable-text {
  //   height: 34px;
  // }

  .el-tree-node__children {
    padding-left: 42px; // 缩进量
  }
  // 竖线
  .el-tree-node::before {
    content: '';
    height: 100%;
    width: 1px;
    position: absolute;
    left: -37px;
    top: -36px;
    border-width: 1px;
    border-left: 1px dashed $color-dividers--light;
  }

  // 当前层最后一个节点的竖线高度固定
  .el-tree-node:last-child::before {
    height: 54px; // 可以自己调节到合适数值
  }

  // 横线
  .el-tree-node::after {
    content: '';
    width: 34px;
    height: 20px;
    position: absolute;
    left: -36px;
    top: 18px;
    border-width: 1px;
    border-top: 1px dashed $color-dividers--light;
  }

  // 去掉最顶层的虚线，放最下面样式才不会被上面的覆盖了
  & > .el-tree-node::after {
    border-top: none;
  }
  & > .el-tree-node::before {
    border-left: none;
  }

  // 展开关闭的icon
  .el-tree-node__expand-icon {
    font-size: $font-size-base;
    padding: 0 !important;
    padding-left: 0;
    margin-right: 8px;
    width: 12px;
    height: 12px;
    // 叶子节点（无子节点）
    &.is-leaf {
      color: transparent;
      //   width: 0;
      // display: none; // 也可以去掉
    }
  }
  .el-tree-node__children {
    .el-tree-node__expand-icon {
      &.is-leaf {
        color: transparent;
        //   width: 0;
        display: none; // 也可以去掉
      }
    }
  }
}
.org-box {
  height: calc(100% - 64px);
  overflow-y: auto;
}
.menu-box {
  height: calc(100vh - 216px);
  overflow-y: auto;
}
::v-deep .el-icon-circle-plus {
  color: $primary;
}
::v-deep .icon-tree {
  // margin: 0 5px 0 10px;
  position: relative;
  background: url('../../assets/images/add.png');
  background-size: 100% 100%;
}
::v-deep .icon-tree.expanded {
  background: url('../../assets/images/sub.png');
  background-size: 100% 100%;
  transform: 0 !important;
}
::v-deep .is-leaf {
  background-image: none;
  background-size: 100% 100%;
}
::v-deep .el-tree-node__expand-icon {
  transform: rotate(0deg) !important;
}
::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: $primary !important;
  border-color: $primary !important;
}
::v-deep .el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: $primary !important;
  border-color: $primary !important;
}
::v-deep .el-checkbox__inner:hover {
  border-color: $primary !important;
}
::v-deep .el-checkbox__input.is-focus .el-checkbox__inner {
  border-color: $primary !important;
}
</style>
