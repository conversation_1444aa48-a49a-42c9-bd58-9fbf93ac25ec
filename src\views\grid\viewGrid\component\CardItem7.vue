<template>
  <!-- 数据采集图 -->
  <div class="box">
    <div class="box-header box-position-top">
      {{ title }}
      <!-- <span class="box-header-num">{{ total | numberToFormat }}</span> -->
    </div>

    <template v-if="list.data.length !== 0">
      <vsoc-chart
        :echartId="echartId"
        class="box-chart d-flex align-center"
        :option="option"
      ></vsoc-chart>
    </template>
    <template v-else>
      <div
        class="box-chart d-flex align-center color--primary justify-center fs-16"
      >
        None
      </div>
    </template>
  </div>
</template>

<script>
import VsocChart from '@/components/VsocChart.vue'
import moment from 'moment'

import { lineOptionFn } from './chart'

export default {
  name: 'CardItem7',
  props: {
    title: {
      type: String,
      default: '',
    },
    echartId: {
      type: String,
      default: '',
    },
    // xList: Array,
    list: {
      type: Object,
      default: () => {
        return {
          data: [],
          updateDate: '',
        }
      },
    },
    total: {
      type: [Number, String],
      default: 0,
    },
  },
  components: {
    VsocChart,
  },
  data() {
    return {
      myChart: undefined,
    }
  },
  created() {},
  computed: {
    option() {
      const { list = [] } = this // 设置默认值为空数组

      if (list.data.length === 0) {
        return lineOptionFn([], [])
      }

      // 定义一个函数来根据日期字符串的格式进行不同的格式化
      const formatDateTime = dateString => {
        // 尝试解析日期字符串
        const m = moment(
          dateString,
          ['YYYY-MM-DD', 'YYYY-MM-DD HH:mm:ss'],
          true,
        )

        if (m.isValid()) {
          // 检查是否有时间部分
          const hasTime = dateString.includes(' ')

          if (hasTime) {
            // 如果有时间部分，即使时间是 00:00:00，也格式化为 'HH:mm'
            return m.format('HH:mm')
          } else {
            // 如果没有时间部分，格式化为 'MM-DD'
            return m.format('MM-DD')
          }
        }
        return dateString // 如果解析失败，返回原始字符串
      }
      // 提取并格式化日期或时间
      const xList = list.data.map(v => formatDateTime(v.name))
      list.data.forEach(v => {
        if (v.value === 0) {
          v.value = 0.5
        }
      })
      // 提取消息数量数据
      const yList = [
        {
          name: this.$t('global.count'),
          data: list.data.map(v => v.value),
          color: '#21CBFF',
        },
      ]

      // 需要同步chart.js的代码，以支持对时间段09:00-10:00的特殊处理
      return lineOptionFn(xList, yList, list.updateDate)
    },
  },
}
</script>
