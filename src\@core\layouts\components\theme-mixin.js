import router from '@/router'
import store from '@/store'
import { changeTheme, toggleLotus } from '@/util/utils'
import useAppConfig from '@core/@app-config/useAppConfig'
import { computed, getCurrentInstance } from '@vue/composition-api'
export default {
  setup() {
    const vm = getCurrentInstance().proxy
    // eslint-disable-next-line object-curly-newline
    const { appSkinVariant, isDark } = useAppConfig()

    const themeOption = computed(() => {
      return [
        {
          icon: 'icon-xitongzhutiseW',
          label: 'Auto',
          value: 'auto',
        },
        {
          icon: 'icon-xitongzhutiseW',
          label: 'Light',
          value: 'light',
        },
        {
          icon: 'icon-xitongzhutiseB',
          label: 'Dark',
          value: 'dark',
        },
        {
          icon: 'icon-xitongzhutiseB',
          label: 'Semi Dark',
          value: 'semi-dark',
        },
      ]
    })
    const appMode = computed({
      get() {
        return store.state.appConfig.globalThemeMode
        // return vm.$route.meta.isDark
        //   ? vm.$route.meta.isDark
        //   : appSkinVariant.value === 'semi-dark'
        //   ? appSkinVariant.value
        //   : isDark.value
      },
      set(newVal) {
        return newVal
      },
    })
    const onChangeTheme = value => {
      // if (['light', 'dark'].includes(value)) {
      //   isDark.value = value
      //   appSkinVariant.value = 'default'
      // } else {
      //   appSkinVariant.value = value
      //   isDark.value = false
      //   // vm.$route.meta.isDark
      // }
      store.commit('appConfig/UPDATE_GLOBAL_THEME_MODE', value)
      // 点击主题切换按钮时，先将 Primary 选择项切换为'default'解决样式冲突
      // if (value === 'semi-dark') {
      //   store.commit('appConfig/UPDATE_APP_PRIMARY', 'lotus')
      // } else {
      //   store.commit('appConfig/UPDATE_APP_PRIMARY', 'default')
      // }
      store.commit('appConfig/UPDATE_APP_PRIMARY', 'default')
      toggleLotus()
      const { isDark: currentDark, appSkinVariant: currentAppSkinVariant } =
        changeTheme(value, router.currentRoute.meta.isDark)
      isDark.value = currentDark
      appSkinVariant.value = currentAppSkinVariant
    }

    return {
      // Skin
      appSkinVariant,

      // Theme
      themeOption,
      onChangeTheme,
      appMode,
    }
  },
}
