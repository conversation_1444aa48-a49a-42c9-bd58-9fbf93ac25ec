<template>
  <vsoc-drawer
    v-model="isDrawerShow"
    width="380"
    :title="mode"
    @click:confirm="onSave"
  >
    <v-form ref="form" v-model="valid" lazy-validation>
      <div class="mt-6">
        <!-- <div class="text-base font-weight-semibold mb-4">
          {{ $t('model.headers.name') }}
        </div> -->
        <v-text-field
          v-model="editForm.name"
          :rules="nameRules"
          :label="$t('model.headers.name')"
          placeholder="eg. MEB"
          required
          dense
          autocomplete="off"
          color="primary"
          class="is-required"
        >
        </v-text-field>

        <!-- <div class="text-base font-weight-semibold my-4">
          {{ $t('model.headers.desc') }}
        </div> -->
        <v-text-field
          v-model="editForm.description"
          :label="$t('model.headers.desc')"
          required
          dense
          color="primary"
          class="my-6"
          :placeholder="$t('model.headers.desc')"
        >
        </v-text-field>

        <div class="text-base color-base font-weight-semibold mb-4">
          {{ $t('model.edit.picture') }}
        </div>
        <el-upload
          class="upload-demo platform__file"
          :headers="headers"
          :action="vsocPath + '/file/vehicleTypeUpload'"
          :on-preview="handlePreview"
          :on-remove="handleRemove"
          :on-success="handleSuccess"
          :file-list="attachments"
          list-type="picture-card"
        >
          <!-- :before-upload="handleBefore" -->
          <!-- accept="image/png, image/jpeg" -->
          <!-- :limit="1"
          :multiple="false" -->

          <!-- v-if="attachments.length === 0" -->
          <!-- <template>
            <i slot="default" class="el-icon-plus"></i>
            <v-btn color="primary">点击上传</v-btn>
            <div slot="tip" class="el-upload__tip">
              只能上传jpg/png文件，且不超过500kb
            </div>
          </template> -->

          <!-- class="icon--default" -->
          <v-icon color="primary" size="36">mdi-plus</v-icon>
          <template slot="file" slot-scope="{ file }"> </template>
        </el-upload>
        <!-- <el-upload
          ref="refUpload"
          :action="vsocPath + '/file/vehicleTypeUpload'"
          :headers="headers"
          :on-preview="handlePreview"
          :on-remove="handleRemove"
          :on-success="handleSuccess"
          :file-list="attachments"
          list-type="picture-card"
          :limit="1"
          :multiple="false"
        >
          <v-icon>mdi-plus</v-icon>
        </el-upload> -->
      </div>
    </v-form>
  </vsoc-drawer>
</template>

<script>
import { required } from '@/@core/utils/validation'
import { addVehiclePlatform, editVehiclePlatform } from '@/api/asset/platform'
import VsocDrawer from '@/components/VsocDrawer.vue'
import { getToken } from '@/util/token'
import { cloneDeep } from 'lodash'
import { vsocPath } from '../../../util/request'
export default {
  name: 'PlatformEdit',
  components: {
    VsocDrawer,
  },
  props: {
    item: {
      type: Object,
      required: false,
    },
    mode: {
      type: String,
      required: false,
      default: () => 'new',
    },
  },
  watch: {
    isDrawerShow: {
      handler(newVal) {
        if (newVal) {
          if (this.mode === 'new') {
            this.addOpen()
          } else {
            this.editOpen()
          }
        } else {
          this.attachments = []
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      headers: {
        Authorization: getToken(),
      },
      vsocPath,
      attachments: [],
      editForm: this.item,

      valid: true,
      isDrawerShow: false,

      nameRules: [v => required(v, this.$t('model.headers.name'))],
    }
  },
  methods: {
    handleBefore(file) {
      if (file.type !== 'image/png' && file.type !== 'image/jpeg') {
        this.$notify.info('error', '只能上传jpg/png文件！')
        return false
      }
    },
    handleRemove(file) {
      this.attachments = []
    },
    handlePreview(file) {
      window.open(file.url)
    },
    handleSuccess(response, file, fileList) {
      if (response.code === 200) {
        this.attachments = [
          {
            id: response.data,
            ...file,
          },
        ]
      } else {
        this.$notify.info('error', response.msg)
      }
    },
    addOpen() {
      this.attachments = [
        // 默认车型图片
        {
          id: 'vehicleType/<EMAIL>',
          name: 'default-car',
          url: `${
            window.location.href?.split('model')[0]
          }static/img/<EMAIL>`,
          // 更换图片时，yarn build，修改url
          // url: `http://${window.location.host}${
          //   process.env.NODE_ENV === 'development' ? '/vsocwebdev15/' : '/test/'
          // }static/img/<EMAIL>`,
          // url: `https://vsoc-1251622578.cos.ap-guangzhou.myqcloud.com/vehicleType/1769675931305115648-default.svg?q-sign-algorithm=sha1&q-ak=AKIDVs31pV3VuDQVWSLBzYWLD4oS16KYuQa3&q-sign-time=1694162152%3B1694163952&q-key-time=1694162152%3B1694163952&q-header-list=host&q-url-param-list=&q-signature=ee40aab1bd1c4bc830d3980f6f54b73f434e0df7`,
        },
      ]
      this.editForm = {
        name: '', //车型
        description: '', //描述
        fileId: '', //车型图片id
      }
      this.$refs.form.resetValidation()
      this.isDrawerShow = true
    },
    editOpen() {
      this.attachments = []
      this.editForm = cloneDeep(this.item)
      if (this.editForm.fileId && this.editForm.fileUrl) {
        this.attachments = [
          {
            id: this.editForm.fileId,
            name: this.editForm.fileId,
            url: this.editForm.fileUrl,
          },
        ]
      }
      this.isDrawerShow = true
    },
    async onSave(callback) {
      try {
        const bool = this.$refs.form.validate()
        if (!bool) {
          callback(false, true)
          return
        }
        if (!this.attachments || this.attachments.length === 0) {
          this.$notify.info('error', this.$t('global.file.uploadFile'))
          callback(false, true)
          return
        }
        this.editForm.fileId = this.attachments.map(v => v.id).join(',')
        if (this.mode === 'new') {
          await addVehiclePlatform(this.editForm)
          this.$notify.info(
            'success',
            this.$t('global.hint.add', [this.$t('model.currentTitle')]),
          )
        }
        if (this.mode === 'edit') {
          await editVehiclePlatform(this.editForm)
          this.$notify.info(
            'success',
            this.$t('global.hint.edit', [this.$t('model.currentTitle')]),
          )
        }
        this.$emit('refresh')
        this.attachments = []
        callback()
      } catch (err) {
        callback(false, true)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.icon--default {
  background: url('../../../assets/images/pages/<EMAIL>')
    center/cover;
  width: 100%;
  height: 100%;
}
</style>
