<template>
  <div>
    <bread-crumb
      ref="topHeader"
      :showDoQuery="showDoQuery"
      :filterList="filterList"
      :showAdvance="true"
    >
    </bread-crumb>

    <!-- <div
      ref="filterList"
      class="rounded-0 d-flex align-center justify-lg-space-between py-1 px-2"
      :class="{ 'pb-2': filterList.length === 0 }"
    >
      <v-chip-group v-if="filterList.length" class="w-90 pa-0 ma-0">
        <v-chip
          v-for="(item, index) in filterList"
          :key="index"
          small
          pill
          dense
          class="pa-0 active-chip rounded-pill color-base bg-white px-4"
          close
          close-icon="mdi-close"
          @click:close="$_clearFilter(item, index)"
        >
          {{ item.text }}
        </v-chip>
      </v-chip-group>
      <v-btn
        v-if="filterList.length"
        color="primary"
        x-small
        rounded
        class="px-4 py-1 rounded-pill text-base"
        @click="onReset"
      >
        <v-icon left> mdi-eraser </v-icon>
        {{ $t('action.clear') }}
      </v-btn>
    </div> -->

    <v-card tile class="main-content">
      <v-card-text class="pa-0">
        <div class="d-flex justify-space-between align-center flex-row-reverse">
          <div class="d-flex align-end">
            <table-search
              :searchList="searchList"
              :searchQuery="query"
              @search="$_search"
            ></table-search>
            <!-- <vsoc-date-range
              v-model="dateRange.range"
              no-title
              @input="onChangeDate"
            >
              <template v-slot:text="{ on, attrs }">
                <v-text-field
                  type="button"
                  clearable
                  outlined
                  dense
                  class="append-icon-max me-3 date-width"
                  readonly
                  hide-details
                  color="primary"
                  large
                  :label="$t('loginLog.headers.accessDate')"
                  prepend-inner-icon="mdi-calendar-range-outline"
                  :value="RANGE_STR(query.startDate, query.endDate)"
                  @click:clear="onChangeDate({ start: '', end: '' })"
                ></v-text-field>
              </template>
            </vsoc-date-range>

            <v-text-field
              v-model="query.userId"
              class="me-3 text-width"
              outlined
              dense
              clearable
              hide-details
              color="primary"
              :label="$t('loginLog.headers.name')"
              large
              @keyup.enter.native="$_search"
              @click:clear="onClear"
            ></v-text-field>

            <v-btn color="primary--text bg-btn" elevation="0" @click="$_search">
              <span>{{ $t('action.search') }}</span>
            </v-btn> -->
          </div>
          <div class="d-flex justify-end align-center">
            <!-- @click="add" -->
            <v-btn
              elevation="0"
              color="primary"
              @click="showExport = true"
              v-has:login-log-export
            >
              <span>
                {{
                  $generateMenuTitle($route.meta.buttonInfo['login-log-export'])
                }}
              </span>
            </v-btn>
            <!--
            <v-menu
              offset-y
              left
              nudge-bottom="4"
              transition="scale-transition"
              content-class="menu-list"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-btn
                  class="px-2 py-0 d-flex align-center"
                  color="primary"
                  dark
                  v-bind="attrs"
                  v-on="on"
                >
                  <span class="ml-2 mr-3" style="color: $primary"
                    >批量登录</span
                  >
                  <v-icon size="12px">mdi-chevron-down</v-icon>
                </v-btn>
              </template>

              <v-list>
                <v-list-item
                  v-for="(item, i) in menuItems"
                  :key="i"
                  class="list-hover"
                >
                  <v-list-item-title>
                    {{ item.title }}
                  </v-list-item-title>
                </v-list-item>
              </v-list>
            </v-menu> -->
          </div>
        </div>
        <v-data-table
          fixed-header
          :items-per-page="query.pageSize"
          item-key="id"
          :height="tableHeight"
          hide-default-footer
          :headers="headers"
          :items="tableData"
          class="table border-radius-xl mt-3 thead-light"
          :loading="tableLoading"
        >
          <!-- <template v-slot:item.userId="{ item }">
            <v-chip small color="primary" label>
              {{ item.userId }}
            </v-chip>
          </template> -->
          <template v-slot:item.msg="{ item }">
            <div v-show-tips style="max-width: 10rem">{{ item.msg }}</div>
          </template>

          <template v-slot:item.address="{ item }">
            <div style="max-width: 16rem" v-show-tips>
              {{ item.address | dataFilter }}
            </div>
          </template>

          <template v-slot:item.accessDate="{ item }">
            <span v-show-tips>{{ item.accessDate | toDate }}</span>
          </template>
          <template v-slot:item.status="{ item }">
            <!-- <v-chip
              v-if="statusEnum[item.status]"
              v-show-tips="statusEnum[item.status].text"
              small
              :color="statusEnum[item.status].color"
              label
            >
              {{ statusEnum[item.status].text }}
            </v-chip> -->
            <v-icon
              v-if="statusEnum[item.status]"
              v-show-tips="item.statusName"
              size="1.5rem"
              :color="statusEnum[item.status].color"
            >
              {{ statusEnum[item.status].icon }}
            </v-icon>
          </template>
        </v-data-table>

        <vsoc-pagination
          :page.sync="query.pageNum"
          :size.sync="query.pageSize"
          :total="tableDataTotal"
          @change-page="getTableData"
          @change-size="getTableData"
        >
        </vsoc-pagination>
      </v-card-text>
    </v-card>
    <login-drawer
      ref="advancedDrawer"
      v-model="showAdvance"
      @do-query="doQuery"
    ></login-drawer>

    <vsoc-dialog
      v-model="showExport"
      dense
      width="400"
      :title="$generateMenuTitle($route.meta.buttonInfo['login-log-export'])"
      @click:confirm="onExport"
    >
      <v-form ref="form" v-model="valid">
        <vsoc-date-range
          ref="refRange"
          v-model="dateRange1.range"
          no-title
          :menu-props="dateRange1.menuProps"
          @input="onChangeDate1"
        >
          <template v-slot:text="{ on, attrs }">
            <v-text-field
              clearable
              style="width: 370px"
              outlined
              dense
              class="me-3 pa-0"
              readonly
              persistent-hint
              :hint="$t('global.hint.maxExportCount')"
              color="primary"
              large
              :label="$t('loginLog.dateRange')"
              append-icon="mdi-calendar-range-outline"
              v-bind="attrs"
              v-on="on"
              :value="RANGE_STR(exportQuery.startDate, exportQuery.endDate)"
              @click:clear="onChangeDate1({ start: '', end: '' })"
              :rules="[v => required(v, $t('loginLog.dateRange'))]"
            ></v-text-field>
          </template>
        </vsoc-date-range>
      </v-form>
    </vsoc-dialog>
  </div>
</template>

<script>
import { required } from '@/@core/utils/validation'
import { exportLoginLog, getLoginLog } from '@/api/log/login'
import VsocDialog from '@/components/VsocDialog.vue'
import VsocPagination from '@/components/VsocPagination.vue'
import breadCrumb from '@/components/bread-crumb/index'
import VsocDateRange from '@/components/vsoc-date-range/VsocDateRange.vue'
import { RANGE_STR } from '@/components/vsoc-date-range/constants'
import { PAGESIZE } from '@/util/constant'
import { clearFilterItem, setRemainingHeight } from '@/util/utils'
import { differenceInDays } from 'date-fns'
import { cloneDeep } from 'lodash'
import LoginDrawer from './LoginDrawer.vue'
import TableSearch from '@/components/TableSearch/index.vue'

export default {
  name: 'LoginLog',
  components: {
    breadCrumb,
    VsocPagination,
    VsocDateRange,
    LoginDrawer,
    VsocDialog,
    TableSearch,
  },
  data() {
    return {
      required,
      exportQuery: {
        startDate: '',
        endDate: '',
      },
      dateRange1: {
        range: {},
        menuProps: { offsetY: true, closeOnContentClick: false },
      },
      showExport: false,
      valid: false,
      filterList: [],
      dateRange: {
        range: {},
      },
      showAdvance: false,
      query: {
        startDate: null,
        endDate: null,
        userId: '',
        status: '',
        ipAddress: '',
        pageNum: 1,
        pageSize: PAGESIZE,
      },

      tableData: [],
      tableHeight: '34.5rem',
      tableLoading: false,
      btnLoading: false,
      tableDataTotal: 0,
    }
  },
  created() {
    this.$_setTableHeight()
    this.$bus.$on('resize', this.$_setTableHeight)
    this.getTableData()
  },
  beforeDestroy() {
    this.$bus.$off('resize', this.$_setTableHeight)
  },
  computed: {
    searchList() {
      return [
        {
          type: 'date',
          value: ['startDate', 'endDate'],
          text: this.$t('loginLog.headers.accessDate'),
          dateRange: {
            range: {
              start: '',
              end: '',
            },
            menuProps: { offsetY: true, closeOnContentClick: false },
          },
        },
        {
          type: 'input',
          value: 'userId',
          text: this.$t('loginLog.headers.name'),
        },
      ]
    },
    headers() {
      return [
        {
          text: this.$t('loginLog.headers.id'),
          value: 'id',
          width: 100,
        },
        {
          text: this.$t('loginLog.headers.name'),
          value: 'userId',
          width: 120,
        },
        {
          text: this.$t('loginLog.headers.ip'),
          value: 'ipAddress',
          width: 160,
        },
        {
          text: this.$t('loginLog.headers.address'),
          value: 'address',
          width: 200,
        },
        {
          text: this.$t('loginLog.headers.browser'),
          value: 'browser',
          width: 100,
        },
        {
          text: this.$t('loginLog.headers.system'),
          value: 'operatingSystem',
          width: 120,
        },
        {
          text: this.$t('loginLog.headers.status'),
          value: 'status',
          width: 100,
        },
        {
          text: this.$t('loginLog.headers.msg'),
          value: 'msg',
          width: 100,
        },
        {
          text: this.$t('loginLog.headers.accessDate'),
          value: 'accessDate',
          width: 160,
        },
      ]
    },
    statusEnum() {
      return this.$store.getters['enums/getOperateStatus']
    },
    // 资产类型
    assetTypeEnum() {
      return this.$store.state.enums.enums.AssetType
    },
  },
  watch: {
    filterList() {
      this.$_setTableHeight()
    },
    '$store.state.enums.enums': {
      handler() {
        this.$_appendFilterListItem()
      },
      deep: true,
    },
  },
  methods: {
    onClear() {
      this.query.userId = ''
      this.$_search()
    },
    //全部导出
    async onExport(callBack) {
      if (!this.$refs.form.validate()) return callBack(false, true)
      const days = differenceInDays(
        new Date(this.exportQuery.endDate),
        new Date(this.exportQuery.startDate),
      )
      if (days > 31) {
        this.$notify.info('error', this.$t('global.hint.monthMore1'))
        return callBack(false, true)
      }
      try {
        const file = await exportLoginLog(this.exportQuery)
        const xlsx = 'application/vnd.ms-excel;charset=utf-8'
        const blob = new Blob([file], { type: xlsx })
        //转换数据类型
        const a = document.createElement('a') // 转换完成，创建一个a标签用于下载
        a.download = `${this.$t('loginLog.currentTitle')}(${
          this.exportQuery.startDate
        }~${this.exportQuery.endDate}).xlsx`
        a.href = URL.createObjectURL(blob)
        document.body.appendChild(a)
        a.click()
        a.remove()
        callBack()
      } catch (err) {
        this.$notify.info('error', err)
        callBack(false, true)
      }
    },
    // 导出时间改变
    onChangeDate1(range) {
      this.dateRange1.range = range
      this.exportQuery.startDate = range.start
      this.exportQuery.endDate = range.end
    },
    onReset() {
      Object.assign(this.$data.query, this.$options.data.call(this).query)
      this.$_search()
    },
    onDetail(record) {
      this.showDetail = true
      this.currentRecord = record
    },
    RANGE_STR,
    // 首次登记时间改变
    onChangeDate(range) {
      this.dateRange.range = range
      this.query.startDate = range.start
      this.query.endDate = range.end
    },
    // 展示高级查询
    showDoQuery() {
      this.showAdvance = true

      let advanceQuery = cloneDeep(this.query)

      delete advanceQuery.pageNum
      delete advanceQuery.pageSize
      this.$refs.advancedDrawer.advanceQuery = advanceQuery
    },
    //  高级查询
    doQuery(advanceQuery) {
      this.query = Object.assign(this.query, cloneDeep(advanceQuery))
      this.dateRange.range = {
        start: this.query.startDate,
        end: this.query.endDate,
      }
      this.$_search()
    },
    async getTableData() {
      try {
        this.tableLoading = true
        this.tableData = []
        const { data } = await getLoginLog(this.query)
        this.tableData = data.records
        // this.tableData = await this.getLocaltion(data.records)
        this.tableDataTotal = data.total
      } catch (e) {
        console.error(`获取组关系实例：${e}`)
      }
      this.tableLoading = false
    },

    $_search() {
      this.query.pageNum = 1
      this.getTableData()
      this.$_appendFilterListItem()
    },
    // 添加查询条件
    $_appendFilterListItem() {
      const searchList = cloneDeep(this.query)
      delete searchList.pageNum
      delete searchList.pageSize
      delete searchList.startDate
      delete searchList.endDate
      delete searchList.userId
      this.filterList =
        this.$refs.advancedDrawer &&
        this.$refs.advancedDrawer.setFilterList(searchList)
      // const searchKeyList = [
      //   {
      //     key: 'userId',
      //     type: 'String',
      //   },
      //   {
      //     key: 'status',
      //     type: 'String',
      //   },
      //   {
      //     key: 'ipAddress',
      //     type: 'String',
      //   },
      // ]
      // this.$_setTableHeight()
      // handleFilterItem.call(this, searchKeyList)
      // // 状态
      // const index = this.filterList.findIndex(v => v.key === 'status')
      // if (index !== -1) {
      //   const value = this.query.status
      //   this.filterList[index].text = this.statusEnum[value]?.text
      // }
    },
    // 清除某个查询条件
    $_clearFilter(item) {
      const bool = clearFilterItem.call(this, item)
      if (!bool) {
        this.$_search()
      }
    },

    // 设置表格高度
    $_setTableHeight() {
      this.$nextTick(() => {
        const fn = () => {
          return -this.$refs.topHeader.filterHeight
        }
        this.tableHeight = setRemainingHeight(fn)
      })
    },
  },
}
</script>
