const tool = {
  event: '事件',
  previousState: '历史状态',
  value: '值',
  list: '列表',
  formula: '公式',
  abnormal: '异常',
  currentMessage: '最新消息',
  currentState: '当前状态',
  abnormal: '异常值',
  anomaly: {
    tip: '以上单位检测',
    tip1: '最大限制',
  },
  createFormula: {
    name: '公式名称',
    type: '表达式类型',
    expression: '表达式',
    tip: '选择现有公式',
    tip1: '创建表达式。使用“添加变量”按钮来引用来自数字孪生的信号的动态值。可以使用以下字符+,-,*,/,(,)',
    tip2: '使用验证表达式按钮将验证表达式返回类型与合法性',
    tip3: '校验通过',
    tip4: '公式名称重复',
    tip5: '公式名称只能以字母开头的字母、数字和下划线组成',
    tip6: '变量数量已达上限',
    tip7: '请为公式中使用的所有变量选择源和信号',
    tip8: '计算值为{0}类型，不能与{1}进行比较',
  },
  hint: {
    tip: '仅由当前消息触发时应用',
    tip1: '该逻辑条件不完整，请填写或删除',
    tip2: '至少填写一条完整的逻辑',
    tip3: '请输入一个数字类型的值',
    tip4: '请输入一个整数类型的值',
    tip5: '值的有效值范围在{0}至{1}之间',
    tip6: '值的有效值范围不能小于{0}',
    tip7: '值的有效值范围不能大于{0}',
    tip8: '时间间隔超出最大限制',
  },
}
export default tool
