import { cmdbPath, postForm, request, vsocPath } from '../../util/request'

// 获取消息列表
export const getMessageList = function (params) {
  return request({
    url: `${vsocPath}/my-msg`,
    method: 'get',
    params,
  })
}

// 读取消息
export const makeRead = function (params) {
  return postForm(`${vsocPath}/my-msg/markRead`, params)
}

// 获取未读消息数量
export const getUnreadCount = function () {
  return request({
    url: `${vsocPath}/my-msg/unreadCount`,
    method: 'get',
  })
}

// 发消息
export const sendMessage = function (data) {
  return request({
    url: `${vsocPath}/msg`,
    method: 'post',
    data,
  })
}

// 获取所有组名
export const getGroupAll = function () {
  return request({
    url: `${vsocPath}/group/all-simple`,
    method: 'get',
  })
}

// 获取所有用户名
export const getUserNames = function () {
  return request({
    url: `${vsocPath}/user/getAllUserNames`,
    method: 'get',
  })
}

export const getEnumList = function () {
  return request({
    url: `${vsocPath}/dict/findAllDict`,
    method: 'post',
  })
}

// 查询config表
export const getConfig = function (type) {
  return request({
    url: `${cmdbPath}/config/${type}`,
    method: 'get',
  })
}

// 查询枚举表
export const getEnum = function () {
  return request({
    url: `${cmdbPath}/cmdb-enum/metadata`,
    method: 'get',
    params: {
      types:
        'AssetType,ActiveStatus,AlarmLevel,AlarmStatus,DetectorActionMode,DetectorExtent,DetectorType,DisposeActionLogStatus,DisposeActionType,HealthStatus,KafkaType,SignalType,SignalValueType',
    },
  })
}

//获取所有参数值
export const findAllConfigProperty = function () {
  return request({
    url: `${vsocPath}/configProperty/findAllConfigProperty`,
    method: 'post',
  })
}
