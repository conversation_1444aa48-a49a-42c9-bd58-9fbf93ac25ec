<template>
  <div :class="isLightTheme === 'true' ? 'data-view-4' : 'data-view-5'">
    <div class="data-view-1" v-resize="getScreenSize">
      <dv-loading v-if="isLoading">Loading...</dv-loading>
      <div
        class="dv-full-screen-container-1"
        :style="{
          backgroundImage:
            'url(' + imgs[Number(canvasItem.backgroundImage) - 5] + ')',
        }"
        v-else
      >
        <div
          v-if="!isFull && !isFullScreen"
          class="main-edit main-edit-1 w-100 d-flex align-center justify-space-between"
        >
          <div class="d-flex align-center edit-tip font-weight-semibold-light">
            <v-btn icon elevation="0" @click="goback">
              <vsoc-icon
                :size="$getCeilSize(24)"
                icon="icon-quxiao1"
              ></vsoc-icon>
            </v-btn>
            <div class="ml-3 fs-22">
              {{
                showCard || showGrid
                  ? $t('grid.screen.edit')
                  : $t('grid.screen.view')
              }}
            </div>
          </div>
          <div class="d-flex">
            <v-btn
              elevation="0"
              class="ml-6"
              color="rgba(168, 168, 168, 0.2)"
              @click="openGrid"
            >
              <vsoc-icon
                :size="$getCeilSize(24)"
                icon="icon-xuanzemoban"
              ></vsoc-icon>
              <span class="ml-3 fs-14-1" style="color: #fff">
                {{ $t('grid.screen.btn1') }}
              </span>
            </v-btn>
            <v-btn
              elevation="0"
              class="ml-6"
              color="rgba(168, 168, 168, 0.2)"
              @click="openCard"
            >
              <vsoc-icon
                :size="$getCeilSize(24)"
                icon="icon-shejikanban1"
              ></vsoc-icon>
              <span class="ml-3 fs-14-1" style="color: #fff">
                {{ $t('grid.screen.btn') }}
              </span>
            </v-btn>
            <v-btn
              elevation="0"
              class="ml-6"
              color="rgba(168, 168, 168, 0.2)"
              @click="cancelCard"
              :loading="saveLoading"
            >
              <vsoc-icon
                style="color: #fff"
                :size="$getCeilSize(24)"
                icon="icon-guanbi-copy"
              ></vsoc-icon>
              <span class="ml-3 fs-14-1" style="color: #fff">
                {{ $t('action.reset') }}
              </span>
            </v-btn>
            <v-btn
              elevation="0"
              class="ml-6"
              color="rgba(168, 168, 168, 0.2)"
              @click="saveCard"
              :loading="saveLoading"
            >
              <vsoc-icon
                :size="$getCeilSize(24)"
                icon="icon-baocun1"
              ></vsoc-icon>
              <span class="ml-3 fs-14-1" style="color: #fff">
                {{ $t('action.save') }}
              </span>
            </v-btn>
          </div>
        </div>
        <div class="main-layout main-layout-1">
          <div class="main-header">
            <div class="title-date d-flex align-center">
              <span>{{ dateYear }}</span
              ><span class="ml-1">{{ dateDay }}</span
              ><span class="ml-1"> {{ dateWeek }}</span>
            </div>
            <div v-if="continueDay" class="title-date1 d-flex align-center">
              {{ lang === 'en' ? continueObj.itemEnName : continueObj.itemName
              }}<span class="title-header-day mx-2">{{ continueDay }}</span
              ><span>{{ $t('global.time.day1') }}</span>
            </div>
            <div
              v-else-if="isAnhuiMap"
              class="title-date1 title-date2 d-flex align-center font-weight-semibold"
            >
              安徽省通信管理局
            </div>
            <dv-decoration-8
              @click="goIndex"
              class="header-left-decoration mh-high"
              :color="[primary, primary]"
            />

            <dv-decoration-5
              class="header-center-decoration"
              :color="[primary, primary]"
            />
            <dv-decoration-8
              class="header-right-decoration"
              :reverse="true"
              :color="[primary, primary]"
            />
            <div class="mh-title" @click="goIndex">
              {{ lang === 'en' ? canvasItem.titleEnName : canvasItem.title }}
            </div>
            <div class="mh-date d-flex justify-space-between align-center">
              <!-- <span class="text-center"
              >{{ dateYear }} <v-spacer></v-spacer> {{ dateDay }}
              {{ dateWeek }}</span
            > -->
              <v-menu offset-y left nudge-bottom="5">
                <template v-slot:activator="{ on, attrs }">
                  <v-btn
                    elevation="0"
                    small
                    v-bind="attrs"
                    class="d-flex align-center text-base date-option"
                    v-on="on"
                  >
                    <template>
                      <span class="fs-14-1">{{ currentText }}</span>
                      <v-icon class="ml-1">mdi-chevron-down</v-icon>
                    </template>
                  </v-btn>
                </template>
                <v-list class="pa-0 model-list">
                  <!-- mandatory -->
                  <v-list-item-group
                    mandatory
                    :value="currentDate"
                    @change="onSelectData1"
                  >
                    <v-list-item
                      v-for="item in dateOption"
                      :key="item.value"
                      :value="item.value"
                    >
                      <v-list-item-title class="fs-14-1">{{
                        item.text
                      }}</v-list-item-title>
                    </v-list-item>
                  </v-list-item-group>
                </v-list>
              </v-menu>

              <v-combobox
                v-if="!isAnhuiMap"
                v-model="currentTypeList"
                dense
                hide-details
                multiple
                append-icon="mdi-chevron-down"
                class="date-combox"
                :items="vehicleType"
                item-text="text"
                item-value="value"
                :menu-props="{ offsetY: true }"
                @blur="onSelectData"
                @change="changeVehicles"
                :return-object="false"
                item-color="#44e2fe"
              >
                <template v-slot:selection="{ index }">
                  <span v-if="index === 0" class="check-text fs-14-1">
                    {{ $t('global.selected') }}{{ currentTypeList.length }}
                  </span>
                </template>
              </v-combobox>

              <!-- <v-menu offset-y left nudge-bottom="5">
              <template v-slot:activator="{ on, attrs }">
                <v-btn
                  elevation="0"
                  small
                  v-bind="attrs"
                  class="d-flex align-center text-base date-option"
                  v-on="on"
                >
                  <template>
                    <span class="fs-14-1">{{ currentTypeText }}</span>
                    <v-icon class="ml-1">mdi-chevron-down</v-icon>
                  </template>
                </v-btn>
              </template>
              <v-list class="pa-0 model-list">
                <v-list-item-group
                  multiple
                  mandatory
                  :value="currentTypeList"
                  @blur="onSelectData"
                >
                  <v-list-item
                    v-for="item in vehicleType"
                    :key="item.id"
                    :value="item.id"
                  >
                    <v-list-item-title class="fs-14-1">{{
                      item.name
                    }}</v-list-item-title>
                  </v-list-item>
                </v-list-item-group>
              </v-list>
            </v-menu> -->
              <v-btn v-if="isShowJump" icon @click.stop="jumpPage">
                <vsoc-icon
                  :icon="'icon-tiaozhuan'"
                  class="color--primary tianzhuan-icon"
                  type="fill"
                ></vsoc-icon>
              </v-btn>

              <v-btn icon @click="toggleFullScreen">
                <vsoc-icon
                  v-if="!isFull && !isFullScreen"
                  :size="$getCeilSize(30)"
                  :icon="'icon-daxiaopingqiehuan-1'"
                ></vsoc-icon>
                <v-icon v-else :size="$getCeilSize(30)" :color="primary"
                  >mdi-power</v-icon
                >
              </v-btn>
              <v-btn v-has:canvas-share icon @click="shareScreen">
                <vsoc-icon
                  :size="$getCeilSize(30)"
                  :icon="'icon-fenxiang'"
                ></vsoc-icon>
              </v-btn>
            </div>
          </div>
          <div class="main-container">
            <div
              ref="layout-box"
              class="layout-box"
              :style="{ width: showCard || showGrid ? '75%' : '100%' }"
            >
              <grid-layout
                :layout="layoutData"
                :col-num="layoutColNum"
                :row-height="rowHeight"
                :minRows="minRows"
                :is-draggable="isDraggable"
                :is-resizable="isResizable"
                :is-mirrored="false"
                :vertical-compact="true"
                :margin="[marginBoder, marginBoder]"
                :use-css-transforms="true"
                @layout-updated="layoutUpdatedEvent"
                @layout-ready="layoutReadyEvent"
              >
                <grid-item
                  v-for="item in layoutData"
                  :x="item.x"
                  :y="item.y"
                  :w="item.w"
                  :h="item.h"
                  :i="item.i"
                  :minW="item.minW"
                  :minH="item.minH"
                  :key="item.i"
                  @resized="resizedEvent"
                  :class="item.isBorder === '1' ? 'grid-border' : ''"
                >
                  <!-- 定制化图形*1 -->
                  <!-- <card-border
                  v-if="item.chartValue === '0'"
                  :ref="'comp' + item.i"
                  :isBorder="item.isBorder"
                  :showDel="showDel"
                  @deleteCard="deleteCard(item.i)"
                  :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                >
                  <card-item1
                    :title="lang === 'en' ? item.itemEnName : item.itemName"
                    :list="item.value"
                  ></card-item1>
                </card-border> -->
                  <!-- 定制化态势 -->

                  <card-border
                    v-if="item.chartValue === '11'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item12
                      :refreshDate="item.value.time"
                      :posture="item.value.posture"
                    ></card-item12>
                  </card-border>

                  <!-- 定制化图形*2 -->

                  <card-border
                    v-if="item.chartValue === '1'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item2
                      :isFull="!isFull && !isFullScreen"
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :list="item.value"
                    ></card-item2>
                  </card-border>

                  <card-border
                    v-if="item.chartValue === '18'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item18
                      :isFull="!isFull && !isFullScreen"
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :list="item.value"
                    ></card-item18>
                  </card-border>

                  <card-border
                    v-if="item.chartValue === '19'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item19
                      :isFull="!isFull && !isFullScreen"
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :list="item.value"
                      :echartId="item.itemKey"
                    ></card-item19>
                  </card-border>

                  <!-- 定制化图形*3 车型-->
                  <card-border
                    v-if="item.chartValue === '2'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item3
                      :isFull="!isFull && !isFullScreen"
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :list="item.value"
                    ></card-item3>
                  </card-border>
                  <!-- 定制化图形*4 -->
                  <card-border
                    v-if="item.chartValue === '3'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item4
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :list="item.value"
                      :isFull="!isFull && !isFullScreen"
                      :headers1="item.headers1"
                    ></card-item4>
                  </card-border>

                  <!-- 环形图 -->
                  <card-border
                    v-if="item.chartValue === '13'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item13
                      :echartId="item.itemKey"
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :list="item.value"
                      :isFull="!isFull && !isFullScreen"
                    ></card-item13>
                  </card-border>

                  <!-- 安徽地图 -->
                  <card-border
                    v-if="item.chartValue === '14'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="false"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item14
                      :showType="item.type"
                      :echartId="item.itemKey"
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :toData="item.value"
                      :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                      :width="item.w * colWidth + (item.w - 1) * marginBoder"
                      @changeMap="changeMap(item, $event)"
                    ></card-item14>
                  </card-border>

                  <!-- 条形图3 上下布局 -->
                  <card-border
                    v-if="item.chartValue === '15'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item15
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :list="item.value"
                      :isFull="!isFull && !isFullScreen"
                      :itemKey="item.itemKey"
                    ></card-item15>
                  </card-border>

                  <!-- 双环形图 -->
                  <card-border
                    v-if="item.chartValue === '16'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item16
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :list="item.value"
                      :isFull="!isFull && !isFullScreen"
                    ></card-item16>
                  </card-border>

                  <!-- 横向柱状图 -->
                  <card-border
                    v-if="item.chartValue === '17'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item17
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :list="item.value"
                      :echartId="item.itemKey"
                    ></card-item17>
                  </card-border>

                  <!-- 条形图1 -->
                  <card-border
                    v-if="item.chartValue === '4'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item5
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :list="item.value"
                    ></card-item5>
                  </card-border>

                  <!-- 条形图2 -->
                  <card-border
                    v-if="item.chartValue === '5'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item6
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :total="item.value.total"
                      :list="item.value.list"
                    ></card-item6>
                  </card-border>

                  <!-- 折线图-基础面积图 -->
                  <card-border
                    v-if="item.chartValue === '6'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <!-- :xList="xList" -->
                    <card-item7
                      :echartId="item.itemKey"
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :list="item.value"
                    ></card-item7>
                  </card-border>

                  <!-- 折线图-基础平滑折线图 -->
                  <card-border
                    v-if="item.chartValue === '7'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item8
                      :echartId="item.itemKey"
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :total="item.value.total"
                      :list="item.value.list"
                    ></card-item8>
                  </card-border>

                  <!-- 空心环形图1 -->
                  <card-border
                    v-if="item.chartValue === '8'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item9
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :list="item.value"
                      :vehicleType="vehicleType"
                      :currentTypeList="currentTypeList"
                    ></card-item9>
                  </card-border>

                  <!-- 空心环形图2 -->
                  <card-border
                    v-if="item.chartValue === '9'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item10
                      :echartId="item.itemKey"
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :total="item.value.total"
                      :list="item.value.list"
                      :isFull="!isFull && !isFullScreen"
                    ></card-item10>
                  </card-border>

                  <!-- 右侧显示环形图 -->
                  <card-border
                    v-if="item.chartValue === '20'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item20
                      :echartId="item.itemKey"
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :total="item.value.total"
                      :list="item.value.list"
                      :isFull="!isFull && !isFullScreen"
                    ></card-item20>
                  </card-border>

                  <!-- 定制化Map -->
                  <card-border
                    v-if="item.chartValue === '10'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item11
                      :echartId="item.itemKey"
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :toData="item.value"
                      :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                      :width="item.w * colWidth + (item.w - 1) * marginBoder"
                    ></card-item11>
                  </card-border>
                </grid-item>
              </grid-layout>
            </div>
          </div>

          <aside v-if="showCard" class="aside-box">
            <v-card class="w-100 h-100 theme--dark">
              <v-list class="theme--dark">
                <v-subheader class="d-flex align-center justify-space-between">
                  <div>{{ $t('grid.screen.select') }}</div>
                  <v-btn icon @click.stop="closeCard">
                    <vsoc-icon
                      :size="$getCeilSize(32)"
                      type="fill"
                      style="color: #ffffff"
                      icon="icon-guanbi"
                    ></vsoc-icon>
                  </v-btn>
                </v-subheader>
                <v-text-field
                  v-model="editForm.itemName"
                  :label="$t('grid.headers.name')"
                  color="primary"
                  outlined
                  dense
                  class="mt-4 theme--dark"
                  hide-details
                  append-icon="mdi-magnify"
                  large
                  clearable
                  @change="intCardList"
                  @click:clear="resetCard"
                  @keyup.enter.native="intCardList"
                >
                </v-text-field>
                <v-list-item-group
                  v-model="selectItems"
                  multiple
                  class="theme--dark"
                >
                  <v-list-item
                    v-for="(item, i) in resourcesData"
                    :key="i"
                    class="theme--dark"
                    :value="item.i"
                  >
                    <template v-slot:default="{ active }">
                      <div
                        class="list-item d-flex align-center w-100"
                        @click="addCard(item, active)"
                      >
                        <vsoc-icon
                          :size="$getCeilSize(32)"
                          :icon="chartEnum[item.chartValue].icon"
                        ></vsoc-icon>
                        <v-list-item-content class="ml-3">
                          {{ lang === 'en' ? item.itemEnName : item.itemName }}
                        </v-list-item-content>
                      </div>
                    </template>
                  </v-list-item>
                </v-list-item-group>
              </v-list>
            </v-card>
          </aside>

          <!-- 分享showShare -->
          <aside
            v-if="showShare"
            :style="{
              height: !isFull && !isFullScreen ? 'calc(100vh - 8%)' : '100vh',
            }"
            class="aside-box share-box"
          >
            <v-card class="w-100 h-100 theme--dark">
              <v-list class="theme--dark">
                <v-subheader class="d-flex align-center justify-space-between">
                  <div>{{ $t('action.share') }}</div>
                  <v-btn icon @click.stop="showShare = false">
                    <vsoc-icon
                      :size="$getCeilSize(32)"
                      type="fill"
                      style="color: #ffffff"
                      icon="icon-guanbi"
                    ></vsoc-icon>
                  </v-btn>
                </v-subheader>
                <div class="mt-6 switch-box d-flex justify-space-between">
                  <v-text-field
                    outlined
                    v-model="shareToken"
                    class="theme--dark"
                    dense
                    label="Token"
                    hide-details
                  >
                  </v-text-field>
                  <v-btn class="item-text ml-3" @click.stop="changeShareToken">
                    {{ $t('grid.link') }}
                  </v-btn>
                  <!-- <div class="item-text item-color">
                  {{ $t('grid.share.btn') }}
                </div>
                <v-switch
                  class="ma-0 pa-0"
                  v-model="switchShow"
                  hide-details
                  color="#44e2fe"
                  @change="changeSwitch"
                ></v-switch> -->
                </div>
                <div class="mt-6 item-color item-text">
                  {{ $t('grid.headers.url') }}
                </div>
                <div class="mt-2 item-color item-text1">
                  {{ $t('grid.share.tip') }}
                </div>
                <v-text-field
                  class="mt-6"
                  outlined
                  v-model="shareUrl"
                  dense
                  hide-details
                  :disabled="!shareUrl"
                >
                </v-text-field>
                <v-btn
                  class="mt-6 item-text w-100"
                  v-copy="shareUrl"
                  :disabled="!shareUrl"
                >
                  {{ $t('grid.share.copyBtn') }}
                </v-btn>
              </v-list>
            </v-card>
          </aside>

          <!-- 选择默认布局 -->
          <aside v-if="showGrid" class="aside-box grid-box">
            <v-card class="w-100 h-100 theme--dark">
              <v-list class="theme--dark">
                <v-subheader class="d-flex align-center justify-space-between">
                  <div>{{ $t('grid.screen.grid') }}</div>
                  <v-btn icon @click.stop="closeGrid">
                    <vsoc-icon
                      :size="$getCeilSize(32)"
                      type="fill"
                      style="color: #ffffff"
                      icon="icon-guanbi"
                    ></vsoc-icon>
                  </v-btn>
                </v-subheader>

                <v-list-item-group v-model="selectGrid" class="theme--dark">
                  <v-list-item
                    v-for="(item, i) in resourceGrid"
                    :key="i"
                    class="theme--dark"
                    :value="item.type"
                  >
                    <template v-slot:default="{ active }">
                      <div
                        class="d-flex align-center w-100"
                        @click="changeGrid(item.type, active)"
                      >
                        <v-img
                          height="100%"
                          max-width="100%"
                          :src="require(`./images/grid${item.type}.png`)"
                        >
                          <template v-slot:placeholder>
                            <v-progress-circular
                              indeterminate
                              color="grey lighten-5"
                            ></v-progress-circular>
                          </template>
                        </v-img>
                      </div>
                    </template>
                  </v-list-item>
                </v-list-item-group>
              </v-list>
            </v-card>
          </aside>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import img1 from '../../../assets/images/bg1.png'
// import img2 from '../../../assets/images/bg2.png'
// import img3 from '../../../assets/images/bg3.png'
// import img4 from '../../../assets/images/bg4.png'
import { toDate } from '@/util/filters'
import {
  deepClone,
  exitFullscreen,
  firstPath,
  isFullScreen,
  requestFullScreen,
} from '@/util/utils'

import {
  differenceInDays,
  endOfDay,
  format,
  startOfDay,
  subDays,
} from 'date-fns'
import img5 from '../../../assets/images/bg5.png'
import img7 from '../../../assets/images/bg7.png'
import DvDecoration8 from './component/dv-decoration8/Index.vue'

export const ISO_FORMAT = 'yyyy-MM-dd HH:mm:ss'

import {
  addLayoutData,
  canvasDetail,
  generateLink,
  layoutItemData,
  revokeLink,
  validLayoutItems,
} from '@/api/grid/index'

import VueGridLayout from 'vue-grid-layout'
import CardBorder from './component/CardBorder.vue'

import { required } from '@/@core/utils/validation'
import VsocDialog from '@/components/VsocDialog.vue'
import CardItem1 from './component/CardItem1.vue'
import CardItem10 from './component/CardItem10.vue'
import CardItem11 from './component/CardItem11.vue'
import CardItem12 from './component/CardItem12.vue'
import CardItem13 from './component/CardItem13.vue'
import CardItem14 from './component/CardItem14.vue'
import CardItem15 from './component/CardItem15.vue'
import CardItem16 from './component/CardItem16.vue'
import CardItem17 from './component/CardItem17.vue'
import CardItem18 from './component/CardItem18.vue'
import CardItem19 from './component/CardItem19.vue'
import CardItem2 from './component/CardItem2.vue'
import CardItem20 from './component/CardItem20.vue'
import CardItem3 from './component/CardItem3.vue'
import CardItem4 from './component/CardItem4.vue'
import CardItem5 from './component/CardItem5.vue'
import CardItem6 from './component/CardItem6.vue'
import CardItem7 from './component/CardItem7.vue'
import CardItem8 from './component/CardItem8.vue'
import CardItem9 from './component/CardItem9.vue'
import { defaultPreviewData } from './util/defaultPreview'

const GridLayout = VueGridLayout.GridLayout
const GridItem = VueGridLayout.GridItem

//判断是否是全屏状态
let isFull =
  Math.abs(
    window.screen.height - window.document.documentElement.clientHeight,
  ) <= 17

// 阻止F11键默认事件，用HTML5全屏API代替
window.addEventListener('keydown', function (e) {
  e = e || window.event
  if (e.keyCode === 122 && !isFull) {
    e.preventDefault()
    // enterFullScreen()
    requestFullScreen(document.documentElement)
  }
})

export default {
  name: 'grid-screen',
  components: {
    CardItem1,
    CardItem2,
    CardItem3,
    CardItem4,
    CardItem5,
    CardItem6,
    CardItem7,
    CardItem8,
    CardItem9,
    CardItem10,
    CardItem11,
    CardItem12,
    CardItem13,
    CardItem14,
    CardItem15,
    CardItem16,
    CardItem17,
    CardItem18,
    CardItem19,
    CardItem20,
    GridLayout,
    GridItem,
    DvDecoration8,
    CardBorder,
    VsocDialog,
  },
  data() {
    return {
      isLightTheme: 'false',
      isAnhuiMap: false,
      isShowJump: false,
      imgs: [img5, img7],
      required,
      valid: true,
      showShare: false,
      switchShow: false,
      showUrl: false,
      shareUrl: '',
      shareToken: '',
      selectItems: [],
      saveLoading: false,
      showDel: false,
      editForm: {
        itemName: '',
      },
      lang: '',
      canvasItem: {
        title: '',
        backgroundImage: '',
        titleEnName: '',
      },
      oldBackgroundImage: '',
      // imgs: [img1, img2, img3, img4],
      historyData: [],
      gridData: [],
      resourcesData: [],
      layoutData: [],
      xList: [],
      // 布局二维数组地图
      layoutMap: [],
      // 布局列数
      layoutColNum: 29,
      minRows: 29,
      rowHeight: 0, //行高
      colWidth: 0,
      isDraggable: false,
      isResizable: false,
      isMirrored: false,
      verticalCompact: true,
      // margin: [24, 24],
      useCssTransforms: true,
      showCard: false,

      refreshDate: null,
      timer2: null,
      isFull: isFull,
      isFullScreen: isFullScreen(),
      currentDate: 7,
      primary: '#44e2fe',
      dateDay: null,
      dateYear: null,
      dateWeek: null,
      timer: null,
      isLoading: false,
      isPageLoading: false,
      // 获取浏览器可视区域高度（包含滚动条）、
      // 获取浏览器可视区域高度（不包含工具栏高度）、
      // 获取body的实际高度  (三个都是相同，兼容性不同的浏览器而设置的)
      screenHeight:
        window.innerHeight ||
        document.documentElement.clientHeight ||
        document.body.clientHeight,
      screenWidth:
        window.innerWidth ||
        document.documentElement.clientWidth ||
        document.body.clientWidth,
      currentTypeList: [],

      showGrid: false,
      selectGrid: '',
      resourceGrid: [{ type: 1 }, { type: 2 }, { type: 3 }, { type: 4 }],
      continueDay: '',
      continueObj: {},
      vehicleType: [],
      lastVehicle: '',
    }
  },
  computed: {
    marginBoder() {
      // return this.$getCeilSize(12)
      return 12
    },
    chartEnum() {
      return this.$store.state.enums.enums['Layout Item Chart Type']
    },
    weekday() {
      return [
        this.$t('screen.week.sun'),
        this.$t('screen.week.mon'),
        this.$t('screen.week.tue'),
        this.$t('screen.week.wed'),
        this.$t('screen.week.thu'),
        this.$t('screen.week.fri'),
        this.$t('screen.week.sat'),
      ]
    },
    currentText() {
      return this.dateOption.find(v => v.value === this.currentDate)?.text
    },
    dateOption() {
      if (this.isAnhuiMap) {
        return [
          // {
          //   text: this.$t('enums.datePresets.today'),
          //   value: 1,
          // },
          {
            text: '所有年份',
            value: 7,
          },
          {
            text: '2025',
            value: 15,
          },
          {
            text: '2024',
            value: 30,
          },
          {
            text: '2023',
            value: 31,
          },
          {
            text: '2022',
            value: 32,
          },
        ]
      } else {
        return [
          {
            text: this.$t('enums.datePresets.today'),
            value: 1,
          },
          {
            text: this.$t('enums.datePresets.last7'),
            value: 7,
          },
          {
            text: this.$t('enums.datePresets.last15'),
            value: 15,
          },
          {
            text: this.$t('enums.datePresets.last30'),
            value: 30,
          },
        ]
      }
    },
    currentTypeText() {
      let text = ''
      if (this.currentTypeList.length) {
        text = `${this.$t('global.selected')}${this.currentTypeList.length}`
      }
      return text
    },
    // vehicleType() {
    //   return this.$store.getters['enums/getDataVehicleType'].filter(
    //     v => v.text !== 'ALL',
    //   )
    // },
  },
  watch: {
    '$i18n.locale': {
      handler(lang) {
        this.lang = lang
        window.document.documentElement.setAttribute('data-lang', lang)
      },
      deep: true,
      immediate: true,
    },
    isFullScreen: {
      handler(val) {
        //不是全屏
        if (!val && !this.isFull) {
          this.isDraggable = true
          this.isResizable = true
          this.showDel = true
        } else {
          this.isDraggable = false
          this.isResizable = false
          this.showDel = false
        }
        this.setRowHeight()
      },
      deep: true,
      immediate: true,
    },
    showCard: {
      handler(val) {
        if (!val) {
          let time =
            this.$store.getters['enums/getPollingInterval'](
              this.$generateMenuTitle(this.$route.meta),
            ) || 60
          this.timer2 = setInterval(() => {
            if (!this.isPageLoading) {
              // let layoutData = deepClone(this.layoutData)
              // if (Object.keys(this.continueObj).length) {
              //   layoutData.push(this.continueObj)
              // }
              this.loadList(this.layoutData, '', 'false')
            }
          }, time * 1000)
        } else {
          clearInterval(this.timer2)
        }
      },
      deep: true,
      immediate: true,
    },
    layoutData: {
      handler(val) {
        let findItem = val.find(v => v.itemKey === 'C010')
        if (findItem) {
          this.canvasItem.backgroundImage = '6'
        } else {
          this.canvasItem.backgroundImage = this.oldBackgroundImage
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    this.isLightTheme = this.$route.query.isLightTheme
    this.primary = this.isLightTheme === 'true' ? '#0094e7' : '#44e2fe'
    this.isShowJump =
      this.$route.query.id === '2213755318620389376' ? true : false

    this.isLoading = true
    this.intCardList()
    this.getAllVehicle(() => {
      if (this.$route.query.id) {
        this.initCanvas(this.$route.query.id)
      }
    })
  },
  mounted() {
    this.timeInterval()
    window.addEventListener('resize', this.setRowHeight)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.setRowHeight)
    clearInterval(this.timer)
    if (this.timer2) {
      clearInterval(this.timer2)
    }
  },
  beforeRouteLeave(to, from, next) {
    if (isFull || isFullScreen()) {
      exitFullscreen()
    }
    next()
  },
  methods: {
    jumpPage() {
      this.$router.push(
        '/grid/shareCanvas?id=2097228304061825024&isLightTheme=false',
      )
    },
    changeMap(item, type) {
      item.type = type
      let defaultGrid = defaultPreviewData.find(v => v.type === 3)
      if (type === 1) {
        this.layoutData = defaultGrid.defaultList.filter(
          v => v.itemKey !== 'C100' && v.itemKey !== 'C101',
        )
      } else if (type === 2) {
        //境内TOP
        this.layoutData = defaultGrid.defaultList.filter(
          v =>
            v.itemKey !== 'C101' &&
            v.itemKey !== 'C042' &&
            v.itemKey !== 'C044',
        )
      } else if (type === 3) {
        this.layoutData = defaultGrid.defaultList.filter(
          v =>
            v.itemKey !== 'C100' &&
            v.itemKey !== 'C042' &&
            v.itemKey !== 'C044',
        )
      }
    },
    //获取车型
    async getAllVehicle(callBack) {
      const data = await this.$store.dispatch('global/loadAllAutomaker')
      this.vehicleType = data || []
      this.currentTypeList = this.vehicleType.map(v => v.id)
      callBack && callBack()
    },
    openCard() {
      this.showCard = true
      this.showGrid = false
      this.selectItems = this.layoutData.map(v => v.i)
      if (Object.keys(this.continueObj).length) {
        this.selectItems.push(this.continueObj.i)
      }
      setTimeout(() => {
        this.setRowHeight()
        this.resizedEvent()
      }, 300)
    },
    closeCard() {
      this.showCard = false
      setTimeout(() => {
        this.setRowHeight()
        this.resizedEvent()
      }, 300)
    },
    //选择默认布局
    openGrid() {
      this.showGrid = true
      this.showCard = false
      setTimeout(() => {
        this.setRowHeight()
        this.resizedEvent()
      }, 300)
    },
    closeGrid() {
      this.showGrid = false
      setTimeout(() => {
        this.setRowHeight()
        this.resizedEvent()
      }, 300)
    },
    //选择布局
    async changeGrid(type, isNoActive) {
      if (isNoActive) {
        this.layoutData = []
        this.continueObj = {}
        this.continueDay = ''
        return
      }
      let layoutData = []
      let defaultGrid = defaultPreviewData.find(v => v.type === type)
      if (defaultGrid.type === 3) {
        this.layoutData = defaultGrid.defaultList.filter(
          v => v.itemKey !== 'C100' && v.itemKey !== 'C101',
        )
        this.$notify.info('success', '已选择默认布局' + type)
      } else if (defaultGrid.type === 4) {
        this.layoutData = defaultGrid.defaultList
        this.$notify.info('success', '已选择默认布局' + type)
      } else if (defaultGrid) {
        let itemKeyList = defaultGrid.defaultList.map(v => v.itemKey)
        let inferKeyList = []
        this.resourcesData.forEach(v => {
          if (itemKeyList.includes(v.i) && v.contentSourceType === '1') {
            inferKeyList.push(v.i)
          }
        })
        // this.isLoading = true
        this.selectGrid = type
        const params = {
          itemKeyList: inferKeyList,
          vehicleTypeList: this.currentTypeList,
          startDate: format(
            startOfDay(subDays(new Date(), this.currentDate - 1)),
            ISO_FORMAT,
          ),
          endDate: format(endOfDay(new Date()), ISO_FORMAT),
        }
        let findObj = this.resourcesData.find(v => v.itemKey === 'C015')
        if (
          !params.itemKeyList.includes('C015') &&
          findObj?.contentSourceType === '1'
        ) {
          params.itemKeyList.push('C015')
        }
        const res = await layoutItemData(params)
        defaultGrid.defaultList.forEach(item => {
          let itemData = res.data.find(v => v.itemKey === item.itemKey)
          let itemObj = this.resourcesData.find(v => v.itemKey === item.itemKey)
          if (itemData) {
            itemObj.value = itemData.data
          }
          let obj = {
            ...itemObj,
            x: item.x,
            y: item.y,
            w: item.w,
            h: item.h,
          }
          layoutData.push(obj)
        })
        this.layoutData = layoutData
        let findDay = res.data.find(v => v.itemKey === 'C015')
        if (findDay) {
          this.continueObj.value = findDay.data
          this.continueDay = differenceInDays(
            new Date(),
            new Date(this.continueObj.value),
          )
        }
        // let findObj = this.resourcesData.find(v => v.itemKey === 'C015')
        // if (findObj) {
        //   this.continueObj = findObj
        //   this.continueDay = differenceInDays(
        //     new Date(),
        //     new Date(this.continueObj.value),
        //   )
        // }
        // this.isLoading = false
        this.$notify.info('success', '已选择默认布局' + type)
      }
    },
    //获取链接
    async fetchShareUrl() {
      const params = {
        canvasId: this.$route.query.id,
        endDate: '2089-12-31 23:59:59',
      }
      const { data } = await generateLink(params)
      this.shareUrl = data.url
    },
    // 撤销画布分享
    async removeShareUrl() {
      this.$swal({
        title: this.$t('grid.share.delTitle'),
        text: this.$t('grid.share.delTip'),
        icon: 'warning',
        reverseButtons: true,
        showCancelButton: true,
        confirmButtonText: this.$t('action.confirm'),
        cancelButtonText: this.$t('action.cancel'),
        customClass: {
          confirmButton: 'sweet-btn-primary',
        },
      }).then(async result => {
        if (result.isConfirmed) {
          try {
            const res = await revokeLink({ canvasId: this.$route.query.id })
            if (res.code === 200) {
              this.$notify.info('success', this.$t('global.hint.operate'))
            }
          } catch (e) {
            console.error(`撤销画布错误：${e}`)
          }
        } else {
          this.switchShow = true
        }
      })
    },
    changeShareToken() {
      if (!this.shareToken) {
        return this.$notify.info('error', this.$t('grid.hint'))
      }
      // http://************:8090/vsocwebdev15/grid/shareCanvas?id=1997103293075030016&token=bearer 12110e85-1d55-4646-87be-d32461e519c2
      this.shareUrl =
        window.location.href?.split('grid')[0] +
        `grid/shareCanvas?id=${this.$route.query.id}&token=${this.shareToken}`
      this.$notify.info('success', this.$t('grid.hint1'))
    },
    shareScreen() {
      this.showShare = true
      this.shareUrl = ''
      this.shareToken = ''
      // this.fetchShareUrl()
    },
    changeSwitch() {
      if (this.switchShow) {
        this.fetchShareUrl()
      } else {
        this.removeShareUrl()
      }
    },
    goback() {
      this.$router.push('/grid/canvas')
    },
    //清空事件
    resetCard() {
      this.editForm.itemName = ''
      this.intCardList()
    },
    //获取所有统计指标有效的数据
    async intCardList() {
      const res = await validLayoutItems(this.editForm)
      let resourcesData = []
      if (res.data.length) {
        resourcesData = res.data.map(v => {
          return {
            ...v,
            x: 0,
            y: 0,
            w: v.startWidth,
            h: v.startHeight,
            i: v.itemKey,
            minW: v.minWidth,
            minH: v.minHeight,
          }
        })
      }
      this.resourcesData = resourcesData
    },
    //获取画布详情
    async initCanvas(id) {
      try {
        const res = await canvasDetail({ id: id })
        this.canvasItem.title = res.data.title
        this.isAnhuiMap = this.canvasItem.title.includes('安徽省智能网联')
        this.canvasItem.titleEnName = res.data.titleEnName
        this.canvasItem.backgroundImage = res.data.backGroundImg
        this.oldBackgroundImage = res.data.backGroundImg
        this.historyData = res.data.data
        if (this.historyData.length) {
          let findDay = this.historyData.find(v => v.itemKey === 'C015')
          if (findDay) {
            this.continueObj = findDay
            this.continueDay = differenceInDays(
              new Date(),
              new Date(this.continueObj.value),
            )
          }
          this.loadList(
            this.historyData,
            () => {
              this.isLoading = false
            },
            'false',
          )
        } else {
          this.isLoading = false
        }
      } catch (err) {
        this.isLoading = false
      }
    },
    async loadList(loadData1, callback, loading) {
      try {
        this.isPageLoading = true
        this.refreshDate = new Date()
        //list为过滤掉了C015
        let list = deepClone(loadData1)
        // let loadData = list.filter(v => v.itemKey !== 'C015')
        let contentSourceTypeList = list.filter(
          v => v.contentSourceType === '1',
        )
        if (contentSourceTypeList.length) {
          const params = {
            itemKeyList: contentSourceTypeList.map(v => v.i),
            vehicleTypeList: this.currentTypeList,
            startDate: format(
              startOfDay(subDays(new Date(), this.currentDate - 1)),
              ISO_FORMAT,
            ),
            endDate: format(endOfDay(new Date()), ISO_FORMAT),
          }
          if (
            Object.keys(this.continueObj).length &&
            !params.itemKeyList.includes('C015') &&
            this.continueObj.contentSourceType === '1'
          ) {
            params.itemKeyList.push('C015')
          }
          const res = await layoutItemData(params, '', loading)
          list.forEach(item => {
            let findItem = res.data.find(v => v.itemKey === item.i)
            if (findItem) {
              item.value = findItem.data
            }
          })
          let findDay = res.data.find(v => v.itemKey === 'C015')
          if (findDay) {
            // this.continueObj = findDay
            this.continueObj.value = findDay.data
            this.continueDay = differenceInDays(
              new Date(),
              new Date(this.continueObj.value),
            )
          }
        }
        this.xList = []
        for (let i = this.currentDate - 1; i >= 0; i--) {
          this.xList.push(format(subDays(new Date(), i), 'MM/dd'))
        }
        // else {
        //   if (!Object.keys(this.continueObj).length) {
        //     this.continueObj = {}
        //     this.continueDay = ''
        //   }
        // }
        let loadData = list.filter(v => v.itemKey !== 'C015')
        this.initCard(loadData)
      } catch (err) {
        console.log('数据大屏数据报错', err)
      } finally {
        this.isPageLoading = false
        callback && callback()
      }
    },
    initCard(loadData) {
      setTimeout(() => {
        this.layoutData = deepClone(loadData)
        this.setRowHeight()
        this.resizedEvent()
      }, 500)
    },
    layoutReadyEvent() {
      this.layoutMap = this.genereatePlaneArr(this.layoutData)
      this.setRowHeight()
    },
    // 当插件内容布局发生变化后  获取现在的二维地图树
    layoutUpdatedEvent() {
      this.layoutMap = this.genereatePlaneArr(this.layoutData)
      setTimeout(() => {
        this.resizedEvent()
      }, 300)
    },
    setRowHeight() {
      this.$nextTick(() => {
        let ele = this.$refs['layout-box']
        let allHeight = ele && ele.offsetHeight
        // let marginBoder = 14
        let height = allHeight - (this.minRows + 1) * this.marginBoder
        this.rowHeight = Math.floor(height / this.minRows)

        let allWidth = ele && ele.offsetWidth
        let width = allWidth - (this.layoutColNum + 1) * this.marginBoder
        this.colWidth = Math.floor(width / this.layoutColNum)
        // console.log(this.colWidth, 111)
      })
    },
    async saveCard() {
      try {
        this.saveLoading = true
        const params = {
          id: this.$route.query.id,
          data: deepClone(this.layoutData),
        }
        if (Object.keys(this.continueObj).length) {
          params.data.push(this.continueObj)
        }
        const res = await addLayoutData(params)
        if (res.code === 200) {
          this.showCard = false
          setTimeout(() => {
            this.resizedEvent()
          }, 300)
        }
      } catch (err) {
        console.log('保存报错', err)
      } finally {
        this.saveLoading = false
      }
    },
    cancelCard() {
      this.showCard = false
      this.saveLoading = true
      this.selectGrid = ''
      // this.continueDay = ''
      // this.continueObj = {}
      this.loadList(this.historyData, () => {
        this.saveLoading = false
        setTimeout(() => {
          this.resizedEvent()
        }, 300)
      })
    },

    resizedEvent() {
      this.$nextTick(() => {
        this.layoutData.forEach(item => {
          if (
            item.isBorder === '0' &&
            this.$refs['comp' + item.i] &&
            this.$refs['comp' + item.i].length
          ) {
            this.$refs['comp' + item.i][0].$children.length &&
              this.$refs['comp' + item.i][0].$children[0].initWH()
          }
        })
      })
    },
    deleteCard(compId) {
      let findIndex = this.layoutData.findIndex(v => v.i === compId)
      if (findIndex !== -1) {
        this.layoutData.splice(findIndex, 1)
      }
      let itemIndex = this.selectItems.findIndex(v => v === compId)
      if (itemIndex !== -1) {
        this.selectItems.splice(itemIndex, 1)
      }
    },
    // 生成二维数组地图
    genereatePlaneArr(data) {
      var map = []
      if (Array.isArray(data)) {
        for (var i = 0; i < data.length; i++) {
          var one = data[i]
          // 循环行
          for (var r = one.y; r < one.y + one.h; r++) {
            // 循环列
            for (var c = one.x; c < one.x + one.w; c++) {
              // 检修当前行是否存在
              if (!map[r]) {
                map[r] = new Array(this.layoutColNum)

                for (let i = 0; i < this.layoutColNum; i++) {
                  map[r][i] = 0
                }
              }
              // 占据为1
              map[r][c] = 1
            }
          }
        }
      }
      return map
    },
    async addCard(item, isNoActive) {
      if (isNoActive) {
        if (item.itemKey === 'C015') {
          this.continueObj = {}
          this.continueDay = ''
          return
        }
        let findIndex = this.layoutData.findIndex(v => v.i === item.i)
        if (findIndex !== -1) {
          this.layoutData.splice(findIndex, 1)
        }
      } else {
        if (item.itemKey === 'C015') {
          this.continueObj = item
          this.continueDay = differenceInDays(new Date(), new Date(item.value))
          return
        }
        this.xList = []
        for (let i = this.currentDate - 1; i >= 0; i--) {
          this.xList.push(format(subDays(new Date(), i), 'MM/dd'))
        }
        if (item.contentSourceType === '1') {
          const params = {
            itemKeyList: [item.i],
            vehicleTypeList: this.currentTypeList,
            startDate: format(
              startOfDay(subDays(new Date(), this.currentDate - 1)),
              ISO_FORMAT,
            ),
            endDate: format(endOfDay(new Date()), ISO_FORMAT),
          }
          const res = await layoutItemData(params)
          item.value = res.data.length ? res.data[0].data : []
        }
        var itemW = item.w
        var itemH = item.h
        var addItem = item
        addItem.minH = item.minHeight
        addItem.minW = item.minWidth
        addItem.x = 0
        addItem.y = this.layoutMap.length
        if (this.layoutMap.length) {
          for (let r = 0, rLen = this.layoutMap.length; r < rLen; r++) {
            for (let c = 0; c <= this.layoutColNum - itemW; c++) {
              let res = this.regionalTest(
                c,
                r,
                itemW,
                rLen > r + itemH ? itemH : rLen - r,
              )
              if (res.result) {
                addItem.x = res.x
                addItem.y = res.y
                c = this.layoutColNum + 1
                r = rLen + 1
              } else {
                c = res.offsetX
              }
            }
          }
        }
        // 更新二维数组地图
        for (let itemR = 0; itemR < itemH; itemR++) {
          for (let itemC = 0; itemC < itemW; itemC++) {
            // 如果没有该行，初始化
            if (!this.layoutMap[addItem.y + itemR]) {
              this.layoutMap[addItem.y + itemR] = new Array(this.layoutColNum)
              for (let i = 0; i < this.layoutColNum; i++) {
                this.layoutMap[addItem.y + itemR][i] = 0
              }
            }
            // 标记点
            this.layoutMap[addItem.y + itemR][addItem.x + itemC] = 1
          }
        }
        // 添加数据
        this.layoutData.push(addItem)
      }
    },
    // 区域检测 x,y 二维数据地图起始坐标点  w,h 检测区域宽高
    regionalTest(x, y, w, h) {
      // 定义返回 x,y 偏移 及 是否有空位置
      let offsetX = 0,
        offsetY = 0,
        res = true

      // console.log(x, y, offsetX, 3)
      // 按区域循环检测 二维数组地图
      for (let r = 0; r < w; r++) {
        for (let c = 0; c < h; c++) {
          let point = this.layoutMap[y + r] ? this.layoutMap[y + r][x + c] : 0
          // 如该点被占据 记录偏移值
          if (point === 1) {
            res = false
            offsetX = offsetX > x + c ? offsetX : x + c
            offsetY = offsetY > y + r ? offsetY : y + r
          }
        }
      }
      return {
        result: res,
        offsetX: offsetX,
        x: x,
        y: y,
      }
    },
    changeVehicles() {
      if (this.currentTypeList.length === 1) {
        this.lastVehicle = this.currentTypeList[0]
      } else if (this.currentTypeList.length === 0) {
        this.currentTypeList = [this.lastVehicle]
      }
    },
    onSelectData(value) {
      if (!value) return
      // this.currentTypeList = value
      // this.isLoading = true
      this.loadList(this.layoutData, () => {
        // this.isLoading = false
      })
    },
    onSelectData1(value) {
      if (!value) return
      this.currentDate = value
      // this.isLoading = true
      this.loadList(this.layoutData, () => {
        // this.isLoading = false
      })
    },
    toggleFullScreen() {
      if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen()
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen()
        }
      }
    },
    timeInterval() {
      this.timer = setInterval(() => {
        this.dateDay = toDate(new Date(), 'hh:mm:ss')
        this.dateYear = toDate(new Date(), 'yyyy/MM/dd')
        this.dateWeek = this.weekday[new Date().getDay()]
      }, 1000)
    },
    goIndex() {
      this.$router.push(firstPath())
      // this.$router.push('/')
    },
    getScreenSize() {
      this.getScreenHeight()
      this.getScreenWidth()
      isFull =
        Math.abs(
          window.screen.height - window.document.documentElement.clientHeight,
        ) <= 17
      this.isFull = isFull
      this.isFullScreen = isFullScreen()
    },
    // 获取浏览器高度进行自适应
    getScreenHeight() {
      this.screenHeight =
        window.innerHeight ||
        document.documentElement.innerHeight ||
        document.body.clientHeight
    },
    // 字体大小根据宽度自适应
    getScreenWidth() {
      this.screenWidth =
        window.innerWidth ||
        document.documentElement.clientWidth ||
        document.body.clientWidth
      this.$store.commit('global/setClientWidth', this.screenWidth)
    },
  },
}
</script>

<style lang="scss">
@import './index.scss';

.vue-grid-item {
  transition: none !important; /* 禁用 vue-grid-item 的过渡效果 */
}
.shadowed {
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.13);
}
.rounded {
  border-radius: 8px;
}
.bordered {
  border: 1px solid #f0f0f0;
}
.model-list .v-list-item--active::before {
  // opacity: 0.28 !important;
}
.vue-grid-item > .vue-resizable-handle {
  opacity: 0 !important;
}
.grid-border.vue-grid-item > .vue-resizable-handle {
  width: 24px;
  height: 24px;
  background: url('./images/point.svg') !important;
  opacity: 1 !important;
}
</style>
