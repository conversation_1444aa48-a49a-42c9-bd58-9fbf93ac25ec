<template>
  <v-row no-gutters>
    <!-- 头像 -->
    <v-col cols="3" class="pt-2 mt-5 text-center tab-height">
      <!-- @click="$refs.refInputEl.click()" -->
      <div class="pl-6 mt-6">
        <v-badge
          class="cursor-pointer"
          avatar
          bordered
          overlap
          offset-x="50"
          offset-y="100"
        >
          <template v-slot:badge>
            <v-avatar size="24">
              <v-icon>mdi-camera-flip</v-icon>
            </v-avatar>
          </template>

          <v-avatar size="100" class="me-6">
            <v-img :src="accountDataLocale.avatarImg"></v-img>
          </v-avatar>
        </v-badge>
      </div>
      <!-- upload photo -->
      <!-- <div>
        <v-btn
          color="primary"
          class="me-3 mt-5"
          @click="$refs.refInputEl.click()"
        >
          <v-icon class="d-sm-none">
            {{ icons.mdiCloudUploadOutline }}
          </v-icon>
          <span class="d-none d-sm-block">上传新头像</span>
        </v-btn>

        <input
          ref="refInputEl"
          type="file"
          accept=".jpeg,.png,.jpg,GIF"
          :hidden="true"
        />

        <v-btn color="error" outlined class="mt-5"> 重置 </v-btn>
        <p class="text-sm mt-5">允许JPG，GIF，PNG格式，最大不能超过25K</p>
      </div> -->
      <input
        ref="refInputEl"
        type="file"
        accept=".jpeg,.png,.jpg,.gif"
        :hidden="true"
      />
      <div class="text-h4 color-base mt-6">
        {{ accountDataLocale.userName }}
      </div>
      <div class="mt-2 mb-7">{{ accountDataLocale.roleName }}</div>

      <v-chip
        class="mr-2 mt-3"
        v-for="(item, index) in accountDataLocale.departments"
        :key="index"
        small
      >
        <span class="px-1">{{ item.departmentName | dataFilter }}</span>
      </v-chip>
    </v-col>

    <div>
      <v-divider vertical class="mx-1"></v-divider>
    </div>

    <!-- 用户信息 -->
    <v-col cols="7" class="mt-7 pl-14">
      <v-form ref="form" class="multi-col-validation mt-6 w-60">
        <v-row>
          <v-col md="12" cols="12">
            <v-text-field
              v-model="accountDataLocale.userId"
              :label="$t('login.userName')"
              disabled
            ></v-text-field>
          </v-col>
          <v-col md="12" cols="12">
            <v-text-field
              v-model="accountDataLocale.userName"
              :label="$t('user.headers.fullName')"
              :rules="[
                required(
                  accountDataLocale.userName,
                  $t('user.headers.fullName'),
                ),
              ]"
            ></v-text-field>
          </v-col>

          <v-col cols="12" md="12">
            <v-text-field
              v-model="accountDataLocale.email"
              :label="$t('user.headers.email')"
              :rules="rules.email"
            ></v-text-field>
          </v-col>

          <v-col cols="12" md="12">
            <v-text-field
              v-model="accountDataLocale.phone"
              type="number"
              :label="$t('user.drawer.phone')"
            ></v-text-field>
          </v-col>

          <!-- <v-col cols="12" md="12">
            <v-select
              v-model="accountDataLocale.roleId"
              :items="roleList"
              label="角色"
              chips
              :multiple="false"
              outlined
              height="46"
              item-value="roleId"
              item-text="roleName"
              disabled
            >
              <template v-slot:selection="{ item }">
                <v-chip label color="primary" class="py-1 px-2 my-0">
                  <span class="text-caption ls-0">{{ item.roleName }}</span>
                </v-chip>
              </template>
            </v-select>
          </v-col> -->

          <!-- <v-col cols="12" md="12">
            <v-select
              v-model="accountDataLocale.groupId"
              :items="userGroupList"
              label="用户组"
              chips
              multiple
              outlined
              height="46"
              item-value="groupId"
              item-text="groupName"
              disabled
            >
              <template v-slot:selection="{ item }">
                <v-chip label color="primary" class="py-1 px-2 my-0">
                  <span class="text-caption ls-0">{{ item.groupName }}</span>
                </v-chip>
              </template>
            </v-select>
          </v-col> -->
          <!-- alert -->
          <!-- <v-col cols="12" class="pt-0">
            <v-alert color="warning" text class="mb-0 mt-0">
              <div class="d-flex align-start">
                <v-icon color="warning">
                  {{ icons.mdiAlertOutline }}
                </v-icon>

                <div class="ms-3">
                  <p class="text-base font-weight-medium mb-1">
                    您的电子邮件尚未被验证。请检查您的收件箱。
                  </p>
                  <a
                    href="javascript:void(0)"
                    class="text-decoration-none warning--text"
                  >
                    <span class="text-sm">重新验证</span>
                  </a>
                </div>
              </div>
            </v-alert>
          </v-col> -->

          <v-col cols="12" class="pt-0 pb-0 d-flex flex-row-reverse">
            <v-btn
              color="primary"
              class="mt-4"
              :loading="isLoading"
              @click.prevent="onSave"
              min-width="64"
              width="64"
            >
              {{ $t('action.save') }}
            </v-btn>
            <v-btn
              color="secondary"
              outlined
              class="mt-4 me-4"
              type="reset"
              @click.prevent="resetForm"
              min-width="64"
              width="64"
            >
              {{ $t('action.cancel') }}
            </v-btn>
          </v-col>
        </v-row>
      </v-form>
    </v-col>
    <!-- <v-card-actions> </v-card-actions> -->
  </v-row>
</template>

<script>
import { emailValidator, required } from '@/@core/utils/validation'
import { updateUser, userRole } from '@/api/system/user'
import { PAGESIZE_MAX } from '@/util/constant'
import { dataFilter } from '@/util/filters'
import { mdiAlertOutline, mdiCloudUploadOutline } from '@mdi/js'
import {
  getCurrentInstance,
  onMounted,
  reactive,
  ref,
} from '@vue/composition-api'
import { cloneDeep } from 'lodash'

export default {
  name: 'AccountSettingsAccount',
  setup(props) {
    const vm = getCurrentInstance().proxy
    const form = ref(null)
    const isLoading = ref(false)
    const userInfo = JSON.parse(JSON.stringify(vm.$store.getters.userInfo))
    const accountDataLocale = ref(
      Object.assign(userInfo, {
        avatarImg: require('@/assets/images/avatars/3.png'),
      }),
    )

    // accountDataLocale.avatarImg =
    const rules = reactive({
      email: [
        v => required(v, vm.$t('user.headers.email')),
        v => emailValidator(v),
      ],
    })
    const roleList = ref([])

    const getRoleList = async () => {
      const params = {
        pageNum: 1,
        pageSize: PAGESIZE_MAX,
      }
      const res = await userRole(params)
      roleList.value = res.data.records
    }

    const userGroupList = ref([])

    const getGroupUserList = async () => {
      // const params = {
      //   pageNum: 1,
      //   pageSize: PAGESIZE_MAX,
      // }
      // const res = await userGroup(params)
      // userGroupList.value = res.data.records
    }

    const resetForm = () => {
      const originData = JSON.parse(JSON.stringify(vm.$store.getters.userInfo))
      accountDataLocale.value = Object.assign(originData, {
        avatarImg: require('@/assets/images/avatars/1.png'),
      })
    }

    const onSave = async () => {
      isLoading.value = true
      const bool = form.value.validate()
      if (!bool) {
        return
      }
      const formData = reactive(cloneDeep(accountDataLocale.value))
      formData.roleList = reactive([{ roleId: accountDataLocale.value.roleId }])

      delete formData.roleId
      delete formData.roles
      updateUser(formData)
        .then(res => {
          if (res.code === 200) {
            vm.$notify.info('success', vm.$t('global.hint.edit', []))
            vm.$store.dispatch('user/getUserInfo')
          }
        })
        .catch(err => {
          vm.$notify.info('error', err)
        })
        .finally(() => {
          isLoading.value = false
        })
    }

    onMounted(() => {
      getRoleList()
      getGroupUserList()
    })

    return {
      form,
      isLoading,
      rules,
      required,
      roleList,
      userGroupList,
      accountDataLocale,
      resetForm,
      onSave,
      icons: {
        mdiAlertOutline,
        mdiCloudUploadOutline,
      },
    }
  },
}
</script>
<style lang="scss" scoped>
.tab-height {
  height: calc(100vh - 160px);
}
</style>
