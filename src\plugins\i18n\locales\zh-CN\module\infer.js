const infer = {
  currentTitle: '推断',
  headers: {
    order: '顺序',
    name: '推断名称',
    signal: '更新信号',
    event: '更新事件',
  },
  priority: '优先级',
  group: {
    title: '资产组',
    applicableType: '适用类型',
    hint: '请选择资产组',
    hint1: '请至少选择一个资产组!',
    allGroups: '适用于所有资产组',
    selectGroups: '适用于选择的资产组',
    excludeGroups: '排除选择的资产组',
  },
  inferLogic: '推断逻辑',
  conditionalInference: '条件推断',
  stateSignal: '信号状态',
  event: '行为事件',
  hintEvent1: '请选择行为事件。',
  hintEvent2: '当推断逻辑成立时，将向选择的事件添加行为事件实例(最多1个事件)。',
  hintEvent3: '当推断逻辑成立时，将向选择的事件添加行为事件实例(最多1个事件)。',
  selectEvent: '选择行为事件',
  hintSignal: '当推断逻辑成立时，选择的信号信息将会被更新。(最多10个信号)',
  hintSignal1: '当推断逻辑成立时，选择的信号信息将会被更新。(最多1个信号)',
  select: '请选择',
  method: '赋值方式',
  input: '请输入',
  signalSurvey: '信号调查',
  recommended: '推荐信号',
  all: '全部信号',
  search: '搜索信号',
  selectedSiganl: '已选择的信号',
  most: '最多选择{count}条',
  itemSelect: '已选择{count}条',
  inferRule: '请指定至少一个条件推断(事件或状态信号)',
  logicalError: '该逻辑条件不完整，请填写或删除',
  improveLogic: '请完善信号状态逻辑',
  swal: {
    title: '删除推断',
    hint: '确认删除推断：{name}？',
  },
  include: '包含',
  add: '新增推断',
  edit: '修改推断',
  expression: {
    title: '定时表达式',
    express: '表达式',
    old: '旧表达式',
    new: '新表达式',
    everyDay: '每天的',
    hour: '小时',
    minute: '分钟',
    second: '秒',
    every: '每',
    everyMonth: '每月的',
    day: '日',
    week: '周',
    month: '月',
    custom: '自定义',
  },
}

export default infer
