// Default
$light-placeholder-lg-false: false;
$light-placeholder-sm-false: false;

// LG
$light-lg-placeholder-lg: true;
$light-lg-placeholder-sm: false;

// SM
$light-sm-placeholder-lg: false;
$light-sm-placeholder-sm: true;

$map: (
  'dark': (
    rgba($black, 0.6),
    $light-placeholder-lg-false,
    $light-placeholder-sm-false,
  ),
  'white': (
    rgba($white, 0.8),
    $light-placeholder-lg-false,
    $light-placeholder-sm-false,
  ),
  'search-dark': (
    rgba($white, 0.6),
    $light-placeholder-lg-false,
    $light-placeholder-sm-false,
  ),
  'lighter': (
    $gray-500,
    $light-placeholder-lg-false,
    $light-placeholder-sm-false,
  ),
  'light': (
    $gray-600,
    $light-placeholder-lg-false,
    $light-placeholder-sm-false,
  ),
  'light-lg': (
    $gray-600,
    $light-lg-placeholder-lg,
    $light-lg-placeholder-sm,
  ),
  'light-sm': (
    $gray-600,
    $light-sm-placeholder-lg,
    $light-sm-placeholder-sm,
  ),
);

@each $key, $value in $map {
  .placeholder-#{$key} textarea,
  .placeholder-#{$key} input {
    @include placeholder($value...);
  }
}

.text-color-search-dark input {
  color: rgba($white, 0.5) !important;
}

.text-color-light input {
  color: $gray-600 !important;
}

.text-color-white input {
  color: $white !important;
}

.font-size-input {
  textarea,
  &.v-text-field input {
    font-size: $input-font-size;
  }
  .v-label {
    font-size: $input-font-size;
    color: $body-color;
  }
}

.font-size-input-lg {
  textarea,
  &.v-text-field input {
    font-size: $font-size-base;
  }
}

.font-size-input-sm {
  textarea,
  &.v-text-field input {
    font-size: $font-size-base - 0.125;
  }
}

.text-light-input input,
.text-light-input textarea {
  color: $gray-700 !important;
}

.navbar-search {
  &.navbar-search-dark .v-input--is-focused .v-input__slot {
    background-color: $input-dark-bg !important;
  }
}

.input-icon.v-text-field {
  .v-input__slot {
    padding: 0 $input-icon-padding-x + 0.25;
  }
}

.input-icon.v-text-field {
  .v-input__prepend-inner {
    padding-right: $input-icon-padding-x;
    margin-top: $input-icon-margin-top;
  }

  .v-input__append-inner {
    padding-left: $input-icon-padding-x;
  }
}

.input-style:not(.v-input--is-focused) {
  .v-input__slot {
    fieldset {
      border-color: $gray-300;
    }
  }
}

.input-focused-alternative {
  &.v-input--is-focused .v-input__slot {
    box-shadow: $input-alternative-focused-box-shadow;
  }
}

.input-focused {
  &.v-input--is-focused .v-input__slot {
    box-shadow: $input-focused-box-shadow;
  }
}

.error--text {
  textarea,
  input {
    @include placeholder(
      map-get($theme-colors, 'danger'),
      $light-placeholder-lg-false,
      $light-placeholder-sm-false
    );
  }

  .v-messages__message,
  .checkbox-validation {
    color: map-get($theme-colors, 'danger') !important;
  }
}

.v-text-field {
  &.v-text-field.v-select--chips {
    .v-label {
      font-size: $font-size-base;
      top: 50%;
      transform: translateY(-50%);

      &--active {
        top: 0;
        transform: translateY(-50%) scale(0.75);
      }
    }

    // .v-input__append-inner {
    //     .v-input__icon .v-icon {
    //         position: absolute;
    //         top: 50%;
    //         right: 0.75rem;
    //         margin-top: -0.5rem;
    //     }
    // }
  }

  & .v-label {
    font-size: 1rem;
  }
}

// ui改造后的样式
.form-section-title {
  font-size: $font-size-base;
  font-weight: $font-weight-semibold-light;
  color: var(--v-color-base);
}

// 必填
.is-required {
  .v-label::before {
    content: '*';
    color: var(--v-error-base);
    margin-right: 4px;
  }
}
