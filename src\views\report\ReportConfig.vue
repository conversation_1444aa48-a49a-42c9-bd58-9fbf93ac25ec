<template>
  <div>
    <bread-crumb></bread-crumb>

    <v-card tile class="main-content">
      <v-card-text class="pa-0">
        <div class="d-flex justify-space-between align-center mb-3">
          <div class="d-flex justify-end align-center">
            <v-btn
              elevation="0"
              color="primary"
              @click="add"
              v-has:reportConfig-new
            >
              <span>
                {{ $t('action.add') }}
              </span>
            </v-btn>
          </div>
          <div class="d-flex align-center">
            <table-search
              :searchList="searchList"
              :searchQuery="query"
              @search="$_search"
            ></table-search>
            <!-- <v-text-field
              v-model="query.reportName"
              color="primary"
              hide-details="auto"
              label="报告名称"
              dense
              outlined
              class="me-3 text-width"
              clearable
              @click:clear="onClear"
              @keyup.enter.native="$_search"
            ></v-text-field>
            <div>
              <v-btn
                class="primary--text bg-btn"
                elevation="0"
                @click="$_search"
              >
                <span>
                  {{ $t('action.search') }}
                </span>
              </v-btn>
            </div> -->
          </div>
        </div>

        <v-data-table
          fixed-header
          :items-per-page="query.pageSize"
          item-key="id"
          :height="tableHeight"
          hide-default-footer
          :headers="headers"
          :items="tableData"
          class="flex-1 thead-light"
          :loading="tableLoading"
        >
          <template v-slot:item.reportType="{ item }">
            <v-icon
              v-if="item.reportType == '2' || item.reportType == '5'"
              size="1.5rem"
            >
              mdi-file-word
            </v-icon>
            <v-icon v-else size="1.5rem"> mdi-file-excel-outline </v-icon>
          </template>
          <template v-slot:item.reportName="{ item }">
            <div v-show-tips style="width: 160px">{{ item.reportName }}</div>
          </template>
          <template v-slot:item.modelType="{ item }">
            <div>
              {{
                item.modelType === '1'
                  ? item.vehicleModelName
                  : item.modelType === '0'
                  ? item.modelTypeName
                  : 'N/A'
              }}
            </div>
          </template>
          <template v-slot:item.active="{ item }">
            <div v-if="activeEnum[item.active]">
              <v-badge
                dot
                inline
                offset-x="10"
                :offset-y="-18"
                :color="activeEnum[item.active].color"
                class="mr-1"
              ></v-badge>
              <span>{{ activeEnum[item.active].text }}</span>
            </div>
            <span v-else>N/A</span>
            <!-- <v-icon
              v-if="activeEnum[Number(item.active)]"
              size="1.5rem"
              :color="activeEnum[Number(item.active)].color"
            >
              {{ activeEnum[Number(item.active)].icon }}
            </v-icon>
            <span v-else>N/A</span> -->
          </template>
          <template v-slot:item.createDate="{ item }">
            <span v-show-tips>{{ item.createDate | toDate }}</span>
          </template>

          <template v-slot:item.updateTime="{ item }">
            <span v-show-tips>{{ item.updateTime | toDate }}</span>
          </template>
          <template v-slot:item.actions="{ item }">
            <v-btn v-has:reportConfig-edit icon @click.stop="edit(item)">
              <vsoc-icon
                v-show-tips="$t('action.edit')"
                type="fill"
                icon="icon-bianji"
                class="action-btn"
                size="x-large"
              ></vsoc-icon>
            </v-btn>
            <v-btn v-has:reportConfig-del icon @click.stop="del(item)">
              <vsoc-icon
                v-show-tips="$t('action.del')"
                type="fill"
                icon="icon-shanchu"
                class="action-btn"
                size="x-large"
              ></vsoc-icon>
            </v-btn>
            <!-- <v-btn icon @click.stop="edit(item)">
              <v-icon size="1.25rem"> mdi-pencil </v-icon>
            </v-btn>

            <v-btn :loading="item.btnLoading" icon @click.stop="del(item)">
              <v-icon size="1.25rem"> mdi-delete </v-icon>
            </v-btn> -->
          </template>
        </v-data-table>

        <vsoc-pagination
          :page.sync="query.pageNum"
          :size.sync="query.pageSize"
          :total="tableDataTotal"
          @change-page="getTableData"
          @change-size="$_search"
        >
        </vsoc-pagination>
      </v-card-text>

      <vsoc-drawer
        v-model="showEditGroup"
        :title="
          editType === 'add' ? $t('report.btn.add') : $t('report.btn.edit')
        "
        @click:confirm="confirmReport"
        :width="500"
      >
        <v-form ref="form" v-model="valid">
          <v-text-field
            v-model="editReport.reportName"
            color="primary"
            :label="$t('report.headers.reportName')"
            :rules="reportRules.name"
            class="mt-2 is-required"
          ></v-text-field>
          <div
            class="text-base drawer-2 mb-2 text--primary font-weight-semibold-light"
          >
            {{ $t('report.headers.status') }}
          </div>
          <v-radio-group
            v-model="editReport.active"
            row
            hide-details
            color="primary"
            class="ma-0 pa-0"
          >
            <v-row class="ma-0">
              <v-col
                v-for="(item, index) in activeList"
                :key="index"
                class="pa-0"
              >
                <v-radio :label="item.text" :value="item.value" class="mr-0">
                  <template #label>
                    <span class="color-base">{{ item.text }}</span>
                  </template>
                </v-radio>
              </v-col>
            </v-row>
          </v-radio-group>

          <div
            class="text-base text--primary font-weight-semibold-light mt-6 mb-2"
          >
            {{ $t('report.headers.period') }}
          </div>
          <v-radio-group
            v-model="editReport.period"
            row
            hide-details
            :disabled="editFlag"
            color="primary"
            class="ma-0 pa-0"
            @change="changePeriod"
          >
            <v-row class="ma-0">
              <v-col
                v-for="(item, index) in reportCycleList"
                :key="index"
                class="pa-0"
              >
                <v-radio :label="item.text" :value="item.value" class="mr-0">
                  <template #label>
                    <span class="color-base">{{ item.text }}</span>
                  </template>
                </v-radio>
              </v-col>
            </v-row>
          </v-radio-group>

          <div
            class="text-base text--primary font-weight-semibold-light mt-6 mb-2"
          >
            {{ $t('report.headers.model') }}
          </div>
          <v-radio-group
            v-model="editReport.modelType"
            row
            hide-details
            color="primary"
            class="ma-0 pa-0"
            :disabled="editFlag"
          >
            <v-row class="ma-0">
              <v-col
                v-for="(item, index) in modelTypeEnum"
                :key="index"
                class="pa-0"
              >
                <v-radio :label="item.text" :value="item.value" class="mr-0">
                  <template #label>
                    <span class="color-base">{{ item.text }}</span>
                  </template>
                </v-radio>
              </v-col>
            </v-row>
          </v-radio-group>

          <v-select
            v-if="editReport.modelType === '1'"
            v-model="editReport.vehicleModel"
            :menu-props="{ offsetY: true }"
            :label="$t('report.headers.model')"
            :items="modelList"
            :rules="reportRules.vehicleModel"
            item-text="text"
            item-value="value"
            class="mt-6 is-required"
            :disabled="editFlag"
          >
          </v-select>

          <v-select
            v-model="editReport.reportType"
            :menu-props="{ offsetY: true }"
            :label="$t('report.headers.reportTypeName')"
            :items="reportTypeOptions"
            :rules="reportRules.type"
            item-text="text"
            item-value="value"
            :disabled="editFlag"
            class="mt-6 is-required"
          >
          </v-select>
          <div
            class="text-base drawer-2 my-2 text--primary font-weight-semibold-light"
          >
            {{ $t('report.headers.email') }}
          </div>
          <v-radio-group
            v-model="editReport.emailPushStatus"
            row
            hide-details
            color="primary"
            class="ma-0 pa-0"
          >
            <v-row class="ma-0">
              <v-col
                v-for="(item, index) in reportEmailList"
                :key="index"
                class="pa-0"
              >
                <v-radio :label="item.text" :value="item.value" class="mr-0">
                  <template #label>
                    <span class="color-base">{{ item.text }}</span>
                  </template>
                </v-radio>
              </v-col>
            </v-row>
          </v-radio-group>
          <div v-if="editReport.emailPushStatus === '0'" class="mb-6">
            <v-autocomplete
              v-model="editReport.recipientsList"
              multiple
              :menu-props="{ offsetY: true, maxHeight: 300 }"
              append-icon="mdi-chevron-down"
              :items="userList"
              chips
              item-text="text"
              item-value="value"
              :label="$t('response.swal.notice1')"
              :rules="[v => required(v, $t('response.swal.notice1'))]"
              class="mt-2 is-required"
            >
              <template v-slot:selection="{ item, index }">
                <v-chip
                  v-if="index <= 2"
                  color="primary"
                  close
                  small
                  @click:close="editReport.recipientsList.splice(index, 1)"
                >
                  <span>{{ item.text }}</span>
                </v-chip>
                <span v-if="index === 3">
                  (+{{ editReport.recipientsList.length - 3 }})
                </span>
              </template>
            </v-autocomplete>
            <v-text-field
              v-model="editReport.noticeTitle"
              color="primary"
              :label="$t('report.email.subject')"
              :rules="[v => required(v, $t('report.email.subject'))]"
              class="mt-1 is-required"
            ></v-text-field>
            <quill-editor
              :key="editorKey"
              ref="myQuillEditor"
              v-model="editReport.noticeContent"
              class="editor-box editor is-required mt-2"
              :options="editorOption"
              @change="onEditorChange($event)"
            >
            </quill-editor>
            <div v-if="isContentShow" style="color: #da1f1f" class="mt-2">
              {{ $t('report.edit.content') }}
            </div>
            <div class="mt-4">
              <div class="d-inline-flex primary--text cursor-pointer">
                {{ $t('response.swal.tip4') }}
              </div>
              <div class="mt-2">
                {{ $t('response.swal.tip5') }}
              </div>
              <v-data-table
                :headers="tableHeaders"
                hide-default-footer
                fixed-header
                :items="tagTable"
                item-key="value"
                class="table border-radius-xl mt-2 thead-light"
              >
                <template v-slot:item.value="{ item }">
                  <div class="d-flex">
                    <span>{{ item.value }}</span>
                    <vsoc-icon
                      type="fill"
                      class="action-btn ml-2 cursor-pointer"
                      icon="icon-fuzhi"
                      v-copy="item.value"
                    />
                  </div>
                </template>
              </v-data-table>
            </div>
          </div>
        </v-form>
      </vsoc-drawer>
    </v-card>
  </div>
</template>

<script>
import { required } from '@/@core/utils/validation'
import {
  addReportConfig,
  deleteReportConfig,
  editReportConfig,
  getReportConfig,
} from '@/api/report/report'
import { findAllUsers } from '@/api/system/user'
import TableSearch from '@/components/TableSearch/index.vue'
import VsocDrawer from '@/components/VsocDrawer.vue'
import VsocPagination from '@/components/VsocPagination.vue'
import BreadCrumb from '@/components/bread-crumb/index.vue'
import VsocDateRange from '@/components/vsoc-date-range/VsocDateRange.vue'
import { PAGESIZE } from '@/util/constant'
import { deepClone, setRemainingHeight } from '@/util/utils'
import 'quill/dist/quill.bubble.css'
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import { quillEditor } from 'vue-quill-editor'
export default {
  name: 'ReportInstance',
  components: {
    VsocDrawer,
    VsocPagination,
    VsocDateRange,
    BreadCrumb,
    TableSearch,
    quillEditor,
  },
  data() {
    return {
      tagTable: [
        {
          value: '${report.url}',
          name: '报告链接',
        },
        {
          value: '${report.week}',
          name: '报告周数',
        },
        {
          value: '${report.month}',
          name: '报告月份',
        },
        {
          value: '${report.year}',
          name: '报告年份',
        },
      ],
      editorKey: 0,
      required,
      menu: false,
      // dateRange: {
      //   range: {
      //     start: new Date().toISOString().substr(0, 7),
      //     end: new Date().toISOString().substr(0, 7),
      //   },
      //   menuProps: { offsetY: true, closeOnContentClick: false },
      // },
      editFlag: false,
      showEditGroup: false,
      // periodName: ['月度', '季度', '年度', '单次'], // 0:月度 1：季度  2：年度  3单次
      editType: 'add',
      reportTypeOptions: [],

      tableData: [],
      tableHeight: '34.5rem',
      tableLoading: false,
      btnLoading: false,
      query: {
        pageNum: 1,
        pageSize: PAGESIZE,
        reportName: '',
      },
      filterList: [],
      tableDataTotal: 0,
      valid: false,

      // 编辑报告信息
      editReport: {
        id: '',
        reportName: '',
        reportType: '',
        active: '1',
        period: '1',
        emailPushStatus: '1',
        recipientsList: [],
        noticeTitle: '',
        noticeContent: '',
        modelType: '0', // 0:全部 1:车型
        vehicleModel: '', // 车型
      },
      userList: [],
      modelList: [],
      reportRules: {
        name: [v => required(v, this.$t('report.headers.reportName'))],
        type: [v => required(v, this.$t('report.headers.reportTypeName'))],
        vehicleModel: [v => required(v, this.$t('report.headers.model'))],
      },
      editActive: '',
      isContentShow: false,
      tagShow: false,
    }
  },
  watch: {
    '$i18n.locale': {
      handler(newVal) {
        this.editorKey++
      },
      deep: true,
    },
  },
  computed: {
    modelTypeEnum() {
      return Object.assign([], this.$store.state.enums.enums.ModelType)
    },
    tableHeaders() {
      return [
        {
          text: this.$t('response.infoHeaders.value'),
          value: 'value',
          width: '100px',
        },
        {
          text: this.$t('response.infoHeaders.name'),
          value: 'name',
          width: '100px',
        },
      ]
    },
    editorOption() {
      return {
        placeholder: this.$t('report.email.content'),
        modules: {
          toolbar: false,
        },
      }
    },
    searchList() {
      return [
        {
          type: 'input',
          value: 'reportName',
          text: this.$t('report.headers.reportName'),
        },
      ]
    },
    headers() {
      return [
        {
          text: '',
          value: 'reportType',
          width: '50px',
        },
        {
          text: this.$t('report.headers.reportName'),
          value: 'reportName',
          width: '160px',
        },
        {
          text: this.$t('report.headers.reportTypeName'),
          value: 'reportTypeName',
          width: '160px',
        },
        {
          text: this.$t('report.headers.period'),
          value: 'periodName',
          width: '120px',
        },
        {
          text: this.$t('report.headers.model'),
          value: 'modelType',
          width: '120px',
        },
        {
          text: this.$t('report.headers.status'),
          value: 'active',
          width: '120px',
        },
        {
          text: this.$t('global.createDate'),
          value: 'createDate',
          width: '160px',
        },
        {
          text: this.$t('global.updateDate'),
          value: 'updateTime',
          width: '160px',
        },
        {
          text: this.$t('global.updateUser'),
          value: 'updateUser',
          width: '120px',
        },
        {
          text: '',
          value: 'actions',
          width: '120px',
          sortable: false,
        },
      ]
    },
    activeEnum() {
      return this.$store.state.enums.enums.ActiveStatus
    },
    activeList() {
      return this.$store.getters['enums/getActiveStatus']
    },
    reportCycleList() {
      // console.log(this.$store.getters['enums/getReportCycle'])
      return this.$store.getters['enums/getReportCycle'].filter(
        v => v.value !== '0',
      )
    },
    reportEmailList() {
      return this.$store.getters['enums/getReportEmail']
    },
  },
  created() {
    this.$_setTableHeight()
    this.$bus.$on('resize', this.$_setTableHeight)
    this.$_search()
    this.getAllVehicle()
    this.getUsers()
    this.changePeriod(this.editReport.period)
  },
  beforeDestroy() {
    this.$bus.$off('resize', this.$_setTableHeight)
  },

  methods: {
    //获取车型
    async getAllVehicle() {
      const data = await this.$store.dispatch('global/loadAllAutomaker')
      this.modelList = data || []
    },
    onEditorChange(e) {
      this.isContentShow = this.editReport.noticeContent ? false : true
    },
    async getUsers() {
      const res = await findAllUsers({ status: '1' })
      this.userList = res.data.map(v => {
        return {
          ...v,
          value: v.userId,
          text: v.userName,
        }
      })
    },
    onClear() {
      this.query.reportName = ''
      this.$_search()
    },
    // RANGE_STR,
    // 时间范围改变
    // onChangeDate(range) {
    //   this.dateRange.range = range
    //   this.editReport.startDate = range.start
    //   this.editReport.endDate = range.end
    // },
    changePeriod(val) {
      // this.editReport.type = ''
      const reportTypList = Object.assign(
        [],
        this.$store.state.enums.enums.ReportType,
      )
      if (val === '0') {
        this.reportTypeOptions = reportTypList.filter(
          v => ['0', '1', '2'].indexOf(v.dictId) !== -1,
        )
      } else {
        this.reportTypeOptions = reportTypList.filter(
          v => ['3', '4', '5'].indexOf(v.dictId) !== -1,
        )
      }
    },
    add() {
      this.editType = 'add'
      this.editFlag = false
      this.showEditGroup = true
      this.editReport = {
        id: '',
        reportName: '',
        reportType: '',
        active: '1',
        period: '1',
        emailPushStatus: '1',
        recipientsList: [],
        noticeTitle: '',
        noticeContent: '',
        modelType: '0', // 0:全部 1:车型
        vehicleModel: '',
      }
      this.$refs.form.resetValidation()
    },
    edit(item) {
      this.editType = 'edit'
      this.editFlag = true
      this.editReport = deepClone(item)
      this.editReport.recipientsList = item.recipients
        ? item.recipients.split(',')
        : []
      this.editActive = item.active
      this.showEditGroup = true
    },
    async confirmReport(callBack) {
      const bool = this.$refs.form.validate()
      const data = deepClone(this.editReport)
      if (!bool || (data.emailPushStatus === '0' && !data.noticeContent)) {
        this.isContentShow =
          data.emailPushStatus === '0' && !data.noticeContent ? true : false
        return callBack(false, true)
      }
      if (data.emailPushStatus === '1') {
        data.noticeTitle = ''
        data.noticeContent = ''
        data.recipients = ''
        data.recipientsList = []
      }
      try {
        if (this.editType === 'add') {
          const res = await addReportConfig(data)
          if (res.code === 200) {
            this.$notify.info(
              'success',
              this.$t('global.hint.add', [this.$t('report.currentTitle')]),
            )
          }
          callBack()
          this.$_search()
        }
        if (this.editType === 'edit') {
          if (this.editActive !== data.active) {
            this.$swal({
              title: this.$t('report.btn.edit'),
              text: this.$t('report.edit.text', [data.reportName]),
              icon: 'warning',
              reverseButtons: true,
              showCancelButton: true,
              confirmButtonText: this.$t('action.confirm'),
              cancelButtonText: this.$t('action.cancel'),
              customClass: {
                container: 'container-box',
                confirmButton: 'sweet-btn-primary',
              },
            }).then(async result => {
              if (result.isConfirmed) {
                try {
                  await this.updateReport(data, callBack)
                } catch (e) {
                  console.error(`编辑报告错误：${e}`)
                }
              } else {
                callBack(false, true)
              }
            })
          } else {
            await this.updateReport(data, callBack)
          }
        }
      } catch (e) {
        console.error(`编辑报告错误：${e}`)
        callBack(false, true)
      }
    },
    async updateReport(data, callBack) {
      const res = await editReportConfig(data)
      if (res.code === 200) {
        this.$notify.info(
          'success',
          this.$t('global.hint.edit', [this.$t('report.currentTitle')]),
        )
      }
      callBack && callBack()
      this.$_search()
    },
    $_search() {
      this.query.pageNum = 1
      this.getTableData()
    },
    async getTableData() {
      try {
        // handleQueryParams({
        //   type: 'set',
        //   query: this.query,
        //   key: 'reportConfig',
        // })
        this.tableLoading = true
        this.tableData = []
        const res = await getReportConfig(this.query)
        this.tableData = res.data.records
        this.tableDataTotal = res.data.total
      } catch (e) {
        console.error(`获取报告实例：${e}`)
      }
      this.tableLoading = false
    },
    $_setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = setRemainingHeight() - 12
      })
    },
    //  删除报告
    del(item) {
      this.$swal({
        title: this.$t('report.del.title'),
        text: this.$t('report.del.text', [item.reportName]),
        icon: 'warning',
        reverseButtons: true,
        showCancelButton: true,
        confirmButtonText: this.$t('action.confirm'),
        cancelButtonText: this.$t('action.cancel'),
        customClass: {
          confirmButton: 'sweet-btn-primary',
        },
      }).then(async result => {
        if (result.isConfirmed) {
          this.$set(item, 'btnLoading', true)
          try {
            const res = await deleteReportConfig(item.id)
            if (res.code === 200) {
              this.$notify.info(
                'success',
                this.$t('global.hint.del', [this.$t('report.currentTitle')]),
              )
            }
            this.$set(item, 'btnLoading', false)
            this.$_search()
          } catch (e) {
            console.error(`删除报告错误：${e}`)
            this.$set(item, 'btnLoading', false)
          }
        }
      })
    },
  },
}
</script>
<style>
::v-deep .swal2-container,
.swal2-popup {
  z-index: 9999;
}
.editor {
  height: 200px !important;
}
</style>
