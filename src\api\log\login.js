import { request, vsocPath } from '../../util/request'

export const getLoginLog = function (data) {
  return request({
    url: `${vsocPath}/loginLog/logs`,
    method: 'post',
    data,
  })
}

export const exportLoginLog = function (data) {
  return request({
    url: `${vsocPath}/loginLog/export`,
    method: 'post',
    data,
    loading: true,
    // headers: {
    //   'Content-Type': 'application/x-download',
    // },
    responseType: 'blob',
  })
}
