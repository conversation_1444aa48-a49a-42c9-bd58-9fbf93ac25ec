/**
 * Simple throttle function that executes a passed function only once in the specified timeout
 * @param handlerFunc
 * @param [timeout] the throttle interval
 */
export function throttle(handlerFunc, timeout = 66) {
  let resizeTimeout
  if (!resizeTimeout) {
    resizeTimeout = setTimeout(() => {
      resizeTimeout = null
      handlerFunc()

      // The actualResizeHandler will execute at a rate of 15fps
    }, timeout)
  }
}

// 深度克隆
export function deepClone(obj) {
  if (!obj) return null
  const result = typeof obj.splice === 'function' ? [] : {} // obj为list时typeof obj.splice为'function',为对象时typeof obj.splice为'undefined'
  if (obj && typeof obj === 'object') {
    for (const key in obj) {
      if (obj[key] && typeof obj[key] === 'object') {
        result[key] = deepClone(obj[key])
      } else {
        result[key] = obj[key]
      }
    }

    return result
  }

  return obj
}
