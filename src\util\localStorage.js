export function getLocalStorage(key) {
  return localStorage.getItem(process.env.BASE_URL + key)
}

export function setLocalStorage(key, value) {
  localStorage.setItem(process.env.BASE_URL + key, value)
}

export function removeLocalStorage(key) {
  localStorage.removeItem(process.env.BASE_URL + key)
}

export function clearLocalStorage() {
  for (let i = localStorage.length - 1; i >= 0; i--) {
    const key = localStorage.key(i)
    if (key.startsWith(process.env.BASE_URL)) {
      localStorage.removeItem(key)
    }
  }
}
