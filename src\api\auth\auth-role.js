import { deleteRequest, getRequest, postRequest, putRequest, vsocPath } from '../../util/request'

const MODULE_PATH = vsocPath

export function qeuryRoleLike(param, page, size) {
  Object.assign(param, { page, size })

  return getRequest(`${MODULE_PATH}/auth-role/like`, param)
}

export function addAuthRole(authRole) {
  return postRequest(`${MODULE_PATH}/auth-role`, authRole)
}

export function updateAuthRole(id, authRole) {
  return putRequest(`${MODULE_PATH}/auth-role`, authRole)
}

export function deleteAuthRole(ids) {
  return deleteRequest(`${MODULE_PATH}/auth-role`, ids)
}
