<template>
  <div>
    <bread-crumb
      ref="topHeader"
      :showDoQuery="showDoQuery"
      :filterList="filterList"
      :showAdvance="true"
    >
      <template slot="center">
        <div ref="tab">
          <v-tabs
            v-model="query.assetType"
            height="54"
            centered
            @change="$_search"
          >
            <v-tab
              class="mr-14"
              v-for="item in assetTypeEnum"
              :key="item.value"
              :disabled="tableLoading"
            >
              <!-- <v-icon size="1.5rem"> {{ item.icon }} </v-icon> -->
              <span class="ml-2">{{ item.text }}</span>
            </v-tab>
          </v-tabs>
        </div>
      </template>
    </bread-crumb>
    <v-card tile class="pa-0" elevation="0">
      <v-card-text class="main-content">
        <div class="mb-3 d-flex align-center justify-space-between">
          <div>
            <v-btn
              elevation="0"
              color="primary"
              v-has:asset-import
              @click="onBatchImport"
            >
              <span>
                {{ $generateMenuTitle($route.meta.buttonInfo['asset-import']) }}
              </span>
            </v-btn>
          </div>
          <table-search
            :searchList="searchList"
            :searchQuery="query"
            @search="$_search"
          ></table-search>
        </div>

        <!-- :height="tableHeight" -->
        <v-data-table
          ref="assetsTable"
          fixed-header
          class="flex-1 thead-light"
          :height="tableHeight"
          :items-per-page="query.pageSize"
          hide-default-footer
          :headers="headers"
          item-key="id"
          :items="tableData"
          :loading="tableLoading"
          @click:row="$_clickRow"
        >
          <template v-slot:item.type="{ item }">
            <!-- <v-icon
              class="flex-shrink-0"
              name="vehicle"
              width="28"
              height="21"
              mdi-car-connected
            ></v-icon> -->
            <div v-if="assetTypeEnum[item.assetType]" class="d-flex">
              <vsoc-icon
                :icon="assetTypeEnum[item.assetType].icon"
                type="fill"
                class="primary--text"
              >
              </vsoc-icon>
              <span class="ml-2">{{ assetTypeEnum[item.assetType].text }}</span>
            </div>
          </template>

          <template v-slot:item.id="{ item }">
            <div class="d-flex align-center">
              <span v-show-tips class="pl-1">{{ item.id }}</span>
              <!-- v-copy="item.id" -->
              <div @click.stop>
                <!-- <v-icon
                v-show-tips="'复制'"
                size="1rem"
                class="ml-2"
              >
                mdi-content-copy
              </v-icon> -->
                <v-btn v-copy="item.id" v-show-tips="'复制'" icon>
                  <!-- <v-icon size="1.25rem"> mdi-content-copy </v-icon> -->
                  <vsoc-icon
                    type="fill"
                    class="secondary--text"
                    icon="icon-fuzhi"
                  ></vsoc-icon>
                </v-btn>
              </div>
            </div>
          </template>
          <template v-slot:item.model="{ item }">
            <span v-show-tips>{{ item.model }}</span>
          </template>
          <template v-slot:item.vin="{ item }">
            <div class="d-flex align-center" v-if="item.vin">
              <vsoc-icon
                v-if="assetActivityLevelEnum.length"
                type="fill"
                class="mr-2"
                :style="{
                  color:
                    item.diffDays <= assetActivityLevelEnum[0]
                      ? '#12e055'
                      : item.diffDays > assetActivityLevelEnum[0] &&
                        item.diffDays <= assetActivityLevelEnum[1]
                      ? '#f5d018'
                      : '#b4bbcc',
                }"
                icon="icon-huoyuedu"
              ></vsoc-icon>
              <div
                v-show-tips
                style="max-width: 160px"
                class="text-no-wrap ml-2"
              >
                {{ item.vin }}
              </div>
              <v-btn
                v-copy="item.vin"
                v-show-tips="$t('action.copy')"
                icon
                class="ml-1"
                @click.stop
              >
                <vsoc-icon
                  type="fill"
                  icon="icon-fuzhi"
                  class="secondary--text"
                ></vsoc-icon>
              </v-btn>
            </div>
            <span v-else>N/A</span>
          </template>

          <template v-slot:item.assetAlarmInfoVos="{ item }">
            <div class="d-flex align-center">
              <!-- <v-avatar
              v-if="item.alarmInfo.disasterNumber"
              size="2.25rem"
              :color="$alertColor0"
              class="mr-1"
            >
              <span
                v-show-tips="item.alarmInfo.disasterNumber"
                class="font-weight-semibold text-truncate text-no-wrap white--text"
              >{{ item.alarmInfo.disasterNumber | tranNumber(1) }}</span>
            </v-avatar> -->
              <div
                v-if="item.assetAlarmInfoVos.length > 0"
                @click.stop="onAssetAlarm(item)"
                class="cursor-pointer"
              >
                <v-avatar
                  v-for="(alarmItem, index) in item.assetAlarmInfoVos"
                  :key="index"
                  class="mr-1 cursor-pointer"
                  :color="alarmLevel[alarmItem.alarmLevel].color || ''"
                  size="1.5rem"
                >
                  <span
                    v-show-tips="
                      alarmLevel[alarmItem.alarmLevel].text +
                      ':' +
                      alarmItem.alarmCount
                    "
                    class="text-base text-no-wrap white--text"
                    >{{ alarmItem.alarmCount | tranNumber(1) }}</span
                  >
                </v-avatar>
              </div>
              <div v-else>
                {{ '' | dataFilter }}
              </div>
              <!-- <v-avatar
              v-if="item.alarmInfo.minorNumber"
              class="mr-1"
              :color="$alertColor2"
              size="2.25rem"
            >
              <span
                v-show-tips="item.alarmInfo.minorNumber"
                class="font-weight-semibold text-truncate text-no-wrap white--text"
              >{{ item.alarmInfo.minorNumber | tranNumber(1) }}</span>
            </v-avatar>
            <v-avatar
              v-if="item.alarmInfo.warningNumber"
              class="mr-1"
              :color="$alertColor3"
              size="2.25rem"
            >
              <span
                v-show-tips="item.alarmInfo.warningNumber"
                class="font-weight-semibold text-truncate text-no-wrap white--text"
              >{{ item.alarmInfo.warningNumber | tranNumber(1) }}</span>
            </v-avatar>
            <v-avatar
              v-if="item.alarmInfo.infoNumber"
              class="mr-1"
              :color="$alertColor4"
              size="2.25rem"
            >
              <span
                v-show-tips="item.alarmInfo.infoNumber"
                class="font-weight-semibold text-truncate text-no-wrap white--text"
              >{{ item.alarmInfo.infoNumber | tranNumber(1) }}</span>
            </v-avatar> -->
            </div>
          </template>
          <template v-slot:item.regisDate="{ item }">
            <span>{{ item.regisDate | toDate }}</span>
          </template>
          <template v-slot:item.lastReceiveDate="{ item }">
            <span>{{ item.lastReceiveDate | toDate }}</span>
          </template>
        </v-data-table>
        <!-- 分页器 -->
        <vsoc-pagination
          :page.sync="query.pageNum"
          :size.sync="query.pageSize"
          :total="tableDataTotal"
          @change-page="$_getTableData"
          @change-size="$_getTableData"
        />
      </v-card-text>
      <!-- 地图组件 -->
      <!-- <vsoc-map-sheet
        :value="showMap"
        :map-info="mapInfo"
        :is-drawer="true"
        @input="showMap = false"
      ></vsoc-map-sheet> -->
      <asset-drawer
        ref="advancedDrawer"
        v-model="showAdvanceSearch"
        :modelItems="modelItems"
        :groupList="groupList"
        @do-query="doQuery"
      ></asset-drawer>
      <batch-import ref="batchImport" @refresh="$_search"></batch-import>
    </v-card>
  </div>
</template>

<script>
import VsocFilter from '@/components/VsocFilter.vue'
import BreadCrumb from '@/components/bread-crumb/index.vue'
import { RANGE_STR } from '@/components/vsoc-date-range/constants'
import { PAGESIZE } from '@/util/constant'

// import VsocDrawer from '@/components/VsocDrawer.vue'
import { vehicles } from '@/api/asset/index'
import VsocPagination from '@/components/VsocPagination.vue'

// import VsocDateRange from '@/components/VsocDateRange.vue'
import VsocDateRange from '@/components/vsoc-date-range/VsocDateRange.vue'
import {
  clearFilterItem,
  deepClone,
  handleFilterItem,
  handleQueryParams,
  setRemainingHeight,
  updateURL,
} from '@/util/utils'

import TableSearch from '@/components/TableSearch/index.vue'
import { sortBy } from 'lodash'
import AssetDrawer from './components/AssetDrawer.vue'

export default {
  name: 'AssetIndex',
  components: {
    VsocFilter,
    VsocPagination,
    BreadCrumb,
    // VsocDateRange,
    VsocDateRange,
    // VsocMapSheet,
    AssetDrawer,
    TableSearch,
    // VsocDrawer,
  },

  data() {
    return {
      dateRange: {
        range: {},
        menuProps: { offsetY: true, closeOnContentClick: false },
      },
      searchQuery: '',

      // 当前态势查询条件列表
      searchConditions: [
        {
          text: '资产编号',
          value: 'id',
        },
        {
          text: 'VIN',
          value: 'vin',
        },
        {
          text: '车型',
          value: 'vehicleType',
        },
        {
          text: '当前态势',
          value: 'healthStatus',
        },
      ],

      // 查询内容
      queryKey: 'id',

      // 展示最近位置
      showMap: false,

      // 展示高级查询
      showAdvanceSearch: false,
      advanceQuery: {},

      // 查询条件下拉选择
      query: {
        id: '', // 资产编号
        pageNum: 1, // 当前页码
        pageSize: PAGESIZE,
        model: '', // 车型
        startLastReceiveDate: '',
        endLastReceiveDate: '',
        groupNameList: [], // 资产组
        startRegisDate: '', // 首次登记时间起始时间
        endRegisDate: '', // 首次登记时间结束时间
        healthStatusList: [], // 态势：A B C D
        vin: '',
        date: [],
        assetType: 0,
        groupType: '0',
      },
      filterList: [],

      tableData: [],
      tableHeight: '552px',
      tableDataTotal: 0,
      tableLoading: false,
      selectedArray: [],
      mapInfo: {},
      modelItems: [],
      groupList: [],
      groupNameListEnum: {},
    }
  },
  computed: {
    // assetActivityLevelEnum() {
    //   return this.$store.getters['enums/getAssetActivityLevel']
    // },
    // mapEnabledStatus() {
    //   return this.$store.getters['enums/getMapEnabledStatus']
    // },
    searchList() {
      return [
        {
          type: 'date',
          value: ['startRegisDate', 'endRegisDate'],
          text: this.$t('asset.headers.firstRegistrationTime'),
          dateRange: {
            range: {
              start: '',
              end: '',
            },
            menuProps: { offsetY: true, closeOnContentClick: false },
          },
        },
        {
          type: 'combobox',
          value: 'model',
          itemList: this.modelItems,
          text: this.$t('asset.headers.model'),
        },
        {
          type: 'input',
          value: 'id',
          text: `${this.$t('asset.headers.assetId')}/VIN`,
        },
      ]
    },
    headers() {
      return [
        {
          text: this.$t('global.assetType'),
          value: 'type',
          width: 120,
        },
        {
          text: this.$t('asset.headers.assetId'),
          value: 'id',
          width: '100px',
        },
        {
          text: this.$t('asset.headers.model'),
          value: 'model',
          width: 100,
        },
        {
          text: 'VIN',
          value: 'vin',
          width: '160px',
        },
        {
          text: this.$t('asset.headers.currentSituation'),
          value: 'healthStatus',
          width: 180,
        },
        {
          text: this.$t('asset.headers.newAlert'),
          value: 'assetAlarmInfoVos',
          width: '260px',
          sortable: false,
        },
        {
          text: this.$t('asset.headers.assetGroup'),
          value: 'assetGroupList',
          width: '160px',
          sortable: false,
        },
        {
          text: this.$t('asset.headers.firstRegistrationTime'),
          value: 'regisDate',
          width: 200,
        },
        {
          text: this.$t('asset.headers.lastActiveTime'),
          value: 'lastReceiveDate',
          width: '180px',
        },
        {
          text: this.$t('asset.headers.lastPosition'),
          value: 'location',
          width: 120,
          sortable: false,

          // calculateWidths: true,
        },
      ]
    },
    assetTypeEnum() {
      return this.$store.state.enums.enums.AssetType
    },
    alarmLevel() {
      return this.$store.state.enums.enums.AlarmLevel
    },
    // vehicleStatus() {
    //   return this.$store.state.enums.enums.HealthStatus
    // },

    // vehicleStatusArray() {
    //   const arr = []
    //   for (const key in this.vehicleStatus) {
    //     arr.push(this.vehicleStatus[key])
    //   }

    //   return arr
    // },
    // groupsTypeEnum() {
    //   return this.$store.state.enums.enums.GroupsType
    // },
  },

  watch: {
    filterList() {
      this.$_setTableHeight()
    },
  },
  created() {
    // this.loadGroupData()
    this.loadAllAutomaker()
    if (
      JSON.stringify(this.$route.query) !== '{}' &&
      this.$route.query.isQuery
    ) {
      if (this.$route.query.all) {
        this.$_search()
      } else {
        const keyList = Object.keys(this.$route.query).filter(
          v => v !== 'isQuery',
        )
        keyList.forEach(key => {
          if (Array.isArray(this.query[key])) {
            this.query[key] = JSON.parse(this.$route.query[key])
          } else {
            if (key === 'startRegisDate') {
              this.searchList[0].dateRange.range.start = this.$route.query[key]
            }
            if (key === 'endRegisDate') {
              this.searchList[0].dateRange.range.end = this.$route.query[key]
            }
            this.query[key] = this.$route.query[key]
          }
        })
        this.$_search()
      }
    } else {
      const query = handleQueryParams({ type: 'get', key: 'assets' })
      if (query) {
        this.query = query
        if (!this.query.groupType) {
          this.query.groupType = '0'
        }
      }
      this.$_search()
    }
    this.$_setTableHeight()
    this.$bus.$on('resize', this.$_setTableHeight)
  },
  beforeDestroy() {
    this.$bus.$off('resize', this.$_setTableHeight)
  },
  methods: {
    // 加载资产组信息
    // async loadGroupData() {
    //   const data = await this.$store.dispatch('global/searchGroups')
    //   this.groupList = data
    //   data.forEach(v => {
    //     this.groupNameListEnum[v.value] = v
    //   })
    //   this.$_appendFilterListItem()
    // },
    onBatchImport() {
      this.$refs.batchImport.isShow = true
    },
    //获取车型
    async loadAllAutomaker() {
      const data = await this.$store.dispatch('global/loadAllAutomaker')
      this.modelItems = data.map(v => v.name)
    },
    onClear() {
      this.query.id = ''
      this.$_search()
    },
    RANGE_STR,
    onAssetAlarm(item) {
      const alarmLevelList = item.assetAlarmInfoVos.map(v => v.alarmLevel)
      this.$router.push({
        path: '/alerts',
        query: {
          isQuery: 1,
          vehicleId: item.id,
          statusList: JSON.stringify(['0', '1']),
          alarmLevelList: JSON.stringify(alarmLevelList),
        },
      })
    },

    // 首次登记时间改变
    onChangeDate(range) {
      this.dateRange.range = range
      this.query.startRegisDate = range.start
      this.query.endRegisDate = range.end
    },

    onReset() {
      Object.assign(this.$data.query, this.$options.data.call(this).query)
      this.$_search()
    },

    // 查询按钮
    $_search() {
      if (this.tableLoading) return
      //如果route的值为空，则清空route的参数
      if (JSON.stringify(this.$route.query) !== '{}') {
        updateURL.call(this)
      }
      this.query.pageNum = 1
      this.$_getTableData()

      setTimeout(() => {
        this.$_appendFilterListItem()
      })
    },

    // 获取后台数据 TODO
    async $_getTableData() {
      try {
        // console.log('TEsting  /data-table/asset begin')

        this.tableLoading = true

        this.tableData = []
        handleQueryParams({ type: 'set', query: this.query, key: 'assets' })

        const params = deepClone(this.query)

        params.groupNameList = this.query.groupNameList

        delete params.date
        delete params.healthStatus
        // delete params.healthStatus
        // 保存查询参数
        const res = await vehicles(params)
        this.tableDataTotal = res.data.total

        // const data = await getVehicleAssetListDt({
        //   // ...handleParams(this.query),
        //   page: this.query.pageNum - 1,
        // })
        this.tableData = res.data.records.map((record, index) => {
          record.groupNames =
            record.groupNameList && record.groupNameList.length > 0
              ? record.groupNameList?.join('，')
              : 'N/A'

          record.location = `${record.longitude},${record.latitude}`

          // 20221110:模拟【待处理告警】数据，待关联、修改
          // record.alarmInfo = data.content[index % 2].alarmInfo
          // 升序
          record.assetAlarmInfoVos = sortBy(
            record.assetAlarmInfoVos,
            'alarmLevel',
          )
          // record.diffDays =
          //   moment().diff(
          //     moment(record.lastReceiveDate),
          //     'days',
          //   )
          record.diffDays =
            (new Date().getTime() -
              new Date(record.lastReceiveDate).getTime()) /
            86400000
          return record
        })
      } catch (error) {
        console.error(`获取资产管理信息：${error}`)
      }
      this.tableLoading = false
    },

    // 添加查询条件
    $_appendFilterListItem() {
      const searchKeyList = [
        // {
        //   key: 'id',
        //   type: 'String',
        // },
        {
          key: 'vin',
          type: 'String',
          label: 'VIN',
        },

        // {
        //   key: 'vehicleType',
        //   type: 'String',
        // },
        // {
        //   key: 'groupType',
        //   type: 'String',
        //   label: 'assetGroup.groupType',
        //   mapKey: 'groupsTypeEnum',
        //   close: false,
        // },
        {
          key: 'healthStatusList',
          type: 'Array',
          mapKey: 'vehicleStatus',
          label: 'asset.headers.currentSituation',
        },
        // {
        //   key: 'groupNameList',
        //   type: 'Array',
        //   mapKey: 'groupNameListEnum',
        //   label: 'asset.headers.assetGroup',
        // },
        {
          key: 'dateTime',
          type: 'time',
          mapKey: ['startLastReceiveDate', 'endLastReceiveDate'],
          label: 'asset.headers.lastActiveTime',
          close: true,
        },
      ]
      this.$_setTableHeight()
      handleFilterItem.call(this, searchKeyList)
    },

    // 展示高级查询
    showDoQuery() {
      this.showAdvanceSearch = true

      this.advanceQuery = deepClone(this.query)

      // 多选
      // this.advanceQuery.healthStatus = this.query.healthStatus.split(',')
      delete this.advanceQuery.pageNum
      delete this.advanceQuery.pageSize
      this.$refs.advancedDrawer.advanceQuery = this.advanceQuery
    },

    // 设置表格高度
    $_setTableHeight() {
      this.$nextTick(() => {
        const filterFn = () => {
          return -this.$refs.topHeader.filterHeight
        }
        this.tableHeight = setRemainingHeight(filterFn)
      })
    },

    //  高级查询
    doQuery(advanceQuery) {
      this.query = Object.assign(this.query, deepClone(advanceQuery))
      this.dateRange.range = {
        start: this.query.startRegisDate,
        end: this.query.endRegisDate,
      }
      this.$_search()
    },

    // 清除某个查询条件
    $_clearFilter(item) {
      // this.filterList.splice(index, 1)
      // console.log('this.filterList', this.filterList)
      // const tempObj = Object.assign(this.$data.query, this.$options.data.call(this).query)
      // this.query = Object.assign(tempObj, Object.fromEntries(this.filterList))
      // this.$_search()

      const bool = clearFilterItem.call(this, item)
      if (!bool) {
        this.$_search()
      }
    },

    // 单击表格行
    $_clickRow(item) {
      if (this.$route.meta.buttons.includes('asset-detail-btn')) {
        this.$router.push({
          path: `/asset/detail`,
          query: { id: item.id },
        })
      }
    },

    // // 清空表格多选
    // $_clearTableSelected() {
    //   this.$refs.assetsTable && this.$refs.assetsTable.toggleSelectAll()
    //   this.selectedArray = []
    // },
  },
}
</script>

<style lang="scss">
.status-color {
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  margin: 0 1rem;
}
</style>
