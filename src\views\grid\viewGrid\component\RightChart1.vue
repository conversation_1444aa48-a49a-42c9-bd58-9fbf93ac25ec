<template>
  <div class="box" :class="{ 'pb-0': currentTab === 1 }">
    <div class="mh-tab d-flex">
      <dv-border-box-14 :disabled="currentTab !== 1" @click="currentTab = 1">{{
        $t('screen.ticketStatus')
      }}</dv-border-box-14>
      <dv-border-box-14 :disabled="currentTab !== 2" @click="currentTab = 2">{{
        $t('screen.ticketType')
      }}</dv-border-box-14>
    </div>
    <div class="tab-content">
      <tab-item-1
        v-if="currentTab === 1"
        :status="status"
        :total="total"
      ></tab-item-1>
      <tab-item-2 v-if="currentTab === 2" :events="events"></tab-item-2>
    </div>
    <!-- <div class="box-header mb-4">
      告警事件类型 <span class="ffDef">Top5</span>
    </div> -->
    <!-- <dv-capsule-chart :config="option" /> -->
    <!-- <dv-scroll-ranking-board ref="scroll" :config="config" class="box-chart" /> -->
    <!-- <template>
      <div
        v-for="(earning, index) in list"
        :key="earning.avatar"
        class="d-flex align-center flex-fill"
      >
        <span class="right-chart1-text">{{ earning.title }}</span>
        <v-progress-linear
          background-color="transparent"
          rounded
          :color="earning.color"
          :value="earning.progress"
          class="progress-linear"
          :class="`progress-${index}`"
        >
        </v-progress-linear>
        <span class="right-chart1-value">
          {{ earning.value | numberToFormat }} ({{ earning.progress }}%)
        </span>
      </div>
    </template> -->
    <!-- <dv-capsule-chart :config="option" class="fs-16" /> -->
  </div>
</template>

<script>
import TabItem1 from '@/views/data-big-screen/component/Tab/TabItem1.vue'
import TabItem2 from '@/views/data-big-screen/component/Tab/TabItem2.vue'
import DvBorderBox14 from './dv-border-box14/Index.vue'
const colorList = ['#32FDB8', '#21CBFF', '#FE9837', '#44E2FE', '#FFE436']
const list = [
  {
    name: '恶意外联IP',
    alarmLevel: '3',
    alarmLevelName: '低',
    detectorId: '1663007853415235584',
    alarmPercentage: '35.12%',
    count: 335,
    title: '恶意外联IP',
    color: '#32FDB8',
    value: 335,
    progress: '35.12',
    level: '3',
  },
  {
    name: '疑似危险指令',
    alarmLevel: '5',
    alarmLevelName: '测试',
    detectorId: '1663080128713850880',
    alarmPercentage: '21.59%',
    count: 206,
    title: '疑似危险指令',
    color: '#21CBFF',
    value: 206,
    progress: '21.59',
    level: '5',
  },
  {
    name: '超速',
    alarmLevel: '1',
    alarmLevelName: '高',
    detectorId: '1597109462940778496',
    alarmPercentage: '16.35%',
    count: 156,
    title: '超速',
    color: '#FE9837',
    value: 156,
    progress: '16.35',
    level: '1',
  },
  {
    name: '总线负载过大',
    alarmLevel: '0',
    alarmLevelName: '严重',
    detectorId: '1663093495977476096',
    alarmPercentage: '14.36%',
    count: 137,
    title: '总线负载过大',
    color: '#44E2FE',
    value: 137,
    progress: '14.36',
    level: '0',
  },
  {
    name: '里程欺诈',
    alarmLevel: '1',
    alarmLevelName: '高',
    detectorId: '1622360118140076032',
    alarmPercentage: '12.58%',
    count: 120,
    title: '里程欺诈',
    color: '#FFE436',
    value: 120,
    progress: '12.58',
    level: '1',
  },
]
export default {
  name: 'RightChart1',
  components: {
    DvBorderBox14,
    TabItem1,
    TabItem2,
  },
  props: {
    status: Array,
    events: Array,
    total: [Number, String],
  },
  data() {
    return {
      currentTab: 1,
      list,
      option: {
        data: list.map(t => {
          return {
            name: t.name,
            value: t.value,
          }
        }),
        colors: colorList,
      },
      // config: {
      //   // data: list,
      //   data: [list, list].flat(),
      //   waitTime: 2000,
      // },
    }
  },
}
</script>
