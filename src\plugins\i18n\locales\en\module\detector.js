const detector = {
  currentTitle: 'Detector',
  headers: {
    name: 'Detector name',
    description: 'Description',
    detectorRange: 'Extent',
    detectorType: 'Detection type',
    severity: 'Severity',
    active: 'Activity',
    detectorClassifyNameList: 'Tags',
    createUser: 'Ownership',
    groups: 'Groups',
    disposal: 'Trigger disposal',
    time: 'Polymerization time',
    aggregationtime: 'Aggregation time',
    period: 'Detection period',
    threshold: 'Detection threshold ',
    logic: 'Logic',
    actions: 'Actions',
    signals: 'Signals for investigation',
    config: 'Alert config',
    parameter: 'Parameter',
  },
  allText: 'Detected Data',
  btn: {},
  swal: {
    text: 'Are you sure to delete the detector:{0}?',
    add: 'Add successfully！',
    edit: 'Update successfully！',
    del: {
      title: 'Delete detectors',
      text: 'Whether to confirm the deletion of detectors:{0}?',
    },
    batchDel: {
      title: 'Batch delete detectors',
      text: 'Are you sure to delete a total of {0} detectors?',
    },
    batchExport: {
      title: 'Batch export detectors',
      text: 'Are you sure to export a total of {0} detectors?',
    },
    hint: {
      del: 'Please select detectors to be deleted first!',
      batchDel: 'Successfully deleted detectors in batches!',
      allDel: 'All detectors deleted successfully!',
      export: 'Please select the detector to be exported first!',
      batchExport: 'Successfully exported detectors in batches!',
    },
  },
  hint: {
    del: 'Delete successfully！',
    tip: 'Disposal process has not been configured yet',
    tip1: 'The system will automatically match the pre-set disposal process based on the severity level',
    tip2: 'The alarm triggered by the detector in test mode will not affect the asset situation and will not be displayed in the dashboard',
    tip3: 'Recommended detector type selection: For scenarios that often reach (>100 times/hour), it is strongly recommended to choose "high frequency"',
    tip4: 'Alarm: Low frequency detection (<100 times/hour), supporting single alarm investigation on the platform',
    tip5: 'High frequency: High frequency detection (>100 times/hour), does not support investigation on the platform. Subsequent investigation will be completed through Kafka integration',
    tip6: 'The multi-asset detector triggers an alarm when the number of affected assets exceeds the "detection threshold" within the configured "detection cycle"',
    tip7: 'This detector will aggregate such violations detected by the same asset within the configured aggregation time into an alert',
    tip8: 'The aggregation time should be within 0-1440 minutes',
    tip9: 'Specify the minimum number of affected assets that trigger an alarm',
    tip10:
      'Please choose the disposal method, high-frequency can only choose the Kafka disposal process',
    tip11: 'Groups',
    tip12: 'Selected signals',
    tip13:
      'The alarm triggered by this detector will have the following description, and dynamic signals can be added to the description',
    tip14: 'Max 30',
    tip15: 'Please select the group',
    tip16: 'Select',
    tip17: 'Detection threshold greater than 0',
    tip18: 'You have not selected any disposal process, are you sure?',
  },
  detail: {
    min: ' minutes',
    match: 'Preset matching',
    disposition: 'Custom disposition',
    selected: 'Selected disposition',
    type: 'Type',
    recommed: 'Recommended',
    all: 'All',
    name: 'Signal name',
    modelType: 'Model type',
    signal: 'Detailed signal',
    tip: 'Total number of triggered alarms',
    warn: 'Warn',
    tip1: 'This detection rule has triggered [{0}] alarms. Do you want to update these alarm information?',
  },
  dialog: {
    disposition: 'Select dispositions',
    name: 'Name',
    description: 'Description',
    disposeTypeName: 'Type',
    active: 'Active',
  },
  hover1: 'If there is a duplicate CPE for assets, keep one record',
  hover2: 'If the CPE of the asset is empty, it will not be exported',
  fileHint:
    'The size of a single attachment should not exceed 5M; attachment format requirements: json',
}

export default detector
