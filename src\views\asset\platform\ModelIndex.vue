<template>
  <div>
    <bread-crumb>
      <template v-slot:left>
        <v-chip
          small
          label
          style="font-size: 12px"
          class="color-base ml-3 bg-btn"
        >
          {{ $t('global.pagination.total') }} {{ tableData.length }}
        </v-chip>
      </template>
      <template>
        <table-search
          :searchList="searchList"
          :searchQuery="query"
          @search="$_search"
        ></table-search>
        <!-- <v-text-field
          v-model="query.name"
          class="text-width"
          color="primary"
          hide-details="auto"
          :label="$t('model.headers.name')"
          dense
          outlined
          clearable
          @click:clear="onClear"
          @keyup.enter.native="$_search"
        ></v-text-field>
        <v-btn
          elevation="0"
          class="ms-3 primary--text bg-btn"
          :loading="tableLoading"
          @click="$_search"
        >
          {{ $t('action.search') }}
        </v-btn> -->
      </template>
    </bread-crumb>
    <div
      v-if="tableData.length > 0"
      :style="{ height: `calc(100vh - ${topHeaderHeight}px)` }"
      class="overflow-auto scroll-bar-bg"
    >
      <div class="pt-0 pa-2 mt-n1 cards-container ma-auto">
        <div class="pa-2" v-for="item in tableData" :key="item.id">
          <v-card class="pa-6" @click="goAsset(item)" ripple>
            <v-card-title class="pa-0 flex-nowrap">
              <v-divider
                vertical
                class="my-2 mr-2"
                style="border-right-width: 4px; height: 1rem"
                :style="{
                  borderColor:
                    healthStatus[item.level] && healthStatus[item.level].color,
                }"
              ></v-divider>
              <!-- <hr style="height: 1rem; width: 1rem; background: #f00" /> -->
              <span
                v-show-tips="item.name"
                class="text-xxl font-weight-medium text-no-wrap text-overflow-hide"
                style="max-width: 40%"
                >{{ item.name }}
              </span>
              <v-spacer></v-spacer>
              <div class="text-base w-58 d-flex">
                <vsoc-icon
                  icon="icon-kapianshuaxin"
                  type="fill"
                  class="mr-1 accent--text"
                ></vsoc-icon>

                <span
                  class="secondary--text text-overflow-hide"
                  style="min-width: 10.2rem"
                >
                  {{ item.time | toDate }}
                </span>
              </div>
            </v-card-title>

            <v-card-text class="pa-0">
              <div class="d-flex" style="height: 11rem">
                <div
                  class="w-40 h-full d-flex flex-column justify-space-between"
                >
                  <div></div>
                  <div class="mb-2">
                    <span
                      class="text-n5xl mr-2 color-base font-weight-semibold"
                      >{{ item.score || 0 }}</span
                    >
                    <!--                    <span>{{ $tc('global.point', item.score) }}</span>-->
                    <div class="mt-2 d-flex align-center">
                      <span class="color-base">{{
                        $t('model.posture.safePosture')
                      }}</span>
                      <vsoc-icon
                        v-show-tips="item.description"
                        type="fill"
                        size="1.166666rem"
                        class="accent--text ml-2 primary--hover"
                        icon="icon-shuoming"
                      ></vsoc-icon>
                    </div>
                  </div>
                </div>
                <div class="w-60 h-full">
                  <v-img
                    contain
                    height="70%"
                    max-width="100%"
                    :src="
                      item.pictureCode ||
                      require('../../../assets/images/pages/<EMAIL>')
                    "
                  ></v-img>
                  <div class="text-center secondary--text mt-2">
                    {{ $t('model.posture.modelNum')
                    }}<span class="ml-2">{{ item.count | toNumber }}</span>
                  </div>
                </div>
              </div>
            </v-card-text>
          </v-card>
        </div>
      </div>
    </div>

    <div
      v-if="tableLoading === false && tableData.length === 0"
      style="height: 80vh"
      class="d-flex flex-column justify-center align-center"
    >
      <v-img
        max-width="22rem"
        max-height="22rem"
        contain
        :src="require('@/assets/images/tree-empty.png')"
      ></v-img>
      <div class="text--primary text-xxl">
        {{ $t('model.empty', { name: query.name }) }}
      </div>
      <div class="text-ml mt-1">{{ $t('model.hint') }}</div>
    </div>
  </div>
</template>

<script>
import { getModelList } from '@/api/asset/platform'
import breadCrumb from '@/components/bread-crumb/index'
import TableSearch from '@/components/TableSearch/index.vue'

export default {
  name: 'ModelIndex',
  components: {
    breadCrumb,
    TableSearch,
  },
  data() {
    return {
      query: {
        name: '',
      },
      tableData: [],
      tableLoading: false,
      groupList: [],
    }
  },
  filters: {
    toNumber(num) {
      // 小数
      // return num.toLocaleString('en-US')
      // 整数
      return String(num).replace(/(\d)(?=(\d{3})+$)/g, '$1,')
    },
  },
  computed: {
    searchList() {
      return [
        {
          type: 'input',
          value: 'name',
          text: this.$t('model.headers.name'),
        },
      ]
    },
    healthStatus() {
      return this.$store.state.enums.enums.HealthStatus
    },
    topHeaderHeight() {
      return this.$store.getters['global/getTopHeaderHeight']
    },
  },

  mounted() {
    this.$_getTableData()
    // this.loadGroupData()
  },
  methods: {
    goAsset(record) {
      this.$router.push({
        path: '/asset-instance',
        query: {
          model: record.name,
          groupType: '2',
          groupNameList: JSON.stringify(
            this.groupList
              .filter(v => v.assetGroupType === '0')
              .map(v => v.value),
          ),
          isQuery: 1,
        },
      })
    },
    // 加载资产组信息
    // async loadGroupData() {
    //   const data = await this.$store.dispatch('global/searchGroups')
    //   this.groupList = data
    // },
    onClear() {
      this.query.name = ''
      this.$_search()
    },
    // con
    $_search() {
      this.query.pageNum = 1
      this.$_getTableData()
    },
    async $_getTableData() {
      try {
        this.tableLoading = true
        this.tableData = []
        const res = await getModelList(this.query)
        this.tableData = res.data
        this.tableDataTotal = res.data.total
      } catch (e) {
        console.error(`获取车辆资产组管理：${e}`)
      }
      this.tableLoading = false
    },
  },
}
</script>
<style scoped lang="scss">
.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(326px, 1fr));
  // grid-gap: 0;
  // align-content: stretch;
  // align-items: stretch;
  width: auto;
}

@media (min-width: 1600px) {
  .cards-container {
    grid-template-columns: repeat(auto-fill, minmax(390px, 1fr));
  }
}
.v-card:hover {
  box-shadow: 0 -3px 0 0 $primary inset, 3px 3px 10px 0px rgba(0, 0, 0, 10%) !important;
}

.primary--hover:hover {
  color: $primary !important;
}
</style>
