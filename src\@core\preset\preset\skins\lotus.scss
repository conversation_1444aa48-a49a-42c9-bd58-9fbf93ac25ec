.lotus {
  $nav-menu-bg-color: #000;
  $text: #fff200;
  $border: 1px solid $nav-menu-bg-color !important;

  // 统一字体修改
  * {
    font-family: HarmonyOS Sans SC, -apple-system, BlinkMacSystemFont, Segoe UI,
      Roboto, Oxygen, Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue,
      sans-serif !important;
  }

  .el-picker-panel__footer .el-button.is-plain {
    background: $text !important;
    color: $nav-menu-bg-color !important;
    box-shadow: none !important;
  }

  .v-dialog .primary {
    background: $nav-menu-bg-color !important;
  }
  .bg-btn {
    color: $nav-menu-bg-color !important;
    caret-color: $nav-menu-bg-color !important;
  }

  // 二次确认框按钮
  .swal2-popup .sweet-btn-primary {
    background: $text !important;
    color: $nav-menu-bg-color !important;
    box-shadow: none !important;
  }
  .swal2-popup .swal2-cancel.swal2-styled {
    background-color: transparent !important;
    border: $border;
    color: $nav-menu-bg-color !important;
  }

  .swal2-actions:not(.swal2-loading) .swal2-styled:hover,
  .swal2-styled.swal2-cancel:focus,
  .swal2-styled.swal2-confirm:focus,
  .swal2-popup [class*='sweet-btn-'],
  .swal2-popup [class*='sweet-btn-']:hover {
    box-shadow: none !important;
    transform: none !important;
  }

  .swal2-popup.swal2-modal.swal2-icon-warning.swal2-show {
    .swal2-actions {
      .swal2-cancel.swal2-styled {
        background-color: transparent !important;
        border: $border;
        color: $nav-menu-bg-color !important;
      }
      .swal2-confirm.sweet-btn-primary.swal2-styled {
        background-color: $text !important;
        color: $nav-menu-bg-color;
      }
    }
  }

  // 选择框选中效果
  .v-list-item--active .v-list-item__title {
    color: #726d06 !important;
  }

  .v-application a {
    color: $nav-menu-bg-color;
    &:hover {
      color: #ede20b;
    }
  }

  .v-application .list-hover-1:hover {
    color: $nav-menu-bg-color !important;
    background-color: rgba(255, 242, 0, 0.1) !important;
  }

  .v-btn:not(.v-btn--outlined) {
    &.primary,
    &.secondary,
    &.accent,
    &.success,
    &.error,
    &.warning,
    &.info {
      color: $text;
    }
  }

  .v-btn:not(.v-btn--outlined) {
    &.primary {
      color: $nav-menu-bg-color !important;
      background: $text !important;
    }
  }

  // 时间选择范围组件
  .v-date-range__menu-content .v-btn {
    &.primary {
      color: $text !important;
      background: $nav-menu-bg-color !important;
    }
    &.accent {
      color: $text !important;
      background: $nav-menu-bg-color !important;
    }
    &.accent--text {
      border-color: $nav-menu-bg-color !important;
      color: #000 !important;
    }
  }
  .v-date-range__pickers {
    .v-card__text {
      .v-date-range__content {
        .d-flex.justify-space-between {
          .d-flex.align-center.justify-end.flex-grow-1 {
            button:nth-child(1) {
              background-color: transparent !important;
              border: $border;
            }
          }
        }
      }
    }
  }

  .v-picker__title.primary {
    background-color: $nav-menu-bg-color !important;
    color: $text !important;
  }
  .v-date-picker-table.v-date-picker-table--date.theme--light {
    .v-btn.v-btn--active.v-btn--rounded.theme--light.accent {
      background-color: $nav-menu-bg-color !important;
      color: $text !important;
    }
  }

  @mixin semi-dark--bg($component, $white-opacity: 0.76, $style: 'all') {
    .#{$component} {
      @if ($style == 'all' or $style == 'background') {
        background: $nav-menu-bg-color;
      }
      @if ($style == 'all' or $style == 'color') {
        // color: rgba(#fff, $white-opacity) !important;
        color: map-get($shades, 'white') !important;
      }
    }
  }

  .v-application.skin-variant--semi-dark {
    .primary--text {
      color: $nav-menu-bg-color !important;
    }
    .theme--light.app-system-bar {
      background: $nav-menu-bg-color !important;
    }
    .v-navigation-drawer__border {
      box-shadow: -1px 0px 0px 0px #1d233f inset !important;
      display: none;
    }
    @include semi-dark--bg(app-navigation-menu);

    //   Style App Title
    @include semi-dark--bg(
      'app-navigation-menu .app-title.text--primary',
      $white-opacity: 1,
      $style: 'color'
    );

    //   Style all lists
    @include semi-dark--bg('app-navigation-menu .v-list-item', $style: 'color');

    //   Style all icons inside nav menu
    @include semi-dark--bg('app-navigation-menu  .v-icon', $style: 'color');

    //   Style all list titles
    @include semi-dark--bg(
      'app-navigation-menu .v-list-item .v-list-item__title',
      $style: 'color'
    );

    // Style active list item item
    @include semi-dark--bg(
      'app-navigation-menu .v-list-item .v-list-item__title.white--text',
      $white-opacity: 0.87,
      $style: 'color'
    );

    .app-navigation-menu {
      .v-list-item .v-list-item__title,
      .v-icon {
        color: inherit !important;
        caret-color: inherit !important;
      }
      .v-list-item--active,
      .primary--text {
        color: $primary !important;
        caret-color: $primary !important;
      }
      .bg-btn {
        color: $primary !important;
        caret-color: $primary !important;
        background: color-mix(
          in srgb,
          var(--v-primary-base) 10%,
          transparent
        ) !important;
        // background: $blue-white-color !important;
      }
      .v-list-item:hover {
        background: color-mix(in srgb, $text 10%, transparent) !important;
      }
    }

    //   Style nav menu section title
    @include semi-dark--bg(
      'app-navigation-menu .v-subheader .title-wrapper span'
    );

    .vertical-nav-menu-container {
      .shadow-bottom {
        background: linear-gradient(
          $nav-menu-bg-color 40%,
          rgba($nav-menu-bg-color, 0.1) 95%,
          rgba($nav-menu-bg-color, 0.05)
        );
      }
    }
    // 按钮默认样式
    .d-flex > .primary,
    .edit-center-box .primary {
      background-color: $text !important;
      color: $nav-menu-bg-color !important;
    }

    .theme--light.primary-bg {
      background-color: $text !important;
      color: $nav-menu-bg-color !important;
    }
    // 表格筛选条件栏目
    .bar-box > .bar-box-right > div > .v-btn.v-btn--outlined,
    .bg-btn.v-btn {
      background-color: transparent !important;
      border: $border;
      color: $nav-menu-bg-color !important;
    }
    .bg-btn.v-btn.v-btn--disabled {
      background-color: rgba(94, 86, 105, 0.12) !important;
      color: rgba(94, 86, 105, 0.26) !important;
      border-color: rgba(94, 86, 105, 0.12) !important;
    }
    .v-btn.reply-btn {
      border: $border;
      color: $nav-menu-bg-color !important;
    }
    .organization-box-right {
      .btn-outline-secondary {
        border: $border;
        .action-btn {
          color: $nav-menu-bg-color !important;
        }
      }
    }
    /* 设置选中状态下的checkbox样式 */
    //.v-input--selection-controls__input>.v-icon.primary--text{
    //  color: $text !important;
    //}
    //.v-input--selection-controls__input>.v-icon.primary--text>svg>path {
    //  background-color: $nav-menu-bg-color !important;
    //}
    //.v-input--selection-controls__input>.v-icon.primary--text>input {
    //  color: $text !important;
    //}

    /* 设置v-menu统一样式 */
    .user-profile-menu-content {
      .v-list {
        background-color: $nav-menu-bg-color;
        .theme--light,
        .text--disabled,
        .text--primary {
          color: #fff !important;
        }
        .theme--light:hover {
          color: $text !important;
        }
      }
    }

    /* drawer统一样式 */
    .vsoc-drawer {
      div:nth-child(2) {
        .vsoc-drawer__footer.d-flex.justify-end.border-top {
          button:nth-child(1) {
            background-color: transparent !important;
            border: $border;
            color: $nav-menu-bg-color !important;
          }
        }
      }
    }

    /* dialog统一样式 */
    .v-dialog {
      div {
        .vsoc-dialog__footer {
          button:nth-child(1) {
            background-color: transparent !important;
            border: $border;
            color: $nav-menu-bg-color !important;
          }
        }
      }
    }

    /* avatar统一样式 */
    .v-avatar .primary {
      color: $nav-menu-bg-color !important;
    }

    /* .v-application.theme--light .v-btn--is-elevated */
    .v-btn--is-elevated,
    .v-application.theme--light .v-btn--is-elevated:hover {
      box-shadow: none !important;
    }

    /* v-chip统一样式 */
    .v-chip {
      background-color: $text !important;
      color: $nav-menu-bg-color !important;
    }
    .v-chip--disabled {
      opacity: 1;
      color: $nav-menu-bg-color !important;
    }
  }

  /* menu样式 */
  .v-menu__content.menu-list .list-hover[data-v-26b6f5a8]:hover,
  .v-menu__content.menu-list .list-hover[data-v-7d28dca7]:hover {
    color: $nav-menu-bg-color !important;
    background: rgba(255, 242, 0, 0.1) !important;
  }
  /* 关于Element组件的样式调整 */
  .el-date-table td.today span {
    border-color: $nav-menu-bg-color !important;
    color: #000 !important;
  }

  .el-date-table td.current:not(.disabled) span {
    background-color: $nav-menu-bg-color !important;
    color: $text !important;
  }
  @include theme(v-btn-toggle) using ($material) {
    // .v-btn {
    //   border-color: var(--v-primary-base) !important;
    // }
    > .v-btn.v-btn--active {
      background: $text !important;
      border-left: 1px solid $text !important;
    }
    &:not(.v-btn-toggle--group) .v-btn {
      // border-color: #e6eaf2 !important;
    }
    &:not(.v-btn-toggle--group) .v-btn.v-btn--active {
      border-color: var(--v-primary-base) !important;
    }
  }
}
