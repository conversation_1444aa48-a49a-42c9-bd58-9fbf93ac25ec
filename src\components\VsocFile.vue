<template>
  <div>
    <div class="mt-6 font-weight-semibold-light mb-4 d-flex align-center">
      <span class="mr-6 text--secondary"> {{ title }} </span>
      <el-upload
        ref="upload"
        :headers="headers"
        :data="params"
        :action="vsocPath + action"
        :on-change="handleChange"
        :on-success="handleSuccess"
        :on-error="handleError"
        :on-exceed="handleExceed"
        :before-upload="handleBefore"
        :file-list="attachments"
        :show-file-list="false"
        :accept="accept"
        :limit="limit"
        :disabled="disabled"
      >
        <!-- :auto-upload="false" -->
        <v-btn
          color="primary--text bg-btn"
          elevation="0"
          :loading="isUploading"
          min-width="36"
          :disabled="disabled"
        >
          <v-icon dense style="transform: rotate(45deg)" class="text-content">
            mdi-paperclip
          </v-icon>
          <!-- {{ $t('action.upload') }} -->
        </v-btn>
      </el-upload>
    </div>
    <div class="d-flex align-center" v-if="!disabled">
      <v-icon dense class="text-xl" color="#A1A6B1">mdi-paperclip</v-icon>
      <span class="ml-2 text--secondary">
        {{ hint }}
      </span>
    </div>
    <div v-if="attachments.length > 0">
      <v-chip
        class="mt-3 text-content mr-3 primary-hover-chip"
        label
        v-for="(item, index) in attachments"
        :key="index"
      >
        <vsoc-icon
          type="fill"
          icon="icon-fujianicon"
          style="color: #06309b"
        ></vsoc-icon>
        <span class="ml-2 mr-3 text-content">{{ item.name }}</span>
        <span class="action-btn mr-3">{{ item.size | formatFileSize }}</span>
        <v-btn
          v-if="!disabled && actions.includes('edit')"
          icon
          @click.stop="onDownload(item)"
          :loading="item.loading"
        >
          <vsoc-icon
            class="cursor-pointer"
            type="fill"
            icon="icon-xiazai"
          ></vsoc-icon>
        </v-btn>
        <v-btn
          icon
          v-if="!disabled && actions.includes('del')"
          @click.stop="attachments.splice(index, 1)"
        >
          <vsoc-icon
            class="cursor-pointer"
            type="fill"
            icon="icon-shanchu"
          ></vsoc-icon>
        </v-btn>
      </v-chip>
    </div>
  </div>
</template>

<script>
import { formatFileSize } from '@/util/filters'
import { request, vsocPath } from '@/util/request'
export default {
  name: 'VsocFile',
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
    title: String,
    hint: String,
    accept: {
      type: String,
      default: '*',
    },
    action: {
      type: String,
      default: '/file/upload',
    },
    downloadApi: {
      type: String,
      default: '/file/download',
    },
    maxSize: {
      type: Number,
      defalut: 20 * 1024 * 1024, // 20M
    },
    limit: {
      type: [Number],
      default: 10000,
    },
    actions: {
      type: Array,
      default: () => [],
    },
    fileList: Array,
  },
  data() {
    return {
      vsocPath,
      attachments: [],
      isUploading: false,
      params: {
        moduleName: this.$route.meta.code,
      },
    }
  },
  computed: {
    headers() {
      return this.$store.getters['global/getFileHeaders']
    },
  },
  watch: {
    fileList(newList) {
      this.attachments = newList.map(attach => {
        return {
          name: attach.fileName,
          size: Number(attach.fileSize),
          path: attach.filePath,
          type: attach.fileType,
          id: attach.id,
          loading: false,
        }
      })
    },
  },
  methods: {
    async onDownload(record) {
      try {
        record.loading = true
        const blob = await request({
          method: 'get',
          url: vsocPath + this.downloadApi,
          params: {
            fileId: record.path,
          },
          responseType: 'blob',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        })
        // const blob = new Blob([file], { type: record.type })
        //转换数据类型
        const a = document.createElement('a') // 转换完成，创建一个a标签用于下载

        a.download = record.name
        a.href = URL.createObjectURL(blob)
        document.body.appendChild(a)
        a.click()
        a.remove()
      } catch (err) {
        this.$notify.info('error', err)
      } finally {
        record.loading = false
      }
    },
    toFileSize(size) {
      let str = formatFileSize(size)
      if (!str) {
        return
      }
      let last = str.length - 1
      return Number(str.slice(0, last))
    },
    handleBefore(file) {
      // 文件大小和格式校验
      // let isAccept = this.accept.includes(file.type)
      // let isSize = file.size <= this.maxSize
      // console.log('isAccept', file, isAccept, isSize)
      // if (!isAccept || !isSize) {
      //   this.$notify.info('error', this.hint)
      // } else {
      //   this.isUploading = true
      // }
      // return isAccept && isSize
      this.isUploading = true
    },
    handleExceed(files, fileList) {
      this.$notify.info('error', `最多可上传${this.limit}个`)
    },
    handleError(err, file, fileList) {
      this.$notify.info('error', err)
    },
    handleSuccess(res, file, fileList) {
      const { code, msg, data } = res
      if (code === 200) {
        const curFile = {
          ...file,
          id: data,
          loading: false,
        }
        this.attachments.push(curFile)
        this.$emit(
          'input',
          this.attachments.map(v => v.id),
        )
        this.$notify.info('success', this.$t('global.hint.fileUploadSuccess'))
      } else {
        this.$notify.info('error', msg)
      }
      this.isUploading = false
    },
    handleChange(file, fileList) {},
  },
}
</script>
<style scoped lang="scss">
.v-chip.v-size--default {
  height: 3.1667rem;
}
</style>
