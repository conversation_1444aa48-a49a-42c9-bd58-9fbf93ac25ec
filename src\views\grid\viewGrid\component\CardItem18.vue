<template>
  <!-- 定制化图形2 -->
  <div class="box">
    <div class="box-header">{{ title }}</div>
    <!-- style="padding: 0 6%" -->
    <div
      :style="{ marginTop: isFull ? '0' : '4%' }"
      class="box-chart d-flex align-center w-100"
    >
      <div
        v-for="(item, index) in list"
        :class="
          index === list.length - 1
            ? 'card-item-18 card-item-18-1'
            : 'card-item-18'
        "
        :key="index"
      >
        <div class="font-weight-medium fs-14" style="margin-bottom: 2%">
          {{ item.name }}
        </div>
        <div class="d-flex">
          <template v-if="Array.isArray(item.value)">
            <div
              v-for="(x, i) in item.value"
              :key="i"
              class="d-flex card-item-title-text align-center"
            >
              <div
                :style="{
                  color: x.color,
                }"
              >
                *
              </div>
              <div class="ml-1">{{ x.value }}%</div>
              <div
                v-if="item.value.length > 1 && i === 0"
                class="mx-3 font-weight-medium fs-14"
              >
                VS
              </div>
            </div>
          </template>
          <template v-else>
            <div class="d-flex card-item-title-text align-center">
              {{ item.value }}
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { numberToFormat } from '@/util/filters'
import countTo from 'vue-count-to'

export default {
  name: 'CardItem18',
  props: {
    title: {
      type: String,
      default: '',
    },
    list: {
      type: Array,
      default: () => {
        return []
      },
    },
    isFull: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    countTo,
  },
  computed: {},
  watch: {
    list: {
      handler(val) {
        if (val.length === 0) return
        // val.forEach((item, index) => {
        //   this.$nextTick(() => {
        //     this.$refs['count' + index][0] &&
        //       this.$refs['count' + index][0].start()
        //   })
        // })
      },
      immediate: true,
    },
  },
  data() {
    return {
      numberToFormat,
    }
  },
}
</script>
