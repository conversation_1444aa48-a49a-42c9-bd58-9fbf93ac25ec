<template>
  <v-card class="chart-card-total-visitors d-flex flex-column h-full">
    <v-card-title class="align-start">
      {{ $t('analytics.alertLevelRadio') }}
    </v-card-title>
    <v-card-text class="flex-1">
      <vsoc-chart
        ref="echart"
        class="echart"
        :echart-id="echartId"
        :option="chartOption"
        @highlight="onHighlight"
        @finished="onFinished"
        @select="onSelect"
        @mouseout="onMouseout"
      ></vsoc-chart>
    </v-card-text>
  </v-card>
</template>

<script>
import VsocChart from '@/components/VsocChart.vue'
import { numberToFormat } from '@/util/filters'
import { getVuetify } from '@core/utils'
import themeConfig from '@themeConfig'
import lodash from 'lodash'
import VueApexCharts from 'vue-apexcharts'
import { ref, watch } from 'vue-demi'
export default {
  components: {
    VueApexCharts,
    VsocChart,
  },
  props: {
    title: {
      type: String,
      default: () => '标题',
    },
    colorList: {
      type: Array,
      default: () => [],
    },
    list: {
      type: Array,
      default: () => [],
    },
    echartId: {
      type: String,
      default: () => 'echart',
    },
    padding: {
      type: [Number, Array],
      default: () => [4, 0],
    },
    formatter: {
      type: Function,
      default(params) {
        return `\n{value|${params.data.format}}\n{name|${params.name}}`
      },
    },
  },
  setup(props) {
    const $vuetify = getVuetify()

    // let selected = ref({
    //   name: '严重',
    //   value: 85,
    //   color: '#FF4C51',
    // })
    let selected = {
      name: '严重',
      value: 100,
      color: '#FF4C51',
    }
    let myChart = ''
    let dataIndex = 0
    let backgroundColor = ''
    let textColor = ''
    let rich = {
      name: {
        fontSize: 14,
        fontWeight: 400,
        padding: props.padding,
        // color: '#aaa5c0',
        fontFamily:
          'Helvetica, Helvetica Neue, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, "Inter", SansSerif, sans-serif',
        // backgroundColor: '#312d4b',
      },
      value: {
        lineHeight: 22,
        fontSize: 22,
        fontWeight: 600,
        padding: props.padding,
        verticalAlign: 'bottom',
        color: '#cfcce5',
        fontFamily:
          'Helvetica, Helvetica Neue, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, "Inter", SansSerif, sans-serif',
        // backgroundColor: '#312d4b',
      },
    }
    let emphasis = {
      show: true,
      scale: false,
      // scaleSize: 1.5,

      label: {
        fontSize: 36,
        lineHeight: 50.4,
        // color: '#fafafa',
        // formatter: params => props.formatter(params),
        formatter: params => {
          const sum = lodash.sum(props.list.map(v => v.value))
          return numberToFormat(sum)
        },
        // // 切换非默认选项配置数据展示
        // if (params.dataIndex != 0) {
        //   return `\n{value|${params.value}k}` + `\n{name|${params.name}}`
        // }
      },
    }
    let label = {
      show: true,
      position: 'center',

      lineHeight: 16,

      formatter: params => '',

      // if (params.dataIndex === 0) {
      //   return `\n{value|${params.value}k}\n{name|${params.name}}`
      // }
    }
    let itemStyle = {
      // borderWidth: 0,
      // borderColor: 'transparent',
      borderWidth: 10,
      borderRadius: 16,
      borderColor: '#171B34',
    }
    let chartOption = ref({
      // color: ['#9155fd', '#d4d5d7', '#ffb400', '#ff4c51'],
      color: props.colorList,

      tooltip: {
        trigger: 'item',
        // formatter: '{b}: {d}%',
        formatter: params => {
          return `${params.name}:\t${params.data.format}`
        },
        backgroundColor: '#fff',
        padding: [6, 10],
        textStyle: {
          color: '#fff',
          fontFamily:
            'Helvetica, Helvetica Neue, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, "Inter", SansSerif, sans-serif',
        },
      },

      legend: {
        bottom: '0%',
        left: 'center',
        padding: [10, 0, 0, 0],

        show: true,
        itemStyle: {
          fontSize: 14,
        },

        // width: 10,

        icon: 'path://M6.18388 9.16513H3.50055C3.15022 9.16513 2.90855 8.81446 3.03322 8.48729L5.69955 1.48863C5.73562 1.39394 5.79961 1.31245 5.88304 1.25494C5.96646 1.19744 6.06539 1.16664 6.16672 1.16663H10.6661C11.0211 1.16663 11.2631 1.52629 11.1291 1.85513L9.57655 5.66579H12.4992C12.9291 5.66579 13.1584 6.17246 12.8749 6.49546L5.70922 14.6605C5.36088 15.0575 4.71505 14.7135 4.85022 14.203L6.18388 9.16513Z',
        itemHeight: 16, // 修改icon图形大小
        itemGap: 14,
        // itemWidth: 18,
        align: 'left',
        textStyle: {
          fontSize: 14,
          lineHeight: 18,

          padding: [0, 0, 0, 0], // 修改文字和图标距离
        },

        formatter: name => {
          let record = props.list.find(v => v.name === name)

          return `{base|${name}}\t\t\t{num|${numberToFormat(
            record?.value,
          )}}\t\t\t{secondary|${record?.format}}`
        },
        selectedMode: false,
      },

      series: [
        {
          name: '告警严重级别占比',
          type: 'pie',
          radius: ['62%', '78%'],
          center: ['50%', '38%'],
          avoidLabelOverlap: true,
          percentPrecision: 2,
          minAngle: 20,
          stillShowZeroSum: true,
          bottom: 40,
          top: 0,
          label: {},
          labelLine: {
            show: false,
          },

          // 数据倒叙排序
          // data: [
          //   { value: 85, name: 'series-1' },
          //   { value: 20, name: 'series-2' },
          //   { value: 30, name: 'series-3' },
          //   { value: 50, name: 'series-4' },
          // ],
          data: props.list,
        },
      ],
    })

    const onHighlight = obj => {
      selected = Object.assign(obj.data, { color: obj.color })
      if (dataIndex !== obj.dataIndex) {
        // 取消前一条高亮
        myChart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: dataIndex,
        })
        dataIndex = obj.dataIndex
      }
      chartOption.value.tooltip.backgroundColor = obj.color
      chartOption.value.series[0].emphasis.scale = true
      myChart.setOption(chartOption.value)
    }

    const onMouseout = event => {
      // 取消动画
      chartOption.value.series[0].emphasis.scale = false
      myChart.setOption(chartOption.value)
    }

    const onFinished = chart => {
      myChart = chart
      // 默认第一条数据高亮
      myChart.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: dataIndex,
      })
    }

    const onSelect = (evnet, chart) => {
      // console.log('event', events)
    }

    const resetColor = () => {
      rich.name.color = textColor
      rich.name.backgroundColor = ''
      rich.value.color = textColor
      rich.value.backgroundColor = ''

      itemStyle.borderColor = backgroundColor
      Object.assign(label, { rich: rich })
      Object.assign(emphasis, { rich: rich })
      Object.assign(
        chartOption.value.series[0],
        { label: label },
        { itemStyle: itemStyle },
        { emphasis: emphasis },
      )
    }
    // onMounted(() => {
    //   myChart.on('select', event => {
    //     console.log('event', event)
    //     if (dataIndex !== event.dataIndex) {
    //       // 取消前一条高亮
    //       myChart.dispatchAction({
    //         type: 'downplay',
    //         seriesIndex: 0,
    //         dataIndex: dataIndex,
    //       })
    //       dataIndex = event.dataIndex
    //     }
    //   })
    // })
    watch(
      () => $vuetify.theme.dark,
      value => {
        if (value) {
          backgroundColor = themeConfig.themes.dark.backgroundColor
          textColor = themeConfig.themes.dark.color
        } else {
          backgroundColor = themeConfig.themes.light.backgroundColor
          textColor = themeConfig.themes.light.color
        }
        resetColor()
      },
      { deep: true, immediate: true },
    )
    watch(
      () => props.list,
      newArr => {
        const sortArr = lodash.orderBy(newArr, ['value'], ['desc'])
        chartOption.value.series[0].data = sortArr
        selected = sortArr[0]
        chartOption.value.color = sortArr.map(v => v.color)
        const valueArr = sortArr.map(item => item.value)
        if (myChart) {
          // 取消前一条高亮
          myChart.dispatchAction({
            type: 'downplay',
            seriesIndex: 0,
            dataIndex: dataIndex,
          })
          dataIndex = 0
        }
      },
      { immediate: true },
    )
    return {
      chartOption,
      onHighlight,
      onFinished,
      onSelect,
      onMouseout,
    }
  },
}
</script>
