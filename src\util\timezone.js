import { isEmpty } from '@/@core/utils'
import { getLocalStorage } from '@/util/localStorage'
import { addHours, format, subDays, subHours } from 'date-fns'
import moment from 'moment-timezone'
// 获取时区列表
export function getTimeZoneList() {
  let names = moment.tz.names()
  return names.map(name => {
    return {
      text: name,
      value: name,
    }
  })
}

// 获取用户时区
export function getUserTimeZone() {
  let userInfoJson = getLocalStorage('userInfo')
  let timezone = undefined
  if (!isEmpty(userInfoJson)) {
    // 'America/Los_Angeles' 美国洛杉矶 -8区
    // Europe/London 英国伦敦 0时区
    timezone = JSON.parse(userInfoJson)?.timezoneId
  }
  let result = timezone || Intl.DateTimeFormat().resolvedOptions().timeZone
  moment.tz.setDefault(result)
  return result
}

let systemOffset = getSystemOffset() * -1
function getSystemOffset() {
  return new Date().getTimezoneOffset()
}

export function setDate() {
  Date = class extends Date {
    constructor() {
      if (arguments.length > 0) {
        super(...arguments)
      } else {
        const systemTimeZone = moment.tz.guess()

        const userTimeZone = getUserTimeZone()
        if (systemTimeZone === userTimeZone) {
          super(...arguments)
        } else {
          // 分钟
          const userOffset = moment().utcOffset()
          const sub = systemOffset - userOffset
          // 偏移的毫秒数
          let offset = sub * 60 * 1000

          super(Date.now() - offset)
        }
      }
    }
  }
}

/**
 * 计算偏移的时区
 * @returns 偏移的毫秒数
 */
export function getLocalOffset() {
  let localDate = new Date()
  let localTime = localDate.getTime()
  let localOffset = localDate.getTimezoneOffset() * 60 * 1000
  return localOffset
}

/**
 * x轴坐标日期转换
 * @param {Array} arr
 * @param {String} _format
 * @returns x坐标
 */
export function toXDataYMD(arr, _format) {
  if (isEmpty(arr)) {
    return []
  }
  return arr.map(item => {
    let curTime = new Date(item)
    return format(curTime, _format)
  })
}

/**
 * 计算最近value天、最近value小时
 * @param {Number} value 相差值，0表示【所有时段】
 * @param {Days/Hours} unit 最近value天/小时
 * @param {String} _format 日期格式
 * @returns result {开始时间,结束时间}
 */
export function toRecentSubDate(
  value,
  unit = 'Days',
  _format = 'yyyy-MM-dd HH:mm:ss',
  type,
) {
  let result = {
    startDate: null,
    endDate: null,
  }
  if (value === 0) {
    return result
  }
  // 计算相差天数
  if (unit === 'Days') {
    result = {
      startDate: format(subDays(new Date(), value - 1), _format),
      endDate: format(new Date(), _format),
    }
  }
  if (unit === 'Hours') {
    if (type) {
      // result = {
      //   startDate: '2024-09-09 11:00:00',
      //   endDate: '2024-09-10 10:59:59',
      // }
      result = {
        startDate: format(subHours(new Date(), 22), 'yyyy-MM-dd HH:00:00'),
        endDate: format(addHours(new Date(), 1), 'yyyy-MM-dd HH:59:59'),
      }
    } else {
      result = {
        startDate: format(subHours(new Date(), value), _format),
        endDate: format(new Date(), _format),
      }
    }
  }
  return result
}
