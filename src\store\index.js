import getters from '@/store/getters'
import appConfigStoreModule from '@core/@app-config/appConfigStoreModule'
import Vue from 'vue'
import Vuex from 'vuex'
import app from './app'
import enums from './modules/enum'
import global from './modules/global'
import permission from './modules/permission'
import user from './modules/user'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {},
  mutations: {},
  actions: {},
  modules: {
    enums, // 枚举值
    global, // 全局常量
    appConfig: appConfigStoreModule, // 模块里面的app配置
    app, // 项目app信息
    user, // 用户信息(token，账户信息)
    permission, // 权限信息，包含路由，菜单，按钮，用户等信息
  },
  getters,
})
