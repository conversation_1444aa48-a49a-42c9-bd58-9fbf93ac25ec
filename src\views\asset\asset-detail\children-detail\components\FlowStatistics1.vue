<template>
  <v-card class="h-full">
    <v-card-title class="float-left">{{
      $t('analytics.alertTrend')
    }}</v-card-title>
    <v-card-text class="h-full">
      <vsoc-chart
        echart-id="flow-statistics"
        :option="gradientChart"
        :is-y-dashed="true"
      ></vsoc-chart>
    </v-card-text>
  </v-card>
</template>

<script>
import { hexToRgb } from '@/@core/utils'
import Vsoc<PERSON>hart from '@/components/VsocChart'
import { numberToFormat } from '@/util/filters'
import { startOfDay, subHours } from 'date-fns'
import {
  grid,
  legend,
  linearGradient,
  tooltip,
} from '../../../../../assets/echarts-theme/constant'
let xData = []
for (let i = 24; i > 0; i--) {
  i = i - 1
  let time = subHours(startOfDay(new Date()), i)
  xData.push(time)
}
const yData = [
  {
    name: '告警总量',
    data: [100, 120, 200, 210, 130, 160, 280, 240, 260, 200, 270, 280, 120],
  },
  {
    name: '严重告警总量',
    data: [60, 80, 100, 110, 80, 100, 90, 180, 160, 140, 200, 220, 275, 100],
  },
]
export default {
  name: 'FlowStatistics',
  props: {
    collectionData: {
      type: Object,
      default() {
        return {
          xData: xData,
          yData: yData,
        }
      },
    },
  },
  components: {
    VsocChart,
  },

  computed: {
    gradientChart() {
      const colorList = ['#107FFF', '#55D1FD']
      // let sum = this.collectionData.yData
      //   .map(v => v.data)
      //   .flat()
      //   .reduce((x, y) => x + y)
      // this.collectionData.total = numberToFormat(sum)
      return {
        color: colorList,
        // title: {
        //   show: true,
        //   text: this.$t('analytics.alertTrend'),
        //   textStyle: {
        //     lineHeight: 24,
        //   },
        // },
        series: this.collectionData.yData.map((item, index) => {
          const rgb = hexToRgb(colorList[index])
          const end = `rgba(${rgb.r},${rgb.g},${rgb.b},0)`
          return {
            name: item.name,
            data: item.data,
            type: 'line',
            smooth: true,
            showSymbol: false,
            // symbol: 'none',
            symbolSize: 10,
            emphasis: { focus: 'series' },
            animationDuration: 2500,
            animationEasing: 'cubicInOut',
            lineStyle: {
              width: 4,
            },
            areaStyle: {
              width: 4,
              opacity: 0.25,
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0.389, color: colorList[index] },
                  { offset: 1, color: end },
                ],
                global: false,
              },
            },
          }
        }),
        tooltip,
        legend: {
          ...legend,
          show: false,
          formatter: name => {
            let record = this.collectionData.yData.find(v => v.name === name)
            let total = record.data.reduce((x, y) => x + y)
            return `${name}(${numberToFormat(total)})`
          },
        },
        grid,
        xAxis: linearGradient.xAxisFn(this.collectionData.xData),
        yAxis: [
          {
            type: 'value',
          },
        ],
      }
    },
  },
}
</script>
<style scoped lang="scss"></style>
