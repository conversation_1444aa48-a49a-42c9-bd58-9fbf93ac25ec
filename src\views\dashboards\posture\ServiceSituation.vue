<template>
  <v-card class="h-full">
    <v-card-text
      class="text-base d-flex flex-column align-center bg-img justify-center"
      style="height: 48%"
    >
      <div class="text-nxl text--primary mt-5 text-xl-h4 font-weight-normal">
        {{ $t('posture.serviceSituation') }}
      </div>
      <div class="mt-xl-2 text-xl-h6 font-weight-normal">Current Posture</div>
      <v-img
        :src="
          require(`@/assets/images/pages/healthStatusImg/${
            currentPosture | 0
          }.png`)
        "
        contain
        max-width="10vw"
        max-height="24vh"
        class="my-3"
      ></v-img>
      <div class="text-ml reverse--text d-flex mt-xl-5">
        <vsoc-icon
          icon="mdi-refresh-circle"
          class="mr-1"
          color="reverse"
        ></vsoc-icon>
        {{ $t('global.updateDiff', [getDateDiff(updateDate)]) }}
      </div>
      <div class="text--primary mt-1 text-ml">
        {{ updateDate | toDate }}
      </div>
    </v-card-text>
    <!-- <div class="my-xl-16"></div> -->
    <div style="height: 52%" class="d-flex pt-xl-16 mt-xl-2 flex-column">
      <v-card-title class="pb-0 pb-xl-2 pt-1 text-xxl font-weight-semibold">
        <!-- {{ $t('posture.newEvents') }} -->
        {{ $t('posture.assetPostureDetails') }}
      </v-card-title>
      <div class="text-center d-flex justify-center mt-5">
        <v-icon
          :size="$vuetify.breakpoint.xl ? 24 : 18"
          color="primary"
          class="iconfont icon-Vehicles"
        ></v-icon>
        <span class="text-xxl text--primary ml-2">Automakers</span>
      </div>
      <v-card-text class="flex-1">
        <div
          style="height: 25%"
          v-for="(earning, index) in totalEarning"
          :key="earning.healthStatus"
          class="d-flex align-center text-ml"
        >
          <div
            class="d-flex w-100 align-center cursor-pointer"
            @click.stop="goAsset(earning)"
          >
            <div style="min-width: 5.5rem" class="text-right mr-3 text-no-wrap">
              {{ earning.healthStatusName }}
            </div>
            <v-progress-linear
              :value="earning.progress"
              :color="earning.color"
              background-color="bg-body"
              rounded
              class="mx-1"
              :height="8 | getRoundSize"
            ></v-progress-linear>
            <div
              style="min-width: 8rem"
              class="text-right ml-1 ml-xl-2 mr-2 text-no-wrap"
            >
              <span class="text--primary text-no-wrap">{{
                earning.count | numberToFormat
              }}</span>
              <span class="ml-2 ml-xl-3 text-no-wrap">{{
                earning.alarmPercentage
              }}</span>
            </div>
          </div>
        </div>
      </v-card-text>
    </div>
  </v-card>
</template>

<script>
import { hexToRgb } from '@/@core/utils'
import { getDateDiff, numberToFormat } from '@/util/filters'

export default {
  props: {
    eventList: [Array, Object],
    currentPosture: [String, Number],
    updateDate: [Date, String, Number],
    totalEarning: [Array, Object],
  },
  filters: {
    countConfig(count) {
      return {
        number: [count],
        textAlign: 'right',
        formatter: numberToFormat,
        style: {
          fontStyle: 36,
          fill: '#ffffff',
        },
      }
    },
  },

  data() {
    return {
      currentDate: new Date(),
      hexToRgb,
    }
  },
  methods: {
    getDateDiff,
    goAlert(item) {
      this.$router.push({
        path: '/alerts',
        query: {
          isQuery: 1,
          alarmLevelList: JSON.stringify([item.alarmLevel]),
        },
      })
    },
    goAsset(item) {
      // 检查是否是车企态势数据（根据 healthStatus 的值判断）
      if (['Strong', 'Good', 'Fair', 'Poor'].includes(item.healthStatus)) {
        // 跳转到车企态势页面，根据安全等级筛选
        this.$router.push({
          path: '/automaker-posture',
          // query: {
          //   securityLevel: item.healthStatus,
          //   isQuery: 1,
          // },
        })
      } else {
        // 原有的资产实例跳转逻辑
        this.$router.push({
          path: '/automaker-posture',
          // query: {
          //   isQuery: 1,
          //   healthStatusList: JSON.stringify([item.healthStatus]),
          // },
        })
      }
    },
  },
}
</script>
<style scoped lang="scss">
.v-card .v-card__title {
  padding-top: 1.3125rem;
}

.bg-img {
  position: relative;
  &::before {
    position: absolute;
    content: '';
    left: 0;
    bottom: 0;
    width: 100%;
    height: 65%;
    opacity: 0.8;
    background: url('../../../assets/images/pages/service-car.png') no-repeat
      bottom right;
    background-size: contain;
  }
}
.event-box {
  width: 100%;
  height: calc(25% - 1rem);
  // height: 5rem;
  max-height: 110px;

  // background: $blue-white-color;
  margin-top: 1rem;
  padding: 0 1.4583rem;
  border-radius: 8px;

  // &:first-child {
  //   margin-top: 0;
  // }
}
</style>
