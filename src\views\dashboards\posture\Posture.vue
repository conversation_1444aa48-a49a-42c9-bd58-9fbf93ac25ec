<template>
  <div class="grid-container">
    <div class="item1">
      <v-skeleton-loader
        :dark="$vuetify.theme.dark"
        :loading="isLoading"
        type="image"
        class="mx-auto h-full w-100"
      >
        <analytics-statistics-card
          :list="statisticsData"
        ></analytics-statistics-card>
      </v-skeleton-loader>
    </div>
    <div class="item2">
      <v-skeleton-loader
        :dark="$vuetify.theme.dark"
        :loading="isLoading"
        type="image"
        class="mx-auto h-full w-100"
      >
        <data-collection :collectionData="collectionData"></data-collection>
      </v-skeleton-loader>
    </div>
    <div class="item3">
      <v-skeleton-loader
        :dark="$vuetify.theme.dark"
        :loading="isLoading"
        type="image"
        class="mx-auto h-full w-100"
      >
        <asset-detail
          :totalEarning="totalEarning"
          :eventList="waithandleAlarmList"
        ></asset-detail>
      </v-skeleton-loader>
    </div>
    <div class="item4">
      <v-skeleton-loader
        :dark="$vuetify.theme.dark"
        :loading="isLoading"
        type="image"
        class="mx-auto h-full w-100"
      >
        <service-situation
          :currentPosture="currentPosture"
          :updateDate="updateDate"
          :eventList="waithandleAlarmList"
          :totalEarning="totalEarning"
        ></service-situation>
      </v-skeleton-loader>
    </div>
  </div>
</template>

<script>
import { mdiCalendarBlankOutline, mdiDotsVertical } from '@mdi/js'
import DataCollection from './DataCollection.vue'
// demos
import VsocChart from '@/components/VsocChart'
import { computed, onMounted, ref } from 'vue-demi'

import { getAutomakerList } from '@/api/asset/automaker'
import { getSecurityPosture } from '@/api/posture/index.js'
import { getAutomakerSecurityLevelUnified } from '@/util/algorithm'

/**
 * 智能动态计算整体车企态势算法
 * 基于多维度评估和动态权重，适用于50个左右车企的规模
 * @param {Object} postureStats - 各等级车企统计数据
 * @param {number} totalSupervised - 监管车企总数
 * @returns {number} 整体态势等级 (0-3)
 */
function calculateOverallPosture(postureStats, totalSupervised) {
  // 获取各等级数量
  const strongCount = postureStats.Strong.count
  const goodCount = postureStats.Good.count
  const fairCount = postureStats.Fair.count
  const poorCount = postureStats.Poor.count

  // 计算各等级占比
  const strongRatio = strongCount / totalSupervised
  const goodRatio = goodCount / totalSupervised
  const fairRatio = fairCount / totalSupervised
  const poorRatio = poorCount / totalSupervised

  // === 1. 极端情况优先判断 ===

  // 如果没有车企，默认优秀
  if (totalSupervised === 0) return 0

  // 如果较差车企占比超过30%，整体态势为较差
  if (poorRatio >= 0.3) return 3

  // 如果较差+一般车企占比超过60%，整体态势为一般
  if (poorRatio + fairRatio >= 0.6) return 2

  // 如果优秀车企占比超过70%，整体态势为优秀
  if (strongRatio >= 0.7) return 0

  // 如果优秀+良好车企占比超过80%，且较差车企少于5%，整体态势为良好
  if (strongRatio + goodRatio >= 0.8 && poorRatio < 0.05) return 1

  // === 2. 动态权重计算 ===

  // 基础权重：优秀=0分，良好=1分，一般=3分，较差=6分
  // 较差和一般的权重更高，体现风险敏感性
  let baseWeightedScore =
    strongRatio * 0 + goodRatio * 1 + fairRatio * 3 + poorRatio * 6

  // === 3. 规模调整因子 ===

  // 根据车企总数调整评级严格度
  let scaleFactor = 1.0
  if (totalSupervised < 20) {
    // 小规模：更严格，单个较差车企影响更大
    scaleFactor = 1.3
  } else if (totalSupervised > 80) {
    // 大规模：相对宽松，允许少量较差车企
    scaleFactor = 0.8
  }

  const adjustedScore = baseWeightedScore * scaleFactor

  // === 4. 分布均匀性调整 ===

  // 计算分布的均匀性（避免极端分化）
  const nonZeroLevels = [strongCount, goodCount, fairCount, poorCount].filter(
    count => count > 0,
  ).length

  // 如果分布过于集中（只有1-2个等级有车企），适度调整
  if (nonZeroLevels <= 2 && totalSupervised >= 10) {
    // 分布集中时，向中等评级靠拢
    if (adjustedScore < 1.0) {
      // 如果计算结果偏好，适度提高到良好
      return Math.min(1, Math.floor(adjustedScore + 0.5))
    } else if (adjustedScore > 4.0) {
      // 如果计算结果偏差，适度降低
      return Math.max(2, Math.floor(adjustedScore - 1.0))
    }
  }

  // === 5. 最终评级决策 ===

  // 根据调整后的加权分数确定整体态势
  if (adjustedScore <= 0.8) {
    return 0 // Strong - 优秀
  } else if (adjustedScore <= 2.2) {
    return 1 // Good - 良好
  } else if (adjustedScore <= 4.5) {
    return 2 // Fair - 一般
  } else {
    return 3 // Poor - 较差
  }
}

import store from '@/store'
import AnalyticsStatisticsCard from './AnalyticsStatisticsCard.vue'
import AssetDetail from './AssetDetail.vue'
import ServiceSituation from './ServiceSituation.vue'
import apexChatData from './apexChartData'
export default {
  components: {
    VsocChart,
    DataCollection,
    AssetDetail,
    AnalyticsStatisticsCard,
    ServiceSituation,
  },
  setup() {
    const seriesOption = {
      type: 'line',
      lineStyle: {
        width: 0,
      },
      showSymbol: false,
      symbol: 'none',
      emphasis: {
        focus: 'series',
      },
      areaStyle: {},
    }
    let series = apexChatData.lineAreaChartSpline.series.map(item =>
      Object.assign(item, seriesOption),
    )
    let areaLineChart = ref(
      Object.assign(apexChatData.lineAreaChartSpline, { series }),
    )
    let collectionData = ref({
      xData: [],
      yData: [],
      total: 0,
      updateDate: '',
    })
    // 获取【数据处理】数据
    const handleDataProcessing = data => {
      const record = data.allDataList
      collectionData.value.yData = record.map(item => {
        let data = item.arrays.map(v => Number(v.value))
        return {
          // name: `${store.state.enums.enums.CollectionType[item.type].text}`,
          name: item.typeName,
          type: item.type,
          data,
        }
      })
      collectionData.value.xData =
        record[0].arrays && record[0].arrays.map(t => t.key)
      collectionData.value.total = data.allDataNum
      collectionData.value.updateDate = data.updateDate
    }

    let alarmLevelEnum = store.state.enums.enums.AlarmLevel

    let isLoading = ref(false)
    let statisticsData = ref([])
    let currentPosture = ref(0)
    let updateDate = ref(new Date())

    // 获取车企统计和态势数据（合并接口调用）
    const loadAutomakerData = async () => {
      try {
        const res = await getAutomakerList({})
        const automakerData = res.data || []

        // ===== 基础统计数据处理 =====
        const totalCount = automakerData.length
        const supervisedCount = automakerData.filter(
          item => item.isSupervise === '0',
        ).length
        const riskCount = automakerData.filter(
          item => (item.riskScore || 0) > 0,
        ).length

        // 构造统计数据格式
        statisticsData.value = [
          {
            title: '车企总数',
            icon: 'icon-Vehicles',
            total: totalCount,
            assetTypeName: 'AutomakerTotal',
          },
          {
            title: '监管车企',
            icon: 'icon-zaixiancheliang',
            total: supervisedCount,
            percent:
              totalCount > 0
                ? ((supervisedCount / totalCount) * 100).toFixed(0) + '%'
                : '0%',
            assetTypeName: 'SupervisedAutomaker',
          },
          {
            title: '风险车企',
            icon: 'icon-weixiancheliang',
            total: riskCount,
            percent:
              totalCount > 0
                ? ((riskCount / totalCount) * 100).toFixed(0) + '%'
                : '0%',
            assetTypeName: 'RiskAutomaker',
          },
        ]

        // ===== 态势统计数据处理 =====
        // 筛选已监管的车企
        const supervisedAutomakers = automakerData.filter(
          item => item.isSupervise === '0',
        )

        // 统计不同安全态势的车企数量
        const postureStats = {
          Strong: { count: 0, text: '优秀', color: '#2EE56A' },
          Good: { count: 0, text: '良好', color: '#44E2FE' },
          Fair: { count: 0, text: '一般', color: '#FF910F' },
          Poor: { count: 0, text: '较差', color: '#DA1F1F' },
        }

        // 计算每个车企的安全等级并统计
        supervisedAutomakers.forEach(automaker => {
          // 使用统一的智能算法计算安全等级
          const securityLevel = getAutomakerSecurityLevelUnified({
            riskDetails: automaker.riskDetails,
            riskScore: automaker.riskScore,
            scoreType: 'auto',
          })

          postureStats[securityLevel.level].count++
        })

        const totalSupervised = supervisedAutomakers.length

        // 智能动态计算整体车企态势（优化版）
        let overallPosture = 0 // 默认为优秀态势
        if (totalSupervised > 0) {
          overallPosture = calculateOverallPosture(
            postureStats,
            totalSupervised,
          )

          // 调试信息（开发环境）
          if (process.env.NODE_ENV === 'development') {
            console.log('=== 整体态势计算调试信息 ===')
            console.log('车企分布:', {
              优秀: postureStats.Strong.count,
              良好: postureStats.Good.count,
              一般: postureStats.Fair.count,
              较差: postureStats.Poor.count,
              总数: totalSupervised,
            })
            console.log(
              '计算结果:',
              ['优秀', '良好', '一般', '较差'][overallPosture],
            )
            console.log('================================')
          }
        }

        // 更新整体态势
        currentPosture.value = overallPosture

        // 更新态势计算时间
        updateDate.value = new Date()

        // 构造展示数据格式（与原有的 totalEarning 格式保持一致）
        totalEarning.value = Object.keys(postureStats).map(level => {
          const stat = postureStats[level]
          const percentage =
            totalSupervised > 0
              ? ((stat.count / totalSupervised) * 100).toFixed(1)
              : '0.0'

          return {
            healthStatus: level,
            healthStatusName: stat.text,
            count: stat.count,
            alarmPercentage: `${percentage}%`,
            color: stat.color,
            progress: Math.ceil(parseFloat(percentage)),
          }
        })
      } catch (error) {
        console.error('获取车企数据失败：', error)
        // 设置默认值
        statisticsData.value = [
          {
            title: '车企总数',
            icon: 'icon-Vehicles',
            total: 0,
            assetTypeName: 'AutomakerTotal',
          },
          {
            title: '监管车企',
            icon: 'icon-zaixiancheliang',
            total: 0,
            percent: '0%',
            assetTypeName: 'SupervisedAutomaker',
          },
          {
            title: '风险车企',
            icon: 'icon-weixiancheliang',
            total: 0,
            percent: '0%',
            assetTypeName: 'RiskAutomaker',
          },
        ]

        currentPosture.value = 0 // 默认为优秀态势
        updateDate.value = new Date() // 设置计算时间
        totalEarning.value = [
          {
            healthStatus: 'Strong',
            healthStatusName: '优秀',
            count: 0,
            alarmPercentage: '0.0%',
            color: '#2EE56A',
            progress: 0,
          },
          {
            healthStatus: 'Good',
            healthStatusName: '良好',
            count: 0,
            alarmPercentage: '0.0%',
            color: '#44E2FE',
            progress: 0,
          },
          {
            healthStatus: 'Fair',
            healthStatusName: '一般',
            count: 0,
            alarmPercentage: '0.0%',
            color: '#FF910F',
            progress: 0,
          },
          {
            healthStatus: 'Poor',
            healthStatusName: '较差',
            count: 0,
            alarmPercentage: '0.0%',
            color: '#DA1F1F',
            progress: 0,
          },
        ]
      }
    }

    const loadSecurityPostureData = async () => {
      try {
        isLoading.value = true
        const params = {
          vehicleAssetVo: {
            assetTypeList: ['0', '1', '2', '3'],
          },
          assetAlarmVo: {
            alarmLevelList: ['0', '1', '2', '3'],
          },
          dataFlowVo: {
            collectTypes: ['1', '2', '3'],
          },
        }
        const { data } = await getSecurityPosture(params)
        // 获取车企统计和态势数据（合并接口调用）
        await loadAutomakerData()
        // 数据处理
        handleDataProcessing(data.processing)

        // 待处理事件
        handlePendingEvent(data.pendingEvent)
        isLoading.value = false
      } catch (err) {
        console.log('安全态势报错', err)
        isLoading.value = false
      }
    }

    const healthStatusEnum = computed(() =>
      Object.assign([], store.state.enums.enums.HealthStatus),
    ).value

    let totalEarning = ref([])
    const handleTotalEarning = data => {
      if (!(data && data.length > 0)) {
        return
      }

      totalEarning.value = healthStatusEnum.map(health => {
        const currentItem = data.find(v => v.healthStatus == health.value)
        const progress =
          currentItem && Number(currentItem.alarmPercentage.split('%')[0])
        return {
          ...currentItem,
          color: health.color,
          progress: Math.ceil(progress),
        }
      })
    }

    let waithandleAlarmList = ref([])
    const handlePendingEvent = data => {
      if (!(data && data.length > 0)) {
        return
      }

      waithandleAlarmList.value = data
    }

    onMounted(() => {
      loadSecurityPostureData()
    })
    return {
      collectionData,

      waithandleAlarmList,
      totalEarning,
      areaLineChart,

      icons: {
        mdiCalendarBlankOutline,
        mdiDotsVertical,
      },
      isLoading,
      statisticsData,
      currentPosture,
      updateDate,
    }
  },
}
</script>

<style lang="scss" scoped>
.grid-container {
  display: grid;

  grid-template-columns: 68% auto;
  // grid-auto-rows: 1fr 2fr 2fr;
  grid-template-rows: minmax(120px, 1fr) minmax(240px, 2fr) 2fr;
  gap: 0.9rem;
  .item4 {
    grid-area: 1 / 2 / 4 / 4;
  }

  height: 100%;
  padding: 1rem;
}
</style>
