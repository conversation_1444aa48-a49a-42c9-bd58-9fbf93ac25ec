const ticketStatistics = {
  statusHeaders: {
    priority: '优先级',
    opening: '待处理',
    openingExpired: '待处理（已超期）',
    handling: '处理中',
    handlingExpired: '处理中（已超期）',
    resolved: '已解决',
    closed: '已关闭',
    cancelled: '已取消',
    total: '总数',
  },
  peopleHeaders: {
    assigned: '处理人',
    received: '已接收',
    resolved: '已完成',
    handling: '处理中（未超期）',
    handlingExpired: '处理中（已超期）',
  },
  closeAutoRefresh: '自动刷新已关闭！',
  setRefresh: '设置刷新频率',
  addByDay: '新增总数',
  backlogByDay: '积压总数',
  ticketType: '工单类型',
  title1: '最近新增',
  title2: '工单处理状态',
  title3: '人员处理情况',
  title4: '工单新增趋势',
  title5: '未完结工单趋势',
  recentlyAdd: '最近新增',
  totalTicket: '工单总数',
}

export default ticketStatistics
