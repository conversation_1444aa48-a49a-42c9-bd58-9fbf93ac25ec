<template>
  <div
    v-show="isLoadingFinished"
    class="d-flex flex-column align-center justify-center h-full"
  >
    <v-img
      contain
      max-width="12.5rem"
      max-height="10rem"
      class="mt-n2"
      src="@/assets/images/chart-empty.svg"
    ></v-img>
    <!-- <vsoc-icon class="mt-n3" size="12.5rem" icon="icon-zanwushuju"></vsoc-icon> -->
    <div>{{ $t('global.noData') }}</div>
  </div>
</template>

<script>
export default {
  name: 'ChartEmpty',
  props: {
    // 是否加载完成
    isLoadingFinished: {
      type: Boolean,
      default: true,
    },
  },
}
</script>
<style scoped lang="scss"></style>
