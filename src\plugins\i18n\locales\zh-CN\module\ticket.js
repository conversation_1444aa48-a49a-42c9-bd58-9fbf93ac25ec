const ticket = {
  currentTitle: '工单',
  headers: {
    ticketType: '工单类型',
    ticketId: '工单编号',
    title: '工单标题',
    name: '工单标题',
    content: '工单内容',
    priority: '优先级',
    createDate: '创建时间',
    createUser: '创建人',
    classifyName: '工单标签',
    assignedToName: '处理人',
    assignedGroupName: '处理组',
    status: '状态',
    dataSourceName: '来源',
    relationId: '关联ID',
    time: '时间',
    reason: '原因',
    impact: '影响等级',
    eta: '预期完成时间',
    TimeLeft: '剩余时间',
    ElapsedTime: '已用时间',
    ElapsedPercentage: '已用百分比',
    WatchList: '关注人',
    Operation: '操作类型',
    models: '影响车企',
  },
  changeHeaders: {
    claimed: '待认领',
    list: '我的待办',
    Application: '我的申请',
    tickets: '全部工单',
    follow: '我的关注',
  },
  btn: {
    reback: '退出',
    assign: '指派',
    cancel: '取消',
    resolve: '完结',
    upLoad: '上传附件',
    import: '导入',
    detail: '工单详情',
    add: '新增工单',
    edit: '工单处理',
  },
  title: {},
  hint: {
    updateTips: '显示更新操作',
    can: '可{0}工单',
    time: '请选择时间',
    suceess: '成功！',
    timeTip1: '时间跨度不允许超过3个月',
    opInfo: '确定要{0}「{1}」条工单吗？',
    attachments: '关联附件',
    tracks: '工单轨迹',
    follow: '跟进结果',
    analy: '漏洞分析',
    solution: '解决方案',
    attachmentsTip: '附件数量上限{0}个，单个附件大小不超过{1}；附件格式要求',
    Details: '详细描述',
    please: '请输入',
    upLoad: '上传的文件格式有误!',
    upLoad1: '上传的文件大小不能超过{0}!',
    upLoad2: '上传失败',
    upLoad3: '最多上传附件{0}个',
    comfirmInfo: '处理人未发生变更，无需指派！',
    successfully: '成功',
    tips: '点击确认后，原内容会被立即覆盖，请谨慎选择！',
    timeTip: '预计完成时间需要大于当前时间！',
  },
  template: {
    hint: '下载批量创建工单导入模板',
    text: '批量创建工单导入模板',
  },
  fileHint: '单个附件大小不超过5M；附件格式要求：xls,xlsx',
  remarkHint: '最大允许输入字符为5W，超过请使用附件上传！',
  remarkHint1: '输入的内容超出最大限制，建议采用附件的方式上传！',
  dashboardVulner: {
    attackCharacter: '攻击角色',
    attackVector: '攻击向量',
    importProport: '影响范围',
    accessType: '访问类型',
    attackDistance: '范围',
    vulnerabilityAssessment: '漏洞评估',
  },
  completeAlertTip1:
    '在工单完结前，请确保工单关联的告警已得到妥善处置或明确标记为不需处置。',
  completeAlertTip2: '请注意，此工单仍有未处理的告警。您确定要强制关闭工单吗？',
  completeVulTip1:
    '在工单完结前，请确保工单关联的漏洞已得到妥善处置或明确标记为不需处置。',
  completeVulTip2: '请注意，此工单仍有未处理的漏洞。您确定要强制关闭工单吗？',
}

export default ticket
