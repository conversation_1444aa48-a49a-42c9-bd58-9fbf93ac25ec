server {
    listen       80;
    server_name  localhost;
    client_max_body_size 20m;
    #charset koi8-r;
    #access_log  /var/log/nginx/host.access.log  main;
    proxy_set_header  X-Forwarded-Host $http_host;
    # proxy_set_header  x-iam-app-host  $http_host;
    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
    }
    location /test {
            proxy_set_header  X-Forwarded-Host $http_host;
            proxy_set_header  Host $http_host;
            alias /usr/share/nginx/html/test;
            try_files $uri $uri/ /test/index.html;
            index  index.html index.htm;
        }
        location /uat {
            alias /usr/share/nginx/html/uat;
            try_files $uri $uri/ /uat/index.html;
            index  index.html index.htm;
        }

    #error_page  404              /404.html;

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

    # proxy the PHP scripts to Apache listening on 127.0.0.1:80
    #
    #location ~ \.php$ {
    #    proxy_pass   http://127.0.0.1;
    #}

    # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
    #
    #location ~ \.php$ {
    #    root           html;
    #    fastcgi_pass   127.0.0.1:9000;
    #    fastcgi_index  index.php;
    #    fastcgi_param  SCRIPT_FILENAME  /scripts$fastcgi_script_name;
    #    include        fastcgi_params;
    #}

    # deny access to .htaccess files, if Apache's document root
    # concurs with nginx's one
    #
    #location ~ /\.ht {
    #    deny  all;
    #}
}
