const parameter = {
  currentTitle: 'Parameter Setting',
  headers: {
    id: 'ID', //主键id
    propertyName: 'Parameter Name', //参数名称
    groupName: 'Group Name', //参数组名
    propertyKey: 'Parameter Key', //参数键值
    propertyType: 'Parameter Type', //参数值类型 URL/BOOLEAN/STRING/INTEGER/ENCRYPTEDSTRING/NUMBER等
    propertyValue: 'Parameter Value',
    isDefault: 'Built in system', //系统内置（0是 1否）
    description: 'Remark', //描述
  },
  tip: 'Click to create group name',
  tip1: 'Only English, numbers, and special characters - _ . , ( ) can be entered',
  del: {
    title: 'Delete Parameter',
    text: 'Are you sure to delete the Parameter:{0}?',
  },
}
export default parameter
