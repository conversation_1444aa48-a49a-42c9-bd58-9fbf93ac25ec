<template>
  <!-- <v-card flat class="mt-5 tab-height">
    <v-form ref="form">
      <div class="px-3">
        <v-card-text class="pt-5">
          <v-row>
            <v-col cols="12" sm="8" md="6">
              <v-text-field
                v-model="currentPassword"
                :type="isCurrentPasswordVisible ? 'text' : 'password'"
                :append-icon="
                  isCurrentPasswordVisible
                    ? icons.mdiEyeOffOutline
                    : icons.mdiEyeOutline
                "
                label="旧密码"
                :rules="[passwordRules.required]"
                outlined
                dense
                @click:append="
                  isCurrentPasswordVisible = !isCurrentPasswordVisible
                "
              ></v-text-field>
              <v-text-field
                v-model="newPassword"
                :type="isNewPasswordVisible ? 'text' : 'password'"
                :append-icon="
                  isNewPasswordVisible
                    ? icons.mdiEyeOffOutline
                    : icons.mdiEyeOutline
                "
                label="新密码"
                :rules="[passwordRules.required, passwordRules.min]"
                outlined
                dense
                hint="密码长度不能少于8位数"
                persistent-hint
                @click:append="isNewPasswordVisible = !isNewPasswordVisible"
              ></v-text-field>
              <v-text-field
                v-model="cPassword"
                :type="isCPasswordVisible ? 'text' : 'password'"
                :append-icon="
                  isCPasswordVisible
                    ? icons.mdiEyeOffOutline
                    : icons.mdiEyeOutline
                "
                label="确认新密码"
                :rules="[passwordRules.required, passwordRules.min]"
                outlined
                dense
                class="mt-3"
                @click:append="isCPasswordVisible = !isCPasswordVisible"
              ></v-text-field>
            </v-col>

            <v-col
              cols="12"
              sm="4"
              md="6"
              class="d-none d-sm-flex justify-center position-relative"
            >
              <v-img
                contain
                max-width="170"
                src="@/assets/images/3d-characters/pose-m-1.png"
                class="security-character"
              ></v-img>
            </v-col>
          </v-row>
        </v-card-text>
      </div>
      <v-divider></v-divider>
      <div>
        <v-card-title class="flex-nowrap">
          <v-icon class="text--primary me-3">
            {{ icons.mdiKeyOutline }}
          </v-icon>
          <span class="text-break">双重身份验证</span>
        </v-card-title>

        <v-card-text class="two-factor-auth text-center mx-auto">
          <v-avatar
            color="primary"
            class="v-avatar-light-bg primary--text mb-4"
            rounded
          >
            <v-icon size="25" color="primary">
              {{ icons.mdiLockOpenOutline }}
            </v-icon>
          </v-avatar>
          <p class="text-base text--primary font-weight-semibold">
            双重身份验证尚未开启
          </p>
          <p class="text-sm text--primary">
            双重身份验证增加了另一层通过要求的不仅仅是登录密码。
            <a href="#">了解更多</a>
          </p>
        </v-card-text>
        <v-card-text>
          <v-btn color="primary" class="me-3 mt-3" @click="onSave">
            保存更改
          </v-btn>
          <v-btn color="secondary" outlined class="mt-3" @click="onReset">
            取消
          </v-btn>
        </v-card-text>
      </div>
    </v-form>
  </v-card> -->

  <v-row no-gutters>
    <!-- 头像 -->
    <v-col cols="3" class="pt-2 mt-5 text-center tab-height">
      <div class="pl-6 mt-6">
        <v-badge avatar bordered overlap offset-x="50" offset-y="100">
          <template v-slot:badge>
            <v-avatar size="24">
              <v-icon>mdi-camera-flip</v-icon>
            </v-avatar>
          </template>

          <v-avatar size="100" class="me-6">
            <v-img :src="accountDataLocale.avatarImg"></v-img>
          </v-avatar>
        </v-badge>
      </div>

      <div class="text-h4 color-base mt-6">
        {{ accountDataLocale.userName }}
      </div>
      <div class="mt-2 mb-7">{{ accountDataLocale.roleName }}</div>

      <v-chip
        class="mr-2 mt-3"
        v-for="(item, index) in accountDataLocale.departments"
        :key="index"
        small
      >
        <span class="px-1">{{ item.departmentName | dataFilter }}</span>
      </v-chip>
    </v-col>

    <div>
      <v-divider vertical class="mx-1"></v-divider>
    </div>

    <!-- 用户信息 -->
    <v-col cols="7" class="mt-7 pl-14">
      <v-form ref="form" class="multi-col-validation mt-6 w-60">
        <v-row>
          <v-col md="12" cols="12">
            <v-text-field
              v-model="currentPassword"
              :type="isCurrentPasswordVisible ? 'text' : 'password'"
              :append-icon="
                isCurrentPasswordVisible
                  ? icons.mdiEyeOffOutline
                  : icons.mdiEyeOutline
              "
              :label="$t('account.oldPassword')"
              :rules="[passwordRules.required]"
              @click:append="
                isCurrentPasswordVisible = !isCurrentPasswordVisible
              "
            ></v-text-field>
          </v-col>
          <v-col md="12" cols="12">
            <v-text-field
              v-model="newPassword"
              :type="isNewPasswordVisible ? 'text' : 'password'"
              :append-icon="
                isNewPasswordVisible
                  ? icons.mdiEyeOffOutline
                  : icons.mdiEyeOutline
              "
              :label="$t('account.newPassword')"
              :rules="[passwordRules.required, passwordRules.min]"
              persistent-hint
              @click:append="isNewPasswordVisible = !isNewPasswordVisible"
            ></v-text-field>
            <!-- hint="密码长度不能少于8位数" -->
          </v-col>
          <v-col md="12" cols="12">
            <v-text-field
              v-model="cPassword"
              :type="isCPasswordVisible ? 'text' : 'password'"
              :append-icon="
                isCPasswordVisible
                  ? icons.mdiEyeOffOutline
                  : icons.mdiEyeOutline
              "
              :label="$t('user.drawer.confirmPassword')"
              :rules="[
                passwordRules.required,
                passwordRules.min,
                passwordRules.confirmedValidator(cPassword, newPassword),
              ]"
              class="mt-3"
              @click:append="isCPasswordVisible = !isCPasswordVisible"
            ></v-text-field>
          </v-col>
          <v-col cols="12" class="pt-0 pb-0 d-flex flex-row-reverse">
            <v-btn
              color="primary"
              class="mt-4"
              @click="onSave"
              min-width="64"
              width="64"
            >
              {{ $t('action.save') }}
            </v-btn>
            <v-btn
              color="secondary"
              outlined
              class="mt-4 me-4"
              @click="onReset"
              min-width="64"
              width="64"
            >
              {{ $t('action.cancel') }}
            </v-btn>
          </v-col>
        </v-row>
      </v-form>
    </v-col>
  </v-row>
</template>

<script>
import { confirmedValidator, min, required } from '@/@core/utils/validation'
// eslint-disable-next-line object-curly-newline
import { resetPassword } from '@/api/system/user'
import router from '@/router'
import {
  mdiEyeOffOutline,
  mdiEyeOutline,
  mdiKeyOutline,
  mdiLockOpenOutline,
} from '@mdi/js'
import { getCurrentInstance, ref } from '@vue/composition-api'
import md5 from 'md5'

export default {
  setup() {
    const vm = getCurrentInstance().proxy
    const isCurrentPasswordVisible = ref(false)
    const isNewPasswordVisible = ref(false)
    const isCPasswordVisible = ref(false)
    const currentPassword = ref('')
    const newPassword = ref('')
    const cPassword = ref('')
    const passwordRules = ref({
      required: v => required(v, vm.$t('user.drawer.password')),
      min: v => min(v, [vm.$t('user.drawer.password')]),
      confirmedValidator,
    })
    const form = ref(null)

    // text: `重置密码成功，${vm.$store.getters.userInfo?.userName}将退出系统!`,

    const onShowDia = () => {
      vm.$swal({
        title: vm.$t('account.swal.title'),
        text: vm.$t('account.swal.text'),
        icon: 'success',
        reverseButtons: true,
        showCancelButton: false,
        confirmButtonText: this.$t('action.confirm'),
        cancelButtonText: this.$t('action.cancel'),
        customClass: {
          confirmButton: 'sweet-btn-primary',
        },
      }).then(async result => {
        vm.$store.dispatch('user/logOut').then(rep => {
          router.push({ path: '/login', query: { redirect: vm.$route.path } })
        })
      })
    }

    // 保存更改
    const onSave = () => {
      const bool = vm.$refs.form.validate()
      if (!bool) {
        return
      }

      // if (newPassword.value !== cPassword.value) {
      //   vm.$notify.info('error', '新密码两次输入不一致！')

      //   return
      // }
      const params = {
        oldPassword: `${md5(currentPassword.value).slice(0, 10)}vsoc`,
        password: `${md5(newPassword.value).slice(0, 10)}vsoc`,
        rePassword: `${md5(cPassword.value).slice(0, 10)}vsoc`,
      }
      resetPassword(params)
        .then(res => {
          if (res.code === 200) {
            vm.$notify.info('success', this.$t('account.reset'))
            onShowDia()
          } else {
            throw new Error(res.msg)
          }
        })
        .catch(err => {
          vm.$notify.info('error', err)
        })
    }

    // 取消修改
    const onReset = () => {
      currentPassword.value = ''
      newPassword.value = ''
      cPassword.value = ''

      // 重置校验和表单
      vm.$refs.form.resetValidation()
      vm.$refs.form.reset()
    }

    const userInfo = JSON.parse(JSON.stringify(vm.$store.getters.userInfo))
    const accountDataLocale = ref(
      Object.assign(userInfo, {
        avatarImg: require('@/assets/images/avatars/3.png'),
      }),
    )

    return {
      form,
      onSave,
      onReset,
      passwordRules,
      isCurrentPasswordVisible,
      isNewPasswordVisible,
      currentPassword,
      isCPasswordVisible,
      newPassword,
      cPassword,
      icons: {
        mdiKeyOutline,
        mdiLockOpenOutline,
        mdiEyeOffOutline,
        mdiEyeOutline,
      },
      accountDataLocale,
    }
  },
}
</script>

<style lang="scss" scoped>
.two-factor-auth {
  max-width: 35rem;
}
.security-character {
  position: absolute;
  bottom: -0.5rem;
}
.tab-height {
  height: calc(100vh - 130px);
}
</style>
