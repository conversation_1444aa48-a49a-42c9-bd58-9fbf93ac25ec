<template>
  <edit-page
    :popsName="
      editType === 'add'
        ? $t('model.btn.new')
        : editType === 'edit'
        ? $t('model.btn.edit')
        : $t('model.btn.detail')
    "
  >
    <div v-if="editType !== 'detail'" slot="BreadBtn">
      <v-btn
        color="primary"
        :loading="confirmLoading"
        elevation="0"
        @click="confirm"
      >
        {{ $t('action.save') }}
      </v-btn>
    </div>

    <div class="edit-center-box model-center-box pb-9">
      <v-form ref="form" v-model="valid">
        <div class="text-title color-base font-weight-semibold-light mb-4 mt-6">
          {{ $t('global.drawer.baseInfo') }}
        </div>
        <div class="px-9">
          <v-row>
            <v-col class="mr-6"
              ><v-text-field
                v-model="editForm.name"
                color="primary"
                :label="$t('model.headers.name')"
                :rules="[required(editForm.name, $t('model.headers.name'))]"
                :disabled="editType === 'detail'"
              ></v-text-field
            ></v-col>
            <v-col>
              <v-text-field
                v-model="editForm.modelCode"
                color="primary"
                :label="$t('model.headers.code')"
                :rules="[max(editForm.modelCode, 100)]"
                :disabled="editType === 'detail'"
              ></v-text-field>
            </v-col>
          </v-row>
          <v-textarea
            v-model="editForm.description"
            :label="$t('model.headers.desc')"
            :rows="$AREA_ROWS"
            color="primary"
            :disabled="editType === 'detail'"
            outlined
          ></v-textarea>
        </div>
        <div class="text-title color-base font-weight-semibold-light mb-5">
          {{ $t('model.edit.picture') }}
        </div>
        <div class="px-9">
          <el-upload
            class="upload-demo platform__file"
            action=""
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :on-change="handleChange"
            :on-success="handleSuccess"
            :file-list="attachments"
            list-type="picture-card"
            accept=".jpg,.png,.jpeg"
            :auto-upload="false"
            :disabled="editType === 'detail'"
          >
            <v-icon color="primary" size="36">mdi-plus</v-icon>
            <template slot="file" slot-scope="{ file }"> </template>
          </el-upload>
          <Dialog :visible.sync="dialogVisible">
            <img width="100%" :src="dialogImageUrl" alt="" />
          </Dialog>
        </div>
        <div class="text-title color-base font-weight-semibold-light mt-8 mb-5">
          {{ $t('model.device.title') }}
        </div>
        <div class="px-9">
          <v-btn
            v-if="editType !== 'detail'"
            class="mb-3"
            elevation="0"
            color="primary"
            @click="addDevice"
          >
            {{ $t('action.add') }}
          </v-btn>
        </div>
      </v-form>
    </div>
  </edit-page>
</template>

<script>
import { max, required } from '@/@core/utils/validation'
import {
  addVehiclePlatform,
  editVehiclePlatform,
  vehicleTypeDetail,
} from '@/api/asset/platform'
import editPage from '@/components/EditPage.vue'
import { getToken } from '@/util/token'
import { deepClone, uploadImgToBase64 } from '@/util/utils'
import { Dialog } from 'element-ui'
import draggable from 'vuedraggable'
import { vsocPath } from '../../../util/request'

export default {
  name: 'ModelEdit',
  components: {
    editPage,
    draggable,
    Dialog,
  },
  data() {
    return {
      dialogImageUrl: '',
      dialogVisible: false,
      headers: {
        Authorization: getToken(),
      },
      vsocPath,
      attachments: [],
      signalList: [],
      deviceSignalList: [],
      valid: true,
      editType: 'add',
      nameRules: [v => required(v, this.$t('model.headers.name'))],
      editForm: {
        name: '',
        modelCode: '',
        pictureCode: '',
        description: '',
        fileId: '',
        fileUrl: '',
        deviceTypeInfosObj: [],
      },
      max,
      required,
      confirmLoading: false,
    }
  },
  computed: {
    headers1() {
      return [
        {
          text: this.$t('model.device.sort'),
          value: 'sort',
        },
        {
          text: this.$t('model.device.type'),
          value: 'deviceType',
          width: '20%',
        },
        {
          text: this.$t('model.device.icon'),
          value: 'deviceIcon',
          width: '15%',
        },
        {
          text: this.$t('model.device.name'),
          value: 'deviceName',
          width: '20%',
        },
        {
          text: this.$t('model.device.id'),
          value: 'deviceId',
          width: '20%',
        },
        {
          text: this.$t('model.device.info'),
          value: 'deviceInfo',
          width: '25%',
        },
        {
          text: '',
          value: 'actions',
          sortable: false,
        },
      ]
    },
    unitItems() {
      return [
        {
          icon: 'icon-T-Box',
        },
        {
          icon: 'icon-IVI',
        },
        {
          icon: 'icon-wangguan',
        },
        {
          icon: 'icon-yukong',
        },
      ]
    },
    deviceTypeMap() {
      return Object.assign([], this.$store.state.enums.enums.DeviceType)
    },
  },
  created() {
    console.log('created')

    if (this.$route.query.id) {
      if (this.$route.query.isDetail === '1') {
        this.editType = 'detail'
      } else {
        this.editType = 'edit'
      }
      this.getInfoById(this.$route.query.id)
    } else {
      this.editType = 'add'
      this.attachments = [
        // 默认车型图片
        {
          id: 'vehicleType/<EMAIL>',
          name: 'default-car',
          url: require('@/assets/images/pages/<EMAIL>'),
          // url: `${
          //   window.location.href?.split('model')[0]
          // }static/img/<EMAIL>`,
        },
      ]
      this.$nextTick(() => {
        this.$refs.form.resetValidation()
      })
    }
    this.getSignal()
  },
  methods: {
    async getSignal() {
      await this.$store.dispatch('global/searchSignal')
      this.signalList = this.$store.state.global.signals.filter(
        v => v.dataType !== '2',
      )
      this.deviceSignalList = this.$store.state.global.signals.filter(
        v => ['0', '2', '3'].includes(v.signalValueType) && v.dataType !== '2',
      )
    },
    addDevice() {
      const item = {
        sort: this.editForm.deviceTypeInfosObj.length + 1, //排序
        deviceType: '', //设备类型
        deviceIcon: '', //设备图标
        deviceName: '', //设备名称
        deviceInfo: [],
      }
      this.editForm.deviceTypeInfosObj.push(item)
    },
    // 删除单项
    delList(slotData) {
      const indexRow = slotData.index
      this.editForm.deviceTypeInfosObj.splice(indexRow, 1)
    },
    async ondragend(event, list) {
      this.editForm.deviceTypeInfosObj = list || []
    },
    handleChange(file) {
      if (file.size > 200 * 1024) {
        this.attachments = this.attachments.filter(v => v.uid !== file.uid)
        return this.$notify.info('error', this.$t('model.hint3'))
      }
      let testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
      const isJpg = ['jpg', 'png', 'jpeg', 'JPG', 'PNG', 'JPEG'].includes(
        testmsg,
      )
      if (!isJpg) {
        //图片格式判断
        this.attachments = this.attachments.filter(v => v.uid !== file.uid)
        this.$notify.info('error', this.$t('model.hint2'))
      } else {
        this.attachments = [{ ...file }]
      }
    },
    // handleBefore(file) {
    //   if (file.type !== 'image/png' && file.type !== 'image/jpeg') {
    //     this.$notify.info('error', '只能上传jpg/png文件！')
    //     return false
    //   }
    // },
    handleRemove(file) {
      this.attachments = []
    },
    handlePreview(file) {
      // 判断是否为base64
      // if (file.url.indexOf('data:image/jpg;base64,') > -1) {
      //   window.open(file.url)
      // } else {
      //   const img = new Image()
      //   img.src = file.url
      //   const newWin = window.open('', '_blank')
      //   newWin.document.write(img.outerHTML)
      // }
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleSuccess(response, file, fileList) {
      // console.log(response, file, fileList)
      // if (response.code === 200) {
      //   this.attachments = [
      //     {
      //       id: response.data,
      //       ...file,
      //     },
      //   ]
      // } else {
      //   this.$notify.info('error', response.msg)
      // }
    },

    // 获取详情
    async getInfoById(id) {
      try {
        const res = await vehicleTypeDetail({
          id: id,
        })
        for (const i in this.editForm) {
          if (i === 'deviceTypeInfosObj' && !res.data[i]) {
            res.data[i] = []
          }
          this.editForm[i] = res.data[i]
        }
        this.editForm.id = res.data.id
        this.attachments = []

        if (this.editForm.pictureCode) {
          this.attachments = [
            {
              id: this.editForm.id,
              url: this.editForm.pictureCode,
            },
          ]
        }
        // if (this.editForm.fileId && this.editForm.fileUrl) {
        //   this.attachments = [
        //     {
        //       id: this.editForm.fileId,
        //       name: this.editForm.fileId,
        //       url: this.editForm.fileUrl,
        //     },
        //   ]
        // }

        if (this.editForm.deviceTypeInfosObj.length) {
          this.editForm.deviceTypeInfosObj.forEach(v => {
            v.deviceInfo = v.deviceInfo.map(v => v.signalId)
          })
        }
      } catch (e) {
        console.error(`获取车型详情：${e}`)
      }
    },
    async confirm() {
      const bool = this.$refs.form.validate()
      if (!bool) return
      if (!this.attachments || this.attachments.length === 0) {
        return this.$notify.info('error', this.$t('global.file.uploadFile'))
      }
      // this.editForm.fileId = this.attachments.map(v => v.id).join(',')
      if (this.attachments[0].raw) {
        this.editForm.pictureCode = await uploadImgToBase64(
          this.attachments[0].raw,
        )
      } else {
        this.editForm.pictureCode = this.attachments[0].url
      }
      const data = deepClone(this.editForm)
      if (data.deviceTypeInfosObj.length) {
        const isSomeDeviceType = data.deviceTypeInfosObj.some(
          (item, index, array) => {
            return (
              array.filter(
                innerItem => innerItem.deviceType === item.deviceType,
              ).length > 1
            )
          },
        )
        if (isSomeDeviceType) {
          // 设备类型不能重复
          return this.$notify.info('error', this.$t('model.hint1'))
        }
        data.deviceTypeInfosObj.forEach((item, index) => {
          item.sort = index + 1
          item.deviceInfo = item.deviceInfo.map(v => {
            return {
              sort: item.sort,
              signalId: v,
            }
          })
        })
      }
      try {
        this.confirmLoading = true
        if (this.editType === 'add') {
          const res = await addVehiclePlatform(data)
          if (res.code === 200) {
            this.$notify.info(
              'success',
              this.$t('global.hint.add', [this.$t('model.currentTitle')]),
            )
            this.$router.push('/model')
          }
        } else if (this.editType === 'edit') {
          const res = await editVehiclePlatform(data)
          if (res.code === 200) {
            this.$notify.info(
              'success',
              this.$t('global.hint.edit', [this.$t('model.currentTitle')]),
            )
            this.$router.push('/model')
          }
        }
      } catch (error) {
        console.error(`确认车型错误：${error}`)
      }
      this.confirmLoading = false
    },
  },
}
</script>

<style lang="scss" scoped>
.icon--default {
  background: url('../../../assets/images/pages/<EMAIL>')
    center/cover;
  width: 100%;
  height: 100%;
}
</style>
