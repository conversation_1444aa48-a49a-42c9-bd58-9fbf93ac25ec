<template>
  <!--车型轮播 定制化图形3-->
  <div class="box h-100">
    <div class="box-header">{{ title }}</div>
    <div class="h-100 d-flex justify-space-between">
      <div
        class="text-right d-flex justify-space-between"
        style="margin-top: 3%; margin-left: 5%; padding-right: 10%; width: 90%"
      >
        <div
          v-for="(item, index) in list"
          :key="index"
          style="margin-top: 15%; margin-right: 2%"
        >
          <div class="font-weight-medium fs-14-1">
            {{ item.name }}
          </div>
          <div
            v-show-tips="item.value"
            class="card-item-title-text fs-14-1 text-center"
            :style="{
              color:
                Number(item.type) === 1
                  ? '#32FDB8'
                  : Number(item.type) === 2
                  ? '#FF385D'
                  : '#44E2FE',
            }"
            style="filter: blur(0.3px)"
          >
            {{ numberToFormat(item.value) }}
          </div>
        </div>
      </div>
      <!-- <div class="enterprise-showcase-container h-100">
        <div class="tech-border-frame">
          <div class="corner-decoration top-left"></div>
          <div class="corner-decoration top-right"></div>
          <div class="corner-decoration bottom-left"></div>
          <div class="corner-decoration bottom-right"></div>

          <div class="scan-line horizontal top"></div>
          <div class="scan-line horizontal bottom"></div>
          <div class="scan-line vertical left"></div>
          <div class="scan-line vertical right"></div>
        </div>

        <div class="showcase-glow-bg"></div>

        <div class="carousel-main-wrapper">
          <v-carousel
            v-model="imgValue"
            :cycle="itemImgs.length > 1 ? true : false"
            :show-arrows="false"
            height="100%"
            interval="4000"
            hide-delimiters
            class="enterprise-carousel"
          >
            <template v-if="itemImgs.length">
              <v-carousel-item
                v-for="(item, i) in itemImgs"
                :key="i"
                :src="item"
                :value="i"
                class="showcase-item"
              >
              </v-carousel-item>
            </template>
            <template v-else>
              <v-carousel-item
                src="@/assets/images/pages/<EMAIL>"
                class="showcase-item"
              >
              </v-carousel-item>
            </template>
          </v-carousel>
        </div>
      </div> -->
    </div>
  </div>
</template>

<script>
import { numberToFormat } from '@/util/filters'
import countTo from 'vue-count-to'

export default {
  name: 'CardItem9',
  props: {
    title: {
      type: String,
      default: '',
    },
    list: {
      type: Array,
      default: () => {
        return []
      },
    },
    modelImgs: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  components: {
    countTo,
  },
  computed: {},
  watch: {
    list: {
      handler() {
        // 处理列表数据变化
      },
      immediate: true,
    },
    modelImgs: {
      handler(val) {
        this.itemImgs = val || []
        if (this.itemImgs.length === 2) {
          this.itemImgs.push(val[0])
          this.itemImgs.push(val[1])
        }
      },
      immediate: true,
    },
    // imgValue(val) {
    //   this.findType(val)
    // },
  },
  data() {
    return {
      currentTypeImgText: '',
      imgValue: '',
      itemImgs: [],
      numberToFormat,
    }
  },
  created() {
    // this.imgValue = this.currentTypeList[0]
    // this.findType(this.imgValue)
  },
  methods: {
    // findType(id) {
    //   let findItem = this.vehicleType.find(v => v.id === id)
    //   if (findItem) {
    //     this.currentTypeImgText = findItem.text
    //   }
    // },
  },
}
</script>
<style lang="scss" scoped>
// 车企特色展示容器
.enterprise-showcase-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px 1rem;
  margin-right: 8px;
  margin-top: 0.8rem;
  width: 40%;
  overflow: hidden;
}

// 科技感边框框架
.tech-border-frame {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 3;
}

// 四角装饰
.corner-decoration {
  position: absolute;
  width: 20px;
  height: 20px;
  border: 2px solid #44e2fe;
  border-radius: 2px; // 添加圆角弧度

  &.top-left {
    top: 2px;
    left: 2px;
    border-right: none;
    border-bottom: none;

    // animation: corner-glow 3s ease-in-out infinite alternate;
  }

  &.top-right {
    top: 2px;
    right: 2px;
    border-left: none;
    border-bottom: none;
    // animation: corner-glow 3s ease-in-out infinite alternate 0.5s;
  }

  &.bottom-left {
    bottom: 2px;
    left: 2px;
    border-right: none;
    border-top: none;
    // animation: corner-glow 3s ease-in-out infinite alternate 1s;
  }

  &.bottom-right {
    bottom: 2px;
    right: 2px;
    border-left: none;
    border-top: none;
    // animation: corner-glow 3s ease-in-out infinite alternate 1.5s;
  }
}

// 扫描线效果
.scan-line {
  position: absolute;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(68, 226, 254, 0.8) 50%,
    transparent 100%
  );

  &.horizontal {
    height: 1px;
    width: 100%;

    &.top {
      top: 0;
      animation: scan-horizontal 4s linear infinite;
    }

    &.bottom {
      bottom: 0;
      animation: scan-horizontal 4s linear infinite 2s;
    }
  }

  &.vertical {
    width: 1px;
    height: 100%;
    background: linear-gradient(
      180deg,
      transparent 0%,
      rgba(68, 226, 254, 0.8) 50%,
      transparent 100%
    );

    &.left {
      left: 0;
      animation: scan-vertical 4s linear infinite 1s;
    }

    &.right {
      right: 0;
      animation: scan-vertical 4s linear infinite 3s;
    }
  }
}

// 背景光效
.showcase-glow-bg {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120%;
  height: 120%;
  background: radial-gradient(
    ellipse,
    rgba(68, 226, 254, 0.08) 0%,
    rgba(68, 226, 254, 0.04) 40%,
    transparent 70%
  );
  border-radius: 50%;
  animation: glow-pulse 5s ease-in-out infinite alternate;
  z-index: 1;
}

// 轮播主体包装器
.carousel-main-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
  background: rgba(0, 20, 40, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(68, 226, 254, 0.2);
  z-index: 2;

  // 内部光效
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(68, 226, 254, 0.1) 0%,
      transparent 30%,
      transparent 70%,
      rgba(68, 226, 254, 0.05) 100%
    );
    pointer-events: none;
    z-index: 1;
  }
}

// 企业轮播样式
.enterprise-carousel {
  position: relative;
  z-index: 2;
  border-radius: 12px;
  overflow: hidden;

  // 添加微妙的内阴影
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    box-shadow: inset 0 0 30px rgba(0, 0, 0, 0.3);
    pointer-events: none;
    z-index: 3;
  }
}

// 展示项样式
.showcase-item {
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      45deg,
      rgba(68, 226, 254, 0.1) 0%,
      transparent 50%,
      rgba(68, 226, 254, 0.05) 100%
    );
    opacity: 0;
    transition: opacity 0.5s ease;
    z-index: 2;
  }

  &:hover::before {
    opacity: 1;
  }
}

// 自定义指示器
.custom-indicators {
  position: absolute;
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 12px;
  z-index: 4;
}

.indicator-dot {
  position: relative;
  width: 12px;
  height: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  .dot-inner {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    border: 1px solid rgba(68, 226, 254, 0.5);
    transition: all 0.3s ease;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background: #44e2fe;
      opacity: 0;
      transition: all 0.3s ease;
    }
  }

  &:hover .dot-inner {
    border-color: rgba(68, 226, 254, 0.8);
    box-shadow: 0 0 8px rgba(68, 226, 254, 0.4);
    transform: scale(1.1);
  }

  &.active .dot-inner {
    background: rgba(68, 226, 254, 0.2);
    border-color: #44e2fe;
    box-shadow: 0 0 12px rgba(68, 226, 254, 0.6),
      0 0 24px rgba(68, 226, 254, 0.3);

    &::before {
      opacity: 1;
    }
  }
}

// 车企特色标签
.enterprise-label {
  position: absolute;
  top: 16px;
  left: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  background: rgba(0, 20, 40, 0.8);
  border: 1px solid rgba(68, 226, 254, 0.3);
  border-radius: 20px;
  backdrop-filter: blur(8px);
  z-index: 4;
  animation: label-glow 4s ease-in-out infinite alternate;
}

.label-icon {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #44e2fe;
  box-shadow: 0 0 8px rgba(68, 226, 254, 0.6);
  animation: icon-pulse 2s ease-in-out infinite;
}

.label-text {
  font-size: 12px;
  color: #44e2fe;
  font-weight: 500;
  text-shadow: 0 0 4px rgba(68, 226, 254, 0.5);
}

// 动画关键帧
@keyframes corner-glow {
  0% {
    border-color: rgba(68, 226, 254, 0.5);
    box-shadow: 0 0 5px rgba(68, 226, 254, 0.3);
  }
  100% {
    border-color: rgba(68, 226, 254, 1);
    box-shadow: 0 0 15px rgba(68, 226, 254, 0.6);
  }
}

@keyframes scan-horizontal {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes scan-vertical {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateY(100%);
    opacity: 0;
  }
}

@keyframes glow-pulse {
  0% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

@keyframes label-glow {
  0% {
    border-color: rgba(68, 226, 254, 0.3);
    box-shadow: 0 0 8px rgba(68, 226, 254, 0.2);
  }
  100% {
    border-color: rgba(68, 226, 254, 0.6);
    box-shadow: 0 0 16px rgba(68, 226, 254, 0.4);
  }
}

@keyframes icon-pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 8px rgba(68, 226, 254, 0.6);
  }
  50% {
    transform: scale(1.2);
    box-shadow: 0 0 12px rgba(68, 226, 254, 0.8);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 8px rgba(68, 226, 254, 0.6);
  }
}

// 深度选择器样式优化 - 确保轮播内容正常显示
::v-deep .v-carousel {
  width: 100% !important;
  height: 100% !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

::v-deep .v-window {
  width: 100% !important;
  height: 100% !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

::v-deep .v-window__container {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

::v-deep .v-window-item {
  width: 100% !important;
  height: 100% !important;
  margin: 0 !important;
  border-radius: 12px;
  overflow: hidden;
  // 移除强制显示设置，让轮播组件控制显示状态
}

::v-deep .v-image {
  width: 100% !important;
  height: 100% !important;
  border-radius: 12px;
  // 让轮播组件控制图片的显示状态
}

::v-deep .v-image__image--cover {
  background-size: cover !important;
  background-position: center !important;
  border-radius: 12px;
  transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: scale(1.05);
  }
}

::v-deep .v-responsive {
  width: 100% !important;
  height: 100% !important;
  // 让轮播组件控制响应式容器的显示状态
}

::v-deep .v-responsive__sizer {
  padding-bottom: 0 !important;
}

// 精确隐藏轮播控制元素，不影响内容显示
::v-deep .v-window__prev,
::v-deep .v-window__next {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
}

// 隐藏轮播控制区域（指示器容器）
::v-deep .v-carousel__controls {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
}

// 隐藏指示器按钮组
::v-deep .v-carousel__controls .v-item-group,
::v-deep .v-carousel__controls .v-btn {
  display: none !important;
}

// 轮播过渡效果
::v-deep .v-window__container {
  transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

// // 确保轮播项正确切换 - 只有激活的项显示
// ::v-deep .v-window-item:not(.v-window-item--active) {
//   display: none !important;
// }

::v-deep .v-window-item.v-window-item--active {
  display: block !important;
  position: relative;
  width: 100%;
  height: 100%;
}

// 响应式适配
@media (max-width: 1200px) {
  .enterprise-showcase-container {
    width: 48%;
    padding: 12px;
  }

  .corner-decoration {
    width: 16px;
    height: 16px;
    border-radius: 3px; // 中等屏幕的圆角弧度
  }

  .custom-indicators {
    gap: 10px;
  }

  .indicator-dot {
    width: 10px;
    height: 10px;
  }
}

@media (max-width: 768px) {
  .enterprise-showcase-container {
    width: 50%;
    padding: 8px;
  }

  .corner-decoration {
    width: 12px;
    height: 12px;
    border-radius: 2px; // 小屏幕的圆角弧度
  }

  .enterprise-label {
    padding: 4px 8px;
    font-size: 10px;
  }

  .label-icon {
    width: 6px;
    height: 6px;
  }
}
</style>
