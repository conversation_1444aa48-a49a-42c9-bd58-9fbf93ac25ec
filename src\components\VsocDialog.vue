+
<!-- 适用弹框交互 -->
<!--
    1. 默认插槽在滚动区域内
    2. 底部区域可以用插槽代替默认按钮
    3. 点击确认按钮会有默认loading效果,但是会抛出一个回调函数,执行回调函数可以取消loading,以及关闭弹框
 -->
<template>
  <v-dialog
    :content-class="customClass"
    :value="value"
    :persistent="hideFooter"
    :width="dialogWidth"
    :retain-focus="false"
    @input="$emit('input', false)"
  >
    <v-card class="px-0" :class="dense ? 'py-4' : 'py-6'">
      <div
        class="vsoc-dialog__title d-flex justify-space-between align-center"
        :class="dense ? 'px-4 pb-4' : 'px-6 pb-6'"
      >
        <template v-if="title && !$slots.title">
          <div v-show-tips class="font-weight-semibold text-h5 w-50">
            {{ title }}
          </div>
        </template>
        <slot name="title"></slot>
        <!-- <div>
          <slot name="right"> </slot>
        </div> -->
      </div>
      <div
        class="vsoc-dialog__content overflow-y"
        :class="dense ? 'px-4 py-4' : 'px-6 py-6'"
        :style="contentStyle"
      >
        <slot></slot>
      </div>

      <div
        v-if="!hideFooter"
        class="vsoc-dialog__footer d-flex justify-end"
        :class="dense ? 'pt-4 pr-4' : 'pt-6 pr-6'"
      >
        <template v-if="!$slots.footer">
          <v-btn
            v-if="!hideCancelBtn"
            :height="dense ? '2.3rem' : ''"
            class="font-weight-normal btn-secondary btn-outline-secondary px-6 py-2"
            depressed
            outlined
            @click="$_cancel"
          >
            {{ cancelBtnText || $t('action.cancel') }}
          </v-btn>
          <v-btn
            v-if="!hideConfirmBtn"
            :height="dense ? '2.3rem' : ''"
            :loading="confirmLoading"
            class="ml-4 px-6 py-2 btn-primary"
            color="primary"
            :disabled="confirmDisabled"
            @click="$_confirm"
          >
            {{ confirmBtnText || $t('action.confirm') }}
          </v-btn>
        </template>
        <slot name="footer"></slot>
      </div>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'VsocDialog',
  props: {
    // 是否显示v-model绑定
    value: Boolean,

    // 弹框标题
    title: String,

    // dialog宽度
    width: {
      type: [String, Number],
      default: '60%',
    },

    // 隐藏确认按钮
    hideConfirmBtn: {
      type: Boolean,
      default: false,
    },

    // 隐藏取消按钮
    hideCancelBtn: {
      type: Boolean,
      default: false,
    },

    // 隐藏底部按钮
    hideFooter: {
      type: Boolean,
      default: false,
    },

    // 确认按钮文字
    confirmBtnText: {
      type: String,
      // default: '确定',
    },

    // 取消按钮文字
    cancelBtnText: {
      type: String,
      // default: '取消',
    },

    // dialog滚动区域高度
    height: {
      type: [Number, String],
      default: 'auto',
    },

    // dialog滚动区域最大高度
    maxHeight: {
      type: [Number, String],
      default: 380,
    },
    dense: {
      type: Boolean,
      default: false,
    },
    customClass: String,
    confirmDisabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      confirmLoading: false,
    }
  },
  computed: {
    contentStyle() {
      return {
        height:
          typeof this.height === 'string' ? this.height : `${this.height}px`,
        maxHeight:
          typeof this.maxHeight === 'string'
            ? this.maxHeight
            : `${this.maxHeight}px`,
      }
    },
    dialogWidth() {
      return typeof this.width === 'string' ? this.width : `${this.width}px`
    },
  },
  created() {},
  methods: {
    $_cancel() {
      this.$emit('input', false)
      this.$emit('click:cancel')
    },

    $_confirm() {
      this.setLoading(true)
      this.$emit('click:confirm', (loading = false, dialog = false) => {
        this.setLoading(loading)
        this.$emit('input', dialog)
      })
    },

    setLoading(bool) {
      this.confirmLoading = bool
    },
  },
}
</script>

<style lang="scss" scoped>
.vsoc-dialog {
  &__footer {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
  }

  &__title {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  }
}
.email-dialog {
  .vsoc-dialog__title {
    border-bottom: 0px solid rgba(0, 0, 0, 0.05) !important;
  }
  .vsoc-dialog__content {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
}
</style>
