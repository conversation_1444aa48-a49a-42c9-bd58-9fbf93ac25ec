---
description: 项目使用的技术栈
globs: 
alwaysApply: false
---

#项目使用的技术栈-技术架构
- 项目基于vue2.6.14版本开发
- 使用Vuetify2.6.7组件库, 如果组件库时，
- CCS默认使用vuetify的样式规范；
- 使用Javascript语言
- 项目包使用yarn管理 
- 项目使用vue-cli-service打包编译


#代码规范
- 目录为全小写字母，多个字母用-分割；eg: asset-info
- vue文件采用驼峰命名规范，并且首字母大写；eg:AssertProfile
- js文件采用驼峰命名规范，并且首字母小写; eg:appConfigStoreModule.js
- css文件 采用全小写字母，多个字母用-分割；eg: knowledge-base.scss
- 公共组件目录统一命名为components，无论是全局公共组件目录 还是业务内模块组件
- 所有常量定义 放在util/constant.js中
- 枚举值定义放在util/enum.js中
- 全局filter放在util/filter.js中
- 公共函数放在util/utils.js中
- 方法与函数都需要增加备注与描述
- 所有组件 方法 函数 都需要增加备注与描述

