/* 改变主题色变量 */
$--color-primary: $primary;

// primary: '#2940CB',
// accent: '#2A3867',
// secondary: '#038cd6',

/* 改变 icon 字体路径变量，必需 */
$--font-path: '~element-ui/lib/theme-chalk/fonts';

// @import '~element-ui/packages/theme-chalk/src/index';

$--color-success: $success !default;
/// color|1|Functional Color|1
$--color-warning: $warning !default;
/// color|1|Functional Color|1
$--color-danger: $danger !default;
/// color|1|Functional Color|1
$--color-info: $info !default;

/// color|1|Font Color|2
$--color-text-primary: $primary !default;
/// color|1|Font Color|2
$--color-text-regular: var(--v-accent-base) !default;
/// color|1|Font Color|2
$--color-text-secondary: $secondary !default;
/// color|1|Font Color|2
$--color-text-placeholder: #c0c4cc !default;
/// color|1|Border Color|3
$--border-color-base: var(--v-color-base) !default;
/// color|1|Border Color|3
$--border-color-light: #e4e7ed !default;
/// color|1|Border Color|3
$--border-color-lighter: #ebeef5 !default;
/// color|1|Border Color|3
$--border-color-extra-light: #f2f6fc !default;

// Background
/// color|1|Background Color|4
$--background-color-base: #f5f7fa !default;
