const organization = {
  currentTitle: 'Organization',
  headers: {
    company: 'Company',
    department: 'Department',
    code: 'Code',
    name: 'Name',
    parent: 'Higher',
    manager: 'Manager',
    DingTalkId: 'DingTalk Id',
  },
  btn: {
    add: 'Create peer company',
    add1: 'Create sub company',
    add2: 'Create sub department',
    add3: 'Create peer department',
  },
  swal: {
    del: {
      title: 'Delete',
      text: 'Are you sure to delete the node?',
    },
  },
  hint: {
    tip: 'Please input the name or code',
    addTip: 'Add Organization',
    noTip: 'No company or department',
    showTip: 'Show disabled company or departments',
    selectTip: 'Please select the organization to edit',
    selectTip1: 'No the organization selected',
    delTip:
      'There are sub departments under the current department, operation not allowed',
    delTip1:
      'There are companies or departments under the current company, operation is not allowed',
    delTip2:
      'Department deleted successfully! If there are members under the department, please readjust the department',
  },
}

export default organization
