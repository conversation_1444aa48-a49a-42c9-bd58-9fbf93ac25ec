<!-- 分页组件 -->
<template>
  <div
    class="d-flex justify-space-between vsoc-pagination pt-1 w-100 align-center"
  >
    <div class="d-flex align-center justify-start w-30">
      <slot name="prev">
        <!-- 插槽默认内容 -->
      </slot>
    </div>
    <div class="d-flex text-end align-center justify-end w-70">
      <span class="pr-0 mr-8 font-weight-normal text-no-wrap">
        {{ $t('global.pagination.total') }}
        <span class="ml-1">{{ total }}</span>
      </span>

      <div class="d-flex align-center">
        <span class="px-2 text-no-wrap"
          >{{ $t('global.pagination.perPage') }}
        </span>
        <v-select
          :value="size"
          :menu-props="{ top: true, offsetY: true }"
          single-line
          hide-details="auto"
          dense
          :items="sizes"
          outlined
          height="24px"
          @input="$_updateSize($event)"
        >
          <template #append>
            <v-icon class="ma-0 pa-0" size="16">mdi-chevron-down</v-icon>
          </template>
        </v-select>
        <span class="px-2 text-body">{{
          $tc('global.pagination.items', 1)
        }}</span>
      </div>

      <v-pagination
        class="pagination primary--text"
        :value="page"
        :length="length"
        :total-visible="totalVisible"
        @input="$_updatePage($event)"
      ></v-pagination>
      <!-- </div> -->
    </div>
  </div>
</template>

<script>
export default {
  name: 'VsocPagination',
  components: {},
  props: {
    page: Number,
    size: Number,
    sizes: {
      type: Array,
      default() {
        return [10, 20, 50, 100, 200]
      },
    },
    total: Number,
    totalVisible: {
      type: Number,
      default: 6,
    },

    // length: {
    //   type: Number,
    //   require: true,
    // },
  },
  data() {
    return {}
  },

  computed: {
    length() {
      // console.log('total', this.total)
      // console.log('size', this.size)
      // console.log('page', this.page)
      // console.log('totalVisible', this.totalVisible)
      // console.log('length', Math.ceil(this.total / this.size))
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      const num = Math.ceil(this.total / this.size)

      // this.isPaginationShow = true
      // this.$forceUpdate()

      // 防止分页器出现断层，比如1,2,...,3
      // eslint-disable-next-line vue/no-async-in-computed-properties
      // setTimeout(() => {
      //   this.isPaginationShow = true
      //   this.$forceUpdate()
      // }, 200)

      return num
    },
  },
  created() {},
  methods: {
    $_updateSize(e) {
      this.$emit('update:size', e)
      this.$emit('update:page', 1)
      this.$emit('change-size', e)
    },

    $_updatePage(e) {
      this.$emit('update:page', e)
      this.$emit('change-page', e)
    },
  },
}
</script>

<style lang="scss" scoped>
.vsoc-pagination {
  padding-bottom: 5px;
  // background: #eee;

  .v-text-field--enclosed.v-input--dense:not(
      .v-text-field--solo
    ).v-text-field--outlined
    ::v-deep
    .v-input__append-inner {
    margin-top: 5px !important;
    padding-left: 0px !important;
  }
  .v-select ::v-deep.v-input__slot {
    height: 24px;
    min-height: 24px !important;
    max-width: 5.5rem;
    font-size: $font-size-base;
    padding: 0px 10px !important;
    .v-input__icon {
      margin-top: -2px;
      font-size: $font-size-base;
    }
  }
}
::v-deep .v-pagination .v-pagination__item--active {
  // background: $white !important;
  background: inherit !important;
  color: $primary !important;
  font-weight: $font-weight-semibold;
}
</style>
