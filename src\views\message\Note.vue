<template>
  <vsoc-dialog
    v-model="showNote"
    dense
    width="480"
    :title="$t('message.btn.note')"
    @click:confirm="$_confirmEdit"
  >
    <v-form ref="form" v-model="valid" class="px-4 py-2">
      <div class="text-base color-base font-weight-semibold-light mb-2">
        {{ $t('message.note.open') }}
      </div>
      <v-radio-group
        v-model="noteQuery.flag"
        row
        hide-details
        color="primary"
        class="ma-0 pa-0"
      >
        <v-radio
          v-for="item in flagList"
          :key="item.value"
          :value="item.value"
          class="w-40"
        >
          <template #label>
            <span class="text-base color-base">{{ item.text }}</span>
          </template>
        </v-radio>
      </v-radio-group>

      <v-text-field
        v-if="noteQuery.flag === '0'"
        v-model="noteQuery.time"
        :suffix="$t('infer.expression.second')"
        :label="$t('message.note.time')"
        :rules="[
          v => !!v || $t('validation.required', [$t('message.note.time')]),
        ]"
        oninput="value=value.replace(/[^\d]/g,'')"
        class="mt-6"
      ></v-text-field>
    </v-form>
  </vsoc-dialog>
</template>
<script>
import VsocDialog from '@/components/VsocDialog.vue'
import {
  getLocalStorage,
  removeLocalStorage,
  setLocalStorage,
} from '@/util/localStorage'
export default {
  components: {
    VsocDialog,
  },
  computed: {
    flagList() {
      return [
        { text: this.$t('message.note.yes'), value: '0' },
        { text: this.$t('message.note.no'), value: '1' },
      ]
    },
  },
  data() {
    return {
      showNote: false,
      valid: true,
      noteQuery: {
        flag: '0',
        time: '300',
      },
    }
  },
  methods: {
    open() {
      this.showNote = true
      const query = getLocalStorage('noteSetting')
      if (query) {
        let params = JSON.parse(query)
        this.noteQuery.flag = params.flag
        this.noteQuery.time = params.time
      }
    },
    $_confirmEdit(callBack) {
      if (!this.$refs.form.validate()) return callBack(false, true)
      setLocalStorage('noteSetting', JSON.stringify(this.noteQuery))
      // localStorage.setItem('noteSetting', JSON.stringify(this.noteQuery))
      removeLocalStorage('messageList')
      this.$store.dispatch('global/getMessge')
      this.$store.dispatch('global/poll')
      callBack()
    },
  },
}
</script>
