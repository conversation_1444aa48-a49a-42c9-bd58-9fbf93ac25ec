<template>
  <vsoc-drawer
    :title="$t('action.advanced')"
    :width="400"
    :value="value"
    @click:confirm="doQuery"
    @input="close"
    @click:close="close"
    @click:cancel="close"
  >
    <!-- <template #title>
      <v-btn
        class="no-hb ml-4"
        text
        color="#fff"
        @click="clearAdvanceQuery"
      >
        重置
      </v-btn>
    </template> -->
    <template #right-title>
      <v-btn icon class="no-hb ml-4" @click="clearAdvanceQuery">
        <v-icon small>mdi-filter-variant-remove</v-icon>
      </v-btn>
    </template>
    <!-- <div class="text-lg font-weight-semibold mb-4">
      {{ $t('asset.headers.firstRegistrationTime') }}
    </div> -->
    <v-form ref="form" v-model="valid">
      <v-combobox
        v-model="advanceQuery.model"
        dense
        :items="modelItems"
        class="mt-6"
        color="primary"
        :label="$t('asset.headers.model')"
      ></v-combobox>
      <v-text-field
        v-model="advanceQuery.id"
        dense
        color="primary"
        class="mt-6"
        :label="$t('asset.headers.assetId') + '/VIN'"
      ></v-text-field>
      <vsoc-date-range
        v-model="dateRange"
        no-title
        :menu-props="menuProps"
        @input="onChangeDate"
      >
        <template v-slot:text="{ on, attrs }">
          <v-text-field
            clearable
            dense
            class="mt-6"
            readonly
            hide-details
            color="primary"
            :label="$t('asset.headers.firstRegistrationTime')"
            :value="
              RANGE_STR(advanceQuery.startRegisDate, advanceQuery.endRegisDate)
            "
            v-bind="attrs"
            v-on="on"
            @click:clear="onChangeDate({ start: '', end: '' })"
          ></v-text-field>
        </template>
        <!-- <h3 slot="title">
        select a date range
      </h3> -->
      </vsoc-date-range>

      <vsoc-date-range
        v-model="dateRange1"
        no-title
        :menu-props="menuProps"
        @input="onChangeDate1"
        :isAsset="true"
        :presetFn="ASSETPRESETSFN"
      >
        <template v-slot:text="{ on, attrs }">
          <v-text-field
            clearable
            dense
            style="margin-top: 42px"
            readonly
            hide-details
            color="primary"
            :label="$t('asset.headers.lastActiveTime')"
            :value="
              RANGE_STR(
                advanceQuery.startLastReceiveDate,
                advanceQuery.endLastReceiveDate,
              )
            "
            v-bind="attrs"
            v-on="on"
            @click:clear="onChangeDate1({ start: '', end: '' })"
          ></v-text-field>
        </template>
        <!-- <h3 slot="title">
        select a date range
      </h3> -->
      </vsoc-date-range>
      <!-- <vsoc-date-range
      ref="dateInput"
      v-model="advanceQuery.date"
      type="datetimerange"
      :min-scope="0"
      :picker-options="pickerOptions"
      value-format="yyyy-MM-dd HH:mm:ss"
      @change="onChangeDate"
    >
      <v-text-field
        clearable
        outlined
        dense
        class="append-icon-max"
        readonly
        hide-details
        color="primary"
        label="首次登记时间"
        prepend-inner-icon="mdi-calendar-range-outline"
        :value="advanceQuery.date.join(' - ')"
        @click.native="$refs.dateInput.show()"
        @click:clear="advanceQuery.date = [],onChangeDate([])"
      ></v-text-field>
    </vsoc-date-range> -->
      <!-- <div class="text-lg font-weight-semibold my-4">
      {{ $t('asset.headers.assetGroup') }}
    </div> -->
      <v-select
        v-model="advanceQuery.groupType"
        color="primary"
        class="mt-10"
        :items="assetsGroupTypes"
        :label="$t('assetGroup.groupType')"
        dense
        hide-details
        item-text="text"
        item-value="value"
        :menu-props="{ offsetY: true }"
      >
      </v-select>

      <v-select
        v-if="['1', '2'].includes(advanceQuery.groupType)"
        v-model="advanceQuery.groupNameList"
        color="primary"
        class="mt-10 is-required"
        :items="groupList"
        item-text="text"
        item-value="value"
        :label="$t('asset.headers.assetGroup')"
        multiple
        dense
        :rules="[v => !!v.toString().trim() || this.$t('validation.minSelect')]"
        :menu-props="{ offsetY: true }"
      >
        <template v-slot:selection="{ index }">
          <span v-if="index === 0" class="text-caption">
            {{ $t('global.selected') }}{{ advanceQuery.groupNameList.length }}
          </span>
        </template>
        <template v-slot:item="{ item, on, attrs }">
          <v-list-item v-bind="attrs" v-on="on">
            <v-list-item-action>
              <v-checkbox
                class="selectBox"
                @click="activeBox(item, attrs)"
                v-bind="attrs"
                v-on="on"
              />
            </v-list-item-action>
            <v-list-item-content style="margin-top: -2px">
              <v-list-item-title>
                <v-badge
                  v-if="item.assetGroupType === '0'"
                  dot
                  inline
                  offset-x="10"
                  :offset-y="-18"
                  color="success"
                  class="mr-1 mt-0"
                ></v-badge>
                <span style="vertical-align: middle">{{ item.text }}</span>
              </v-list-item-title>
            </v-list-item-content>
          </v-list-item>
        </template>
      </v-select>

      <div
        class="text-content font-weight-medium color-base"
        :class="['1', '2'].includes(advanceQuery.groupType) ? 'mt-2' : 'mt-6'"
      >
        {{ $t('asset.headers.currentSituation') }}
      </div>
      <!-- <div class="d-flex align-center flex-wrap"> -->
      <v-checkbox
        v-for="(item, i) in healthStatusOption"
        :key="i"
        v-model="advanceQuery.healthStatusList"
        color="primary"
        :value="item.value"
        hide-details
        class="mt-0 mr-8 mb-4"
      >
        <template v-slot:label>
          <div class="d-flex align-center text-center">
            <v-icon
              size="1.5rem"
              :color="item.color"
              class="rounded-0 iconfont"
            >
              icon-dunpai
            </v-icon>

            <span class="color-base ml-2">
              {{ item.dictName }}
            </span>
          </div>
        </template>
      </v-checkbox>
    </v-form>
    <!-- </div> -->
    <!-- <div class="text-lg font-weight-semibold mb-4">
      {{ $t('asset.headers.assetId') }}
    </div> -->

    <!-- <div class="text-lg font-weight-semibold mb-4">
      {{ $t('asset.headers.model') }}
    </div> -->

    <!-- <v-text-field
      v-model="advanceQuery.model"
      class="mt-6"
      dense
      color="primary"
      :label="$t('asset.headers.model')"
    ></v-text-field> -->
    <!-- <div class="text-lg font-weight-semibold mb-4">VIN</div> -->
    <!-- <v-text-field
      v-model="advanceQuery.vin"
      class="mt-6"
      dense
      color="primary"
      label="VIN"
    ></v-text-field> -->
    <!-- 20221111:待商议，暂不做查询 -->
    <!-- <div class="text-lg font-weight-semibold mb-4">最后位置</div>
    <v-text-field
      v-model="advanceQuery.location"
      outlined
      dense
      color="primary"
      label="最后位置"
    ></v-text-field> -->
  </vsoc-drawer>
</template>

<script>
import {
  ASSETPRESETSFN,
  RANGE_STR,
} from '@/components/vsoc-date-range/constants'
import VsocDateRange from '@/components/vsoc-date-range/VsocDateRange.vue'
import VsocDrawer from '@/components/VsocDrawer.vue'
import { alertStatusList } from '@/util/enum'

export default {
  name: 'AssetDrawer',
  components: {
    VsocDrawer,
    VsocDateRange,
  },
  props: {
    value: Boolean,
    modelItems: {
      type: Array,
      default: () => [],
    },
    groupList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      valid: true,
      menuProps: { offsetY: true, closeOnContentClick: false },
      advanceQuery: {
        id: '', // 资产编号
        model: '', // 车型
        date: [], // 日期区间段
        healthStatusList: [], // 当前态势
        groupNameList: [], // 资产组
        location: '', // 最后位置
        startRegisDate: '', // 首次登记时间起始时间
        endRegisDate: '',
        startLastReceiveDate: '',
        endLastReceiveDate: '',
        vin: '', // vin
        groupType: '0',
        // tags: [],
        // status: [],
        // vehicleAssetEngine: [],
        // vehicleAssetYear: [],
      },
      alertStatusList,

      // engineTypeList: ['Combustion', 'Electric', 'Hybrid', 'Pluginbybrid', 'Unknown'],
      engineTypeList: ['燃油', '纯电', '混动', '插混', '未知'],
      yearList: ['2022', '2021', '2020', '2019', '2018'],
    }
  },
  computed: {
    assetsGroupTypes() {
      return Object.assign([], this.$store.state.enums.enums.GroupsType)
    },
    alertStatus() {
      return this.$store.state.enums.enums.AlarmStatus
    },

    // 获取当前态势选项
    healthStatusOption() {
      return this.$store.state.enums.enums.HealthStatus
    },
    dateRange: {
      get() {
        return {
          start: this.advanceQuery.startRegisDate,
          end: this.advanceQuery.endRegisDate,
        }
      },
      set() {
        return {
          start: this.advanceQuery.startRegisDate,
          end: this.advanceQuery.endRegisDate,
        }
      },
    },
    dateRange1: {
      get() {
        return {
          start: this.advanceQuery.startLastReceiveDate,
          end: this.advanceQuery.endLastReceiveDate,
        }
      },
      set() {
        return {
          start: this.advanceQuery.startLastReceiveDate,
          end: this.advanceQuery.endLastReceiveDate,
        }
      },
    },
  },
  created() {
    // this.loadAllAutomaker()
  },
  methods: {
    RANGE_STR,
    ASSETPRESETSFN,
    activeBox(item, attrs) {
      this.advanceQuery.groupNameList.push(item.value)
    },
    // 首次登记时间改变
    onChangeDate(range) {
      this.advanceQuery.startRegisDate = range.start
      this.advanceQuery.endRegisDate = range.end
    },
    // 最后接收时间改变
    onChangeDate1(range) {
      this.advanceQuery.startLastReceiveDate = range.start
      this.advanceQuery.endLastReceiveDate = range.end
    },

    // async loadAllAutomaker() {
    //   const data = await this.$store.dispatch('global/loadAllAutomaker')
    //   this.modelItems = data.map(v => v.name)
    // },
    close(bool) {
      if (!bool) {
        this.$emit('input', false)
      }
    },

    setModel(val) {
      this.advanceQuery = val
    },

    closeChip(index) {
      console.log(index)
    },

    clearAdvanceQuery() {
      Object.assign(
        this.$data.advanceQuery,
        this.$options.data.call(this).advanceQuery,
      )

      // this.advanceQuery = {
      //   page: 1,
      //   size: 10,
      //   date: [],
      //   healthStatusList: [],
      //   groupNameList: [],
      //   tags: [],
      //   status: [],
      //   vehicleAssetEngine: [],
      //   vehicleAssetYear: [],
      //   sort: 'updateDate,desc',
      // }
    },

    //  高级查询
    doQuery(callback) {
      const bool = this.$refs.form.validate()
      if (!bool) {
        callback(false, true)
        return
      }
      if (this.advanceQuery.groupType === '0') {
        this.advanceQuery.groupNameList = []
      }
      setTimeout(() => {
        this.$emit('do-query', this.advanceQuery)
        callback()
      }, 180)
    },

    removeAlertStatus(i) {
      this.advanceQuery.status.splice(i, 1)
    },

    removeTag(i) {
      this.advanceQuery.tags.splice(i, 1)
    },
  },
}
</script>

<style lang="scss" scoped>
.selectBox {
  ::v-deep .v-list-item__title > ::v-deep .v-badge {
    margin-top: 0 !important;
  }

  ::v-deep .v-application--is-ltr .v-list-item__action:first-child {
    margin-right: 0 !important;
  }
  ::v-deep .v-input--selection-controls__ripple:before {
    opacity: 0 !important;
  }

  ::v-deep .v-list-item__title > .v-badge {
    margin-top: 0;
  }

  ::v-deep .v-list .v-list-item--active input,
  ::v-deep .v-input--selection-controls__input input {
    display: none;
  }

  ::v-deep .v-input--selection-controls__input {
    display: flex;
    justify-content: center;
  }
}
</style>
