<template>
  <div class="c-box pb-0">
    <div class="c-box-header">{{ $t('screen.cloud.right2') }}</div>

    <vsoc-chart
      echartId="right-cloud-2"
      class="box-chart"
      :option="chartOption"
      @highlight="onHighlight"
    ></vsoc-chart>
  </div>
</template>

<script>
import VsocChart from '@/components/VsocChart.vue'
import { numberToFormat } from '@/util/filters'
import {
  cloudPieColor,
  cloudPieLegendFn,
  cloudPieSeriesFn,
  cloudTooltip,
} from './chart'
export default {
  name: 'RightCloud3',
  components: {
    VsocChart,
  },
  props: {
    list: {
      type: Array,
      default: () => {
        return [{ name: 'events', value: 0 }]
      },
    },
    total: {
      type: [Number, String],
      default: 2756,
    },
    // events: {
    //   type: [Number, String, Object],
    //   default: () => {
    //     return {
    //       num: 0,
    //       unit: 'Events',
    //     }
    //   },
    // },
  },
  computed: {
    chartOption() {
      // const events = {
      //   num: numberToFormat(this.total),
      //   unit: 'Events',
      // }
      const events = {
        ...numberToFormat(this.total, 'Object'),
      }
      const formatter = () => {
        return `\n{value|${events.num}}{unit|${events.unit}}`
      }
      return {
        color: cloudPieColor,
        tooltip: cloudTooltip,
        legend: cloudPieLegendFn(),
        series: cloudPieSeriesFn(this.list, formatter),
      }
    },
  },
  methods: {
    onHighlight(obj, myChart) {
      const option = this.chartOption
      option.tooltip.backgroundColor = obj.color
      myChart.setOption(option)
    },
  },
}
</script>
