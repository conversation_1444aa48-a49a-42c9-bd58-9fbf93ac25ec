import { hexToRgb } from '@/@core/utils'
import { findAllConfigProperty, getEnumList } from '@/api/system/index'
import { i18n } from '@/plugins/i18n'
import {
  activeColor,
  alarmStatusColor,
  alertColor,
  disableColor,
  inactiveColor,
  strategyStatusColor,
  ticketStatusColor,
  userColor,
} from '@/plugins/systemColor'
import store from '@/store'
import { getLocalStorage } from '@/util/localStorage'
import Vue from 'vue'
Vue.prototype.$toItem = (list, value) => {
  return Object.assign([], list).find(v => v.value === value)
}
export default {
  namespaced: true,
  state: {
    enums: '',
    configs: '',
  },
  getters: {
    getAssetActivityLevel(state) {
      const propertyValue =
        state.configs?.['Asset_activity_level']?.['Asset_activity_level']
          .propertyValue
      let values = []
      if (propertyValue) {
        values = propertyValue.split('-')
      } else {
        values = ['7', '15']
      }
      return values
    },

    getAutomakerSecurityLevel(state) {
      const propertyValue =
        state.configs?.['Automaker_Security_Level']?.[
          'Automaker_Security_Level'
        ].propertyValue
      let values = []
      if (propertyValue) {
        values = propertyValue.split('-')
      } else {
        values = ['100', '200', '500']
      }
      return values
    },
    getRecommendedSignalsList(state) {
      const list =
        state.configs?.['Recommended_Signals_List']?.[
          'Recommended_Signals_List'
        ].propertyValue
      if (list) {
        return list.split(',')
      } else {
        return [
          'vehicle_message_senttimestamp',
          'vehicle_message_name',
          'vehicle_motion_speed',
          'vehicle_location_currentlocation_latitude',
          'vehicle_location_currentlocation_longitude',
        ]
      }
    },
    getAddresFlag(state) {
      return state.configs?.['Address_Resolution_Flag']?.[
        'Address_Resolution_Flag'
      ].propertyValue
    },
    getBMapAk(state) {
      return state.configs?.['MapAk']?.['MapAk'].propertyValue
    },
    getDingType(state) {
      return state.configs?.['Group_Notification']?.['Group_Notification']
        .propertyValue
    },
    getMapEnabledStatus(state) {
      if (state.configs?.['MapAk']?.['MapAk']?.propertyValue) {
        return state.configs?.['Map_Enabled_Status']?.['Map_Enabled_Status']
          .propertyValue
      } else {
        return 'false'
      }
    },
    getMapType(state) {
      return state.configs?.['Map_Type']?.['Map_Type'].propertyValue
    },
    // 字典管理
    getCavdVulnerabilityLevel(state) {
      let obj = state.enums['CNNVD Level']
      const colorList = ['#686E7C', ...alertColor]
      for (const index in obj) {
        obj[index].color = colorList[index]
      }
      return Object.assign([], obj).filter(v => v.value != 0)
    },
    getCnnvdVulnerabilityLevel(state) {
      let obj = state.enums['CNNVD Level']
      const colorList = ['#686E7C', ...alertColor]
      for (const index in obj) {
        obj[index].color = colorList[index]
      }
      return Object.assign([], obj)
    },
    getLaboratoryVulnerabilityLevel(state) {
      let obj = state.enums.LaboratoryVulnerabilityLevel
      for (const index in obj) {
        obj[index].color = alertColor[index]
      }
      return Object.assign([], obj)
    },
    getFeatureAutoThreshold(state) {
      return Object.assign([], state.enums.FeatureAutoThreshold).reverse()
    },
    getUserStatus(state) {
      let obj = state.enums.Role
      for (const key in obj) {
        obj[key].key = Number(obj[key].value)
        obj[key].value = obj[key].text
        obj[key].color = userColor[key]
      }
      return Object.assign([], obj)
    },
    getCvss2(state) {
      let list = Object.values(state.enums['CVSS2.X'])
        .reverse()
        .map((item, index) => {
          return {
            ...item,
            color: alertColor[index],
          }
        })
      return list
    },
    getCvss3(state) {
      let list = Object.values(state.enums['CVSS3.X'])
        .reverse()
        .map((item, index) => {
          return {
            ...item,
            color: alertColor[index],
          }
        })
      return list
    },
    getPollingInterval(state) {
      return name => {
        let list = Object.values(state.enums['Polling Interval'])
        let current = list.find(v => v.text === name)
        return current?.value
      }
    },
    getMessageType(state) {
      let arr = []
      if (i18n.locale === 'en') {
        arr = ['Notice', 'Ticket', 'Alert']
      } else {
        arr = ['公告', '工单', '告警']
      }
      const reslut = Object.assign([], state.enums.MessageType).map(
        (item, index) => {
          return {
            ...item,
            text2: arr[index],
          }
        },
      )
      return reslut
    },
    getOperateStatus(state) {
      if (!state.enums) {
        return []
      }
      let obj = state.enums.OperateStatus
      const extraList = [
        {
          icon: 'mdi-check-circle',
          color: activeColor,
        },
        {
          icon: 'mdi-close-circle',
          color: inactiveColor,
        },
      ]
      Object.values(obj).forEach((item, index) => {
        Object.assign(item, extraList[index])
      })
      return obj
    },
    getEncrypt(state) {
      return Object.assign([], state.enums.Encrypt).reverse()
    },
    getActiveStatus(state) {
      return Object.assign([], state.enums.ActiveStatus).reverse()
    },
    getAlarmLevel(state) {
      return Object.assign([], state.enums.AlarmLevel)
    },
    getAlertStatus(state) {
      return Object.assign([], state.enums.AlarmStatus)
    },
    getAlertStatusWithTitle(state) {
      let list = Object.assign([], state.enums.AlarmStatus)
      list.unshift({
        text: i18n.t('alert.headers.unhandledAlertStatus'),
        value: '-1',
        disabled: true,
      })
      list.splice(3, 0, {
        text: i18n.t('alert.headers.handledAlertStatus'),
        value: '-2',
        disabled: true,
      })
      return list
    },
    getAlertType(state) {
      let typeList = []
      for (let i in state.enums['Alert Type']) {
        let item = state.enums['Alert Type'][i]
        typeList.push(item)
      }
      return typeList
    },
    getAlertTypeObj(state) {
      let obj = {}
      for (let i in state.enums['Alert Type']) {
        let item = state.enums['Alert Type'][i]
        obj[item.value] = {
          text: item.text,
        }
      }
      return obj
    },
    getDisposeType(state) {
      return Object.assign([], state.enums.DisposeType)
    },
    getDataSources(state) {
      return Object.assign([], state.enums.DataSources)
    },
    getSoftwareTypes(state) {
      return Object.assign([], state.enums.SoftwareType)
    },
    getVulnerabilityLevel(state) {
      return Object.assign([], state.enums.VulnerabilityLevel)
    },
    getVulnerabilitySource(state) {
      return Object.assign([], state.enums.VulnerabilityDataSource)
    },
    getSynchronizationStatus(state) {
      return Object.assign([], state.enums.SynchronizationStatus)
    },
    getReadStatus(state) {
      return Object.assign([], state.enums.ReadStatus)
    },
    getMessageSendStatus(state) {
      return Object.assign([], state.enums['Message Send Status'])
    },
    getReceiverTypeList(state) {
      return Object.assign([], state.enums['RecipientType'])
    },
    getReceiverTimeList(state) {
      return Object.assign([], state.enums['Send Time Type'])
    },
    getClassifyTypeList(state) {
      return Object.assign([], state.enums['ClassifyType'])
    },
    getClassifyStatusList(state) {
      return Object.assign([], state.enums['ClassifyStatus'])
    },
    getAlarmClassifyType(state) {
      return Object.assign([], state.enums['AlarmClassifyType'])
    },
    getResponseEvent(state) {
      return Object.assign([], state.enums['Response Event'])
    },
    getResponseConditionType(state) {
      return Object.assign([], state.enums['Response Condition Type'])
    },
    getResponseConditionField(state) {
      let allField = Object.assign([], state.enums['Response Condition Field'])
      let responseConditions = {
        0: allField.filter(
          v => ['0', '1', '2', '3', '4', '5', '6'].indexOf(v.dictId) !== -1,
        ),
        1: allField.filter(
          v =>
            [
              '0',
              '1',
              '2',
              '3',
              '4',
              '5',
              '6',
              '7',
              '8',
              '9',
              '10',
              '11',
              '12',
              '13',
              '14',
              '15',
              '16',
              '17',
            ].indexOf(v.dictId) !== -1,
        ),
        2: allField.filter(
          v => ['18', '19', '20', '21', '22', '23'].indexOf(v.dictId) !== -1,
        ),
        3: allField.filter(
          v => ['18', '19', '20', '21', '22', '23'].indexOf(v.dictId) !== -1,
        ),
      }
      return responseConditions
    },
    getResponseConditionOperator(state) {
      return Object.assign([], state.enums['Response Condition Operator'])
    },
    getResponseExecuteType(state) {
      return Object.assign([], state.enums['Response Execute Type'])
    },
    getResponseNotificationObject(state) {
      return Object.assign([], state.enums['Response Notification Object'])
    },
    getResponseStatus(state) {
      return Object.assign([], state.enums['Response Status'])
    },
    getReportCycle(state) {
      return Object.assign([], state.enums.ReportCycle)
    },
    getReportEmail(state) {
      return Object.assign([], state.enums['Email Push Status'])
    },
    getTicketImpact(state) {
      return Object.assign([], state.enums['Ticker Impact'])
    },
    getTicketStatus(state) {
      return Object.assign([], state.enums['TicketStatus'])
    },
    getTicketLevel(state) {
      return Object.assign([], state.enums['TicketLevel'])
    },
    getTicketDataSource(state) {
      return Object.assign([], state.enums['TicketSource']).filter(v => v)
    },
    getAccessType(state) {
      const iconList = ['icon-yuanchengfangwen', 'icon-wulifangwen']
      return Object.assign([], state.enums['AccessType']).map((item, index) => {
        return {
          ...item,
          icon: iconList[index],
        }
      })
    },
    getAttackDistance(state) {
      const iconList = ['icon-yuanchenggongji', 'icon-duanchenggongji']
      return Object.assign([], state.enums['AttackDistance']).map(
        (item, index) => {
          return {
            ...item,
            icon: iconList[index],
          }
        },
      )
    },
    getAttackCharacter(state) {
      return Object.assign([], state.enums['AttackCharacter'])
    },
    getChangeType(state) {
      return Object.assign([], state.enums['Call Method Change Type'])
    },
    getCloudMethod(state) {
      return Object.assign([], state.enums['Layout Item Data Call Method'])
    },
    getChartItems(state) {
      return Object.assign([], state.enums['Layout Item Chart Type'])
    },
    getChartBorder(state) {
      return Object.assign([], state.enums['Layout Item Status Is Border'])
    },
    getChartactive(state) {
      return Object.assign([], state.enums['Layout Item Status'])
    },
    getHealthStatus(state) {
      return Object.assign([], state.enums['HealthStatus'])
    },
    //项目
    getProjectClassfy() {
      return [
        {
          value: 'APPLICATION',
          text: i18n.t('analyseTotal.message.component_application'),
        },
        {
          value: 'FRAMEWORK',
          text: i18n.t('analyseTotal.message.component_framework'),
        },
        {
          value: 'LIBRARY',
          text: i18n.t('analyseTotal.message.component_library'),
        },
        {
          value: 'CONTAINER',
          text: i18n.t('analyseTotal.message.component_container'),
        },
        {
          value: 'OPERATING_SYSTEM',
          text: i18n.t('analyseTotal.message.component_operating_system'),
        },
        {
          value: 'DEVICE',
          text: i18n.t('analyseTotal.message.component_device'),
        },
        {
          value: 'FIRMWARE',
          text: i18n.t('analyseTotal.message.component_firmware'),
        },
        { value: 'FILE', text: i18n.t('analyseTotal.message.component_file') },
      ]
    },
    getProjectStatus() {
      return [
        {
          value: true,
          text: i18n.t('analyseTotal.message.active'),
          color: '#21D95D',
        },
        {
          value: false,
          text: i18n.t('analyseTotal.message.inactive'),
          color: '#7A8599',
        },
      ]
    },
    //漏洞
    getSuppressed() {
      return [
        {
          value: 't',
          text: i18n.t('analyseTotal.yes'),
          color: '#21D95D',
        },
        {
          value: 'f',
          text: i18n.t('analyseTotal.no'),
          color: '#7A8599',
        },
      ]
    },
    getVulnerabilitySeverity() {
      return [
        {
          value: null,
          color: '#B4BBCC',
          text: i18n.t('analyseTotal.severity.derive_from_cvss_or_owasp_rr'),
        },
        {
          value: 'CRITICAL',
          color: '#DA1F1F',
          text: i18n.t('analyseTotal.severity.critical'),
        },
        {
          value: 'HIGH',
          color: '#FA9114',
          text: i18n.t('analyseTotal.severity.high'),
        },
        {
          value: 'MEDIUM',
          color: '#F5D018',
          text: i18n.t('analyseTotal.severity.medium'),
        },
        {
          value: 'LOW',
          color: '#12B2A7',
          text: i18n.t('analyseTotal.severity.low'),
        },
        {
          value: 'INFO',
          color: '#563FFA',
          text: i18n.t('analyseTotal.severity.info'),
        },
        {
          value: 'UNASSIGNED',
          color: '#B4BBCC',
          text: i18n.t('analyseTotal.severity.unassigned'),
        },
      ]
    },
    //漏洞分析
    getAnalysisChoices() {
      return [
        { value: 'NOT_SET', text: i18n.t('analyseTotal.message.not_set') },
        {
          value: 'EXPLOITABLE',
          text: i18n.t('analyseTotal.message.exploitable'),
        },
        { value: 'IN_TRIAGE', text: i18n.t('analyseTotal.message.in_triage') },
        { value: 'RESOLVED', text: i18n.t('analyseTotal.message.resolved') },
        {
          value: 'FALSE_POSITIVE',
          text: i18n.t('analyseTotal.message.false_positive'),
        },
        {
          value: 'NOT_AFFECTED',
          text: i18n.t('analyseTotal.message.not_affected'),
        },
      ]
    },
  },
  actions: {
    /*
                async getEnums({ commit }) {
                    try {
                        const resEnums = {}
                        const res = await getEnum()
                        res.data.forEach(item => {
                            if (enums[item.type]) {
                                let obj = {}
                                item.values.forEach(v => {
                                    if (enums[item.type][v]) {
                                        obj[v] = enums[item.type][v]
                                    }
                                })
                                Object.keys(obj).length ? resEnums[item.type] = obj : ''
                            }
                        })
                        commit('setEnums', resEnums)
                    } catch (e) {
                        console.error(`获取枚举值错误：${e}`)
                    }
                }
        */
    async getEnums({ commit }) {
      try {
        const e = await Promise.all([getEnumList()])
        const data = e[0].data
        const enums = {}
        for (let [key, value] of Object.entries(data)) {
          let extraList = {}
          let arr = Object.assign([], value)
          switch (key) {
            case 'DisposeResultStatus':
              extraList = {
                0: {
                  color: activeColor,
                },
                1: {
                  color: inactiveColor,
                },
              }
              break
            case 'ClassifyStatus':
              extraList = {
                0: {
                  color: activeColor,
                },
                1: {
                  color: inactiveColor,
                },
              }
              break
            case 'ActiveStatus':
              extraList = {
                0: {
                  // icon: 'mdi-close-circle',
                  icon: 'mdi-circle-medium',
                  color: inactiveColor,
                },
                1: {
                  // icon: 'mdi-check-circle',
                  icon: 'mdi-circle-medium',
                  color: activeColor,
                },
              }
              break
            case 'Enable State':
              extraList = {
                0: {
                  color: activeColor,
                },
                1: {
                  color: disableColor,
                },
              }
              break

            case 'AlarmLevel':
            case 'TicketLevel':
              extraList = arr.map((t, j) => {
                return {
                  color: alertColor[j],
                  svgUrl: require(`@/assets/images/svg/local-icon-${t.dictId}.svg`),
                }
              })
              break
            case 'AlarmStatus':
              extraList = arr.map((t, j) => {
                return { color: alarmStatusColor[j] }
              })
              break
            case 'Ruleset State':
              extraList = arr.map((t, j) => {
                return { color: strategyStatusColor[j] }
              })
              break
            case 'TicketStatus':
              extraList = arr.map((t, j) => {
                let statusObj = {
                  color: '',
                  background: '',
                }
                if (ticketStatusColor[j]) {
                  statusObj.color = ticketStatusColor[j]
                  let rgbObj = hexToRgb(ticketStatusColor[j])
                  let rgbaStr = `rgba(${rgbObj.r},${rgbObj.g},${rgbObj.b},0.2)`
                  statusObj.background = rgbaStr
                }
                return statusObj
              })
              break
            case 'VulnerabilityLevel':
              extraList = {
                0: {
                  color: alertColor[0],
                },
                1: {
                  color: alertColor[1],
                },
                2: {
                  color: alertColor[2],
                },
                3: {
                  color: alertColor[3],
                },
              }
              break
            case 'OperateTypes':
              let iconList = [
                'mdi-plus',
                'mdi-pencil',
                'mdi-delete',
                'mdi-account-details',
                'mdi-export',
                'mdi-import',
                'mdi-database-export',
                'mdi-magnify',
                'mdi-dots-horizontal',
              ]
              extraList = arr.map((t, j) => {
                return { icon: iconList[j] }
              })
              break
            case 'AssetType':
              extraList = {
                0: {
                  icon: 'icon-Vehicles',
                  key: '0',
                  color: '#214EA8',
                },
                1: {
                  icon: 'icon-CEI',
                  key: '1',
                  color: '#214EA8',
                },
              }
              break
            case 'Encrypt':
              extraList = {
                0: {
                  icon: 'mdi-window-close',
                },
                1: {
                  icon: 'mdi-check',
                },
              }
              break
            case 'FeatureDataType':
              extraList = {
                0: {
                  icon: 'icon-quanju',
                },
                1: {
                  icon: 'icon-shishi',
                },
              }
              break
            case 'FeatureClassify':
              extraList = {
                0: {
                  icon: 'icon-fenlei',
                },
                1: {
                  icon: 'icon-shuzhi',
                },
              }
              break
            case 'DetectorType':
              extraList = {
                0: {
                  icon: 'icon-jingbao',
                },
                1: {
                  icon: 'icon-gaopin',
                },
              }
              break
            case 'HealthStatus':
              let reg = /^(\w+)\s\(([A-Z])\)$/
              extraList = {
                0: {
                  colorCss: 'success',
                  color: '#2EE56A',
                  text: arr[0].dictName.replace(reg, '$2'),
                  text1: arr[0].dictName.replace(reg, '$1'),
                  // text: 'A',
                  // text1: arr[0].dictName,
                },
                1: {
                  colorCss: 'warning',
                  color: '#44E2FE',
                  text: arr[1].dictName.replace(reg, '$2'),
                  text1: arr[1].dictName.replace(reg, '$1'),
                  // text: 'B',
                  // text1: arr[1].dictName,
                },
                2: {
                  colorCss: 'danger',
                  color: '#FF910F',
                  text: arr[2].dictName.replace(reg, '$2'),
                  text1: arr[2].dictName.replace(reg, '$1'),
                  // text: 'C',
                  // text1: arr[2].dictName,
                },
                3: {
                  colorCss: 'error',
                  color: '#DA1F1F',
                  text: arr[3].dictName.replace(reg, '$2'),
                  text1: arr[3].dictName.replace(reg, '$1'),
                  // text: 'D',
                  // text1: arr[3].dictName,
                },
              }
              break
            case 'SignalType':
              extraList = {
                0: {
                  key: arr[0].dictId,
                  value: 'FieldMetadata',
                },
                1: {
                  key: arr[1].dictId,
                  value: 'EventField',
                },
              }
              break
            case 'SignalValueType':
              extraList = {
                0: {
                  text: '字符型(String)',
                  oldText: 'Text',
                  value: 'STRING',
                  key: arr[0].dictId,
                },
                1: {
                  text: '布尔型(Boolean)',
                  oldText: 'Boolean',
                  value: 'BOOLEAN',
                  key: arr[1].dictId,
                },
                2: {
                  text: '小数型(Double)',
                  oldText: 'Numeric',
                  value: 'DOUBLE',
                  key: arr[2].dictId,
                },
                3: {
                  text: '整数型(Long)',
                  oldText: 'Whole number',
                  value: 'LONG',
                  key: arr[3].dictId,
                },
                4: {
                  text: '时间戳(Timestamp)',
                  oldText: 'Timestamp',
                  value: 'TIMESTAMP',
                  key: arr[4].dictId,
                },
                5: {
                  text: '字典型(Dictionary)',
                  oldText: 'Structured',
                  value: 'DICTIONARY',
                  key: arr[5].dictId,
                },
                6: {
                  text: '对象数组(Object array)',
                  oldText: 'Structured list',
                  value: 'OBJECT_ARRAY',
                  key: arr[6].dictId,
                },
                7: {
                  text: '数组型(Array)',
                  oldText: 'Array',
                  value: 'ARRAY',
                  key: arr[7].dictId,
                },
                8: {
                  text: '柱状图(Histogram)',
                  oldText: 'Histogram',
                  value: 'Histogram',
                  key: arr[8].dictId,
                },
              }
              break
            case 'DetectorExtent':
              extraList = {
                0: {
                  icon: 'icon-danzichan',
                },
                1: {
                  icon: 'icon-duozichan',
                },
              }
              break
            case 'Layout Item Chart Type':
              // 定制化图形*1
              // 定制化图形*2
              // 定制化图形*3
              // 定制化图形*4
              // 条形图*1
              // 条形图*2
              // 折线图-基础面积图
              // 折线图-基础平滑折线图
              // 饼图-空心环形图*1
              // 饼图-空心环形图*2
              // 定制化中国地图Map
              // 定制化态势
              let iconList1 = [
                'icon-dingzhihuatuxing1',
                'icon-dingzhihuatuxing2',
                'icon-dingzhihuatuxing3',
                'icon-dingzhihuatuxing4',
                'icon-tiaoxingtu1',
                'icon-tiaoxingtu2',
                'icon-zhexianmianjitu',
                'icon-pinghuaquxiantu',
                'icon-huanxingtu1',
                'icon-huanxingtu2',
                'icon-dingzhihuaditu',
                'icon-dingzhihuataishi',
              ]
              extraList = arr.map((t, j) => {
                return { ...t, icon: iconList1[j] }
              })
              break
            default:
              break
          }
          // if (key === 'ActiveStatus') {
          //   extraList = {
          //     0: {
          //       icon: 'mdi-close-circle',
          //       color: inactiveColor,
          //     },
          //     1: {
          //       icon: 'mdi-check-circle',
          //       color: activeColor,
          //     },
          //   }
          // } else if (key === 'AlarmLevel' || key === 'TicketLevel') {
          //   extraList = arr.map((t, j) => {
          //     return {
          //       color: alertColor[j],
          //       svgUrl: require(`@/assets/images/svg/local-icon-${alertColor[
          //         j
          //       ].slice(1)}.svg`),
          //     }
          //   })
          // } else if (key === 'AlarmStatus') {
          //   extraList = arr.map((t, j) => {
          //     return { color: alarmStatusColor[j] }
          //   })
          // } else if (key === 'TicketStatus') {
          //   let rgbObj = hexToRgb(ticketStatusColor)
          //   let rgbaStr = `rgba(${rgbObj.r},${rgbObj.g},${rgbObj.b},0.2)`
          //   extraList = arr.map((t, j) => {
          //     return { color: ticketStatusColor[j], background: rgbaStr }
          //   })
          // } else if (key === 'VulnerabilityLevel') {
          //   extraList = {
          //     0: {
          //       color: alertColor[0],
          //     },
          //     1: {
          //       color: alertColor[1],
          //     },
          //     2: {
          //       color: alertColor[2],
          //     },
          //     3: {
          //       color: alertColor[3],
          //     },
          //   }
          // } else if (key === 'OperateTypes') {
          //   let iconList = [
          //     'mdi-plus',
          //     'mdi-pencil',
          //     'mdi-delete',
          //     'mdi-account-details',
          //     'mdi-export',
          //     'mdi-import',
          //     'mdi-database-export',
          //     'mdi-magnify',
          //     'mdi-dots-horizontal',
          //   ]
          //   extraList = arr.map((t, j) => {
          //     return { icon: iconList[j] }
          //   })
          // } else if (key === 'AssetType') {
          //   extraList = {
          //     0: {
          //       icon: 'mdi-car',
          //       key: '0',
          //       color: '#16B1FF',
          //     },
          //     1: {
          //       icon: ' mdi-cellphone-link',
          //       key: '1',
          //       color: '#3caea3',
          //     },
          //   }
          // } else if (key === 'Encrypt') {
          //   extraList = {
          //     0: {
          //       icon: 'mdi-window-close',
          //     },
          //     1: {
          //       icon: 'mdi-check',
          //     },
          //   }
          // } else if (key === 'DetectorType') {
          //   extraList = {
          //     0: {
          //       icon: 'mdi-flash-outline',
          //     },
          //     1: {
          //       icon: 'mdi-apache-kafka',
          //     },
          //   }
          // } else if (key === 'HealthStatus') {
          // }

          Object.values(value).forEach((v, i) => {
            let obj = {
              text: v.dictName,
              value: v.dictId,
            }
            Object.assign(v, obj, extraList[i])
          })
          enums[key] = value
        }
        // 注意：以上为旧数据的处理方式，新数据，如需加字段请参考OperateStatus的使用
        commit('setEnums', enums)
      } catch (e) {
        console.error(`获取枚举值错误：${e}`)
      }
    },
    async getAllConfig({ commit }) {
      try {
        const e = await Promise.all([findAllConfigProperty()])
        const configList = e[0].data
        const config = {}
        for (let [key, value] of Object.entries(configList)) {
          config[key] = value
        }

        if (config?.['Theme_Color']?.['Theme_Color']?.propertyValue) {
          let themeColor = JSON.parse(
            config['Theme_Color']['Theme_Color'].propertyValue,
          )
          let localMode =
            getLocalStorage('GLOBAL_THEME_MODE') ||
            (themeColor && themeColor.Theming)
          if (['auto', 'light', 'dark', 'semi-dark'].includes(localMode)) {
            store.commit('appConfig/UPDATE_GLOBAL_THEME_MODE', localMode)
          } else {
            store.commit('appConfig/UPDATE_GLOBAL_THEME_MODE', 'auto')
          }

          let localThemeColor =
            getLocalStorage('UPDATE_APP_PRIMARY') ||
            (themeColor && themeColor.PrimaryColor)
          if (['default', 'lotus'].includes(localThemeColor)) {
            store.commit('appConfig/UPDATE_APP_PRIMARY', localThemeColor)
          } else {
            store.commit('appConfig/UPDATE_APP_PRIMARY', 'default')
          }
        } else {
          store.commit('appConfig/UPDATE_GLOBAL_THEME_MODE', 'auto')
          store.commit('appConfig/UPDATE_APP_PRIMARY', 'default')
        }

        if (
          config?.['Theme_Configuration']?.['Theme_Configuration']
            ?.propertyValue
        ) {
          let themeFlag =
            config?.['Theme_Configuration']?.['Theme_Configuration']
              ?.propertyValue || 'false'
          store.commit('appConfig/UPDATE_THEMES_FLAG', themeFlag)
        }
        if (config?.['Timezone_Switch']?.['Timezone_Switch']?.propertyValue) {
          let timezoneSwitch =
            config?.['Timezone_Switch']?.['Timezone_Switch']?.propertyValue ||
            'false'
          store.commit(
            'appConfig/UPDATE_ISTHEMEANDTIMEZONE',
            timezoneSwitch === 'true' ? true : false,
          )
        }
        commit('setConfig', config)
      } catch (e) {
        console.error(`获取参数值错误：${e}`)
      }
    },
  },
  mutations: {
    setEnums(state, val) {
      // let assetTypeEnum = Object.assign([], val.AssetType)
      let detectorExtentEum = Object.assign([], val.DetectorExtent)
      let detectorTypeEum = Object.assign([], val.DetectorType)
      // val.AssetType = assetTypeEnum.filter(v => v.value === '0')
      let { level } = JSON.parse(getLocalStorage('userInfo'))
      if (level == '0') {
        // val.AssetType = assetTypeEnum.slice(0, 1)
        val.DetectorExtent = detectorExtentEum.filter(v => v.value === '0')
        val.DetectorType = detectorTypeEum.filter(v => v.value === '0')
      }
      state.enums = val
    },
    clearEnums(state) {
      state.enums = ''
    },
    clearConfigs(state) {
      state.configs = ''
    },
    setConfig(state, val) {
      state.configs = val
    },
  },
}
