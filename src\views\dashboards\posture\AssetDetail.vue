<template>
  <v-card class="h-full d-flex flex-column">
    <v-card-title class="text-xxl font-weight-semibold pb-0">
      {{ $t('posture.newEvents') }}
    </v-card-title>
    <v-card-text class="flex-1 text-base pb-1">
      <v-row class="h-100 ma-0">
        <v-col
          v-for="(item, index) in pendingEvent"
          :key="index"
          class="h-100 d-flex flex-column justify-center align-center"
          :style="{ color: item.color }"
        >
          <div class="text-base" @click="goAlert(item)">
            {{ item.addNum }}
            <span class="ml-1">{{ $t('action.add') }}</span>
          </div>
          <v-progress-circular
            :size="125 | getRoundSize"
            value="100"
            :color="item.color"
            width="6"
            class="my-4 my-xl-6"
            @click="goAllAlert(item)"
          >
            <span class="text-n2xl text--primary">
              {{ item.value }}
            </span>
            <span v-if="item.unit" class="text--primary mt-auto text-ml pl-1">{{
              item.unit
            }}</span>
          </v-progress-circular>
          <div>
            <v-icon
              :color="item.color"
              class="iconfont icon-gaojingjibiebiaozhi"
            ></v-icon>
            <span class="ml-2 text-ml">{{ item.name }}</span>
          </div>
        </v-col>
      </v-row>
    </v-card-text>
  </v-card>
</template>

<script>
import { numberToFormat } from '@/util/filters'
export default {
  props: {
    totalEarning: [Array, Object],
    eventList: [Array, Object],
  },
  computed: {
    pendingEvent() {
      // 严重 高 中 低 未分配
      let alarmLevelEnum = Object.assign(
        [],
        this.$store.state.enums.enums.AlarmLevel,
      )

      let currentList = alarmLevelEnum.map(item => {
        let currentItem = this.eventList.find(v => v.alarmLevel == item.value)
        // currentItem 可能不存在
        let obj = currentItem && numberToFormat(currentItem.count, 'Object')
        return {
          ...currentItem,
          name: item.text,
          color: item.color,
          value: obj?.num || 0,
          unit: obj?.unit,
        }
      })

      return currentList
    },
  },
  methods: {
    goAlert(data) {
      this.$router.push({
        path: '/alerts',
        query: {
          isQuery: 1,
          statusList: JSON.stringify(['0']),
          alarmLevelList: JSON.stringify([data.alarmLevel]),
        },
      })
    },
    goAllAlert(data) {
      this.$router.push({
        path: '/alerts',
        query: {
          isQuery: 1,
          statusList: JSON.stringify(['0', '1']),
          alarmLevelList: JSON.stringify([data.alarmLevel]),
        },
      })
    },
  },
}
</script>
