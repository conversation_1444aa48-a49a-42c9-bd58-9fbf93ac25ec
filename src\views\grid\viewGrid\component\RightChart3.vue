<template>
  <div class="box">
    <div class="box-header box-position-top">
      {{ $t('screen.ticketTrend')
      }}<span class="box-header-num">{{ total | numberToFormat }}</span>
    </div>
    <template v-if="ticketTrend.length !== 0 && !isEmpty">
      <vsoc-chart
        echartId="order-bar"
        class="box-chart d-flex align-center"
        :option="option"
      ></vsoc-chart>
    </template>
    <template v-else>
      <div
        class="box-chart d-flex align-center color--primary justify-center fs-16"
      >
        None
      </div>
    </template>
  </div>
</template>

<script>
import VsocChart from '@/components/VsocChart.vue'
import { format } from 'date-fns'
import { stackOptionFn } from './chart'
const xList = [
  '03/28',
  '03/29',
  '03/30',
  '03/31',
  '04/01',
  '04/02',
  '04/03',
  '04/04',
  '04/05',
  '04/06',
  '04/07',
  '04/08',
  '04/09',
  '04/10',
  '04/11',
  '04/12',
  '04/13',
  '04/14',
  '04/15',
  '04/16',
  '04/17',
  '04/18',
  '04/19',
  '04/20',
  '04/21',
  '04/22',
  '04/23',
  '04/24',
  '04/25',
  '04/26',
]

const yList = [
  {
    name: 'P0',
    color: '#FF4C51',
    data: [
      [0, 3],
      [17, 143],
      [20, 61],
      [21, 52],
      [23, 5],
      [26, 15],
      [27, 44],
      [28, 31],
      [29, 33],
    ],
  },
  {
    name: 'P1',
    color: '#fb8c00',
    data: [
      [0, 3],
      [17, 143],
      [20, 61],
      [21, 52],
      [23, 5],
      [26, 15],
      [27, 44],
      [28, 31],
      [29, 33],
    ],
  },
  {
    name: 'P2',
    color: '#EEC900',
    data: [
      [17, 3],
      [20, 4],
      [21, 5],
      [27, 8],
    ],
  },
  {
    name: 'P3',
    color: '#3caea3',
    data: [
      [17, 335],
      [20, 2],
      [21, 34],
      [26, 7],
      [27, 13],
      [28, 2],
    ],
  },
]

export default {
  name: 'RightChart3',
  props: {
    xList: Array,
    ticketTrend: Array,
    total: {
      type: [Number, String],
      default: 0,
    },
  },
  components: {
    VsocChart,
  },
  data() {
    return {
      myChart: undefined,
      isEmpty: false,
    }
  },
  computed: {
    option() {
      const ticketLevel = this.$store.state.enums.enums.TicketLevel
      let yList = []
      if (
        !this.ticketTrend ||
        this.ticketTrend.length === 0 ||
        this.ticketTrend.filter(v => v.arrays.length !== 0).length === 0
      ) {
        this.isEmpty = true
        return
      }
      this.isEmpty = false
      this.ticketTrend.forEach((item, index) => {
        let obj = {
          name: ticketLevel[item.level].text.slice(0, 2),
          color: ticketLevel[item.level].color,
          data: item.arrays.map(s => {
            return [
              this.xList.findIndex(v => v == format(new Date(s.time), 'MM/dd')),
              s.number,
            ]
          }),
        }
        yList.push(obj)
      })
      return stackOptionFn(this.xList, yList)
    },
  },
  // mounted() {
  //   this.onDraw()
  // },
  // methods: {
  //   onResize() {
  //     this.$nextTick(() => {
  //       this.option = stackOptionFn(xList, yList)
  //       this.myChart.setOption(this.option)
  //       this.myChart.resize()
  //     })
  //   },
  //   onDraw() {
  //     const ele = document.getElementById('order-bar')
  //     this.myChart = this.$echarts.init(ele, {
  //       height: 'auto',
  //       width: 'auto',
  //     })
  //     this.option = stackOptionFn(xList, yList)
  //     this.myChart.setOption(this.option)
  //   },
  // },
}
</script>
