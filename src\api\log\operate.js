import { request, vsocPath } from '../../util/request'

export const getOperateLog = function (data) {
  return request({
    url: `${vsocPath}/operationLog/logs`,
    method: 'post',
    data,
  })
}

export const getOperateLogDetail = function (data) {
  return request({
    url: `${vsocPath}/operationLog/logDetail`,
    method: 'post',
    data,
  })
}

export const exportOperateLog = function (data) {
  return request({
    url: `${vsocPath}/operationLog/export`,
    method: 'post',
    data,
    loading: true,
    // headers: {
    //   'Content-Type': 'application/x-download',
    // },
    responseType: 'blob',
  })
}
