@import './media';
@import './variables.scss';
@import '~@core/preset/preset/variables.scss';
@import '~@core/preset/preset/mixins.scss';
@import './mixins/background';
@import './mixins/placeholder';
@import './utilities.scss';
@import './misc';
@import './forms';
@import './table';
@import './_logic-engine.scss';
@import './animate.scss';

@import './vendors/_sweetalert2';

@import '~vuetify/src/styles/styles.sass';

// @import './el-select';
// Override template styles or write your custom styles
html {
  -webkit-font-smoothing: auto;
  overflow-y: auto !important;
}
:root {
  --responsive-font-size-primary: max(12px, 1em);
}
* {
  // font-size: min(1em, 12px) !important;
  // font-size: max(1em, 12px) !important;
  font-size: var(--responsive-font-size-primary);
}

.v-application {
  .display-1,
  .display-2,
  .display-3,
  .display-4,
  .headline,
  .title,
  .text-subtitle-1,
  .text-subtitle-2,
  .text-body-1,
  .text-body-2,
  .text-caption,
  .text-overline {
    font-size: var(--responsive-font-size-primary) !important;
  }
}

#nprogress .bar {
  @include bg-gradient-variant($primary-gradient, $primary-gradient-state);
}

#nprogress .peg {
  box-shadow: none;
}

/* chrome & safari 浏览器 IE不支持修改*/
/*滚动条整体部分,必须要设置*/
/*滚动条的轨道*/
// ::-webkit-scrollbar {
//   width: 8px; /*no*/
//   height: 8px; /*no*/
// }
// ::-webkit-scrollbar-track {
//   background: transparent;
//   border-radius: 2px;
// }
// /*滚动条的滑块按钮*/
// ::-webkit-scrollbar-thumb {
//   background: rgba(191, 191, 191, 0.5);
//   border-radius: 10px;
// }

// ::-webkit-scrollbar-thumb:hover {
//   background: rgba(191, 191, 191, 0.9);
// }

//overflow
.overflow-y {
  overflow-y: auto;
  overflow-x: hidden;
  overscroll-behavior: contain;
}

.overflow-x {
  overflow-x: auto;
  overflow-y: hidden;
  overscroll-behavior: contain;
}

//自定义Table 2022/10/25
.v-data-table {
  &.thead-light .v-data-table-header th {
    // color: #a6aab3 !important;
    // background-color: $white !important;
    height: 36px;
  }

  //Henry @20220512 需要改 thead-dark样式
  &.thead-dark .v-data-table-header th {
    color: $gray-300 !important;
    font-weight: $font-weight-semibold !important;
    background-color: $table-header-dark !important;

    // var(--v-primary-base) !important;
    // $table-head-gradient !important;
    // border-color: $table-head-gradient !important;

    // background-image: linear-gradient(310deg, #141727, #3a416f) !important;
  }

  //Henry @20220520 需要改 thead-dark样式问题
  &.thead-dark .v-data-table-header .v-icon {
    color: $light-gradient !important;
  }
}

.shadow {
  box-shadow: $box-shadow !important;
}

.border-radius-xl {
  border-radius: $border-radius-xl !important;
}

//baidu-map
.bg-white {
  background-color: $white !important;
}
.shadow-lg {
  box-shadow: $box-shadow-lg !important;
}

//baidu-map
.v-application {
  .material-icons-round,
  .v-icon {
    font-family: 'Material Icons Round';
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-smoothing: antialiased;
  }

  .border-bottom-radius-0 {
    border-bottom-left-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
  }

  .border-top-radius-0 {
    border-top-left-radius: 0 !important;
    border-top-right-radius: 0 !important;
  }

  .map-control {
    padding: 2px;
    margin: 16px;
    background-color: $white !important;
    border-radius: 0.4rem;
    border: 2px solid rgba(0, 0, 0, 0.2);

    &:hover {
      background-color: #f4f4f4 !important;
    }
  }

  .map-zoom {
    border: none;
    @extend .map-control;
    padding: 0;
    .v-icon {
      border: 2px solid #ccc;
      border-radius: 0 0.4rem 0.4rem 0;
      padding: 2px;
      &:first-child {
        border-radius: 0.4rem 0 0 0.4rem;
        border-right: none;
      }
      &:hover {
        background-color: #f4f4f4 !important;
      }

      &::after {
        display: none;
      }
    }
    &:hover {
      background-color: $white !important;
    }

    &.vertical {
      .v-icon {
        display: block;
        border-radius: 0 0 0.4rem 0.4rem;
        &:first-child {
          border-right: 2px solid #ccc;
          border-bottom: none;
          border-radius: 0.4rem 0.4rem 0 0;
        }
      }
    }
  }
  // //toolbar 右侧抽屉
  // .cus-toolbar {
  //   .v-toolbar__content {
  //     padding: 0;
  //     height: 3.75rem !important;
  //   }
  // }

  .text-fb {
    cursor: pointer;
    transition: 0.3s;
    &:hover {
      transform: scale(1.02);
      text-shadow: 2px 2px 2px rgba($color: #ccc, $alpha: 0.3);
      background-color: transparent;
    }
  }

  .text-overflow-hide {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-break: break-all;
  }

  .overflow-hide {
    overflow: hidden;
  }
  .inline-block {
    display: inline-block;
  }

  .a-hover {
    cursor: pointer;
    &:hover {
      text-decoration: underline;
    }
  }

  .list-hover {
    cursor: pointer;
    transition: background 0.3s;
    &:hover {
      background-color: rgba(0, 0, 0, 0.03);
    }

    &:active {
      background-color: rgba(0, 0, 0, 0.1);
    }
  }

  .list-hover-1 {
    cursor: pointer;
    transition: background 0.3s;
    width: 180px !important;
    height: 46px !important;
    &:hover {
      color: $primary !important;
      background-color: rgba(1, 76, 241, 0.1) !important;
    }

    &:active {
      color: $primary !important;
      background-color: rgba(1, 76, 241, 0.1) !important;
    }
  }
  .chart-card {
    .v-card {
      .v-card__title {
        line-height: 1;
        padding-top: $chart-card-spacer;
        padding-bottom: $chart-card-spacer;
      }
      .v-card__title ~ .v-card__title {
        padding-top: $chart-card-spacer-content;
      }

      .v-card__subtitle,
      .v-card__text,
      .v-card__actions {
        padding-top: 0;

        @at-root {
          .v-card {
            > .v-card__subtitle:first-child,
            > .v-card__text:first-child,
            > .v-card__actions:first-child {
              padding-top: $chart-card-spacer;
            }
          }
        }

        &:last-child {
          padding-bottom: $chart-card-spacer;
        }
      }

      .v-divider + {
        .v-card__subtitle,
        .v-card__text,
        .v-card__actions {
          padding-top: $chart-card-spacer-content;
        }
      }

      .v-card__actions {
        &.dense {
          padding: 0 calc(#{$chart-card-spacer} - 8px)
            calc(#{$chart-card-spacer} - 8px);
          .v-btn {
            &:not(.v-btn--icon) {
              min-width: 68px;
            }
          }
        }
      }
    }
  }
}

.color-base {
  color: var(--v-color-base);
}
.bg-base {
  background: var(--v-backgroundColor-base);
}
// .white-opacity {
//   color: rgba($primary, $white-opacity) !important;
//   .v-icon {
//     color: rgba($primary, $white-opacity) !important;
//   }
// }

@include theme(v-system-bar) using ($material) {
  color: map-deep-get($material, 'text', 'primary') !important;
  .v-icon {
    // color: map-deep-get($material, 'text', 'primary') !important;
    color: #fff !important;
  }
}

@include theme(white-opacity) using ($material) {
  color: map-deep-get($material, 'text', 'primary') !important;
  .v-icon {
    color: map-deep-get($material, 'text', 'primary') !important;
  }
}

// .theme--light.bg-btn {
//   color: $primary !important;
//   background: $primary-hover-color !important;
// }
// .theme--dark.bg-btn,
.theme--dark.primary-bg {
  color: $primary !important;
  background: $blue-white-color !important;
}

.bg-btn {
  color: $primary !important;
  background: color-mix(
    in srgb,
    var(--v-primary-base) 10%,
    transparent
  ) !important;
}

.theme--light.primary-bg {
  background: $primary !important;
  color: $white !important;
}

@include theme--single(reverse--text) using ($material) {
  color: map-deep-get($material, 'text', 'reverse') !important;
  caret-color: map-deep-get($material, 'text', 'reverse') !important;
}

// 输入框
@include theme(v-text-field) using ($material) {
  &.v-input--is-disabled .v-input__slot::before {
    border-image: none !important;
  }
}
.v-label--is-disabled {
  color: map-get($theme-colors, 'accent') !important;
}

// 虚线
@include theme(divider--dashed) using ($material) {
  border-width: 0.8 !important;
  // border-color: #e6eaf2;
  border-image: repeating-linear-gradient(
      to right,
      map-get($material, 'dividers') 0px,
      map-get($material, 'dividers') 8px,
      transparent 8px,
      transparent 15px
    )
    1 repeat !important;
}
// .divider--dashed {
//   // border: dashed;
//   border-width: thin;
//   // border-color: #e6eaf2;
//   border-image: repeating-linear-gradient(
//       to right,
//       #2b3152 0px,
//       #2b3152 8px,
//       transparent 8px,
//       transparent 15px
//     )
//     1 repeat !important;
// }

.display-none {
  display: none;
}
.disabled-text {
  color: $disable-color !important;
}
.action-btn {
  fill: currentColor;
  color: $action-btn-color;
}
.action-tip {
  color: $action-tip-color;
}

.v-application.theme--light .v-main .v-tabs:not(.v-tabs--vertical) {
  box-shadow: none !important;
}

.bg-gray {
  background: #f5f6fa !important;
  border-color: #f5f6fa !important;
}
.lotus .theme--light.v-chip.chip-box {
  background-color: #f5f6fa !important;
}
.filter-chip.v-chip {
  margin: 4px 0 4px 16px !important;
}
.filter-chip.v-chip .v-chip__content {
  display: block !important;
  line-height: 24px !important;
}
.filter-chip.v-chip .v-chip__content .v-icon.v-chip__close {
  vertical-align: text-top !important;
}

.active-chip.v-chip--active::before,
.active-chip:focus::before {
  background: #fff !important;
}

.active-chip.v-chip--active .v-chip__content,
.active-chip:focus .v-chip__content {
  color: #1f2533 !important;
  z-index: 11 !important;
}
.active-chip.v-chip--clickable:active {
  box-shadow: none !important;
}
.active-chip:hover::before {
  background: color-mix(
    in srgb,
    var(--v-primary-base) 10%,
    transparent
  ) !important;
  opacity: 1 !important;
  border: 1px solid $primary;
  z-index: 11;
}
.active-chip:hover .v-chip__content {
  color: $primary !important;
}
.active-chip:hover .v-chip__content .v-icon {
  color: $primary !important;
}

.blue--text {
  color: $blue-1 !important;
  caret-color: $blue-1 !important;
}

@include theme--single(vsoc-drawer__footer) using ($material) {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  background: map-deep-get($material, 'background');
}

.edit-center-box {
  // color: var(--v-color-base) !important;
  padding-bottom: 42px;
  .row {
    margin: 0;
  }
  .col {
    padding: 0;
  }
  .v-input--radio-group.v-input--selection-controls {
    margin-top: 0 !important;
    padding-top: 0 !important;
  }
  .v-input--radio-group .v-radio {
    margin-right: 0 !important;
  }
  .v-input--radio-group .v-input__slot {
    margin-bottom: 0;
    height: 36px !important;
  }
  .v-input--radio-group .v-label {
    // font-size: 12px !important;
    // color: var(--v-color-base) !important;
  }
  .v-input--radio-group .v-input--selection-controls__input {
    margin-left: -4px !important;
    margin-right: 4px !important;
  }
}
.tip-color:hover {
  color: $primary !important;
}

// 滚动条----start
* {
  @include style-scroll-bar();
}

.scroll-bar-bg,
.app-content-container {
  @include style-scroll-bar('background');
}

.scroll-bar-bg > .v-data-table__wrapper {
  @include style-scroll-bar('background');
}

// .scroll-bar-card > .v-data-table__wrapper {
//   @include style-scroll-bar();
// }

// div {
//   @include style-scroll-bar('background');
// }
// .v-data-table__wrapper {
//   @include style-scroll-bar();
// }

// 滚动条----end

@include theme--child(v-breadcrumbs__item) using ($material) {
  &:not(.v-breadcrumbs__item--disabled) {
    color: map-deep-get($material, 'text', 'primary');
  }
  &--disabled {
    color: map-deep-get($material, 'text', 'secondary');
  }
}

.baidu-map > div {
  border-radius: $card-border-radius !important;
}
.custom-btn .v-input--selection-controls__input .v-icon {
  color: $primary !important;
}
.toggle-btn.v-item-group.v-btn-toggle {
  .v-btn {
    width: 160px !important;
    height: 40px !important;
    opacity: 1 !important;
    border-right: 1px solid $color-dividers--light !important;
    border-top: 1px solid $color-dividers--light !important;
    border-bottom: 1px solid $color-dividers--light !important;
    .v-btn__content {
      font-size: 14px !important;
      color: var(--v-color-base) !important;
    }
  }
  .v-btn:first-child {
    border-left: 1px solid $color-dividers--light !important;
  }
  .v-btn.v-item--active.v-btn--active {
    border: 1px solid $primary !important;
    border-color: $primary !important;
    background: $blue-white-color !important;
    .v-btn__content {
      color: $primary !important;
    }
  }
}
// .toggle-btn.toggle-btn1.v-item-group.v-btn-toggle {
//   .v-btn.v-item--active.v-btn--active {
//     border: 1px solid $primary !important;
//     border-color: $primary !important;
//     background: $primary !important;
//     .v-btn__content {
//       color: #fff !important;
//       transition-property: none !important;
//     }
//   }
// }
.theme--light .toggle-btn .v-btn.v-btn--disabled {
  background: #f5f6fa !important;
  .v-btn__content {
    color: #a1a6b1 !important;
  }
}

.theme--dark .toggle-btn .v-btn.v-btn--disabled {
  background: #312d4b !important;
  .v-btn__content {
    color: #2a3867 !important;
  }
}
.edit-divider {
  &--boxed {
    height: 12px;
    margin: 0 -96px;
  }
  &--full {
    height: 12px;
    margin: 0 -16px;
  }
}

// 普通输入框宽度
.text-width {
  width: 280px !important;
  max-width: 280px !important;
}

.select-auto-width {
  width: 240px !important;
  max-width: 240px !important;
}
// 日期范围宽度
.date-width {
  width: 360px !important;
  max-width: 360px !important;
}

// 查询条件宽度
.select-width {
  width: 140px !important;
}

// 全局按钮样式
.v-btn {
  font-size: 14px;
  line-height: 22px;
  font-weight: 400;
  // min-width: 0 !important;
}

.v-btn:not(.v-btn--round, .v-btn--rounded, [type='submit']) {
  min-width: 0 !important;
}

.v-text-field .v-label {
  font-size: 14px !important;
}
.container-box.swal2-container {
  z-index: 99999;
}

.drawer-2 {
  margin-top: 2px;
}

// ::v-deep .editor-box {
//   .ql-editor {
//     padding: 8px 12px !important;
//     color: var(--v-color-base) !important;
//     font-size: 14px !important;
//   }
//   ::v-deep .ql-container.ql-snow {
//     border-radius: 5px !important;
//   }
//   ::v-deep .ql-editor.ql-blank::before {
//     font-style: normal !important;
//     color: rgba(94, 86, 105, 0.68) !important;
//     font-size: 1rem !important;
//   }
//   ::v-deep .ql-container.ql-snow.ql-disabled {
//     .ql-editor {
//       color: rgba(94, 86, 105, 0.38) !important;
//     }
//   }
// }
.editor-box .ql-editor {
  padding: 8px 12px !important;
  color: var(--v-color-base) !important;
  font-size: 14px !important;
}
.editor-box .ql-editor.ql-blank::before {
  font-style: normal !important;
  color: rgba(94, 86, 105, 0.68) !important;
  font-size: 1rem !important;
}
.theme--light .editor-box .ql-container.ql-snow.ql-disabled .ql-editor {
  color: rgba(94, 86, 105, 0.38) !important;
}
.theme--dark .editor-box .ql-container.ql-snow.ql-disabled .ql-editor {
  color: rgba(231, 227, 252, 0.5) !important;
}
.editor-box .ql-container.ql-snow {
  border-radius: 5px !important;
  border: 1px solid var(--v-driver-base) !important;
}

// .theme--dark .editor-box .ql-container.ql-snow {
//   border-radius: 5px !important;
//   border: 1px solid rgba(231, 227, 252, 0.14) !important;
// }

.v-list-item--link:before {
  background-color: $primary !important;
  bottom: 0;
  content: '';
  left: 0;
  opacity: 0;
  pointer-events: none;
  position: absolute;
  right: 0;
  top: 0;
  transition: $primary-transition;
}

.v-list-item:hover::before {
  opacity: 0.1 !important;
}
.time-box .el-input .el-input__inner {
  border: 0px solid #dcdfe6 !important;
  padding: 0 !important;
  opacity: 0 !important;
}
.time-box .el-input__icon {
  line-height: 48px !important;
}
.time-box .el-input__prefix {
  display: none;
}
.vue-easy-pie-chart .inner-text {
  color: var(--v-color-base) !important;
  font-size: 28px !important;
  line-height: 104px !important;
  font-weight: 500 !important;
}
.vue-easy-pie-chart canvas {
  width: 104px !important;
  height: 104px !important;
}
.dot.vue-easy-pie-chart canvas {
  width: 40px !important;
  height: 40px !important;
}
.dot.vue-easy-pie-chart .inner-text {
  color: var(--v-color-base) !important;
  font-size: 16px !important;
  line-height: 40px !important;
  font-weight: 500 !important;
  text-align: center;
}
.dot.dot1.vue-easy-pie-chart canvas {
  width: 30px !important;
  height: 30px !important;
}
.dot.dot1.vue-easy-pie-chart .inner-text {
  font-size: 14px !important;
  line-height: 30px !important;
}
.vn-chip-box.v-chip--label {
  font-size: 8px !important;
  font-weight: 500 !important;
  height: 14px !important;
  line-height: 14px !important;
  border-radius: 2px !important;
}
// 时间选择器样式
.flatpickr-calendar.arrowTop:before,
.flatpickr-calendar.arrowTop:after {
  display: none !important;
}
.flatpickr-calendar.hasTime .flatpickr-time {
  border: none;
  border-color: transparent !important;
  border-top: none !important;
}
.flatpickr-calendar.open {
  width: 11.6rem !important;
  box-shadow: none !important;
  background-color: #fff !important;
  border: 1px solid rgba(46, 38, 61, 0.12) !important;
}
.flatpickr-time input.flatpickr-hour {
  font-weight: 400 !important;
}
.flatpickr-time .flatpickr-am-pm,
.flatpickr-time .flatpickr-time-separator,
.flatpickr-time input {
  color: #1f2533 !important;
}
.flatpickr-time-separator {
  font-weight: 700 !important;
}
.flatpickr-wrapper {
  position: absolute !important;
  // left: 154px;
  left: 51%;
  max-height: 32px;
  // width: 10.5rem;
  width: 47%;
}
.flat-picker-custom-style {
  width: 100%;
  height: 100%;
  line-height: 20px;
  flex: 1 1 auto;
  padding: 6px 12px;
  // opacity: 0;
  outline: none;
  color: var(--v-color-base);
  font-size: 14px;
}
.flat-picker-open-time {
  // border-color: $primary !important;
  // border-radius: 5px;
  // border: 2px solid $primary;
}

.v-textarea.v-text-field--enclosed .v-text-field__slot {
  margin-right: -10px !important;
}
