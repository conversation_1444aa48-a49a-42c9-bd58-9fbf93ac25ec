/**
 * 车企安全等级计算工具（智能告警模式 V2.1）
 *
 * 🎯 核心功能：
 * - 基于告警数据的智能风险评级算法
 * - 动态阈值计算，根据告警分布智能调整
 * - 避免评分较低但评级很差的不合理情况
 * - 大量告警场景专项优化
 * - 未分配告警宽松处理机制
 * - 智能评分转换，解决评分差异巨大但评级相同的问题
 *
 * 📊 调用方式：
 *
 * 告警数据模式（推荐方式）：
 *    const alertData = { critical: 1, high: 2, medium: 3, low: 5, unassigned: 19 }
 *    const result = getAutomakerSecurityLevel(alertData)
 *    // 智能计算总分并进行动态评级
 *
 * 评分数据模式（兼容方式）：
 *    const result = getAutomakerSecurityLevelFromScore(riskScore, scoreType)
 *    // 智能转换评分为告警数据并进行评级
 *
 * 🧠 智能算法特点（V2.1优化）：
 * - 基于告警严重程度动态调整阈值
 * - 考虑告警分布特征，避免单纯依赖总分
 * - 智能识别告警模式，提供更准确的风险评估
 * - 自适应保护机制，确保评级结果合理
 * - 大量告警场景优化：支持10-1000+告警的智能处理
 * - 未分配告警专项优化：降低权重，宽松阈值，避免过度惩罚
 * - 增强平衡机制：防止低分和高分分到同一评级
 * - 智能评分转换：根据评分类型和范围进行合理的告警数据转换
 *
 * ⚖️ 告警权重（智能权重）：
 * - 严重告警: 10分
 * - 高级告警: 5分
 * - 中级告警: 2分
 * - 未分配告警: 1.5分（进一步降低权重，更宽松处理）
 * - 低级告警: 1分
 *
 * 🔧 动态阈值策略（V2.1增强）：
 * - 有严重告警时：阈值更严格，评级更敏感
 * - 未分配告警主导时：大幅放宽阈值，避免过度惩罚
 * - 大量告警场景：根据数量规模智能调整倍数
 * - 混合告警时：平衡考虑，智能调整，防止评级分布不合理
 * - 评分转换场景：根据评分类型和范围智能调整阈值
 *
 * 📈 告警数量规模支持：
 * - 小规模：≤10个告警
 * - 中规模：11-50个告警
 * - 大规模：51-200个告警
 * - 超大规模：201-500个告警
 * - 极大规模：>500个告警
 */

/**
 * 告警等级权重配置（智能权重标准）
 */
const ALERT_WEIGHTS = {
  critical: 10, // 严重
  high: 5, // 高
  medium: 2, // 中
  low: 1, // 低
  unassigned: 1.5, // 未分配（进一步降低权重，更宽松处理）
}

/**
 * 告警数量分级阈值配置
 */
const ALERT_VOLUME_THRESHOLDS = {
  small: 10, // 小规模：≤10个告警
  medium: 50, // 中规模：11-50个告警
  large: 200, // 大规模：51-200个告警
  massive: 500, // 超大规模：201-500个告警
  extreme: 1000, // 极大规模：>500个告警
}

/**
 * 安全等级定义常量
 */
const SECURITY_LEVELS = {
  Strong: {
    color: '#2EE56A',
    text: '优秀',
    level: 'Strong',
    priority: 1,
    description: '安全状况优秀，风险可控',
  },
  Good: {
    color: '#44E2FE',
    text: '良好',
    level: 'Good',
    priority: 2,
    description: '安全状况良好，存在轻微风险',
  },
  Fair: {
    color: '#FF910F',
    text: '一般',
    level: 'Fair',
    priority: 3,
    description: '安全状况一般，需要关注',
  },
  Poor: {
    color: '#DA1F1F',
    text: '较差',
    level: 'Poor',
    priority: 4,
    description: '安全状况较差，需要立即处理',
  },
}

/**
 * 智能风险评级算法 - 车企安全等级计算工具
 *
 * 🎯 核心特性：
 * - 动态阈值计算，避免评级分布不合理
 * - 智能告警模式识别，提供精准风险评估
 * - 自适应保护机制，确保评级结果的合理性
 * - 多维度评估，防止单一指标误导
 *
 * @param {Object} alertData - 告警数据对象
 * @param {number} alertData.critical - 严重告警数量
 * @param {number} alertData.high - 高级告警数量
 * @param {number} alertData.medium - 中级告警数量
 * @param {number} alertData.low - 低级告警数量
 * @param {number} alertData.unassigned - 未分配告警数量
 * @returns {Object} 评级结果对象
 */
export function getAutomakerSecurityLevel(alertData) {
  // 输入验证
  if (!alertData || typeof alertData !== 'object') {
    console.warn('告警数据无效，返回默认优秀等级')
    return {
      ...SECURITY_LEVELS.Strong,
      score: 0,
      totalAlerts: 0,
      algorithm: 'default',
      thresholds: null,
      alertPattern: 'none',
    }
  }

  // 标准化告警数据
  const alerts = {
    critical: Math.max(0, parseInt(alertData.critical) || 0),
    high: Math.max(0, parseInt(alertData.high) || 0),
    medium: Math.max(0, parseInt(alertData.medium) || 0),
    low: Math.max(0, parseInt(alertData.low) || 0),
    unassigned: Math.max(0, parseInt(alertData.unassigned) || 0),
  }

  // 计算基础指标
  const totalAlerts = Object.values(alerts).reduce(
    (sum, count) => sum + count,
    0,
  )

  // 无告警情况
  if (totalAlerts === 0) {
    return {
      ...SECURITY_LEVELS.Strong,
      score: 0,
      totalAlerts: 0,
      algorithm: 'no-alerts',
      thresholds: null,
      alertPattern: 'none',
    }
  }

  // 计算加权分数
  const weightedScore =
    alerts.critical * ALERT_WEIGHTS.critical +
    alerts.high * ALERT_WEIGHTS.high +
    alerts.medium * ALERT_WEIGHTS.medium +
    alerts.low * ALERT_WEIGHTS.low +
    alerts.unassigned * ALERT_WEIGHTS.unassigned

  // 告警模式识别
  const alertPattern = identifyAlertPattern(alerts)

  // 告警数量规模识别
  const alertVolume = identifyAlertVolume(totalAlerts)

  // 动态阈值计算
  const thresholds = calculateDynamicThresholds(
    alerts,
    alertPattern,
    weightedScore,
  )

  // 智能评级决策
  const securityLevel = determineSecurityLevel(
    weightedScore,
    alerts,
    alertPattern,
    thresholds,
  )

  return {
    ...SECURITY_LEVELS[securityLevel],
    score: weightedScore,
    totalAlerts: totalAlerts,
    algorithm: 'intelligent-dynamic-v2',
    thresholds: thresholds,
    alertPattern: alertPattern,
    alertVolume: alertVolume,
    alertDistribution: alerts,
  }
}

/**
 * 识别告警数量规模
 * @param {number} total - 总告警数量
 * @returns {string} 告警规模类型
 */
function identifyAlertVolume(total) {
  if (total <= ALERT_VOLUME_THRESHOLDS.small) return 'small'
  if (total <= ALERT_VOLUME_THRESHOLDS.medium) return 'medium'
  if (total <= ALERT_VOLUME_THRESHOLDS.large) return 'large'
  if (total <= ALERT_VOLUME_THRESHOLDS.massive) return 'massive'
  return 'extreme'
}

/**
 * 识别告警模式
 * @param {Object} alerts - 告警数据
 * @returns {string} 告警模式类型
 */
function identifyAlertPattern(alerts) {
  const { critical, high, medium, low, unassigned } = alerts
  const total = critical + high + medium + low + unassigned

  // 严重告警主导模式
  if (critical > 0) {
    const criticalRatio = critical / total
    if (criticalRatio >= 0.3) return 'critical-dominant'
    if (criticalRatio >= 0.1) return 'critical-mixed'
    return 'critical-sparse'
  }

  // 高危告警主导模式
  if (high > 0) {
    const highRatio = high / total
    const highCriticalRatio = (high + critical) / total
    if (highRatio >= 0.5) return 'high-dominant'
    if (highCriticalRatio >= 0.3) return 'high-mixed'
    return 'high-sparse'
  }

  // 中等风险模式
  if (medium > 0) {
    const mediumRatio = medium / total
    const highMediumRatio = (high + medium + critical) / total
    if (mediumRatio >= 0.6) return 'medium-dominant'
    if (highMediumRatio >= 0.4) return 'medium-mixed'
    return 'medium-sparse'
  }

  // 未分配告警主导模式（优化）
  if (unassigned > 0) {
    const unassignedRatio = unassigned / total
    // 降低未分配告警主导的阈值，更容易触发宽松处理
    if (unassignedRatio >= 0.6) return 'unassigned-dominant'
    if (unassignedRatio >= 0.3) return 'unassigned-mixed'
    // 新增：未分配告警稀疏模式
    if (unassignedRatio >= 0.1) return 'unassigned-sparse'
  }

  // 低风险模式
  const lowUnassignedRatio = (low + unassigned) / total
  if (lowUnassignedRatio >= 0.8) return 'low-dominant'
  if (lowUnassignedRatio >= 0.5) return 'low-mixed'

  return 'balanced'
}

/**
 * 动态阈值计算 - 核心智能算法（优化版）
 * 根据告警模式、数量规模和分布特征动态调整评级阈值，避免评级分布不合理
 * @param {Object} alerts - 告警数据
 * @param {string} alertPattern - 告警模式
 * @param {number} weightedScore - 加权分数
 * @returns {Object} 动态阈值对象
 */
function calculateDynamicThresholds(alerts, alertPattern, weightedScore) {
  const { critical, high, medium, low, unassigned } = alerts
  const total = critical + high + medium + low + unassigned

  // 识别告警数量规模
  const alertVolume = identifyAlertVolume(total)

  // 基础阈值（保守设置）
  let baseThresholds = {
    strong: 0, // 0分：优秀
    good: 3, // 3分：良好
    fair: 8, // 8分：一般
    poor: 15, // 15分：较差
  }

  // 根据告警模式动态调整阈值
  switch (alertPattern) {
    case 'critical-dominant':
    case 'critical-mixed':
      // 有严重告警时，阈值更严格
      baseThresholds = {
        strong: 0,
        good: 1, // 任何严重告警都不能是优秀
        fair: 5, // 严重告警较多时快速降级
        poor: 10,
      }
      break

    case 'critical-sparse':
      // 少量严重告警，适度严格
      baseThresholds = {
        strong: 0,
        good: 2,
        fair: 6,
        poor: 12,
      }
      break

    case 'high-dominant':
      // 高危告警主导，中等严格度
      baseThresholds = {
        strong: 0,
        good: 4,
        fair: 10,
        poor: 18,
      }
      break

    case 'high-mixed':
    case 'high-sparse':
      // 高危告警较少，适度宽松
      baseThresholds = {
        strong: 0,
        good: 5,
        fair: 12,
        poor: 20,
      }
      break

    case 'medium-dominant':
    case 'medium-mixed':
      // 中等风险主导，平衡设置
      baseThresholds = {
        strong: 0,
        good: 6,
        fair: 15,
        poor: 25,
      }
      break

    case 'unassigned-dominant':
      // 未分配告警主导，最宽松的阈值
      baseThresholds = {
        strong: 0,
        good: 20, // 进一步提高阈值
        fair: 45, // 更宽松的处理
        poor: 80, // 只有在告警数量极多时才判定为较差
      }
      break

    case 'unassigned-mixed':
      // 未分配告警较多，适度宽松
      baseThresholds = {
        strong: 0,
        good: 15,
        fair: 35,
        poor: 60,
      }
      break

    case 'unassigned-sparse':
      // 未分配告警稀疏，轻度宽松
      baseThresholds = {
        strong: 0,
        good: 10,
        fair: 25,
        poor: 45,
      }
      break

    case 'medium-sparse':
    case 'low-dominant':
    case 'low-mixed':
      // 低风险主导，相对宽松
      baseThresholds = {
        strong: 0,
        good: 8,
        fair: 20,
        poor: 35,
      }
      break

    default:
      // 平衡模式，使用基础阈值
      break
  }

  // 智能阈值微调 - 防止评级分布不合理
  const adjustedThresholds = intelligentThresholdAdjustment(
    baseThresholds,
    alerts,
    weightedScore,
    total,
    alertVolume,
    alertPattern,
  )

  return adjustedThresholds
}

/**
 * 获取告警数量规模调整倍数
 * @param {string} alertVolume - 告警数量规模
 * @param {string} alertPattern - 告警模式
 * @returns {number} 调整倍数
 */
function getVolumeMultiplier(alertVolume, alertPattern) {
  // 基础倍数
  let multiplier = 1.0

  // 根据告警数量规模调整
  switch (alertVolume) {
    case 'small':
      multiplier = 1.0
      break
    case 'medium':
      multiplier = 1.2
      break
    case 'large':
      multiplier = 1.5
      break
    case 'massive':
      multiplier = 2.0
      break
    case 'extreme':
      multiplier = 2.5
      break
  }

  // 对未分配告警主导的场景进一步放宽
  if (alertPattern.includes('unassigned')) {
    multiplier *= 1.3
  }

  return multiplier
}

/**
 * 智能阈值微调算法（优化版）
 * 防止低分高分都分到同一评级的问题，增加大量告警场景优化
 * @param {Object} baseThresholds - 基础阈值
 * @param {Object} alerts - 告警数据
 * @param {number} weightedScore - 加权分数
 * @param {number} total - 总告警数
 * @param {string} alertVolume - 告警数量规模
 * @param {string} alertPattern - 告警模式
 * @returns {Object} 调整后的阈值
 */
function intelligentThresholdAdjustment(
  baseThresholds,
  alerts,
  weightedScore,
  total,
  alertVolume,
  alertPattern,
) {
  const { critical, high, medium, low, unassigned } = alerts

  // 复制基础阈值
  const adjusted = { ...baseThresholds }

  // 告警密度因子（告警数量对阈值的影响）
  const densityFactor = Math.min(total / 10, 2) // 最大2倍调整

  // 严重性权重因子
  const severityWeight = (critical * 10 + high * 5) / Math.max(weightedScore, 1)

  // 未分配告警比例因子
  const unassignedRatio = unassigned / Math.max(total, 1)

  // 分布均匀性检查
  const distribution = [critical, high, medium, low, unassigned]
  const nonZeroLevels = distribution.filter(count => count > 0).length
  const isWellDistributed = nonZeroLevels >= 3

  // 大量告警场景优化（新增）
  const volumeMultiplier = getVolumeMultiplier(alertVolume, alertPattern)

  // 动态调整策略
  if (severityWeight > 0.7) {
    // 高严重性告警占比高，阈值更严格
    adjusted.good = Math.max(1, Math.floor(adjusted.good * 0.7))
    adjusted.fair = Math.max(adjusted.good + 1, Math.floor(adjusted.fair * 0.8))
    adjusted.poor = Math.max(adjusted.fair + 1, Math.floor(adjusted.poor * 0.9))
  } else if (unassignedRatio > 0.5) {
    // 未分配告警占主导，大幅放宽阈值
    adjusted.good = Math.floor(adjusted.good * 2.0 * volumeMultiplier)
    adjusted.fair = Math.floor(adjusted.fair * 1.8 * volumeMultiplier)
    adjusted.poor = Math.floor(adjusted.poor * 1.6 * volumeMultiplier)
  } else if (severityWeight < 0.2 && total > 5) {
    // 低严重性但告警数量多，适度放宽
    adjusted.good = Math.floor(adjusted.good * 1.3 * volumeMultiplier)
    adjusted.fair = Math.floor(adjusted.fair * 1.2 * volumeMultiplier)
    adjusted.poor = Math.floor(adjusted.poor * 1.1 * volumeMultiplier)
  } else if (
    alertVolume === 'large' ||
    alertVolume === 'massive' ||
    alertVolume === 'extreme'
  ) {
    // 大量告警场景，整体放宽阈值
    adjusted.good = Math.floor(adjusted.good * volumeMultiplier)
    adjusted.fair = Math.floor(adjusted.fair * volumeMultiplier)
    adjusted.poor = Math.floor(adjusted.poor * volumeMultiplier)
  }

  // 分布均匀性调整
  if (isWellDistributed && total > 3) {
    // 告警分布均匀，使用更细致的分级
    const gap = Math.max(2, Math.floor(weightedScore / 4))
    adjusted.good = Math.max(1, adjusted.good)
    adjusted.fair = adjusted.good + gap
    adjusted.poor = adjusted.fair + gap
  }

  // 密度调整
  if (densityFactor > 1.5) {
    // 告警密度高，整体阈值下调
    adjusted.good = Math.max(
      1,
      Math.floor((adjusted.good / densityFactor) * 1.2),
    )
    adjusted.fair = Math.max(
      adjusted.good + 1,
      Math.floor((adjusted.fair / densityFactor) * 1.1),
    )
    adjusted.poor = Math.max(
      adjusted.fair + 1,
      Math.floor(adjusted.poor / densityFactor),
    )
  }

  // 确保阈值递增且合理
  adjusted.good = Math.max(1, adjusted.good)
  adjusted.fair = Math.max(adjusted.good + 1, adjusted.fair)
  adjusted.poor = Math.max(adjusted.fair + 1, adjusted.poor)

  return adjusted
}

/**
 * 智能评级决策算法（优化版）
 * 综合考虑分数、告警分布、模式等多个维度进行最终评级
 * 增强对未分配告警的宽松处理
 * @param {number} weightedScore - 加权分数
 * @param {Object} alerts - 告警数据
 * @param {string} alertPattern - 告警模式
 * @param {Object} thresholds - 动态阈值
 * @returns {string} 安全等级
 */
function determineSecurityLevel(
  weightedScore,
  alerts,
  alertPattern,
  thresholds,
) {
  const { critical, high, medium, low, unassigned } = alerts
  const total = critical + high + medium + low + unassigned

  // 未分配告警主导场景的特殊处理（修正版）
  if (
    alertPattern.includes('unassigned-dominant') &&
    critical === 0 &&
    high === 0
  ) {
    // 纯未分配告警场景，适度宽松但不过度优化
    const alertVolume = identifyAlertVolume(total)

    // 极大规模未分配告警的特殊处理
    if (alertVolume === 'extreme') {
      // 极大规模：基于告警数量进行评级，但不会是优秀
      if (unassigned >= 800) return 'Poor'
      if (unassigned >= 400) return 'Fair'
      return 'Good' // 最好也只能是良好
    } else if (alertVolume === 'massive') {
      // 超大规模：大幅放宽阈值，但有下限
      if (weightedScore >= thresholds.poor * 4.0) return 'Poor'
      if (weightedScore >= thresholds.fair * 2.5) return 'Fair'
      return 'Good' // 最好也只能是良好
    }

    // 一般未分配告警主导场景：适度宽松，但存在告警不应该是优秀
    if (weightedScore >= thresholds.poor * 1.5) return 'Poor'
    if (weightedScore >= thresholds.fair * 1.2) return 'Fair'
    return 'Good' // 有未分配告警时，最好评级是良好
  }

  // 严重告警一票否决机制
  if (critical > 0) {
    // 多个严重告警直接判定为较差
    if (critical >= 3) return 'Poor'

    // 严重告警配合其他高危告警
    if (critical >= 2 && high > 0) return 'Poor'
    if (critical === 1 && high >= 3) return 'Poor'

    // 单个严重告警的情况
    if (critical === 1) {
      if (weightedScore >= thresholds.poor) return 'Poor'
      if (weightedScore >= thresholds.fair) return 'Fair'
      return 'Fair' // 有严重告警最好也只能是一般
    }
  }

  // 高危告警评估
  if (high > 0) {
    // 大量高危告警
    if (high >= 5) {
      return weightedScore >= thresholds.poor ? 'Poor' : 'Fair'
    }

    // 中等数量高危告警
    if (high >= 3) {
      if (weightedScore >= thresholds.poor) return 'Poor'
      if (weightedScore >= thresholds.fair) return 'Fair'
      return 'Fair'
    }
  }

  // 基于动态阈值的标准评级
  if (weightedScore >= thresholds.poor) {
    return 'Poor'
  } else if (weightedScore >= thresholds.fair) {
    return 'Fair'
  } else if (weightedScore >= thresholds.good) {
    return 'Good'
  } else {
    return 'Strong'
  }
}

/**
 * 辅助函数：获取评级建议和改进方向
 * @param {Object} result - 评级结果
 * @returns {Object} 包含建议的扩展结果
 */
export function getSecurityLevelAdvice(result) {
  if (!result || !result.alertDistribution) {
    return { ...result, advice: '数据不足，无法提供建议' }
  }

  const { critical, high, medium, low, unassigned } = result.alertDistribution
  const advice = []

  // 严重告警建议
  if (critical > 0) {
    advice.push(`🚨 紧急：存在 ${critical} 个严重告警，需要立即处理`)
  }

  // 高危告警建议
  if (high > 0) {
    advice.push(`⚠️ 重要：存在 ${high} 个高危告警，建议优先处理`)
  }

  // 未分配告警建议
  if (unassigned > 0) {
    advice.push(`📋 管理：存在 ${unassigned} 个未分配告警，需要分类处理`)
  }

  // 中低级告警建议
  if (medium > 0 || low > 0) {
    advice.push(`📊 优化：存在 ${medium + low} 个中低级告警，可安排定期处理`)
  }

  // 根据评级给出总体建议
  switch (result.level) {
    case 'Strong':
      advice.unshift('✅ 安全状况优秀，继续保持当前安全措施')
      break
    case 'Good':
      advice.unshift('👍 安全状况良好，建议定期检查和优化')
      break
    case 'Fair':
      advice.unshift('⚡ 安全状况一般，需要加强安全管控')
      break
    case 'Poor':
      advice.unshift('🔥 安全状况较差，需要立即采取行动')
      break
  }

  return {
    ...result,
    advice: advice.join('\n'),
    recommendations: advice,
  }
}

/**
 * 辅助函数：批量评级（用于多个车企的对比分析）
 * @param {Array} alertDataList - 多个告警数据对象的数组
 * @returns {Array} 评级结果数组
 */
export function batchSecurityLevelEvaluation(alertDataList) {
  if (!Array.isArray(alertDataList)) {
    console.warn('输入数据必须是数组格式')
    return []
  }

  return alertDataList.map((alertData, index) => {
    try {
      const result = getAutomakerSecurityLevel(alertData)
      return {
        index,
        ...result,
        success: true,
      }
    } catch (error) {
      console.error(`第 ${index + 1} 个数据评级失败:`, error)
      return {
        index,
        error: error.message,
        success: false,
      }
    }
  })
}

/**
 * 智能评分转换为告警数据（V2.1）
 * @deprecated 建议使用 getAutomakerSecurityLevelUnified 替代直接调用此函数
 * @param {number} riskScore - 风险评分
 * @param {string} scoreType - 评分类型 ('risk'|'line'|'auto')
 * @returns {Object} 告警数据对象
 */
export function convertScoreToAlertData(riskScore, scoreType = 'auto') {
  // 输入验证
  if (!riskScore || riskScore <= 0) {
    return {
      critical: 0,
      high: 0,
      medium: 0,
      low: 0,
      unassigned: 0,
    }
  }

  const score = Math.max(0, parseFloat(riskScore))

  // 根据评分类型和范围进行智能转换
  let alertData = { critical: 0, high: 0, medium: 0, low: 0, unassigned: 0 }

  // 自动识别评分类型
  if (scoreType === 'auto') {
    if (score >= 1000) {
      scoreType = 'line' // 分线评分（通常范围较大）
    } else {
      scoreType = 'risk' // 风险评分（通常范围较小）
    }
  }

  if (scoreType === 'line') {
    // 分线评分转换（范围通常在0-10000+）
    alertData = convertLineScoreToAlerts(score)
  } else {
    // 风险评分转换（范围通常在0-1000）
    alertData = convertRiskScoreToAlerts(score)
  }

  return alertData
}

/**
 * 风险评分转换为告警数据
 * @param {number} score - 风险评分（0-1000范围）
 * @returns {Object} 告警数据
 */
function convertRiskScoreToAlerts(score) {
  const alertData = { critical: 0, high: 0, medium: 0, low: 0, unassigned: 0 }

  // 风险评分分段转换策略
  if (score >= 500) {
    // 高风险：分配到严重和高级告警
    alertData.critical = Math.floor(score / 100)
    alertData.high = Math.floor((score % 100) / 20)
    alertData.unassigned = Math.floor((score % 20) / 5)
  } else if (score >= 200) {
    // 中高风险：主要分配到高级和中级告警
    alertData.high = Math.floor(score / 50)
    alertData.medium = Math.floor((score % 50) / 10)
    alertData.unassigned = Math.floor((score % 10) / 3)
  } else if (score >= 50) {
    // 中等风险：主要分配到中级和低级告警
    alertData.medium = Math.floor(score / 25)
    alertData.low = Math.floor((score % 25) / 5)
    alertData.unassigned = Math.floor((score % 5) / 2)
  } else {
    // 低风险：主要分配到低级和未分配告警
    alertData.low = Math.floor(score / 10)
    alertData.unassigned = Math.floor((score % 10) / 2)
  }

  return alertData
}

/**
 * 分线评分转换为告警数据
 * @param {number} score - 分线评分（0-10000+范围）
 * @returns {Object} 告警数据
 */
function convertLineScoreToAlerts(score) {
  const alertData = { critical: 0, high: 0, medium: 0, low: 0, unassigned: 0 }

  // 分线评分分段转换策略（更宽松的转换）
  if (score >= 8000) {
    // 极高分线：适度分配到各级告警
    alertData.critical = Math.floor(score / 2000)
    alertData.high = Math.floor((score % 2000) / 400)
    alertData.medium = Math.floor((score % 400) / 100)
    alertData.unassigned = Math.floor((score % 100) / 20)
  } else if (score >= 3000) {
    // 高分线：主要分配到高级和中级告警
    alertData.high = Math.floor(score / 600)
    alertData.medium = Math.floor((score % 600) / 150)
    alertData.low = Math.floor((score % 150) / 30)
    alertData.unassigned = Math.floor((score % 30) / 10)
  } else if (score >= 1000) {
    // 中等分线：主要分配到中级和低级告警
    alertData.medium = Math.floor(score / 300)
    alertData.low = Math.floor((score % 300) / 60)
    alertData.unassigned = Math.floor((score % 60) / 15)
  } else {
    // 低分线：主要分配到低级和未分配告警
    alertData.low = Math.floor(score / 100)
    alertData.unassigned = Math.floor((score % 100) / 25)
  }

  return alertData
}

/**
 * 统一的车企安全等级计算函数（V3.0）
 * 同时支持告警详情和评分数据，提供更智能的综合评级
 * @param {Object} options - 计算参数
 * @param {Object} options.riskDetails - 告警详情数据（优先使用）
 * @param {number} options.riskScore - 风险评分（辅助参考）
 * @param {string} options.scoreType - 评分类型 ('risk'|'line'|'auto')
 * @returns {Object} 评级结果对象
 */
export function getAutomakerSecurityLevelUnified({
  riskDetails = null,
  riskScore = 0,
  scoreType = 'auto',
} = {}) {
  // 优先使用告警详情数据
  if (riskDetails && isValidRiskDetails(riskDetails)) {
    const result = getAutomakerSecurityLevel(riskDetails)

    // 如果同时有评分，添加评分信息用于参考
    if (riskScore > 0) {
      return {
        ...result,
        originalScore: riskScore,
        scoreType: scoreType,
        algorithm: 'alerts-with-score-v3.0',
        dataSource: 'alerts-primary',
      }
    }

    return {
      ...result,
      algorithm: 'alerts-only-v3.0',
      dataSource: 'alerts-only',
    }
  }

  // 如果告警数据无效但有评分，使用评分转换
  if (riskScore > 0) {
    const alertData = convertScoreToAlertData(riskScore, scoreType)
    const result = getAutomakerSecurityLevel(alertData)

    return {
      ...result,
      originalScore: riskScore,
      scoreType: scoreType,
      convertedAlerts: alertData,
      algorithm: 'score-fallback-v3.0',
      dataSource: 'score-fallback',
    }
  }

  // 无有效数据时返回默认等级
  return {
    ...getAutomakerSecurityLevel({
      critical: 0,
      high: 0,
      medium: 0,
      low: 0,
      unassigned: 0,
    }),
    algorithm: 'default-v3.0',
    dataSource: 'default',
  }
}

/**
 * 验证告警数据是否有效
 * @param {Object} riskDetails - 告警详情数据
 * @returns {boolean} 是否有效
 */
function isValidRiskDetails(riskDetails) {
  if (
    !riskDetails ||
    typeof riskDetails !== 'object' ||
    Array.isArray(riskDetails)
  ) {
    return false
  }

  // 检查是否包含必要的字段
  const requiredFields = ['critical', 'high', 'medium', 'low', 'unassigned']
  return requiredFields.every(field => {
    // 使用 in 操作符替代 hasOwnProperty，更安全
    if (!(field in riskDetails)) {
      return false
    }

    const value = riskDetails[field]
    // 检查值是否为有效数字
    return typeof value === 'number' && !isNaN(value) && value >= 0
  })
}

/**
 * @deprecated 使用 getAutomakerSecurityLevelUnified 替代
 * 基于评分的安全等级计算（保留用于向后兼容）
 */
export function getAutomakerSecurityLevelFromScore(
  riskScore,
  scoreType = 'auto',
) {
  console.warn(
    'getAutomakerSecurityLevelFromScore 已废弃，请使用 getAutomakerSecurityLevelUnified',
  )
  return getAutomakerSecurityLevelUnified({ riskScore, scoreType })
}

/**
 * 测试函数：验证优化后的算法效果
 * @returns {Object} 测试结果
 */
export function testOptimizedAlgorithm() {
  const testCases = [
    {
      name: '原问题场景1：风险评分93',
      data: { score: 93, type: 'risk' },
      expectedImprovement: '93分不应该是较差评级',
    },
    {
      name: '原问题场景2：分线评分7000',
      data: { score: 7000, type: 'line' },
      expectedImprovement: '7000分不应该和93分评级相同',
    },
    {
      name: '告警场景：19个未分配告警',
      data: { critical: 0, high: 0, medium: 0, low: 0, unassigned: 19 },
      expectedImprovement: '应该不再是较差评级',
    },
    {
      name: '大量告警场景：100个低级告警',
      data: { critical: 0, high: 0, medium: 0, low: 100, unassigned: 0 },
      expectedImprovement: '大量告警应有宽松处理',
    },
    {
      name: '混合场景：少量严重+大量未分配',
      data: { critical: 1, high: 0, medium: 0, low: 0, unassigned: 50 },
      expectedImprovement: '未分配告警不应过度影响评级',
    },
  ]

  const results = testCases.map(testCase => {
    let result
    if (testCase.data.score !== undefined) {
      // 评分测试
      result = getAutomakerSecurityLevelFromScore(
        testCase.data.score,
        testCase.data.type,
      )
    } else {
      // 告警数据测试
      result = getAutomakerSecurityLevel(testCase.data)
    }

    return {
      ...testCase,
      result: {
        level: result.level,
        score: result.score,
        alertVolume: result.alertVolume,
        alertPattern: result.alertPattern,
        thresholds: result.thresholds,
        originalScore: result.originalScore,
        convertedAlerts: result.convertedAlerts,
      },
    }
  })

  return {
    testResults: results,
    summary: '算法优化测试完成，请检查各场景的评级是否合理',
  }
}
