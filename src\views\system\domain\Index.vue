<template>
  <div>
    <bread-crumb class="mb-3"></bread-crumb>

    <v-card tile class="main-content">
      <v-card-text class="pa-0">
        <div class="d-flex justify-space-between align-center flex-row-reverse">
          <div class="w-0 d-flex align-end">
            <v-text-field
              v-model="query.domainName"
              class="flex-1"
              color="primary"
              hide-details
              :label="$t('domain.headers.domain')"
              dense
              outlined
              @keyup.enter.native="$_search"
              :rules="[max(query.domainName, 128)]"
            ></v-text-field>
            <v-text-field
              v-model="query.ipAddress"
              class="flex-1 ml-3"
              color="primary"
              hide-details
              :label="$t('domain.headers.ipAddress')"
              dense
              outlined
              @keyup.enter.native="$_search"
              :rules="[max(query.ipAddress, 128)]"
            ></v-text-field>
            <v-select
              v-model="query.status"
              class="flex-1 ml-3"
              color="primary"
              hide-details
              :label="$t('global.status')"
              dense
              outlined
              :items="activeEnum"
              :menu-props="{ offsetY: true }"
              clearable
              @keyup.enter.native="$_search"
              :rules="[max(query.status, 128)]"
            ></v-select>
            <v-btn
              color="primary--text bg-btn ml-3"
              elevation="0"
              @click="$_search"
            >
              <!-- <v-icon class="me-1"> mdi-magnify </v-icon> -->
              <span>{{ $t('action.search') }}</span>
            </v-btn>
          </div>
          <div class="d-flex justify-end align-center">
            <v-btn
              width="76"
              min-width="76"
              elevation="0"
              color="primary"
              class="me-1"
              @click="add"
              v-has:domain-add
            >
              <!-- <v-icon>mdi-plus</v-icon> -->
              <span> {{ $t('action.add') }} </span>
            </v-btn>
          </div>
        </div>

        <v-data-table
          ref="collectTable"
          fixed-header
          :items-per-page="query.pageSize"
          item-key="id"
          :height="tableHeight"
          hide-default-footer
          :headers="headers"
          :items="tableData"
          class="table border-radius-xl mt-3 thead-light"
          :loading="tableDataLoading"
        >
          <template v-slot:item.project="{ item }">
            <div
              v-show-tips
              style="max-width: 14rem"
              class="text-overflow-hide"
            >
              {{ item.project }}
            </div>
          </template>

          <template v-slot:item.environment="{ item }">
            <div
              v-show-tips
              style="max-width: 14rem"
              class="text-overflow-hide"
            >
              {{ item.environment }}
            </div>
          </template>

          <template v-slot:item.domainType="{ item }">
            <div v-show-tips style="max-width: 8rem" class="text-overflow-hide">
              {{ item.domainType }}
            </div>
          </template>

          <template v-slot:item.status="{ item }">
            <v-icon
              v-if="$toItem(activeEnum, item.status)"
              v-show-tips="$toItem(activeEnum, item.status).text"
              :color="$toItem(activeEnum, item.status).color"
              >{{ $toItem(activeEnum, item.status).icon }}</v-icon
            >
          </template>

          <template v-slot:item.actions="{ item }">
            <v-btn icon @click="onEdit(item)" v-has:domain-edit>
              <vsoc-icon
                v-show-tips="$t('action.edit')"
                type="fill"
                icon="icon-bianji"
                class="action-btn"
                size="x-large"
              ></vsoc-icon>
            </v-btn>
          </template>
        </v-data-table>
        <!-- 分页器 -->
        <vsoc-pagination
          :page.sync="query.pageNum"
          :size.sync="query.pageSize"
          :total="tableDataTotal"
          @change-size="$_search"
          @change-page="getTableData"
        >
        </vsoc-pagination>
      </v-card-text>
    </v-card>
    <domain-edit
      ref="domainEdit"
      :item="editForm"
      :mode="ops"
      @refresh="getTableData"
    ></domain-edit>
  </div>
</template>

<script>
import { max } from '@/@core/utils/validation'
import { getDomainList } from '@/api/system/domain'
import VsocPagination from '@/components/VsocPagination.vue'
import breadCrumb from '@/components/bread-crumb/index'
import { PAGESIZE } from '@/util/constant'
import { dataFilter } from '@/util/filters'
import { setRemainingHeight } from '@/util/utils'
import DomainEdit from '@/views/system/domain/DomainEdit'
export default {
  name: 'PlatformIndex',
  components: {
    VsocPagination,
    breadCrumb,
    DomainEdit,
  },
  filters: {
    dataFilter,
  },
  data() {
    return {
      max,
      tableDataLoading: false,

      // 分页参数
      query: {
        pageNum: 1,
        pageSize: PAGESIZE,
        ipAddress: '', //IP地址
        domainName: '', //域名
        status: '', //状态 0失效 1有效
      },
      tableDataTotal: 0,
      tableHeight: '34.5rem',
      tableData: [],
      ops: '',
      editForm: this.initRoleData(),
    }
  },
  computed: {
    activeEnum() {
      return this.$store.getters['enums/getActiveStatus']
    },
    headers() {
      return [
        { text: this.$t('domain.headers.id'), value: 'id', width: 120 },
        {
          text: this.$t('domain.headers.project'),
          value: 'project',
          width: 120,
        },
        {
          text: this.$t('domain.headers.environment'),
          value: 'environment',
          width: 160,
        },
        {
          text: this.$t('domain.headers.domain'),
          value: 'domainName',
          width: 160,
        },
        {
          text: this.$t('domain.headers.port'),
          value: 'domainPort',
          width: 160,
        },
        {
          text: this.$t('domain.headers.ipAddress'),
          value: 'ipAddress',
          width: 160,
        },
        {
          text: this.$t('domain.headers.type'),
          value: 'domainType',
          width: 100,
        },
        {
          text: this.$t('domain.headers.domainUser'),
          value: 'domainManager',
          width: 180,
        },
        {
          text: this.$t('domain.headers.remark'),
          value: 'notes',
          width: 200,
        },
        {
          text: this.$t('global.status'),
          value: 'status',
          width: 100,
        },
        // {
        //   text: this.$t('global.createUser'),
        //   value: 'createUser',
        //   width: 160,
        // },
        // {
        //   text: this.$t('global.createDate'),
        //   value: 'createDate',
        //   width: 160,
        // },
        {
          text: this.$t('global.updateUser'),
          value: 'updateUser',
          width: 160,
        },
        {
          text: this.$t('global.updateDate'),
          value: 'updateDate',
          width: 200,
        },
        {
          text: '',
          value: 'actions',
          sortable: false,
          width: 80,
        },
      ]
    },
  },

  created() {
    this.$_setTableHeight()
    this.$bus.$on('resize', this.$_setTableHeight)
    this.$_search()
  },
  beforeDestroy() {
    this.$bus.$off('resize', this.$_setTableHeight)
  },
  methods: {
    add() {
      this.ops = 'new'
      this.$refs.domainEdit.isDrawerShow = true
    },
    onEdit(record) {
      this.ops = 'edit'

      this.editForm = { ...record }
      this.$refs.domainEdit.isDrawerShow = true
    },

    initRoleData() {
      return {
        platformName: '',
        description: '',
      }
    },
    // // 新增
    // addList() {
    //   this.$router.push('/detection/list/edit')
    // },

    // // 修改
    // editList(item) {
    //   this.$router.push(`/detection/list/edit?id=${item.id}`)
    // },

    $_search() {
      this.query.pageNum = 1
      this.getTableData()
    },

    $_setTableHeight() {
      this.$nextTick(() => {
        // let num = 18
        // const fontSize = +getComputedStyle(window.document.documentElement)[
        //   'font-size'
        // ].replace('px', '')
        // if (fontSize >= 16) {
        //   num -= fontSize - 14
        // }
        // this.tableHeight =
        //   document.documentElement.clientHeight - num * fontSize - 4
        const filterFn = () => {
          return -12
        }
        this.tableHeight = setRemainingHeight(filterFn)
      })
    },

    getRandomDate() {
      const start = new Date(2020, 0, 1)
      const end = new Date(2023, 3, 26)
      const randomDate = new Date(
        start.getTime() + Math.random() * (end.getTime() - start.getTime()),
      )
      return randomDate.toLocaleString()
    },
    // 获取平台
    async getTableData() {
      // 保存查询参数
      this.tableDataLoading = true
      // const data = deepClone(this.query)
      // const res = await getVehicleList(data)
      try {
        // for (let i = 0; i < 10; i++) {
        //   const item = {
        //     id: 'PLA' + (i * 10000 + 1),
        //     platformName: '平台名称' + (i + 1),
        //     description: '平台描述' + (i + 1),
        //     created_at: this.getRandomDate(),
        //     updated_at: this.getRandomDate(),
        //     created_by: '创建者' + (i + 1),
        //     updated_by: '更新者' + (i + 1),
        //   }
        //   this.tableData.push(item)
        // }
        // this.tableDataTotal = 10
        // this.tableData = res.data.records
        const { data } = await getDomainList(this.query)
        this.tableData = data.records
        this.tableDataTotal = data.total
      } catch (e) {
        console.error(`获取平台管理错误：${e}`)
      } finally {
        this.tableDataLoading = false
      }
    },
  },
}
</script>
<style lang="scss" scoped></style>
