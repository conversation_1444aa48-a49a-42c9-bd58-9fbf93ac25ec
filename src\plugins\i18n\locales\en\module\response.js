const response = {
  currentTitle: 'Ticket Action',
  headers: {
    id: 'Sort',
    responseName: 'Action name',
    responseEventName: 'Action event',
    statusName: 'Activity',
    // updateUser: 'Update User',
    // updateDate: 'Update Time',
    // createUser: 'Create User',
    // createDate: 'Create Time',
    responseEventName1: 'Response Event',
    responseCondition: 'Response Conditions',
    Operator: 'Action List',
  },
  infoHeaders: {
    type: 'Type',
    value: 'Dynamic Parameter',
    name: 'Name',
    send: 'Sending Attachments',
  },
  btn: {
    add: 'Add Ticket Action',
    edit: 'Edit Ticket Action',
    del: 'Delete Ticket Action',
    detail: 'Ticket Action Details',
    add1: 'New Condition',
    add2: 'New Action',
  },
  swal: {
    tip1: 'Condition',
    tip2: 'Operator',
    tip3: 'Value',
    tip4: 'View all replacement labels',
    tip5: 'The following is a list and explanation of all replacement labels, which you can apply to the notification information. After submission, the system will generate the corresponding content.',
    parameter: 'Parameter',
    notice1: 'Recipients',
    notice2: 'Subject',
    stop: 'Disable Response',
    start: 'Enable Response',
    sure: 'Are you sure to {0} {1}?',
    tip: '{0} successfully!',
    delTip: 'Enabled response tasks are not allowed to be deleted!',
    text: 'Are you sure to delete the response: {0}?',
    delTip1: 'The rule is incomplete, please fill in or delete it',
    delTip2: 'The execution rule is incomplete, please fill in or delete it',

    delTip3: 'Include at least one complete rule',
    delTip4: 'Include at least one complete execution rule',
  },
}

export default response
