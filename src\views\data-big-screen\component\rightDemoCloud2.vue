<template>
  <div class="c-box pb-0">
    <div class="c-box-header">安全事件趋势</div>

    <vsoc-chart
      echartId="alert-line"
      class="box-chart d-flex align-center"
      :option="option"
    ></vsoc-chart>
  </div>
</template>

<script>
import VsocChart from '@/components/VsocChart.vue'
import { lineOptionFn } from './chart'

export default {
  name: 'rightDemoCloud2',
  props: {
    alarmTrend: {
      type: Array,
      default: () => {
        return [
          {
            level: '0',
            levelName: '高危事件',
            arrays: [
              {
                number: '0',
                time: '2025-05-03',
              },
              {
                number: '0',
                time: '2025-05-04',
              },
              {
                number: '0',
                time: '2025-05-05',
              },
              {
                number: 104,
                time: '2025-05-06',
              },
              {
                number: '0',
                time: '2025-05-07',
              },
              {
                number: '0',
                time: '2025-05-08',
              },
              {
                number: '0',
                time: '2025-05-09',
              },
            ],
          },
          {
            level: '1',
            levelName: '预警事件',
            arrays: [
              {
                number: '0',
                time: '2025-05-03',
              },
              {
                number: '0',
                time: '2025-05-04',
              },
              {
                number: '0',
                time: '2025-05-05',
              },
              {
                number: 406,
                time: '2025-05-06',
              },
              {
                number: '0',
                time: '2025-05-07',
              },
              {
                number: '0',
                time: '2025-05-08',
              },
              {
                number: '0',
                time: '2025-05-09',
              },
            ],
          },
          // {
          //   level: '2',
          //   levelName: '中',
          //   arrays: [
          //     {
          //       number: '0',
          //       time: '2025-05-03',
          //     },
          //     {
          //       number: '0',
          //       time: '2025-05-04',
          //     },
          //     {
          //       number: '0',
          //       time: '2025-05-05',
          //     },
          //     {
          //       number: 2,
          //       time: '2025-05-06',
          //     },
          //     {
          //       number: '0',
          //       time: '2025-05-07',
          //     },
          //     {
          //       number: '0',
          //       time: '2025-05-08',
          //     },
          //     {
          //       number: '0',
          //       time: '2025-05-09',
          //     },
          //   ],
          // },
          // {
          //   level: '3',
          //   levelName: '低',
          //   arrays: [
          //     {
          //       number: '0',
          //       time: '2025-05-03',
          //     },
          //     {
          //       number: '0',
          //       time: '2025-05-04',
          //     },
          //     {
          //       number: '0',
          //       time: '2025-05-05',
          //     },
          //     {
          //       number: 321,
          //       time: '2025-05-06',
          //     },
          //     {
          //       number: '0',
          //       time: '2025-05-07',
          //     },
          //     {
          //       number: '0',
          //       time: '2025-05-08',
          //     },
          //     {
          //       number: '0',
          //       time: '2025-05-09',
          //     },
          //   ],
          // },
        ]
      },
    },
    xList: {
      type: Array,
      default: () => {
        return ['05/03', '05/04', '05/05', '05/06', '05/07', '05/08', '05/09']
      },
    },
    total: {
      type: [Number, String],
      default: 0,
    },
  },
  components: {
    VsocChart,
  },
  data() {
    return {
      // option: {},
      myChart: undefined,
    }
  },
  // mounted() {
  //   this.onDraw()
  // },
  computed: {
    yList() {
      let alertLevel = this.$store.state.enums.enums.AlarmLevel
      if (!alertLevel || alertLevel.length === 0) {
        return []
      }
      let colors = ['#4CA3B6', '#1B3F82']
      let yData = []
      this.alarmTrend.forEach(item => {
        let obj = {
          name: item.levelName,
          color: alertLevel[item.level].color,
          // color: colors[item.level],
          data: item.arrays.map(x => x.number),
        }
        yData.push(obj)
      })
      return yData
      // return [
      //   {
      //     name: alertLevel[0].text,
      //     // color: '#FF4C51',
      //     color: '#FF385D',
      //     data: [139, 99, 10, 17, 0, 2, 13, 29],
      //   },
      //   {
      //     name: alertLevel[1].text,
      //     // color: '#fb8c00',
      //     color: '#FF6B00',
      //     data: [61, 52, 0, 5, 0, 0, 15, 7],
      //   },
      //   {
      //     name: alertLevel[2].text,
      //     // color: '#EEC900',
      //     color: '#F0DA4C',
      //     data: [4, 5, 0, 0, 0, 0, 0, 5],
      //   },
      //   {
      //     name: alertLevel[3].text,
      //     // color: '#3caea3',
      //     color: '#32FDB8',
      //     data: [2, 34, 0, 0, 0, 0, 7, 1],
      //   },
      // ]
    },
    option() {
      const op = lineOptionFn(this.xList, this.yList, '', {
        top: '20%',
        bottom: '10%',
      })
      return {
        ...op,
        legend: {
          ...op.legend,
          ...{
            formatter: name => {
              return name
            },
          },
        },
      }
    },
  },
  // methods: {
  //   onResize() {
  //     this.$nextTick(() => {
  //       this.option = lineOptionFn(xList, this.yList)
  //       this.myChart.setOption(this.option)
  //       this.myChart.resize()
  //     })
  //   },
  //   onDraw() {
  //     const ele = document.getElementById('alert-line')
  //     this.myChart = this.$echarts.init(ele, {
  //       height: 'auto',
  //       width: 'auto',
  //     })
  //     this.option = lineOptionFn(xList, this.yList)
  //     this.myChart.setOption(this.option)
  //   },
  // },
}
</script>
