<!-- 告警抽屉 -->
<template>
  <vsoc-drawer
    :title="$t('action.advanced')"
    width="24rem"
    :value="value"
    style="z-index: 9999999999999"
    @click:confirm="doQuery"
    @input="close"
    @click:close="close"
    @click:cancel="close"
  >
    <template #right-title>
      <v-btn icon @click="clearAdvanceQuery">
        <v-icon size="16">mdi-filter-variant-remove</v-icon>
      </v-btn>
    </template>
    <template>
      <!-- <div class="text-lg font-weight-semibold mb-4">
        {{ $t('analytics.drawer.alertType') }}
      </div>
      <v-select
        v-model="advanceQuery.warnClassifyIds"
        color="primary"
        :items="alertTypes"
        class="mx-2"
        multiple
        outlined
        dense
        item-value="name"
        :menu-props="{ offsetY: true }"
      >
        <template v-slot:selection="{ index }">
          <span v-if="index === 0" class="text-caption">
            {{ $t('global.selected') }} {{ advanceQuery.warnClassifyIds.length }}
          </span>
        </template>
      </v-select> -->
      <div class="text-lg font-weight-semibold mb-2">
        {{ $t('analytics.alertLevel') }}
      </div>
      <div class="align-center flex-wrap mx-2">
        <!-- <div class="d-flex align-center flex-wrap"> -->
        <v-checkbox
          v-for="(item, i) in alertLevel"
          :key="i"
          v-model="advanceQuery.alarmLevelList"
          color="primary"
          :value="item.value"
          column
          hide-details
          class="mr-8 my-0"
        >
          <template v-slot:label>
            <div class="d-flex align-center text-center">
              <v-icon :style="{ color: item.color }" size="2.5rem">
                mdi-circle-medium
              </v-icon>
              <span class="text-sm font-weight-semibold">
                {{ item.text }}
              </span>
            </div>
          </template>
        </v-checkbox>
      </div>

      <div class="text-lg font-weight-semibold mt-4 mb-2">
        {{ $t('analytics.alertStatus') }}
      </div>
      <!-- label="告警状态" -->
      <div class="align-center flex-wrap mx-2">
        <v-checkbox
          v-for="(item, i) in alertStatus"
          :key="i"
          v-model="advanceQuery.statusList"
          color="primary"
          :value="item.value"
          column
          hide-details
          multiple
          class="mr-8 my-0"
          :disabled="item.disabled"
        >
          <template v-slot:label>
            <div
              class="d-flex align-center text-center"
              v-if="$toItem(alertStatus, item.value)"
            >
              <v-icon size="2.3rem" :color="item.color">
                mdi-circle-medium
              </v-icon>
              <span class="text-sm font-weight-semibold">
                {{ item.text }}
              </span>
            </div>
          </template>
        </v-checkbox>
      </div>
    </template>
  </vsoc-drawer>
</template>

<script>
// import { getData } from '@/api/detector'
import VsocDrawer from '@/components/VsocDrawer.vue'
import VsocDateRange from '@/components/vsoc-date-range/VsocDateRange.vue'
import { PAGESIZE_MAX } from '@/util/constant'
import { deepClone } from '@/util/utils'

export default {
  name: 'IdpsDrawer',
  components: {
    VsocDrawer,
    VsocDateRange,
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      alertTypes: [],
      advanceQuery: {
        alarmLevelList: [],
        statusList: [],
        // warnClassifyIds: [],
      },
    }
  },

  computed: {
    alertStatus() {
      return this.$store.getters['enums/getAlertStatusWithTitle']
    },
    // 获取告警等级颜色
    alertLevel() {
      return this.$store.state.enums.enums.AlarmLevel
    },
  },
  created() {
    // this.init()
    // this.loadAlertTypeData()
  },
  methods: {
    init() {
      const date = new Date()
      this.advanceQuery.alarmLevelList = ['0', '1', '2', '3', '4']
      this.advanceQuery.statusList = ['0', '1', '2']
      this.handlerData()
    },

    // 查询告警类型数据
    async loadAlertTypeData() {
      try {
        const { data } = await getData({
          pageNum: 1,
          pageSize: PAGESIZE_MAX,
        })
        this.alertTypes = data.records.map(v => ({
          text: v.name,
          value: v.id,
        }))
        const obj = {}
        this.alertTypes.forEach(item => {
          obj[item.value] = {
            text: item.text,
          }
        })
        this.alertTypeMap = obj
      } catch (e) {
        console.error(`获取告警类型数据：${e}`)
      }
    },

    close(bool) {
      if (!bool) {
        this.$emit('input', false)
      }
    },

    setModel(val) {
      this.advanceQuery = val
    },

    closeChip(index) {
      console.log(index)
    },

    clearAdvanceQuery() {
      this.advanceQuery = {
        alarmLevelList: [],
        statusList: [],
        // warnClassifyIds: [],
      }

      // this.init()
    },

    //  高级查询
    doQuery(callback) {
      const params = {
        alarmLevelList: this.advanceQuery.alarmLevelList,
        statusList: this.advanceQuery.statusList,
      }
      this.$emit('do-query', params)
      callback()
    },

    open() {},

    removeAlertStatus(i) {
      this.advanceQuery.statusList.splice(i, 1)
    },

    removeTag(i) {
      this.advanceQuery.tagsList.splice(i, 1)
    },
  },
}
</script>
