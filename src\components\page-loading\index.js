import LoadingVue from './loading.vue'

// 计算请求的个数
let needLoadingRequestCount = 0
let t = null
const $loading = {
  install: Vue => {
    // 通过 Vue.extend方法 获取LoadingComponent 组件 类
    const LoadingComponent = Vue.extend(LoadingVue)

    // new LoadingComponent得到组件的实例
    const vm = new LoadingComponent()

    // 获取组件实例的html 并插入到body中
    const tpl = vm.$mount().$el

    // 插入到body中
    // setTimeout(() => {
    //   document.querySelector('.v-main__wrap').appendChild(tpl)
    // }, 500)
    // 添加 显示loading方法
    Vue.prototype.$showLoading = () => {
      if (needLoadingRequestCount === 0) {
        // if (t) clearTimeout(t)
        document.querySelector('.v-main__wrap') &&
          document.querySelector('.v-main__wrap').appendChild(tpl)
        vm.isShow = true
      }
      needLoadingRequestCount++
    }

    // 添加关闭loading方法
    Vue.prototype.$hideLoading = () => {
      if (needLoadingRequestCount <= 0) return
      needLoadingRequestCount--
      if (needLoadingRequestCount === 0) {
        setTimeout(() => {
          vm.isShow = false
        }, 500) /* 500ms 间隔内的 loading 合并为一次 */
      }
    }
  },
}

export default $loading
