<template>
  <div>
    <div class="d-flex align-baseline">
      <div class="font-weight-semibold">
        {{ $t('infer.expression.express') }}:
      </div>
      <div style="width: 150px">
        <v-text-field
          v-model.trim="cronExpression"
          class="mx-2"
          outlined
          solo
          dense
          @change="emitChange"
          hide-details
        >
        </v-text-field>
      </div>
      <div :class="!parseDetail.isChecked ? 'error--text' : 'primary--text'">
        {{ parseDetail.message }}
      </div>
    </div>
  </div>
</template>

<script>
import { validExpression } from '../utils'

export default {
  name: 'CronAdvanced',
  data() {
    return {
      cronExpression: '0 0 0/1 * * ?',
      parseDetail: { isChecked: true, message: '' },
    }
  },
  computed: {
    cronExp() {
      return this.cronExpression
    },
  },
  watch: {
    cronExpression() {
      this.parseCron()
    },
  },
  mounted() {
    this.parseCron()
  },
  methods: {
    init(value) {
      this.cronExpression = value
    },
    parseCron() {
      const data = validExpression(this.cronExpression)
      this.parseDetail = { ...this.parseDetail, ...data }
    },
    emitChange() {
      this.$emit('change', this.cronExp)
    },
  },
}
</script>

<style lang="scss" scoped>
.v-text-field {
  margin-top: 0 !important;
  padding-top: 0px !important;
}
</style>
