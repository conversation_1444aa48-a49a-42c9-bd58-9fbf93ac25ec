<!-- 事件简要名称 -->
<template>
  <!-- bg-gradient-info / bg-body -->
  <v-card
    :class="{
      'bg-body': $vuetify.theme.isDark && mode === 'asset',
      'bg-gradient-secondary': !$vuetify.theme.isDark && mode === 'asset',
      'bg-transparent digital-twin': mode !== 'asset',
    }"
    elevation="0"
  >
    <v-card-text class="text-base">
      <div class="d-flex white--text" v-if="mode === 'asset'">
        <div class="event-icon">
          <v-btn
            v-if="data.type === 'RecentActivity'"
            fab
            color="primary"
            large
          >
            <v-icon size="3rem"> mdi-car </v-icon>
          </v-btn>
          <v-avatar
            v-else-if="['Statistics', 'Alarm'].includes(data.type)"
            width="4rem"
            height="4rem"
            :color="data.color || 'primary'"
          >
            <v-icon size="2rem" color="white" class="rounded-0">
              mdi-flash
            </v-icon>
          </v-avatar>

          <img
            v-else
            src="@/assets/images/svg/timeline-event.svg"
            class="event-icon"
          />
        </div>
        <div
          class="d-flex align-start justify-space-between ml-10"
          style="width: calc(100% - 5rem)"
          v-if="data.type !== 'Statistics'"
        >
          <div class="asset-event-box">
            <div
              style="min-height: 38px"
              class="text-h6 mb-0 d-flex align-center"
            >
              <v-tooltip bottom color="bg-blur">
                <template v-slot:activator="{ on, attrs }">
                  <p
                    class="text-overflow-hide"
                    style="max-width: 24rem"
                    v-bind="attrs"
                    v-on="on"
                  >
                    {{ data.eventName | generateEventName(data) }}
                  </p>
                </template>
                <span class="white--text">{{
                  data.eventName | generateEventName(data)
                }}</span>
              </v-tooltip>
              <template v-if="data.type !== 'RecentActivity'">
                <v-tooltip bottom color="bg-blur" max-width="30rem">
                  <template v-slot:activator="{ on, attrs }">
                    <p
                      style="max-width: 24rem"
                      class="secondary--text text-base pl-4 text-overflow-hide"
                      v-bind="attrs"
                      v-on="on"
                    >
                      {{ data.description }}
                    </p>
                  </template>
                  <span class="white--text">{{ data.description }}</span>
                </v-tooltip>
                <div
                  v-if="type === 'assets' && Number(data.alarmNoiseCount) >= 1"
                  class="ml-1"
                >
                  <v-tooltip bottom color="bg-blur">
                    <template v-slot:activator="{ on, attrs }">
                      <v-chip
                        small
                        label
                        class="bg-btn px-2 font-weight-medium"
                        v-bind="attrs"
                        v-on="on"
                      >
                        {{ data.alarmNoiseCount }}
                      </v-chip>
                    </template>
                    <span cclass="white--text">{{
                      $t('alert.headers.suppressionFrequency')
                    }}</span>
                  </v-tooltip>
                </div>
                <v-btn
                  v-if="data.description"
                  v-copy="data.description"
                  v-show-tips="$t('action.copy')"
                  icon
                  class="text--primary"
                >
                  <vsoc-icon
                    type="fill"
                    size="16px"
                    icon="icon-fuzhi"
                  ></vsoc-icon>
                </v-btn>
              </template>
            </div>

            <!-- <div
              style="min-height: 38px"
              class="d-flex align-center flex-shrink-0 opacity-8"
            >
              <div>
                <v-icon
                  size="1.25rem"
                  class="white--text opacity-8 material-icons-round mt-n1 mr-1"
                >
                  mdi-map-marker-radius
                </v-icon>

                <template
                  v-if="
                    data.longitude &&
                    data.latitude &&
                    data.longitude !== 'null' &&
                    data.latitude !== 'null'
                  "
                >
                  <span class="font-weight-normal white--text opacity-8">
                    <span
                      class="mr-1"
                      :class="{ 'loader-circle': isAddressLoading }"
                    >
                      {{ data.location | dataFilter }}
                    </span>
                    |
                  </span>
                  <a class="primary--text pl-2 a-hover">{{
                    `${data.longitude}, ${data.latitude}`
                  }}</a>
                </template>
                <template v-else>
                  <span class="font-weight-normal white--text opacity-8"
                    >N/A</span
                  >
                </template>
              </div>
            </div> -->
          </div>

          <div>
            <div
              style="min-height: 38px"
              class="d-flex align-center flex-shrink-0 opacity-8"
            >
              <v-icon
                size="1.25rem"
                class="white--text mr-2"
                v-if="data.firstEventDate && data.lastEventDate"
              >
                mdi-clock-time-four-outline
              </v-icon>

              <span v-if="data.firstEventDate && data.lastEventDate">
                <span>{{ data.firstEventDate | toDate }}</span> -
                <span>{{ data.lastEventDate | toDate }}</span>
              </span>
              <v-chip
                v-if="data.eventCount > 1"
                label
                small
                class="primary-bg ml-3 font-weight-medium"
              >
                {{
                  $tc('asset.axis.frequency', data.eventCount, {
                    num: data.eventCount,
                  })
                }}
              </v-chip>
            </div>
            <div
              v-if="type === 'assets' && data.type === 'Alarm'"
              class="d-flex align-center justify-end"
            >
              <div v-if="alertStatus[data.status]">
                <v-badge
                  dot
                  inline
                  offset-x="10"
                  :offset-y="-18"
                  :color="alertStatus[data.status].color"
                  class="mr-1"
                ></v-badge>
                <span>{{ alertStatus[data.status].text }}</span>
              </div>
              <v-btn icon class="ml-1" @click.stop="goAlert(data.id)">
                <vsoc-icon
                  type="fill"
                  size="16px"
                  class="primary--text"
                  icon="icon-tiaozhuan"
                ></vsoc-icon>
              </v-btn>
            </div>
          </div>
        </div>
        <div v-else class="d-flex align-center ml-10">
          <div class="text-h6 mb-0 d-flex align-end">
            <v-tooltip bottom color="bg-blur">
              <template v-slot:activator="{ on, attrs }">
                <p
                  class="text-overflow-hide mb-0"
                  style="max-width: 24rem"
                  v-bind="attrs"
                  v-on="on"
                >
                  {{ data.eventName | generateEventName(data) }}
                </p>
              </template>
              <span class="white--text">{{
                data.eventName | generateEventName(data)
              }}</span>
            </v-tooltip>
            <v-tooltip bottom color="bg-blur" max-width="30rem">
              <template v-slot:activator="{ on, attrs }">
                <p
                  style="max-width: 24rem"
                  class="secondary--text text-base pl-4 text-overflow-hide mb-0"
                  v-bind="attrs"
                  v-on="on"
                >
                  {{ data.description }}
                </p>
              </template>
              <span class="white--text">{{ data.description }}</span>
            </v-tooltip>
          </div>
        </div>
      </div>

      <div
        v-show="data.type === 'Statistics' && mode !== 'digital-twin'"
        class="w-100 pt-0 mt-0"
      >
        <v-divider
          class="mt-2 divider--dashed"
          style="border-width: 1px"
        ></v-divider>
        <div class="d-flex mt-4 mb-2">
          <v-icon
            size="1.5rem"
            class="pb-1 ml-3 iconfont primary--text"
            v-if="assetTypeEnum[assetType]"
          >
            {{ assetTypeEnum[assetType].icon }}
          </v-icon>
          <p class="ml-3 mb-0 pb-0 white--text">
            {{ data.vehicleId }}
          </p>
        </div>
        <!-- <div id="alert-chart" class="w-100 h-100"></div> -->
        <vsoc-chart
          echartId="alert-chart"
          :option="chartOption"
          :height="164"
        ></vsoc-chart>
      </div>

      <!-- <div v-show="data.type !== 'Statistics' && mode !== 'digital-twin'">
        <v-divider
          v-show="fields.length"
          class="mt-2 divider--dashed"
          style="border-width: 1px"
        ></v-divider>
        <div class="event-list white--text mt-3">
          <v-tooltip
            color="bg-blur"
            v-for="item in fields"
            :key="item._key"
            bottom
          >
            <template v-slot:activator="{ on, attrs }">
              <div
                class="position-relative px-4 event-item"
                v-bind="attrs"
                :style="`color: ${item.color}`"
                v-on="on"
              >
                <div
                  :class="{
                    'font-weight-semibold': item.color,
                    'text--secondary ': $vuetify.theme.isDark && !item.color,
                    'white--text opacity-6':
                      !$vuetify.theme.isDark && !item.color,
                  }"
                  class="text-overflow-hide"
                >
                  {{ item.name }}
                </div>
                <div
                  class="text-overflow-hide pt-1 text-ml"
                  :class="{
                    'font-weight-semibold': item.color,
                  }"
                >
                  <template
                    v-if="['5', '6', '7'].includes(item.signalValueType)"
                  >
                    <a
                      v-if="item.value"
                      :class="{
                        'text-info': $vuetify.theme.isDark,
                        'a-hover': isAHover(item.value),
                      }"
                      @click="onShowSub(item)"
                      >{{ item.value }}</a
                    >
                    <div v-else class="w-100 text-overflow-hide">N/A</div>
                  </template>
                  <template v-else>
                    <div class="w-100 text-overflow-hide">
                      {{ item.value }}
                      <span v-if="item.unit">{{ item.unit }}</span>
                    </div>
                  </template>
                </div>
              </div>
            </template>
            <div class="text-md white--text pt-2">
              <p>
                {{ item.name }}
              </p>
              <p class="my-1">{{ item.value }}{{ item.unit }}</p>

              <p>{{ item.description }}</p>
            </div>
          </v-tooltip>
        </div>
      </div> -->

      <!--  v-if="['RecentActivity', 'Alarm'].includes(data.type)" -->
      <div
        v-if="data.fullDigitalTwin.length && mode !== 'digital-twin'"
        class="d-flex align-center cursor-pointer"
        @click.stop="showDetails = !showDetails"
      >
        <v-icon size="1.25rem" class="iconfont" color="primary"
          >icon-quanjusousuo</v-icon
        >
        <span class="ml-2 white--text">{{ $t('asset.axis.view') }}</span>
        <v-divider
          class="divider--dashed mx-3"
          style="border-width: 1px"
        ></v-divider>
        <v-btn icon>
          <vsoc-icon
            size="1.25rem"
            :class="$vuetify.theme.isDark ? 'secondary--text' : 'white--text'"
            type="fill"
            :icon="showDetails ? 'icon-shouqi' : 'icon-zhankai'"
          >
          </vsoc-icon>
        </v-btn>
        <!-- <div class="text-fb" @click.stop="showDetails = !showDetails">
          <span class="white--text">查看数字孪生</span>
          <v-icon
            size="1.25rem"
            class="white--text material-icons-round rotate-icon mt-n1"
            :class="{ active: showDetails }"
          >
            mdi-chevron-double-down
          </v-icon>
        </div> -->
      </div>

      <v-expand-transition v-if="mode !== 'digital-twin'">
        <div v-show="showDetails && data.fullDigitalTwin.length">
          <div class="d-flex align-end mt-2">
            <div class="flex-1 d-flex">
              <v-btn
                x-small
                elevation="0"
                v-show-tips="'下载所有内容'"
                @click="onDownLoad"
                class="primary-bg"
              >
                <vsoc-icon type="fill" icon="icon-xiazai"> </vsoc-icon>
              </v-btn>
              <v-chip
                small
                label
                class="color-base ml-3 bg-btn font-weight-normal"
              >
                {{ $t('global.pagination.total') }}
                {{ (data.fullDigitalTwin && data.fullDigitalTwin.length) || 0 }}
              </v-chip>
            </div>
            <div class="flex-2 justify-end d-flex">
              <v-text-field
                style="max-width: 20rem !important"
                v-model="searchVal"
                :label="$t('asset.axis.queryField')"
                hide-details
                outlined
                dense
                color="white"
                type="text"
                clearable
                @focus="searchFoucus = true"
                @blur="searchFoucus = false"
              >
                <!-- <template v-slot:append-outer>
                  <v-icon
                    style="color: currentColor"
                    size="1.25rem"
                    class="material-icons-round flex-2 icon-hover-bg is-light"
                    :class="{ 'font-weight-semibold': searchFoucus }"
                  >
                    mdi-magnify
                  </v-icon>
                </template> -->
              </v-text-field>
              <v-btn elevation="0" class="ml-3 primary-bg">{{
                $t('action.search')
              }}</v-btn>
            </div>
          </div>

          <!-- bg-transparent assets-event-details-table -->
          <v-data-table
            :search="searchVal"
            class="bg-transparent asset-detail-table mt-4"
            :class="{ 'scroll-bar-bg': mode === 'asset' }"
            :items-per-page="data.fullDigitalTwin.length"
            height="40vh"
            fixed-header
            item-key="code"
            hide-default-footer
            :headers="headers"
            :items="data.fullDigitalTwin"
          >
            <template v-slot:item.name="{ item }">
              <div
                v-if="item.name && item.name.length > 35"
                class="d-flex align-center"
              >
                <div v-show-tips class="td-showtips">
                  {{ item.name }}
                </div>
                 <v-btn v-copy="item.name" v-show-tips="$t('action.copy')" icon>
                  <vsoc-icon
                    type="fill"
                    size="16px"
                    icon="icon-fuzhi"
                  ></vsoc-icon>
                </v-btn>
              </div>
              <div v-else>{{ item.name }}</div>
            </template>
            <template v-slot:item.value="{ item }">
              <div
                v-if="item.value && item.value.length > 35"
                class="d-flex align-center"
              >
                <div v-show-tips class="td-showtips">
                  <div
                    :class="{
                      'font-weight-semibold': item.color === $alertColor0,
                    }"
                  >
                    <template
                      v-if="['5', '6', '7'].includes(item.signalValueType)"
                    >
                      <a
                        v-if="item.value"
                        :class="{
                          'text-info': $vuetify.theme.isDark,
                          'a-hover': isAHover(item.value),
                        }"
                        @click="onShowSub(item)"
                        >{{ item.value }}</a
                      >
                      <div v-else class="w-100 text-overflow-hide">N/A</div>
                    </template>
                    <template v-else>
                      <div class="w-100 text-overflow-hide">
                        {{ item.value }}
                        <span v-if="item.unit">{{ item.unit }}</span>
                      </div>
                    </template>
                  </div>
                </div>
                 <v-btn
                  v-copy="item.value"
                  v-show-tips="$t('action.copy')"
                  icon
                >
                  <vsoc-icon
                    type="fill"
                    size="16px"
                    icon="icon-fuzhi"
                  ></vsoc-icon>
                </v-btn>
                 <v-btn
                  v-if="item.id === 'vehicle_can_ids_event_messagehistory'"
                  v-show-tips="$t('action.decode')"
                  icon
                  @click.stop="goToDecode(item, data.fullDigitalTwin)"
                >
                  <vsoc-icon
                    type="fill"
                    size="16px"
                    icon="icon-tiaozhuan"
                  ></vsoc-icon>
                </v-btn>
              </div>
              <div v-else>
                <div
                  :class="[
                    { 'font-weight-semibold': item.color === $alertColor0 },
                    'd-flex align-center',
                  ]"
                >
                  <template
                    v-if="['5', '6', '7'].includes(item.signalValueType)"
                  >
                    <a
                      v-if="item.value"
                      :class="{
                        'text-info': $vuetify.theme.isDark,
                        'a-hover': isAHover(item.value),
                      }"
                      @click="onShowSub(item)"
                      >{{ item.value }}</a
                    >
                    <div v-else class="w-100 text-overflow-hide">N/A</div>
                  </template>
                  <template v-else>
                    <div class="w-100 text-overflow-hide">
                      {{ item.value }}
                      <span v-if="item.unit">{{ item.unit }}</span>
                    </div>
                  </template>
                  <v-btn
                    v-if="item.id === 'vehicle_can_ids_event_messagehistory'"
                    v-show-tips="$t('action.decode')"
                    icon
                    @click.stop="goToDecode(item, data.fullDigitalTwin)"
                  >
                    <vsoc-icon
                      type="fill"
                      size="16px"
                      icon="icon-tiaozhuan"
                    ></vsoc-icon>
                  </v-btn>
                </div>
              </div>
            </template>
            <template v-slot:item.signalTime="{ item }">
              <span v-if="item.signalTime">{{ item.signalTime | toDate }}</span>
              <span v-else>N/A</span>
            </template>
            <template v-slot:item.description="{ item }">
              <div
                v-if="item.description && item.description.length > 35"
                class="d-flex align-center"
              >
                <div v-show-tips class="td-showtips">
                  {{ item.description }}
                </div>
                 <v-btn
                  v-copy="item.description"
                  v-show-tips="$t('action.copy')"
                  icon
                >
                  <vsoc-icon
                    type="fill"
                    size="16px"
                    icon="icon-fuzhi"
                  ></vsoc-icon>
                </v-btn>
              </div>
              <div v-else>
                {{ item.description }}
              </div>
            </template>
          </v-data-table>
        </div>
      </v-expand-transition>

      <v-expand-transition v-else>
        <div v-show="showDetails && fields.length">
          <div class="d-flex align-end mt-2">
            <div class="flex-1 d-flex">
              <v-btn
                x-small
                elevation="0"
                v-show-tips="'下载所有内容'"
                @click="onDownLoad"
                class="primary-bg"
              >
                <vsoc-icon type="fill" icon="icon-xiazai"> </vsoc-icon>
              </v-btn>
              <v-chip
                small
                label
                class="color-base ml-3 bg-btn font-weight-normal"
              >
                {{ $t('global.pagination.total') }}
                {{ (fields && fields.length) || 0 }}
              </v-chip>
            </div>
            <div class="flex-2 justify-end d-flex">
              <v-text-field
                style="max-width: 20rem !important"
                v-model="searchVal"
                :label="$t('asset.axis.queryField')"
                hide-details
                outlined
                dense
                color="white"
                type="text"
                clearable
                @focus="searchFoucus = true"
                @blur="searchFoucus = false"
              >
              </v-text-field>
              <v-btn elevation="0" class="ml-3 primary-bg">{{
                $t('action.search')
              }}</v-btn>
            </div>
          </div>

          <v-data-table
            :search="searchVal"
            class="bg-transparent asset-detail-table mt-4"
            :class="{ 'scroll-bar-bg': mode === 'asset' }"
            :items-per-page="fields.length"
            height="40vh"
            fixed-header
            item-key="code"
            hide-default-footer
            :headers="headers"
            :items="fields"
          >
            <template v-slot:item.name="{ item }">
              <div
                v-if="item.name && item.name.length > 35"
                class="d-flex align-center"
              >
                <div v-show-tips class="td-showtips">
                  {{ item.name }}
                </div>
                 <v-btn v-copy="item.name" v-show-tips="$t('action.copy')" icon>
                  <vsoc-icon
                    type="fill"
                    size="16px"
                    icon="icon-fuzhi"
                  ></vsoc-icon>
                </v-btn>
              </div>
              <div v-else>{{ item.name }}</div>
            </template>
            <template v-slot:item.value="{ item }">
              <div
                v-if="item.value && item.value.length > 35"
                class="d-flex align-center"
              >
                <div v-show-tips class="td-showtips">
                  <div
                    :class="{
                      'font-weight-semibold': item.color === $alertColor0,
                    }"
                  >
                    <template
                      v-if="['5', '6', '7'].includes(item.signalValueType)"
                    >
                      <a
                        v-if="item.value"
                        :class="{
                          'text-info': $vuetify.theme.isDark,
                          'a-hover': isAHover(item.value),
                        }"
                        @click="onShowSub(item)"
                        >{{ item.value }}
                      </a>
                      <div v-else class="w-100 text-overflow-hide">N/A</div>
                    </template>
                    <template v-else>
                      <div class="w-100 text-overflow-hide">
                        {{ item.value }}
                        <span v-if="item.unit">{{ item.unit }}</span>
                      </div>
                    </template>
                  </div>
                </div>
                 <v-btn
                  v-copy="item.value"
                  v-show-tips="$t('action.copy')"
                  icon
                >
                  <vsoc-icon
                    type="fill"
                    size="16px"
                    icon="icon-fuzhi"
                  ></vsoc-icon>
                </v-btn>
                 <v-btn
                  v-if="item.id === 'vehicle_can_ids_event_messagehistory'"
                  v-show-tips="$t('action.decode')"
                  icon
                  @click.stop="goToDecode(item, fields)"
                >
                  <vsoc-icon
                    type="fill"
                    size="16px"
                    icon="icon-tiaozhuan"
                  ></vsoc-icon>
                </v-btn>
              </div>
              <div v-else>
                <div
                  :class="[
                    { 'font-weight-semibold': item.color === $alertColor0 },
                    'd-flex align-center',
                  ]"
                >
                  <template
                    v-if="['5', '6', '7'].includes(item.signalValueType)"
                  >
                    <a
                      v-if="item.value"
                      :class="{
                        'text-info': $vuetify.theme.isDark,
                        'a-hover': isAHover(item.value),
                      }"
                      @click="onShowSub(item)"
                      >{{ item.value }}
                    </a>
                    <div v-else class="w-100 text-overflow-hide">N/A</div>
                  </template>
                  <template v-else>
                    <div class="w-100 text-overflow-hide">
                      {{ item.value }}
                      <span v-if="item.unit">{{ item.unit }}</span>
                    </div>
                  </template>
                   <v-btn
                    v-if="item.id === 'vehicle_can_ids_event_messagehistory'"
                    v-show-tips="$t('action.decode')"
                    icon
                    @click.stop="goToDecode(item, fields)"
                  >
                    <vsoc-icon
                      type="fill"
                      size="16px"
                      icon="icon-tiaozhuan"
                    ></vsoc-icon>
                  </v-btn>
                </div>
              </div>
            </template>
            <template v-slot:item.signalTime="{ item }">
              <span v-if="item.signalTime">{{ item.signalTime | toDate }}</span>
              <span v-else>N/A</span>
            </template>
            <template v-slot:item.description="{ item }">
              <div
                v-if="item.description && item.description.length > 35"
                class="d-flex align-center"
              >
                <div v-show-tips class="td-showtips">
                  {{ item.description }}
                </div>
                 <v-btn
                  v-copy="item.description"
                  v-show-tips="$t('action.copy')"
                  icon
                >
                  <vsoc-icon
                    type="fill"
                    size="16px"
                    icon="icon-fuzhi"
                  ></vsoc-icon>
                </v-btn>
              </div>
              <div v-else>
                {{ item.description }}
              </div>
            </template>
          </v-data-table>
        </div>
      </v-expand-transition>

      <sub-sheet
        :vin="vin"
        :lastReceiveDate="lastReceiveDate"
        :subSignalValueType="subSignalValueType"
        :subTitle="subTitle"
        :subHeaders="subHeaders"
        :subList="subList"
        :value="isShowSub"
        @input="isShowSub = false"
      />
    </v-card-text>
  </v-card>
</template>

<script>
import { hexToRgb } from '@/@core/utils'
import VsocChart from '@/components/VsocChart.vue'
import VsocDialog from '@/components/VsocDialog.vue'
import excelUtil from '@/util/dealwithExcelUtil.js'
import { toDate } from '@/util/filters'
import SubSheet from './SubSheet.vue'
export default {
  name: 'AssetsEventDetails',
  props: {
    mode: {
      type: String,
      default: 'asset',
    },
    type: {
      type: String,
      default: 'assets',
    },
    data: Object,
    assetType: {
      type: String,
      default: () => '0',
    },
    vin: String,
    lastReceiveDate: String,
  },
  components: {
    VsocDialog,
    VsocChart,
    SubSheet,
  },

  data() {
    return {
      isAddressLoading: false,
      isShowSub: false,
      subTitle: '',
      subList: [],
      subHeaders: [],
      subSearch: '',
      subSignalValueType: '',

      showDetails: false,
      searchVal: '',
      searchFoucus: false,

      eventList: [],
      chartInstance: null,
    }
  },
  computed: {
    headers() {
      return [
        {
          text: this.$t('asset.axis.headers.signalName'),
          value: 'name',
          width: 120,
        },
        {
          text: this.$t('asset.axis.headers.value'),
          value: 'value',
          width: 100,
        },
        {
          text: this.$t('asset.axis.headers.signalSource'),
          value: 'signalTime',
          width: 160,
        },
        {
          text: this.$t('asset.axis.headers.desc'),
          value: 'description',
          width: 120,
        },
      ]
    },

    chartOption() {
      const primaryColor = this.$vuetify.theme.isDark
        ? this.$vuetify.theme.themes.dark.primary
        : this.$vuetify.theme.themes.light.primary
      const labelColor = this.$vuetify.theme.isDark
        ? this.$vuetify.theme.themes.dark.color
        : this.$vuetify.theme.themes.light.color
      const yData =
        this.data && this.data.statisticsData
          ? this.data.statisticsData.map(v => v.y)
          : []
      const rgb = hexToRgb(primaryColor)
      return {
        grid: {
          top: 20,
          left: '1%',
          right: '1%',
          bottom: '0%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data:
            this.data && this.data.statisticsData
              ? this.data.statisticsData.map(v => v.x)
              : [],
          axisLabel: { interval: 0, color: '#fff' },
          axisLine: {
            show: false,
          },
          axisTick: { show: false },
        },
        yAxis: {
          type: 'value',
          axisLabel: { show: false },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#ffffff33',
              // 实线
              // type: 'solid',
              // width: 0.5,
              // opacity: 0.8,

              // 虚线
              type: [5, 5],
              dashOffset: 0,
              shadowBlur: 0,
            },
          },
          // max: value => {
          //   return value.max + Number((value.max / 4).toFixed(1))
          // },
        },
        series: [
          {
            data: yData,
            type: 'bar',
            barWidth: 10,
            barGap: '25%',
            barMinHeight: 1,
            label: {
              show: true,
              color: labelColor,
              position: 'top',
              formatter: '{c}',
              fontSize: 12,
              color: '#fff',
            },
            itemStyle: {
              borderRadius: 10,
              color: {
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                type: 'linear',
                global: false,
                colorStops: [
                  { offset: 0, color: `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 1)` },
                  { offset: 1, color: `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0)` },
                ],
              },
            },
          },
        ],
      }
    },
    fields() {
      if (!this.data || !this.data.specialFields) {
        return []
      }

      const specialFieldsLength = this.data.specialFields.length
      const originalArr = [
        ...this.data.specialFields,
        ...this.data.defaultFields,
      ]
      const arr = this.initData(originalArr, specialFieldsLength)
      if (arr.length > 12 && this.mode !== 'digital-twin') {
        return arr.slice(0, 12)
      }
      return arr
    },
    activeEnum() {
      return this.$store.state.enums.enums.ActiveStatus
    },
    signalTypeMap() {
      return this.$store.state.enums.enums.SignalType
    },
    assetTypeEnum() {
      return this.$store.state.enums.enums.AssetType
    },
    digitalTwinFlag() {
      return this.$store.state.enums?.configs?.['Digital_Twin_Flag']?.[
        'Digital_Twin_Flag'
      ].propertyValue
    },
    alertStatus() {
      return this.$store.getters['enums/getAlertStatus']
    },
  },
  watch: {
    // data(newVal) {
    //   this.dataChange()
    //   newVal.fullDigitalTwin = this.initData(newVal.fullDigitalTwin)
    // },
    data: {
      handler(newVal) {
        this.dataChange()
        newVal.fullDigitalTwin = this.initData(newVal.fullDigitalTwin)
      },
      immediate: true,
    },
  },
  mounted() {
    this.$store.dispatch('global/searchSignal')
  },
  methods: {
    goAlert(id) {
      window.open(
        this.$router.resolve({ path: `/alert/detail?id=${id}` }).href,
        '_blank',
      )
    },
    goToDecode(item, tableData) {
      const modelCode = tableData.find(
        v => v.id == 'vehicle_vehicleidentification_modelcode',
      )
      const mainVersion = tableData.find(
        v => v.id == 'vehicle_can_ids_ruleset_version',
      )
      const indexId = tableData.find(
        v => v.id == 'vehicle_can_ids_event_algorithmindex',
      )
      const messageTime = tableData.find(
        v => v.id == 'vehicle_message_senttimestamp',
      )
      this.$router.push({
        path: '/decryptionTool/index',
        query: {
          modelCode: modelCode?.value,
          mainVersion: mainVersion?.value,
          indexId: indexId?.value,
          messageTime: messageTime?.value,
          defaultRawData: item.value,
        },
      })
    },
    initData(originalArr, specialFieldsLength = 0) {
      const arr = []
      if (originalArr && originalArr.length) {
        originalArr.forEach((item, index) => {
          let { value } = item
          // if (isEmpty(value) && item.signalValueType !== '0') {
          //   console.log('我到底进来了多少次')
          //   return []
          // }
          if (item.signalValueType === '4') {
            // 时间戳
            value = item.value ? toDate(item.value) : ''
          } else if (item.signalValueType === '1') {
            // 布尔型
            if (
              item.constrainsObject &&
              item.constrainsObject.false &&
              item.constrainsObject.true
            ) {
              value = value === 'true' && item.constrainsObject[item.value]
              value = value || item.value
            }
          } else if (
            item.signalValueType === '2' ||
            item.signalValueType === '3'
          ) {
            // 小数型或者整数型
            value = item.value
            item.unit = item.constrainsObject?.symbol
            // item.newValue = item.value + item.constrainsObject?.symbol
          } else if (['6', '7'].includes(item.signalValueType)) {
            if (!item.value) return (value = '')
            if (Array.isArray(item.value)) {
              item.list = item.value
            }
            let index = Array.isArray(item.list)
              ? item.list.length
              : Array.isArray(item.value)
              ? item.value.length
              : ''
            if (index !== '') {
              value = this.$tc('asset.axis.item', index || 0, { num: index })
            } else {
              value = ''
            }
            // if (item.list && Array.isArray(item.list)) {
            //   let index = (item.list?.length && item.list?.length) || 0
            //   value = this.$tc('asset.axis.item', index || 0, { num: index })
            // } else if (Array.isArray(item.value)) {
            //   item.list = item.value
            //   // 对象数组或者数组
            //   // value = item.value?.length && item.value?.length + '项'
            //   let index = (item.value?.length && item.value?.length) || 0
            //   value = this.$tc('asset.axis.item', index || 0, { num: index })
            // } else {
            //   value = ''
            // }
          } else if (item.signalValueType === '5') {
            if (!item.value) return (value = '')
            if (Array.isArray(Object.keys(item.value))) {
              item.list = item.value
            }
            let index = Array.isArray(Object.keys(item.list))
              ? Object.keys(item.list).length
              : Array.isArray(Object.keys(item.value))
              ? Object.keys(item.value).length
              : ''
            if (index !== '') {
              value = this.$tc('asset.axis.item', index, { num: index })
            } else {
              value = ''
            }
            // if (Array.isArray(Object.keys(item.value))) {
            //   item.list = item.value
            //   const len = Object.keys(item.list).length || 0
            //   value = this.$tc('asset.axis.item', len, { num: len })
            // } else {
            //   value = ''
            // }
          }
          arr.push({
            ...item,
            value,
            signalTime: item.signalTime ? toDate(item.signalTime) : '',
            newValue: ['2', '3'].includes(item.signalValueType)
              ? value + (item.unit || '')
              : ['5', '6', '7'].includes(item.signalValueType) && item.list
              ? JSON.stringify(item.list)
              : value,
            color:
              index < specialFieldsLength &&
              (this.data.type === 'Alarm' ? this.$alertColor0 : '#D35807'),
            _key: Symbol(item.id),
          })
        })
      }

      return arr
    },
    onDownLoad() {
      const data =
        this.mode === 'digital-twin' ? this.fields : this.data.fullDigitalTwin
      if (!data || data.length === 0) {
        this.$notify.info('error', this.$t('global.hint.downEmpty'))
        return
      }
      const initColumn = this.headers.map(item => {
        return {
          title: item.text,
          dataIndex: item.value,
          key: item.value === 'value' ? 'newValue' : item.value,
        }
      })
      excelUtil.exportExcel(initColumn, data, `${this.data.eventName}.xlsx`)
    },
    isAHover(value) {
      return /[1-9]/g.test(value)
    },
    onShowSub(item) {
      if (!this.isAHover(item.value)) {
        this.$notify.info('error', this.$t('asset.noneSubHint'))
        return
      }
      try {
        this.subTitle = item.name
        this.subSignalValueType = item.signalValueType
        if (item.signalValueType == '6') {
          this.subList = item.list
        } else if (item.signalValueType == '7') {
          this.subList = item.list.map(v => {
            return {
              name: v,
            }
          })
        } else {
          let signalList = this.$store.state.global.signals
          // 字典型
          // item.list是一个Object
          this.subList = []

          for (const key in item.list) {
            this.subList.push({
              name: signalList.find(v => v.id === key)?.name,
              key: key,
              value: item.list[key],
            })
          }
        }

        const allKeys = this.subList.reduce((keys, obj) => {
          Object.keys(obj).forEach(key => keys.add(key))
          return keys
        }, new Set())

        this.subHeaders = [...allKeys].map(key => {
          return {
            text: key,
            value: key.toString(),
          }
        })
        this.isShowSub = true
      } catch (err) {
        this.isShowSub = false
        this.$notify.info('error', this.$t('asset.noneSubHint'))
      }
    },
    $_initChart() {
      if (!this.data.statisticsData) return
      const option = {
        xAxis: {
          type: 'category',
          data: this.data.statisticsData.map(item => item.x),
          axisLabel: {
            color: 'rgba(255,255,255,0.8)',
            hideOverlap: true,
            fontSize: this.$getRemFont('md'),
          },

          // X轴样式
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.25)',

              // type: [5, 5],
              type: 'solid',
              dashOffset: 0,
              width: 1,
            },
          },

          // 刻度线
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          show: false,
        },
        grid: [
          {
            bottom: '0',
            left: '0',
            top: '28',
            right: '28',
            containLabel: true,
          },
        ],
        series: [
          {
            type: 'bar',
            barWidth: '18%',
            stack: 'Total',
            label: {
              show: true,
              position: 'top',
              fontSize: this.$getRemFont('md'),
              color: 'rgba(255,255,255,0.8)',
            },
            itemStyle: {
              color: this.$primaryColor,
            },
            data: this.data.statisticsData.map(item => item.y),
          },
        ],
      }

      this.$nextTick(() => {
        if (!this.chartInstance) {
          this.chartInstance = this.$echarts.init(
            document.getElementById('alert-chart'),
          )
        }
        this.chartInstance.setOption(option)
        this.chartInstance.resize()
      })
    },

    dataChange() {
      this.showDetails = this.digitalTwinFlag === 'true' ? true : false
      // && !this.chartInstance
      if (this.data.type === 'Statistics') {
        this.data.statisticsData = this.data.statisticsData.reverse()
        // setTimeout(() => {
        //   this.$_initChart()
        //   const resizeOb = new ResizeObserver(entries => {
        //     for (const entry of entries) {
        //       this.$echarts.getInstanceByDom(entry.target).resize()
        //     }
        //   })
        //   resizeOb.observe(document.getElementById('alert-chart'))
        // })
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.event-icon {
  width: 4rem;
  height: 4rem;
}
.td-showtips {
  max-width: 15rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.bg-gradient-secondary .theme--light.divider--dashed {
  opacity: 0.2;
}

.v-card:not(.digital-twin) {
  ::v-deep
    .theme--light.v-text-field--outlined:not(.v-input--is-focused):not(
      .v-input--has-state
    )
    > .v-input__control
    > .v-input__slot
    fieldset {
    color: rgba(#e6eaf2, 0.2);
  }
  ::v-deep .theme--light.v-label {
    color: rgba(#e6eaf2, 0.6);
  }
  ::v-deep .asset-detail-table.v-data-table td {
    color: $white !important;
  }
  ::v-deep
    .asset-detail-table.theme--light.v-data-table
    > .v-data-table__wrapper
    > table
    > tbody
    > tr:not(:last-child)
    > td:not(.v-data-table__mobile-row) {
    border-bottom: thin solid rgba(#e6eaf2, 0.2);
  }

  ::v-deep
    .asset-detail-table.scroll-bar-bg.theme--light
    .v-data-table__wrapper {
    &::-webkit-scrollbar-thumb {
      border-color: rgba(#677998, 0.9) !important;
    }
    &::-webkit-scrollbar-track {
      background: transparent !important;
    }
  }

  ::v-deep
    .asset-detail-table.theme--light.v-data-table.v-data-table--fixed-header
    thead
    th {
    background: #9fafcf !important;
    box-shadow: inset 0 -1px 0 rgba(231, 227, 252, 0.14);
  }
  ::v-deep
    .asset-detail-table.v-data-table.v-data-table--fixed-header
    thead
    th {
    // background: inherit;
    // background-image: linear-gradient(310deg, #627594, #a8b8d8) !important;

    color: rgba($white, 0.6) !important;
  }
}

// :v-deep .asset-detail-table {
//   &.theme--light.v-data-table.v-data-table--fixed-header thead th {
//     background: #9fafcf !important;
//     box-shadow: inset 0 -1px 0 rgba(231, 227, 252, 0.14);
//   }
//   &.v-data-table.v-data-table--fixed-header thead th {
//     // background: inherit;
//     // background-image: linear-gradient(310deg, #627594, #a8b8d8) !important;

//     color: rgba($white, 0.6) !important;
//   }
//   &.v-data-table td {
//     color: $white !important;
//   }
//   &.theme--light.v-data-table
//     > .v-data-table__wrapper
//     > table
//     > tbody
//     > tr:not(:last-child)
//     > td:not(.v-data-table__mobile-row) {
//     border-bottom: thin solid rgba(#e6eaf2, 0.2);
//   }

//   &.scroll-bar-bg.theme--light .v-data-table__wrapper::-webkit-scrollbar-thumb {
//     border-color: rgba(#677998, 0.9) !important;
//   }
// }

// .bg-transparent.theme--dark.v-data-table.v-data-table--fixed-header thead th {
//   background-color: #3a416f !important;
//   box-shadow: none;
//   border-bottom: 0.0625rem solid rgba(255, 255, 255, 0.7) !important;
// }

// .bg-transparent.theme--dark.v-data-table.v-data-table--fixed-header thead th {
//   background-color: #3a416f !important;
//   box-shadow: none;
//   border-bottom: 0.0625rem solid rgba(255, 255, 255, 0.7) !important;
// }

// .assets-event-details-table {
//   ::v-deep th {
//     border-color: rgba($color: #fff, $alpha: 0.7) !important;
//   }
//   ::v-deep td {
//     border-color: rgba($color: #fff, $alpha: 0.7) !important;
//   }
// }
.asset-event-box p {
  margin-bottom: 0 !important;
}
.event-list {
  display: flex;
  text-align: center;
  flex-wrap: wrap;
  .event-item {
    width: 16.66%;
    padding: 16px 0;

    hr.vertical {
      height: calc(100% - 32px);
      top: 16px;
    }

    &:last-child {
      hr {
        display: none;
      }
    }

    &:nth-of-type(6n) {
      hr {
        display: none;
      }
    }
  }
}
.a-text {
  color: var(--v-info-base);
}

.bg-transparent .white--text {
  color: var(--v-color-base) !important;
}
</style>
