// 定义枚举值，或者一些静态列表
import { activeColor, alertColor, inactiveColor } from '@/plugins/systemColor'

// export const engineTypeList=['燃油', '纯电', '混动', '插混', '未知']
export const engineTypeList = [
  'fuel',
  'pure electricity',
  'hybrid',
  'plug-in mix',
  'unknown',
]

export const idpsStatus = {
  0: {
    text: '运行',
    enText: 'normal',
    value: '0',
    color: activeColor,
    icon: 'mdi-check-circle',
  },
  1: {
    text: '停止',
    enText: 'abnormal',
    value: '1',
    color: alertColor[0],
    icon: 'mdi-close-circle',
  },
  2: {
    text: '规则错误',
    enText: 'error',
    value: '2',
    color: alertColor[0],
    icon: 'mdi-close-circle',
  },
  3: {
    text: '其他',
    enText: 'other',
    value: '3',
    color: alertColor[0],
    icon: 'mdi-check-circle',
  },
}

// 用户状态枚举值
export const userStatus = [
  { key: 0, value: '失效', color: inactiveColor },
  { key: 1, value: '正常', color: activeColor },
  { key: 2, value: '过期', color: alertColor[0] },
  { key: 3, value: '锁定', color: alertColor[1] },
]

// 响应状态枚举值
export const responseStatusList = [
  { key: '0', value: '启用', color: activeColor },
  { key: '1', value: '停用', color: inactiveColor },
]

// 告警级别枚举
export const alertLevel = {
  Critical: {
    color: alertColor[0],
    text: '严重',
    value: 'Critical',
  },
  High: {
    color: alertColor[1],
    text: '高',
    value: 'High',
  },
  Medium: {
    color: alertColor[2],
    text: '中',
    value: 'Medium',
  },
  Low: {
    color: alertColor[3],
    text: '低',
    value: 'Low',
  },
  Info: {
    color: alertColor[4],
    text: '信息',
    value: 'Info',
  },
  Test: {
    color: alertColor[5],
    text: '测试',
    value: 'Test',
  },
}

// 告警状态枚举
export const alertStatus = {
  waitHandle: {
    text: '待处理',
    color: 'rgb(0, 97, 250)',
    background: 'rgba(0, 97, 250, 0.2)',
  },
  handling: {
    text: '处理中',
    color: 'rgb(144, 20, 146)',
    background: 'rgba(144, 20, 146, 0.2)',
  },
  truePositive: {
    text: '有效事件',
    color: 'rgb(0, 151, 167)',
    background: 'rgba(0, 151, 167, 0.2)',
  },
  falsePositive: {
    text: '误报',
    color: 'rgb(68, 68, 68)',
    background: 'rgba(68, 68, 68, 0.2)',
  },
  notAnIssue: {
    text: '不是问题',
    color: 'rgb(97, 97, 97)',
    background: 'rgba(97, 97, 97, 0.2)',
  },
  duplicate: {
    text: '重复告警',
    color: 'rgb(209, 100, 152)',
    background: 'rgba(209, 100, 152, 0.2)',
  },
}

// 告警状态下拉列表 0待处理，1处理中，2有效事件，3误报，4不是问题，5重复警告
export const alertStatusList = [
  {
    text: '未处理状态',
    value: '-1',
    disabled: true,
  },
  {
    text: '待处理',
    value: '0',
    title: '未处理状态',
  },
  {
    text: '处理中',
    value: '1',
  },
  {
    text: '已处理状态',
    value: '-2',
    disabled: true,
  },
  {
    text: '有效事件',
    value: '2',
  },
  {
    text: '误报',
    value: '3',
  },
  {
    text: '不是问题',
    value: '4',
  },
  {
    text: '重复告警',
    value: '5',
  },
]

// 车辆态势枚举
export const vehicleStatus = {
  good: {
    colorCss: 'success',
    color: alertColor[3],
    text: 'A',
    value: 'good',
  },
  fine: {
    colorCss: 'warning',
    color: alertColor[2],
    text: 'B',
    value: 'fine',
  },
  normal: {
    colorCss: 'danger',
    color: alertColor[1],
    text: 'C',
    value: 'normal',
  },
  poor: {
    colorCss: 'error',
    color: alertColor[0],
    text: 'D',
    value: 'poor',
  },
}

// 车辆态势数组
export const vehicleStatusArray = [
  {
    color: alertColor[3],
    text: 'A',
    value: 'good',
  },
  {
    color: alertColor[2],
    text: 'B',
    value: 'fine',
  },
  {
    color: alertColor[1],
    text: 'C',
    value: 'normal',
  },
  {
    color: alertColor[0],
    text: 'D',
    value: 'poor',
  },
]

// 资产类型
export const assetTypeEnum = {
  Vehicle: {
    value: 'Vehicle',
    text: 'Vehicle',
  },
  Consumer: {
    value: 'Consumer',
    text: 'App',
  },
}

// 检测范围
export const extentEnum = {
  SingleAsset: {
    value: 'SingleAsset',
    text: '单资产',
    icon: 'mdi-checkbox-blank-circle',
  },
  MultiAsset: {
    value: 'MultiAsset',
    text: '多资产',
    icon: 'mdi-checkbox-multiple-blank-circle',
  },
}

// 检测类型
export const detectionTypeEnum = {
  Alert: {
    value: 'Alert',
    text: '警报',
    icon: 'mdi-flash-outline',
  },
  Flag: {
    value: 'Flag',
    text: '高频',
    icon: 'mdi-apache-kafka',
  },
}

// 活跃状态枚举
export const activeEnum = {
  Active: {
    text: '有效',
    value: 'Active',
    icon: 'mdi-check-circle',
    color: activeColor,
  },
  Inactive: {
    text: '无效',
    value: 'Inactive',
    icon: 'mdi-close-circle',
    color: inactiveColor,
  },
}

// 处置类型枚举
// 0：工单集成；1：邮件通知；2：系统消息；3：kafka集成
export const actionTypeEnum = {
  0: {
    text: '工单集成',
    value: '0',
  },
  1: {
    text: '邮件通知',
    value: '1',
  },
  2: {
    text: '系统消息',
    value: '2',
  },
  3: {
    text: 'Kafka集成',
    value: '3',
  },
}

// 检测器编辑的告警描述插入信号的key枚举
export const signalTypeEnum = {
  1: 'previousState',
  2: 'currentState',
  3: 'currentMessage',
}

// 处置结果状态枚举
export const actionResultEnum = {
  fail: {
    text: '失败',
    color: '#f44335',
    background: 'rgba(244,67,53, 0.2)',
  },
  init: {
    text: '处理中',
    color: '#1a73e8',
    background: 'rgba(26,115,232, 0.2)',
  },
  success: {
    text: '成功',
    color: '#4caf50',
    background: 'rgba(76,175,80, 0.2)',
  },
}

// 调度引擎执行方式
export const schedulerTypeList = [
  {
    text: '手动',
    value: 'manual',
  },
  {
    text: '周期性',
    value: 'cycle',
  },
  {
    text: '预约时间',
    value: 'onetime',
  },
]

// 调度引擎触发类型
export const triggerTypeList = [
  {
    text: '远程数据库',
    value: 'remoteSql',
  },
]

// 数字孪生信号类型
export const signalTypeList = [
  {
    text: '信号状态',
    value: 'FieldMetadata',
  },
  {
    text: '行为事件',
    value: 'EventField',
  },
]

// 数字孪生信号值类型枚举
export const signalValueTypeEnum = {
  STRING: {
    text: 'Text',
    value: 'STRING',
  },
  BOOLEAN: {
    text: 'Boolean',
    value: 'BOOLEAN',
  },
  DOUBLE: {
    text: 'Numeric',
    value: 'DOUBLE',
  },
  LONG: {
    text: 'Whole number',
    value: 'LONG',
  },
  TIMESTAMP: {
    text: 'Timestamp',
    value: 'TIMESTAMP',
  },
  DICTIONARY: {
    text: 'Structured',
    value: 'DICTIONARY',
  },
  OBJECT_ARRAY: {
    text: 'Structured list',
    value: 'OBJECT_ARRAY',
  },
  ARRAY: {
    text: 'Array',
    value: 'ARRAY',
  },
}
