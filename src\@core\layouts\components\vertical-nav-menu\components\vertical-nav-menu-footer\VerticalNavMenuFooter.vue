<template>
  <!-- ps-4 pe-5 pt-5 pb-2 -->
  <div
    class="vertical-nav-header d-flex align-center justify-space-between py-3 primary--text opacity-b1"
  >
    <v-slide-x-transition>
      <!-- v-if="$vuetify.breakpoint.lgAndUp" -->
      <div
        v-if="
          menuIsVerticalNavMini || (menuIsVerticalNavMini && isMouseHovered)
        "
        style="z-index: 9"
        class="w-100 h-full d-flex justify-center align-center"
        @click.stop="menuIsVerticalNavMini = !menuIsVerticalNavMini"
      >
        <vsoc-icon class="cursor-pointer" icon="icon-quxiaodingzhu"></vsoc-icon>
        <!-- <v-icon
          v-show="!menuIsVerticalNavMini"
          size="16"
          class="cursor-pointer"
        >
          {{ icons.mdiRecordCircleOutline }}
        </v-icon> -->

        <!-- <v-icon v-show="menuIsVerticalNavMini" size="16" class="cursor-pointer">
          {{ icons.mdiRadioboxBlank }}
        </v-icon> -->
      </div>
      <div
        v-else
        style="z-index: 9"
        @click.stop="menuIsVerticalNavMini = !menuIsVerticalNavMini"
        class="w-100 d-flex justify-center align-center"
      >
        <vsoc-icon class="cursor-pointer" icon="icon-dingzhu"></vsoc-icon>
      </div>
      <!-- <v-icon
        v-else
        size="16"
        class="d-inline-block"
        @click.stop="$emit('close-nav-menu')"
      >
        {{ icons.mdiClose }}
      </v-icon> -->
    </v-slide-x-transition>
  </div>
</template>

<script>
import useAppConfig from '@core/@app-config/useAppConfig'
import { mdiClose, mdiRadioboxBlank, mdiRecordCircleOutline } from '@mdi/js'
import themeConfig from '@themeConfig'
import { inject } from '@vue/composition-api'

export default {
  setup() {
    const { menuIsVerticalNavMini } = useAppConfig()
    const isMouseHovered = inject('isMouseHovered')

    return {
      menuIsVerticalNavMini,
      isMouseHovered,
      appLogo: themeConfig.app.logo,

      // Icons
      icons: {
        mdiClose,
        mdiRadioboxBlank,
        mdiRecordCircleOutline,
      },
    }
  },
}
</script>

<style lang="scss" scoped>
.app-title {
  font-size: 1.25rem;
  font-weight: 700;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: 0.3px;
}
::v-deep.v-icon {
  color: var(--v-color-base);
}

// ? Adjust this `translateX` value to keep logo in center when vertical nav menu is collapsed (Value depends on your logo)
.app-logo {
  transition: all 0.18s ease-in-out;
  .v-navigation-drawer--mini-variant & {
    transform: translateX(-4px);
  }
}
.nav-bottom {
  height: 38.21px;
  width: 100%;
  background: $blue-white-color;
  position: fixed;
  bottom: 0;
}
</style>
