const parameter = {
  currentTitle: '参数设置',
  headers: {
    id: '主键ID', //
    propertyName: '参数名称', //参数名称
    groupName: '参数组名', //参数组名
    propertyKey: '参数键名', //参数键值
    propertyType: '参数类型', //参数值类型 URL/BOOLEAN/STRING/INTEGER/ENCRYPTEDSTRING/NUMBER等
    propertyValue: '参数键值',
    isDefault: '系统内置', //系统内置（0是 1否）
    description: '备注', //描述
  },
  tip: '点击创建组名',
  tip1: '只能输入英文、数字和特殊字符-_.,()',
  del: {
    title: '删除参数',
    text: '确认删除参数：{0}？',
  },
}
export default parameter
