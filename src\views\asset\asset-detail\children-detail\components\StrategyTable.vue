<template>
  <v-card>
    <v-card-title
      >{{ $t('asset.idpsTab2.currentFirewallPolicy.title') }}
      <v-spacer></v-spacer>
      <a class="text-base text--secondary text-decoration-underline">{{
        $t('asset.idpsTab2.viewMore')
      }}</a>
    </v-card-title>
    <v-card-text>
      <v-data-table
        :headers="headers"
        :items="tableData"
        :item-class="rowClass"
        height="19.5rem"
        class="asset-children"
        item-key="id"
        :items-per-page="tableData.length"
        hide-default-footer
      ></v-data-table>
    </v-card-text>
  </v-card>
</template>

<script>
import { toDate } from '@/util/filters'
export default {
  name: 'StrategyTable',
  data() {
    return {
      tableData: [
        {
          id: 1,
          userId: 'POL_001',
          process: '白名单',
          shell: '进程白名单',
          cpu: '是',
          ram: toDate(new Date()),
        },
        {
          id: 2,
          userId: 'POL_002',
          process: '白名单',
          shell: '端口白名单',
          cpu: '是',
          ram: toDate(new Date()),
        },
        {
          id: 3,
          userId: 'POL_003',
          process: '白名单',
          shell: 'IP白名单',
          cpu: '是',
          ram: toDate(new Date()),
        },
        {
          id: 4,
          userId: 'POL_004',
          process: '白名单',
          shell: '服务白名单',
          cpu: '是',
          ram: toDate(new Date()),
        },
        {
          id: 5,
          userId: 'POL_005',
          process: '白名单',
          shell: '服务白名单',
          cpu: '是',
          ram: toDate(new Date()),
        },
      ],
    }
  },
  computed: {
    headers() {
      return [
        {
          text: this.$t('asset.idpsTab2.currentFirewallPolicy.headers.id'),
          value: 'userId',
          width: 60,
          align: 'center',
          sortable: false,
        },
        {
          text: this.$t('asset.idpsTab2.currentFirewallPolicy.headers.type'),
          value: 'process',
          width: 100,
          align: 'center',
          sortable: false,
        },
        {
          text: this.$t('asset.idpsTab2.currentFirewallPolicy.headers.detail'),
          value: 'shell',
          width: 100,
          align: 'center',
          sortable: false,
        },
        {
          text: this.$t(
            'asset.idpsTab2.currentFirewallPolicy.headers.isPolicy',
          ),
          value: 'cpu',
          width: 100,
          align: 'center',
          sortable: false,
        },
        {
          text: this.$t('signal.headers.lastUpdate'),
          value: 'ram',
          width: 120,
          align: 'center',
          sortable: false,
        },
      ]
    },
  },
  methods: {
    rowClass(record) {
      let index = this.tableData.findIndex(item => item.id === record.id)
      if (index % 2 === 0) {
        return 'bg-body'
      }
      return
    },
  },
}
</script>
<style scoped lang="scss"></style>
