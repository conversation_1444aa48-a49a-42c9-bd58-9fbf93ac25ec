import { isExternal } from '@/@core/utils/validation'
import { i18n } from '@/plugins/i18n'
import vuetify from '@/plugins/vuetify'
import store from '@/store'
import themeConfig from '@themeConfig'
import axios from 'axios'
import { ACCESS_TOKEN } from './constant'

// 切换lotus主题色
export const toggleLotus = index => {
  const currentPrimary = store.state.appConfig.appPrimary
  // store.commit('appConfig/UPDATE_GLOBAL_THEME_MODE', currentPrimary)
  // // 点击主题切换按钮时，先将 Primary 选择项切换为'default'解决样式冲突
  if (currentPrimary === 'lotus') {
    store.commit('appConfig/UPDATE_APP_PRIMARY', 'lotus')
    // document.documentElement.style.setProperty('--v-primary-base', '#EDE20B')
    document.documentElement.style.setProperty('--v-primary-base', '#fff200')
    document
      .getElementsByClassName('v-main')[0]
      .style.setProperty('--v-primary-base', '#000')

    // vm.$vuetify.theme.themes.dark.primary = '#323232'
    // document.getElementsByTagName('html')[0].className = currentPrimary
    document.documentElement.className = currentPrimary

    delete require.cache[require.resolve('@/assets/lotus-iconfont/iconfont')]
    delete require.cache[
      require.resolve('@/assets/lotus-iconfont/iconfont.css')
    ]

    require('@/assets/lotus-iconfont/iconfont')
    require('@/assets/lotus-iconfont/iconfont.css')
  } else {
    store.commit('appConfig/UPDATE_APP_PRIMARY', 'default')
    document.documentElement.style.removeProperty('--v-primary-base')
    document
      .getElementsByClassName('v-main')[0]
      .style.removeProperty('--v-primary-base')

    // document.getElementsByTagName('html')[0].className = ''
    document.documentElement.className = ''

    // document.getElementsByClassName('lotus-iconfont')[0].remove()
    delete require.cache[require.resolve('@/assets/iconfont/iconfont')]
    delete require.cache[require.resolve('@/assets/iconfont/iconfont.css')]

    require('@/assets/iconfont/iconfont')
    require('@/assets/iconfont/iconfont.css')
  }
}

export const changeTheme = (themeMode, isViewDark) => {
  let appSkinVariant = 'default'
  let isDark = false
  switch (themeMode) {
    case 'auto':
      isDark = isViewDark === 'dark' ? true : false
      appSkinVariant = isViewDark === 'semi-dark' ? 'semi-dark' : 'default'
      break
    case 'dark':
      isDark = true
      break
    case 'semi-dark':
      isDark = false
      appSkinVariant = 'semi-dark'
      break
    default:
      appSkinVariant = 'default'
      isDark = false
      break
  }
  return {
    appSkinVariant,
    isDark,
  }
}
/**
 * 暂无数据
 * @param {number} width
 * @param {number} height
 * @returns option
 */
export function emptyChartOptionFn(width = 150, height = 120) {
  return {
    title: {
      text: `{a|}\n{b|${i18n.t('global.noData')}}`,
      x: 'center',
      y: 'center',
      itemGap: 0,
      textStyle: {
        rich: {
          a: {
            height,
            width,
            backgroundColor: {
              image:
                'data:image/svg+xml;base64,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',
            },
          },
          b: {
            verticalAlign: 'top',
            fontSize: 12,
            lineHeight: 18,
            color: vuetify.framework.theme.isDark
              ? themeConfig.themes.dark.secondary
              : themeConfig.themes.light.secondary,
          },
        },
      },
      subtextStyle: {
        fontSize: 12,
      },
    },
  }
}
/**
 * 轮询函数
 * @param {fn} promiseFn 轮询函数
 * @param {number} wait 间隔
 * @param {id} timeId 计时器id
 */
export function poll(promiseFn, wait, timeId) {
  clearImmediate(timeId)
  timeId = setTimeout(() => {
    promiseFn().then(res => {
      if (res.code === 200) {
        poll()
      }
    })
  }, wait)
}
/**
 * 将file对象（.json文件）转为json对象
 * @param {File} file file对象
 */
export function fileToJson(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = res => {
      const { result } = res.target // 得到字符串
      const data = JSON.parse(result) // 解析成json对象
      resolve(data)
    } // 成功回调
    reader.onerror = err => {
      reject(err)
    } // 失败回调
    reader.readAsText(new Blob([file]), 'utf-8') // 按照utf-8编码解析
  })
}

/**
 * @param {function} fn 需要加、减的高度
 * @returns 表格高度
 */
export function setRemainingHeight(fn = () => 0) {
  let num = 18.4
  const fontSize = +getComputedStyle(window.document.documentElement)[
    'font-size'
  ].replace('px', '')
  if (fontSize >= 14) {
    num = 15.8
  }
  return document.documentElement.clientHeight - num * fontSize + fn()
}

export function deepClone(obj) {
  const result = typeof obj.splice === 'function' ? [] : {} // obj为list时typeof obj.splice为'function',为对象时typeof obj.splice为'undefined'
  if (obj && typeof obj === 'object') {
    for (const key in obj) {
      if (obj[key] && typeof obj[key] === 'object') {
        result[key] = deepClone(obj[key])
      } else {
        result[key] = obj[key]
      }
    }

    return result
  }

  return obj
}

// 判断日期类型
export function isDate(value) {
  if (
    value instanceof Date ||
    (typeof value === 'object' &&
      Object.prototype.toString.call(value) === '[object Date]')
  ) {
    return true
  }
  // if (new Date(value) !== 'Invalid Date') {
  //   return true
  // }
  let fmt = ''
  if (typeof value === 'string') {
    fmt = formatDate(value)
    let reg = /^(d{4}-d{2}-d{2} d{2}:d{2}:d{2})$/
    return reg.test(fmt)
  }
  return false
}

/**
 * 单位转换成秒
 * @param {String|Number} num
 * @param {String} type
 * @returns {String} 转换成的秒数
 */
export function toSeconds(num, type) {
  if (!num) {
    return
  }
  num = Number(num)
  let result = ''
  switch (type) {
    // 分钟
    case 'M':
      result = num * 60
      break

    // 小时
    case 'H':
      result = num * 60 * 60
      break

    // 天
    case 'D':
      result = num * 60 * 60 * 24
      break
    default:
      result = num
      break
  }

  return result
}

/**
 * 将 second 转换成 Days/Hours/Minutes
 * @param {number} time
 * @returns {string}
 */
export function formatSeconds(value) {
  let theTime = parseInt(value) // 需要转换的时间秒
  if (theTime === 0 || theTime === -1 || theTime < -1) {
    // 特殊处理 如何传入的负数
    return value
  }

  let theMinutes = 0 // 分
  let theHours = 0 // 小时
  let theDays = 0 // 天

  const unit = {
    day: {
      num: 60 * 60 * 24,
      type: 'D',
    },
    hour: {
      num: 60 * 60,
      type: 'H',
    },
    minute: {
      num: 60,
      type: 'M',
    },
  }

  let result = {
    num: parseInt(value),
    type: 'S',
  }

  for (const key in unit) {
    const { num, type } = unit[key]
    let rounding = parseInt(theTime / num)
    let remainder = theTime % num
    if (rounding > 0 && remainder === 0) {
      result = {
        num: rounding,
        type: type,
      }
      break
    }
  }

  return result
}

export function formatDate(date, fmt) {
  if (typeof date === 'string') {
    return date
  }

  if (!fmt) fmt = 'yyyy-MM-dd hh:mm:ss'

  if (!date || date == null) return null
  const o = {
    'M+': date.getMonth() + 1, // 月份
    'd+': date.getDate(), // 日
    'h+': date.getHours(), // 小时
    'm+': date.getMinutes(), // 分
    's+': date.getSeconds(), // 秒
    'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
    S: date.getMilliseconds(), // 毫秒
  }
  if (/(y+)/.test(fmt))
    fmt = fmt.replace(
      RegExp.$1,
      `${date.getFullYear()}`.substr(4 - RegExp.$1.length),
    )
  for (const k in o) {
    if (new RegExp(`(${k})`).test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? o[k] : `00${o[k]}`.substr(`${o[k]}`.length),
      )
    }
  }
  return fmt
}

export const handleDate = function (_date, _format) {
  // author: meizz
  if (!_date.getDate) _date = new Date(_date)
  _format = _format || 'yyyy-MM-dd hh:mm:ss'
  const o = {
    'y+': _date.getFullYear(),
    'M+': _date.getMonth() + 1, // 月份
    'd+': _date.getDate(), // 日
    'h+': _date.getHours(), // 小时
    'm+': _date.getMinutes(), // 分
    's+': _date.getSeconds(), // 秒
    'q+': Math.floor((_date.getMonth() + 3) / 3), // 季度
    S: _date.getMilliseconds(), // 毫秒
  }
  if (/(y+)/.test(_format)) {
    _format = _format.replace(
      RegExp.$1,
      `${_date.getFullYear()}`.substr(4 - RegExp.$1.length),
    )
  }
  for (const k in o) {
    if (new RegExp(`(${k})`).test(_format)) {
      _format = _format.replace(
        RegExp.$1,
        RegExp.$1.length == 1 ? o[k] : `00${o[k]}`.substr(`${o[k]}`.length),
      )
    }
  }

  return _format
}

// 获取当天时间
export function getCurrentData(format) {
  const myDate = new Date()
  const year = myDate.getFullYear()
  let month = myDate.getMonth() + 1 // 获取当前月份(0-11,0代表1月，所以要加1);
  let day = myDate.getDate() // 获取当前日（1-31）
  if (month < 10) {
    month = `0${month}`
  }
  if (day >= 0 && day <= 9) {
    day = `0${day}`
  }
  const thisDay = `${year}/${month}/${day} 23:59:59`

  return format ? handleDate(thisDay, format) : thisDay
}

// 获取本月第一天
export function getDateNow(format) {
  const myDate = new Date()
  const year = myDate.getFullYear()
  let month = myDate.getMonth() + 1 // 获取当前月份(0-11,0代表1月，所以要加1);
  let day = myDate.getDate() // 获取当前日（1-31）
  if (month < 10) {
    month = `0${month}`
  }
  if (day >= 0 && day <= 9) {
    day = `0${day}`
  }
  const firstDay = `${year}/${month}/01 00:00:00`

  return format ? handleDate(firstDay, format) : firstDay
}

// 获取近七天时间
export function getCurrentDay(format) {
  const myDate = new Date()
  const year = myDate.getFullYear()
  let month = myDate.getMonth() + 1 // 获取当前月份(0-11,0代表1月，所以要加1);
  let day = myDate.getDate() // 获取当前日（1-31）
  if (month < 10) {
    month = `0${month}`
  }
  if (day >= 0 && day <= 9) {
    day = `0${day}`
  }
  const thisDay = `${year}/${month}/${day} 23:59:59`
  const beforeDay =
    new Date(thisDay).getTime() - (1000 * 60 * 60 * 24 * 8 - 1000)

  return [
    beforeDay ? handleDate(beforeDay, format) : beforeDay,
    format ? handleDate(thisDay, format) : thisDay,
  ]
}

export function handleParams(params) {
  const res = {}
  for (const key in params) {
    if (Array.isArray(params[key])) {
      res[key] = params[key].join(',')
    } else {
      res[key] = params[key]
    }
  }

  return res
}

// 防抖
export function debounce(func, wait) {
  let timeout

  return function () {
    clearTimeout(timeout)
    timeout = setTimeout(func, wait)
  }
}

// JSON转Object
export function jsonToObject(str) {
  if (!str || typeof str !== 'string') {
    return str
  }
  try {
    const obj = JSON.parse(str)
    if (typeof obj === 'object' && obj) {
      return obj
    }

    return str
  } catch (e) {
    return str
  }
}

// 处理查询条件
export function handleFilterItem(searchKeyList) {
  searchKeyList.forEach(item => {
    // 如果此字段查询条件不为空
    if (
      this.query[item.key] &&
      (item.type === 'String' || item.type === 'Boolean')
    ) {
      this.filterList = this.filterList.filter(
        findItem => findItem.key !== item.key,
      )
      this.filterList.push({
        ...item,
        value: this.query[item.key],
        text: this.query[item.key],
        label: item.label,
      })
    } else if (item.type === 'Array' && this.query[item.key].length !== 0) {
      this.filterList = this.filterList.filter(
        findItem => findItem.key !== item.key,
      )
      if (this.query[item.key].length === 1) {
        this.query[item.key].forEach(v => {
          let text = v
          if (item.mapKey && this[item.mapKey] && this[item.mapKey][v]) {
            text = this[item.mapKey][v].text
              ? this[item.mapKey][v].text
              : this[item.mapKey][v]
          }
          this.filterList.push({
            ...item,
            value: v,
            text: text,
            label: item.label,
          })
        })
      } else {
        this.filterList.push({
          ...item,
          value: this.query[item.key],
          text: `+${this.query[item.key].length}`,
          label: item.label,
        })
      }
    } else if (
      item.type === 'time' &&
      this.query[item.mapKey[0]] &&
      this.query[item.mapKey[1]]
    ) {
      this.filterList = this.filterList.filter(
        findItem => findItem.key !== item.key,
      )
      this.filterList.push({
        ...item,
        value: item.mapKey,
        text: this.query[item.mapKey[0]] + '~' + this.query[item.mapKey[1]],
        label: item.label,
      })
    } else {
      // 如果此字段查询条件为空,筛选掉为空字段的条件列表
      this.filterList = this.filterList.filter(
        findItem => findItem.key !== item.key,
      )
    }
  })
}

//删除指定参数
export function updateURL() {
  const keyList = Object.keys(this.$route.query)
  keyList.forEach(key => {
    if (
      !this.query[key] ||
      (Array.isArray(this.query[key]) && this.query[key].length === 0)
    ) {
      delete this.$route.query[key]
      let url = new URL(window.location.href)
      let urlParams = new URLSearchParams(url.search)
      urlParams.delete(key)
      url.search = urlParams.toString()
      let newUrl = url.href
      history.replaceState({}, '', newUrl)
    }
  })
}

// 清除查询条件
export function clearFilterItem(item) {
  if (this.tableLoading) return true
  if (item.type === 'String') {
    this.query[item.key] = ''
  } else if (item.type === 'Array') {
    this.query[item.key] = []
    // const i = this.query[item.key].findIndex(v => v === item.value)
    // this.query[item.key].splice(i, 1)
  } else if (item.type === 'Boolean') {
    this.query[item.key] = false
  } else if (item.type === 'time') {
    this.query[item.mapKey[0]] = ''
    this.query[item.mapKey[1]] = ''
  }
  this.filterList = this.filterList.filter(
    findItem => findItem.key !== item.key,
  )
}

// 保存或者设置查询条件
export function handleQueryParams({ type, query, key }) {
  const params =
    jsonToObject(
      window.sessionStorage.getItem(process.env.BASE_URL + '_params'),
    ) || {}
  if (type === 'set') {
    params[key] = query
    window.sessionStorage.setItem(
      process.env.BASE_URL + '_params',
      JSON.stringify(params),
    )
  } else {
    const params = jsonToObject(
      window.sessionStorage.getItem(process.env.BASE_URL + '_params'),
    )
    if (params && params[key]) {
      return params[key]
    }

    return undefined
  }
}

// 让元素进入全屏
export function requestFullScreen(element) {
  if (element.requestFullScreen) {
    // 标准写法
    element.requestFullScreen()
  } else if (element.webkitRequestFullScreen) {
    // webkit 内核浏览器	谷歌 Safari
    element.webkitRequestFullScreen()
  } else if (element.mozRequestFullScreen) {
    // moz  	内核浏览器	火狐
    element.mozRequestFullScreen()
  } else if (element.msRequestFullscreen) {
    // ms ie浏览器	RequestFullscreen中 Screen中的s ie浏览器需要小写
    element.msRequestFullscreen()
  }
}

// 页面退出全屏
export function exitFullscreen() {
  // esc还是按钮
  if (!document.fullscreenElement) {
    return
  }
  if (document.exitFullscreen) {
    // 标准写法
    document.exitFullscreen()
  } else if (document.webkitCancelFullScreen) {
    // webkit 内核浏览器	谷歌 Safari
    document.webkitCancelFullScreen()
  } else if (document.mozCancelFullScreen) {
    // moz  	内核浏览器  火狐
    document.mozCancelFullScreen()
  } else if (document.msExitFullscreen) {
    // ms  	ie浏览器  取消全屏是Exit  不是Cancel
    document.msExitFullscreen()
  }
}

// 页面是否在全屏
export function isFullScreen() {
  if (document.fullScreen) {
    // 标准写法
    return document.fullScreen
  }
  if (document.webkitCancelFullScreen) {
    // webkit 内核浏览器	谷歌 Safari
    return document.webkitIsFullScreen
  }
  if (document.mozCancelFullScreen) {
    // moz  	内核浏览器  火狐
    return document.mozFullScreen
  }
}
export function formatSeconds1(value, type = 'String') {
  let theTime = parseInt(value) // 需要转换的时间秒
  if (theTime <= 0) {
    //特殊处理 如何传入的负数
    return i18n.tc('global.time.seconds', 1, [0])
  }
  let theMinutes = 0 // 分
  let theHours = 0 // 小时
  let theDays = 0 // 天
  if (theTime >= 60) {
    theMinutes = parseInt(theTime / 60) // 转换为分钟
    theTime = parseInt(theTime % 60)
    if (theMinutes >= 60) {
      //大于1h
      theHours = parseInt(theMinutes / 60) //转为为小时
      theMinutes = parseInt(theMinutes % 60)
      if (theHours >= 24) {
        //大于24小时
        theDays = parseInt(theHours / 24) //转为天
        theHours = parseInt(theHours % 24)
      }
    }
  }
  if (type === 'String') {
    let result = ''
    if (theTime >= 0 && theDays <= 0 && theHours <= 0) {
      // result = ' ' + parseInt(theTime) + ' Seconds'
      result =
        ' ' +
        i18n.tc('global.time.seconds', parseInt(theTime), [parseInt(theTime)])
    }
    if (theMinutes > 0) {
      // result = '' + parseInt(theMinutes) + ' Minutes' + result
      result =
        ' ' +
        i18n.tc('global.time.min', parseInt(theMinutes), [
          parseInt(theMinutes),
        ]) +
        result
    }
    if (theHours > 0) {
      // result = '' + parseInt(theHours) + ' Hours ' + result
      result =
        ' ' +
        i18n.tc('global.time.hours', parseInt(theHours), [parseInt(theHours)]) +
        result
    }
    if (theDays > 0) {
      // result = '' + parseInt(theDays) + ' Days ' + result
      result =
        ' ' +
        i18n.tc('global.time.day', parseInt(theDays), [parseInt(theDays)]) +
        result
    }

    // console.log(result)
    return result
  } else {
    return {
      Days: parseInt(theDays),
      Hours: parseInt(theHours),
      Minutes: parseInt(theMinutes),
    }
  }
}
export function getElapsedPercentageV2(totalTime, elapsedTime) {
  let theTotalTime = parseInt(totalTime) // 需要转换的时间秒
  let theElapsedTime = parseInt(elapsedTime) // 需要转换的时间秒
  const point = theElapsedTime / theTotalTime
  // let percentage = Number(point * 100).toFixed(2)
  let percentage = Number(point * 100)
  percentage = Math.round(percentage * 100) / 100
  //
  // obj.percentage = percentage
  // console.log(percentage)
  return percentage
}

export function getElapsedPercentageV3(totalTime, elapsedTime) {
  let theTotalTime = parseInt(totalTime) // 需要转换的时间秒
  let theElapsedTime = parseInt(elapsedTime) // 已用时间
  const point = theElapsedTime / theTotalTime
  // let percentage = Number(point * 100).toFixed(2)
  let percentage = Number(point * 100)
  percentage = Math.round(percentage * 100) / 100
  //
  // obj.percentage = percentage
  if (percentage > 100) {
    percentage = 100
  }
  // console.log(percentage)
  return percentage
}

export function progressColorFilter(value) {
  const percentage = parseInt(value)
  let color = '#cbcbcb'
  if (percentage === 0) {
    //灰色
    color = '#cbcbcb'
  } else if (percentage < 50 && percentage !== 0) {
    //绿色
    color = '#71e279'
  } else if (percentage >= 50 && percentage <= 70) {
    //黄色
    color = '#fcc742'
  } else if (percentage > 70 && percentage < 100) {
    //橙色
    color = '#fc8a3d'
  } else if (percentage >= 100) {
    //红色
    // console.log('=====================超过100000000000000000 满分')
    color = '#f95050'
  }
  return color
}

//16进制转rgb
export function hexToRgb(hexColor) {
  // 去除开头的 '#' 符号
  var colorCode = hexColor.replace('#', '')
  // 获取红、绿、蓝三个通道的十进制值
  var rgb1 = parseInt(colorCode.substring(0, 2), 16)
  var rgb2 = parseInt(colorCode.substring(2, 4), 16)
  var rgb3 = parseInt(colorCode.substring(4, 6), 16)
  return `${rgb1}, ${rgb2}, ${rgb3}`
}

//将图片转换为Base64编码
export function uploadImgToBase64(file) {
  return new Promise(function (resolve, reject) {
    let reader = new FileReader()
    let imgResult = ''
    reader.readAsDataURL(file)
    reader.onload = function () {
      imgResult = reader.result
    }
    reader.onerror = function (error) {
      reject(error)
    }
    reader.onloadend = function () {
      resolve(imgResult)
    }
  })
}

//跳转至第一个菜单路径
export function firstPath() {
  let myRouters = store.state.permission.routes
  let currentPath = myRouters.find(v => !isExternal(v.path))?.path || '/'
  return currentPath
  // router.push({
  //   path: currentPath,
  // })
}

export function breakWords(words = '', num, did = '\n') {
  let str = ''
  let len = words.length
  for (let i = 1; i <= len; i++) {
    str += words[i - 1]
    if (i % num === 0 && i !== len) {
      str += did
    }
  }
  return str
}

// MINUTES, HOURS, DAYS, WEEK, MONTH, YEARS(年/月/星期/天/小时/分钟)
export function timeAgo(dateString) {
  const endDate = new Date()
  const startDate = new Date(dateString)

  const timeDiff = endDate - startDate

  const days = timeDiff / (1000 * 60 * 60 * 24)

  const years = timeDiff / (1000 * 60 * 60 * 24 * 365)
  const months = timeDiff / (1000 * 60 * 60 * 24 * 30)
  const weeks = timeDiff / (1000 * 60 * 60 * 24 * 7)

  const hours = timeDiff / (1000 * 60 * 60)
  const minutes = timeDiff / (1000 * 60)
  const seconds = timeDiff / 1000

  // console.log(
  //   years,
  //   (new Date().getTime() - new Date(dateString).getTime()) / 86400000,
  //   days,
  //   timeDiff / (1000 * 60 * 60 * 24 * 7),
  //   weeks,
  // )
  if (parseInt(years) > 0) {
    return `${Math.round(years)} year${Math.round(years) === 1 ? '' : 's'} ago`
  } else if (parseInt(months) > 0) {
    return `${Math.round(months)} month${
      Math.round(months) === 1 ? '' : 's'
    } ago`
  } else if (parseInt(weeks) > 0) {
    return `${Math.round(weeks)} week${Math.round(weeks) === 1 ? '' : 's'} ago`
  } else if (parseInt(days) > 0) {
    return `${Math.round(days)} day${Math.round(days) === 1 ? '' : 's'} ago`
  } else if (parseInt(hours) > 0) {
    return `${Math.round(hours)} hour${Math.round(hours) === 1 ? '' : 's'} ago`
  } else if (parseInt(minutes) > 0) {
    return `${Math.round(minutes)} minute${
      Math.round(minutes) === 1 ? '' : 's'
    } ago`
  } else if (parseInt(seconds) > 0) {
    return `${Math.round(seconds)} second${
      Math.round(seconds) === 1 ? '' : 's'
    } ago`
  } else {
    return 'just now'
  }
}
