<template>
  <div class="box">
    <div class="box-header">
      {{ $t('screen.alerting')
      }}<span class="box-header-num">{{
        alarmPending.count | numberToFormat
      }}</span>
    </div>

    <v-row
      no-gutters
      class="box-chart d-flex justify-space-around w-100"
      style="padding-top: 4%"
    >
      <v-col
        v-for="(item, index) in option"
        :key="index"
        cols="auto"
        class="text-center d-flex flex-column align-center w-25"
      >
        <!-- <div class="fs-12">新增{{ item.addNum | num }}</div> -->
        <div class="flex-1 align-center w-100 justify-center">
          <div
            class="h-100 d-flex align-center w-100 dv9"
            style="padding: 9.5% 10% 13.5% 10%"
          >
            <!-- style="height: 100%; width: 100%" -->
            <dv-decoration-9 :style="dvStyle">{{
              numberToFormat(item.count)
            }}</dv-decoration-9>
          </div>
        </div>

        <div
          class="font-weight-medium fs-16"
          :style="'color:' + item.color"
          style="padding-top: 5%; padding-bottom: 10%"
        >
          {{ item.alarmLevelName }}
        </div>
      </v-col>
    </v-row>
    <!-- <template v-for="(item, index) in option">
        <div
          :key="index"
          class="text-center d-flex flex-column align-center"
          @click="goAlert(item)"
        >
          <div class="pb-2 fs-12">新增{{ item.addNum | num }}</div>
          <div class="flex-1 align-center w-25">
            <div class="h-100 d-flex align-center w-100">
              <dv-decoration-9 style="height: 100%; width: 100%">{{
                item.count
              }}</dv-decoration-9>
            </div>
          </div>

          <div
            class="pt-3 font-weight-medium fs-16"
            :style="'color:' + item.color"
          >
            {{ item.alarmLevelName }}
          </div>
        </div>
      </template> -->
  </div>
</template>

<script>
import { numberToFormat } from '@/util/filters'
import { getRoundSize } from './chart'
export default {
  name: 'LeftChart2',
  props: {
    alarmPending: {
      type: Object,
      default: () => {
        return {
          count: 0,
          levelData: [],
        }
      },
    },
  },
  computed: {
    dvStyle() {
      const len = getRoundSize(100)
      return {
        height: len + 'px',
        width: len + 'px',
      }
    },
    option() {
      let alertLevel = this.$store.state.enums.enums.AlarmLevel
      if (!alertLevel || alertLevel.length === 0) {
        return []
      }
      return this.alarmPending.levelData.map(item => {
        return {
          count: item.count,
          alarmLevel: item.level,
          alarmLevelName: alertLevel[item.level].text,
          color: alertLevel[item.level].color,
        }
      })
      // return [
      //   {
      //     addNum: 10,
      //     count: 481,
      //     alarmLevel: '0',
      //     alarmLevelName: alertLevel[0].text,
      //     color: '#FF385D',
      //   },
      //   {
      //     addNum: 15,
      //     count: 278,
      //     alarmLevel: '1',
      //     alarmLevelName: alertLevel[1].text,
      //     color: '#FF6B00',
      //   },
      //   {
      //     addNum: 0,
      //     count: 12,
      //     alarmLevel: '2',
      //     alarmLevelName: alertLevel[2].text,
      //     color: '#F0DA4C',
      //   },
      //   {
      //     addNum: 6,
      //     count: 377,
      //     alarmLevel: '3',
      //     alarmLevelName: alertLevel[3].text,
      //     color: '#32FDB8',
      //   },
      // ]
    },
  },
  data() {
    return {
      numberToFormat,
    }
  },
  methods: {
    goAlert(item) {
      this.$router.push({
        path: '/alerts',
        query: { isQuery: 1, alarmLevelList: [item.alarmLevel] },
      })
    },
  },
}
</script>
