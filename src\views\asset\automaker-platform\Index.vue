<template>
  <div>
    <bread-crumb></bread-crumb>
    <v-card tile class="main-content">
      <v-card-text class="pa-0">
        <div class="d-flex justify-space-between align-center mb-3">
          <div class="d-flex align-center">
            <v-btn
              v-has:automaker-platform-new
              color="primary"
              elevation="0"
              class="me-3"
              @click="add"
            >
              新增
            </v-btn>
          </div>
          <div class="d-flex align-end">
            <table-search
              :searchList="searchList"
              :searchQuery="query"
              @search="changeSize"
            ></table-search>
          </div>
        </div>
        <v-data-table
          fixed-header
          :items-per-page="query.pageSize"
          item-key="id"
          :height="tableHeight"
          hide-default-footer
          :headers="headers"
          :items="tableData"
          :loader-height="6"
          class="table border-radius-xl mt-3 thead-light"
          :loading="tableLoading"
        >
          <!-- 平台名称 -->
          <template v-slot:item.platform_name="{ item }">
            <div class="font-weight-medium" v-show-tips>
              {{ item.platform_name }}
            </div>
          </template>

          <!-- 平台类型 -->
          <template v-slot:item.platform_type="{ item }">
            <div>
              <v-chip
                v-for="type in getPlatformTypes(item.platform_type)"
                :key="type.value"
                small
                class="mr-1 mb-1"
                :color="type.color"
                text-color="white"
              >
                {{ type.text }}
              </v-chip>
            </div>
          </template>

          <!-- 已监管状态 -->
          <template v-slot:item.is_supervise="{ item }">
            <v-chip
              :color="item.is_supervise === '0' ? 'success' : 'grey'"
              :outlined="item.is_supervise !== '0'"
              small
              :text-color="item.is_supervise === '0' ? 'white' : 'grey'"
              class="supervise-chip"
              :class="{ 'supervise-weak': item.is_supervise !== '0' }"
            >
              {{ item.is_supervise === '0' ? '是' : '否' }}
            </v-chip>
          </template>

          <!-- 定级级别 -->
          <template v-slot:item.classification_level="{ item }">
            <v-chip
              small
              :color="getClassificationColor(item.classification_level)"
              text-color="white"
            >
              {{ item.classification_level }}级
            </v-chip>
          </template>

          <!-- 创建时间 -->
          <template v-slot:item.create_date="{ item }">
            {{ formatDate(item.create_date) }}
          </template>

          <!-- 更新时间 -->
          <template v-slot:item.update_date="{ item }">
            {{ formatDate(item.update_date) }}
          </template>

          <template v-slot:item.actions="{ item }">
            <v-btn
              v-has:automaker-platform-edit
              icon
              @click="onEdit(item)"
              v-show-tips="'编辑'"
            >
              <vsoc-icon
                type="fill"
                icon="icon-bianji"
                class="action-btn"
                size="x-large"
              >
              </vsoc-icon>
            </v-btn>
            <v-btn
              v-has:automaker-platform-del
              icon
              v-show-tips="'删除'"
              @click="onDel(item)"
            >
              <vsoc-icon
                type="fill"
                class="action-btn"
                icon="icon-shanchu"
                size="x-large"
              ></vsoc-icon>
            </v-btn>
          </template>
        </v-data-table>

        <!-- 分页器 -->
        <vsoc-pagination
          :page.sync="query.pageNum"
          :size.sync="query.pageSize"
          :total="tableDataTotal"
          @change-page="changePage"
          @change-size="changeSize"
        >
        </vsoc-pagination>
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
import {
  deleteAutomakerPlatform,
  getAutomakerPlatformList,
  // getAutomakerListForSelect, // 暂时不需要，搜索条件中没有车企筛选
} from '@/api/asset/automaker-platform'
import TableSearch from '@/components/TableSearch/index.vue'
import VsocPagination from '@/components/VsocPagination.vue'
import breadCrumb from '@/components/bread-crumb/index'
import { setRemainingHeight, formatDate } from '@/util/utils'
import { PAGESIZE } from '@/util/constant'

export default {
  name: 'AutomakerPlatformIndex',
  components: {
    VsocPagination,
    breadCrumb,
    TableSearch,
  },
  data() {
    return {
      tableData: [],
      tableDataTotal: 10,
      tableLoading: false,
      tableHeight: '34.5rem',
      // 查询条件
      query: {
        platform_name: '',
        is_supervise: '',
        automaker_id: '',
        pageNum: 1,
        pageSize: PAGESIZE,
      },
      headers: [
        {
          text: '平台名称',
          value: 'platform_name',
          width: '200px',
          sortable: false,
        },
        {
          text: '平台编码',
          value: 'platform_code',
          width: '150px',
          sortable: false,
        },
        {
          text: '备案号',
          value: 'registration_number',
          width: '150px',
          sortable: false,
        },
        {
          text: '定级级别',
          value: 'classification_level',
          width: '100px',
          sortable: false,
        },
        {
          text: '所属车企',
          value: 'automaker_name',
          width: '150px',
          sortable: false,
        },
        {
          text: '已监管',
          value: 'is_supervise',
          width: '100px',
          sortable: false,
        },
        {
          text: '创建时间',
          value: 'create_date',
          width: '150px',
          sortable: false,
        },
        {
          text: '更新时间',
          value: 'update_date',
          width: '150px',
          sortable: false,
        },
        {
          text: '操作',
          value: 'actions',
          width: '120px',
          sortable: false,
        },
      ],
      searchList: [
        {
          type: 'input',
          value: 'platform_name',
          text: '平台名称',
        },
        {
          type: 'select',
          value: 'is_supervise',
          text: '已监管',
          itemList: [
            { text: '是', value: '0' },
            { text: '否', value: '1' },
          ],
        },
      ],
      // 平台类型选项
      platformTypeOptions: [
        { value: '0', text: '联网车辆监控管理平台', color: 'primary' },
        { value: '1', text: '车联网信息服务平台', color: 'success' },
        { value: '2', text: '车联网OTA升级诊断服务平台', color: 'info' },
      ],
    }
  },
  created() {
    this.$_setTableHeight()
    this.$bus.$on('resize', this.$_setTableHeight)
    this.$_getTableData()
    // this.loadAutomakerOptions() // 暂时不需要，搜索条件中没有车企筛选
  },
  beforeDestroy() {
    this.$bus.$off('resize', this.$_setTableHeight)
  },
  methods: {
    $_setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = setRemainingHeight(
          this.$store.state.global.subMarginFn,
        )
      })
    },

    // 获取平台类型显示
    getPlatformTypes(typeArray) {
      if (!typeArray || !Array.isArray(typeArray)) return []
      return typeArray.map(type => {
        const option = this.platformTypeOptions.find(opt => opt.value === type)
        return option || { value: type, text: type, color: 'grey' }
      })
    },

    // 获取定级级别颜色
    getClassificationColor(level) {
      const colorMap = {
        1: 'error',
        2: 'warning',
        3: 'success',
      }
      return colorMap[level] || 'grey'
    },

    // 格式化日期
    formatDate(date) {
      return formatDate(date)
    },

    // 新增
    add() {
      this.$router.push('/automaker-platform/add')
    },

    // 编辑
    onEdit(record) {
      this.$router.push(`/automaker-platform/edit?id=${record.id}`)
    },

    // 删除
    onDel(record) {
      this.$swal({
        title: '删除平台',
        text: `是否确认删除平台：${record.platform_name}？`,
        icon: 'warning',
        reverseButtons: true,
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        customClass: {
          confirmButton: 'sweet-btn-primary',
        },
      })
        .then(async result => {
          if (result.isConfirmed) {
            try {
              const res = await deleteAutomakerPlatform({ id: record.id })
              if (res && res.code === 200) {
                this.$notify.info('success', '删除平台成功')
                this.$_getTableData()
              } else {
                const errorMsg = res?.msg || '删除平台失败'
                this.$notify.info('error', errorMsg)
              }
            } catch (error) {
              const errorMsg =
                error?.message || error || '删除平台失败，请稍后重试'
              this.$notify.info('error', errorMsg)
            }
          }
        })
        .catch(error => {
          // 处理确认框异常
          console.error('确认删除对话框异常：', error)
        })
    },

    // 分页变化
    changePage() {
      this.$_getTableData()
    },

    // 页面大小变化或搜索
    changeSize() {
      this.query.pageNum = 1
      this.$_getTableData()
    },

    async $_getTableData() {
      try {
        this.tableLoading = true
        this.tableData = []

        // 调用车企平台列表接口
        const res = await getAutomakerPlatformList(this.query)

        if (res && res.code === 200 && res.data && res.data.records) {
          // 处理接口返回的数据，转换字段名以匹配前端显示
          this.tableData = res.data.records.map(item => ({
            id: item.id,
            platform_name: item.platformName,
            platform_code: item.platformCode,
            registration_number: item.registrationNumber,
            description: item.description,
            platform_type: item.platformTypes || [], // 平台类型数组
            //picture_code: item.pictureCode,
            is_supervise: item.isSupervise,
            classification_level: item.classificationLevel,
            automaker_id: item.automakerId,
            automaker_name: item.automakerName || 'N/A', // 如果接口返回车企名称
            is_active: item.isActive,
            create_user: item.createUser,
            create_date: item.createDate,
            update_user: item.updateUser,
            update_date: item.updateDate,
          }))
          this.tableDataTotal = res.data.total || 0
        } else if (res && res.code !== 200) {
          // 接口返回错误码
          const errorMsg = res.msg || '获取平台列表失败'
          this.$notify.info('error', errorMsg)
          this.tableData = []
          this.tableDataTotal = 0
        } else {
          this.tableData = []
          this.tableDataTotal = 0
          if (!res?.data?.records || res.data.records.length === 0) {
            this.$notify.info('info', '暂无数据')
          }
        }
      } catch (error) {
        const errorMsg =
          error?.message || error || '获取平台列表失败，请稍后重试'
        this.$notify.info('error', errorMsg)
        this.tableData = []
        this.tableDataTotal = 0
      } finally {
        this.tableLoading = false
      }
    },

    // 加载车企选项 - 暂时不需要，搜索条件中没有车企筛选
    // async loadAutomakerOptions() {
    //   let automakerSearchItem = null
    //   try {
    //     // 找到车企选择项
    //     automakerSearchItem = this.searchList.find(
    //       item => item.value === 'automaker_id',
    //     )
    //     if (automakerSearchItem) {
    //       automakerSearchItem.loading = true
    //     }

    //     const res = await getAutomakerListForSelect()
    //     if (res && res.code === 200 && res.data && Array.isArray(res.data)) {
    //       const options = res.data.map(item => ({
    //         text: item.automakerName,
    //         value: item.id,
    //       }))

    //       if (automakerSearchItem) {
    //         automakerSearchItem.options = options
    //       }
    //     } else if (res && res.code !== 200) {
    //       // 接口返回错误码
    //       const errorMsg = res.msg || '加载车企选项失败'
    //       this.$notify.info('error', errorMsg)
    //     } else {
    //       // 数据格式异常
    //       if (automakerSearchItem) {
    //         automakerSearchItem.options = []
    //       }
    //     }
    //   } catch (error) {
    //     const errorMsg =
    //       error?.message || error || '加载车企选项失败，请稍后重试'
    //     this.$notify.info('error', errorMsg)
    //     if (automakerSearchItem) {
    //       automakerSearchItem.options = []
    //     }
    //   } finally {
    //     if (automakerSearchItem) {
    //       automakerSearchItem.loading = false
    //     }
    //   }
    // },
  },
}
</script>

<style scoped>
.logo-avatar img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.logo-placeholder {
  background-color: #f5f5f5;
}

.action-btn {
  color: #666;
}

.action-btn:hover {
  color: #1976d2;
}

/* 已监管状态样式优化 */
.supervise-chip {
  font-size: 11px;
}

.supervise-weak {
  opacity: 0.6;
  font-weight: normal;
}
</style>
