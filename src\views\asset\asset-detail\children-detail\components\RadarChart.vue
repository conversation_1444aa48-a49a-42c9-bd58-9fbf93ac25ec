<template>
  <v-card class="h-full">
    <v-card-title class="float-left">{{ title }}</v-card-title>
    <v-card-text v-empty-chart="isEmpty" class="h-full pt-6">
      <vsoc-chart
        v-if="!isEmpty"
        :echartId="echartId"
        :option="radarOption"
        @highlight="onHighlight"
      ></vsoc-chart>
    </v-card-text>
  </v-card>
</template>

<script>
import VsocChart from '@/components/VsocChart.vue'
export default {
  name: '<PERSON><PERSON><PERSON>',
  props: {
    title: {
      type: String,
    },
    echartId: {
      type: String,
      default: 'radar',
    },
    list: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  components: {
    Vsoc<PERSON>hart,
  },
  computed: {
    isEmpty() {
      return this.list.filter(v => v.count != 0 && v.count != null).length === 0
    },
    radarOption() {
      let min = 0
      let max = 100
      return {
        legend: { show: false },
        tooltip: {
          show: false,
          triggerOn: 'mousemove',
          position: 'inside',
          formatter: function () {
            return ''
          },
        },
        radar: {
          nameGap: 20,
          startAngle: 90,
          scale: true,
          axisName: {
            formatter: (name, indicator) => {
              const record = this.list.find(v => v.name === name)
              return `{a|${
                name.length >= 10 ? name.substring(0, 10) + '...' : name
              }}\n{b|${record.percent}}`
            },
            padding: [-10, -10],
          },
          axisLine: {
            lineStyle: {
              color: '#ebeef3',
            },
          },
          shape: 'circle',
          center: ['50%', '50%'],
          radius: '60%',
          triggerEvent: false,
          indicator: this.list.map(item => {
            return {
              min,
              max,
              name: item.name,
            }
          }),
        },
        series: [
          {
            name: '综合',
            type: 'radar',
            // symbol: 'none',
            areaStyle: {
              color: {
                type: 'radial',
                x: 0.1,
                y: 0.6,
                r: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(15, 126, 255, 0.05)', // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgba(15, 126, 255, 0.5)', // 100% 处的颜色
                  },
                ],
              },
            },
            data: [
              {
                value: this.list.map(item => item.format),
                name: '综合',
              },
            ],
          },
        ],
      }
    },
  },
  methods: {
    onHighlight(params, myChart) {
      let _this = this
      let num = params.event.topTarget.__dimIdx
      if (num === undefined) {
        _this.radarOption.tooltip.show = false
        _this.radarOption.tooltip.formatter = function () {
          return ''
        }
      } else {
        _this.radarOption.tooltip.show = true
        _this.radarOption.tooltip.formatter = function (params) {
          return (
            _this.radarOption.radar.indicator[num].name +
            ':' +
            params.data.value[num] +
            '%'
          )
        }
      }
      myChart.setOption(this.radarOption)
    },
  },
}
</script>
<style scoped lang="scss"></style>
