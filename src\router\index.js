// import 'nprogress/nprogress.css'
import { isExternal } from '@/@core/utils/validation'
import { generateName, i18n } from '@/plugins/i18n'
import staticRouter from '@/router/static-router'
import store from '@/store'
import { getLocalStorage } from '@/util/localStorage'
import { changeTheme } from '@/util/utils'
import themeConfig from '@themeConfig'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import Vue from 'vue'
import VueRouter from 'vue-router'
import { getToken } from '../util/token'
const noPermissionWhiteList = [
  '/login',
  '/register',
  '/reset-password',
  '/forgot-password',
  '/error-404',
  '/grid/shareCanvas',
]

Vue.use(VueRouter)

const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location, onResolve, onReject) {
  if (onResolve || onReject)
    return originalPush.call(this, location, onResolve, onReject)

  return originalPush.call(this, location).catch(err => err)
}
const routes = staticRouter

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes,
  scrollBehavior() {
    return { x: 0, y: 0 }
  },
})
router.beforeEach(async (to, _, next) => {
  // console.log(`从${_.path}跳转到${to.path}`)
  NProgress.start()

  // return next()
  // const token = localStorage.getItem('token')
  const token = getToken()
  if (token) {
    //  || to.path === '/error-404'
    if (to.path === '/login') {
      next()
    } else {
      const { menus } = store.state.permission
      if (menus && menus.length > 0) {
        let allCodes = store.state.permission.menuCodes
        //判断to.path是否有权限编码
        if (allCodes && allCodes.includes(to.meta?.code)) {
          next()
        } else {
          if (_.path === '/login') {
            //找到第一个有权限的路由
            let currentPath =
              store.state.permission.routes.find(v => !isExternal(v.path))
                ?.path || '/'
            next({ path: currentPath, replace: true })
          } else {
            next()
          }
        }
      } else {
        try {
          if (!store.state.enums.enums) {
            await store.dispatch('enums/getEnums')
          }
          if (!store.state.enums.configs) {
            await store.dispatch('enums/getAllConfig')
          }

          await store.dispatch('permission/generateRoutes')
          next({ ...to, replace: true })
          // if (_.path === '/login' && !_.query) {
          //   console.log(8)
          // }
          // console.log(_, to, 9)
          // //如果是edit或者detail结尾的,判断路由中是否包含:mode形式
          // if (_.path === '/login') {
          //   let endPath = ''
          //   if (to.path.endsWith('/add')) {
          //     endPath = router
          //       .getRoutes()
          //       .find(
          //         v =>
          //           v.path ===
          //           `${to.path.slice(0, to.path.indexOf('add'))}:mode`,
          //       )
          //   } else if (to.path.endsWith('/new')) {
          //     endPath = router
          //       .getRoutes()
          //       .find(
          //         v =>
          //           v.path ===
          //           `${to.path.slice(0, to.path.indexOf('new'))}:mode`,
          //       )
          //   } else if (to.path.endsWith('/edit')) {
          //     endPath = router
          //       .getRoutes()
          //       .find(
          //         v =>
          //           v.path ===
          //           `${to.path.slice(0, to.path.indexOf('edit'))}:mode`,
          //       )
          //   } else if (to.path.endsWith('/detail')) {
          //     endPath = router
          //       .getRoutes()
          //       .find(
          //         v =>
          //           v.path ===
          //           `${to.path.slice(0, to.path.indexOf('detail'))}:mode`,
          //       )
          //   }
          //   if (router.getRoutes().filter(v => v.path === to.path).length) {
          //     next({ ...to, replace: true })
          //   } else if (endPath) {
          //     router.push({ path: to.fullPath }).catch(err => {})
          //   } else {
          //     let currentPath =
          //       myRouters.find(v => !isExternal(v.path))?.path || '/'
          //     next({ path: currentPath, replace: true })
          //   }
          //   // let endPath = ''
          //   // if (to.path.endsWith('/edit')) {
          //   //   endPath = router
          //   //     .getRoutes()
          //   //     .find(
          //   //       v =>
          //   //         v.path ===
          //   //         `${to.path.slice(0, to.path.indexOf('edit'))}:mode`,
          //   //     )
          //   // }
          //   // if (to.path.endsWith('/detail')) {
          //   //   endPath = router
          //   //     .getRoutes()
          //   //     .find(
          //   //       v =>
          //   //         v.path ===
          //   //         `${to.path.slice(0, to.path.indexOf('detail'))}:mode`,
          //   //     )
          //   // }
          //   // if (
          //   //   endPath ||
          //   //   router.getRoutes().filter(v => v.path === to.path).length
          //   // ) {
          //   //   next({ ...to, replace: true })
          //   // } else {
          //   //   // 无首页权限（/posture），无法查看菜单
          //   //   // throw '权限不足，请联系管理员授权'
          //   //   let currentPath =
          //   //     myRouters.find(v => !isExternal(v.path))?.path || '/'
          //   //   next({ path: currentPath, replace: true })
          //   //   // let currentPath = myRouters[0].path
          //   //   // next({ path: currentPath, replace: true })
          //   // }
          // } else {
          //   next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
          // }

          // await store.dispatch('enums/getEnums')

          // await store.dispatch('permission/getUserBasicInfo')
          // let bool =
          //   myRouters.filter(v => v.path === to.path)?.length || staticRouter.filter(v => v.path === to.path)?.length
          // console.log('to', to)
          // if (myRouters && Array.isArray(myRouters) && myRouters.length > 0 && bool) {
          //   next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
          // } else {
          //   router.app.$notify.info('error', '权限不足，请联系管理员授权')
          //   next()
          //   // next({ name: 'auth-login' })
          //   // location.reload()

          //   // 刷新页面，否则下次的to.name==='error-404'
          //   // 已废：手输404跳转出错
          //   // let timeId = setTimeout(() => {
          //   // location.reload()
          //   //   timeId = null
          //   // }, 100)
          //   // window.reload() // router环境不存在window
          // }
        } catch (err) {
          console.log('error========', err)
          router.app.$notify.info('error', err)
          store.dispatch('user/logOut').then(res => {
            store.dispatch('permission/clearPermission')
            next({ path: '/login' })
          })
        }
      }
    }
  } else if (noPermissionWhiteList.indexOf(to.path) !== -1) {
    next()
  } else {
    next({
      path: '/login',
      query: { redirect: to.fullPath }, // 将跳转的路由path作为参数，登录成功后跳转到该路由
    })
  }

  NProgress.done()
})

router.afterEach(to => {
  const globalThemeMode =
    getLocalStorage('GLOBAL_THEME_MODE') ||
    store.state.appConfig.globalThemeMode
  const { appSkinVariant, isDark } = changeTheme(
    globalThemeMode,
    to.meta.isDark,
  )
  router.app.$vuetify.theme.dark = isDark
  router.app.$vuetify.theme.appSkinVariant = appSkinVariant
  store.commit('appConfig/UPDATE_APP_SKIN_VARIANT', appSkinVariant)
  // 给body添加一个class，目的是为了区分light和dark主题模式
  document.getElementsByTagName('body')[0].className = router.app.$vuetify.theme
    .dark
    ? 'theme--dark'
    : 'theme--light'
  if (to.name && to.meta.enTitle) {
    document.title = `${themeConfig.app.name}-${generateName(
      to.name,
      to.meta.enTitle,
    )}`
  } else {
    document.title = i18n.t('projectName')
  }

  if (getToken() && store.state.global.userIdByAllList.length === 0) {
    store.dispatch('global/loadUserList', 'admin')
  }
  NProgress.done()
})

export default router

// 重置路由
export const resetRouter = function () {
  // 替换现有router的routes
  router.matcher = new VueRouter({
    routes: staticRouter,
  }).matcher
}
