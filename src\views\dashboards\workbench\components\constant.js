import { toRecentSubDate } from '@/util/timezone'

export const defaultDay = 7

export const ISO_FORMAT = 'yyyy-MM-dd'

export const defaultQueryFn = () => {
  // return {
  //   startDate: format(subDays(new Date(), defaultDay - 1), ISO_FORMAT),
  //   endDate: format(new Date(), ISO_FORMAT),
  // }
  return toRecentSubDate(defaultDay, 'Days', ISO_FORMAT)
}

export const defaultHoursQueryFn = () => {
  // return {
  //   startDate: format(subDays(new Date(), defaultDay - 1), ISO_FORMAT),
  //   endDate: format(new Date(), ISO_FORMAT),
  // }
  return toRecentSubDate(24, 'Hours', '', '_format')
}

export const tooltip = {
  trigger: 'axis',
  axisPointer: {
    type: 'line',
  },

  formatter: '{a}: {c}',
  textStyle: {
    color: '#fff',
  },
  borderColor: 'transparent',
  backgroundColor: 'rgba(0, 0, 0, 0.5)',
}

export const textStyle = {
  color: '#A1A6B1',
  fontSize: '1.1667rem',
}

export const grid = {
  top: '5%',
  left: '2%',
  right: '2.5%',
  bottom: '0%',
  containLabel: true,
}
