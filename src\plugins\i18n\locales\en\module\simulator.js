const simulator = {
  currentTitle: 'Scenarios',
  headers: {
    name: 'Name',
    desc: 'Description',
    messages: 'Messages',
    updateDate: 'Last Updated',
  },
  copySimulator: 'Copy Scenario',
  simulation: 'Simulation Scenario',
  edit: {
    simulateMessage: 'Simulated Messages',
    messageType: {
      online: 'Online editing',
      upload: 'File upload',
    },
    signalType: 'Signal Type',
    addSubkey: 'Add subkey',
    date: 'Date',
    time: 'Time',
    add: 'Add',
  },
  hint: {
    formatError: 'Data format error!',
    jsonError: 'Simulated Message is not in valid JSON format, please check!',
    assetTypeEmpty: 'Please select the asset type first',
    assetTypeRequired: 'Asset type is required',
    messageEmpty: 'Simulated message cannot be empty',
  },
  swal: {
    title: 'Delete @:simulator.simulation',
    text: 'Are you sure to delete @.lower:simulator.simulation:{name}？',
  },
  exec: {
    select: 'Select Scenario',
    selected: 'Selected Scenario',
    channel: 'Data Channel',
    assetMode: 'Asset Mode',
    dateModel: 'Time Mode',
    execution: 'Execution',
    assetOption: {
      new: 'New',
      newText: 'New: Each run will generate a new asset',
      original: 'Original',
      originalText:
        'Original: Each run will use the assets specified in the file',
    },
    dateOption: {
      now: 'Now',
      nowText: 'Now: This mode will generate a signal for the current date',
      original: 'Original',
      originalText:
        'Original: This mode will generate signals with the date specified in the file',
    },
    message: '{num} message | {num} messages',
    hint1: 'Please select a Scenario!',
    hint2: 'No results found!',
    search: 'Search scenario',
    result: 'Execution Result',
    success: 'The Scenario simulation was executed successfully!',
    error: 'The Scenario simulation was executed failed!',
    executing: 'Executing...',
    name: 'Scenario',
    totalMessage: 'Total messages',
  },
  custom: 'Custom Scenario',
  json: 'Upload JSON file',
}

export default simulator
