export default [
  {
    path: '/',
    redirect: '/posture',
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/common/Login.vue'),
    meta: {
      layout: 'blank',
    },
  },

  {
    path: '/error-404',
    name: 'error-404',
    hidden: true,
    component: () => import('@/views/common/Error404.vue'),
    meta: {
      layout: 'blank',
      resource: 'Public',
    },
  },
  // {
  //   path: '/forgot-password',
  //   name: '忘记密码',
  //   hidden: true,
  //   component: () => import('@/views/common/ForgotPassword.vue'),
  //   meta: {
  //     layout: 'blank',
  //     resource: 'Public',
  //   },
  // },
  // {
  //   path: '/reset-password',
  //   name: '重置密码',
  //   hidden: true,
  //   component: () => import('@/views/common/ResetPassword.vue'),
  //   meta: {
  //     layout: 'blank',
  //     resource: 'Public',
  //   },
  // },
  {
    path: '/grid/shareCanvas',
    name: '画布分享',
    hidden: true,
    component: () => import('@/views/grid/viewGrid/detail.vue'),
    meta: {
      layout: 'blank',
    },
  },
  // 车企管理临时路由（用于测试）
  // {
  //   path: '/automaker',
  //   name: '车企管理',
  //   component: () => import('@/views/asset/automaker/Index.vue'),
  //   meta: {
  //     layout: 'content',
  //     code: 'automaker-management',
  //   },
  // },
  // {
  //   path: '/automaker/add',
  //   name: '新增车企',
  //   component: () => import('@/views/asset/automaker/AutomakerEdit.vue'),
  //   meta: {
  //     layout: 'content',
  //     code: 'automaker-add',
  //     navActiveLink: '车企管理',
  //   },
  // },
  // {
  //   path: '/automaker/edit',
  //   name: '编辑车企',
  //   component: () => import('@/views/asset/automaker/AutomakerEdit.vue'),
  //   meta: {
  //     layout: 'content',
  //     code: 'automaker-edit',
  //     navActiveLink: '车企管理',
  //   },
  // },
]
