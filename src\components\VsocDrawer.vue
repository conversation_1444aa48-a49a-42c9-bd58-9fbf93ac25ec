<!-- 右侧抽屉 -->
<template>
  <!-- hide-overlay -->
  <!-- width="100%" rounded-lg -->
  <v-navigation-drawer
    v-model="isShow"
    fixed
    :temporary="temporary"
    :width="width"
    right
    class="vsoc-drawer cus-navigation-drawer v-navigation-drawer--temporary"
    :class="{ 'is-full-sreen': isFullScreen }"
    @input="$emit('input', $event)"
    :style="{ zIndex: zIndex }"
  >
    <template v-slot:prepend>
      <!-- color="primary"
        dark -->
      <div>
        <v-toolbar
          class="cus-toolbar px-2 bg-card"
          height="62"
          min-height="62"
          elevation="0"
          flat
          tile
        >
          <v-toolbar-title>
            <span class="primary--text text-ml font-weight-semibold">{{
              topTitle
            }}</span>
            <slot name="title"></slot>
          </v-toolbar-title>
          <v-spacer></v-spacer>
          <slot name="right-title"></slot>
          <v-btn
            v-if="fullscreen && !isFullScreen"
            v-show-tips="'全屏'"
            icon
            @click="toggleChangeFullScreen"
          >
            <v-icon size="16" dense>mdi-fullscreen</v-icon>
          </v-btn>
          <v-btn
            v-show="isFullScreen"
            v-show-tips="'关闭'"
            class="mr-2"
            text
            icon
            @click="toggleChangeFullScreen"
          >
            <v-icon size="16" dense> mdi-fullscreen-exit </v-icon>
          </v-btn>
          <v-btn class="mr-n4" text icon @click="$_close">
            <v-icon size="16" dense> mdi-close </v-icon>
          </v-btn>
          <!-- <div
            class="d-flex justify-space-between align-center px-6 pt-6 pb-5 flex-fill"
          >
            <div class="d-flex align-center">
              <span class="primary--text text-ml font-weight-semibold">{{
                topTitle
              }}</span>
              <slot name="title"></slot>
            </div>
            <div class="d-flex align-center">
              <slot name="right-title"></slot>
              <v-btn
                v-if="fullscreen && !isFullScreen"
                v-show-tips="'全屏'"
                icon
                @click="toggleChangeFullScreen"
              >
                <v-icon size="16" dense>mdi-fullscreen</v-icon>
              </v-btn>
              <v-btn
                v-show="isFullScreen"
                v-show-tips="'关闭'"
                class="mr-2"
                text
                icon
                @click="toggleChangeFullScreen"
              >
                <v-icon size="16" dense> mdi-fullscreen-exit </v-icon>
              </v-btn>
              <v-btn class="mr-n4" text icon @click="$_close">
                <v-icon size="16" dense> mdi-close </v-icon>
              </v-btn>
            </div>
          </div> -->
        </v-toolbar>
        <v-divider></v-divider>
      </div>
    </template>
    <div
      class="vsoc-drawer__content overflow-y px-6 pt-2"
      :class="{ 'vsoc-drawer__full': hideFooter }"
    >
      <slot></slot>
    </div>
    <!-- 底部按钮 -->
    <div
      v-if="!hideFooter"
      class="vsoc-drawer__footer d-flex justify-end border-top py-4 px-6"
    >
      <template v-if="!$slots.footer">
        <v-btn
          v-if="!hideCancelBtn"
          class="font-weight-normal px-6 py-2 btn-secondary btn-outline-secondary"
          depressed
          outlined
          @click="$_cancel"
          color="secondary"
        >
          {{ cancelBtnText || $t('action.cancel') }}
        </v-btn>
        <v-btn
          v-if="!hideResetBtn"
          class="ml-4 font-weight-normal px-6 py-2 btn-secondary btn-outline-secondary"
          depressed
          outlined
          @click="$_reset"
          color="secondary"
        >
          {{ resetBtnText || $t('action.reset') }}
        </v-btn>
        <v-btn
          v-if="!hideConfirmBtn"
          :loading="confirmLoading"
          class="ml-4 px-6 py-2 btn-primary"
          color="primary"
          @click="$_confirm"
        >
          {{ confirmBtnText || $t('action.confirm') }}
        </v-btn>
      </template>
      <slot name="footer"></slot>
    </div>
  </v-navigation-drawer>
</template>

<script>
export default {
  name: 'VsocDrawer',
  components: {},
  props: {
    value: Boolean,
    title: String,
    zIndex: {
      type: String,
      default: '9999',
    },
    width: {
      type: [String, Number],
      // default: '100%',
      default: '360',
    },

    // 隐藏确认按钮
    hideConfirmBtn: {
      type: Boolean,
      default: false,
    },

    // 隐藏取消按钮
    hideCancelBtn: {
      type: Boolean,
      default: false,
    },
    // 隐藏重置按钮
    hideResetBtn: {
      type: Boolean,
      default: true,
    },
    // 隐藏底部按钮
    hideFooter: {
      type: Boolean,
      default: false,
    },

    // 确认按钮文字
    confirmBtnText: {
      type: String,
      // default: '确认',
    },

    // 取消按钮文字
    cancelBtnText: {
      type: String,
      // default() {
      //   // 不可使用箭头函数
      //   return this.$t('action.cancel')
      // },
    },

    resetBtnText: {
      type: String,
    },

    // 点击抽屉以外的区域是否自动关闭
    temporary: {
      type: Boolean,
      default: true,
    },
    // 是否全屏
    fullscreen: {
      type: Boolean,
      default: () => false,
    },
  },
  computed: {
    topTitle() {
      if (this.title === 'new') {
        return this.$t('global.drawer.addTitle', {
          cur: this.$generateMenuTitle(this.$route.meta).slice(0, -2),
        })
      } else if (this.title === 'edit') {
        return this.$t('global.drawer.editTitle', {
          cur: this.$generateMenuTitle(this.$route.meta).slice(0, -2),
        })
      } else if (this.title === 'search') {
        return this.$t('global.drawer.searchTitle')
      } else if (this.title === 'grant') {
        return this.$t('global.drawer.grantTitle')
      } else {
        return this.title
      }
    },
  },
  watch: {
    value(val) {
      this.isShow = val
    },
    isShow(val) {
      if (!val) {
        this.$emit('close', val)
      }
    },
  },
  data() {
    return {
      isShow: false,
      confirmLoading: false,
      isFullScreen: this.fullscreen,
    }
  },
  created() {},
  methods: {
    input(e) {
      console.log('默认的input', e)
    },
    $_cancel() {
      this.$emit('input', false)
      this.$emit('click:cancel')
      this.$emit('close', false)
    },
    $_reset() {
      this.$emit('click:reset')
    },
    $_close() {
      this.$emit('input', false)
      this.$emit('click:close')
      this.$emit('close', false)
    },

    // 确认的时候会默认加载且不会关闭弹框，如果要关闭弹框和关闭加载就执行第1个参数回调函数
    $_confirm() {
      this.setLoading(true)
      this.$emit('click:confirm', (loading = false, dialog = false) => {
        this.setLoading(loading)
        this.$emit('input', dialog)
      })
    },

    setLoading(bool) {
      this.confirmLoading = bool
    },

    toggleChangeFullScreen() {
      this.isFullScreen = !this.isFullScreen
    },
  },
}
</script>

<style lang="scss" scoped>
.vsoc-drawer {
  &.cus-navigation-drawer {
    max-width: 65rem !important;
    z-index: 10;
  }

  &.is-full-sreen {
    max-width: 100% !important;
    width: 100vw !important;
  }

  ::v-deep.v-navigation-drawer__content {
    position: relative;
    // .vsoc-drawer__content:not(.vsoc-drawer__footer) {
    //   height: calc(100% - 0.3rem);
    // }
    .vsoc-drawer__full {
      height: calc(100% - 0.3rem) !important;
    }
    .vsoc-drawer__content {
      height: calc(100% - 70px);
    }

    // .vsoc-drawer__footer {
    //   position: absolute;
    //   left: 0;
    //   bottom: 0;
    //   width: 100%;
    //   // background: inherit;
    //   // background: #f5f6fa;
    //   // height: 66px;
    //   // box-shadow: 0 -4px 4px rgba(0, 0, 0, 0.1);
    // }

    .v-form {
      .row {
        display: contents !important;
      }
    }
  }
}
</style>
