const asset = {
  headers: {
    assetId: 'Asset ID',
    model: 'Model',
    currentSituation: 'Posture',
    newAlert: 'Unhandled Alerts',
    assetGroup: 'Groups',
    firstRegistrationTime: 'First Seen',
    lastActiveTime: 'Last Seen',
    lastPosition: 'Last Location',
    count: 'Alerts',
    year: 'Vehicle Year',
  },
  swal: {
    title: 'Delete Asset',
    text: 'Please confirm that you need to delete the {id} asset; after deletion, the data cannot be recovered!',
    cancel: 'The asset has been undeleted: {id}',
  },
  template: {
    hint: 'Download the vehicle information template',
    text: 'Vehicle Information Import Template',
  },
  alertStatus: 'Alert Status',
  assetEventAxis: 'Asset Timeline',
  selectAssetGroup: 'Select Asset Group',
  closeAutoRefresh: 'Auto-refresh is turned off!',
  noGrant:
    'No page permissions, please contact the administrator for authorization!',
  setRefresh: 'Set refresh frequency',
  investigationAdvance: 'Advanced Investigation',
  axis: {
    item: '{num} item | {num} items',
    frequency: '{num} time | {num} times',
    view: 'View digital twin',
    queryField: 'Query Field',
    headers: {
      signalName: 'Signal Name',
      value: 'Value',
      signalSource: 'Signal Source Time',
      desc: 'Description',
    },
  },
  idps: {
    posture: 'Cyber Posture',
    status: 'Unhandled Alerts',
    version: 'IDPS version number',
    os: 'System Name',
    typeTotal: 'Alert Types',
    typeDis: 'Alert Tags Distribution',
    alertDis: 'Alert Severity Distribution',
    point: '{num} point | {num} points',
  },
  recentActivity: 'Recent activity',
  tabName0: 'Parts Situation',
  tabName1: 'Part Status',
  idpsTab2: {
    eventDistribution: 'Event Type Distribution TOP5',
    ipDistribution: 'Access IP Distribution TOP5',
    ipDistribution: 'Access IP Distribution TOP5',
    ruleDistribution: 'Idps Protection Rule Distribution',
    ruleTotal: 'protection total',
    ruleUpdates: 'Rule Updates',
    ruleUpdatesMore: 'View more rule updates',
    updates: 'Updates',
    equipmentApplication: 'Equipment Application',
    processDetails: {
      title: 'Process Details',
      userTotal: 'Current total number of users',
      processTotal: 'Current total number of processes',
      headers: {
        user: 'User',
        process: 'Process',
        startCommand: 'Start Command',
        cpu: 'CPU Occupancy',
        ram: 'Ram Occupancy',
        rom: 'Rom Occupancy',
      },
    },
    listeningPort: 'Listening Port',
    viewMore: 'View more',
    currentFirewallPolicy: {
      title: 'Current firewall policy',
      headers: {
        id: 'Firewall rule ID',
        type: 'Type',
        detail: 'Strategy Details',
        isPolicy: 'Yes/No',
      },
    },
    eventDetailList: {
      title: 'IDPS Event Details List',
      headers: {
        time: 'Occurrence Time',
        type: 'Event Type',
        contnet: 'Event Content',
        count: 'Occurrence Times',
      },
    },
  },
  noneSubHint: 'No sub-signal yet!',
  fileHint:
    'The size of a single attachment should not exceed 10M; attachment format requirements: xls,xlsx',
}

export default asset
