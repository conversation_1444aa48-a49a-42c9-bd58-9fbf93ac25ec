@import '~@core/preset/preset/variables.scss';

@import '~@core/preset/preset/mixins.scss';

.auth-wrapper {
  display: flex;
  min-height: calc(var(--vh, 1vh) * 100);
  width: 100%;
  flex-basis: 100%;
  align-items: center;
  color: #ffffff9e;

  // common style for both v1 and v2
  a {
    text-decoration: unset;
    color: inherit;
  }

  ::v-deep.v-label,
  ::v-deep.v-icon {
    color: #ffffff9e !important;
  }
  ::v-deep.v-text-field > .v-input__control > .v-input__slot:before {
    border: 1px solid #ffffff26;
  }
  ::v-deep.v-input input,
  .theme--light.v-input textarea {
    color: #ffffff9e;
  }

  // auth v1
  &.auth-v1 {
    align-items: center;
    justify-content: center;
    overflow: hidden;
    padding: 1.5rem;

    .auth-mask-bg {
      position: absolute;
      bottom: 0;
      width: 100%;
    }
    .auth-tree,
    .auth-tree-3 {
      position: absolute;
    }
    .auth-tree {
      bottom: 0;
      left: 0;
    }
    .auth-tree-3 {
      bottom: 0;
      right: 0;
    }

    // auth card
    .auth-inner {
      width: 28rem;
      z-index: 1;
      .auth-card {
        padding: 0.9375rem 0.875rem;
      }
    }
  }

  //auth v2
  &.auth-v2 {
    align-items: flex-start;

    .auth-inner {
      height: calc(var(--vh, 1vh) * 100);
      width: 100%;
      margin: 0;

      .brand-logo {
        text-decoration: unset;
        position: absolute;
        top: 1.83rem;
        left: 2.5rem;
        z-index: 1;
      }

      .auth-row {
        height: 100%;
        .auth-illustrator-wrapper {
          width: 100%;
          height: 100%;
        }
      }

      .auth-bg {
        .v-card {
          max-width: 26rem;
          margin-left: auto;
          margin-right: auto;
        }
      }

      .auth-mask-bg {
        position: absolute;
        bottom: 0;
        width: 100%;
      }

      .auth-tree {
        left: 0;
        left: 2.125rem;
        bottom: 2.688rem;
        position: absolute;
      }
    }
  }
}

@include theme--child(auth-wrapper) using ($material) {
  &.auth-v2 {
    .auth-inner {
      .auth-bg {
        background: map-deep-get($material, 'cards');
      }
    }
  }
}

@media (max-width: 600px) {
  // auth bg and tree hide in sm screen
  .auth-v1 {
    .auth-tree,
    .auth-tree-3,
    .auth-mask-bg {
      display: none;
    }
  }
}
