<template>
  <div>
    <codemirror
      ref="cm"
      v-model="newCode"
      :options="cmOptions"
      v-bind="$attrs"
      v-on="$listeners"
      :class="{ 'CodeMirror-readonly': readOnly }"
    />
  </div>
</template>

<script>
import { codemirror } from 'vue-codemirror'
// require styles
import 'codemirror/addon/fold/brace-fold'
import 'codemirror/addon/fold/foldcode'
import 'codemirror/addon/fold/foldgutter'
import 'codemirror/addon/fold/foldgutter.css'
import 'codemirror/lib/codemirror.css'
import 'codemirror/mode/javascript/javascript'
// JSON错误检查
import 'codemirror/addon/lint/lint.css'
import 'codemirror/addon/lint/lint.js'
// 需要依赖全局的jsonlint，不是很优雅
import 'codemirror/addon/lint/json-lint.js'
import 'codemirror/addon/lint/yaml-lint.js'
//及时自动更新，配置里面也需要设置autoRefresh为true
import 'codemirror/addon/display/autorefresh'
// 支持括号自动匹配
import 'codemirror/addon/edit/closebrackets.js'
import 'codemirror/addon/edit/matchbrackets.js'
// 引入dark主题
import 'codemirror/theme/duotone-dark.css'

// 全屏
import 'codemirror/addon/display/fullscreen'

// 引入jsonlint

import jsonlint from 'jsonlint-mod'
export default {
  name: 'VsocCodeMirror',
  components: {
    codemirror,
  },
  props: {
    code: {
      type: [Array, Object, String],
      default: () => [],
    },
    mode: {
      type: String,
      default: 'application/json',
    },
    // reHeight: {
    //   type: Function,
    // },
    readOnly: {
      type: Boolean,
      default: () => false,
    },
    height: {
      type: [String, Number],
    },
  },
  data() {
    return {
      resizeObserver: '',
      newCode: '[]',
      // code: '[]',
      cmOptions: {
        // 语言及语法模式
        mode: 'application/json',
        // 主题
        theme: 'duotone-dark',
        autoRefresh: true,
        line: true, // 显示函数
        lint: true, // 校验
        matchBrackets: true, // 括号匹配显示
        autoCloseBrackets: true, // 输入和退格时成对
        indentUnit: 2, // 缩进单位，默认2
        // 软换行
        lineWrapping: true,
        // tab宽度
        tabSize: 4,
        lineNumbers: true,
        lineWrapping: true,
        foldGutter: true,
        smartIndent: true, // 智能缩进
        gutters: [
          'CodeMirror-linenumbers',
          'CodeMirror-foldgutter',
          'CodeMirror-lint-markers', // 实现语法报错
        ],
      },
    }
  },

  // 不能使用computed定义newCode
  // computed: {
  //   newCode: {
  //     get() {
  //       return (
  //         (this.code && JSON.stringify(JSON.parse(this.code), null, 2)) || '{}'
  //       )
  //       // JSON.stringify(JSON.parse(this.code), null, 2)
  //       // return JSON.stringify(this.code, null, 2)
  //     },
  //     set(newVal) {
  //       return newVal
  //     },
  //   },
  // },
  watch: {
    readOnly: {
      handler(val) {
        if (!val) return
        this.$nextTick(() => {
          this.$refs.cm.codemirror.setOption('readOnly', true)
          // 高度自适应
          this.$refs.cm.codemirror.setSize('100%', 'auto')
        })
      },
      immediate: true,
    },
    mode: {
      handler(newMode) {
        this.cmOptions.mode = newMode || 'application/json'
      },
      immediate: true,
    },
    code: {
      handler(newVal) {
        const str = newVal || []
        if (this.mode === 'text/javascript') {
          this.newCode = newVal
        } else {
          this.newCode = JSON.stringify(str, null, 2)
        }
      },
      immediate: true,
    },
    '$vuetify.theme.dark': {
      handler(newVal) {
        if (newVal) {
          this.cmOptions.theme = 'duotone-dark'
        } else {
          this.cmOptions.theme = 'idea'
        }
      },
      deep: true,
      immediate: true,
    },
  },
  beforeCreate() {
    window.jsonlint = jsonlint
  },
  mounted() {
    this.$nextTick(() => {
      this.$refs.cm.codemirror.setSize('100%', this.height || 'auto')
    })
  },
  // created() {
  //   this.$nextTick(() => {
  //     this.onResizeObserver()
  //   })
  // },
  // beforeDestroy() {
  //   const ele = document.querySelector('.v-form')
  //   if (ele) {
  //     this.resizeObserver.unobserve(ele)
  //   } else {
  //     this.resizeObserver.disconnect()
  //   }
  // },
  methods: {
    // onResizeObserver() {
    //   const _this = this

    //   this.resizeObserver = new ResizeObserver(entries => {
    //     _this.setHeight(_this.reHeight)
    //   })
    //   const ele = document.querySelector('.v-form')
    //   this.resizeObserver.observe(ele)
    // },
    getCode() {
      return JSON.parse(this.newCode)
    },
    setHeight(fn = () => 0) {
      if (this.readOnly) {
        return
      }
      this.$nextTick(() => {
        const panelHeight = document
          .querySelectorAll('.v-expansion-panel')[0]
          .getBoundingClientRect().height
        const headerHeight = document
          .querySelectorAll('.v-expansion-panel-header')[1]
          .getBoundingClientRect().height
        const content = document.querySelector(
          '.vsoc-drawer .v-navigation-drawer__content .vsoc-drawer__content',
        )
        const fontSize = +getComputedStyle(window.document.documentElement)[
          'font-size'
        ].replace('px', '')
        const num = 1.8 * fontSize * 3

        const contentHeight = content.getBoundingClientRect().height - num
        const fnNum = fn()
        let height = contentHeight - panelHeight - headerHeight - fnNum
        if (height <= 0) {
          height = 300
        }
        this.$refs.cm.codemirror.setSize('100%', height)
      })
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .CodeMirror-scroll {
  cursor: text;
  min-height: 300px;
}

::v-deep .CodeMirror-readonly .CodeMirror-cursor {
  display: none !important;
}
</style>
