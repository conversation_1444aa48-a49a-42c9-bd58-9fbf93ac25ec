// import { postForm, request, vsocPath } from '../../util/request'

// 获取消息列表
export function getMessageListDt(params) {
  return {
    content: [
      {
        id: 1267,
        createDate: '2023-03-10 10:11:57',
        updateDate: '2023-03-10 10:11:57',
        createUser: 'currentUser',
        type: 1,
        typeName: '公告',
        unRead: true,
        readCount: 20,
        unReadCount: 10,
        status: '0',
        statusName: '未读',
        title: '发动机过热发动机过热发动机过热发动机过热',
        content:
          '告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警',
        receiverType: '1',
        receiverName: '全部人',
        receivers: [],
      },
      {
        id: 1263,
        createDate: '2023-03-19 09:11:57',
        updateDate: '2023-03-10 10:11:57',
        unRead: false,
        createUser: 'currentUser',
        type: 2,
        typeName: '工单',
        readCount: 20,
        unReadCount: 10,
        status: '1',
        statusName: '已读',
        title: '发动机过热发动机过热发动机过热发动机过热',
        content:
          '告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警',
        receiverType: '2',
        receiverName: '用户组',
        receivers: ['1631215135605391360'],
      },
      {
        id: 1264,
        createDate: '2023-03-15 09:11:57',
        updateDate: '2023-03-10 10:11:57',
        createUser: 'currentUser',
        type: 3,
        typeName: '告警',
        unRead: true,
        readCount: 20,
        unReadCount: 10,
        status: '1',
        statusName: '已读',
        title: '发动机过热发动机过热发动机过热发动机过热',
        content:
          '告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警',
        receiverType: '3',
        receiverName: '指定用户',
        receivers: [
          'summer',
          'compute',
          'gfh',
          'yy2',
          'zhangxinxin',
          'lyg',
          'currentUser',
        ],
      },
      {
        id: 123,
        createDate: '2023-03-15 09:11:57',
        updateDate: '2023-03-10 10:11:57',
        createUser: 'currentUser',
        type: 3,
        typeName: '告警',
        unRead: true,
        readCount: 20,
        unReadCount: 10,
        status: '0',
        statusName: '未读',
        title: '发动机过热发动机过热发动机过热发动机过热',
        content:
          '告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警',
        receiverType: '3',
        receiverName: '指定用户',
        receivers: [
          'summer',
          'compute',
          'gfh',
          'yy2',
          'zhangxinxin',
          'lyg',
          'currentUser',
        ],
      },
      {
        id: 124,
        createDate: '2023-03-15 09:11:57',
        updateDate: '2023-03-10 10:11:57',
        createUser: 'currentUser',
        type: 3,
        typeName: '告警',
        unRead: true,
        readCount: 20,
        unReadCount: 10,
        status: '1',
        statusName: '已读',
        title: '发动机过热发动机过热发动机过热发动机过热',
        content:
          '告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警',
        receiverType: '3',
        receiverName: '指定用户',
        receivers: [
          'summer',
          'compute',
          'gfh',
          'yy2',
          'zhangxinxin',
          'lyg',
          'currentUser',
        ],
      },
      {
        id: 122,
        createDate: '2023-03-15 09:11:57',
        updateDate: '2023-03-10 10:11:57',
        createUser: 'currentUser',
        type: 3,
        typeName: '告警',
        unRead: true,
        readCount: 20,
        unReadCount: 10,
        status: '1',
        statusName: '已读',
        title: '发动机过热发动机过热发动机过热发动机过热',
        content:
          '告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警',
        receiverType: '3',
        receiverName: '指定用户',
        receivers: [
          'summer',
          'compute',
          'gfh',
          'yy2',
          'zhangxinxin',
          'lyg',
          'currentUser',
        ],
      },
      {
        id: 121,
        createDate: '2023-03-15 09:11:57',
        updateDate: '2023-03-10 10:11:57',
        createUser: 'currentUser',
        type: 3,
        typeName: '告警',
        readCount: 20,
        unRead: true,
        unReadCount: 10,
        status: '1',
        statusName: '已读',
        title: '发动机过热发动机过热发动机过热发动机过热',
        content:
          '告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警告警id：395,862\n发生了告警',
        receiverType: '3',
        receiverName: '指定用户',
        receivers: [
          'summer',
          'compute',
          'gfh',
          'yy2',
          'zhangxinxin',
          'lyg',
          'currentUser',
        ],
      },
    ],
    pageable: {
      sort: { empty: false, sorted: true, unsorted: false },
      offset: 0,
      pageNumber: 0,
      pageSize: 10,
      paged: true,
      unpaged: false,
    },
    last: false,
    totalElements: 3,
    totalPages: 1,
    size: 10,
    number: 0,
    sort: { empty: false, sorted: true, unsorted: false },
    first: true,
    numberOfElements: 10,
    empty: false,
  }
}

// // 读取消息
// export function makeReadDt(params) {
//   return postForm(`${vsocPath}/my-msg/markRead`, params)
// }

// // 获取未读消息数量
// export function getUnreadCountDt() {
//   return 0
// }

// // 发消息
// export function sendMessageDt(data) {
//   return request({
//     url: `${vsocPath}/msg`,
//     method: 'post',
//     data,
//   })
// }

// 获取所有组名
export function getGroupAllDt() {
  return [
    { id: 4, name: 'test' },
    { id: 7, name: '666' },
    { id: 10, name: '222' },
  ]
}

// // 获取所有用户名
// export function getUserNamesDt() {
//   return request({
//     url: `${vsocPath}/user/getAllUserNames`,
//     method: 'get',
//   })
// }

// 查询config表
export function getConfigDt(type) {
  let data = []
  if (type === 'SOURCE') {
    data = [
      {
        createDate: null,
        updateDate: null,
        createUser: null,
        updateUser: null,
        id: 9,
        type: 'SOURCE',
        enTitle: 'Message',
        code: 'Message',
        value: '9',
        remark: null,
        protect: false,
      },
      {
        createDate: null,
        updateDate: null,
        createUser: null,
        updateUser: null,
        id: 10,
        type: 'SOURCE',
        enTitle: 'State',
        code: 'State',
        value: '10',
        remark: null,
        protect: false,
      },
      {
        createDate: null,
        updateDate: null,
        createUser: null,
        updateUser: null,
        id: 9,
        type: 'SOURCE',
        enTitle: 'Event',
        code: '事件',
        value: '4',
        remark: null,
        protect: false,
      },
      {
        createDate: null,
        updateDate: null,
        createUser: null,
        updateUser: null,
        id: 6,
        type: 'SOURCE',
        enTitle: 'Previous state',
        code: '历史状态',
        value: '1',
        remark: null,
        protect: false,
      },
      {
        createDate: null,
        updateDate: null,
        createUser: null,
        updateUser: null,
        id: 27,
        type: 'SOURCE',
        enTitle: 'Value',
        code: '值',
        value: '5',
        remark: null,
        protect: false,
      },
      {
        createDate: null,
        updateDate: null,
        createUser: null,
        updateUser: null,
        id: 30,
        type: 'SOURCE',
        enTitle: 'List',
        code: '列表',
        value: '8',
        remark: null,
        protect: false,
      },
      {
        createDate: null,
        updateDate: null,
        createUser: null,
        updateUser: null,
        id: 29,
        type: 'SOURCE',
        enTitle: 'Formula',
        code: '公式',
        value: '6',
        remark: null,
        protect: false,
      },
      {
        createDate: null,
        updateDate: null,
        createUser: null,
        updateUser: null,
        id: 28,
        type: 'SOURCE',
        enTitle: 'Abnormal',
        code: '异常',
        value: '7',
        remark: null,
        protect: false,
      },
      {
        createDate: null,
        updateDate: null,
        createUser: null,
        updateUser: null,
        id: 8,
        type: 'SOURCE',
        enTitle: 'Current message',
        code: '最新消息',
        value: '3',
        remark: null,
        protect: false,
      },
      {
        createDate: null,
        updateDate: null,
        createUser: null,
        updateUser: null,
        id: 7,
        type: 'SOURCE',
        enTitle: 'Current state',
        code: '当前状态',
        value: '2',
        remark: null,
        protect: false,
      },
    ]
  } else if (type === 'OPERATOR') {
    data = [
      {
        createDate: null,
        updateDate: null,
        createUser: null,
        updateUser: null,
        id: 39,
        type: 'OPERATOR',
        code: '不为空(Is not NULL)',
        value: 'NOT_NULL',
        remark: null,
        protect: false,
      },
      {
        createDate: null,
        updateDate: null,
        createUser: null,
        updateUser: null,
        id: 19,
        type: 'OPERATOR',
        code: '不包含(Not Contains)',
        value: 'NOT_INCLUDE',
        remark: null,
        protect: false,
      },
      {
        createDate: null,
        updateDate: null,
        createUser: null,
        updateUser: null,
        id: 21,
        type: 'OPERATOR',
        code: '不匹配(Not like)',
        value: 'MISMATCHING',
        remark: null,
        protect: false,
      },
      {
        createDate: null,
        updateDate: null,
        createUser: null,
        updateUser: null,
        id: 23,
        type: 'OPERATOR',
        code: '不在(Not In)',
        value: 'NOT_IN',
        remark: null,
        protect: false,
      },
      {
        createDate: null,
        updateDate: null,
        createUser: null,
        updateUser: null,
        id: 11,
        type: 'OPERATOR',
        code: '不等于(!=)',
        value: '!=',
        remark: null,
        protect: false,
      },
      {
        createDate: null,
        updateDate: null,
        createUser: null,
        updateUser: null,
        id: 24,
        type: 'OPERATOR',
        code: '为空(Is NULL)',
        value: 'NULL',
        remark: null,
        protect: false,
      },
      {
        createDate: null,
        updateDate: null,
        createUser: null,
        updateUser: null,
        id: 18,
        type: 'OPERATOR',
        code: '包含(Contains)',
        value: 'INCLUDE',
        remark: null,
        protect: false,
      },
      {
        createDate: null,
        updateDate: null,
        createUser: null,
        updateUser: null,
        id: 20,
        type: 'OPERATOR',
        code: '匹配(Like)',
        value: 'MATCHING',
        remark: null,
        protect: false,
      },
      {
        createDate: null,
        updateDate: null,
        createUser: null,
        updateUser: null,
        id: 22,
        type: 'OPERATOR',
        code: '在(In)',
        value: 'IN',
        remark: null,
        protect: false,
      },
      {
        createDate: null,
        updateDate: null,
        createUser: null,
        updateUser: null,
        id: 12,
        type: 'OPERATOR',
        code: '大于(>)',
        value: '>',
        remark: null,
        protect: false,
      },
      {
        createDate: null,
        updateDate: null,
        createUser: null,
        updateUser: null,
        id: 13,
        type: 'OPERATOR',
        code: '大于等于(>=)',
        value: '>=',
        remark: null,
        protect: false,
      },
      {
        createDate: null,
        updateDate: null,
        createUser: null,
        updateUser: null,
        id: 14,
        type: 'OPERATOR',
        code: '小于(<)',
        value: '<',
        remark: null,
        protect: false,
      },
      {
        createDate: null,
        updateDate: null,
        createUser: null,
        updateUser: null,
        id: 15,
        type: 'OPERATOR',
        code: '小于等于(<=)',
        value: '<=',
        remark: null,
        protect: false,
      },
      {
        createDate: null,
        updateDate: null,
        createUser: null,
        updateUser: null,
        id: 16,
        type: 'OPERATOR',
        code: '以什么开始(Starts With)',
        value: 'START_WITH',
        remark: null,
        protect: false,
      },
      {
        createDate: null,
        updateDate: null,
        createUser: null,
        updateUser: null,
        id: 10,
        type: 'OPERATOR',
        code: '等于(=)',
        value: '==',
        remark: null,
        protect: false,
      },
      {
        createDate: null,
        updateDate: null,
        createUser: null,
        updateUser: null,
        id: 17,
        type: 'OPERATOR',
        code: '以什么结束(Ends With)',
        value: 'END_WITH',
        remark: null,
        protect: false,
      },
    ]
  }

  return data
}

// 查询枚举表
export function getEnumDt() {
  console.log('getEnumDt******')

  return [
    { type: 'AssetType', values: ['Vehicle', 'Consumer', 'Ecu'] },
    {
      type: 'ActiveStatus',
      values: ['Active', 'Inactive'],
    },
    {
      type: 'AlarmLevel',
      values: ['Critical', 'High', 'Medium', 'Low', 'Info', 'Test'],
    },
    {
      type: 'AlarmStatus',
      values: [
        'waitHandle',
        'handling',
        'truePositive',
        'falsePositive',
        'notAnIssue',
        'duplicate',
      ],
    },
    {
      type: 'DetectorActionMode',
      values: ['DefaultModelClose', 'DefaultModeOpen'],
    },
    {
      type: 'DetectorExtent',
      values: ['SingleAsset', 'MultiAsset'],
    },
    { type: 'DetectorType', values: ['Alert', 'Flag'] },
    {
      type: 'DisposeActionLogStatus',
      values: ['init', 'success', 'fail'],
    },
    {
      type: 'DisposeActionType',
      values: ['ExternalApi', 'Email', 'SystemMessage', 'Kafka'],
    },
    { type: 'HealthStatus', values: ['A', 'B', 'C', 'D'] },
    {
      type: 'KafkaType',
      values: ['Internal', 'External'],
    },
    { type: 'SignalType', values: ['FieldMetadata', 'EventField'] },
    {
      type: 'SignalValueType',
      values: [
        'LONG',
        'DOUBLE',
        'BOOLEAN',
        'STRING',
        'ARRAY',
        'TIMESTAMP',
        'DICTIONARY',
        'OBJECT_ARRAY',
      ],
    },
  ]
}
