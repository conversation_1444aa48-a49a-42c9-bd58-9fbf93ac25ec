import { request, vsocPath } from '../../util/request'

// 根据当前用户角色查询菜单层级树
export const queryMenuListByRole = function (params) {
  return request({
    url: `${vsocPath}/menu/queryMenuListByRole`,
    method: 'post',
    data: params,
  })
}

// 查询菜单树
export const querySysMenuList = function (params) {
  return request({
    url: `${vsocPath}/menu/querySysMenuList`,
    method: 'post',
    data: params,
  })
}

// 新增菜单
export const addMenu = function (params) {
  return request({
    url: `${vsocPath}/menu/addMenu`,
    method: 'post',
    data: params,
  })
}

// 更新菜单
export const updateMenu = function (params) {
  return request({
    url: `${vsocPath}/menu/updateMenu`,
    method: 'post',
    data: params,
  })
}

// 删除菜单
export const deleteMenu = function (params) {
  return request({
    url: `${vsocPath}/menu/deleteMenu`,
    method: 'post',
    data: params,
  })
}
