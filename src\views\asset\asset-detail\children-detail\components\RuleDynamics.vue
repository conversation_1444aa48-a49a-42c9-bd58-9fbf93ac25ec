<template>
  <v-card class="h-full d-flex flex-column">
    <v-card-title>
      <span>{{ $t('asset.idpsTab2.ruleUpdates') }}</span>
      <v-spacer></v-spacer>
      <a class="text-base text-decoration-underline text--secondary">{{
        $t('asset.idpsTab2.ruleUpdatesMore')
      }}</a>
    </v-card-title>
    <v-card-text class="flex-1 pb-1">
      <div
        v-for="(item, index) in dynamicList"
        :key="index"
        style="height: 25%"
      >
        <!-- <v-chip
          label
          small
          class="primary--text opacity-b1 font-weight-medium mr-3"
          >更新动态</v-chip
        >
        <div class="flex-1">
          <span class="text--primary">
            {{ item.text }}
          </span>

          <span class="text-right">{{ item.time | toDate }}</span>
        </div> -->
        <div class="d-flex justify-space-between">
          <div
            class="primary--text opacity-b1 font-weight-medium mr-3 px-1 rounded"
          >
            {{ $t('asset.idpsTab2.updates') }}
          </div>
          <div class="text-right">{{ item.time | toDate }}</div>
        </div>
        <div
          v-show-tips
          class="text--primary text-no-wrap text-overflow-hide w-100"
        >
          {{ item.text }}
        </div>
      </div>
    </v-card-text>
  </v-card>
</template>

<script>
export default {
  name: 'RuleDynamics',
  data() {
    return {}
  },
  computed: {
    dynamicList() {
      return [
        {
          text: 'v1.21 1、新增防护规则PL48336，针对CVE-2022-24816漏洞的防护规则。',
          time: new Date(),
        },
        {
          text: 'v1.21 1、新增防护规则PL48336，针对CVE-2022-24816漏洞的防护规则。',
          time: new Date(),
        },
        {
          text: 'v1.21 1、新增防护规则PL48336，针对CVE-2022-24816漏洞的防护规则。',
          time: new Date(),
        },
        {
          text: 'v1.21 1、新增防护规则PL48336，针对CVE-2022-24816漏洞的防护规则。',
          time: new Date(),
        },
      ]
    },
  },
  mounted() {},
  methods: {},
}
</script>
<style scoped lang="scss"></style>
