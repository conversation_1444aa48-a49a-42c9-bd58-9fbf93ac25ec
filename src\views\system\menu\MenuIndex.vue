<!-- 权限管理 -->
<template>
  <div
    :style="{
      height: `calc(100vh - ${$store.state.global.systemBarHeight})`,
    }"
    class="overflow-hidden"
  >
    <bread-crumb></bread-crumb>
    <v-card class="border-bottom-radius-0 border-top-radius-0" elevation="0">
      <v-card-text class="pa-0">
        <div
          class="px-4 d-flex justify-space-between align-center pt-4 flex-row-reverse"
        >
          <div class="d-flex align-center">
            <table-search
              :searchList="searchList"
              :searchQuery="query"
              @search="$_getTableData"
            ></table-search>
            <!-- <v-text-field
              v-model="query.menuName"
              class="text-width"
              color="primary"
              hide-details="auto"
              :label="$t('menu.headers.name')"
              dense
              outlined
              clearable
              @click:clear=";(query.menuName = ''), $_getTableData()"
              @keyup.enter.native="$_getTableData"
            ></v-text-field>
            <v-btn
              class="primary--text bg-btn ml-3"
              @click="$_getTableData"
              elevation="0"
            >
              <span>{{ $t('action.search') }}</span>
            </v-btn> -->
          </div>
          <div class="d-flex justify-end align-center">
            <!-- <v-btn
            elevation="0"
            height="2.3rem"
            class=" font-weight-semibold bg-gradient-primary px-6 shadow-0 flex-1 "
            color="primary"
            @click="add()"
          >
            新增权限
          </v-btn> -->

            <v-btn
              v-has:menu-add
              color="primary"
              class="me-1"
              @click="add"
              elevation="0"
            >
              <!-- <v-icon>mdi-plus</v-icon> -->
              <span>
                {{ $generateMenuTitle($route.meta.buttonInfo['menu-add']) }}
              </span>
            </v-btn>
          </div>
        </div>
        <div class="position-relative">
          <!-- .filter(data => !query.menuName || data.menuName.toLowerCase().includes(query.menuName.toLowerCase())) -->
          <el-table
            class="table mt-3 mx-4 v-data-table thead-light"
            :data="tableData"
            :height="tableHeight"
            :max-height="tableHeight"
            row-key="id"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          >
            <el-table-column
              prop="menuName"
              :label="$t('menu.headers.name')"
              :show-overflow-tooltip="true"
              min-width="160"
            >
              <template slot-scope="{ row }">
                {{ $generateName(row.menuName, row.englishName) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="menuCode"
              :label="$t('menu.headers.code')"
              :show-overflow-tooltip="true"
              min-width="160"
            >
            </el-table-column>
            <el-table-column
              prop="uiType"
              :label="$t('menu.headers.type')"
              :show-overflow-tooltip="true"
              width="130"
            >
              <template slot-scope="{ row }">
                {{
                  $generateName(
                    uiTypeList.find(e => e.value === row.uiType).name,
                    uiTypeList.find(e => e.value === row.uiType).value,
                  )
                }}
              </template>
            </el-table-column>
            <el-table-column
              prop="orderNum"
              :label="$t('menu.headers.sort')"
              :show-overflow-tooltip="true"
              width="90"
            >
            </el-table-column>
            <el-table-column
              prop="iconCls"
              :label="$t('menu.headers.icon')"
              width="90"
            >
              <template slot-scope="scope">
                <vsoc-icon
                  v-if="scope.row.iconCls && scope.row.iconCls.length > 1"
                  :icon="scope.row.iconCls"
                >
                </vsoc-icon>
                <div
                  v-else
                  class="list-item-action"
                  v-text="scope.row.iconCls"
                ></div>
              </template>
            </el-table-column>
            <!--<el-table-column prop="component" label="组件" :show-overflow-tooltip="true" min-width="180"></el-table-column>-->
            <el-table-column
              prop="url"
              :label="$t('menu.headers.path')"
              :show-overflow-tooltip="true"
              min-width="180"
            ></el-table-column>
            <el-table-column
              label=""
              align="center"
              class-name="small-padding fixed-width"
              min-width="100"
            >
              <template slot-scope="scope">
                <v-btn v-has:menu-edit icon @click="edit(scope.row)">
                  <!-- <v-icon
                    v-show-tips="'修改'"
                    size="1.25rem"
                    @click="edit(scope.row)"
                  >
                    mdi-pencil
                  </v-icon> -->
                  <vsoc-icon
                    v-show-tips="
                      $generateMenuTitle($route.meta.buttonInfo['menu-edit'])
                    "
                    type="fill"
                    icon="icon-bianji"
                    class="action-btn"
                    size="x-large"
                  ></vsoc-icon>
                </v-btn>
                <v-btn v-has:menu-del icon @click="del(scope.row)">
                  <!-- <v-icon
                    v-show-tips="'删除'"
                    size="1.25rem"
                    @click="del(scope.row)"
                  >
                    mdi-delete
                  </v-icon> -->
                  <vsoc-icon
                    v-show-tips="
                      $generateMenuTitle($route.meta.buttonInfo['menu-del'])
                    "
                    type="fill"
                    class="action-btn"
                    icon="icon-shanchu"
                    size="x-large"
                  ></vsoc-icon>
                </v-btn>
              </template>
            </el-table-column>
            <!-- <template #empty>
              <p>{{ tableLoading ? '' : '暂无数据' }}</p>
            </template> -->
          </el-table>
          <div v-if="tableLoading" class="el-table__loading">
            <v-progress-linear
              color="primary"
              indeterminate
              rounded
              height="4"
            ></v-progress-linear>
            <!-- <p>查询中... 请等待</p> -->
            <p>{{ $t('$vuetify.dataIterator.loadingText') }}</p>
          </div>
        </div>
      </v-card-text>

      <vsoc-drawer
        v-model="drawer"
        :title="isEdit ? 'edit' : 'new'"
        @click:confirm="save"
      >
        <v-form ref="form" v-model="valid" lazy-validation>
          <div class="mt-4">
            <v-row class="pl-6 pr-6">
              <div class="mb-6 w-100">
                <div class="text-base font-weight-medium color-base mb-2">
                  {{ $t('menu.drawer.type') }}
                </div>
                <v-btn-toggle
                  v-model="uiType"
                  color="primary"
                  elevation="0"
                  class="mt-3 w-100 mt-sm-0"
                >
                  <v-btn
                    v-for="item in uiTypeList"
                    :key="item.value"
                    :value="item.value"
                    :disabled="
                      authUiEt.uiType === 'Catalogue' &&
                      item.value !== 'Catalogue'
                    "
                    style="width: 33.33%"
                    elevation="0"
                  >
                    <vsoc-icon
                      type="fill"
                      :icon="item.icon"
                      class="mr-2"
                      size="16"
                    ></vsoc-icon>
                    <!-- <v-icon size="16" class="mr-2">
                        {{ item.icon }}
                      </v-icon> -->
                    <span class="text-content">{{
                      $generateName(item.name, item.value)
                    }}</span>
                  </v-btn>
                </v-btn-toggle>
              </div>
            </v-row>

            <v-row v-if="authUiEt.uiType !== 'Button'" class="pl-6 pr-6">
              <div class="mb-6 w-100">
                <div class="text-base font-weight-medium color-base mb-2">
                  {{ $t('menu.drawer.show') }}
                </div>

                <v-btn-toggle
                  class="w-100"
                  v-model="authUiEt.isShow"
                  color="primary"
                >
                  <v-btn elevation="0" class="px-6 w-50" :value="true">
                    <!-- <v-icon size="16" :color="$activeColor" left>
                      mdi-check-circle
                    </v-icon> -->
                    <vsoc-icon
                      type="fill"
                      icon="icon-mimakejian"
                      class="mr-2"
                      size="16"
                    ></vsoc-icon>
                    <span class="text-content">{{
                      $generateName('显示', 'Show')
                    }}</span>
                  </v-btn>
                  <v-btn class="w-50" elevation="0" :value="false">
                    <vsoc-icon
                      type="fill"
                      icon="icon-mimabukejian"
                      class="mr-2"
                      size="16"
                    ></vsoc-icon>
                    <!-- <v-icon size="16" :color="$inactiveColor" left>
                      mdi-close-circle
                    </v-icon> -->
                    <span class="text-content">{{
                      $generateName('隐藏', 'Hide')
                    }}</span>
                  </v-btn>
                </v-btn-toggle>
              </div>
            </v-row>

            <v-row v-if="authUiEt.uiType === 'Menu'" class="pl-6 pr-6">
              <div class="mb-6 w-100">
                <div class="text-base font-weight-medium color-base mb-2">
                  {{ $t('menu.drawer.theme') }}
                </div>
                <v-btn-toggle
                  v-model="authUiEt.isDark"
                  color="primary"
                  class="w-100"
                >
                  <v-btn
                    v-for="theme in themeOption"
                    :key="theme.value"
                    elevation="0"
                    :value="theme.value"
                  >
                    <v-icon size="16" :color="theme.color" left>
                      mdi-checkbox-blank-circle
                    </v-icon>
                    <span>{{ theme.text }}</span>
                  </v-btn>
                  <!-- <v-btn width="120" elevation="0" class="px-6" :value="true">
                    <v-icon
                      size="16"
                      :color=""
                      left
                    >
                      mdi-checkbox-blank-circle
                    </v-icon>
                    <span>dark</span>
                  </v-btn> -->
                </v-btn-toggle>
              </div>
            </v-row>

            <v-row class="pl-6 pr-6">
              <v-text-field
                v-model="authUiEt.menuName"
                :label="$t('menu.headers.name')"
                color="primary"
              >
              </v-text-field>
            </v-row>
            <v-row class="pl-6 pr-6">
              <v-text-field
                v-model="authUiEt.englishName"
                :label="$t('menu.drawer.enName')"
                placeholder="eg: Data Center"
                :rules="enRules"
                color="primary"
              >
              </v-text-field>
            </v-row>
            <v-row class="pl-6 pr-6">
              <v-text-field
                v-model="authUiEt.menuCode"
                :label="$t('menu.headers.code')"
                :rules="menuCodeRules"
                required
                class="is-required"
                color="primary"
              >
              </v-text-field>
            </v-row>
            <v-row v-if="uiType != 'Catalogue'" class="pl-6 pr-6">
              <v-menu
                bottom
                nudge-top="20"
                :close-on-content-click="false"
                content-class="bg-white mx-2"
                transition="slide-y-transition"
                offset-y
                offset-x
                auto
                @input="setMenu"
              >
                <template v-slot:activator="{ on }">
                  <v-text-field
                    v-model="authUiEt.parentName"
                    :label="$t('menu.drawer.parent')"
                    required
                    color="primary"
                    v-on="on"
                  >
                  </v-text-field>
                </template>

                <v-treeview
                  v-model="treeSelect"
                  return-object
                  selected-color="primary"
                  item-key="id"
                  activatable
                  :item-text="$i18n.locale === 'en' ? 'enTitle' : 'title'"
                  :items="menuList"
                  selection-type="independent"
                  @update:active="treeInput"
                ></v-treeview>
              </v-menu>
            </v-row>
            <v-row
              v-if="
                uiType == 'Button' &&
                authUiEt.parentId &&
                menuHideList.length > 0
              "
            >
              <v-select
                :label="$t('menu.boundMenu')"
                clearable
                v-model="authUiEt.boundMenuId"
                color="primary"
                item-value="id"
                item-text="title"
                :items="menuHideList"
                :menu-props="{ offsetY: true }"
              ></v-select>
            </v-row>
            <v-row class="pl-6 pr-6">
              <v-text-field
                v-if="uiType !== 'Button'"
                v-model="authUiEt.url"
                :label="$t('menu.headers.path')"
                color="primary"
              >
                <template v-slot:append>
                  <v-tooltip left>
                    <template v-slot:activator="{ on }">
                      <v-icon class="mt-1" size="1rem" v-on="on">
                        mdi-help-circle-outline
                      </v-icon>
                    </template>
                    {{ $t('menu.hint.path') }}
                  </v-tooltip>
                </template>
              </v-text-field>
            </v-row>
            <v-row class="pl-6 pr-6">
              <v-text-field
                v-if="uiType !== 'Button'"
                v-model="authUiEt.component"
                :label="$t('menu.drawer.component')"
                required
                color="primary"
              >
                <template v-slot:append>
                  <v-tooltip left>
                    <template v-slot:activator="{ on }">
                      <v-icon class="mt-1" size="1rem" v-on="on">
                        mdi-help-circle-outline
                      </v-icon>
                    </template>
                    {{ $t('menu.hint.component') }}
                  </v-tooltip>
                </template>
              </v-text-field>
            </v-row>
            <v-row class="pl-6 pr-6">
              <v-text-field
                v-if="uiType !== 'Button'"
                v-model="authUiEt.iconCls"
                :label="$t('menu.headers.icon')"
                required
                color="primary"
              >
              </v-text-field>
            </v-row>
            <v-row class="pl-6 pr-6">
              <v-text-field
                v-model.number="authUiEt.orderNum"
                :label="$t('menu.headers.sort')"
                required
                color="primary"
                type="number"
              >
                <!-- <template v-slot:append>
                  <div class="cus-text-append">
                    <v-icon size="0.725" @click="addOrderNum()">
                      mdi-chevron-up
                    </v-icon>
                    <v-icon size="0.725" @click="subOrderNum()">
                      mdi-chevron-down
                    </v-icon>
                  </div>
                </template> -->
              </v-text-field>
            </v-row>
            <v-row class="pl-6 pr-6">
              <v-text-field
                v-model="authUiEt.description"
                :label="$t('menu.drawer.description')"
                color="primary"
              >
              </v-text-field>
              <!-- :rules="descriptionRules" -->
              <!-- placeholder="navActiveLink,检测管理" -->
            </v-row>
          </div>
        </v-form>
      </vsoc-drawer>
    </v-card>
  </div>
</template>

<script>
// import { qeuryRoleLikeDt } from '@/api/auth/auth-role-dt'
import { required } from '@/@core/utils/validation'
import {
  addMenu,
  deleteMenu,
  querySysMenuList,
  updateMenu,
} from '@/api/system/menu'
import TableSearch from '@/components/TableSearch/index.vue'
import VsocDrawer from '@/components/VsocDrawer.vue'
import breadCrumb from '@/components/bread-crumb/index'
import { deepClone } from '@/util/throttle'
import { formatDate, setRemainingHeight } from '@/util/utils'
import themeConfig from '@themeConfig'

export default {
  name: 'MenuIndex',
  components: {
    VsocDrawer,
    breadCrumb,
    TableSearch,
  },
  data() {
    return {
      hideList: [],
      filterList: [],
      uiType: 'Catalogue',
      // 分页参数
      query: {
        menuName: '',
      },
      tableLoading: false,
      tableDataTotal: 10,
      tableData: [],
      tableHeight: '40.5rem',

      selectedArray: [],

      uiTypeList: [
        { name: '目录', value: 'Catalogue', icon: 'icon-mulu' },
        { name: '菜单', value: 'Menu', icon: 'icon-caidan' },
        { name: '按钮', value: 'Button', icon: 'icon-anniu' },
      ],
      showStatusList: [
        { name: '显示', value: true },
        { name: '隐藏', value: false },
      ],

      // menuList: [],

      isEdit: false,
      drawer: false,
      authUiEt: this.initData(),
      treeSelect: [],

      valid: true,
      enRules: [
        v =>
          /^[A-Za-z]*( [A-Za-z]*)*$/.test(v) ||
          this.$t('validation.wrongFormat', ['']),
      ],
      menuCodeRules: [v => required(v, this.$t('menu.headers.code'))],
    }
  },
  computed: {
    searchList() {
      return [
        {
          type: 'input',
          value: 'menuName',
          text: this.$t('menu.headers.name'),
        },
      ]
    },
    themeOption() {
      return [
        {
          text: 'light',
          value: 'light',
          color: themeConfig.themes.light.primary,
        },
        {
          text: 'dark',
          value: 'dark',
          color: themeConfig.themes.dark.primary,
        },
        {
          text: 'semi dark',
          value: 'semi-dark',
          color: '#1D233F',
        },
      ]
    },
    menuHideList() {
      let menuHideList = this.hideList
        .filter(v => v.parentId === this.authUiEt.parentId)
        .map(item => {
          return {
            title: this.$generateName(item.menuName, item.englishName),
            ...item,
          }
        })
      // if (menuHideList && menuHideList.length > 0) {
      //   this.authUiEt.boundMenuId = menuHideList[0].id
      // }
      return menuHideList
    },
    menuList() {
      console.log(
        'this.$store.state.permission.menus',
        this.$store.state.permission.menus,
      )
      // 添加不可选中名单
      return this.$store.state.permission.menus
    },
    isDescriptionRequired() {
      return (
        this.authUiEt.uiType === 'Menu' &&
        this.authUiEt.parentId &&
        this.authUiEt.isShow === false
      )
    },
  },
  watch: {
    drawer(val) {
      if (!val) {
        this.navigationDrawerClose()
        this.$refs.form.resetValidation()
      }
    },
    authUiEt: {
      handler(val) {
        if (val) {
          if (val.id) {
            this.isEdit = true
          } else {
            this.isEdit = false
          }
          if (val.parentId) {
            this.authUiEt.parentName = this.toMenuIdNameMap(
              val.parentId,
              this.menuList,
            )
          } else {
            this.authUiEt.parentName = ''
          }
          if (!val.parentName) {
            this.authUiEt.parentId = ''
          }
        }
      },
      deep: true,
    },
  },
  created() {
    this.$bus.$on('resize', this.$_setTableHeight)
    if (this.authUiEt.uiType) {
      this.uiType = this.authUiEt.uiType
    }
  },
  mounted() {
    this.$_getTableData()
    // this.getRoleList()
  },
  beforeDestroy() {
    this.$bus.$off('resize', this.$_setTableHeight)
  },
  methods: {
    $_setTableHeight() {
      this.$nextTick(() => {
        // const num = 16
        // const fontSize = +getComputedStyle(window.document.documentElement)[
        //   'font-size'
        // ].replace('px', '')
        // let pt = 0
        // if (fontSize <= 14) {
        //   pt = fontSize / 1.4
        // }
        // this.tableHeight =
        //   document.documentElement.clientHeight - num * fontSize + pt
        const fn = () => {
          // 分页器+card内边距+table上外边距
          return 50.2 - 11 - 2
        }
        this.tableHeight = setRemainingHeight(fn)
      })
    },

    // 表格选择事件
    $_tableSelected({ item, value, items }) {
      if (items) {
        this.selecedArray = value ? deepClone(items) : []
      } else if (value) {
        this.selecedArray.push(item)
      } else {
        const index = this.selecedArray.findIndex(v => v.code === item.menuCode)
        this.selecedArray.splice(index, 1)
      }
    },

    navigationDrawerClose() {
      this.drawer = false
      this.isEdit = false
      this.authUiEt = this.initData()
    },

    /*      async $_getTableData() {
              this.tableLoading = true;
              this.tableData = [];
              getUiTree().then(resp => {
                if (resp.menuCode === 200) {
                  this.tableData = resp.data;
                  console.log("=====", this.tableData);
                }
              }).catch(() => {
                // this.$notify.info('error', '查询异常！')
              }).finally(() => {
                this.tableLoading = false;
              });
            }, */
    // 加载表格数据
    async $_getTableData() {
      try {
        this.tableLoading = true
        this.tableData = []
        const resp = await querySysMenuList(this.query)
        this.tableData = resp.data
        this.hideList = this.toHide(this.tableData)
      } catch (err) {
        console.log('菜单报错', err)
      } finally {
        this.tableLoading = false
        this.$_setTableHeight()
      }
    },

    toHide(list) {
      let result = []
      list.forEach(item => {
        if (item.children && item.children.length > 0) {
          result.push(...this.toHide(item.children))
        } else if (item.isShow === false && item.uiType === 'Menu') {
          result.push(item)
        }
      })

      return result
    },

    changePage() {
      this.$_getTableData()
    },
    changeSize() {
      this.$_getTableData()
    },

    add() {
      this.drawer = true
      this.treeSelect = []
    },
    findMenuItem(arr, parentId) {
      let findValue = null
      arr.forEach(v => {
        if (v.children && v.children.length) {
          const val = this.findMenuItem(v.children, parentId)
          if (val) findValue = val
        }
        if (v.id === parentId) findValue = v
      })

      return findValue
    },
    edit(data) {
      this.treeSelect = []
      this.authUiEt = deepClone(data)
      this.uiType = this.authUiEt.uiType
      this.drawer = true
      this.isEdit = true
    },
    setMenu() {
      if (!this.authUiEt.parentId) return
      this.treeSelect.push(
        this.findMenuItem(this.menuList, this.authUiEt.parentId),
      )
    },
    del(data) {
      this.$swal({
        title: this.$t('menu.swal.title'),
        text: this.$t('menu.swal.text', [
          this.$generateName(data.menuName, data.englishName),
        ]),
        icon: 'warning',
        reverseButtons: true,
        showCancelButton: true,
        confirmButtonText: this.$t('action.confirm'),
        cancelButtonText: this.$t('action.cancel'),
        customClass: {
          confirmButton: 'sweet-btn-primary',
        },
      }).then(result => {
        if (result.isConfirmed) {
          const ids = { id: data.id }
          deleteMenu(ids).then(resp => {
            if (resp.code === 200) {
              this.$_getTableData()
              this.$notify.info(
                'success',
                this.$t('global.hint.del', [this.$t('menu.currentTitle')]),
              )
            }
          })
        }
      })
    },
    async save(callback) {
      try {
        this.authUiEt.uiType = this.uiType
        const bool = this.$refs.form.validate()
        if (!bool) return callback(false, true)
        if (this.authUiEt.id) {
          await updateMenu(this.authUiEt)
          this.$notify.info(
            'success',
            this.$t('global.hint.edit', [this.$t('menu.currentTitle')]),
          )
          this.$_getTableData()
          this.navigationDrawerClose()
          // 超级管理员
          if (this.$store.getters.userInfo.role === 'admin') {
            location.reload()

            this.$store.dispatch('permission/generateRoutes')
          }
          callback()
        } else {
          await addMenu(this.authUiEt)
          this.$notify.info(
            'success',
            this.$t('global.hint.add', [this.$t('menu.currentTitle')]),
          )
          this.$_getTableData()
          this.navigationDrawerClose()
          callback()
        }
      } catch (err) {
        console.log(err)
        callback(false, true)
      }
    },
    convertDateTime(time) {
      if (time) {
        return formatDate(new Date(time))
      }

      return ''
    },
    initData() {
      return {
        // id: '',
        uiType: '',
        menuName: '',
        englishName: '',
        menuCode: '',
        url: '',
        component: '',
        iconCls: '',
        orderNum: 0,
        isShow: true,
        isDark: 'light',
        description: '',
        parentId: '',
        parentName: '',
      }
    },
    addOrderNum() {
      if (!Number.isInteger(this.authUiEt.orderNum)) {
        this.authUiEt.orderNum = 0
      } else {
        this.authUiEt.orderNum++
      }
    },
    subOrderNum() {
      if (!Number.isInteger(this.authUiEt.orderNum)) {
        this.authUiEt.orderNum = 0
      } else {
        this.authUiEt.orderNum--
      }
    },

    treeInput(select) {
      if (select && select.length > 0) {
        let id = select[0].id
        this.authUiEt.parentId = id
      } else {
        this.authUiEt.parentId = ''
      }
    },

    toMenuIdNameMap(id, menuList) {
      for (let i = 0, l = menuList.length; i < l; i++) {
        const e = menuList[i]
        if (id === e.id) {
          return e.title
        }
        if (e.children) {
          const name = this.toMenuIdNameMap(id, e.children)
          if (name) {
            return name
          }
        }
      }

      return ''
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .el-table {
  width: auto;
}
.cus-text-append {
  margin-top: -0.5rem;
  margin-right: -0.75rem;
  border: none;
  padding: 0;
  .v-icon {
    display: block;
    border: 2px solid #ccc;
    padding: 2px;

    &:first-child {
      border-bottom: none;
      border-top-right-radius: 0.4em;
    }
    &:last-child {
      border-bottom-right-radius: 0.4em;
    }
    &:hover {
      background-color: #f4f4f4;
    }
    &::after {
      display: none;
    }
  }
  &:hover {
    background-color: #fff;
  }
}
.v-data-table {
  border-radius: 0;
}
::v-deep.v-label {
  font-size: 14px !important;
}
</style>
