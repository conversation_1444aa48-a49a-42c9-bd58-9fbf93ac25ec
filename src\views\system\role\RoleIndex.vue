<!-- 角色管理 -->
<template>
  <div>
    <bread-crumb></bread-crumb>

    <v-card tile class="main-content">
      <v-card-text class="pa-0">
        <div class="d-flex justify-space-between align-center flex-row-reverse">
          <div class="d-flex align-center">
            <table-search
              :searchList="searchList"
              :searchQuery="query"
              @search="$_search"
            ></table-search>
            <!-- <v-text-field
              v-model="query.roleName"
              class="text-width"
              color="primary"
              hide-details="auto"
              :label="$t('role.headers.name')"
              dense
              outlined
              clearable
              @click:clear=";(query.roleName = ''), searchRoles()"
              @keyup.enter.native=";(query.pageNum = 1), searchRoles()"
            ></v-text-field>
            <v-btn
              class="primary--text bg-btn ml-3"
              elevation="0"
              @click=";(query.pageNum = 1), searchRoles()"
            >
              <span>{{ $t('action.search') }}</span>
            </v-btn> -->
          </div>
          <div class="d-flex justify-end align-center">
            <!-- <v-btn
            elevation="0"
            height="2.3rem"
            class=" font-weight-semibold bg-gradient-primary  px-6  shadow-0 flex-1 "
            color="primary"
            @click="add()"
          >
            新增角色
          </v-btn> -->

            <v-btn
              elevation="0"
              color="primary"
              class="me-1"
              @click="add"
              v-has:role-add
            >
              <!-- <v-icon>mdi-plus</v-icon> -->
              <span>
                {{ $generateMenuTitle($route.meta.buttonInfo['role-add']) }}
              </span>
            </v-btn>
          </div>
        </div>
        <!-- :search="searchKey" -->
        <!-- loading-text="查询中... 请等待" -->
        <v-data-table
          fixed-header
          :items-per-page="query.pageSize"
          item-key="code"
          :height="tableHeight"
          hide-default-footer
          :headers="headers"
          :items="tableData"
          class="table border-radius-xl mt-3 thead-light"
          :loading="tableLoading"
        >
          <template v-slot:item.icon>
            <v-icon size="1.75rem" color="black"> mdi-account-circle </v-icon>
          </template>

          <template v-slot:item.createDate="{ item }">
            {{ convertDateTime(item.createDate) }}
          </template>

          <template v-slot:item.actions="{ item }">
            <template v-if="item.roleId !== 'admin'">
              <v-btn icon @click="grantUi(item)" v-has:role-grant>
                <vsoc-icon
                  v-has:role-grant
                  v-show-tips="
                    $generateMenuTitle($route.meta.buttonInfo['role-grant'])
                  "
                  icon="icon-quanxianshezhi"
                  class="action-btn"
                  type="fill"
                  size="x-large"
                ></vsoc-icon>
              </v-btn>
              <v-btn v-has:role-edit icon @click="edit(item)">
                <vsoc-icon
                  v-show-tips="
                    $generateMenuTitle($route.meta.buttonInfo['role-edit'])
                  "
                  type="fill"
                  icon="icon-bianji"
                  class="action-btn"
                  size="x-large"
                ></vsoc-icon>
              </v-btn>
              <v-btn v-has:role-del icon @click="del(item)">
                <vsoc-icon
                  v-show-tips="
                    $generateMenuTitle($route.meta.buttonInfo['role-del'])
                  "
                  type="fill"
                  class="action-btn"
                  icon="icon-shanchu"
                  size="x-large"
                ></vsoc-icon>
              </v-btn>
            </template>
          </template>
        </v-data-table>

        <!-- 分页器 -->
        <vsoc-pagination
          :page.sync="query.pageNum"
          :size.sync="query.pageSize"
          :total="tableDataTotal"
          @change-page="changePage"
          @change-size="changeSize"
        >
        </vsoc-pagination>
      </v-card-text>

      <vsoc-drawer v-model="drawer" :title="ops" @click:confirm="save">
        <div v-if="drawer">
          <role-edit
            v-if="ops === 'new' || ops === 'edit'"
            ref="roleEditRef"
            :item="roleEt"
            :mode="ops"
            @save="navigationDrawerCloseAndRefresh"
          ></role-edit>
          <grant-menu
            v-if="ops === 'grant'"
            ref="grantMenuRef"
            :role-id="roleEt.id"
            @save="navigationDrawerCloseAndRefresh"
          ></grant-menu>
        </div>
      </vsoc-drawer>
    </v-card>
  </div>
</template>

<script>
import { delRole, selectUserRoleCount, userRole } from '@/api/system/role'
import VsocDrawer from '@/components/VsocDrawer.vue'
import VsocPagination from '@/components/VsocPagination.vue'
import { deepClone } from '@/util/throttle'
import { formatDate, setRemainingHeight } from '@/util/utils'

import TableSearch from '@/components/TableSearch/index.vue'
import breadCrumb from '@/components/bread-crumb/index'
import GrantMenu from './GrantMenu.vue'
import RoleEdit from './RoleEdit.vue'

// import grantApi from './grant-api'

export default {
  name: 'RoleIndex',
  components: {
    VsocPagination,
    GrantMenu,
    RoleEdit,
    VsocDrawer,
    breadCrumb,
    TableSearch,
  },
  data() {
    return {
      // 查询内容
      searchKey: '',
      filterList: [],

      // 分页参数
      query: {
        roleName: '',
        pageNum: 1,
        pageSize: 10,
      },
      tableLoading: true,
      tableDataTotal: 0,
      tableData: [],
      tableHeight: '34.5rem',

      selectedArray: [],

      ops: '',

      drawer: false,
      roleEt: this.initRoleData(),

      valid: true,

      uiTreeData: [],
    }
  },
  computed: {
    searchList() {
      return [
        {
          type: 'input',
          value: 'roleName',
          text: this.$t('role.headers.name'),
        },
      ]
    },
    headers() {
      return [
        {
          text: this.$t('role.headers.code'),
          value: 'roleId',
          width: '100',
        },
        {
          text: this.$t('role.headers.name'),
          value: 'roleName',
          width: '150',
        },
        {
          text: this.$t('role.headers.description'),
          value: 'description',
          width: '200',
        },
        {
          text: this.$t('role.headers.updateDate'),
          value: 'updateDate',
          width: '150',
        },
        {
          text: this.$t('role.headers.updater'),
          value: 'updateUser',
          width: '100',
        },
        {
          text: '',
          value: 'actions',
          sortable: false,
          width: '150',
          align: 'right',
        },
      ]
    },
  },
  watch: {
    drawer(val) {
      if (!val) {
        this.navigationDrawerClose()
      }
    },
  },
  created() {
    this.$_setTableHeight()
    this.$bus.$on('resize', this.$_setTableHeight)
  },
  mounted() {
    this.searchRoles()
  },
  beforeDestroy() {
    this.$bus.$off('resize', this.$_setTableHeight)
  },
  methods: {
    // $_setTableHeight() {
    //   this.$nextTick(() => {
    //     const num = this.filterList.length === 0 ? 17 : 20
    //     const fontSize = +getComputedStyle(window.document.documentElement)['font-size'].replace('px', '')
    //     this.tableHeight = document.documentElement.clientHeight - num * fontSize
    //     console.log('fontSize', fontSize)
    //     console.log('document.documentElement.clientHeight', document.documentElement.clientHeight)
    //   })
    // },
    $_setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = setRemainingHeight(
          this.$store.state.global.subMarginFn,
        )
      })
    },

    // 表格选择事件
    $_tableSelected({ item, value, items }) {
      if (items) {
        this.selecedArray = value ? deepClone(items) : []
      } else if (value) {
        this.selecedArray.push(item)
      } else {
        const index = this.selecedArray.findIndex(v => v.code === item.code)
        this.selecedArray.splice(index, 1)
      }
    },

    convertDateTime(time) {
      if (time) {
        return formatDate(new Date(time))
      }

      return ''
    },

    navigationDrawerClose() {
      this.drawer = false
      this.roleEt = this.initRoleData()
    },
    navigationDrawerCloseAndRefresh() {
      this.searchRoles()
      this.drawer = false
      this.roleEt = this.initRoleData()
    },

    /*
                  async searchRoles() {
                      qeuryRoleLike({}, this.query.pageNum - 1, this.query.pageSize).then(resp => {
                          if (resp.status === 200) {
                              this.tableDataTotal = resp.data.totalElements
                              this.tableData = resp.data.content
                          }
                      }).catch(e => {
                      })
                  },
      */

    // 加载表格数据
    async searchRoles() {
      this.tableLoading = true
      try {
        const res = await userRole(this.query)
        this.tableDataTotal = res.data.total
        this.tableData = res.data.records
      } catch (e) {
        console.error(`获取角色管理错误：${e}`)
      } finally {
        this.tableLoading = false
      }
    },
    $_search() {
      this.query.pageNum = 1
      this.searchRoles()
    },
    changePage(e) {
      this.searchRoles()
    },
    changeSize(e) {
      this.searchRoles()
    },

    add() {
      this.ops = 'new'
      this.drawer = true
    },
    edit(data) {
      this.ops = 'edit'

      Object.keys(data).forEach(key => {
        this.roleEt[key] = data[key]
      })
      this.drawer = true
    },
    grantUi(data) {
      this.ops = 'grant'
      this.roleEt = {
        id: data.roleId,
        name: data.roleName,
        description: data.description,
      }
      this.drawer = true
    },

    // grantApi(data) {
    //   this.ops = 'grantApi'
    //   this.roleEt = {
    //     id: data.id,
    //     name: data.name,
    //     description: data.description,
    //   }
    //   this.drawer = true
    // },
    async del(item) {
      this.$set(item, 'btnLoading', true)
      let userMembers = 0

      const res = await selectUserRoleCount({ id: item.roleId })
      userMembers = res.data
      this.$set(item, 'btnLoading', false)

      // this.doDeleteGroup(item, userMembers)

      // 如果组已经有使用，不能删除
      if (userMembers > 0) {
        this.$notify.info('error', this.$t('role.hint'))
        // `无法删除已使用的用户组，已被${userMembers}个用户引用！`,
      } else {
        this.doDeleteGroup(item, userMembers)
      }
    },
    doDeleteGroup(item, userMembers) {
      this.$swal({
        title: this.$t('role.swal.title'),
        // text: `角色【${item.roleName}】已关联${userMembers}个用户，确认是否删除此角色？`,
        text: this.$t('role.swal.text', [item.roleName, userMembers]),
        icon: 'warning',
        showCancelButton: true,
        reverseButtons: true,
        confirmButtonText: this.$t('action.confirm'),
        cancelButtonText: this.$t('action.cancel'),
        customClass: {
          confirmButton: 'sweet-btn-primary',
        },
      }).then(result => {
        if (result.isConfirmed) {
          // const ids = { ids: data.id }
          const params = { id: item.roleId }
          delRole(params).then(resp => {
            if (resp.code === 200) {
              this.searchRoles()
              this.$notify.info(
                'success',
                this.$t('global.hint.del', [this.$t('role.currentTitle')]),
              )
            }
          })
        }
      })
    },
    initRoleData() {
      return {
        roleId: '',
        roleName: '',
        description: '',
      }
    },
    save(callback) {
      if (this.ops === 'grant') {
        this.$refs.grantMenuRef.save(callback)
      } else if (this.ops === 'grantApi') {
        this.$refs.grantApiRef.save(callback)
      } else {
        this.$refs.roleEditRef.save(callback)
      }
    },
  },
}
</script>
<style lang="scss" scoped></style>
