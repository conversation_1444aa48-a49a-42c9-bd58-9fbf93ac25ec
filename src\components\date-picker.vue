<template>
  <div class="time-box">
    <v-text-field
      v-model="date1"
      ref="refTime"
      :label="label"
      append-icon="mdi-calendar-range-outline"
      :rules="rules"
      :disabled="disabled"
    ></v-text-field>
    <el-date-picker
      :type="type"
      v-model="date"
      popper-class="elDatePicker1"
      :picker-options="options"
      :default-time="defaultTime"
      value-format="yyyy-MM-dd HH:mm:ss"
      placeholder=""
      @focus="handleEta"
      :disabled="disabled"
    >
    </el-date-picker>
  </div>
</template>
<script>
import { format } from 'date-fns'
export default {
  props: {
    defaultTime: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    label: {
      type: String,
      default: '',
    },
    rules: {
      type: Array,
      default: () => [],
    },
    type: {
      type: String,
      default: 'datetime',
    },
    formatTime: {
      type: String,
      default: 'yyyy-MM-dd HH:mm:ss',
    },
  },
  watch: {
    date(val) {
      this.date1 = val ? format(new Date(val), this.formatTime) : ''
      this.$emit('handleTime', this.date1)
    },
  },
  data() {
    return {
      date: '',
      date1: '',
      options: {
        // 时间不能大于当前时间
        disabledDate: time => {
          return time.getTime() < new Date().getTime() - 24 * 60 * 60 * 1000
        },
      },
    }
  },
  methods: {
    handleEta() {
      this.$refs['refTime'].focus()
    },
  },
}
</script>
<style lang="scss" scoped>
.time-box {
  position: relative;
  .el-date-editor {
    position: absolute;
    top: 8px;
    left: 0;
    width: 90%;
    ::v-deep .el-input__icon {
      line-height: 48px !important;
    }
    ::v-deep .el-input__inner {
      border: 0px solid #dcdfe6 !important;
      padding: 0 !important;
      opacity: 0 !important;
    }
    ::v-deep .el-input__prefix {
      display: none;
    }
  }
}
</style>
