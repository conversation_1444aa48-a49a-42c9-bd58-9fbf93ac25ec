module.exports = {
  root: true,
  env: {
    node: true,
  },
  extends: ['plugin:vue/strongly-recommended', '@vue/standard'],
  rules: {
    'no-console': 'off',
    // 'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    'no-debugger': 'off',
    'generator-star-spacing': 'off',
    'no-mixed-operators': 0,
    'vue/max-attributes-per-line': 0,
    'vue/attribute-hyphenation': 0,
    'vue/html-self-closing': 0,
    'vue/component-name-in-template-casing': 0,
    'vue/html-closing-bracket-spacing': 0,
    'vue/singleline-html-element-content-newline': 0,
    'vue/no-unused-components': 0,
    'vue/multiline-html-element-content-newline': 0,
    'vue/no-use-v-if-with-v-for': 0,
    'vue/html-closing-bracket-newline': 0,
    'vue/no-parsing-error': 0,
    'no-tabs': 0,
    'space-before-function-paren': 0,
    'no-unused-vars': 0,
    'prefer-const': 0,
    eqeqeq: 0,
    'no-useless-rename': 0,
    'camel-case': 0,
    'vue/html-indent': 0,
    'vue/max-attributes-per-line': 0,
    'no-redeclare': 0,
    'spaced-comment': 0,
    'vue/mustache-interpolation-spacing': 0,
    'eol-last': 0,
    'standard/no-callback-literal': 0,
    'comma-dangle': 0,
    quotes: [
      2,
      'single',
      {
        avoidEscape: true,
        allowTemplateLiterals: true,
      },
    ],
    semi: [
      2,
      'never',
      {
        beforeStatementContinuationChars: 'never',
      },
    ],
    'no-delete-var': 2,
    // 'prefer-const': [
    //   2,
    //   {
    //     'ignoreReadBeforeAssign': false
    //   }
    // ],
    'template-curly-spacing': 'off',
    indent: 'off',
    'no-useless-escape': 0,
  },
  parserOptions: {
    parser: 'babel-eslint',
  },
  overrides: [
    {
      files: ['**/__tests__/*.{j,t}s?(x)', '**/tests/unit/**/*.spec.{j,t}s?(x)'],
      env: {
        jest: true,
      },
    },
  ],
  globals: {
    AMap: false,
    AMapUI: false,
    LoadProject: false,
    ProgressLine: false,
    ShowCalendarWindow: false,
    ShowResourcesWindow: false,
    SaveProject: false,
    TimeSaveProject: false,
    AllSaveProject: false,
    createGantt: false,
    exportImage: false,
    LoadProjectAPP: false,
    UUID: false,
    mini: false,
    oldvalue: false,
    ProjectMenu: false,
    mini_JSPath: false,
    params: false,
    obj: false,
    CalendarWindow: false,
    TaskWindow: false,
    LoadProjectByUnitProject: false,
  },
}
