const message = {
  currentTitle: 'Message',
  titleList: {
    all: 'All messages',
    notice: 'Notices',
    ticket: 'Tickets',
    alert: 'Alerts',
  },
  btn: {
    read: 'Current page read',
    note: 'Notification settings',
  },
  headers: {
    messageType: 'Message Type',
    title: 'Message Title',
    content: 'Message Content',
    createDate: 'Create Time',
    status: 'Status',
  },
  hint: {
    read: '{0} messages have been read',
    read1: 'The message have been read',
    read2: 'All messages on the current page have been read',
  },
  note: {
    open: 'Open Notification',
    time: 'Poll Time',
    yes: 'Yes',
    no: 'No',
  },
}

export default message
