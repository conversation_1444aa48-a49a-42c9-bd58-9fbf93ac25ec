import Cookies from 'js-cookie'

export function getToken() {
  // const token = localStorage.getItem('token')
  const token = Cookies.get('token', { path: process.env.BASE_URL })
  return token
}

export function setToken(token) {
  // localStorage.setItem('token', token)
  // token8小时过期
  Cookies.set('token', token, {
    expires: (1 / 24) * 8,
    path: process.env.BASE_URL,
  })
}

export function removeToken() {
  // localStorage.removeItem('token')
  Cookies.remove('token', { path: process.env.BASE_URL })
}
// apiKey
export function getApiKey() {
  const apiKey = Cookies.get('X-Api-Key', { path: process.env.BASE_URL })
  return apiKey
}

export function setApiKey(ApiKey) {
  Cookies.set('X-Api-Key', ApiKey, {
    expires: (1 / 24) * 8,
    path: process.env.BASE_URL,
  })
}

export function removeApiKey() {
  Cookies.remove('X-Api-Key', { path: process.env.BASE_URL })
}
