const ai = {
  totalNumberOfSamples: '样本总数',
  lastLearningStage: '上次学习阶段',
  basicInformation: '基础信息',
  featureType: '特征类型',
  dataType: '数据类型',
  startingTimeOfData: '数据起始时间',
  dataDeadline: '数据截止时间',
  alarmID: '告警ID',
  vehicleModel: '车型',
  classification: '分类',
  typesOf: '类型',
  selectModel: {
    title: '选择模型',
    tips: '我们提供多样化AI模型选择。无论是即刻体验，还是深入对比，致力于帮助您找到最匹配的解决方案',
    btn: 'AI智能分析',
    tips2: '分析成功',
  },
  warning: '请选择至少一个模型再进行分析',
  empty: {
    title: '未配置模型',
    tips: '您还未配置模型，请联系管理员',
    btn: '联系管理员',
  },
  loading: {
    title: '加载中',
    tips: '模型加载中，请您耐心等待',
  },
  error: {
    title: '网络异常',
    tips:
      '连接遇到阻碍：我们暂时无法建立与服务器的通信，这可能是由于您的网络连接不稳定或我们的服务器正在进行短暂维护。\n' +
      '请检查您的网络设置并稍后重试。',
    tips2: '如果多次尝试未果，请查看其他网络应用是否正常工作，以帮助定位问题。',
    tips3: '网络连接失败。',
    tip4: '此操作将永久覆盖原有记录。请确认是否继续。',
    tip5: 'Token超额',
    btn1: '尝试重新连接',
    btn2: '联系我们',
  },
  timeOut: {
    title: 'Token使用超过配额',
    tips: '尊敬的用户，您当前的Token使用量已达到上限。为确保服务不受影响，建议您考虑升级您的配额计划。',
    tips2: '如有任何疑问，或需要即刻提升Token配额，请随时与我们取得联系。',
    btn: '与我们取得联系',
  },
  result: {
    drawer: {
      title: '请选择模型',
      tips: '请选择1个引擎进行对比分析',
      tips2: '重新分析成功',
      tips3: '重新分析失败',
    },
    del: {
      title: '删除记录',
      tips: '删除后记录无法恢复，确定删除吗？',
    },
    save: '保存成功',
    title: '未选择模型',
    tips: '点击右下角加号，添加模型吧',
  },
}

export default ai
