<template>
  <div class="w-100">
    <v-menu
      ref="refMenu"
      content-class="bg-white search-tree-box"
      max-height="300"
      offset-y
      nudge-right="0"
      transition="slide-y-transition"
      eager
      :top="top"
      :bottom="bottom"
    >
      <template v-slot:activator="{ on, attrs }">
        <v-text-field
          ref="refInput"
          v-model.trim="filterText"
          :label="labelText"
          color="primary"
          :outlined="outlinedFlag"
          :dense="isDense"
          :clearable="clearable"
          v-on="on"
          v-bind="attrs"
          :style="{ width: width + 'px' }"
          :rules="ruleFlag ? [v => !!v || labelText + '是必填的'] : []"
          :disabled="disabled"
          hide-details="auto"
        ></v-text-field>
      </template>
      <el-tree
        ref="tree"
        class="tree-line px-3 py-2"
        :data="treeData"
        :props="defaultProps"
        :empty-text="$t('el.tree.emptyText')"
        :default-expand-all="expandFlag"
        node-key="id"
        icon-class="icon-tree"
        :indent="0"
        :expand-on-click-node="false"
        :check-strictly="true"
        :filter-node-method="filterNode"
        :show-checkbox="showCheckbox"
        @check="change"
        @node-click="clickNode"
      >
        <template slot-scope="{ node, data }">
          <div
            v-if="organizationFlag"
            class="custom-tree-node d-flex align-center"
          >
            <vsoc-icon
              v-if="data.type === '0'"
              icon="icon-shujiegouyiji"
              class="action-btn"
              size="16px"
            ></vsoc-icon>
            <vsoc-icon
              v-else
              icon="icon-shujiegousanji"
              class="action-btn"
              size="16px"
            ></vsoc-icon>
            <div
              :class="['ml-2', 'lable-text', data.isShow ? 'is-click' : '']"
              :style="{ color: data.state === '1' ? '#bcbdc0' : '#1F2533' }"
            >
              {{ node.label }}
            </div>
          </div>
          <div v-else>
            {{ node.label }}
          </div>
        </template>
      </el-tree>
    </v-menu>
  </div>
</template>

<script>
export default {
  props: {
    treeData: Array,
    defaultProps: Object,
    showCheckbox: Boolean,
    expandFlag: Boolean,
    labelText: String,
    organizationFlag: Boolean,
    width: String,
    outlinedFlag: Boolean,
    top: {
      default: false,
      type: Boolean,
    },
    bottom: {
      default: false,
      type: Boolean,
    },
    ruleFlag: {
      default: false,
      type: Boolean,
    },
    isDense: {
      default: true,
      type: Boolean,
    },
    clearable: {
      default: true,
      type: Boolean,
    },
    disabled: {
      default: false,
      type: Boolean,
    },
  },

  data() {
    return {
      filterText: '',
      show: false,
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree && this.$refs.tree.filter(val)
      if (!val) {
        this.change()
      }
    },
  },
  created() {},
  beforeDestroy() {},
  methods: {
    change(data, e, l) {
      this.$emit('change', data, e)
    },
    filterNode(value, data) {
      if (!value) return true
      return data[this.defaultProps.label].indexOf(value) !== -1
    },
    clickNode(item) {
      this.$emit('click', [item])
    },
    addItem(item) {
      this.$emit('addItem', item)
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .el-tree-node__content:hover {
  background-color: #fff;
}
::v-deep .el-tree-node:focus > .el-tree-node__content {
  background-color: #fff;
}
::v-deep .custom-tree-node .theme--dark.v-btn {
  color: #fff;
}
::v-deep .custom-tree-node {
  height: 34px;
}
::v-deep .el-tree-node__content .lable-text.is-click {
  color: $primary !important;
  border-bottom: 1px solid $primary;
}

::v-deep .tree-line {
  .el-tree-node {
    position: relative;
    // padding-left: 16px; // 缩进量
  }
  .el-tree-node__content {
    // margin-bottom: 12px;
    height: 34px !important;
    font-size: $font-size-content;
    color: #1f2533;
  }
  // .lable-text {
  //   height: 34px;
  // }

  .el-tree-node__children {
    padding-left: 42px; // 缩进量
  }
  // 竖线
  .el-tree-node::before {
    content: '';
    height: 100%;
    width: 1px;
    position: absolute;
    left: -37px;
    top: -36px;
    border-width: 1px;
    border-left: 1px dashed $color-dividers--light;
  }

  // 当前层最后一个节点的竖线高度固定
  .el-tree-node:last-child::before {
    height: 54px; // 可以自己调节到合适数值
  }

  // 横线
  .el-tree-node::after {
    content: '';
    width: 34px;
    height: 20px;
    position: absolute;
    left: -36px;
    top: 18px;
    border-width: 1px;
    border-top: 1px dashed $color-dividers--light;
  }

  // 去掉最顶层的虚线，放最下面样式才不会被上面的覆盖了
  & > .el-tree-node::after {
    border-top: none;
  }
  & > .el-tree-node::before {
    border-left: none;
  }

  // 展开关闭的icon
  .el-tree-node__expand-icon {
    font-size: $font-size-base;
    padding: 0 !important;
    padding-left: 0;
    margin-right: 8px;
    width: 12px;
    height: 12px;
    // 叶子节点（无子节点）
    &.is-leaf {
      color: transparent;
      //   width: 0;
      // display: none; // 也可以去掉
    }
  }
  .el-tree-node__children {
    .el-tree-node__expand-icon {
      &.is-leaf {
        color: transparent;
        //   width: 0;
        display: none; // 也可以去掉
      }
    }
  }
}

::v-deep .el-icon-circle-plus {
  color: $primary;
}
::v-deep .icon-tree {
  // margin: 0 5px 0 10px;
  position: relative;
  background: url('../../assets/images/add.png');
  background-size: 100% 100%;
}
::v-deep .icon-tree.expanded {
  background: url('../../assets/images/sub.png');
  background-size: 100% 100%;
  transform: 0 !important;
}
::v-deep .is-leaf {
  background-image: none;
  background-size: 100% 100%;
}
::v-deep .el-tree-node__expand-icon {
  transform: rotate(0deg) !important;
}
::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: $primary !important;
  border-color: $primary !important;
}
::v-deep .el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: $primary !important;
  border-color: $primary !important;
}
::v-deep .el-checkbox__inner:hover {
  border-color: $primary !important;
}
::v-deep .el-checkbox__input.is-focus .el-checkbox__inner {
  border-color: $primary !important;
}
</style>
