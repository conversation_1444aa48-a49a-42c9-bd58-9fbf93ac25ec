<template>
  <vsoc-chart
    :echartId="`car-pie-${Math.random()}`"
    class="box-chart d-flex align-center pb-3"
    :option="option"
  ></vsoc-chart>
</template>

<script>
import VsocChart from '@/components/VsocChart.vue'
import { numberToFormat } from '@/util/filters'
import {} from 'date-fns'
import { mean, sum } from 'lodash'
import { getRoundSize } from '../chart'
const list = [
  { name: '迈腾', value: 254 },
  // { name: '帕萨特', value: 1000 },
  // { name: '宝来', value: 360 },
  // { name: '高尔夫', value: 459 },
]
export default {
  name: 'TabItem1',
  props: {
    status: {
      type: Array,
      default: () => {
        return list
      },
    },
    // total: [Number, String],
  },
  components: {
    VsocChart,
  },
  data() {
    return {
      total: 0,
      avg: 0,
    }
  },
  computed: {
    clientWidth() {
      return this.$store.state.global.clientWidth
    },
    option() {
      let data = []
      let data1 = []
      if (this.status.length === 0) {
        // 暂无数据的环
        return {
          backgroundColor: 'transparent',
          color: ['#4CA3B6', '#4A687B', '#1B3F82', '#177754'],
          legend: { show: false },
          textStyle: {
            fontFamily: 'Fontquan-XinYiGuanHeiTi',
            fontStyle: 'normal',
            fontWeight: 400,
            color: '#ffffffcc',
          },
          title: {
            text: 'None',
            top: '40%',
            left: '49%',
            textAlign: 'center',
            textStyle: {
              fontSize: getRoundSize(22),
              lineHeight: getRoundSize(26),
              letterSpacing: 0.08,
              color: '#FFFFFF',
              opacity: 0.8,
            },
          },
          series: [
            {
              name: '外面的环',
              radius: ['75%', '80%'],
              itemStyle: { opacity: 1 },
              label: {
                position: 'outside',
                alignTo: 'edge',
                edgeDistance: 10,
                show: false,
              },
              labelLine: { length: 10, length2: 5, lineStyle: { width: 1 } },
              type: 'pie',
              clockwise: false,
              emphasis: { scale: true, scaleSize: 2 },
              center: ['50%', '52%'],
              bottom: 8,
              percentPrecision: 0,
              data: [{ value: 0, name: '空', percent: '100%' }],
            },
            {
              name: '',
              radius: ['45%', '65%'],
              type: 'pie',
              clockwise: false,
              emphasis: { scale: true },
              center: ['50%', '52%'],
              bottom: 8,
              percentPrecision: 0,
              itemStyle: { opacity: 0.56 },
              label: { show: false },
              data: [
                {
                  value: 100,
                  name: '空',
                  label: { show: false },
                  labelLine: { show: false },
                },
              ],
            },
          ],
        }
      }
      let numList = this.status.map(v => v.value)
      this.total = sum(numList)
      let avg = numList.length > 1 ? mean(numList) / 10 : 0
      // avg = avg / this.total
      for (var i = 0; i < this.status.length; i++) {
        data.push(
          {
            value: this.status[i].value,
            name: this.status[i].name,
            percent: this.status[i].percent,
            // itemStyle: {
            //   normal: {
            //     label: {
            //       show: true, //false不显示饼图上的标签
            //       position: 'outside', //inside（在饼图上显示）,outside(默认就会出现引导线) center
            //       formatter: '{b}:{c}',
            //     },
            //     //只有设置了label:show=ture;position=outside 设置labelLine才会有效
            //     labelLine: {
            //       show: true, //显示引导线
            //       length: 500, //连接饼图第1段线条的长度 length length2 不写自适应
            //       length2: 300, //链接饼图第2段线条长度
            //       smooth: true, //是否光滑连接线
            //     },
            //   },
            // },
          },
          {
            value: avg,
            name: '',
            itemStyle: {
              color: 'rgba(0, 0, 0, 0)',
              borderColor: 'rgba(0, 0, 0, 0)',
              borderWidth: 0,
            },
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
          },
        )
        data1.push(
          {
            value: this.status[i].value,
            name: this.status[i].name,
            label: {
              show: false, //false不显示饼图上的标签
            },
            //只有设置了label:show=ture;position=outside 设置labelLine才会有效
            labelLine: {
              show: false, //显示引导线
            },
          },
          {
            value: avg,
            name: '',
            itemStyle: {
              color: 'rgba(0, 0, 0, 0)',
              borderColor: 'rgba(0, 0, 0, 0)',
              borderWidth: 0,
            },
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
          },
        )
      }
      let common = {
        type: 'pie',
        clockwise: false,
        emphasis: {
          scale: true,
          scaleSize: 2,
        },
        center: ['50%', '52%'],
        bottom: getRoundSize(10),
        percentPrecision: 0,
      }
      let seriesOption = [
        {
          name: '外面的环',
          radius: ['75%', '80%'],
          itemStyle: {
            opacity: 1,
          },
          label: {
            position: 'outside',
            alignTo: 'edge',
            edgeDistance: 10,
            rich: {
              value: {
                fontSize: getRoundSize(12),
                lineHeight: getRoundSize(22),
                fontFamily: 'PingFang HK',
                color: '#FFFFFF',
                opacity: 0.8,
                padding: [getRoundSize(8), 0],
              },
              name: {
                fontSize: getRoundSize(14),
                lineHeight: getRoundSize(22),
                fontFamily: 'PingFang HK',
                color: '#FFFFFF',
                opacity: 0.8,
              },
              legend: {
                // width: getRoundSize(8),
                // height: getRoundSize(8),
                padding: [0, getRoundSize(2), 0, 0],
                fontSize: getRoundSize(18),
                color: 'inherit',
              },
            },
            formatter: function (params) {
              if (params.name !== '') {
                return `{legend|●}\t{name|${params.name}}\n{value|${params.value}\t(${params.data.percent})}`
              } else {
                return ''
              }
            },
          },
          labelLine: {
            length: 10,
            length2: 5,
            lineStyle: {
              width: 1,
            },
          },
          ...common,
          data: data,
        },
        {
          name: '',
          radius: ['45%', '65%'],
          ...common,
          itemStyle: {
            opacity: 0.56,
          },
          label: {
            show: false,
          },
          emphasis: {
            scale: true,
          },
          data: data1,
        },
      ]
      return {
        backgroundColor: 'transparent',
        color: ['#4CA3B6', '#4A687B', '#1B3F82', '#177754'],
        legend: {
          show: false,
        },
        textStyle: {
          fontFamily: 'Fontquan-XinYiGuanHeiTi',
          fontStyle: 'normal',
          fontWeight: 400,
          color: '#ffffffcc',
        },
        title: {
          text: `${numberToFormat(this.total) || 'None'}`,
          top: '40%',
          left: '49%',
          // top: 'center',
          // left: 'center',
          textAlign: 'center',
          textStyle: {
            fontSize: getRoundSize(22),
            lineHeight: getRoundSize(26),
            letterSpacing: 0.08,
            color: '#FFFFFF',
            opacity: 0.8,
          },
        },
        series: seriesOption,
        // series: [
        //   {
        //     name: '里面的环',
        //     type: 'pie',
        //     // radius: [40, 45],
        //     radius: ['39%', '45%'],
        //     emphasis: {
        //       scale: false,
        //     },
        //     label: {
        //       show: false,
        //     },
        //     labelLine: {
        //       show: false,
        //     },
        //     color: '#103d60',
        //     data: [{ value: 1548, name: '填充的值' }],
        //   },
        //   {
        //     name: '中间的环',
        //     type: 'pie',
        //     percentPrecision: 0,
        //     radius: ['50%', '67%'],
        //     // radius: [50, 70],
        //     // height: "33.33%",
        //     left: 'center',
        //     width: '90%',
        //     emphasis: {
        //       scale: true,
        //     },
        //     label: {
        //       show: true,
        //       alignTo: 'edge',
        //       formatter: '{name|{b}}\n{value|{c}\t({d}%)}',
        //       minMargin: 5,
        //       edgeDistance: 10,
        //       lineHeight: 15,
        //       rich: {
        //         value: {
        //           fontSize: getRoundSize(10),
        //           lineHeight: getRoundSize(18),
        //           fontFamily: 'PingFang HK',
        //           fontStyle: 'normal',
        //           fontWeight: 400,
        //           color: '#FFFFFF',
        //           opacity: 0.8,
        //         },
        //         name: {
        //           fontSize: getRoundSize(14),
        //           lineHeight: getRoundSize(18),
        //           fontFamily: 'PingFang HK',
        //           fontStyle: 'normal',
        //           fontWeight: 400,
        //           color: '#FFFFFF',
        //           opacity: 0.8,
        //         },
        //       },
        //     },
        //     labelLine: {
        //       show: true,
        //       length: 15,
        //       length2: 0,
        //       // maxSurfaceAngle: 80,
        //       // minTurnAngle: 90,
        //       lineStyle: {
        //         width: 3,
        //       },
        //     },
        //     data: this.status,
        //   },
        // ],
      }
    },
  },
}
</script>
<style scoped lang="scss">
// .box-chart {
//   // margin-top: 5%;
//   // margin-left: -4px;
//   background: url('../../images/box-pie.png') no-repeat;
//   background-size: cover;
//   background-position: center;
// }
</style>
