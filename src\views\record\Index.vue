<!-- 告警处置记录管理 -->
<template>
  <div>
    <bread-crumb
      ref="topHeader"
      :showDoQuery="showDoQuery"
      :filterList="filterList"
      :showAdvance="true"
    >
    </bread-crumb>

    <v-card tile class="pa-0" elevation="0">
      <v-card-text class="main-content">
        <div class="mb-3 d-flex justify-space-between">
          <div class="d-flex justify-end align-center"></div>
          <div class="d-flex align-center">
            <vsoc-date-range
              v-model="dateRange.range"
              no-title
              :menu-props="dateRange.menuProps"
              @input="onChangeDate"
              @search="$_search"
            >
              <template v-slot:text="{ on, attrs }">
                <v-text-field
                  type="button"
                  clearable
                  outlined
                  dense
                  class="append-icon-max me-3 date-width"
                  readonly
                  hide-details
                  color="primary"
                  large
                  :label="$t('global.createDate')"
                  prepend-inner-icon="mdi-calendar-range-outline"
                  :value="RANGE_STR(query.startDate, query.endDate)"
                  @click:clear="onChangeDate({ start: '', end: '' })"
                ></v-text-field>
              </template>
            </vsoc-date-range>
            <v-btn class="primary--text bg-btn" elevation="0" @click="$_search">
              <span>{{ $t('action.search') }}</span>
            </v-btn>
          </div>
        </div>

        <v-data-table
          fixed-header
          :items-per-page="query.pageSize"
          :height="tableHeight"
          hide-default-footer
          :headers="headers"
          :items="tableData"
          :loading="tableLoading"
          class="flex-1 thead-light"
        >
          <template v-slot:item.disposeName="{ item }">
            <div v-show-tips style="width: 160px">{{ item.disposeName }}</div>
          </template>

          <template v-slot:item.alarmId="{ item }">
            <a
              v-if="item.alarmId"
              style="max-width: 120px"
              class="text-overflow-hide d-block"
              href="javaScript:;"
              @click="jumpAlert(item)"
              >{{ item.alarmId }}
            </a>
            <span v-else>N/A</span>
          </template>

          <template v-slot:item.disposeType="{ item }">
            <div
              v-if="item.disposeType && disposeTypeEum[item.disposeType]"
              style="width: 120px"
            >
              {{ disposeTypeEum[item.disposeType].text }}
            </div>
            <span v-else>N/A</span>
          </template>

          <template v-slot:item.status="{ item }">
            <div v-if="disposeResultStatusEum[item.status]">
              <v-badge
                dot
                inline
                offset-x="10"
                :offset-y="-18"
                :color="
                  disposeResultStatusEum[item.status]
                    ? disposeResultStatusEum[item.status].color
                    : ''
                "
                class="mr-1"
              ></v-badge>
              <span>{{ disposeResultStatusEum[item.status].text }}</span>
            </div>
            <span v-else>N/A</span>
          </template>

          <template v-slot:item.alarmType="{ item }">
            <div v-show-tips style="width: 160px">
              {{ item.alarmType | dataFilter }}
            </div>
          </template>

          <template v-slot:item.createDate="{ item }">
            <span>{{ item.createDate | dataFilter }}</span>
          </template>
          <template v-slot:item.updateDate="{ item }">
            <span>{{ item.updateDate | toDate | dataFilter }}</span>
          </template>

          <template v-slot:item.actions="{ item }">
            <v-btn v-has:record-detail icon @click="checkInfo(item)">
              <vsoc-icon
                v-show-tips="$generateBtnTitle('record-detail')"
                type="fill"
                icon="icon-xiangqing"
                class="action-btn"
                size="x-large"
              ></vsoc-icon>
            </v-btn>
          </template>
        </v-data-table>

        <!-- 分页器 -->
        <vsoc-pagination
          :page.sync="query.pageNum"
          :size.sync="query.pageSize"
          :total="tableDataTotal"
          @change-size="$_search"
          @change-page="getTableData"
        >
        </vsoc-pagination>
      </v-card-text>
    </v-card>
    <records-drawer
      ref="recordsDrawer"
      v-model="showAdvanceSearch"
      @do-query="doQuery"
    ></records-drawer>

    <vsoc-drawer
      v-model="showDetail"
      :hideFooter="true"
      width="400px"
      :title="$t('record.headers.actions')"
    >
      <v-row class="pt-4 color-base">
        <v-col cols="12" class="d-flex align-center">
          <span class="font-weight-bold mr-4 text-no-wrap"
            >{{ $t('record.method') }}:</span
          >

          <span
            v-show-tips="currentRecord.method"
            class="d-inline-block w-75"
            style="vertical-align: text-bottom"
          >
            {{ currentRecord.method }}
          </span>
        </v-col>

        <v-col cols="12">
          <span class="font-weight-bold mr-4">{{ $t('record.time') }}:</span>
          <span v-show-tips>{{ currentRecord.createDate | toDate }}</span>
        </v-col>

        <v-col cols="6">
          <span class="font-weight-bold mr-4"
            >{{ $t('record.headers.status') }}:</span
          >
          <span v-if="disposeResultStatusEum[currentRecord.status]">
            <v-badge
              dot
              inline
              offset-x="10"
              :offset-y="-18"
              :color="
                disposeResultStatusEum[currentRecord.status]
                  ? disposeResultStatusEum[currentRecord.status].color
                  : ''
              "
              class="mr-1"
            ></v-badge>
            <span>{{ disposeResultStatusEum[currentRecord.status].text }}</span>
          </span>
        </v-col>

        <v-col cols="12" class="pa-0 pl-3">
          <v-tabs v-model="currentTabs" height="40">
            <v-tab class="text-base font-weight-bold">{{
              $t('record.response')
            }}</v-tab>
            <v-tab class="text-base font-weight-bold">
              {{ $t('record.request') }}
            </v-tab>
          </v-tabs>

          <div
            class="position-relative copy-box"
            v-if="
              currentRecord.resultParam &&
              currentRecord.requestParam &&
              showDetail
            "
          >
            <vsoc-code-mirror
              :code="
                currentTabs === 0
                  ? currentRecord.resultParam
                  : currentRecord.requestParam
              "
              :readOnly="true"
            ></vsoc-code-mirror>
            <v-btn icon v-show-tips="$t('action.copy')" class="copy-btn"
              ><vsoc-icon
                type="fill"
                class="action-btn"
                icon="icon-fuzhi"
                v-copy="
                  currentTabs === 0
                    ? currentRecord.resultParam
                    : currentRecord.requestParam
                "
            /></v-btn>
          </div>
        </v-col>
      </v-row>
    </vsoc-drawer>
  </div>
</template>

<script>
import { getResult, getResultInfo } from '@/api/record'
import BreadCrumb from '@/components/bread-crumb/index.vue'
import VsocFilter from '@/components/VsocFilter.vue'
import VsocPagination from '@/components/VsocPagination.vue'
import { format, subDays } from 'date-fns'

import { RANGE_STR } from '@/components/vsoc-date-range/constants'
import VsocDateRange from '@/components/vsoc-date-range/VsocDateRange.vue'
import VsocCodeMirror from '@/components/VsocCodeMirror.vue'
import VsocDrawer from '@/components/VsocDrawer.vue'
import { dataFilter } from '@/util/filters'

import {
  clearFilterItem,
  deepClone,
  handleFilterItem,
  setRemainingHeight,
} from '@/util/utils'
import RecordsDrawer from './RecordsDrawer.vue'

export default {
  name: 'RecordIndex',
  components: {
    VsocPagination,
    BreadCrumb,
    VsocFilter,
    VsocDateRange,
    RecordsDrawer,
    VsocDrawer,
    VsocCodeMirror,
  },
  filters: {
    dataFilter,
  },
  data() {
    return {
      currentTabs: 0,
      showDetail: false,
      showAdvanceSearch: false,
      dateRange: {
        range: {
          start: '',
          end: '',
        },
        menuProps: { offsetY: true, closeOnContentClick: false },
      },
      // 分页参数
      query: {
        pageNum: 1,
        pageSize: 10,
        alarmId: '',
        alarmTypeList: [],
        disposeName: '',
        disposeTypeList: [],
        status: '',
        startDate: '',
        endDate: '',
      },
      currentRecord: {
        method: '',
        createDate: '',
        status: '',
        resultParam: {},
        requestParam: {},
      },
      tableData: [],
      tableHeight: '34.5rem',
      tableDataTotal: 0,
      tableLoading: false,
      filterList: [],
      alertTypeMap: {},
    }
  },
  computed: {
    disposeResultStatusEum() {
      return this.$store.state.enums.enums.DisposeResultStatus
    },
    disposeTypeEum() {
      return this.$store.state.enums.enums.DisposeType
    },
    headers() {
      return [
        {
          text: this.$t('record.headers.alarmId'),
          value: 'alarmId',
          width: '120px',
        },
        {
          text: this.$t('record.headers.alarmType'),
          value: 'alarmType',
          width: '160px',
        },
        {
          text: this.$t('record.headers.disposeTypeName'),
          value: 'disposeType',
          width: '120px',
        },
        {
          text: this.$t('record.headers.status'),
          value: 'status',
          width: '140px',
        },
        {
          text: this.$t('record.headers.disposeName'),
          value: 'disposeName',
          width: '160px',
        },
        {
          text: this.$t('global.createDate'),
          value: 'createDate',
          width: '180px',
        },
        {
          text: this.$t('global.updateDate'),
          value: 'updateDate',
          width: '180px',
        },
        {
          text: this.$t('record.headers.actions'),
          value: 'actions',
          sortable: false,
          width: '140px',
        },
      ]
    },
  },
  watch: {
    // 监听筛选框内容，重新设置表格高度
    filterList() {
      this.$_setTableHeight()
    },
  },
  created() {
    this.$_setTableHeight()
    this.$bus.$on('resize', this.$_setTableHeight)
    if (this.$route.query.alarmId) {
      this.query.alarmId = this.$route.query.alarmId
      this.$_search()
    } else {
      //默认查询最近7天的数据:
      const defalutTime = [subDays(new Date(), 7), new Date()]
      const start = format(defalutTime[0], 'yyyy-MM-dd')
      const end = format(defalutTime[1], 'yyyy-MM-dd')
      const range = {
        start: [start, '00:00:00'].join(' ').trim(),
        end: [end, '23:59:59'].join(' ').trim(),
      }
      this.onChangeDate(range)
    }
  },
  beforeDestroy() {
    this.$bus.$off('resize', this.$_setTableHeight)
  },
  methods: {
    RANGE_STR,
    // 时间范围改变
    onChangeDate(range) {
      this.dateRange.range = range
      this.query.startDate = range.start
      this.query.endDate = range.end
      this.$_search()
    },

    onReset() {
      Object.assign(this.$data.query, this.$options.data.call(this).query)
      this.$_search()
    },
    // 查询
    $_search() {
      this.query.pageNum = 1
      this.getTableData()
      this.$nextTick(() => {
        this.$_appendFilterListItem()
      })
    },

    // 获取表格内容
    async getTableData() {
      try {
        // 保存查询参数
        const data = deepClone(this.query)
        this.tableLoading = true
        const res = await getResult(data)
        this.tableDataTotal = res.data.total
        this.tableData = res.data.records
      } catch (e) {
        console.error(`获取告警记录管理错误：${e}`)
      }
      this.tableLoading = false
    },

    $_setTableHeight() {
      this.$nextTick(() => {
        const filterFn = () => {
          return -this.$refs.topHeader.filterHeight
        }
        this.tableHeight = setRemainingHeight(filterFn)
      })
    },
    // 查看详情
    async checkInfo(item) {
      const res = await getResultInfo({ id: item.id })
      if (res.code === 200) {
        this.showDetail = true
        res.data.resultParam = res.data.resultParam
          ? JSON.parse(res.data.resultParam)
          : {}
        res.data.requestParam = res.data.requestParam
          ? JSON.parse(res.data.requestParam)
          : {}
        this.currentRecord = res.data
      }
    },
    //跳转至详情
    jumpAlert(item) {
      this.$router.push(`/alert/detail?id=${item.alarmId}`)
      // this.$router.push(
      //   `/alert/detail?id=${item.alarmId}&vehicleId=${item.vehicleId}&name=${item.alarmType}`,
      // )
    },
    showDoQuery() {
      this.showAdvanceSearch = true
      const data = deepClone(this.query)
      this.$refs.recordsDrawer.setModel(data)
    },
    //  高级查询
    doQuery(params) {
      this.query = deepClone(params.advanceQuery)
      this.alertTypeMap = deepClone(params.alertTypeMap)
      this.$_search()
    },
    // 重置
    clearAdvanceQuery() {
      this.advanceQuery = {
        pageNum: 1,
        pageSize: 10,
        alarmId: '',
        alarmType: '',
        disposeName: '',
        disposeType: '',
        status: '',
        startDate: '',
        endDate: '',
      }
    },

    // 添加查询条件
    $_appendFilterListItem() {
      const searchKeyList = [
        {
          key: 'status',
          type: 'String',
          label: 'record.headers.status',
          mapKey: 'disposeResultStatusEum',
        },
        {
          key: 'alarmId',
          type: 'String',
          label: 'record.headers.alarmId',
        },
        {
          key: 'disposeName',
          type: 'String',
          label: 'record.headers.disposeName',
        },
        {
          key: 'disposeTypeList',
          type: 'Array',
          mapKey: 'disposeTypeEum',
          label: 'record.headers.disposeTypeName',
        },
        {
          key: 'alarmTypeList',
          type: 'Array',
          mapKey: 'alertTypeMap',
          label: 'record.headers.alarmType',
        },
      ]
      handleFilterItem.call(this, searchKeyList)
      // 状态
      // const status = this.filterList.find(v => v.key === 'status')
      // if (status) {
      //   status.text = this.disposeResultStatusEum[status.value].text
      // }
    },

    $_clearFilter(item) {
      const bool = clearFilterItem.call(this, item)
      if (!bool) {
        this.$_search()
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.copy-btn {
  position: absolute;
  top: 0;
  right: 4px;
}
::v-deep .copy-box .CodeMirror {
  height: calc(100vh - 270px) !important;
}
</style>
