.logic-engine {
  .chip-box {
    padding: 0 24px !important;
    height: 30px !important;
    line-height: 30px !important;
    justify-content: center !important;
  }
  .white-btn.v-btn:focus::before {
    opacity: 0.08 !important;
  }
  .logic-engine-group-box {
    padding: 24px;
    // background: #f5f6fa;
    border-radius: $border-radius-xl;
    overflow: auto hidden;
    // box-shadow: $box-shadow-xl;
    &:not(:last-child) {
      // margin-bottom: 12px;
    }
  }
  .text-lable {
    color: #1f2533;
  }
  .del-btn1 {
    width: 36px;
    height: 40px;
    background: #fff;
    border-radius: 4px;
    line-height: 40px;
    cursor: pointer;
    color: #686e7c;
    margin-left: 24px;
    margin-top: 24px;
    .action-btn1 {
      justify-content: center;
    }
  }
  .del-btn1:hover {
    background: rgba(1, 76, 241, 0.1);
    color: $primary !important;
  }
  .logic-engine-item {
    .logic-engine-item__content {
      width: 100%;
      display: flex;
      align-items: flex-start;

      .select-item__delete {
        transform: translateY(24px);
      }
    }
    .signal-item {
      // min-width: calc(100% / 7);
      // max-width: 20%;
    }
    .signal-item {
      width: 14%;
    }
    .logic-select-box {
      display: flex;
      flex: 1;
      .select-item {
        width: 100%;
        margin-left: 8px;
      }
    }
    .select-item-3 {
      flex-shrink: 0;
      width: 30%;
      margin-right: 16px;
    }

    .add {
      width: 100%;
      .add-btn {
        border-radius: 100px !important;
        width: 300px !important;
        height: 30px !important;
      }
    }

    .v-text-field__suffix {
      font-size: 1rem;
    }

    .logic__thecustom {
      height: 40px;
      // height: 2.875rem;
      border: 1px solid rgba($primary-shade--light, 0.3);
      color: $primary;
      padding: 0 1rem;
      text-align: center;
      line-height: 40px;
      border-radius: 0.25rem;
      cursor: pointer;
      transition: 0.2s;
      &:hover {
        border-color: $primary;
        color: $primary;
        background-color: rgba($color: $primary, $alpha: 0.1);
      }
    }
  }

  .logic-engine__error {
    padding: 8px 0;
    font-size: 14px;
    color: var(--v-error-base) !important;
  }

  .v-input--checkbox {
    transform: translateY(-5px);
    .v-input--selection-controls__input {
      margin-right: 0;
    }
  }

  .logic-error {
    .el-input__inner {
      border-color: $primary;
      border-width: 0.125rem;
    }

    .v-input__slot fieldset {
      color: $primary !important;
      border: 0.125rem solid currentColor;
    }
  }
}
