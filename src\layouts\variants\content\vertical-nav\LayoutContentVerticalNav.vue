<template>
  <!-- navMenuItems -->
  <layout-content-vertical-nav :nav-menu-items="$store.getters.menus">
    <slot></slot>

    <!-- Slot: Navbar -->
    <template
      #navbar="{ isVerticalNavMenuActive, toggleVerticalNavMenuActive }"
    >
      <div
        class="navbar-content-container"
        :class="{ 'expanded-search': shallShowFullSearch }"
      >
        <vertical-nav-menu-header
          @close-nav-menu="$emit('close-nav-menu')"
        ></vertical-nav-menu-header>
        <!-- Left Content: Search -->
        <!-- <div class="d-flex align-center">
          <v-icon
            v-if="$vuetify.breakpoint.mdAndDown"
            class="me-3"
            @click="toggleVerticalNavMenuActive"
          >
            {{ icons.mdiMenu }}
          </v-icon>
          <span class="font-weight-semibold text-h6">{{ appName }}</span>
          &nbsp;
          <span class="font-weight-semibold text-h6">{{ appTitle }}</span>
        </div> -->

        <!-- Right Content: I18n, Light/Dark, Notification & User Dropdown -->
        <div class="d-flex align-center right-row">
          <!-- <app-bar-search
            :shall-show-full-search.sync="shallShowFullSearch"
            :data="appBarSearchData"
            :filter="searchFilterFunc"
            :search-query.sync="appBarSearchQuery"
            @update:shallShowFullSearch="
              handleShallShowFullSearchUpdate(
                isVerticalNavMenuActive,
                toggleVerticalNavMenuActive,
              )
            "
          ></app-bar-search> -->

          <!-- <template v-if="$store.state.appConfig.isThemeAndTimeZone">
            <span>{{ userTimeZone }}</span>
            <v-divider
              class="ml-7 mr-4 my-2 opacity-2 white"
              vertical
            ></v-divider>
          </template> -->

          <app-bar-i18n></app-bar-i18n>
          <div class="mx-2" v-if="$store.state.appConfig.isThemeAndTimeZone">
            <app-bar-theme-switcher
              v-if="
                $store.getters.userInfo &&
                $store.getters.userInfo.roleId === 'admin' &&
                $store.state.appConfig.themeFlag === 'true'
              "
              class="mx-2"
            ></app-bar-theme-switcher>
          </div>

          <app-bar-notification></app-bar-notification>
          <v-divider
            class="ml-6 mr-4 my-2 opacity-2 white"
            vertical
          ></v-divider>
          <app-bar-user-menu class="ms-2"></app-bar-user-menu>
        </div>
      </div>
    </template>
    <!-- Slot: Footer -->
    <template #footer>
      <!-- <div class="d-flex justify-space-between">
        <span
          >COPYRIGHT &copy; {{ new Date().getFullYear() }}
          <a href="https://ACT.com" class="text-decoration-none">ACT</a
          ><span class="d-none d-md-inline">, All rights Reserved</span></span
        >
        <div class="align-center d-none d-md-flex">
          <span>Hand-crafted &amp; Made with</span>
          <v-icon color="error" class="ms-2">
            {{ icons.mdiHeartOutline }}
          </v-icon>
        </div>
      </div> -->
    </template>
    <!-- 回到顶部 -->
    <go-top style="bottom: 46px" />
    <template #v-app-content>
      <app-customizer class="d-none d-md-block"></app-customizer>
    </template>
  </layout-content-vertical-nav>
</template>

<script>
import VerticalNavMenuHeader from '@/@core/layouts/components/vertical-nav-menu/components/vertical-nav-menu-header/VerticalNavMenuHeader.vue'
import LayoutContentVerticalNav from '@/@core/layouts/variants/content/vertical-nav/LayoutContentVerticalNav.vue'
import navMenuItems from '@/navigation/vertical'
import AppCustomizer from '@core/layouts/components/app-customizer/AppCustomizer.vue'
// App Bar Components

import AppBarNotification from '@core/layouts/components/app-bar/AppBarNotification.vue'
import AppBarThemeSwitcher from '@core/layouts/components/app-bar/AppBarThemeSwitcher.vue'

import AppBarUserMenu from '@/@core/layouts/components/app-bar/AppBarUserMenu.vue'
import AppBarSearch from '@core/layouts/components/app-bar/AppBarSearch.vue'

import { mdiHeartOutline, mdiMenu } from '@mdi/js'

import { getVuetify } from '@core/utils'

// Search Data
import appBarSearchData from '@/assets/app-bar-search-data'

import AppBarI18n from '@/@core/layouts/components/app-bar/AppBarI18n.vue'
import GoTop from '@/components/go-top/index.vue'
import { getUserTimeZone } from '@/util/timezone'
import themeConfig from '@themeConfig'
import { computed, ref, watch } from '@vue/composition-api'
export default {
  components: {
    LayoutContentVerticalNav,
    VerticalNavMenuHeader,
    AppCustomizer,
    GoTop,

    // App Bar Components

    AppBarSearch,
    AppBarThemeSwitcher,
    AppBarUserMenu,
    AppBarNotification,
    AppBarI18n,
  },
  setup() {
    const $vuetify = getVuetify()

    // Search
    const appBarSearchQuery = ref('')
    const shallShowFullSearch = ref(false)
    const maxItemsInGroup = 5
    const totalItemsInGroup = ref({
      pages: 0,
      files: 0,
      contacts: 0,
    })
    watch(appBarSearchQuery, () => {
      totalItemsInGroup.value = {
        pages: 0,
        files: 0,
        contacts: 0,
      }
    })

    // NOTE: Update search function according to your usage
    const searchFilterFunc = (item, queryText, itemText) => {
      if (item.header || item.divider) return true

      const itemGroup = (() => {
        if (item.to !== undefined) return 'pages'
        if (item.size !== undefined) return 'files'
        if (item.email !== undefined) return 'contacts'

        return null
      })()

      const isMatched =
        itemText.toLocaleLowerCase().indexOf(queryText.toLocaleLowerCase()) > -1

      if (isMatched) {
        if (itemGroup === 'pages') totalItemsInGroup.value.pages += 1
        else if (itemGroup === 'files') totalItemsInGroup.value.files += 1
        else if (itemGroup === 'contacts') totalItemsInGroup.value.contacts += 1
      }

      return (
        appBarSearchQuery.value &&
        isMatched &&
        totalItemsInGroup.value[itemGroup] <= maxItemsInGroup
      )
    }

    // ? Handles case where in <lg vertical nav menu is open and search is triggered using hotkey then searchbox is hidden behind vertical nav menu overlaty
    const handleShallShowFullSearchUpdate = (
      isVerticalNavMenuActive,
      toggleVerticalNavMenuActive,
    ) => {
      if ($vuetify.breakpoint.mdAndDown && isVerticalNavMenuActive) {
        toggleVerticalNavMenuActive()
      }
    }

    const userTimeZone = computed(() => {
      return getUserTimeZone()
    })

    return {
      navMenuItems,
      handleShallShowFullSearchUpdate,

      // Search
      appBarSearchQuery,
      shallShowFullSearch,
      appBarSearchData,
      searchFilterFunc,

      icons: {
        mdiMenu,
        mdiHeartOutline,
      },
      appTitle: themeConfig.app.title,
      appName: themeConfig.app.name,
      userTimeZone,
    }
  },
}
</script>
<style lang="scss" scoped>
.navbar-content-container {
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-grow: 1;
  position: relative;
}

// ? Handle bg of autocomplete for blured appBar
.v-app-bar.bg-blur {
  .expanded-search {
    ::v-deep .app-bar-autocomplete-box div[role='combobox'] {
      background-color: transparent;
    }

    > .d-flex > button.v-icon {
      display: none;
    }

    // ===

    & > .right-row {
      visibility: hidden;
      opacity: 0;
    }

    ::v-deep .app-bar-search-toggler {
      visibility: hidden;
    }
  }
}
</style>
