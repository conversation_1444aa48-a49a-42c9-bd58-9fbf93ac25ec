// ? Original Vuetify Mixin: states
@mixin app-states($material) {
  &::before {
    opacity: 1;
    background-color: inherit;
  }

  &:hover::before {
    background-color: rgba(
      94,
      86,
      105,
      map-deep-get($material, 'states', 'hover')
    );
    // opacity: 1 !important;
  }

  &:focus::before {
    background-color: rgba(
      94,
      86,
      105,
      map-deep-get($material, 'states', 'focus')
    );
    // opacity: 1 !important;
  }

  &--active {
    @include app-active-states($material);
  }
}

// ? Original Vuetify Mixin: active-states
@mixin app-active-states($material) {
  &:hover::before,
  &::before {
    background-color: rgba(
      94,
      86,
      105,
      map-deep-get($material, 'states', 'activated')
    );
    // opacity: 1 !important;
  }

  &:focus::before {
    background-color: rgba(
      94,
      86,
      105,
      map-deep-get($material, 'states', 'pressed')
    );
    // opacity: 1 !important;
  }
}

// ? Inspired Vuetify Mixin: theme
@mixin theme--child($class) {
  .v-application.theme--light .#{$class} {
    @content ($material-light);
  }
  .v-application.theme--dark .#{$class} {
    @content ($material-dark);
  }
}

@mixin theme--single($class) {
  .v-application.theme--light {
    .#{$class} {
      @content ($material-light);
    }
  }
  .v-application.theme--dark {
    .#{$class} {
      @content ($material-dark);
    }
  }
}

// ? Original Vuetify Mixin: elevation
@mixin app-elevation($class, $z, $important: false) {
  @include theme--child($class) using ($material) {
    // ——— Completely Customized Shadows ——————— //

    @if $z == 1 {
      box-shadow: 0 1px 3px 0 rgba(map-get($material, 'shadow-color'), 0.2),
        0 2px 1px -1px rgba(map-get($material, 'shadow-color'), 0.12),
        0
          1px
          1px
          0
          rgba(map-get($material, 'shadow-color'), 0.14)
          if($important, !important, null);
    } @else if $z == 2 {
      box-shadow: 0 1px 3px 0 rgba(map-deep-get($material, 'shadow-color'), 0.2),
        0 3px 1px -2px rgba(map-deep-get($material, 'shadow-color'), 0.12),
        0
          2px
          2px
          0
          rgba(map-deep-get($material, 'shadow-color'), 0.14)
          if($important, !important, null);
    } @else if $z == 3 {
      box-shadow: 0
        4px
        8px -4px
        rgba(map-deep-get($material, 'shadow-color'), 0.42)
        if($important, !important, null);
    } @else if $z == 4 {
      box-shadow: 0
        6px
        18px -8px
        rgba(map-deep-get($material, 'shadow-color'), 0.56)
        if($important, !important, null);
    } @else if $z == 6 {
      box-shadow: 0
        2px
        10px
        0
        rgba(map-deep-get($material, 'shadow-color'), 0.1)
        if($important, !important, null);
    } @else if $z == 8 {
      box-shadow: 0
        4px
        14px
        0
        rgba(map-deep-get($material, 'shadow-color'), 0.14)
        if($important, !important, null);
    } @else if $z == 9 {
      box-shadow: 0
          5px
          6px -3px
          rgba(map-deep-get($material, 'shadow-color'), 0.2),
        0 3px 16px 2px rgba(map-deep-get($material, 'shadow-color'), 0.12),
        0
          9px
          12px
          1px
          rgba(map-deep-get($material, 'shadow-color'), 0.14)
          if($important, !important, null);
    } @else if $z == 12 {
      box-shadow: 0
          7px
          8px -4px
          rgba(map-deep-get($material, 'shadow-color'), 0.2),
        0 5px 22px 4px rgba(map-deep-get($material, 'shadow-color'), 0.12),
        0
          12px
          17px
          2px
          rgba(map-deep-get($material, 'shadow-color'), 0.14)
          if($important, !important, null);
    } @else if $z == 16 {
      box-shadow: 0
          8px
          10px -5px
          rgba(map-deep-get($material, 'shadow-color'), 0.2),
        0 6px 30px 5px rgba(map-deep-get($material, 'shadow-color'), 0.12),
        0
          16px
          24px
          2px
          rgba(map-deep-get($material, 'shadow-color'), 0.14)
          if($important, !important, null);
    } @else if $z == 24 {
      box-shadow: 0
        18px
        42px -6px
        rgba(map-deep-get($material, 'shadow-color'), 0.18)
        if($important, !important, null);
    }

    // ——— Vuetify Shadows w/ Color update ——————— //
    // TODO: Next update - Sync our color changes with shadows we didn't overridden

    // ——— Else for 0 elevation or any arbitrary value ——————— //
    @else {
      box-shadow: map-get($shadow-key-umbra, $z),
        map-get($shadow-key-penumbra, $z),
        map-get($shadow-key-ambient, $z) if($important, !important, null);
    }
  }
}

@mixin style-scroll-bar($module: 'cards') {
  // /* chrome & safari 浏览器 IE不支持修改*/
  // /*滚动条整体部分,必须要设置*/
  // /*滚动条的轨道*/
  &::-webkit-scrollbar {
    // box-sizing: content-box;
    width: 15px; /*no*/
    height: 15px; /*no*/
    border-radius: 4px;
  }
  // 轨道
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  .v-application.theme--dark &::-webkit-scrollbar-track {
    border-radius: 0 $border-radius-root $border-radius-root 0;
    background: rgba(map-deep-get($material-dark, $module), 1);
  }
  .v-application.theme--light &::-webkit-scrollbar-track {
    border-radius: 0 $border-radius-root $border-radius-root 0;
    background: rgba(map-deep-get($material-light, $module), 1);
  }
  // 轨道交界处
  &::-webkit-scrollbar-corner {
    background: transparent;
  }
  /*滚动条的滑块按钮*/
  .v-application.theme--dark &::-webkit-scrollbar-thumb {
    // background: $placeholder;
    background: rgba(191, 191, 191, 0.5);
    border-radius: 10px;
    border: 5px solid rgba(map-deep-get($material-dark, $module), 1);
    &:hover {
      background: rgba(191, 191, 191, 0.9);
      border-width: 2px;
    }
  }
  .v-application.theme--light &::-webkit-scrollbar-thumb {
    // background: $placeholder;
    background: rgba(191, 191, 191, 0.5);
    border-radius: 10px;
    border: 5px solid rgba(map-deep-get($material-light, $module), 1);
    &:hover {
      background: rgba(191, 191, 191, 0.9);
      border-width: 2px;
    }
  }
}

// ——— Class Generator using SCSS Map ——————— //
@mixin class-generator($map, $important: false) {
  @each $name, $styles in $map {
    #{$name} {
      @each $key, $value in $styles {
        #{$key}: $value if($important, !important, null);
      }
    }
  }
}

// ——— Light background generator ——————— //
//! With this you have to give text color to the component you want light bg
// e.g. class="avatar-initial primary--text" for primary light bg
@mixin light-bg-provider($component, $inner-selector: '', $opacity: 0.12) {
  .v-application .#{$component}.#{$component}-light-bg #{$inner-selector} {
    background-color: transparent !important;

    &.bg-static-white {
      background-color: white !important;
    }

    &::before {
      background-color: currentColor;
      border-radius: inherit;
      bottom: 0;
      content: '';
      left: 0;
      opacity: $opacity;
      position: absolute;
      pointer-events: none;
      right: 0;
      top: 0;
    }
  }
}

// ——— Generate Light/Dark style ——————— //
// ? Inspired from Vuetify's  `theme` mixin

@mixin theme-diff(
  $component,
  $lightThemePropertyValue,
  $darkThemePropertyValue
) {
  .theme--light.#{$component} {
    @content ($lightThemePropertyValue);
  }

  .theme--dark.#{$component} {
    @content ($darkThemePropertyValue);
  }
}

// Ability to create style based on theme colors
@mixin themeable() {
  @each $color, $value in $theme-colors {
    @content ($color, $value);
  }
}

// ————————————————————————————————————
//* ——— Avatar
// ————————————————————————————————————

@mixin avatar-border($material, $always-white: false) {
  @if ($always-white) {
    border: 2px solid #fff;
  } @else {
    border: 2px solid map-deep-get($material, 'cards');
  }
}
