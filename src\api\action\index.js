import { request, vsocPath } from '../../util/request'

// 获取处置列表
export const getAction = function (data) {
  return request({
    url: `${vsocPath}/dispose/disposes`,
    method: 'post',
    data,
  })
}

// 告警联动详情
export const disposeActionDetail = function (data) {
  return request({
    url: `${vsocPath}/dispose/disposeActionDetail`,
    method: 'post',
    data,
  })
}

// 新增处置
export const addAction = function (data) {
  return request({
    url: `${vsocPath}/dispose/addDisposeAction`,
    method: 'post',
    data,
  })
}

// 修改处置状态
export const updateAction = function (data) {
  return request({
    url: `${vsocPath}/dispose/updateDisposeAction`,
    method: 'post',
    data,
  })
}

// 删除处置详情
export const deleteAction = function (data) {
  return request({
    url: `${vsocPath}/dispose/delDisposeAction`,
    method: 'post',
    data,
  })
}

// 删除处置详情
export const updateActive = function (data) {
  return request({
    url: `${vsocPath}/dispose/updateActive`,
    method: 'post',
    data,
  })
}
