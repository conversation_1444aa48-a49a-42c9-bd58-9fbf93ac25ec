{"name": "SEC", "description": "SEC", "version": "2.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve --mode development", "serve:prod": "vue-cli-service serve --mode production", "build:prod": "vue-cli-service build --mode production", "build": "vue-cli-service build --mode development", "lint": "vue-cli-service lint", "install:custom": "yarn install --registry=https://registry.npmmirror.com/"}, "dependencies": {"@casl/ability": "^5.3.1", "@casl/vue": "1.x", "@jiaminghi/data-view": "^2.10.0", "@mdi/font": "^6.6.96", "@mdi/js": "^5.9.55", "@riophae/vue-treeselect": "^0.4.0", "@toast-ui/editor": "^3.2.2", "@vue/composition-api": "^1.7.2", "@vuetify/cli-plugin-utils": "^0.0.9", "@vueuse/core": "^4.8.1", "angular-expressions": "^1.1.8", "apexcharts-clevision": "^3.28.3", "axios": "^0.21.1", "axios-mock-adapter": "^1.19.0", "chart.js": "3.5.1", "chroma-js": "^2.4.2", "compression-webpack-plugin": "1.1.2", "core-js": "^3.6.5", "cron-expression-validator": "^1.0.20", "cronstrue": "^2.23.0", "date-fns": "^2.29.3", "docxtemplater": "^3.36.1", "docxtemplater-image-module-free": "^1.1.1", "echarts": "^5.3.2", "element-ui": "^2.15.7", "file-saver": "^2.0.5", "js-cookie": "^3.0.5", "json-bigint": "^1.0.0", "json-editor-vue": "^0.17.3", "jsonlint-mod": "^1.7.6", "jsonwebtoken": "^8.5.1", "jszip-utils": "^0.1.0", "lodash": "^4.17.21", "md5": "^2.3.0", "moment-timezone": "^0.5.43", "nprogress": "^0.2.0", "pizzip": "^3.1.4", "prismjs": "^1.23.0", "showdown": "^2.1.0", "stylus": "^0.59.0", "stylus-loader": "^7.1.0", "sweetalert2": "^11.6.8", "vue": "2.6.14", "vue-apexcharts": "^1.6.1", "vue-clipboard2": "^0.3.3", "vue-codemirror": "4", "vue-count-to": "^1.0.13", "vue-easy-pie-chart": "^1.0.3", "vue-easytable": "^2.27.1", "vue-flatpickr-component": "^8", "vue-grid-layout": "^2.4.0", "vue-i18n": "^8.24.5", "vue-prism-component": "1", "vue-quill-editor": "^3.0.6", "vue-router": "^3.5.2", "vue-seamless-scroll": "^1.1.23", "vue-sweetalert2": "^5.0.5", "vue-tabs-chrome": "^0.10.0", "vue2-perfect-scrollbar": "^1.5.0", "vuedraggable": "^2.24.3", "vuetify": "^2.6.7", "vuex": "^3.4.0", "xlsx": "^0.18.5", "xlsx-style": "^0.8.13"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-airbnb": "^5.0.2", "babel-eslint": "^10.1.0", "babel-plugin-component": "^1.1.1", "babel-plugin-transform-remove-console": "^6.9.4", "eslint": "^7.23.0", "eslint-plugin-import": "^2.23.4", "eslint-plugin-vue": "^6.2.2", "sass": "~1.32", "sass-loader": "^10.0.0", "vue-cli-plugin-vuetify": "~2.3.1", "vue-template-compiler": "2.6.14", "vuetify-loader": "^1.7.0"}, "__npminstall_done": false, "resolutions": {"commander": "^2.17.1", "@achrinza/node-ipc": "9.2.9"}}