<template>
  <div>
    <bread-crumb>
      <template v-slot:left>
        <div class="ml-4">
          <i class="iconfont icon-shuoming"></i>
          {{ $t('auditConfig.hint') }}
        </div>
      </template>
      <template>
        <!-- @click="$_setStatus" -->
        <v-btn
          color="primary"
          elevation="0"
          @click="onSubmit"
          :loading="isLoading"
        >
          {{ $t('action.submit') }}
        </v-btn>
      </template>
    </bread-crumb>
    <v-radio-group row v-model="keepYear" dense hide-details class="pa-4 pt-1">
      <span class="pr-8">{{ $t('auditConfig.retentionTime') }}</span>
      <v-radio
        class="pr-13"
        :label="$t('auditConfig.year1')"
        value="1"
      ></v-radio>
      <v-radio
        class="pr-13"
        :label="$t('auditConfig.year2')"
        value="2"
      ></v-radio>
      <v-radio
        class="pr-13"
        :label="$t('auditConfig.year3')"
        value="3"
      ></v-radio>
    </v-radio-group>
    <v-card v-resize="$_setTableHeight" tile class="main-content">
      <!-- <v-card-title> 审计配置 </v-card-title>
    <v-divider></v-divider> -->
      <v-card-text class="text--primary font-weight-medium pa-0"> </v-card-text>

      <v-data-table
        class="table v-data-table--select color-base thead-light"
        :headers="tableColumn"
        :items="notifications"
        :items-per-page="notifications.length"
        hide-default-footer
        :height="tableHeight"
        show-select
        :loading="tableLoading"
        checkbox-color="primary"
        @item-selected="$_tableSelected"
        @toggle-select-all="$_tableSelected"
      >
        <template v-slot:item.btnType="{ item }">
          <v-row align="center" justify="left">
            <template v-for="btn in item.feature">
              <span>{{ btn.featureName }}</span>
              <v-checkbox v-model="btn.checked"></v-checkbox>
            </template>
            <!-- <span>修改</span>
            <v-checkbox
              v-model="item.email"
              hide-details
              dense
              class="shrink"
            ></v-checkbox>
            <span>删除</span>
            <v-checkbox
              v-model="item.email"
              hide-details
              dense
              class="shrink"
            ></v-checkbox>

            <span>新增</span>
            <v-checkbox
              v-model="item.email"
              hide-details
              dense
              class="shrink"
            ></v-checkbox>
            <span>修改</span>
            <v-checkbox
              v-model="item.email"
              hide-details
              dense
              class="shrink"
            ></v-checkbox>
            <span>删除</span>
            <v-checkbox
              v-model="item.email"
              hide-details
              dense
              class="shrink"
            ></v-checkbox> -->
          </v-row>
        </template>
      </v-data-table>

      <v-divider></v-divider>
    </v-card>
  </div>
</template>

<script>
import { editOperation, getOperation } from '@/api/log/audit'
import breadCrumb from '@/components/bread-crumb/index'
import { setRemainingHeight } from '@/util/utils'
import { cloneDeep } from 'lodash'
export default {
  name: 'AuditConfig',
  components: { breadCrumb },
  data() {
    return {
      selectedList: [],
      isLoading: false,
      tableLoading: false,
      tableHeight: 0,
      keepYear: '1',

      notifications: [],
    }
  },
  computed: {
    tableColumn() {
      return [
        {
          text: this.$t('operateLog.headers.module'),
          value: 'menu',
          sortable: false,
        },
        {
          text: this.$t('operateLog.headers.type'),
          value: 'btnType',
          sortable: false,
          align: 'left',
        },
      ]
    },
  },
  watch: {
    '$i18n.locale': {
      handler() {
        this.loadData()
      },
      deep: true,
    },
  },

  mounted() {
    this.loadData()
  },
  methods: {
    $_tableSelected({ item, value, items }) {
      if (items) {
        this.selectedList = value ? cloneDeep(items) : []
      } else if (value) {
        this.selectedList.push(item)
      } else {
        const index = this.selectedList.findIndex(v => v.id === item.id)
        this.selectedList.splice(index, 1)
      }
      if (!value) {
        // 一键反选
        let list = items || [item]
        this.notifications
          .filter(v => list.findIndex(l => l.id === v.id) !== -1)
          .forEach(v => {
            v.feature.forEach(y => {
              y.checked = false
            })
          })
      }
      if (this.selectedList && this.selectedList.length > 0) {
        this.notifications.forEach(v => {
          if (this.selectedList.findIndex(s => v.id === s.id) !== -1) {
            v.feature.forEach(y => {
              y.checked = true
            })
          }
        })
        this.$forceUpdate()
      }
    },
    async onSubmit() {
      try {
        this.isLoading = true

        let params = []
        this.notifications.forEach(item => {
          params.push({
            retentionTime: this.keepYear,
            operation: item.feature
              .filter(v => v.checked)
              ?.map(m => m.featureNum),
            id: item.id,
          })
        })
        await editOperation(params)
        this.$notify.info('success', this.$t('auditConfig.success'))
        this.loadData()
      } catch (err) {
        console.log('审计配置报错', err)
      } finally {
        this.isLoading = false
      }
    },
    // 设置表格高度
    $_setTableHeight() {
      this.$nextTick(() => {
        let fn = () => {
          return +42
        }
        this.tableHeight = setRemainingHeight(fn)
      })
    },
    async loadData() {
      try {
        this.tableLoading = true
        const { data } = await getOperation()
        this.notifications = data.map(item => {
          item.feature.forEach(v => {
            v.checked = v.status === '1'
          })
          return item
        })
      } catch (err) {
        console.log('审计配置报错', err)
      } finally {
        this.tableLoading = false
      }
    },
    addOperation() {
      this.operations.push({ action: [] })
    },
    removeOperation(index) {
      this.operations.splice(index, 1)
    },
  },
}
</script>
