<template>
  <!-- 定制化图形1 -->
  <div class="box">
    <div class="box-header">{{ title }}</div>
    <div
      class="h-100 tab-content d-flex flex-column justify-space-between"
      style="padding: 4% 0"
    >
      <div class="c-center2-num">
        <count-to
          ref="count1"
          style="filter: blur(0.3px)"
          :startVal="0"
          :endVal="list.length ? list[0].onlineCount : 0"
          :duration="2500"
        ></count-to>
        <!-- <span class="c-center2-unit">{{ $t('screen.cloud.times') }}</span> -->
      </div>
      <v-progress-linear
        rounded
        :height="$getCeilSize(8)"
        color="#32FDB8"
        :value="50"
      >
      </v-progress-linear>
    </div>
  </div>
</template>

<script>
import countTo from 'vue-count-to'
export default {
  name: 'CardItem1',
  props: {
    title: {
      type: String,
      default: '',
    },
    list: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  components: {
    countTo,
  },
  watch: {
    list: {
      handler(val) {
        if (val.length === 0) return
        this.$nextTick(() => {
          this.$refs.count1 && this.$refs.count1.start()
        })
      },
      immediate: true,
    },
  },
}
</script>
