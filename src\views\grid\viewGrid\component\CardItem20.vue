<template>
  <!-- 环形图2（带线）资产态势 -->
  <div class="box">
    <div class="box-header">{{ title }}</div>
    <template v-if="list.length !== 0">
      <vsoc-chart
        class="box-chart d-flex align-center"
        :echartId="echartId"
        :option="chartOption"
        @highlight="onHighlight"
      ></vsoc-chart>
    </template>
    <template v-else>
      <div
        class="box-chart d-flex align-center color--primary justify-center fs-16"
      >
        None
      </div>
    </template>
  </div>
</template>
<script>
import VsocChart from '@/components/VsocChart.vue'
import { num, numberToFormat } from '@/util/filters'

import { riskColor } from '../util/defaultPreview'
import { cloudPieSeriesFn, cloudTooltip, getRoundSize, primary } from './chart'

export default {
  name: 'CardItem20',
  components: {
    VsocChart,
  },
  props: {
    title: {
      type: String,
      default: '',
    },
    isFull: {
      type: Boolean,
      default: false,
    },
    total: {
      type: [Number, String],
      default: () => {
        return 0
      },
    },
    echartId: {
      type: String,
      default: '',
    },
    list: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  data() {
    return {}
  },
  computed: {
    chartOption() {
      let _this = this
      const formatter = () => {
        const totalObj = numberToFormat(this.total, 'Object')
        return `\n{value|${totalObj.num}}{unit|${totalObj.unit}}`
      }
      return {
        color: riskColor,
        tooltip: {
          ...cloudTooltip,
          formatter(params) {
            return `${params.name}：${num(params.value)} (${(
              (params.value / _this.total) *
              100
            ).toFixed(2)}%)`
          },
        },
        legend: {
          type: 'scroll',
          orient: 'vertical',
          right: 0,
          top: 'center',
          icon: 'circle',
          itemGap: getRoundSize(15), // 图标间距
          itemHeight: getRoundSize(10),
          textStyle: {
            fontSize: getRoundSize(12),
            // padding: [0, 0, 0, 0],
            color: '#fff',
            opacity: 0.6,
            // width: getRoundSize(100),
            overflow: 'truncate',
          },
          formatter: name => {
            let listItem = _this.list.find(item => item.name === name)
            if (!listItem) {
              return name
            } else {
              let percent = (listItem.value / _this.total) * 100
              return `${name}  ${numberToFormat(
                listItem.value,
              )} (${percent.toFixed(2)}%) `
            }
          },
        },
        series: [
          {
            ...cloudPieSeriesFn(this.list, formatter)[0],
            top: 0,
            bottom: 0,
            left: -getRoundSize(240),
            radius: ['46%', '57%'],
            label: {
              show: true,
              position: 'center',
              // lineHeight: getRoundSize(0),
              formatter: formatter,
              rich: {
                name: {
                  fontSize: getRoundSize(12),
                  color: '#fff',
                  opacity: 0.6,
                  lineHeight: getRoundSize(14),
                },
                value: {
                  lineHeight: getRoundSize(28),
                  fontSize: getRoundSize(24),
                  fontWeight: 600,
                  padding: [0, 0, getRoundSize(10), 0],
                  // verticalAlign: 'center',
                  color: primary,
                },
                unit: {
                  fontSize: getRoundSize(14),
                  fontWeight: 500,
                  padding: [0, 0, getRoundSize(4), 0],
                  // padding: [getRoundSize(10), getRoundSize(5)],
                  // verticalAlign: 'bottom',
                  color: primary,
                  opacity: 0.6,
                },
              },
            },
          },
        ],
      }
    },
  },
  created() {},
  methods: {
    onHighlight(obj, myChart) {
      const option = this.chartOption
      option.tooltip.backgroundColor = obj.color
      myChart.setOption(option)
    },
  },
}
</script>
