const response = {
  currentTitle: '响应',
  headers: {
    id: '排序',
    responseName: '响应名称',
    responseEventName: '响应事件',
    statusName: '当前状态',
    // updateUser: '更新者',
    // updateDate: '更新时间',
    // createUser: '创建者',
    // createDate: '创建时间',
    responseEventName1: '响应事件',
    responseCondition: '响应条件',
    Operator: '执行操作',
  },
  infoHeaders: {
    type: '类型',
    value: '动态参数',
    name: '参数名称',
    send: '发送附件',
  },
  btn: {
    add: '新增响应',
    edit: '编辑响应',
    del: '删除响应',
    detail: '响应详情',
    add1: '新增条件',
    add2: '新增操作',
  },
  swal: {
    tip1: '响应条件',
    tip2: '操作符',
    tip3: '值',
    tip4: '查看所有替换标签',
    tip5: '下面是所有的替换标签列表和说明，你可以将它们应用在通知信息中，提交后系统会生成对应的内容。',
    parameter: '请输入您要查看的参数',
    notice1: '通知对象',
    notice2: '通知主题',
    stop: '停用响应',
    start: '启用响应',
    sure: '确定要{0}【{1}】吗？',
    tip: '{0}成功!',
    delTip: '已启用的响应任务不允许删除!',
    text: '是否确认删除该响应：{0}？',
    delTip1: '该规则不完整，请填写或删除',
    delTip2: '执行规则不完整，请填写或删除',

    delTip3: '至少包括一条完整的规则',
    delTip4: '至少包括一条完整的执行操作',
  },
}

export default response
