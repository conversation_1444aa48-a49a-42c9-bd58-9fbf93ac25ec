<template>
  <div>
    <bread-crumb></bread-crumb>
    <v-card class="main-content" tile>
      <v-card-text class="pa-0">
        <div class="d-flex justify-space-between align-center mb-3">
          <div class="d-flex align-end">
            <v-btn
              v-has:platform-new
              color="primary"
              elevation="0"
              class="me-1"
              @click="add"
            >
              {{ $generateBtnTitle('platform-new') }}
            </v-btn>
          </div>
          <div class="d-flex align-end">
            <table-search
              :searchList="searchList"
              :searchQuery="query"
              @search="$_search"
            ></table-search>
          </div>
        </div>
        <v-data-table
          fixed-header
          :items-per-page="query.pageSize"
          item-key="id"
          :height="tableHeight"
          hide-default-footer
          :headers="headers"
          :items="tableData"
          class="thead-light"
          :loading="tableLoading"
        >
          <template v-slot:item.name="{ item }">
            <div v-show-tips style="max-width: 10rem">
              {{ item.name }}
            </div>
          </template>
          <template v-slot:item.description="{ item }">
            <div v-show-tips style="max-width: 10rem">
              {{ item.description | dataFilter }}
            </div>
          </template>
          <template v-slot:item.actions="{ item }">
            <v-btn
              v-has:platform-detail
              icon
              @click="onInfo(item)"
              v-show-tips="$generateBtnTitle('platform-detail')"
            >
              <vsoc-icon
                type="fill"
                icon="icon-xiangqing2"
                class="action-btn"
                size="x-large"
              >
              </vsoc-icon>
            </v-btn>
            <v-btn
              v-has:platform-edit
              icon
              @click="onEdit(item)"
              v-show-tips="$generateBtnTitle('platform-edit')"
            >
              <vsoc-icon
                type="fill"
                icon="icon-bianji"
                class="action-btn"
                size="x-large"
              >
              </vsoc-icon>
            </v-btn>
            <v-btn
              v-has:platform-del
              icon
              v-show-tips="$generateBtnTitle('platform-del')"
              @click="onDel(item)"
            >
              <vsoc-icon
                type="fill"
                class="action-btn"
                icon="icon-shanchu"
                size="x-large"
              ></vsoc-icon>
            </v-btn>
          </template>
        </v-data-table>

        <vsoc-pagination
          :page.sync="query.pageNum"
          :size.sync="query.pageSize"
          :total="tableDataTotal"
          @change-page="$_getTableData"
          @change-size="$_search"
        >
        </vsoc-pagination>
      </v-card-text>
    </v-card>
    <platform-edit
      ref="platformEdit"
      :item="editForm"
      :mode="ops"
      @refresh="$_getTableData"
    ></platform-edit>
  </div>
</template>

<script>
import {
  delVehiclePlatform,
  getVehiclePlatformList,
} from '@/api/asset/platform'
import TableSearch from '@/components/TableSearch/index.vue'
import VsocDrawer from '@/components/VsocDrawer.vue'
import VsocPagination from '@/components/VsocPagination.vue'
import breadCrumb from '@/components/bread-crumb/index'
import { PAGESIZE } from '@/util/constant'
import { setRemainingHeight } from '@/util/utils'
import PlatformEdit from './PlatformEdit.vue'

export default {
  name: 'PlatformIndex',
  components: {
    VsocPagination,
    VsocDrawer,
    PlatformEdit,
    breadCrumb,
    TableSearch,
  },
  data() {
    return {
      ops: 'new',
      editForm: {},
      // 查询条件列表
      // 查询条件下拉选择
      query: {
        pageNum: 1,
        pageSize: PAGESIZE,
        name: '',
      },

      tableData: [],
      tableHeight: '34.5rem',
      tableLoading: false,
      btnLoading: false,
      tableDataTotal: 0,
    }
  },
  computed: {
    searchList() {
      return [
        {
          type: 'input',
          value: 'name',
          text: this.$t('model.headers.name'),
        },
      ]
    },
    headers() {
      return [
        { text: this.$t('model.headers.name'), value: 'name' },
        { text: this.$t('model.headers.code'), value: 'modelCode' },
        { text: this.$t('model.headers.desc'), value: 'description' },
        { text: this.$t('global.createDate'), value: 'createDate' },
        { text: this.$t('global.updateDate'), value: 'updateDate' },
        { text: this.$t('global.createUser'), value: 'createUser' },
        { text: this.$t('global.updateUser'), value: 'updateUser' },

        {
          text: '',
          value: 'actions',
          sortable: false,
          width: '110px',
        },
      ]
    },
    assetTypeEnum() {
      return this.$store.state.enums.enums.AssetType
    },
  },
  created() {
    this.$_setTableHeight()
    this.$bus.$on('resize', this.$_setTableHeight)
    this.$_getTableData()
  },
  beforeDestroy() {
    this.$bus.$off('resize', this.$_setTableHeight)
  },
  methods: {
    onClear() {
      this.query.name = ''
      this.$_search()
    },
    onDel(record) {
      this.$swal({
        title: this.$t('model.swal.title'),
        text: this.$t('model.swal.hint', { name: record.name }),
        icon: 'warning',
        reverseButtons: true,
        showCancelButton: true,
        confirmButtonText: this.$t('action.confirm'),
        cancelButtonText: this.$t('action.cancel'),
        customClass: {
          confirmButton: 'sweet-btn-primary',
        },
      }).then(async result => {
        if (result.isConfirmed) {
          const params = {
            id: record.id,
          }
          await delVehiclePlatform(params)
          this.$notify.info(
            'success',
            this.$t('global.hint.del', [this.$t('model.currentTitle')]),
          )
          this.$_getTableData()
        }
      })
    },
    add() {
      this.$router.push('/model/add')
      // this.ops = 'new'
      // this.$refs.platformEdit.isDrawerShow = true
    },
    onEdit(record) {
      this.$router.push(`/model/edit?id=${record.id}`)
      // this.ops = 'edit'
      // this.editForm = { ...record }
      // this.$refs.platformEdit.isDrawerShow = true
    },
    onInfo(record) {
      this.$router.push(`/model/detail?id=${record.id}&isDetail=1`)
    },
    async $_getTableData() {
      try {
        this.tableLoading = true
        this.tableData = []
        const res = await getVehiclePlatformList(this.query)
        this.tableData = res.data.records
        this.tableDataTotal = res.data.total

        // const res = await searchVehicleGroupDt({
        //   ...this.query,
        //   page: this.query.pageNum - 1,
        // })
        // this.tableData = res.content
        // this.tableDataTotal = res.totalElements
      } catch (e) {
        console.error(`获取车辆资产组管理：${e}`)
      }
      this.tableLoading = false
    },

    $_search() {
      this.query.pageNum = 1
      this.$_getTableData()
    },

    doQuery() {
      setTimeout(() => {
        this.query.loading = true
      }, 2000)
      this.query.loading = false
    },

    $_setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = setRemainingHeight(
          this.$store.state.global.subMarginFn,
        )
      })
    },
  },
}
</script>
