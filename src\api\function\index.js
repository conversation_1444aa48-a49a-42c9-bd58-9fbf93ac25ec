import { cmdbPath, request } from '../../util/request'

// 获取函数列表
export const getFunctionList = function (params) {
  return request({
    url: `${cmdbPath}/vehicleFunction`,
    method: 'get',
    params,
  })
}

// 新增函数
export const addFunction = function (data) {
  return request({
    url: `${cmdbPath}/vehicleFunction`,
    method: 'post',
    data,
  })
}

// 更新函数
export const updatetFunction = function (data) {
  return request({
    url: `${cmdbPath}/vehicleFunction/${data.id}`,
    method: 'put',
    data,
  })
}

// 删除函数
export const deleteFunction = function (id) {
  return request({
    url: `${cmdbPath}/vehicleFunction/${id}`,
    method: 'delete',
  })
}
