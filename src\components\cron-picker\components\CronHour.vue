<template>
  <div class="d-flex align-center">
    <div>{{ $t('infer.expression.every') }}</div>
    <div style="width: 100px">
      <v-select
        v-model="perHour"
        class="mx-2"
        color="primary"
        dense
        outlined
        solo
        :items="hourOptions2"
        hide-details
        @change="emitChange()"
        :menu-props="{ offsetY: true }"
      >
      </v-select>
    </div>
    <!-- <el-select
      v-model="perHour"
      class="mx-2"
      filterable
      style="width: 6rem"
      @change="emitChange()"
    >
      <el-option
        v-for="item in hourOptions2"
        :key="item"
        :label="item"
        :value="item"
      />
    </el-select> -->
    <div>{{ $t('infer.expression.hour') }}</div>
    <div style="width: 100px">
      <v-select
        v-model="minute"
        class="mx-2"
        color="primary"
        dense
        outlined
        disabled
        solo
        :items="minuteOptions"
        hide-details
        item-text="label"
        item-value="value"
        @change="emitChange()"
        :menu-props="{ offsetY: true }"
      >
      </v-select>
    </div>
    <!-- <el-select
      v-model="minute"
      class="mx-2"
      filterable
      style="width: 6rem"
      @change="emitChange()"
    >
      <el-option
        v-for="item in minuteOptions"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select> -->
    <div>{{ $t('infer.expression.minute') }}</div>
  </div>
</template>

<script>
export default {
  name: 'CronHour',
  data() {
    return {
      perHour: 6,
      minute: 0,
    }
  },
  computed: {
    hourOptions2() {
      return Array.from(Array(12), (_, i) => i + 1)
    },
    minuteOptions() {
      return Array.from(Array(60), (_, i) => {
        if (i < 10) {
          return {
            value: i,
            label: `0${i}`,
          }
        }

        return {
          value: i,
          label: i,
        }
      })
    },
    cronExp() {
      return `0 ${this.minute} 0/${this.perHour} * * ?`
    },
  },
  methods: {
    init(value) {
      const tempArr = value.split(' ')
      this.minute = Number(tempArr[1])
      const hourArr = tempArr[2].split('/')
      this.perHour = Number(hourArr[1])
    },
    emitChange() {
      this.$emit('change', this.cronExp)
    },
  },
}
</script>
