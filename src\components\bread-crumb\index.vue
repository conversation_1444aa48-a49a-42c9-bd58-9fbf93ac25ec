<template>
  <div>
    <v-card
      elevation="0"
      tile
      class="bar-box border-bottom"
      :class="{ 'mb-3': !showAdvance }"
      v-resize="setListMaxHeight"
    >
      <div v-if="propsBreadList.length === 0" class="d-flex align-center">
        <template v-if="!$slots.center">
          <v-btn
            v-if="breadlist.length > 1"
            icon
            small
            @click="goBack()"
            class="mr-6"
          >
            <v-icon class="text--primary" size="16px"> mdi-arrow-left </v-icon>
          </v-btn>

          <span class="text-title color-base font-weight-medium">
            <slot name="title">
              {{ title || $generateMenuTitle($route.meta) }}
            </slot>
          </span>

          <slot name="left"></slot>
        </template>
      </div>
      <div v-else class="d-flex align-center">
        <v-btn icon small @click="goBack()" class="mr-6">
          <v-icon class="text--primary" size="16px"> mdi-arrow-left </v-icon>
        </v-btn>
        <v-breadcrumbs
          class="pa-0 font-weight-medium"
          :items="propsBreadList"
        ></v-breadcrumbs>
      </div>
      <slot name="center"></slot>
      <!-- <div>
      <v-breadcrumbs class="px-0 py-0" :items="breadlist">
        <template v-slot:divider>
          <v-icon>mdi-chevron-right</v-icon>
        </template>
        <template v-slot:item="{ item }">
          <v-breadcrumbs-item :to="item.href" :disabled="item.disabled">
            {{ item.text }}
          </v-breadcrumbs-item>
        </template>
      </v-breadcrumbs>
    </div> -->
      <div class="bar-box-right d-flex">
        <v-menu
          offset-y
          :close-on-content-click="false"
          :close-on-click="false"
          :retain-focus="false"
          v-if="showHeaderEdit"
        >
          <template v-slot:activator="{ on, attrs }">
            <v-btn
              small
              v-bind="attrs"
              v-on="on"
              @click="onChange"
              elevation="0"
              class="primary--text bg-btn mt-1"
            >
              {{
                isHeaderShow
                  ? $t('action.confirmUpdate')
                  : $t('action.setHeader')
              }}
            </v-btn>
          </template>
          <v-list class="overflow-auto" :max-height="listMaxHeight">
            <v-list-item>
              <v-switch
                flat
                v-model="allIn"
                :label="$t('action.selectAll')"
                @change="onSelectAll"
              ></v-switch>
            </v-list-item>
            <draggable
              :list="newHeaderList"
              :handle="'.mover'"
              @end="onDragEnd"
            >
              <transition-group>
                <v-list-item
                  v-for="item in newHeaderList"
                  :key="item.value"
                  class="mover"
                >
                  <v-list-item-action>
                    <v-checkbox
                      :disabled="item.disabled"
                      v-model="item.checked"
                      color="primary"
                    ></v-checkbox>
                  </v-list-item-action>
                  <v-list-item-title>{{ $t(item.text) }}</v-list-item-title>
                </v-list-item>
              </transition-group>
            </draggable>
          </v-list>
        </v-menu>
        <slot></slot>
        <slot v-if="showAdvance">
          <div
            class="d-flex align-center justify-end cursor-pointer tip-color"
            @click="showDoQuery"
          >
            <vsoc-icon type="fill" icon="icon-gaojichaxun" size="x-large">
            </vsoc-icon>
            <span class="ml-2">{{ $t('action.advanced') }}</span>
          </div>
        </slot>
      </div>
    </v-card>
    <vsoc-filter
      ref="filterList"
      v-bind="$attrs"
      v-on="$listeners"
      v-if="showAdvance"
      :clearType="clearType"
      :filterList="filterList"
    ></vsoc-filter>
  </div>
</template>
<script>
import VsocFilter from '@/components/VsocFilter.vue'
import { getLocalStorage, setLocalStorage } from '@/util/localStorage'
import draggable from 'vuedraggable'
export default {
  name: 'BreadCrumb',
  props: {
    clearType: {
      type: String,
      default: '',
    },
    showAdvance: {
      type: Boolean,
      default: () => false,
    },
    filterList: {
      type: Array,
      default: () => [],
    },
    showDoQuery: {
      type: Function,
    },
    showHeaderEdit: {
      type: Boolean,
      default: false,
    },
    headerList: {
      type: Array,
      default: () => [],
    },
    popsName: {
      type: String,
      default: '',
    },
    propsBreadList: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    title() {
      if (this.popsName === 'new') {
        return this.$t('global.drawer.addTitle', {
          cur: this.$generateMenuTitle(this.$route.meta),
        })
      } else if (this.popsName === 'edit') {
        return this.$t('global.drawer.editTitle', {
          cur: this.$generateMenuTitle(this.$route.meta),
        })
      } else {
        return this.popsName
      }
    },
    filterHeight() {
      return this.$refs.filterList.offsetHeight
    },
    allIn: {
      get() {
        const len = this.newHeaderList.filter(item => item.checked).length
        return len === this.newHeaderList.length
      },
      set(newVal) {
        return newVal
      },
    },
    newHeaderList() {
      const jsonList = getLocalStorage(this.$route.path + '_headerList')
      const list = JSON.parse(jsonList)
      if (list && list.length > 0) {
        this.$emit('refresh', list)
        this.actionItem = list.find(v => v.value === 'actions')
        return list.filter(v => v.value !== 'actions')
      }
      this.actionItem = this.headerList.find(v => v.value === 'actions')
      return this.headerList
        .filter(v => v.value !== 'actions')
        .map(t => {
          return {
            checked: t.checked || true,
            ...t,
          }
        })
    },
  },
  watch: {
    $route() {
      this.getBreadcrumb()
    },
  },
  components: {
    draggable,
    VsocFilter,
  },
  data() {
    return {
      breadHeight: 54,
      isHeaderShow: false,
      actionItem: undefined,
      listMaxHeight: 888,
      breadlist: [],
      showFixed: false,
    }
  },
  created() {
    this.getBreadcrumb()
  },

  methods: {
    goBack() {
      if (this.$parent.$parent && this.$parent.$parent.goToBack) {
        this.$parent.$parent.goToBack()
      } else {
        this.$router.go(-1)
      }
    },
    setListMaxHeight() {
      const screenHeight =
        window.innerHeight ||
        document.documentElement.clientHeight ||
        document.body.clientHeight
      this.listMaxHeight = screenHeight - 48 - 54
    },
    onSelectAll(bool) {
      if (bool) {
        this.newHeaderList.forEach(item => {
          item.checked = true
        })
      } else {
        this.newHeaderList.forEach(item => {
          item.checked = item?.disabled || false
        })
      }
    },
    onDragEnd(e) {
      this.$forceUpdate()
    },
    onChange() {
      this.isHeaderShow = !this.isHeaderShow
      if (this.isHeaderShow) return
      const filterList = this.newHeaderList.map(item => {
        if (!item.checked) {
          item.class = 'display-none'
          item.cellClass = 'display-none'
        } else {
          delete item.class
          delete item.cellClass
        }
        return item
      })
      const newList = this.actionItem
        ? [...filterList, this.actionItem]
        : filterList
      setLocalStorage(this.$route.path + '_headerList', JSON.stringify(newList))
      // localStorage.setItem(
      //   this.$route.path + '_headerList',
      //   JSON.stringify(newList),
      // )
      this.$emit('refresh', newList)
    },
    onSelect() {
      this.isHeaderShow = true
    },
    showBread() {
      if (document.documentElement.scrollTop + document.body.scrollTop > 90) {
        this.showFixed = true
      } else {
        this.showFixed = false
      }
    },
    getBreadcrumb() {
      let breadList = []
      let allRoutes = this.$store.state.permission.routes
      this.breadlist = this.dealRoute(allRoutes, this.$route.meta.id, breadList)
      this.breadlist[this.breadlist.length - 1].disabled = true
      if (!this.$route.meta.navActiveLink) {
        this.breadlist = [this.$route]
      }
    },
    dealRoute(list, id, breadList) {
      if (list && list.length) {
        list.forEach(v => {
          if (v.meta && v.meta.id === id) {
            v.text = v.name
            v.href = v.path
            v.disabled = false
            breadList.unshift(v)
            if (v.meta.parentId !== '0') {
              this.dealRoute(list, v.meta.parentId, breadList)
            }
          }
        })
      }
      return breadList
    },
  },
}
</script>

<style lang="scss" scoped>
.bar-box {
  height: 54px;
  // background: #fff;
  // background: inherit;
  padding: 16px !important;
  // margin-bottom: 12px !important;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  top: 0;
  left: 0;
  z-index: 5;
  width: 100%;
  .bar-title {
    font-size: 16px;
    // color: #1f2533;
  }
  ::v-deep .v-breadcrumbs__item {
    font-size: 16px !important;
    line-height: 24px !important;
  }
  // box-shadow: 0px -1px 0px 0px $color-dividers--light inset;
  // border-bottom: 1px solid $color-dividers--light;
}
// .bar-box-showFixed {
//   position: fixed;
//   z-index: 5;
//   top: 48px;
//   left: 161px;
//   overflow: hidden;
//   width: calc(100% - 161px);
//   box-shadow: 0px 1px 8px 0px rgba(0, 0, 0, 0.1);
// }
// ::v-deep .v-breadcrumbs .v-breadcrumbs__item--disabled {
//   font-size: 14px !important;
//   color: #1f2533 !important;
// }
</style>
