<template>
  <div class="message-box">
    <bread-crumb></bread-crumb>
    <v-card tile class="main-content" elevation="0">
      <v-card-text class="pa-0">
        <div class="d-flex align-center justify-space-between">
          <div class="d-flex justify-end align-center">
            <v-btn
              elevation="0"
              color="primary"
              @click="addMessage"
              v-has:message-new
            >
              <span>
                {{ $t('notices.btn.publish') }}
              </span>
            </v-btn>
          </div>

          <div class="d-flex align-center">
            <table-search
              :searchList="searchList"
              :searchQuery="query"
              @search="$_search"
            ></table-search>
            <!-- <div class="d-flex align-end">
              <v-select
                outlined
                dense
                v-model="queryKey"
                small
                color="primary"
                :menu-props="{ offsetY: true }"
                append-icon="mdi-chevron-down"
                class="me-3 select-width"
                hide-details="auto"
                :items="searchConditions"
                :label="$t('action.queryConditions')"
                @change="changeCondition"
              ></v-select>
              <v-text-field
                v-if="queryKey === 'title'"
                v-model="query[queryKey]"
                color="primary"
                hide-details="auto"
                :label="$t('notices.headers.title')"
                dense
                outlined
                class="me-3 text-width"
                clearable
                @click:clear="onClear"
                @keyup.enter.native="$_search"
              ></v-text-field>
              <v-select
                v-else
                v-model="query[queryKey]"
                :items="MessageSendStatus"
                dense
                :label="$t('notices.headers.sendStatus')"
                hide-details="auto"
                class="me-3 text-width"
                clearable
                outlined
                :menu-props="{ offsetY: true }"
                @change="$_search"
              >
              </v-select>
            </div>

            <vsoc-date-range
              v-model="dateRange.range"
              no-title
              :menu-props="dateRange.menuProps"
              @input="onChangeDate"
              @search="$_search"
            >
              <template v-slot:text="{ on, attrs }">
                <v-text-field
                  type="button"
                  clearable
                  class="append-icon-max me-3 date-width"
                  outlined
                  dense
                  readonly
                  hide-details="auto"
                  color="primary"
                  large
                  prepend-inner-icon="mdi-calendar-range-outline"
                  :label="$t('notices.headers.sendDate')"
                  :value="RANGE_STR(query.startDate, query.endDate)"
                  @click:clear="onChangeDate({ start: '', end: '' })"
                ></v-text-field>
              </template>
            </vsoc-date-range>

            <div>
              <v-btn
                class="primary--text bg-btn"
                elevation="0"
                @click="$_search"
              >
                <span>
                  {{ $t('action.search') }}
                </span>
              </v-btn>
            </div> -->
          </div>
        </div>

        <v-data-table
          ref="table"
          fixed-header
          show-expand
          :items-per-page="query.pageSize"
          item-key="id"
          :height="tableHeight"
          hide-default-footer
          :headers="headers"
          :items="tableData"
          class="table border-radius-xl mt-3 thead-light"
          :loading="tableLoading"
          @click:row="$_clickRow"
        >
          <template v-slot:item.title="{ item }">
            <div v-show-tips style="width: 140px">
              {{ item.title }}
            </div>
          </template>
          <template v-slot:item.sendStatus="{ item }">
            <v-icon
              v-if="item.sendStatus === '0'"
              size="1.1667rem"
              :color="$activeColor"
            >
              mdi-checkbox-marked-circle
            </v-icon>
            <v-icon v-else size="1.1667rem" color="#f5a639">
              mdi-calendar-range-outline
            </v-icon>
            <span class="ml-2">{{
              MessageSendStatusMap[item.sendStatus].text
            }}</span>
          </template>
          <template v-slot:item.receiverType="{ item }">
            <div
              v-if="item.receiverType === '1'"
              v-show-tips
              style="width: 160px"
            >
              {{ item.receiverList }}
            </div>
            <div v-else-if="item.receiverType === '0'" style="width: 160px">
              {{ recipientTypeMap[item.receiverType].text }}
            </div>
          </template>
          <template v-slot:item.readingPercentage="{ item }">
            <div style="width: 120px">
              {{
                Number(item.readingPercentage) > 0
                  ? parseInt(Number(item.readingPercentage) * 100) + '%'
                  : 0
              }}
            </div>
          </template>
          <template v-slot:item.createDate="{ item }">
            <span v-show-tips>{{ item.createDate | toDate }}</span>
          </template>
          <template v-slot:item.actions="{ item }">
            <v-btn v-has:message-situation icon @click.stop="checkInfo(item)">
              <vsoc-icon
                v-show-tips="$t('notices.readInfo.title')"
                type="fill"
                icon="icon-xiangqing"
                class="action-btn"
                size="x-large"
              ></vsoc-icon>
            </v-btn>
          </template>
          <template v-slot:expanded-item="{ headers, item }">
            <td :colspan="headers.length + 1" class="py-6">
              <div class="text-sm pl-14">
                <div class="pb-2 text--primary font-weight-bold">
                  {{ $t('notices.headers.content') }}
                </div>
                <div>
                  <span>{{ item.content }}</span>
                  <v-btn
                    v-copy="item.content"
                    v-show-tips="$t('action.copy')"
                    icon
                    class="copy-box"
                  >
                    <vsoc-icon
                      size="1.2rem"
                      type="fill"
                      icon="icon-fuzhi"
                    ></vsoc-icon>
                    <!-- <v-icon size="1rem"> mdi-content-copy </v-icon> -->
                  </v-btn>
                </div>
              </div>
            </td>
          </template>
        </v-data-table>

        <!-- 分页器 -->
        <vsoc-pagination
          :page.sync="query.pageNum"
          :size.sync="query.pageSize"
          :total="tableDataTotal"
          @change-page="changePage"
          @change-size="changeSize"
        >
        </vsoc-pagination>
      </v-card-text>
      <div v-if="sendMessageVisible" class="drawer-mask mask"></div>
      <vsoc-drawer
        v-model="sendMessageVisible"
        :title="$t('notices.btn.publish')"
        @click:confirm="confirmSendMessage"
        :temporary="false"
      >
        <v-form ref="form" v-model="valid" lazy-validation>
          <v-text-field
            v-model="sendModel.title"
            color="primary"
            :rules="sendRule.title"
            :label="$t('notices.headers.title')"
          ></v-text-field>

          <v-textarea
            v-model="sendModel.content"
            color="primary"
            :rules="sendRule.content"
            :label="$t('notices.headers.content')"
            :rows="$AREA_ROWS"
          ></v-textarea>

          <v-select
            v-model="sendModel.receiverType"
            color="primary"
            :menu-props="{ offsetY: true }"
            append-icon="mdi-chevron-down"
            :items="receiverTypeList"
            :label="$t('notices.sendTitle.object')"
            @change="changeReceiverType"
          >
          </v-select>

          <template v-if="sendModel.receiverType === '1'">
            <v-autocomplete
              v-model="sendModel.receivers"
              multiple
              :menu-props="{ offsetY: true, maxHeight: 300 }"
              append-icon="mdi-chevron-down"
              chips
              :items="userNameAll"
              item-text="userName"
              item-value="userId"
              :label="$t('notices.sendTitle.user')"
              :rules="sendRule.receivers"
              class="mt-0"
            >
              <template v-slot:selection="{ item, index }">
                <v-chip
                  v-if="index <= 1"
                  color="primary"
                  close
                  small
                  @click:close="sendModel.receivers.splice(index, 1)"
                >
                  {{ item.userName }}
                </v-chip>
                <span v-if="index === 2" class="grey--text text-caption">
                  (+{{ sendModel.receivers.length - 2 }})
                </span>
              </template>
            </v-autocomplete>
          </template>

          <v-select
            v-model="sendModel.sendTimeType"
            color="primary"
            :menu-props="{ offsetY: true }"
            append-icon="mdi-chevron-down"
            :items="receiverTimeList"
            :label="$t('notices.sendTitle.time')"
            item-text="text"
            item-value="value"
            @change="changeTime"
          >
          </v-select>

          <template v-if="sendModel.sendTimeType === '1'">
            <date-picker
              :label="$t('notices.sendTitle.timeRange')"
              :rules="[
                v =>
                  !!v ||
                  $t('validation.required', [
                    $t('notices.sendTitle.timeRange'),
                  ]),
              ]"
              @handleTime="handleTime"
            />
            <!-- <div class="d-flex">
              <v-menu
                v-model="menu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
                left
                bottom
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    v-model="date"
                    :label="$t('notices.sendTitle.timeRange')"
                    append-icon="mdi-calendar-range-outline"
                    readonly
                    v-bind="attrs"
                    v-on="on"
                    style="width: 120px"
                    clearable
                    :rules="[
                      v =>
                        !!v ||
                        $t('validation.required', [
                          $t('notices.sendTitle.timeRange'),
                        ]),
                    ]"
                  ></v-text-field>
                </template>
                <v-date-picker
                  v-model="date"
                  :locale="$i18n.locale"
                  no-title
                  :min="
                    new Date(
                      new Date().getTime() -
                        new Date().getTimezoneOffset() * 60000,
                    )
                      .toISOString()
                      .substr(0, 10)
                  "
                  @input="menu = false"
                >
                </v-date-picker>
              </v-menu>
              <v-menu
                ref="menu"
                v-model="menu1"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
                left
                bottom
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    v-model="time"
                    append-icon="mdi-clock-time-four-outline"
                    readonly
                    v-bind="attrs"
                    v-on="on"
                    style="width: 120px"
                    class="ml-2"
                    clearable
                    :rules="[
                      v =>
                        !!v ||
                        $t('validation.required', [
                          $t('notices.sendTitle.timeRange'),
                        ]),
                    ]"
                  ></v-text-field>
                </template>
                <v-time-picker
                  v-if="menu1"
                  v-model="time"
                  use-seconds
                  :min="minTime"
                  @click:second="$refs.menu.save(time)"
                ></v-time-picker>
              </v-menu>
            </div> -->
          </template>
        </v-form>
      </vsoc-drawer>

      <vsoc-drawer
        :width="500"
        v-model="infoShow"
        :title="$t('notices.readInfo.title')"
        hideFooter
      >
        <v-data-table
          ref="table"
          fixed-header
          :items-per-page="infoQuery.pageSize"
          item-key="id"
          :height="tableHeight + 100"
          hide-default-footer
          :headers="infoHeaders"
          :items="infoData"
          class="table border-radius-xl thead-light"
          :loading="infoTableLoading"
        >
          <template
            v-for="item in infoHeaders"
            v-slot:[`header.${item.value}`]="{ header }"
          >
            <div :key="item.value">{{ $t(header.text) }}</div>
          </template>
        </v-data-table>
        <!-- 分页器 -->
        <vsoc-pagination
          :page.sync="infoQuery.pageNum"
          :size.sync="infoQuery.pageSize"
          :total="infoTotal"
          @change-page="getInfo"
          @change-size="$_infoSearch"
        >
        </vsoc-pagination>
      </vsoc-drawer>
    </v-card>
  </div>
</template>

<script>
import { getList, readDetail, sendMessage } from '@/api/message/index'
import { findAllUsers } from '@/api/system/user'
import VsocDrawer from '@/components/VsocDrawer.vue'
import VsocPagination from '@/components/VsocPagination.vue'
import breadCrumb from '@/components/bread-crumb/index'
import VsocDateRange from '@/components/vsoc-date-range/VsocDateRange.vue'
import { RANGE_STR } from '@/components/vsoc-date-range/constants'
import { deepClone } from '@/util/throttle'
import { setRemainingHeight } from '@/util/utils'
import { endOfDay, format, startOfDay, subDays } from 'date-fns'
import TableSearch from '@/components/TableSearch/index.vue'
import DatePicker from '@/components/date-picker.vue'
export default {
  name: 'MessageIndex',
  components: {
    VsocPagination,
    VsocDrawer,
    VsocDateRange,
    breadCrumb,
    TableSearch,
    DatePicker,
  },
  data() {
    return {
      queryKey: 'title',
      infoTableLoading: false,
      infoShow: false,
      infoTotal: 0,
      infoQuery: {
        pageSize: 10,
        pageNum: 1,
        id: '',
      },
      infoHeaders: [
        {
          text: 'notices.readInfo.user',
          value: 'name',
          width: '50%',
          sortable: false,
        },
        {
          text: 'notices.readInfo.status',
          value: 'readStatusName',
          width: '50%',
          sortable: false,
        },
      ],
      infoData: [],
      minTime: format(new Date(), 'HH:mm:ss'),
      menu: false,
      menu1: false,
      time: null,
      date: '',

      tableLoading: false,
      // 分页参数
      query: {
        pageNum: 1,
        pageSize: 10,
        title: '',
        messageType: '0',
        sendStatus: '',
        startDate: format(
          startOfDay(subDays(new Date(), 30)),
          'yyyy-MM-dd HH:mm:ss',
        ),
        endDate: format(endOfDay(new Date()), 'yyyy-MM-dd HH:mm:ss'),
      },
      dateRange: {
        range: {
          start: format(
            startOfDay(subDays(new Date(), 30)),
            'yyyy-MM-dd HH:mm:ss',
          ),
          end: format(endOfDay(new Date()), 'yyyy-MM-dd HH:mm:ss'),
        },
        menuProps: { offsetY: true, closeOnContentClick: false },
      },
      tableDataTotal: 0,
      tableHeight: '34.5rem',
      tableData: [],

      sendMessageVisible: false,
      sendModel: {
        messageType: '0',
        title: '',
        content: '',
        receiverType: '0', //消息接收人类型 0全部人 1用户组  2指定用户
        sendTimeType: '0',
        sendDate: '',
        receivers: [], //指定用户数组
      },
      sendRule: {
        title: [
          v =>
            !!v ||
            this.$t('validation.required', [
              this.$t('notices.sendTitle.title'),
            ]),
          v => v.length <= 100 || this.$t('validation.maxLen', [100]),
        ],
        content: [
          v =>
            !!v ||
            this.$t('validation.required', [
              this.$t('notices.sendTitle.content'),
            ]),
          v => v.length <= 512 || this.$t('validation.maxLen', [512]),
        ],

        receivers: [
          v => {
            if (v.length !== 0) return true
            return this.$t('validation.required', [
              this.$t('notices.readInfo.user'),
            ])
          },
        ],
      },
      userNameAll: [],
      groupNameAll: [],
      valid: true,
    }
  },
  created() {
    this.init()
    this.$_setTableHeight()
    this.$bus.$on('resize', this.$_setTableHeight)
  },
  computed: {
    searchList() {
      return [
        {
          type: 'multiSearch',
          value: 'title',
          conditions: [
            {
              type: 'input',
              value: 'title',
              text: this.$t('notices.headers.title'),
            },
            {
              type: 'select',
              value: 'sendStatus',
              text: this.$t('notices.headers.sendStatus'),
              itemList: this.$store.getters['enums/getMessageSendStatus'],
            },
          ],
        },
        {
          type: 'date',
          value: ['startDate', 'endDate'],
          text: this.$t('notices.headers.sendDate'),
          dateRange: {
            range: {
              start: format(
                startOfDay(subDays(new Date(), 30)),
                'yyyy-MM-dd HH:mm:ss',
              ),
              end: format(endOfDay(new Date()), 'yyyy-MM-dd HH:mm:ss'),
            },
            menuProps: { offsetY: true, closeOnContentClick: false },
          },
        },
      ]
    },
    headers() {
      return [
        {
          text: this.$t('notices.headers.title'),
          value: 'title',
          width: '140px',
          sortable: false,
        },
        {
          text: this.$t('notices.headers.receiverType'),
          value: 'receiverType',
          width: '160px',
          sortable: false,
        },
        {
          text: this.$t('notices.headers.sendStatus'),
          value: 'sendStatus',
          width: '140px',
          sortable: true,
        },
        {
          text: this.$t('notices.headers.sendCount'),
          value: 'sendCount',
          width: '120px',
          sortable: true,
        },
        {
          text: this.$t('notices.headers.readCount'),
          value: 'readCount',
          width: '120px',
          sortable: true,
        },
        {
          text: this.$t('notices.headers.readingPercentage'),
          value: 'readingPercentage',
          width: '120px',
          sortable: true,
        },
        {
          text: this.$t('global.createDate'),
          value: 'createDate',
          width: '160px',
          sortable: true,
        },
        {
          text: this.$t('notices.headers.sendDate'),
          value: 'sendDate',
          width: '160px',
          sortable: true,
        },
        {
          text: '',
          value: 'actions',
          sortable: false,
          width: '60px',
        },
      ]
    },
    // 查询条件列表
    searchConditions() {
      return [
        {
          text: this.$t('notices.headers.title'),
          value: 'title',
        },
        {
          text: this.$t('notices.headers.sendStatus'),
          value: 'sendStatus',
        },
      ]
    },
    recipientTypeMap() {
      return this.$store.state.enums.enums.RecipientType
    },
    MessageSendStatus() {
      return this.$store.getters['enums/getMessageSendStatus']
    },
    MessageSendStatusMap() {
      return this.$store.state.enums.enums['Message Send Status']
    },
    receiverTypeList() {
      return this.$store.getters['enums/getReceiverTypeList'].filter(
        v => v.dictId !== '2',
      )
    },
    receiverTimeList() {
      return this.$store.getters['enums/getReceiverTimeList']
    },
  },
  beforeDestroy() {
    this.$bus.$off('resize', this.$_setTableHeight)
  },
  methods: {
    handleTime(val) {
      this.sendModel.sendDate = val
    },
    onClear(queryKey) {
      this.query[queryKey] = ''
      this.$_search()
    },
    changeCondition(item) {
      this.searchConditions.forEach(v => {
        if (v.value !== item) {
          this.query[v.value] = ''
        }
      })
    },
    checkInfo(item) {
      this.infoShow = true
      this.infoQuery.id = item.id
      this.infoTableLoading = false
      this.infoData = []
      this.infoTotal = 0
      this.$_infoSearch()
    },
    $_infoSearch() {
      this.infoQuery.pageNum = 1
      this.getInfo()
    },
    async getInfo() {
      this.infoTableLoading = true
      try {
        const res = await readDetail(this.infoQuery)
        this.infoData = res.data.records || []
        this.infoTotal = res.data.total
      } catch (e) {
        console.log(`获取详情数据错误：${e}`)
      }
      this.infoTableLoading = false
    },
    $_reset() {
      this.query = Object.assign(
        this.$data.query,
        this.$options.data.call(this).query,
      )
      this.$_search()
    },
    changeTime() {
      this.date = ''
      this.time = null
    },
    changeReceiverType() {
      this.sendModel.receivers = []
    },
    // 单击表格行
    $_clickRow(item, slot) {
      slot.expand(!slot.isExpanded)
    },
    RANGE_STR,
    // 消息发送时间改变
    onChangeDate(range) {
      this.dateRange.range = range
      this.query.startDate = range.start
      this.query.endDate = range.end
      this.$_search()
    },
    $_setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = setRemainingHeight() - 12
      })
    },

    $_search() {
      this.query.pageNum = 1
      this.getMessageList()
    },

    init() {
      this.getMessageList()
      this.getUserNames()
    },
    changePage(e) {
      this.getMessageList()
    },
    changeSize(e) {
      this.$_search()
    },
    async getMessageList() {
      this.tableLoading = true
      try {
        const res = await getList(this.query)
        this.tableData = res.data.records || []
        this.tableDataTotal = res.data.total
        this.tableData = this.tableData.map(v => {
          return {
            ...v,
            receiverList: v.receivers.length
              ? v.receivers.map(v => v.name).join(',')
              : '',
          }
        })
      } catch (e) {
        console.log(`获取消息列表数据错误：${e}`)
      }
      this.tableLoading = false
    },

    async getUserNames() {
      try {
        const res = await findAllUsers({ status: '1' })
        this.userNameAll = res.data || []
      } catch (e) {
        console.log(`获取所有用户名错误：${e}`)
      }
    },

    async confirmSendMessage(callBack) {
      try {
        const bool = await this.$refs.form.validate()
        if (!bool) return callBack(false, true)
        const data = deepClone(this.sendModel)
        // data.sendDate = this.date + ' ' + this.time
        let userList = []

        if (data.receiverType === '1') {
          userList =
            this.userNameAll.filter(
              v => data.receivers.indexOf(v.userId) !== -1,
            ) || []

          if (userList.length) {
            data.receivers = userList.map(v => {
              return {
                id: v.userId,
                name: v.userName,
              }
            })
          }
        }
        const res = await sendMessage(data)
        if (res.code === 200) {
          this.$notify.info(
            'success',
            this.$t('global.hint.add', [this.$t('notices.currentTitle')]),
          )
          this.$store.dispatch('global/getMessge')
          this.$_search()
        }
        callBack()
      } catch (e) {
        callBack(false, true)
        console.error(`公告创建失败：${e}`)
      }
    },

    initSendModel() {
      this.date = ''
      this.time = null
      this.sendModel = {
        messageType: '0',
        title: '',
        content: '',
        receiverType: '0', //消息接收人类型 0全部人 1用户组  2指定用户
        sendTimeType: '0',
        sendDate: '',
        receivers: [], //指定用户数组
      }
    },

    addMessage() {
      this.sendMessageVisible = true
      this.initSendModel()
      this.$refs.form.resetValidation()
    },
  },
}
</script>

<style lang="scss" scoped>
.drawer-mask {
  position: fixed;
  z-index: 99;
  opacity: 0.46;
  background-color: rgb(33, 33, 33);
}
::v-deep .copy-box {
  margin-left: 4px;
  width: 24px !important;
  height: 24px !important;
}
::v-deep .vsoc-pagination {
  white-space: nowrap !important;
  span {
    margin: 0 !important;
  }
  .v-pagination__navigation {
    margin: 0 !important;
  }
}

.message-box {
  .row {
    padding: 0;
    margin: 0;
  }
  .col {
    padding: 0;
    margin: 0;
  }
}
</style>
