<template>
  <vsoc-drawer
    v-model="isDrawerShow"
    width="30%"
    :title="
      mode === 'new'
        ? $t('global.drawer.addTitle', { cur: $generateMenuTitle($route.meta) })
        : $t('global.drawer.editTitle', {
            cur: $generateMenuTitle($route.meta),
          })
    "
    @click:confirm="onSave"
  >
    <v-form ref="form" v-model="valid" lazy-validation>
      <div class="mt-4">
        <v-row class="pl-6 pr-6">
          <v-text-field
            v-model="editForm.itemKey"
            :label="$t('cloud.headers.itemKey')"
            required
            autocomplete="off"
            color="primary"
            :disabled="mode === 'edit'"
          >
          </v-text-field>
        </v-row>
        <v-row class="pl-6 pr-6">
          <v-text-field
            v-model="editForm.itemName"
            :label="$t('cloud.headers.itemName')"
            required
            color="primary"
            :rules="rulesName"
          >
          </v-text-field>
        </v-row>
        <!-- <v-row class="pl-6 pr-6">
          <v-text-field
            v-model="editForm.itemEnName"
            :label="$t('cloud.headers.itemEnName')"
            required
            color="primary"
            :rules="rulesEnName"
          >
          </v-text-field>
        </v-row>
        <v-row  class="pl-6 pr-6">
          <v-select
            v-model="editForm.chartValue"
            :label="$t('cloud.headers.chart')"
            :menu-props="{ offsetY: true }"
            color="primary"
            :items="chartEnum"
            required
            disabled
          >
          </v-select>
        </v-row> -->
        <!-- <v-row class="pl-6 pr-6">
          <div class="d-flex align-center">
            <v-col class="pa-0 ma-0 mr-6">
              <v-text-field
                v-model="editForm.startWidth"
                :label="$t('cloud.headers.initialWidth')"
                required
                color="primary"
                :rules="[
                  v => required(v, $t('cloud.headers.initialWidth')),
                  v => /^(1[0-2]|[1-9])$/.test(v) || $t('cloud.tip'),
                ]"
              >
              </v-text-field>
            </v-col>
            <v-col class="pa-0 ma-0">
              <v-text-field
                v-model="editForm.startHeight"
                :label="$t('cloud.headers.initialHeight')"
                required
                color="primary"
                :rules="[v => required(v, $t('cloud.headers.initialHeight'))]"
              >
              </v-text-field>
            </v-col>
          </div>
        </v-row>
        <v-row class="pl-6 pr-6">
          <div class="d-flex align-center">
            <v-col class="pa-0 ma-0 mr-6">
              <v-text-field
                v-model="editForm.minWidth"
                :label="$t('cloud.headers.minWidth')"
                required
                color="primary"
                :rules="[v => required(v, $t('cloud.headers.minWidth'))]"
              >
              </v-text-field>
            </v-col>
            <v-col class="pa-0 ma-0">
              <v-text-field
                v-model="editForm.minHeight"
                :label="$t('cloud.headers.minHeight')"
                required
                color="primary"
                :rules="[v => required(v, $t('cloud.headers.minHeight'))]"
              >
              </v-text-field>
            </v-col>
          </div>
        </v-row>
        <v-row class="pl-6 pr-6">
          <v-select
            v-model="editForm.isActive"
            :label="$t('cloud.headers.status')"
            :menu-props="{ offsetY: true }"
            color="primary"
            :items="borderEnum"
            required
          >
          </v-select>
        </v-row> -->
        <v-row class="pl-6 pr-6">
          <v-select
            v-model="editForm.isBorder"
            :label="$t('cloud.headers.border')"
            :menu-props="{ offsetY: true }"
            color="primary"
            :items="borderEnum"
            required
          >
          </v-select>
        </v-row>
        <v-row class="pl-6 pr-6">
          <v-select
            v-model="editForm.contentSourceType"
            :label="$t('cloud.headers.readMethod')"
            :menu-props="{ offsetY: true }"
            color="primary"
            :items="cloudMethodEnum"
          >
          </v-select>
        </v-row>
        <v-row v-if="editForm.contentSourceType === '0'" class="pl-6 pr-6">
          <!-- <div v-if="editForm.itemKey === 'C031'">
            <v-menu
              v-model="isMenu"
              transition="scale-transition"
              offset-y
              :close-on-content-click="false"
              min-width="auto"
              max-width="100%"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  v-model="editForm.value"
                  :label="$t('cloud.headers.staticValue')"
                  readonly
                  append-icon="mdi-calendar-range-outline"
                  v-bind="attrs"
                  v-on="on"
                ></v-text-field>
              </template>
              <v-date-picker
                v-model="editForm.value"
                @input="isMenu = false"
                no-title
                :locale="$i18n.locale"
              ></v-date-picker>
            </v-menu>
          </div> -->
          <div>
            <div class="color-base text-ml d-flex justify-lg-space-between">
              {{ $t('cloud.headers.staticValue') }}
            </div>
            <vsoc-code-mirror
              ref="codeMirror"
              :code="editForm.value"
              height="auto"
            ></vsoc-code-mirror>
          </div>
        </v-row>
        <!-- <v-row class="pl-6 pr-6">
          <div v-if="item.itemKey === 'C001'">
            <div class="mb-4 text-ml color-base font-weight-semibold">
              {{ $t('cloud.static.added') }}({{ editForm.value.length }})
            </div>
            <v-chip
              label
              close
              color="primary"
              class="mr-2 mb-2"
              v-for="(item, index) in editForm.value"
              :key="index"
              @click:close="onRemoveItem(item, index)"
              >{{ item }}</v-chip
            >
            <v-text-field
              v-model="currentTag"
              :label="$t('cloud.static.handling')"
              color="primary"
              :hint="currentTag && `${$t('cloud.static.hint')}${currentTag}`"
              @keyup.enter.native="onAddItem"
            >
            </v-text-field>
          </div>
          <div v-else-if="item.itemKey === 'C006'">
            <v-menu
              v-model="isMenu"
              transition="scale-transition"
              offset-y
              :close-on-content-click="false"
              min-width="auto"
              max-width="100%"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  v-model="editForm.value"
                  :label="$t('cloud.headers.staticValue')"
                  readonly
                  append-icon="mdi-calendar-range-outline"
                  v-bind="attrs"
                  v-on="on"
                ></v-text-field>
              </template>
              <v-date-picker
                v-model="editForm.value"
                @input="isMenu = false"
                no-title
                :locale="$i18n.locale"
              ></v-date-picker>
            </v-menu>
          </div>
          <template v-else>
            <template v-if="typeof editForm.value == 'object'">
              <div class="color-base text-ml d-flex justify-lg-space-between">
                <div>{{ $t('cloud.headers.staticValue') }}</div>
                <v-btn
                  v-if="item.itemKey === 'C007'"
                  icon
                  v-show-tips="$t('cloud.exampleHint')"
                  @click="onDownLoad"
                >
                  <vsoc-icon icon="mdi-download"></vsoc-icon>
                </v-btn>
              </div>
              <vsoc-code-mirror
                ref="codeMirror"
                :code="editForm.value"
                height="auto"
              ></vsoc-code-mirror
            ></template>
            <template v-else>
              <v-textarea
                v-model="editForm.value"
                auto-grow
                :label="$t('cloud.headers.staticValue')"
                color="primary"
                rows="4"
              >
              </v-textarea>
            </template>
          </template>
        </v-row> -->
      </div>
    </v-form>
  </vsoc-drawer>
</template>

<script>
import { max, required } from '@/@core/utils/validation'
import {
  queryDashboardsByItemKey,
  updateConfig,
  updateDashboardsByItemKey,
} from '@/api/grid/index'
import VsocCodeMirror from '@/components/VsocCodeMirror.vue'
import VsocDrawer from '@/components/VsocDrawer.vue'
// import VsocIcon from '@/components/VsocIcon.vue'
export default {
  name: 'CloudEdit',
  components: {
    VsocDrawer,
    VsocCodeMirror,
    // VsocIcon,
  },
  props: {
    item: {
      type: Object,
      required: false,
    },
    mode: {
      type: String,
      required: false,
      default: () => 'new',
    },
  },
  computed: {
    chartEnum() {
      return this.$store.getters['enums/getChartItems']
    },
    cloudMethodEnum() {
      return this.$store.getters['enums/getCloudMethod']
    },
    borderEnum() {
      return this.$store.getters['enums/getChartBorder']
    },
    activeEnum() {
      return this.$store.getters['enums/getChartactive']
    },
  },
  data() {
    return {
      isMenu: false,
      currentTag: '',
      max,
      required,
      editForm: this.item,
      valid: true,
      isDrawerShow: false,
      rulesName: [v => required(v, this.$t('cloud.headers.itemName'))],
      rulesEnName: [v => required(v, this.$t('cloud.headers.itemEnName'))],
    }
  },
  watch: {
    isDrawerShow: {
      handler(newVal) {
        if (newVal) {
          this.$refs.form.resetValidation()
          if (this.mode === 'new') {
            this.editForm = {
              id: '',
              itemKey: '',
              itemName: '',
              itemEnName: '',
              contentSourceType: '',
              changeType: '',
              value: [],
            }
          } else {
            this.editForm = { ...this.item }
          }
        }
      },
      immediate: true,
    },
  },
  methods: {
    // onDownLoad() {
    //   if (
    //     !this.editForm.valueExample ||
    //     this.editForm.valueExample.length === 0
    //   ) {
    //     this.$notify.info('error', this.$t('cloud.empty'))
    //     return
    //   }
    //   const blob = new Blob([JSON.stringify(this.editForm.valueExample)], {
    //     type: 'application/json;charset=utf-8',
    //   })
    //   const href = URL.createObjectURL(blob)
    //   const alink = document.createElement('a')
    //   alink.style.display = 'none'
    //   alink.download = this.editForm.itemEnName + '.json'
    //   alink.href = href
    //   document.body.appendChild(alink)
    //   alink.click()
    //   document.body.removeChild(alink)
    //   URL.revokeObjectURL(href)
    // },
    // onAddItem() {
    //   this.editForm.value.push(this.currentTag)
    //   this.currentTag = ''
    // },
    // onRemoveItem(record, index) {
    //   this.editForm.value.splice(index, 1)
    // },
    async onSave(callback) {
      try {
        const bool = this.$refs.form.validate()
        if (!bool) {
          callback(false, true)
          return
        }
        if (this.$refs.codeMirror) {
          this.editForm.value = this.$refs.codeMirror.getCode()
        }
        updateConfig(this.editForm).then(res => {
          queryDashboardsByItemKey({ itemKey: this.editForm.itemKey }).then(
            v => {
              if (v.data && v.data.length) {
                this.$swal({
                  title: this.$t('cloud.swal.title'),
                  text: this.$t('cloud.swal.text', [v.data.length]),
                  icon: 'warning',
                  reverseButtons: true,
                  showCancelButton: true,
                  confirmButtonText: this.$t('cloud.swal.title1'),
                  cancelButtonText: this.$t('cloud.swal.title2'),
                  customClass: {
                    confirmButton: 'sweet-btn-primary',
                    container: 'container-box',
                  },
                }).then(result => {
                  if (result.isConfirmed) {
                    const obj = {
                      itemKey: this.editForm.itemKey,
                      canvasIdList: v.data.map(x => x.id),
                    }
                    updateDashboardsByItemKey(obj).then(x => {
                      this.$notify.info('success', this.$t('cloud.swal.hint'))
                      this.$emit('refresh')
                      callback()
                    })
                  } else {
                    this.$notify.info(
                      'success',
                      this.$t('global.hint.edit', {
                        cur: this.$t('cloud.currentTitle'),
                      }),
                    )
                    this.$emit('refresh')
                    callback()
                  }
                })
              } else {
                this.$notify.info(
                  'success',
                  this.$t('global.hint.edit', {
                    cur: this.$t('cloud.currentTitle'),
                  }),
                )
                this.$emit('refresh')
                callback()
              }
            },
          )
        })
      } catch (err) {
        callback(false, true)
      }
    },
  },
}
</script>
