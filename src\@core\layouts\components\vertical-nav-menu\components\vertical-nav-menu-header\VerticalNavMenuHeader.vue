<template>
  <!-- align-center justify-center -->
  <div
    class="vertical-nav-header d-flex"
    :style="`width: ${$store.state.global.leftMenuWidth}px`"
  >
    <div class="d-flex align-center text-decoration-none" @click.stop="goHome">
      <v-img
        :src="appLogo"
        max-height="100%"
        alt="logo"
        contain
        class="app-logo ml-4"
      ></v-img>
      <div class="white--text text-nxl font-weight-semibold ml-6">
        {{ appName }}
      </div>
      <!-- <v-slide-x-transition>
        <h2
          v-show="!(menuIsVerticalNavMini && !isMouseHovered)"
          class="app-title text--primary"
        >
          {{ appName }}
        </h2>
      </v-slide-x-transition> -->
    </div>
    <!-- <v-slide-x-transition>
      <div
        v-show="!(menuIsVerticalNavMini && !isMouseHovered)"
        v-if="$vuetify.breakpoint.lgAndUp"
        class="d-flex align-center ms-1"
        @click.stop="menuIsVerticalNavMini = !menuIsVerticalNavMini"
      >
        <v-icon
          v-show="!menuIsVerticalNavMini"
          size="20"
          class="cursor-pointer"
        >
          {{ icons.mdiRecordCircleOutline }}
        </v-icon>
        <v-icon
          v-show="menuIsVerticalNavMini"
          size="20"
          class="cursor-pointer"
        >
          {{ icons.mdiRadioboxBlank }}
        </v-icon>
      </div>
      <v-icon
        v-else
        size="20"
        class="d-inline-block"
        @click.stop="$emit('close-nav-menu')"
      >
        {{ icons.mdiClose }}
      </v-icon>
    </v-slide-x-transition> -->
  </div>
</template>

<script>
import { isExternal } from '@/@core/utils/validation'
import useAppConfig from '@core/@app-config/useAppConfig'
import { useRouter } from '@core/utils'
import { mdiClose, mdiRadioboxBlank, mdiRecordCircleOutline } from '@mdi/js'
import themeConfig from '@themeConfig'
import { getCurrentInstance } from '@vue/composition-api'

export default {
  setup() {
    const { menuIsVerticalNavMini } = useAppConfig()
    const vm = getCurrentInstance().proxy
    const { router } = useRouter()

    // const isMouseHovered = inject('isMouseHovered')
    const goHome = () => {
      let myRouters = vm.$store.state.permission.routes
      let currentPath = myRouters.find(v => !isExternal(v.path))?.path || '/'
      router.push({
        path: currentPath,
      })
    }
    return {
      menuIsVerticalNavMini,
      // isMouseHovered,
      appName: themeConfig.app.name,
      appLogo: themeConfig.app.logo,
      goHome,
      // Icons
      icons: {
        mdiClose,
        mdiRadioboxBlank,
        mdiRecordCircleOutline,
      },
    }
  },
}
</script>

<style lang="scss" scoped>
.vertical-nav-header {
  height: 100%;
  // box-shadow: 1px 0px 0px 0px #1f2342;
}
.app-title {
  font-size: 1.25rem;
  font-weight: 700;
  letter-spacing: 0.3px;
}

// ? Adjust this `translateX` value to keep logo in center when vertical nav menu is collapsed (Value depends on your logo)
.app-logo {
  transition: all 0.18s ease-in-out;
  .v-navigation-drawer--mini-variant & {
    transform: translateX(-4px);
  }
}
</style>
