<template>
  <!-- 环形图 -->
  <div class="box">
    <div class="box-header">{{ title }}</div>
    <template v-if="list.out.list.length !== 0">
      <vsoc-chart
        class="box-chart d-flex align-center"
        :echartId="`car-pie-${Math.random()}`"
        :option="chartOption"
        @highlight="onHighlight"
      ></vsoc-chart>
    </template>
    <template v-else>
      <div
        class="box-chart d-flex align-center color--primary justify-center fs-16"
      >
        None
      </div>
    </template>
  </div>
</template>
<script>
import VsocChart from '@/components/VsocChart.vue'
import { num, numberToFormat } from '@/util/filters'
import { getRoundSize, tooltip } from './chart'
export default {
  name: 'CardItem16',
  components: {
    VsocChart,
  },
  props: {
    title: {
      type: String,
      default: '',
    },
    isFull: {
      type: Boolean,
      default: false,
    },
    total: {
      type: [Number, String],
      default: () => {
        return 0
      },
    },
    echartId: {
      type: String,
      default: '',
    },
    list: {
      type: Object,
      default: () => {
        return {
          out: {
            list: [],
          },
          in: {
            list: [],
          },
        }
      },
    },
  },
  data() {
    return {}
  },
  computed: {
    chartOption() {
      let outData = this.list.out.list
      let inData = this.list.in.list

      let common = {
        type: 'pie',
        clockwise: false,
        emphasis: {
          scale: true,
          scaleSize: 2,
        },
        center: ['50%', '52%'],
        bottom: -getRoundSize(8),
        percentPrecision: 2,
      }

      let seriesOption = [
        //外面的环
        {
          name: '',
          radius: ['46%', '57%'],
          itemStyle: {
            normal: {
              opacity: 1,
            },
          },
          labelLayout: {
            hideOverlap: false, // 不隐藏重叠标签
          },
          label: {
            normal: {
              position: 'outside',
              alignTo: 'edge',
              edgeDistance: '4%',
              formatter: function (params) {
                if (params.name !== '') {
                  return `{legend|●}\t{name|${
                    params.name
                  }}{value|(${numberToFormat(params.value)})}`
                } else {
                  return ''
                }
              },
              rich: {
                value: {
                  fontSize: getRoundSize(12),
                  lineHeight: getRoundSize(22),
                  color: '#FFFFFF',
                  opacity: 0.7,
                  padding: [getRoundSize(8), 0],
                },
                name: {
                  fontSize: getRoundSize(12),
                  lineHeight: getRoundSize(22),
                  color: '#FFFFFF',
                  opacity: 0.7,
                },
                legend: {
                  padding: [0, getRoundSize(2), 0, 0],
                  fontSize: getRoundSize(14),
                  color: 'inherit',
                },
              },
            },
          },
          labelLine: {
            length: 10,
            length2: 5,
            lineStyle: {
              width: 1,
            },
          },
          ...common,
          data: outData,
        },
        {
          name: '',
          radius: ['20%', '31%'],
          itemStyle: {
            normal: {
              opacity: 1,
            },
          },
          labelLayout: {
            hideOverlap: false, // 不隐藏重叠标签
          },
          label: {
            normal: {
              position: 'outside',
              alignTo: 'edge',
              edgeDistance: '8%',
              formatter: function (params) {
                if (params.name !== '') {
                  return `{legend|●}\t{name|${
                    params.name
                  }}{value|(${numberToFormat(params.value)})}`
                } else {
                  return ''
                }
              },
              rich: {
                value: {
                  fontSize: getRoundSize(12),
                  lineHeight: getRoundSize(22),
                  color: '#FFFFFF',
                  opacity: 0.7,
                  padding: [getRoundSize(8), 0],
                },
                name: {
                  fontSize: getRoundSize(12),
                  lineHeight: getRoundSize(22),
                  color: '#FFFFFF',
                  opacity: 0.7,
                },
                legend: {
                  padding: [0, getRoundSize(2), 0, 0],
                  fontSize: getRoundSize(14),
                  color: 'inherit',
                },
              },
            },
          },
          labelLine: {
            length: 10,
            length2: 5,
            lineStyle: {
              width: 1,
            },
          },
          ...common,
          data: inData,
        },
      ]
      return {
        backgroundColor: 'transparent',
        color: ['#66C8FF', '#F2BA02', '#2ee56a', '#EE822F'],
        legend: {
          show: false,
        },
        tooltip: {
          ...tooltip(),
          trigger: 'item',
          axisPointer: { type: 'shadow' },
          borderWidth: 0,
          formatter: function (params) {
            return `${params.marker} ${params.name}：${num(params.value)} (${
              params.percent
            }%)`
          },
        },
        series: seriesOption,
      }
    },
  },
  created() {},
  methods: {
    onHighlight(obj, myChart) {
      // const option = this.chartOption
      // option.tooltip.backgroundColor = obj.color
      // myChart.setOption(option)
    },
  },
}
</script>
