<!-- 资产详情 -->
<template>
  <div class="px-2 py-3 asset-detail-box">
    <div class="d-flex align-center mt-n1 mb-1">
      <v-btn class="mr-3" icon small @click="$router.go(-1)">
        <v-icon class="text--primary" size="20"> mdi-arrow-left </v-icon>
      </v-btn>
      <v-breadcrumbs class="pa-0" :items="breadItems"></v-breadcrumbs>
    </div>
    <v-row dense class="ma-0">
      <v-col cols="12">
        <v-card class="px-4 pb-6 pt-12 d-flex align-center position-relative">
          <v-btn
            @click.stop="onDel"
            v-show-tips="$t('action.del')"
            v-has:asset-detail-del
            class="mr-1 bg-btn position-absolute px-3 py-2"
            style="top: 20px; right: 16px"
            elevation="0"
            x-small
          >
            <vsoc-icon
              type="fill"
              class="primary--text"
              size="middle"
              icon="icon-shanchu"
            ></vsoc-icon>
          </v-btn>

          <v-card-text class="w-40 py-0 pl-8">
            <v-img
              contain
              width="22vw"
              max-width="22vw"
              :src="
                assetsDetails.pictureCode ||
                require('@/assets/images/pages/<EMAIL>')
              "
            />
            <div
              v-if="carStatusMap[assetsDetails.healthStatus]"
              class="d-flex align-center justify-center mt-1"
            >
              <div
                class="text-uppercase text-5xl font-weight-semibold"
                :style="{
                  color: `${carStatusMap[assetsDetails.healthStatus].color}`,
                }"
              >
                {{ carStatusMap[assetsDetails.healthStatus].text1 }}
              </div>
              <v-divider
                vertical
                class="my-4 mx-4 accent"
                inset
                style="border-width: 2px"
              ></v-divider>
              <div>
                <div class="text-xxl text--primary">
                  {{ $t('asset.headers.currentSituation') }}
                </div>
                <!-- <div class="">Current Situation</div> -->
              </div>
            </div>
          </v-card-text>
          <v-card-text class="pl-10 text-base">
            <v-row>
              <v-col cols="4">
                <div class="mt-1">
                  <v-chip tile label x-small color="primary" class="me-2 pa-1"
                    >No.</v-chip
                  >
                  <span class="primary--text">{{
                    $t('asset.headers.assetId')
                  }}</span>
                </div>
                <span class="text--primary text-ml">
                  {{ assetsDetails.id }}
                </span>
                <v-btn
                  v-copy="assetsDetails.id"
                  v-show-tips="$t('action.copy')"
                  icon
                  class="ml-n1 mt-n1"
                >
                  <vsoc-icon
                    type="fill"
                    size="16px"
                    icon="icon-fuzhi"
                  ></vsoc-icon>
                </v-btn>
              </v-col>

              <v-col cols="3">
                <div class="my-1">
                  {{ $t('asset.headers.firstRegistrationTime') }}
                </div>
                <div class="text--primary text-ml">
                  {{ assetsDetails.regisDate | toDate }}
                </div>
              </v-col>
              <v-col cols="5">
                <div class="my-1">{{ $t('asset.headers.newAlert') }}</div>
                <div
                  v-if="assetsDetails.healthStatus"
                  class="w-100 d-flex align-center text-white text-base font-weight-semibold alert-list"
                >
                  <div
                    v-if="assetsDetails.assetAlarmInfoVos.length > 0"
                    class="cursor-pointer"
                    @click.stop="goAlert"
                  >
                    <v-avatar
                      class="mr-2 icon-fb"
                      size="2.35rem"
                      v-for="(
                        alarmItem, index
                      ) in assetsDetails.assetAlarmInfoVos"
                      :key="index"
                      v-show-tips="
                        (alarmLevel[alarmItem.alarmLevel] &&
                          alarmLevel[alarmItem.alarmLevel].text) +
                        ':' +
                        alarmItem.alarmCount
                      "
                      :color="
                        alarmLevel[alarmItem.alarmLevel] &&
                        alarmLevel[alarmItem.alarmLevel].color
                      "
                    >
                      <span class="text-sm">{{ alarmItem.alarmCount }}</span>
                    </v-avatar>
                  </div>
                  <div v-else>
                    {{ '' | dataFilter }}
                  </div>
                </div>
              </v-col>
            </v-row>
            <!-- <div class="d-flex align-center my-7">
              <div
                class="d-flex justify-center white-space-nowrap overflow-auto"
              >
                <div
                  v-if="assetsDetails.healthStatus"
                  class="w-100 d-flex align-center text-white text-base font-weight-semibold alert-list"
                >
                  <div
                    v-if="assetsDetails.assetAlarmInfoVos.length > 0"
                    class="cursor-pointer"
                    @click.stop="goAlert"
                  >
                    <v-avatar
                      class="mr-2 icon-fb"
                      size="2.35rem"
                      v-for="(
                        alarmItem, index
                      ) in assetsDetails.assetAlarmInfoVos"
                      :key="index"
                      v-show-tips="
                        alarmLevel[alarmItem.alarmLevel] &&
                        alarmLevel[alarmItem.alarmLevel].text
                      "
                      :color="
                        alarmLevel[alarmItem.alarmLevel] &&
                        alarmLevel[alarmItem.alarmLevel].color
                      "
                    >
                      <span class="text-sm">{{ alarmItem.alarmCount }}</span>
                    </v-avatar>
                  </div>
                  <div v-else>
                    {{ '' | dataFilter }}
                  </div>
                </div>
              </div>

              <v-divider
                vertical
                inset
                class="mx-4 my-2 accent"
                style="border-width: 2px"
              ></v-divider>
              <div>{{ $t('asset.alertStatus') }}</div>
            </div> -->
            <v-row>
              <v-col cols="4">
                <div class="mt-1">VIN</div>
                <div class="d-flex align-center">
                  <div
                    v-show-tips
                    style="max-width: 160px"
                    class="text-ml color-base"
                  >
                    {{ assetsDetails.vin }}
                  </div>
                  <v-btn
                    v-has:asset-detail-lock
                    v-show-tips="
                      isDecipherVin ? $t('action.encode') : $t('action.decode')
                    "
                    icon
                    class="alert-handle-btn ml-1"
                    @click="onVin"
                  >
                    <vsoc-icon
                      size="16px"
                      :icon="
                        !isDecipherVin
                          ? 'mdi-lock-check-outline'
                          : 'mdi-lock-plus-outline'
                      "
                    ></vsoc-icon>
                  </v-btn>
                  <v-btn
                    v-copy="assetsDetails.vin"
                    v-show-tips="$t('action.copy')"
                    icon
                    class="alert-handle-btn"
                  >
                    <vsoc-icon
                      type="fill"
                      size="16px"
                      icon="icon-fuzhi"
                    ></vsoc-icon>
                  </v-btn>
                  <!-- <span class="text-ml color-base">{{
                    assetsDetails.vin
                  }}</span>
                  <v-btn
                    v-show-tips="
                      isDecipherVin ? $t('action.encode') : $t('action.decode')
                    "
                    icon
                    v-has:asset-detail-lock
                    @click="onVin"
                    class="mt-n1"
                  >
                    <vsoc-icon
                      size="middle"
                      :icon="
                        !isDecipherVin
                          ? 'mdi-lock-check-outline'
                          : 'mdi-lock-plus-outline'
                      "
                    ></vsoc-icon>
                  </v-btn> -->
                </div>
              </v-col>
              <v-col cols="3">
                <div class="my-1">{{ $t('asset.headers.lastActiveTime') }}</div>
                <div class="text--primary text-ml">
                  {{ assetsDetails.lastReceiveDate | toDate }}
                </div>
              </v-col>
              <v-col cols="5">
                <div class="my-1">{{ $t('asset.headers.lastPosition') }}</div>
                <div v-show-tips="address" class="text--primary text-ml">
                  {{ address | dataFilter }}
                </div>
              </v-col>
            </v-row>
            <v-row class="pt-2">
              <v-col>
                <!-- 第二版资产组 -->
                <!-- <template>
                      <div class="text-center">
                        <v-menu offset-x :close-on-content-click="false" @input="onBlurGroupSelect">
                          <template v-slot:activator="{ on, attrs }">
                            <v-btn icon rounded color="#fff" width="1.5rem" height="1.5rem"
                              @click.stop="editGroupMember(assetsDetails)" v-bind="attrs" v-on="on">
                              <v-icon size="1.25rem" class="material-icons-round " dark>
                                mdi-plus-circle
                              </v-icon>
                            </v-btn>
                          </template>
                          <v-list dense>
                            <v-list-item v-for="(item, index) in groupMember.allGroups" :key="index">
                              <v-checkbox v-model="groupMember.selectGroups" :value="item.text" :label="item.text"></v-checkbox>
                            </v-list-item>
                          </v-list>
                        </v-menu>
                      </div>
                    </template> -->
                <!-- 第一版资产组 -->
                <!-- <v-btn
                      icon
                      rounded
                      color="#fff"
                      width="1.5rem"
                      height="1.5rem"
                      @click.stop="editGroupMember(assetsDetails)"
                    >
                      <v-icon size="1.25rem" class="material-icons-round" dark>
                        mdi-plus-circle
                      </v-icon>
                    </v-btn>
                    <template v-if="groupMember.showEditGroup">
                      <v-select
                        full-width
                        v-model="groupMember.selectGroups"
                        multiple
                        item-text="text"
                        item-value="text"
                        :items="groupMember.allGroups"
                        outlined
                        dense
                        hide-details="auto"
                        class="mb-4"
                        @blur="onBlurGroupSelect"
                      >
                      </v-select>
                    </template> -->
                <!-- 资产组 -->
                <div>
                  <span class="mr-1">{{ $t('asset.headers.assetGroup') }}</span>
                  <v-btn
                    x-small
                    icon
                    rounded
                    color="reverse"
                    width="1.5rem"
                    height="1.5rem"
                    @click.stop="editGroupMember()"
                  >
                    <v-icon size="1.25rem" class="material-icons-round">
                      mdi-plus-circle
                    </v-icon>
                  </v-btn>
                </div>
                <!-- <template>

                  <v-dialog
                    max-width="45rem"
                    v-model="isDialogVisible"
                    scrollable
                  >
                    <template #activator="{ on, attrs }"> </template>
                    <v-card>
                      <v-card-title>选择资产组</v-card-title>
                      <v-divider></v-divider>
                      <v-card-text>
                       <v-autocomplete
                          v-model="groupMember.selectGroups"
                          color="primary"
                          :items="groupMember.allGroups"
                          label="资产组"
                          multiple
                          outlined
                          hide-details
                          chips
                          :menu-props="{ offsetY: true }"
                          class="mt-5 mb-1"
                          item-text="text"
                          item-value="text"
                        >
                          <template v-slot:selection="{ item, index }">
                            <v-chip
                              label
                              color="primary"
                              close
                              @click:close="
                                groupMember.selectGroups.splice(index, 1)
                              "
                            >
                              <span>{{ item.text }}</span>
                            </v-chip>
                          </template>
                        </v-autocomplete>
                      </v-card-text>

                      <v-divider></v-divider>

                      <v-card-actions>
                        <v-spacer></v-spacer>
                        <v-btn
                          outlined
                          depressed
                          @click="isDialogVisible = false"
                        >
                          取消
                        </v-btn>
                        <v-btn
                          color="primary"
                          :loading="confirmLoading"
                          @click="onBlurGroupSelect"
                        >
                          确认
                        </v-btn>
                      </v-card-actions>
                    </v-card>
                  </v-dialog>
                </template> -->
                <div
                  v-if="
                    assetsDetails.groupsInfoList &&
                    assetsDetails.groupsInfoList.length > 0
                  "
                  class="text-overflow-hide w-100 text--primary text-ml"
                >
                  <!--                  {{ assetsDetails.groupNameList.join('，') }}-->
                  <span
                    class="mr-1 text--primary"
                    v-for="(tag, index) in assetsDetails.groupsInfoList"
                    :key="tag.id"
                  >
                    <v-badge
                      v-if="tag.assetGroupType === '0'"
                      dot
                      inline
                      offset-x="10"
                      :offset-y="-18"
                      color="success"
                      style="width: 12px; height: 12px; margin-right: 2px"
                    ></v-badge>
                    <span>{{ tag.name || tag.text }}</span>
                    <template
                      v-if="!(index == assetsDetails.groupsInfoList.length - 1)"
                    >
                      ,
                    </template>
                  </span>
                </div>
                <div v-else class="text--primary text-ml">N/A</div>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
      <!-- <v-col cols="12">
        <v-card class="px-4 pb-6 position-relative">
          <v-card-title
            v-if="carStatusMap[assetsDetails.healthStatus]"
            class="d-flex"
          >
            <div class="text-xxl">当前态势</div>
            <v-divider
              vertical
              class="ma-4 secondary"
              inset
              style="border-width: 2px"
            ></v-divider>
            <div
              class="text-uppercase text-5xl"
              :style="{
                color: `${carStatusMap[assetsDetails.healthStatus].color}`,
              }"
            >
              {{ carStatusMap[assetsDetails.healthStatus].text1 }}
            </div>
          </v-card-title>
          <v-card-text class="d-flex">
            <div class="d-flex align-center">
              <div class="text--primary text-xxl">告警状况</div>
              <v-divider
                vertical
                class="mx-4 my-4 secondary"
                inset
                style="border-width: 2px"
              ></v-divider>
              <div
                class="d-flex justify-center white-space-nowrap overflow-auto"
              >
                <div
                  v-if="assetsDetails.healthStatus"
                  class="w-100 d-flex align-center text-white text-base font-weight-semibold alert-list"
                  @click="goAlert"
                >
                  <div v-if="assetsDetails.assetAlarmInfoVos.length > 0">
                    <v-avatar
                      class="mr-2 icon-fb"
                      size="2.5rem"
                      v-for="(
                        alarmItem, index
                      ) in assetsDetails.assetAlarmInfoVos"
                      :key="index"
                      v-show-tips="
                        alarmLevel[alarmItem.alarmLevel] &&
                        alarmLevel[alarmItem.alarmLevel].text
                      "
                      :color="
                        alarmLevel[alarmItem.alarmLevel] &&
                        alarmLevel[alarmItem.alarmLevel].color
                      "
                    >
                      <span class="text-sm">{{ alarmItem.alarmCount }}</span>
                    </v-avatar>
                  </div>
                  <div v-else>
                    {{ '' | dataFilter }}
                  </div>
                </div>
              </div>
            </div>
            <div class="ml-16">
              <div>
                <v-chip tile label x-small color="primary" class="me-2 pa-1"
                  >No.</v-chip
                >
                <span>资产编号</span>
              </div>
              <div class="d-flex justify-start">
                <div class="w-100">
                  <div
                    v-show-tips="assetsDetails.id"
                    class="text-base text--primary cursor-pointer d-flex align-center"
                  >
                    <div>
                      {{ assetsDetails.id }}
                    </div>
                    <v-btn
                      v-copy="assetsDetails.id"
                      v-show-tips="'复制'"
                      icon
                      class="text--primary"
                    >
                      <vsoc-icon
                        type="fill"
                        size="middle"
                        icon="icon-fuzhi"
                      ></vsoc-icon>
                    </v-btn>
                  </div>
                </div>
              </div>
            </div>
          </v-card-text>

          <v-card-text>
            <v-row>
              <v-col cols="2">
                <div class="my-1">首次登记时间</div>
                <div class="text--primary">
                  {{ assetsDetails.regisDate | toDate }}
                </div>
              </v-col>
              <v-col cols="2">
                <div class="my-1">最后接收时间</div>
                <div class="text--primary">
                  {{ assetsDetails.lastReceiveDate | toDate }}
                </div>
              </v-col>
              <v-col cols="3">
                <div class="my-1">最后位置</div>
                <div v-show-tips="address" class="text--primary">
                  {{ address | dataFilter }}
                </div>
              </v-col>
              <v-col style="z-index: 9">
                <template>
                  <v-dialog
                    max-width="45rem"
                    v-model="isDialogVisible"
                    scrollable
                  >
                    <template #activator="{ on, attrs }">
                      <div>
                        <span class="mr-1">资产组</span>
                        <v-btn
                          x-small
                          icon
                          rounded
                          color="reverse"
                          width="1.5rem"
                          height="1.5rem"
                          v-on="on"
                          v-bind="attrs"
                          @click.stop="editGroupMember(assetsDetails)"
                        >
                          <v-icon size="1.25rem" class="material-icons-round">
                            mdi-plus-circle
                          </v-icon>
                        </v-btn>
                      </div>
                    </template>
                    <v-card>
                      <v-card-title>选择资产组</v-card-title>
                      <v-divider></v-divider>
                      <v-card-text>
                        <v-autocomplete
                          v-model="groupMember.selectGroups"
                          color="primary"
                          :items="groupMember.allGroups"
                          label="资产组"
                          multiple
                          outlined
                          hide-details
                          chips
                          :menu-props="{ offsetY: true }"
                          class="mt-5 mb-1"
                          item-text="text"
                          item-value="text"
                        >
                          <template v-slot:selection="{ item, index }">
                            <v-chip
                              label
                              color="primary"
                              close
                              @click:close="
                                groupMember.selectGroups.splice(index, 1)
                              "
                            >
                              <span>{{ item.text }}</span>
                            </v-chip>
                          </template>
                        </v-autocomplete>
                      </v-card-text>

                      <v-divider></v-divider>

                      <v-card-actions>
                        <v-spacer></v-spacer>
                        <v-btn
                          outlined
                          depressed
                          @click="isDialogVisible = false"
                        >
                          取消
                        </v-btn>
                        <v-btn
                          color="primary"
                          :loading="confirmLoading"
                          @click="onBlurGroupSelect"
                        >
                          确认
                        </v-btn>
                      </v-card-actions>
                    </v-card>
                  </v-dialog>
                </template>
                <div>
                  <span
                    class="mr-2 text--primary"
                    v-for="tag in assetsDetails.groupNameList"
                    :key="tag"
                  >
                    {{ tag }}
                  </span>
                </div>
              </v-col>
            </v-row>
          </v-card-text>
          <v-img
            contain
            class="car-model"
            :src="
              assetsDetails.modelUrl ||
              require('@/assets/images/pages/model-m-4x.png') ||
              require('@/assets/images/pages/model-m.png')
            "
          />
        </v-card>
      </v-col> -->
      <!-- <v-col cols="12">
        <v-sheet max-width="100%">
          <v-slide-group show-arrows>
            <v-col cols="12" md="3">
              <v-slide-item v-for="n in 6" :key="n">
                <v-card
                  class="d-flex align-center mr-3"
                  dark
                  height="200"
                  width="400"
                >
                  <v-scroll-y-transition>
                    <div class="text-h2 flex-grow-1 text-center">Active</div>
                  </v-scroll-y-transition>
                </v-card>
              </v-slide-item>
            </v-col>
          </v-slide-group>
        </v-sheet>
      </v-col> -->
      <v-col v-if="unitItems.length" cols="12" class="py-0">
        <div
          class="d-flex align-center"
          :style="{ margin: unitItems.length > 4 ? '0' : '0 -12px' }"
          v-has:asset-detail-submodule-btn
        >
          <v-btn
            v-if="unitItems.length > 4"
            elevation="0"
            class="arrow-btn primary--text bg-btn"
            @click="preArrow"
            :disabled="!showLeftIcon"
          >
            <vsoc-icon
              type="fill"
              size="x-large"
              icon="icon-xiajiantou"
              style="transform: rotate(90deg)"
            ></vsoc-icon>
          </v-btn>
          <div class="w-100 overflow-hidden" id="adv-pad-scroll">
            <div
              class="w-100 d-flex align-center adv-pad"
              style="transition: 0.3s cubic-bezier(0.25, 0.8, 0.5, 1)"
            >
              <v-col
                cols="12"
                :md="unitItems.length > 4 ? '3' : 12 / unitItems.length"
                v-for="(unit, index) in unitItems"
                :key="index"
                class="adv-pad-item py-2"
              >
                <v-card
                  ripple
                  class="bg-card d-flex align-center cursor-pointer"
                  style="z-index: 1; padding: 18px 16px !important"
                  @click="goChildren(unit)"
                >
                  <v-avatar class="rounded-pill py-5 primary--text opacity-b1">
                    <vsoc-icon
                      class="primary--text"
                      size="x-large"
                      type="fill"
                      :icon="unit.deviceIcon"
                    ></vsoc-icon>
                  </v-avatar>

                  <v-spacer></v-spacer>
                  <div class="d-flex align-center">
                    <v-badge
                      dot
                      inline
                      offset-x="10"
                      :offset-y="-18"
                      :color="
                        carStatusMap[unit.healthStatus]
                          ? carStatusMap[unit.healthStatus].color
                          : '#8F9AB2'
                      "
                      size="1.3333333rem"
                    ></v-badge>
                    <div
                      class="ml-3 text--primary text-xxl font-weight-semibold"
                    >
                      {{ unit.deviceName }}
                    </div>
                    <v-icon class="ml-6" size="1.7rem" color="secondary"
                      >mdi-chevron-right</v-icon
                    >
                  </div>
                </v-card>
              </v-col>
            </div>
          </div>
          <v-btn
            v-if="unitItems.length > 4"
            elevation="0"
            :disabled="!showRightIcon"
            class="arrow-btn primary--text bg-btn"
            @click="nextArrow"
          >
            <vsoc-icon
              type="fill"
              size="x-large"
              icon="icon-shangjiantou"
              style="transform: rotate(90deg)"
            ></vsoc-icon>
          </v-btn>
        </div>
      </v-col>

      <v-col cols="12">
        <v-card class="pb-1">
          <v-card-title>
            <div>
              {{ $t('asset.assetEventAxis') }}
              <v-tooltip
                right
                transition="slide-x-transition"
                max-width="40rem"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-icon :color="$bodyColor" v-bind="attrs" v-on="on">
                    mdi-information
                  </v-icon>
                </template>
                <span>Based on asset sent timestamps</span>
              </v-tooltip>
            </div>
            <v-spacer></v-spacer>
            <v-menu offset-y z-index="9999">
              <template v-slot:activator="{ on, attrs }">
                <v-btn
                  x-small
                  elevation="0"
                  class="bg-body"
                  v-bind="attrs"
                  v-on="on"
                >
                  <div class="d-flex align-center">
                    <span class="primary--text font-weight-medium">{{
                      eventFilterOption[query.statisticsMethod].text
                    }}</span>
                    <v-icon size="1.5rem" color="primary">
                      mdi-chevron-down
                    </v-icon>
                  </div>
                </v-btn>
              </template>
              <v-list-item-group
                v-model="query.statisticsMethod"
                active-class="primary--text"
                class="bg-body"
                @change="changeStatistics"
              >
                <v-list-item
                  v-for="(item, index) in eventFilterOption"
                  :key="index"
                  :value="item.value"
                >
                  <v-list-item-title class="text-capitalize">{{
                    item.text
                  }}</v-list-item-title>
                </v-list-item>
              </v-list-item-group>
            </v-menu>
            <!-- <vsoc-date-range
              class="mr-2"
              :value="{ start: query.startDate, end: query.endDate }"
              :presetFn="presetFn"
              :actionLabels="{ reset: $t('action.reset') }"
              @input="onDateChange"
            >
              <template v-slot:text="{ inputValue }">
                <v-btn x-small elevation="0" class="bg-body primary--text">
                  <span class="mr-2">{{
                    inputValue || $t('enums.datePresets.all')
                  }}</span>
                  <v-icon>mdi-calendar-range-outline</v-icon>
                </v-btn>
              </template>
            </vsoc-date-range> -->

            <v-combobox
              v-if="['1', '2'].includes(query.statisticsMethod)"
              v-model="query.alarmStatusList"
              dense
              hide-details
              multiple
              append-icon="mdi-chevron-down"
              class="date-combox bg-body ml-3"
              :items="alertStatusList"
              item-text="text"
              item-value="value"
              :menu-props="{ offsetY: true }"
              @blur="onReload"
              :return-object="false"
              :label="`${$t('analytics.alertStatus')}：ALL`"
              solo
            >
              <template v-slot:selection="{ index }">
                <span v-if="index === 0" class="check-text">
                  {{ $t('analytics.alertStatus') }}：{{
                    query.alarmStatusList.length
                  }}
                </span>
              </template>
            </v-combobox>

            <!-- <v-menu
              v-if="['1', '2'].includes(query.statisticsMethod)"
              offset-y
              z-index="9999"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-btn
                  x-small
                  elevation="0"
                  class="bg-body ml-3"
                  v-bind="attrs"
                  v-on="on"
                >
                  <div class="d-flex align-center">
                    <span class="primary--text font-weight-medium">
                      {{ $t('global.selected')
                      }}{{ query.alarmStatusList.length }}</span
                    >
                    <v-icon size="1.5rem" color="primary">
                      mdi-chevron-down
                    </v-icon>
                  </div>
                </v-btn>
              </template>
              <v-list-item-group
                v-model="query.alarmStatusList"
                active-class="primary--text"
                class="bg-body"
                @change="onReload"
                multiple
              >
                <v-list-item
                  v-for="(item, index) in alertStatusList"
                  :key="index"
                  :value="item.value"
                >
                  <v-list-item-title>
                    <div class="d-flex">
                      <v-badge
                        dot
                        inline
                        offset-x="10"
                        :offset-y="-18"
                        :color="alertStatus[item.value].color"
                        class="mr-1"
                        style="margin-top: 2px"
                      ></v-badge>
                      <span class="text-capitalize">{{ item.text }}</span>
                    </div>
                  </v-list-item-title>
                </v-list-item>
              </v-list-item-group>
            </v-menu> -->

            <v-menu offset-y z-index="9999">
              <template v-slot:activator="{ on, attrs }">
                <v-btn
                  x-small
                  elevation="0"
                  class="bg-body ml-3"
                  v-bind="attrs"
                  v-on="on"
                >
                  <div class="d-flex align-center">
                    <span
                      class="primary--text font-weight-medium"
                      v-if="presetFn()[query.rangeValue]"
                      >{{ presetFn()[query.rangeValue].label }}</span
                    >
                    <v-icon size="1.5rem" color="primary">
                      mdi-chevron-down
                    </v-icon>
                  </div>
                </v-btn>
              </template>
              <v-list-item-group
                v-model="query.rangeValue"
                active-class="primary--text"
                class="bg-body"
                @change="onReload"
              >
                <v-list-item
                  v-for="(item, index) in presetFn()"
                  :key="index"
                  :value="item.value"
                >
                  <v-list-item-title>{{ item.label }}</v-list-item-title>
                </v-list-item>
              </v-list-item-group>
            </v-menu>
            <v-btn
              elevation="0"
              class="bg-body ml-3"
              x-small
              v-show-tips="$t('action.refresh')"
              @click="onReload"
              :loading="isLoading"
            >
              <vsoc-icon
                size="1.5rem"
                class="primary--text"
                type="fill"
                icon="icon-shuaxin"
              ></vsoc-icon>
            </v-btn>
            <v-menu offset-y z-index="9999">
              <template v-slot:activator="{ on, attrs }">
                <v-btn
                  x-small
                  elevation="0"
                  class="bg-body ml-3"
                  v-bind="attrs"
                  v-on="on"
                  v-show-tips="$t('asset.setRefresh')"
                >
                  <div class="d-flex align-center">
                    <span class="primary--text font-weight-medium">{{
                      refreshOption.find(v => v.value === autoRefresh).text
                    }}</span>
                    <v-icon size="1.5rem" color="primary">
                      mdi-chevron-down
                    </v-icon>
                  </div>
                </v-btn>
              </template>
              <v-list-item-group
                v-model="autoRefresh"
                @change="onAutoRefresh(autoRefresh)"
                class="bg-body"
                mandatory
                active-class="primary--text"
              >
                <v-list-item
                  v-for="(item, index) in refreshOption"
                  :key="index"
                  :value="item.value"
                >
                  <v-list-item-title>{{ item.text }}</v-list-item-title>
                </v-list-item>
              </v-list-item-group>
            </v-menu>

            <v-btn
              elevation="0"
              x-small
              v-show-tips="$t('asset.investigationAdvance')"
              @click.stop="goAdvance"
              class="bg-body ml-3"
            >
              <vsoc-icon
                icon="icon-gaojitiaocha"
                type="fill"
                class="primary--text"
              ></vsoc-icon>
              <!-- <v-icon size="1.5rem" color="primary">
                  mdi-cloud-search-outline
                </v-icon> -->
            </v-btn>
          </v-card-title>

          <div class="mt-4">
            <asset-time-line
              :time-line-data="timeLineData"
              time-line-type="last-dash-arrow"
              :is-no-data="timeLineNoData"
              @change="timeLineChange"
            ></asset-time-line>
          </div>
          <!-- 事件简要名称 -->
          <asset-event-detail
            class="ma-3"
            v-if="!timeLineNoData"
            :data="currentLineData"
            :vin="assetsDetails.vin"
            :lastReceiveDate="assetsDetails.lastReceiveDate"
            :assetType="assetsDetails.assetType"
          ></asset-event-detail>
        </v-card>
      </v-col>
      <v-col cols="12" v-if="mapEnabledStatus === 'true'">
        <template v-if="!timeLineNoData">
          <!-- 最近位置 -->
          <asset-map
            ref="map"
            :local-list="localList"
            :zoom="[4, 12]"
          ></asset-map>
        </template>
      </v-col>
    </v-row>

    <vsoc-drawer
      :value="groupMember.showEditGroup"
      :title="$t('asset.selectAssetGroup')"
      @click:confirm="onBlurGroupSelect"
      @close="groupMember.showEditGroup = false"
    >
      <template>
        <vsoc-autocomplete
          v-model="groupMember.selectGroups"
          color="primary"
          :items="groupMember.allGroups"
          :label="$t('asset.headers.assetGroup')"
          multiple
          outlined
          hide-details
          chips
          :menu-props="{ offsetY: true }"
          class="mt-5 mb-1"
          item-text="text"
          item-value="text"
        >
        </vsoc-autocomplete>
      </template>
    </vsoc-drawer>
  </div>
</template>

<script>
import { getIdpsStatistics } from '@/api/analytics'
import {
  addRelationByAssetId,
  delDigitalTwin,
  getVehicleDetails,
  getVin,
} from '@/api/asset/index'
import VsocDrawer from '@/components/VsocDrawer.vue'
import VsocDateRange from '@/components/vsoc-date-range/VsocDateRange.vue'
import { generateEventName, toDate } from '@/util/filters'
// import AssetChart from '../asset-chart/AssetChart.vue'
import VsocAutocomplete from '@/components/vsoc-autocomplete/vsoc-autocomplete.vue'
import { idpsStatus } from '@/util/enum'
import { format, subDays, subHours } from 'date-fns'
import { cloneDeep, sortBy } from 'lodash'
import AssetEventDetail from '../asset-info/components/AssetEventDetail.vue'
import AssetTimeLine from '../asset-info/components/AssetTimeLine.vue'
export default {
  name: 'AssetDetail',
  components: {
    VsocAutocomplete,
    VsocDrawer,
    AssetTimeLine,
    AssetEventDetail,
    VsocDateRange,
  },
  data() {
    return {
      maxClickNum: 0, // 最大点击次数
      lastLeft: 0, // 上次滑动距离
      clickNum: 0, // 点击次数
      showRightIcon: true,
      showLeftIcon: false,
      isDecipherVin: false,
      query: {
        statisticsMethod: '0',
        rangeValue: 3,
        startDate: format(subHours(new Date(), 24), 'yyyy-MM-dd HH:mm:ss'),
        endDate: format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
        alarmStatusList: ['0', '1', '2'],
      },
      timerId: null,
      autoRefresh: 0,
      // refreshOption: [
      //   {
      //     text: 'OFF',
      //     value: 0,
      //   },
      //   {
      //     text: '1s',
      //     value: 1,
      //   },
      //   {
      //     text: '3s',
      //     value: 3,
      //   },
      //   {
      //     text: '5s',
      //     value: 5,
      //   },
      //   {
      //     text: '15s',
      //     value: 15,
      //   },
      //   {
      //     text: '30s',
      //     value: 30,
      //   },
      // ],
      refreshOption: [
        {
          text: 'OFF',
          value: 0,
        },
        {
          text: '15s',
          value: 15,
        },
        {
          text: '30s',
          value: 30,
        },
        {
          text: '60s',
          value: 60,
        },
        {
          text: '120s',
          value: 120,
        },
      ],
      eventFilterOption: [
        {
          text: 'Only Events',
          value: '0',
        },
        {
          text: 'Only alerts',
          value: '1',
        },
        {
          text: 'Events and alerts',
          value: '2',
        },
      ],
      assetsDetails: {
        alarmInfo: {},
        vehicleGroups: [],
      },
      refreshDate: '',
      isRefreshError: false,
      confirmLoading: false,
      isDialogVisible: false,
      // TODO 临时使用
      assetsGroups: [],

      groupMember: {
        showEditGroup: false,
        allGroups: [],
        groupsMap: {},
        selectGroups: [],
      },
      timeLineData: null,
      currentLineData: {
        fullDigitalTwin: [],
      },
      localList: [],
      timeLineNoData: false,
      address: '',
      isLoading: false,
      unitItems: [],
    }
  },
  filters: {
    filterText(value) {
      return this.refreshOption.find(v => v.value === value)?.text
    },
  },
  computed: {
    statusEnum() {
      return idpsStatus
      // return {
      //   normal: {
      //     text: '运行',
      //     value: 'normal',
      //     color: 'primary',
      //     icon: 'mdi-check-circle',
      //   },
      //   abnormal: {
      //     text: '停止',
      //     value: 'abnormal',
      //     color: 'error',
      //     icon: 'mdi-close-circle',
      //   },
      //   error: {
      //     text: '规则错误',
      //     value: 'error',
      //     color: 'error',
      //     icon: 'mdi-close-circle',
      //   },
      //   other: {
      //     text: '其他',
      //     value: 'other',
      //     color: 'info',
      //     icon: 'mdi-check-circle',
      //   },
      // }
    },
    // unitItems() {
    //   return [
    //     {
    //       deviceName: 'T-BOX',
    //       status: '0',
    //       deviceIcon: 'icon-T-Box',
    //     },
    //     {
    //       deviceName: 'IVI',
    //       status: '0',
    //       deviceIcon: 'icon-IVI',
    //     },
    //     {
    //       deviceName: '网关',
    //       status: '0',
    //       deviceIcon: 'icon-wangguan',
    //     },
    //     {
    //       deviceName: '域控-101',
    //       status: '1',
    //       deviceIcon: 'icon-yukong',
    //     },
    //   ]
    // },
    mapEnabledStatus() {
      return this.$store.getters['enums/getMapEnabledStatus']
    },
    carStatusMap() {
      return this.$store.state.enums.enums.HealthStatus
    },
    addressInfo() {
      return this.address
    },
    alarmLevel() {
      return this.$store.state.enums.enums.AlarmLevel
    },
    breadItems() {
      const routerList = this.$router.getRoutes()
      let first = routerList.find(
        v => v.name === this.$route.meta.navActiveLink,
      )
      return [
        {
          text: this.$generateMenuTitle(first.meta),
          disabled: true,
          href: '#',
        },
        {
          text: this.$generateMenuTitle(this.$route.meta),
          disabled: false,
          href: '#',
        },
      ]
    },

    alertStatusList() {
      return Object.assign([], this.$store.getters['enums/getAlertStatus'])
    },
    alertStatus() {
      return this.$store.getters['enums/getAlertStatus']
    },
  },
  watch: {
    assetsDetails: {
      handler(newVal) {
        if (!newVal) return
        this.getTimeLine()
        // this.getVehicleGroups()
        // if (newVal.latitude && newVal.longitude) {
        //   this.getLocaltion(newVal).then(e => {
        //     this.address = e || ''
        //   })
        // }
      },
      immediate: true,
    },
  },

  created() {
    this.init()
  },
  beforeDestroy() {
    clearInterval(this.timerId)
  },

  methods: {
    changeStatistics() {
      // this.query.alarmStatusList = ['0', '1', '2', '5']
      this.onReload()
    },
    activeBox(item, attrs) {
      this.groupMember.selectGroups.push(item.text)
    },
    preArrow() {
      if (this.clickNum > 0) {
        // console.log(document.querySelectorAll('.adv-pad-item'))
        let width =
          document.querySelectorAll('.adv-pad-item')[this.clickNum - 1]
            .offsetWidth
        // console.log(document.getElementsByClassName('adv-pad'))
        document.getElementsByClassName('adv-pad')[0].style.marginLeft = `${
          this.lastLeft + width
        }px`
        this.lastLeft = width + this.lastLeft
        this.clickNum = this.clickNum - 1
        if (this.clickNum < this.maxClickNum) {
          this.showRightIcon = true
        }
        if (this.lastLeft === 0) {
          this.showLeftIcon = false
        }
      }
    },
    nextArrow() {
      if (this.clickNum < this.unitItems.length - 1) {
        let width =
          document.querySelectorAll('.adv-pad-item')[this.clickNum].offsetWidth
        let lastItemOffsetLeft =
          document.getElementsByClassName('adv-pad-item')[
            this.unitItems.length - 1
          ].offsetLeft
        const lookWidth = document.getElementById('adv-pad-scroll').clientWidth

        if (lastItemOffsetLeft > lookWidth) {
          document.getElementsByClassName('adv-pad')[0].style.marginLeft = `${
            -width + this.lastLeft
          }px`
          this.lastLeft = -width + this.lastLeft
          this.clickNum += 1
          this.maxClickNum = this.clickNum
        }
        this.showLeftIcon = true
        this.showRightIcon = lastItemOffsetLeft > lookWidth + width
      }
    },
    async onVin() {
      try {
        const { data } = await getVin({ vin: this.assetsDetails.vin })
        this.assetsDetails.vin = data
        this.isDecipherVin = !this.isDecipherVin
      } catch (err) {
        console.log('vin报错', err)
      }
    },
    onDel() {
      this.$swal({
        title: this.$t('asset.swal.title'),
        text: this.$t('asset.swal.text', { id: this.assetsDetails.id }),
        icon: 'warning',
        reverseButtons: true,
        showCancelButton: true,
        confirmButtonText: this.$t('action.confirm'),
        cancelButtonText: this.$t('action.cancel'),
        customClass: {
          confirmButton: 'sweet-btn-primary',
        },
      }).then(async result => {
        if (result.isConfirmed) {
          await delDigitalTwin({ id: this.assetsDetails.id })
          this.$notify.info('success', this.$t('global.hint.del', ['']))
          this.$router.go(-1)
        } else {
          this.$notify.info(
            'warning',
            this.$t('asset.swal.cancel', { id: this.assetsDetails.id }),
          )
        }
      })
    },
    presetFn() {
      const newDate = new Date()
      return [
        {
          label: this.$t('enums.datePresets.last1'),
          value: 0,
          range: [subHours(newDate, 1), newDate],
        },
        {
          label: this.$tc('enums.datePresets.lastxh', 6, { num: 6 }),
          value: 1,
          range: [subHours(newDate, 6), newDate],
        },
        {
          label: this.$tc('enums.datePresets.lastxh', 12, { num: 12 }),
          value: 2,
          range: [subHours(newDate, 12), newDate],
        },
        {
          label: this.$tc('enums.datePresets.lastxh', 24, { num: 24 }),
          value: 3,
          range: [subHours(newDate, 24), newDate],
        },
        {
          label: this.$tc('enums.datePresets.lastxd', 3, { num: 3 }),
          value: 4,
          range: [subDays(newDate, 2), newDate],
        },
        {
          label: this.$tc('enums.datePresets.lastxd', 7, { num: 7 }),
          value: 5,
          range: [subDays(newDate, 6), newDate],
        },
        {
          label: this.$tc('enums.datePresets.lastxd', 15, { num: 15 }),
          value: 6,
          range: [subDays(newDate, 14), newDate],
        },
        {
          label: this.$tc('enums.datePresets.lastxd', 30, { num: 30 }),
          value: 7,
          range: [subDays(newDate, 29), newDate],
        },
        {
          label: this.$tc('enums.datePresets.lastxd', 60, { num: 60 }),
          value: 8,
          range: [subDays(newDate, 59), newDate],
        },
        {
          label: this.$tc('enums.datePresets.lastxd', 90, { num: 90 }),
          value: 9,
          range: [subDays(newDate, 89), newDate],
        },
        {
          label: this.$t('enums.datePresets.all'),
          value: 10,
          range: [null, null],
        },
      ]
    },
    onDateChange(range) {
      this.query.startDate = range.start
      this.query.endDate = range.end
      this.onReload()
    },
    goChildren(item) {
      // let nextRoute = this.$router.getRoutes().find(v => v.name === item.text)
      // if (!nextRoute) {
      //   const route = {
      //     path: `/asset/detail/:parentId/:text`,
      //     name: item.text,
      //     component: () =>
      //       import('@/views/asset/asset-detail/children-detail/Index.vue'),
      //     meta: {
      //       layout: 'content',
      //       navActiveLink: this.$route.meta.navActiveLink,
      //     },
      //   }
      //   this.$router.addRoute(route)
      // }
      this.$router.push({
        path: `/asset/submodule`,
        query: { id: this.$route.query.id, deviceType: item.deviceType },
      })
    },
    refresh(mode = 'init') {
      this.$nextTick(() => {
        this.init(mode)
      })
    },
    init(mode) {
      this.refreshDate = new Date().getTime()
      this.getVehicleAssetDetail(mode)
    },

    async getVehicleAssetDetail(mode) {
      if (mode !== 'refresh') {
        this.$showLoading()
      }
      this.isLoading = true
      try {
        const query = {
          id: this.$route.query.id,
        }
        const currentRangeList = this.presetFn()[this.query.rangeValue].range
        this.query.startDate =
          currentRangeList[0] &&
          format(currentRangeList[0], 'yyyy-MM-dd HH:mm:ss')
        this.query.endDate =
          currentRangeList[1] &&
          format(currentRangeList[1], 'yyyy-MM-dd HH:mm:ss')
        Object.assign(query, this.query)
        let res = await getVehicleDetails(query)
        let data = res.data
        data.groupNameList = data.groupsInfoList.map(v => v.name)
        this.assetsDetails = data
        console.log('this.assetsDetails', data)
        // 升序
        this.assetsDetails.assetAlarmInfoVos = sortBy(
          this.assetsDetails.assetAlarmInfoVos,
          'alarmLevel',
        )
        this.unitItems = data.deviceTypeInfos || []
        if (this.unitItems.length) {
          this.fetchIdps()
        }
      } catch (e) {
        console.error('获取资产详情：', e)
      } finally {
        this.isLoading = false
        if (mode !== 'refresh') {
          this.$hideLoading()
        }
      }
    },
    async fetchIdps() {
      this.unitItems.forEach(async v => {
        const params = {
          vehicleId: this.$route.query.id,
          deviceType: v.deviceType,
        }
        const res = await getIdpsStatistics(params)
        if (res.code === 200) {
          this.$set(v, 'healthStatus', res.data.deviceTypeInfo.healthStatus)
        }
      })
    },
    changeGroups(e) {
      this.assetsDetails.vehicleGroups = e
    },

    async onBlurGroupSelect(cb) {
      // if (bool) {
      //   return
      // }

      try {
        let groupIds = this.groupMember.allGroups
          .filter(v => this.groupMember.selectGroups.includes(v.text))
          .map(t => t.value)
        let params = {
          assetId: this.assetsDetails.id,
          groupIds,
        }
        const res = await addRelationByAssetId(params)
        if (res.code === 200) {
          const selectedObjects = this.groupMember.allGroups.filter(v =>
            this.groupMember.selectGroups.includes(v.text),
          )
          this.assetsDetails.groupsInfoList = selectedObjects
          this.assetsDetails.groupNameList = this.groupMember.allGroups
            .filter(v => this.groupMember.selectGroups.includes(v.text))
            .map(t => t.text)
          this.groupMember.showEditGroup = false
          this.confirmLoading = false
        } else {
          this.$notify.info('error', res.msg)
        }
        cb()
      } catch (e) {
        console.error(`修改资产组失败：${e}`)
        cb(true, false)
      }
    },

    onReload() {
      this.getTimeLine()
      this.getVehicleGroups()
      // this.$emit('refresh', 'refresh')
      this.refresh('refresh')
    },
    onAutoRefresh(value) {
      if (value === 0) {
        this.$notify.info('info', this.$t('asset.closeAutoRefresh'))
        clearInterval(this.timerId)
        return
      }
      clearInterval(this.timerId)
      this.timerId = setInterval(() => {
        this.onReload()
      }, value * 1000)
    },

    clearChip(index) {
      this.groupMember.selectGroups.splice(index, 1)
    },

    async $_confirmGroupMember(callBack) {
      try {
        const res = await updateVehicleGroupMember({
          groupIds: this.groupMember.selectGroups,
          vehicleId: this.sn,
        })
        if (res.status === 200) {
          callBack()
          this.$notify.info('success', this.$t('global.hint.edit'))
          // this.$emit(
          //   'change-groups',
          //   Object.assign([], this.groupMember.selectGroups),
          // )
          this.changeGroups(Object.assign([], this.groupMember.selectGroups))
        }
      } catch (e) {
        callBack(false, true)
        console.error(`修改资产组失败：${e}`)
      }
    },

    /*   async getVehicleGroups() {
                        try {
                            this.groupMember.allGroups = []
                            // TODO 需要查询所有 前端需要考虑 做滚动加载调用的逻辑
                            const res = await searchAllVehicleGroup()
                            this.groupMember.allGroups = res.data
                            this.groupMember.allGroups.forEach(v => {
                                this.groupMember.groupsMap[v.id] = v.name
                            })
                        } catch (e) {
                            console.error(`获取资产组列表错误：${e}`)
                        }
                    }, */

    // async getVehicleGroups() {
    //   try {
    //     this.groupMember.allGroups = []

    //     // TODO 需要查询所有 前端需要考虑 做滚动加载调用的逻辑
    //     // const data = await searchAllVehicleGroupDt()
    //     const data = await this.$store.dispatch(
    //       'global/searchGroups',
    //       this.assetsDetails.assetType,
    //     )
    //     this.groupMember.allGroups = data
    //     this.groupMember.allGroups.forEach(v => {
    //       this.groupMember.groupsMap[v.id] = v.name
    //     })
    //   } catch (e) {
    //     this.isRefreshError = true
    //     console.error(`获取资产组列表错误：${e}`)
    //   }
    // },

    // editGroupMember() {
    //   this.groupMember.selectGroups = Object.assign(
    //     [],
    //     this.assetsDetails.groupNameList,
    //   )
    //   this.groupMember.showEditGroup = true
    // },

    /*
                    async getTimeLine() {
                        try {
                            const res = await getTimeLine(this.sn)
                            res.data.forEach(item => {
                                item.latitude = ''
                                item.longitude = ''
                                item.fullDigitalTwin ? '' : item.fullDigitalTwin = []
                                if (item.fullDigitalTwin && item.fullDigitalTwin.length) {
                                    item.fullDigitalTwin.forEach(v => {
                                        if (v.name === 'vehicle_location_currentlocation_latitude') {
                                            item.latitude = v.value
                                        }
                                        if (v.name === 'vehicle_location_currentlocation_longitude') {
                                            item.longitude = v.value
                                        }
                                    })
                                }
                            })
                            Promise.all(res.data.map(item => this.getLocaltion(item))).then(() => {
                                this.timeLineData = res.data
                                this.timeLineNoData = !res.data.length
                                this.currentLineData = this.timeLineData[this.timeLineData.length - 1]
                                this.handleMapData();
                            })

                        } catch (e) {
                            console.error('资产时间线：', e)
                        }
                    },
            */

    async getTimeLine() {
      try {
        if (!this.assetsDetails.eventAxisVos) {
          return
        }
        const data = cloneDeep(this.assetsDetails.eventAxisVos)
        data.forEach(item => {
          // item.latitude = ''
          // item.longitude = ''
          item.fullDigitalTwin ? '' : (item.fullDigitalTwin = [])
          if (item.fullDigitalTwin && item.fullDigitalTwin.length) {
            item.fullDigitalTwin.forEach(v => {
              if (v.id === 'vehicle_location_currentlocation_latitude') {
                item.latitude = v.value
              }
              if (v.id === 'vehicle_location_currentlocation_longitude') {
                item.longitude = v.value
              }
            })
          }
        })
        this.timeLineData = data.map(item => {
          return {
            ...item,
            color: item.alarmLevel && this.alarmLevel[item.alarmLevel].color,
          }
        })
        this.timeLineNoData = !data.length
        this.currentLineData = this.timeLineData[this.timeLineData.length - 1]
        // this.handleMapData()
        // this.currentLineData.localList = await this.getLocaltion(
        //   this.timeLineData[this.timeLineData.length - 1],
        // )
        // Promise.all(data.map(item => this.getLocaltion(item))).then(res => {
        //   this.timeLineData = data
        //   this.timeLineNoData = !data.length
        //   this.currentLineData = this.timeLineData[this.timeLineData.length - 1]
        // })
      } catch (e) {
        this.isRefreshError = true
        console.error('资产时间线：', e)
      }
    },

    goAdvance() {
      const query = {
        alertId: this.assetsDetails.alertId,
        vehicleId: this.assetsDetails.id,
        assetType: this.assetsDetails.assetType,
        searchDateTime: this.assetsDetails.lastReceiveDate,
      }
      let fullDigitalTwin = this.timeLineData.find(
        v => v.type === 'RecentActivity',
      )?.fullDigitalTwin
      if (fullDigitalTwin && fullDigitalTwin.length) {
        query.fullDigitalTwin = JSON.stringify(fullDigitalTwin)
      }
      if (
        this.$router.getRoutes().findIndex(v => v.path === '/investigate') !==
        -1
      ) {
        this.$router.push({
          path: '/investigate',
          query: query,
        })
      } else {
        this.$notify.info('error', this.$t('asset.noGrant'))
      }
    },

    timeLineChange(i) {
      this.currentLineData = this.timeLineData[i]
      this.$refs.map &&
        this.$refs.map.flyTo(
          this.currentLineData.longitude,
          this.currentLineData.latitude,
        )
    },

    // 点击告警状态
    goAlert() {
      sessionStorage.setItem('isRefreshed', 'Asset')
      const alarmLevelList = this.assetsDetails.assetAlarmInfoVos
        .filter(v => v.alarmCount > 0)
        .map(t => t.alarmLevel)
      if (alarmLevelList.length === 0) {
        return
      }
      this.$router.push({
        path: '/alerts',
        query: {
          isQuery: 1,
          vehicleId: this.assetsDetails.id,
          alarmLevelList: JSON.stringify(alarmLevelList),
          statusList: JSON.stringify(['0', '1']),
        },
      })
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .date-combox {
  // background: #f5f6fa !important;
  border-radius: 5px !important;
  height: 26px !important;
  margin-top: 0;
  max-width: 140px !important;
}
::v-deep .date-combox.v-text-field > .v-input__control > .v-input__slot:before {
  border-color: transparent !important;
}

::v-deep
  .date-combox.v-text-field:not(.v-input--has-state):hover
  > .v-input__control
  > .v-input__slot:before {
  border-color: transparent !important;
}

::v-deep
  .date-combox.v-text-field.v-text-field--solo:not(.v-text-field--solo-flat)
  > .v-input__control
  > .v-input__slot {
  box-shadow: none !important;
  background-color: transparent !important;
  min-height: 26px !important;
}

::v-deep .date-combox.v-text-field > .v-input__control > .v-input__slot:after {
  border-color: transparent !important;
  background-color: transparent !important;
}

.date-combox .check-text {
  font-size: 12px !important;
  color: $primary !important;
  // padding-left: 9px;
}
::v-deep .date-combox .v-label {
  // top: 14px !important;
  font-size: 12px !important;
  color: var(--v-primary-base) !important;
  // padding-left: 13px;
  font-weight: 500 !important;
}
::v-deep .date-combox .v-icon {
  color: $primary !important;
  position: absolute;
  bottom: 0;
  top: 0;
}

::v-deep .date-combox .v-select__selections input {
  color: $primary !important;
  height: 0px !important;
  padding: 0px !important;
}

.alert-handle-btn {
  width: 34px !important;
  height: 34px !important;
}
::v-deep .v-application--is-ltr .v-list-item__action:first-child {
  margin-right: 0 !important;
}
::v-deep .v-input--selection-controls__ripple:before {
  opacity: 0 !important;
}

::v-deep .v-list-item__title > .v-badge {
  margin-top: 0;
}

::v-deep .v-list .v-list-item--active input,
::v-deep .v-input--selection-controls__input input {
  display: none;
}

::v-deep .v-input--selection-controls__input {
  display: flex;
  justify-content: center;
}
.asset-detail-box {
  ::v-deep .v-badge--dot .v-badge__badge {
    top: -2px !important;
  }
}
.car-model {
  position: absolute;
  top: 15%;
  right: 0;
  // z-index: 1;
  max-width: 65vh;

  // padding: 0 4rem 0 0;
  // max-width: 550px;
  // min-width: 300px;
  margin: auto;
  // opacity: 0.8;
}
.adv-pad-item {
  // padding: 6px 12px !important;
}
.arrow-btn {
  width: 34px !important;
  height: 34px !important;
  padding: 0 !important;
}
.alert-list ::v-deep.v-avatar > span {
  text-overflow: initial !important;
  overflow: initial !important;
}
.bg {
  background: url(../../../assets/images/svg/waves-white.svg) center no-repeat;
}
</style>
