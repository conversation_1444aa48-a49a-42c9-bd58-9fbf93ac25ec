const alertAction = {
  currentTitle: 'Alert Action',
  headers: {
    name: 'Action name',
    disposeTypeName: 'Action type',
    version: 'Version',
    severity: 'Severity',
    severityList: 'Triggered by severity',
    active: 'Activity',
    warnClassifyTypeName: 'Triggered by alert type',
    receiveUser: 'Recipients',
    noticeContent: 'Notification content',
  },
  detail: {
    link: 'Alarm Details Link',
  },
  btn: {
    add: 'Add Alert Action',
    edit: 'Edit Alert Action',
    detail: 'Alert Action Details',
    alert: 'Alert parameters',
    app: 'Vehicle parameters',
  },
  swal: {
    del: {
      title: 'Delete Alert Action',
      text: 'After deletion, all external integrations based on this disposal process will be terminated',
      tip: 'Enabled response tasks are not allowed to be deleted!',
    },
  },
  hint: {
    require: '{0} is required!',
  },
}
export default alertAction
