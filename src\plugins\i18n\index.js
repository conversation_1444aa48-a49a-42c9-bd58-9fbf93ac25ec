import enMessages from '@/plugins/i18n/locales/en/index'
import messages from '@/plugins/i18n/locales/zh-CN/index'
import router from '@/router'
import { getLocalStorage, setLocalStorage } from '@/util/localStorage'
import themeConfig from '@themeConfig'
import { findKey } from 'lodash'
import Vue from 'vue'
import VueI18n from 'vue-i18n'
import action from './locales/zh-CN/module/action'
Vue.use(VueI18n)

export const i18n = new VueI18n({
  locale: getLocalStorage('LOCALE') || 'zh-CN', // set locale
  fallbackLocale: 'zh-CN', // 备选语言环境
  silentTranslationWarn: true, // 去掉控制台警告
  messages: {
    'zh-CN': messages,
    en: enMessages,
  }, // set locale messages
})

const loadedLanguages = ['en', 'zh-CN'] // our default language that is preloaded

function setI18nLanguage(lang) {
  i18n.locale = lang
  // localStorage.setItem('LOCALE', lang)
  setLocalStorage('LOCALE', lang)
  const { title, enTitle } = router.currentRoute.meta
  if (title && enTitle) {
    document.title = `${themeConfig.app.name}-${generateName(title, enTitle)}`
  } else {
    document.title = i18n.t('projectName')
  }

  window.document.documentElement.setAttribute('lang', lang)
  return lang
}

export function loadLanguageAsync(lang) {
  // If the same language
  if (i18n.locale === lang) {
    return Promise.resolve(setI18nLanguage(lang))
  }

  // If the language was already loaded
  if (loadedLanguages.includes(lang)) {
    return Promise.resolve(setI18nLanguage(lang))
  }

  // If the language hasn't been loaded yet
  /* eslint-disable prefer-template */
  return import(
    /* webpackChunkName: "lang-[request]" */ '@/plugins/i18n/locales/' +
      lang +
      '/index.js'
  ).then(msgs => {
    i18n.setLocaleMessage(lang, msgs.default)
    loadedLanguages.push(lang)

    return setI18nLanguage(lang)
  })
  /* eslint-enable */
}

// 热更新
if (module.hot) {
  module.hot.accept(
    ['@/plugins/i18n/locales/en/index', '@/plugins/i18n/locales/zh-CN/index'],
    function () {
      i18n.setLocaleMessage(
        'en',
        require('@/plugins/i18n/locales/en/index').default,
      )
      i18n.setLocaleMessage(
        'zh-CN',
        require('@/plugins/i18n/locales/zh-CN/index').default,
      )
    },
  )
}

// 菜单
export function generateMenuTitle(record) {
  if (i18n.locale === 'en') {
    const translatedTitle = record?.enTitle

    return translatedTitle
  }
  return record?.title
}

export function generateName(title, enTitle) {
  if (i18n.locale === 'en') {
    return enTitle
  }
  return title
}

// 按钮
export function generateBtnTitle(code) {
  let btnList = this.$route.meta.buttonInfo
  let record = btnList[code]
  if (record && record.enTitle) {
    return generateMenuTitle(record)
  } else if (!record) {
    return ''
  } else {
    const key = findKey(action, name => name === record.title)
    return key ? i18n.t(`action.${key}`) : ''
  }
}

Vue.prototype.$generateMenuTitle = generateMenuTitle
Vue.prototype.$generateName = generateName
Vue.prototype.$generateBtnTitle = generateBtnTitle
