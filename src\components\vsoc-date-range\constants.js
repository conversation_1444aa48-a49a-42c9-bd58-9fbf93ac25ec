import { i18n } from '@/plugins/i18n'
import store from '@/store'
import {
  addMonths,
  endOfDay,
  format,
  getHours,
  getMinutes,
  getSeconds,
  set,
  startOfDay,
  subDays,
  subHours,
  subSeconds,
} from 'date-fns'

export const ISO_FORMAT = 'yyyy-MM-dd'
export const DEFAULT_DATE = format(new Date(), ISO_FORMAT)
export const TIME_FORMAT = 'HH:mm'
export const SECONDS_FORMAT = 'HH:mm:ss'
export const DEFAULT_TIME = format(new Date(), TIME_FORMAT)
export const MONTH_FORMAT = 'yyyy-MM'
export const DEFAULT_MONTH = format(new Date(), MONTH_FORMAT)

export const DEFAULT_FORMAT = 'yyyy-MM-dd HH:mm'

// 一天开始时间
export const STARTOFDAY = format(startOfDay(new Date()), DEFAULT_FORMAT)

// 一天结束时间
export const ENDOFDAY = format(
  endOfDay(addMonths(new Date(), 1)),
  DEFAULT_FORMAT,
)
export const DEFAULT_ACTION_LABELS_FN = () => {
  return {
    apply: i18n.t('action.confirm'),
    cancel: i18n.t('action.cancel'),
    reset: i18n.t('action.reset'),
  }
}

// 日期范围字符串拼接
export const RANGE_STR = function (start, end) {
  return [start, end].filter(v => !!v).join('~')
}

const endDay = endOfDay(new Date())
// 默认快捷键方式
// export const PRESETS = [
//   {
//     label: '最近1小时',
//     range: [subHours(new Date(), 1), new Date()],
//   },
//   {
//     label: '最近24小时',
//     range: [subHours(new Date(), 24), new Date()],
//   },
//   {
//     label: '最近7天',
//     range: [startOfDay(subDays(endDay, 6)), endDay],
//   },
//   {
//     label: '最近30天',
//     range: [startOfDay(subDays(endDay, 29)), endDay],
//   },
// ]

export const PRESETSFN = newDate => {
  const endDay = endOfDay(newDate)
  return [
    {
      label: i18n.t('enums.datePresets.last1'),
      range: [subHours(newDate, 1), newDate],
    },
    {
      label: i18n.t('enums.datePresets.last24'),
      range: [subHours(newDate, 24), newDate],
    },
    {
      label: i18n.t('enums.datePresets.last7'),
      range: [startOfDay(subDays(endDay, 6)), endDay],
    },
    {
      label: i18n.t('enums.datePresets.last30'),
      range: [startOfDay(subDays(endDay, 29)), endDay],
    },
  ]
}

export const ASSETPRESETSFN = newDate => {
  let values = store.getters['enums/getAssetActivityLevel']
  if (!values.length) return []
  const endDay = endOfDay(newDate)
  let currentHours = getHours(newDate)
  let currentMinutes = getMinutes(newDate)
  let currentSeconds = getSeconds(newDate)
  const last7days = set(subDays(newDate, values[0]), {
    hours: currentHours,
    minutes: currentMinutes,
    seconds: currentSeconds,
  })
  const last15days = set(subDays(newDate, values[1]), {
    hours: currentHours,
    minutes: currentMinutes,
    seconds: currentSeconds,
  })
  return [
    {
      label: i18n.t('enums.datePresets.last1'),
      range: [subHours(newDate, 1), newDate],
    },
    {
      label: i18n.t('enums.datePresets.highActivity'),
      range: [last7days, endDay],
    },
    {
      label: i18n.t('enums.datePresets.moderateActivity'),
      range: [last15days, subSeconds(last7days, 1)],
    },
    {
      label: i18n.t('enums.datePresets.lowActivity'),
      range: [new Date('1970-01-01 08:00:00'), subSeconds(last15days, 1)],
    },
  ]
}
