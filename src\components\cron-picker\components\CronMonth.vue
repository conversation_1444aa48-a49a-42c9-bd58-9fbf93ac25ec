<template>
  <div class="d-flex align-center">
    <div>{{ $t('infer.expression.everyMonth') }}</div>
    <div style="width: 360px">
      <v-select
        v-model="days"
        class="mx-2"
        color="primary"
        dense
        outlined
        solo
        multiple
        chips
        :items="dayOptions"
        hide-details
        @change="emitChange"
        :menu-props="{ offsetY: true }"
      >
        <template v-slot:selection="{ item, index }">
          <v-chip
            v-if="index <= 3"
            color="primary"
            class="chip-box"
            close
            label
            size="x-small"
            @click:close="removeTags(index)"
          >
            <span class="text-caption">{{ item }}</span>
          </v-chip>
          <span v-if="index === 4" class="grey--text text-caption">
            (+{{ days.length - 4 }})
          </span>
        </template>
      </v-select>
    </div>
    <!-- <el-select
      v-model="days"
      class="mx-2"
      multiple
      filterable
      style="width: 30rem"
      @change="emitChange"
    >
      <el-option
        v-for="item in dayOptions"
        :key="item"
        :label="item"
        :value="item"
      />
    </el-select> -->
    <div>{{ $t('infer.expression.day') }}</div>
    <v-icon size="1.25rem">mdi-clock-outline</v-icon>
    <div style="width: 100px">
      <v-select
        v-model="hour"
        class="mx-2"
        color="primary"
        dense
        outlined
        solo
        :items="hourOptions"
        item-text="label"
        item-value="value"
        hide-details
        @change="emitChange"
        :menu-props="{ offsetY: true }"
      >
      </v-select>
    </div>
    <!-- <el-select
      v-model="hour"
      class="mx-2"
      filterable
      style="width: 6rem"
      @change="emitChange"
    >
      <el-option
        v-for="item in hourOptions"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select> -->
    <div>{{ $t('infer.expression.hour') }}</div>
    <div style="width: 100px">
      <v-select
        v-model="minute"
        class="mx-2"
        color="primary"
        dense
        outlined
        disabled
        solo
        :items="minuteOptions"
        item-text="label"
        item-value="value"
        hide-details
        @change="emitChange"
        :menu-props="{ offsetY: true }"
      >
      </v-select>
    </div>
    <!-- <el-select
      v-model="minute"
      class="mx-2"
      filterable
      style="width: 6rem"
      @change="emitChange"
    >
      <el-option
        v-for="item in minuteOptions"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select> -->
    <div>{{ $t('infer.expression.minute') }}</div>
  </div>
</template>

<script>
export default {
  name: 'CronMonth',
  data() {
    return {
      days: [1],
      hour: 0,
      minute: 0,
    }
  },
  computed: {
    dayOptions() {
      return Array.from(Array(31), (_, i) => i + 1)
    },
    hourOptions() {
      return Array.from(Array(24), (_, i) => {
        if (i < 10) {
          return {
            value: i,
            label: `0${i}`,
          }
        }

        return {
          value: i,
          label: i,
        }
      })
    },
    minuteOptions() {
      return Array.from(Array(60), (_, i) => {
        if (i < 10) {
          return {
            value: i,
            label: `0${i}`,
          }
        }

        return {
          value: i,
          label: i,
        }
      })
    },
    cronExp() {
      if (this.days.length === 0) {
        return `0 ${this.minute} ${this.hour} * * ?`
      }

      return `0 ${this.minute} ${this.hour} ${this.days.join(',')} * ?`
    },
  },
  methods: {
    init(value) {
      const tempArr = value.split(' ')
      this.minute = Number(tempArr[1])
      this.hour = Number(tempArr[2])
      if (tempArr[3] === '*') {
        this.days = []
      } else {
        const dayArr = tempArr[3].split(',')
        this.days = dayArr.filter(v => v !== '').map(v => Number(v))
      }
    },
    emitChange() {
      this.$emit('change', this.cronExp)
    },
    removeTags(index) {
      this.days.splice(index, 1)
      this.$emit('change', this.cronExp)
    },
  },
}
</script>
<style lang="scss" scoped></style>
