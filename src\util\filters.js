import { i18n } from '@/plugins/i18n'
import store from '@/store'
import useAppConfig from '@core/@app-config/useAppConfig'
import { handleDate } from './utils'
const toItemText = (value, items) => {
  const cur = items.find(v => v.value == value)
  return cur ? cur.text : ''
}
// 最近活动
const generateEventName = (name, { eventName, type }) => {
  if (type === 'RecentActivity') {
    return i18n.t('asset.recentActivity')
  } else {
    return eventName
  }
}

const getRoundSize = num => {
  let screenWidth = store.state.global.clientWidth
  return Math.round((screenWidth / 1920) * num)
}

const getCheckedIcon = (icon, bool) => {
  if (!icon) {
    return
  }
  const { isDark, appSkinVariant } = useAppConfig()
  if (bool) {
    if (isDark.value || appSkinVariant.value === 'semi-dark') {
      return icon.replace('Default-W', 'checked-D')
    }
    return icon.replace('Default-W', 'checked-W')
  }
  if (isDark.value || appSkinVariant.value === 'semi-dark') {
    return icon.replace('Default-W', 'Default-D')
  }
  return icon
}
const formatFileSize = size => {
  if (size < 1024) {
    return size + 'B'
  } else if (size < 1024 * 1024) {
    return Math.ceil(size / 1024) + 'KB'
  } else if (size < 1024 * 1024 * 1024) {
    return Math.ceil(size / (1024 * 1024)) + 'MB'
  } else {
    return Math.ceil(size / (1024 * 1024 * 1024)) + 'GB'
  }
}
/**
 * @param {Number} value
 * @param {String} mode 返回结构是String还是Object，默认为Strig
 * @param {String} int 保留小数点
 * @returns 格式化的数字
 */
const numberToFormat = (value, mode = 'String', int = 2) => {
  let abbreviations = {
    thousand: 'K',
    tenThousand: 'W',
    million: 'M',
    billion: 'B',
    trillion: 'T',
  }
  const trillion = Math.pow(10, 12) // 一万亿
  const billion = Math.pow(10, 8) // 十亿
  const million = Math.pow(10, 6) // 百万
  const tenThousand = Math.pow(10, 4) // 万
  const thousand = Math.pow(10, 3) // 千
  let abbr = ''
  // 避免为空
  value = value || 0
  if (typeof value !== 'number') {
    value = parseFloat(value)
  }
  if (Number.isNaN(value) || !Number.isFinite(value)) {
    return
  }
  // 绝对值
  let abs = Math.abs(value)
  if (abs >= trillion) {
    abbr = abbreviations.trillion
    value = value / trillion
  } else if (abs < trillion && abs >= billion) {
    abbr = abbreviations.billion
    value = value / billion
  } else if (abs < billion && abs >= million) {
    abbr = abbreviations.million
    value = value / million
  } else if (abs < million && abs >= tenThousand) {
    abbr = abbreviations.tenThousand
    value = value / tenThousand
  } else if (abs < million && abs >= thousand) {
    abbr = abbreviations.thousand
    value = value / thousand
  }
  // 是否存在小数
  let arr = String(value).split('.')
  let result = {
    num: 0,
    unit: '',
  }
  if (arr.length > 1) {
    // 小数保留两位
    result = {
      num: value.toFixed(int),
      unit: abbr,
    }
    // value.toFixed(2) + abbr
  } else {
    result = {
      num: value,
      unit: abbr,
    }
  }

  // value + abbr
  if (mode === 'String') {
    return result.num + result.unit
  } else {
    return result
  }
}

const num = val => {
  val = `${val}` // 转换成字符串
  let int = val
  int = int.split('').reverse().join('') // 翻转整数
  let temp = '' // 临时变量
  for (let i = 0; i < int.length; i++) {
    temp += int[i]
    if ((i + 1) % 3 === 0 && i !== int.length - 1) {
      temp += ',' // 每隔三个数字拼接一个逗号
    }
  }
  temp = temp.split('').reverse().join('') // 加完逗号之后翻转

  return temp // 返回
}

// 把时间转为更友好的显示方式
const handleChatDate = date => {
  const cut = new Date()
  const cutDate = cut.getDate()
  const cutYear = cut.getFullYear()
  const par = new Date(date)
  const parDate = par.getDate()
  const parYear = par.getFullYear()

  // 当天
  if (handleDate(cut, 'yyyy-MM-dd') === handleDate(par, 'yyyy-MM-dd')) {
    return handleDate(date, 'hh:mm')
  }

  // 前一天
  if (
    cutDate - parDate === 1 &&
    handleDate(cut, 'yyyy-MM') === handleDate(par, 'yyyy-MM')
  ) {
    return `昨天 ${handleDate(date, 'hh:mm')}`
  }

  // 今年
  if (cutYear === parYear) {
    return handleDate(date, 'M月d日')
  }

  // 前几年了
  return handleDate(date, 'yyyy年M月d日')
}

// 转为系统时间显示
const toDate = (_date, _format) => {
  // author: meizz
  if (!_date) return ''
  if (!_date.getDate) _date = new Date(_date)
  _format = _format || 'yyyy-MM-dd hh:mm:ss'
  const o = {
    'y+': _date.getFullYear(),
    'M+': _date.getMonth() + 1, // 月份
    'd+': _date.getDate(), // 日
    'h+': _date.getHours(), // 小时
    'm+': _date.getMinutes(), // 分
    's+': _date.getSeconds(), // 秒
    'q+': Math.floor((_date.getMonth() + 3) / 3), // 季度
    S: _date.getMilliseconds(), // 毫秒
  }
  if (/(y+)/.test(_format)) {
    _format = _format.replace(
      RegExp.$1,
      `${_date.getFullYear()}`.substr(4 - RegExp.$1.length),
    )
  }
  for (const k in o) {
    if (new RegExp(`(${k})`).test(_format)) {
      _format = _format.replace(
        RegExp.$1,
        RegExp.$1.length == 1 ? o[k] : `00${o[k]}`.substr(`${o[k]}`.length),
      )
    }
  }

  return _format
}

const getDateDiff = updateTime => {
  if (!updateTime) {
    return ''
  }
  if (typeof updateTime === 'string') {
    updateTime = new Date(updateTime).getTime()
  }
  const now = new Date().getTime()
  const second = Math.floor((now - updateTime) / 1000)
  const minute = Math.floor(second / 60)
  const hour = Math.floor(minute / 60)
  const day = Math.floor(hour / 24)
  const month = Math.floor(day / 31)
  const year = Math.floor(month / 12)
  if (year > 0) {
    // return `${year}年前`
    return i18n.tc('global.diff.year', year, { num: year })
  }
  if (month > 0) {
    // return `${month}月前`
    return i18n.tc('global.diff.month', month, { num: month })
  }
  if (day > 0) {
    // let ret = `${day}天前`
    let ret = i18n.tc('global.diff.day', day, { num: day })
    let week = 0
    if (day >= 7 && day < 14) {
      // ret = '1周前'
      week = 1
    } else if (day >= 14 && day < 21) {
      // ret = '2周前'
      week = 2
    } else if (day >= 21 && day < 28) {
      // ret = '3周前'
      week = 3
    } else if (day >= 28 && day < 31) {
      // ret = '4周前'
      week = 4
    }
    if (week > 0) {
      ret = i18n.tc('global.diff.week', week, { num: week })
    }

    return ret
  }
  if (hour > 0) {
    // return `${hour}小时前`
    return i18n.tc('global.diff.hour', hour, { num: hour })
  }
  if (minute > 0) {
    // return `${minute}分钟前`
    return i18n.tc('global.diff.minute', minute, { num: minute })
  }
  if (second > 0) {
    // return `${second}秒前`
    return i18n.tc('global.diff.second', second, { num: second })
  }

  return i18n.t('global.diff.justNow')
}

const avatar = name => {
  let n = name
  if (name) {
    if (name.length > 2) {
      n = name.substr(name.length - 2)
    }
  }

  return n.toUpperCase()
}
const firstAvatar = name => {
  let n = name
  if (name) {
    if (name.length > 1) {
      n = name.substr(0, 1)
    }
  }

  return n.toUpperCase()
}

const tranNumber = (num, point) => {
  const numStr = num.toString()

  // 1千以内直接返回
  if (numStr.length < 5) {
    return numStr
  }

  // 大于8位数是亿
  if (numStr.length > 8) {
    const decimal = numStr.substring(
      numStr.length - 8,
      numStr.length - 8 + point,
    )

    return `${parseFloat(`${parseInt(num / 100000000)}.${decimal}`)}y`
  }

  // 大于5位数是1万
  if (numStr.length >= 5) {
    const decimal = numStr.substring(
      numStr.length - 4,
      numStr.length - 4 + point,
    )

    return `${parseFloat(`${parseInt(num / 10000)}.${decimal}`)}w`
  }

  // 大于4位数是1千
  if (numStr.length >= 4) {
    const decimal = numStr.substring(
      numStr.length - 3,
      numStr.length - 3 + point,
    )

    return `${parseFloat(`${parseInt(num / 1000)}.${decimal}`)}k`
  }
}

// IP转成整型
function ipv4ToInt(ip) {
  let num = 0
  ip = ip.split('.')
  num =
    Number(ip[0]) * 256 * 256 * 256 +
    Number(ip[1]) * 256 * 256 +
    Number(ip[2]) * 256 +
    Number(ip[3])
  num >>>= 0

  return num
}

// 整型解析为IP地址
function IntToIpv4(num) {
  let str
  const tt = new Array()
  tt[0] = (num >>> 24) >>> 0
  tt[1] = ((num << 8) >>> 24) >>> 0
  tt[2] = (num << 16) >>> 24
  tt[3] = (num << 24) >>> 24
  str = `${String(tt[0])}.${String(tt[1])}.${String(tt[2])}.${String(tt[3])}`

  return str
}

const intervalSize = num => {
  if (num < 60) {
    return num > 1 ? `${num} seconds` : `${num} second`
  }
  if (num < 3600) {
    return num / 60 > 1 ? `${num / 60} minutes` : `${num / 60} minute`
  }
  if (num < 86400) {
    return num / 3600 > 1 ? `${num / 3600} hours` : `${num / 3600} hour`
  }
  if (num < 2592000) {
    return num / 86400 > 1 ? `${num / 86400} days` : `${num / 86400} day`
  }
}

/**
 * 将 second 转换成 Days/Hours/Minutes
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */

function formatSeconds(value, type = 'String') {
  let theTime = parseInt(value) // 需要转换的时间秒
  if (theTime === 0 || theTime === -1 || theTime < -1) {
    // 特殊处理 如何传入的负数
    return value
  }

  let theMinutes = 0 // 分
  let theHours = 0 // 小时
  let theDays = 0 // 天
  if (theTime >= 60) {
    theMinutes = parseInt(theTime / 60) // 转换为分钟
    theTime = parseInt(theTime % 60)
    if (theMinutes >= 60) {
      // 大于1h
      theHours = parseInt(theMinutes / 60) // 转为为小时
      theMinutes = parseInt(theMinutes % 60)
      if (theHours >= 24) {
        // 大于24小时
        theDays = parseInt(theHours / 24) // 转为天
        theHours = parseInt(theHours % 24)
      }
    }
  }
  if (type === 'String') {
    let result = ''
    if (theTime > 0) {
      const seconds = parseInt(theTime)
      result = ` ${i18n.tc('global.time.seconds', seconds, [seconds])}`
      // result = ` ${parseInt(theTime)}秒`
    }
    if (theMinutes > 0) {
      const minutes = parseInt(theMinutes)
      result = ` ${i18n.tc('global.time.min', minutes, [minutes])}${result}`
    }
    if (theHours > 0) {
      const hours = parseInt(theHours)
      result = ` ${i18n.tc('global.time.hours', hours, [hours])}${result}`
    }
    if (theDays > 0) {
      const days = parseInt(theDays)
      result = ` ${i18n.tc('global.time.day', days, [days])}${result}`
    }

    return result
  }

  return {
    Days: parseInt(theDays),
    Hours: parseInt(theHours),
    Minutes: parseInt(theMinutes),
  }
}

// 转为N/A
const dataFilter = value => value || 'N/A'

export {
  avatar,
  dataFilter,
  firstAvatar,
  formatFileSize,
  formatSeconds,
  generateEventName,
  getCheckedIcon,
  getDateDiff,
  getRoundSize,
  handleChatDate,
  intervalSize,
  IntToIpv4,
  ipv4ToInt,
  num,
  numberToFormat,
  toDate,
  toItemText,
  tranNumber,
}
