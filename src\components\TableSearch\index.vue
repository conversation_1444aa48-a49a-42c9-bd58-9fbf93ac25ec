<template>
  <div class="d-flex align-center">
    <div v-for="(item, index) in searchList" :key="index">
      <div v-if="item.type === 'multiSearch'" class="d-flex align-end">
        <v-select
          v-model="item.value"
          outlined
          dense
          small
          color="primary"
          :menu-props="{ offsetY: true }"
          append-icon="mdi-chevron-down"
          class="me-3 select-width"
          hide-details="auto"
          :items="item.conditions"
          item-value="value"
          item-text="text"
          :label="$t('action.queryConditions')"
          @change="changeCondition(item.conditions, item)"
        >
        </v-select>
        <div v-if="isShow">
          <div v-for="(v, i) in item.conditions" :key="i">
            <v-text-field
              v-if="v.type === 'input' && v.value === item.value"
              v-model="searchQuery[v.value]"
              color="primary"
              hide-details="auto"
              :label="v.text"
              dense
              outlined
              class="me-3 text-width"
              clearable
              @click:clear="onClear(v.value)"
              @keyup.enter.native="$_search"
            ></v-text-field>
            <v-select
              v-else-if="v.type === 'select' && v.value === item.value"
              v-model="searchQuery[v.value]"
              :items="v.itemList"
              dense
              :label="v.text"
              hide-details="auto"
              class="me-3 text-width"
              clearable
              outlined
              :menu-props="{ offsetY: true }"
              @change="$_search"
            >
            </v-select>
            <v-autocomplete
              v-else-if="v.type === 'autocomplete' && v.value === item.value"
              v-model="searchQuery[v.value]"
              :items="v.itemList"
              multiple
              outlined
              dense
              hide-details
              class="me-3 text-width"
              :menu-props="{ offsetY: true }"
              :label="v.text"
              clearable
              @change="$_search"
            >
              <template v-slot:selection="{ index }">
                <span v-if="index === 0">
                  {{ $t('global.pagination.selected') }}：{{
                    searchQuery[v.value].length
                  }}
                </span>
              </template>
            </v-autocomplete>

            <vsoc-date-range
              v-else-if="v.type === 'date' && v.value === item.value"
              v-model="v.dateRange.range"
              no-title
              :menu-props="v.dateRange.menuProps"
              @input="onChangeDate(v, $event)"
              @search="$_search"
            >
              <template v-slot:text="{ on, attrs }">
                <v-text-field
                  type="button"
                  clearable
                  class="append-icon-max me-3 date-width"
                  outlined
                  dense
                  readonly
                  hide-details="auto"
                  color="primary"
                  large
                  prepend-inner-icon="mdi-calendar-range-outline"
                  :label="v.text"
                  :value="
                    RANGE_STR(searchQuery[v.value[0]], searchQuery[v.value[1]])
                  "
                  @click:clear="onChangeDate(v, { start: '', end: '' })"
                ></v-text-field>
              </template>
            </vsoc-date-range>

            <v-radio-group
              v-else-if="v.type === 'radio' && v.value === item.value"
              v-model="searchQuery[v.value]"
              row
              small
              dense
              hide-details
              class="ml-4 mt-0 text-width"
              @change="$_search"
            >
              <v-radio
                v-for="x in v.itemList"
                :key="x.value"
                :value="x.value"
                :label="x.text"
              ></v-radio>
            </v-radio-group>
          </div>
        </div>
      </div>
      <v-text-field
        v-if="item.type === 'input'"
        v-model.trim="searchQuery[item.value]"
        class="me-3 text-width"
        outlined
        dense
        clearable
        hide-details="auto"
        color="primary"
        :label="item.text"
        @keyup.enter.native="$_search"
        @click:clear="onClear(item.value)"
      ></v-text-field>
      <v-select
        v-if="item.type === 'select'"
        v-model="searchQuery[item.value]"
        :items="item.itemList"
        dense
        :label="item.text"
        hide-details="auto"
        :multiple="item.multiple"
        class="me-3 text-width"
        clearable
        outlined
        :menu-props="{ offsetY: true }"
        @change="$_search"
      >
      </v-select>
      <v-combobox
        v-if="item.type === 'combobox'"
        v-model="searchQuery[item.value]"
        :items="item.itemList"
        dense
        :label="item.text"
        hide-details="auto"
        :multiple="item.multiple"
        class="me-3 text-width"
        clearable
        outlined
        :menu-props="{ offsetY: true }"
        @change="$_search"
      >
      </v-combobox>
      <template>
        <v-autocomplete
          v-if="item.type === 'autocomplete' && item.multiple === 'false'"
          v-model="searchQuery[item.value]"
          :items="item.itemList"
          outlined
          dense
          hide-details
          class="me-3 text-width"
          :menu-props="{ offsetY: true }"
          :label="item.text"
          @change="$_search"
        >
        </v-autocomplete>
        <v-autocomplete
          v-else-if="item.type === 'autocomplete'"
          v-model="searchQuery[item.value]"
          :items="item.itemList"
          multiple
          outlined
          dense
          hide-details
          class="me-3 text-width"
          :menu-props="{ offsetY: true }"
          :label="item.text"
          clearable
          @change="$_search"
        >
          <template v-slot:selection="{ index }">
            <span v-if="index === 0">
              {{ $t('global.pagination.selected') }}：{{
                searchQuery[item.value].length
              }}
            </span>
          </template>
        </v-autocomplete>
      </template>

      <el-select
        v-if="item.type === 'elSelect'"
        class="me-3 text-width"
        v-model="searchQuery[item.value]"
        filterable
        :placeholder="item.text"
        @change="$_search"
      >
        <el-option
          v-for="item in item.itemList"
          :key="item.value"
          :label="item.text"
          :value="item.value"
        >
        </el-option>
      </el-select>
      <search-tree
        v-if="item.type === 'tree'"
        class="me-3"
        :labelText="item.text"
        ref="elTree"
        :organizationFlag="true"
        :showCheckbox="true"
        :outlinedFlag="true"
        :expandFlag="true"
        :treeData="item.itemList"
        :default-props="item.defaultProps"
        @change="changeTree"
        :bottom="true"
        width="280"
      />
      <vsoc-date-range
        v-if="item.type === 'date'"
        v-model="item.dateRange.range"
        no-title
        :menu-props="item.dateRange.menuProps"
        @input="onChangeDate(item, $event)"
        @search="$_search"
      >
        <template v-slot:text="{ on, attrs }">
          <v-text-field
            type="button"
            clearable
            class="append-icon-max me-3 date-width"
            outlined
            dense
            readonly
            hide-details="auto"
            color="primary"
            large
            prepend-inner-icon="mdi-calendar-range-outline"
            :label="item.text"
            :value="
              RANGE_STR(searchQuery[item.value[0]], searchQuery[item.value[1]])
            "
            @click:clear="onChangeDate(item, { start: '', end: '' })"
          ></v-text-field>
        </template>
      </vsoc-date-range>
    </div>
    <!-- 按钮 -->
    <v-btn class="primary--text bg-btn" elevation="0" @click="$_search">
      <span>
        {{ $t('action.search') }}
      </span>
    </v-btn>
  </div>
</template>
<script>
import searchTree from '@/components/search-tree/index'
import VsocDateRange from '@/components/vsoc-date-range/VsocDateRange.vue'
import { RANGE_STR } from '@/components/vsoc-date-range/constants'

export default {
  components: {
    VsocDateRange,
    searchTree,
  },
  props: {
    searchList: {
      type: Array,
      default: [],
    },
    searchQuery: {
      type: Object,
      default: {},
    },
  },
  watch: {
    searchList: {
      handler() {
        this.searchList.forEach(v => {
          if (v.type === 'multiSearch') {
            v.conditions.forEach(item => {
              if (
                item.type === 'date' &&
                this.searchQuery[item.value[0]] &&
                this.searchQuery[item.value[1]]
              ) {
                v.value = item.value
              } else {
                if (Array.isArray(this.searchQuery[item.value])) {
                  if (this.searchQuery[item.value].length) {
                    v.value = item.value
                  }
                } else {
                  if (
                    this.searchQuery[item.value] ||
                    this.searchQuery[item.value] === '0'
                  ) {
                    v.value = item.value
                  }
                }
              }
            })
          }
        })
      },
      deep: true,
      immediate: true,
    },
  },
  data() {
    return {
      isShow: true,
    }
  },
  created() {},
  methods: {
    RANGE_STR,
    onClear(queryKey) {
      this.searchQuery[queryKey] = ''
      this.$emit('search')
    },
    $_search() {
      this.$emit('search')
    },
    onChangeDate(item, range) {
      item.dateRange.range = range
      this.searchQuery[item.value[0]] = range.start
      this.searchQuery[item.value[1]] = range.end
      this.$emit('search')
    },
    changeTree(data, e) {
      if (e && e.checkedKeys.length) {
        this.$refs['elTree'][0].$refs.tree.setCheckedKeys([data.id])
        this.$refs['elTree'][0].filterText = data.name
        this.searchQuery.id = data.id
      } else {
        this.$refs['elTree'][0].$refs.tree.setCheckedKeys([])
        this.$refs['elTree'][0].filterText = ''
        this.searchQuery.id = ''
      }
      this.$emit('search')
    },
    changeCondition(conditionList, item) {
      this.isShow = false
      this.isShow = true
      conditionList.forEach(v => {
        if (v.value !== item.value) {
          this.searchQuery[v.value] = v.type === 'autocomplete' ? [] : ''
        }
      })
    },
  },
}
</script>
