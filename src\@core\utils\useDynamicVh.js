import store from '@/store'
import { getCurrentInstance, watch } from '@vue/composition-api'
import { useWindowSize } from '@vueuse/core'

export default () => {
  // 增加防抖 resize方法实现
  const setResize = () => {
    let resizeTimeout
    const ins = getCurrentInstance()

    const vm = ins && ins.proxy ? ins.proxy : null
    if (!resizeTimeout && vm) {
      resizeTimeout = setTimeout(() => {
        resizeTimeout = null
        window.onresize = e => {
          vm.$bus.$emit('resize', e)
          const { clientWidth } = window.document.documentElement
          store.commit('global/setClientWidth', clientWidth)
        }
      }, 1000)
    }
  }
  const setVh = () => {
    document.documentElement.style.setProperty('--vh', `${window.innerHeight * 0.01}px`)
  }
  const { width: windowWidth } = useWindowSize()
  watch(windowWidth, setVh, { immediate: true })

  // 增加防抖 resize方法调用
  watch(windowWidth, setResize, { immediate: true })

  return {}
}
