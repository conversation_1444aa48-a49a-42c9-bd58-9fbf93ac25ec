import { request, vsocPath } from '../../util/request'

// 菜单授权
export const saveOrUpdateRoleMenu = function (params) {
  return request({
    url: `${vsocPath}/sysRole/saveOrUpdateRoleMenu`,
    method: 'post',
    data: params,
  })
}

// 查询角色绑定的菜单id
export const findRoleMenuList = function (params) {
  return request({
    url: `${vsocPath}/sysRole/findRoleMenuList`,
    method: 'post',
    data: params,
  })
}

// 根据roleid获取菜单层级树
export const queryRoleMenuList = function (params) {
  return request({
    url: `${vsocPath}/sysRole/queryRoleMenuList`,
    method: 'post',
    data: params,
  })
}

// 根据roleid获取关联用户个数
export const selectUserRoleCount = function (params) {
  return request({
    url: `${vsocPath}/sysRole/selectUserRoleCount`,
    method: 'post',
    params,
  })
}

// 角色查询接口
export const userRole = function (params) {
  return request({
    url: `${vsocPath}/sysRole/userRole`,
    method: 'post',
    data: params,
  })
}

// 角色添加接口
export const addRole = function (params) {
  return request({
    url: `${vsocPath}/sysRole/addRole`,
    method: 'post',
    data: params,
  })
}

// 角色修改接口
export const updateRole = function (params) {
  return request({
    url: `${vsocPath}/sysRole/updateRole`,
    method: 'post',
    data: params,
  })
}

// 角色删除接口
export const delRole = function (params) {
  return request({
    url: `${vsocPath}/sysRole/delRole`,
    method: 'post',
    params,
  })
}
