const model = {
  currentTitle: 'Model',
  headers: {
    id: 'Model ID',
    name: 'Model Name',
    code: 'Model Code',
    desc: 'Description',
  },
  device: {
    title: 'Device Details',
    sort: 'Sort',
    id: 'Device ID',
    type: 'Device Type',
    icon: 'Device Icon',
    name: 'Device Name',
    info: 'Device Information',
    valid: 'Enter 2-20 characters',
    valid1: 'Select 4-10 fields',
  },
  btn: {
    new: 'New Model',
    edit: 'Edit Model',
    detail: 'Model Details',
  },
  edit: {
    picture: 'Model Picture',
  },
  posture: {
    safePosture: 'Posture',
    modelNum: 'Protected vehicles',
  },
  empty: 'No relevant content found for "{name}"',
  hint: 'Please try another search',
  hint1: 'The device types cannot be the same',
  hint2: 'Only jpg/png/jpeg format files can be uploaded',
  hint3:
    'The size of the uploaded image should not exceed 200KB. The current image exceeds the limit. Please compress it before attempting to upload.',
  swal: {
    title: 'Delete Model',
    hint: 'Are you sure to delete model: {name}?',
  },
}

export default model
