import CronValidator from 'cron-expression-validator'
import Cronstrue from 'cronstrue/i18n'

// import Cookies from 'js-cookie'
// import { Message } from 'element-ui'
// import i18n from '@/i18n'

export const aliasToNumberMapping = {
  SUN: 0,
  MON: 1,
  TUE: 2,
  WED: 3,
  THU: 4,
  FRI: 5,
  SAT: 6,
}

//

export function toDayNumber(alias) {
  const number = aliasToNumberMapping[alias]
  if (number == undefined) {
    throw new Error(`unhandled alias ${alias}`)
  }

  return number
}

export function toDayAlias(num) {
  const alias = Object.keys(aliasToNumberMapping).find(k => aliasToNumberMapping[k] === num)
  if (alias == undefined) {
    throw new Error(`unhandled number ${num}`)
  }

  return alias
}

export function isDayAlias(s) {
  return Object.keys(aliasToNumberMapping).includes(s)
}

export function isAny(token) {
  return token.type == 'question' || token.type == 'asterisk'
}

export function isAnyTime(token) {
  return token.type == 'asterisk' || (token.type == 'number' && token.value == 0)
}

export function parseSubExpr(expr) {
  expr = expr.trim()
  let match
  if ((match = expr.match(/\*\/(\d+)/)) != null) {
    return {
      type: 'cronNumber',
      at: { type: 'asterisk' },
      every: { type: 'number', value: parseInt(match[1]) },
    }
  }
  if ((match = expr.match(/(\d+)\/(\d+)/)) != null) {
    return {
      type: 'cronNumber',
      at: { type: 'number', value: parseInt(match[1]) },
      every: { type: 'number', value: parseInt(match[2]) },
    }
  }
  if ((match = expr.match(/(\d+)/)) != null) {
    return {
      type: 'number',
      value: parseInt(match[1]),
    }
  }
  if (expr == '?') {
    return { type: 'question' }
  }
  if (expr == '*') {
    return { type: 'asterisk' }
  }
  throw new Error(`Unhandled subexpression: ${expr}`)
}

export function parseDayOfWeek(expr) {
  expr = expr.trim()
  if (expr == '*') {
    return {
      type: 'asterisk',
    }
  }
  if (expr == '?') {
    return {
      type: 'question',
    }
  }

  const groups = expr.match(
    /([a-zA-Z0-9]+)(,[a-zA-Z0-9]+)?(,[a-zA-Z0-9]+)?(,[a-zA-Z0-9]+)?(,[a-zA-Z0-9]+)?(,[a-zA-Z0-9]+)?(,[a-zA-Z0-9]+)?/,
  )
  if (groups == null) throw new Error(`invalid days expression: ${expr}`)

  return {
    type: 'setOfDays',
    days: groups
      .slice(1)
      .map(d => d && d.replace(/,/, ''))
      .filter(d => d)
      .map(d => (!isDayAlias(d) ? toDayAlias(parseInt(d)) : d)),
  }
}

export function getExpressionType(expression) {
  const advanced = {
    type: 'advanced',
    cronExpression: expression,
  }
  const groups = expression.split(' ')
  if (groups.length != 5 && groups.length != 6) {
    return advanced
  }
  const cron =
    groups.length == 6
      ? {
          seconds: parseSubExpr(groups[0]),
          minutes: parseSubExpr(groups[1]),
          hours: parseSubExpr(groups[2]),
          dayOfTheMonth: parseSubExpr(groups[3]),
          month: parseSubExpr(groups[4]),
          dayOfWeek: parseDayOfWeek(groups[5]),
        }
      : {
          minutes: parseSubExpr(groups[0]),
          hours: parseSubExpr(groups[1]),
          dayOfTheMonth: parseSubExpr(groups[2]),
          month: parseSubExpr(groups[3]),
          dayOfWeek: parseDayOfWeek(groups[4]),
        }

  // console.log('cron============')
  // console.log(cron)
  if (
    cron.minutes.type == 'cronNumber' &&
    isAnyTime(cron.minutes.at) &&
    cron.hours.type == 'asterisk' &&
    cron.dayOfTheMonth.type == 'asterisk' &&
    cron.month.type == 'asterisk' &&
    isAny(cron.dayOfWeek)
  ) {
    return {
      type: 'minute',
    }
  }
  if (
    cron.minutes.type == 'number' &&
    cron.hours.type == 'cronNumber' &&
    isAnyTime(cron.hours.at) &&
    cron.dayOfTheMonth.type == 'asterisk' &&
    cron.month.type == 'asterisk' &&
    isAny(cron.dayOfWeek)
  ) {
    return {
      type: 'hour',
    }
  }

  // 兼容星号
  if (
    cron.minutes.type == 'number' &&
    cron.hours.type == 'number' &&
    (cron.dayOfTheMonth.type == 'asterisk' ||
      (cron.dayOfTheMonth.type === 'cronNumber' &&
        cron.dayOfTheMonth.at &&
        cron.dayOfTheMonth.at.type == 'asterisk')) &&
    cron.month.type == 'asterisk' &&
    isAny(cron.dayOfWeek)
  ) {
    return {
      type: 'day',
    }
  }
  if (
    cron.minutes.type == 'number' &&
    cron.hours.type == 'number' &&
    isAny(cron.dayOfTheMonth) &&
    cron.month.type == 'asterisk' &&
    cron.dayOfWeek.type == 'setOfDays'
  ) {
    return {
      type: 'week',
    }
  }

  // 兼容星号
  if (
    cron.minutes.type == 'number' &&
    cron.hours.type == 'number' &&
    cron.dayOfTheMonth.type == 'number' &&
    (cron.month.type == 'asterisk' ||
      (cron.month.type === 'cronNumber' && cron.month.at && cron.month.at.type == 'asterisk')) &&
    isAny(cron.dayOfWeek)
  ) {
    return {
      type: 'month',
    }
  }

  return advanced
}

export function validExpression(expression, type = 'tips') {
  const values = expression.split(' ').filter(item => !!item)
  if (values && values.length > 6) {
    if (type === 'tips') {
      //   Message.error(i18n.t('定时表达式最多只支持6位'))
    }

    return { isChecked: false, message: '定时表达式最多只支持6位' }
  }
  const rs = CronValidator.isValidCronExpression(expression, { error: true })

  if (rs === true) {
    // 因为 cronstrue 的中文 只支持 zh_CN  不支持zh-CN
    const msg = Cronstrue.toString(expression, {
      dayOfWeekStartIndexZero: false,
      locale: 'zh_CN',
    })

    return { isChecked: true, message: msg }
  }
  if (rs.errorMessage && Array.isArray(rs.errorMessage)) {
    const len = rs.errorMessage.length
    if (type === 'tips') {
      // Message.error('' + rs.errorMessage[len - 1])
    }

    return { isChecked: false, message: rs.errorMessage[len - 1].toString() }
  }
  if (type === 'tips') {
    // Message.error('' + rs.errorMessage)
  }

  return { isChecked: false, message: rs.errorMessage && rs.errorMessage.toString() }
}
