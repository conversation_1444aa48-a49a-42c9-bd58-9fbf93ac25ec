const dataCenter = {
  headers: {
    id: '编号',
    name: '数据名称',
    type: '数据类型',
    source: '数据来源',
    collectionDate: '采集时间',
    generationDate: '报告生成时间',
    reportId: '报告ID',
    attachmentCount: '附件',
  },
  collectionRange: '采集时间范围',
  jsonView: 'JSON 视图',
  tab: {
    periodic: '定时报告观察周期',
    case: 'Case报告观察周期',
    dataId: '数据ID',
  },

  vehicles: '预期发送报告的车辆数',
  reportVehicle: '已发送报告车辆数（至少一次）',
  cdi: {
    title: '标题',
    id: '事件ID',
    contact: '联系人',
    type: '类型',
    createdDate: '创建时间',
    lastChangeDate: '最后更新时间',
    lifecycleStatus: '生命周期状态',
    priority: '优先级',
    threatLevel: '威胁等级',
    referenceUrl: '参考URL',
    cveRef: 'CVE参考URL',
    description: '描述',
  },
  vehicle: {
    title: '影响车型',
    count: '影响车辆数',
  },
  attach: {
    title: '附件列表',
    id: '文件ID',
    name: '文件名称',
    path: '路径',
    type: '类型',
    size: '大小',
    creater: '添加者',
    createDate: '添加时间',
  },
  changeList: {
    serviceListChangesMonitor: '服务列表变更监控报告',
    abnormalMemoryProfileMonitor: '异常内存监控报告',
    networkConnectionStatusMonitor: '网络链接监控报告',
    privilegeUserLoginMonitor: '用户监控报告',
    oTAAbnormalMonitor: 'OTA异常监控报告',
  },
  service: '服务列表变更监控统计',
  serviceListChangesMonitor: {
    totalSuccessNum: '成功事件总数',
    totalFailNum: '失败事件总数',
    newNum: '新增应用服务列表类型总数',
    reduceNum: '减少应用服务列表类型总数',
  },
  abnormal: '异常内存监控统计',
  abnormalMemoryProfileMonitor: {
    cpuTypeNum: 'CPU类型的事件总数',
    ramMemoryTypeNum: 'RAM内存类型的事件总数',
  },
  network: '网络连接监控统计',
  networkConnectionStatusMonitor: {
    typeNum: '345G类型的数量',
    wifiTypeNum: 'WIFI类型的数量',
    connSuccess: '连接成功数',
    connFail: '连接失败数',
  },
  privilege: '用户监控统计',
  ota: 'OTA监控统计',
  occurDate: '发生时间',
  summary: '总结',
  scale: '规模',
  lep: {
    vehiclesCount: '最近{count}天发送报告车辆数',
    firstCount: '最近{count}天首次发送报告车辆数',
    affectedModels: '事件影响车型',
    suspiciousCount: '可疑事件总数',
    model: '车型',
    caseStatistics: 'CASE报告统计',
    caseCount: '正在调查的案例总数',
    addCaseCount: '新建的案例数最近{count}天',
    closeCaseCount: '关闭的案例总数最近{count}天',
    suspiciousDistribution: '可疑事件类型分布',
    title: 'IDS周期性统计报告',
    resport1: '预期发送报告的车辆数',
    resport2: '已发送过报告的车辆数(至少一次)',
    resport3: '最近{0}天没有发送报告车辆数',
    resport4: '最近{0}天第1次发送报告车辆数',
  },
}

export default dataCenter
