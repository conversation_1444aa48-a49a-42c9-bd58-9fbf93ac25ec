<template>
  <div>
    <v-card class="elevation-1">
      <v-tabs
        :value="typeMap[type]"
        background-color="transparent"
        left
        @change="onTypeChange"
      >
        <v-tabs-slider></v-tabs-slider>
        <v-tab v-for="item in typeOptions" :key="item.value">
          <span class="pl-2 text-sm font-weight-semibold">{{
            item.label
          }}</span>
        </v-tab>
      </v-tabs>

      <v-tabs-items v-model="typeMap[type]">
        <v-tab-item v-for="item in typeOptions" :key="item.value">
          <!-- <v-card class="card-box my-4 mx-4"> -->
          <v-card-text class="py-4">
            <!-- <div class="mt-4"></div> -->
            <keep-alive>
              <component
                :is="currentComponent"
                ref="picker"
                @change="onChange"
              />
            </keep-alive>
          </v-card-text>
          <!-- </v-card> -->
        </v-tab-item>
      </v-tabs-items>
    </v-card>
  </div>
</template>

<script>
import CronAdvanced from './CronAdvanced.vue'
import CronDay from './CronDay.vue'
import CronHour from './CronHour.vue'
import CronMinute from './CronMinute.vue'
import CronMonth from './CronMonth.vue'
import CronWeek from './CronWeek.vue'

export default {
  name: 'CronPicker',
  components: {
    CronMinute,
    CronHour,
    CronDay,
    CronWeek,
    CronMonth,
    CronAdvanced,
  },
  props: {
    // 调度周期
    interval: {
      type: String,
      default: 'hour',
    },
    // Cron 表达式
    cron: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      type: 'hour',

      typeMap: {
        hour: 0,
        day: 1,
        week: 2,
        month: 3,
        advanced: 4,
      },
      // typeMap: {
      //   minute: 0,
      //   hour: 1,
      //   day: 2,
      //   week: 3,
      //   month: 4,
      //   advanced: 5,
      // },
    }
  },
  computed: {
    typeOptions() {
      return [
        {
          value: 'hour',
          label: this.$t('infer.expression.hour'),
        },
        {
          value: 'day',
          label: this.$t('infer.expression.day'),
        },
        {
          value: 'week',
          label: this.$t('infer.expression.week'),
        },
        {
          value: 'month',
          label: this.$t('infer.expression.month'),
        },
        {
          value: 'advanced',
          label: this.$t('infer.expression.custom'),
        },
      ]
    },
    currentComponent() {
      if (this.typeOptions[this.type]) {
        return 'cron-' + this.typeOptions[this.type].value
      }
      return 'cron-' + this.type
    },
  },
  watch: {
    interval: {
      handler: function (newVal) {
        // this.type = newVal || 'minute'
        this.type = newVal || 'hour'
      },
      immediate: true,
    },
    cron: {
      handler: function (newVal) {
        if (newVal) {
          this.$nextTick(() => {
            this.$refs['picker'] && this.$refs['picker'][0].init(newVal)
            // this.$refs.picker && this.$refs.picker.init(newVal)
          })
        }
      },
      immediate: true,
    },
  },
  methods: {
    onTypeChange(tab) {
      this.type = this.typeOptions[tab].value
      this.$nextTick(() => {
        const cron = this.$refs['picker'] && this.$refs['picker'][0].cronExp
        this.$emit('change', {
          interval: this.typeOptions[tab].value,
          cron,
        })
      })
    },
    onChange(cron) {
      this.$emit('change', {
        interval: this.interval,
        cron,
      })
    },
  },
}
</script>

<style lang="scss" scoped></style>
