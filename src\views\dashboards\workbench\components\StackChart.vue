<template>
  <common :title="title" v-on="$listeners" @refresh="obj => (params = obj)">
    <template #subTitle>
      {{ total | numberToFormat }}
    </template>
    <template #chart>
      <vsoc-chart
        :echartId="echartId"
        :option="chartOption"
        :isYDashed="true"
      ></vsoc-chart>
    </template>
  </common>
</template>

<script>
import VsocChart from '@/components/VsocChart.vue'
import store from '@/store'
import { toDate } from '@/util/filters'
import { getVuetify } from '@core/utils'
import { addDays, differenceInDays } from 'date-fns'
import { sum, sumBy } from 'lodash'
import { computed, ref } from 'vue-demi'
import Common from './Common.vue'
import { defaultQueryFn, grid, textStyle, tooltip } from './constant'
export default {
  components: {
    VsocChart,
    Common,
  },
  props: {
    list: {
      type: [Array],
      default: () => [],
    },
    echartId: {
      type: String,
      default: () => 'echart',
    },
    title: {
      type: String,
    },
    type: {
      type: String,
      default: 'alert',
    },
  },
  setup(props, ctx) {
    const $vuetify = getVuetify()

    const baseSeries = {
      name: 'Email',
      type: 'bar',
      stack: 'total-profit',
      barWidth: 10,
      barMinHeight: 15,

      chip: false,
      emphasis: {
        focus: 'series',
      },
      itemStyle: {
        borderRadius: 10,
        borderWidth: 2, // 堆叠间隔
        borderColor: 'transparent',
        borderJoin: 'round',
        borderType: 'solid',
      },
      data: [],
    }

    const alarmLevel = computed(() => store.state.enums.enums.AlarmLevel)
    let params = ref(defaultQueryFn())
    let total = ref(0)
    // watch(
    //   () => props.list,
    //   newArr => {
    //     let diffDay = differenceInDays(
    //       new Date(params.value.endDate),
    //       new Date(params.value.startDate),
    //     )
    //     let xAxisData = []
    //     for (let i = 0; i <= diffDay; i++) {
    //       const cur = toDate(
    //         addDays(new Date(params.value.startDate), i),
    //         ISO_FORMAT,
    //       )
    //       xAxisData.push(cur)
    //     }
    //     let sumList = []
    //     chartOption.value.series = newArr.map(item => {
    //       const sum = sumBy(item.arrays, v => v.number)
    //       sumList.push(sum)
    //       return {
    //         ...baseSeries,
    //         name: item.alarmName || item.prioritiesName,
    //         data: item.arrays.map(s => {
    //           return [
    //             xAxisData.findIndex(
    //               v => v === toDateByTimeZone(s.time, 1, 'yyyy-MM-dd'),
    //             ),
    //             Number(s.number),
    //           ]
    //         }),
    //       }
    //     })
    //     total.value = sum(sumList)

    //     chartOption.value.xAxis[0].data = xAxisData.map(date =>
    //       toDate(date, 'MM/dd'),
    //     )

    //     // // : ['#0EAA9F', '#dbf2f1']

    //     // const colorList = chroma
    //     //   .scale(baseColor)
    //     //   .mode('lch')
    //     //   .correctLightness()
    //     //   .colors(6)
    //     // const colorList = chroma.scale(baseColor).classes(5).colors(6)
    //     const colorList =
    //       props.type === 'alert'
    //         ? ['#D9ECF9', '#A6D3F1', '#4DA7E2', '#0082D6']
    //         : ['#E7F7F6', '#CFEEEC', '#9FDDD9', '#6ECCC5', '#3EBBB2', '#0EAA9F']

    //     chartOption.value.color = colorList
    //   },
    //   { immediate: true, deep: true },
    // )

    const chartOption = computed(() => {
      const startDate = new Date(params.value.startDate)
      const endDate = new Date(params.value.endDate)

      let diffDay = differenceInDays(endDate, startDate)
      let xAxisData = []
      for (let i = 0; i <= diffDay; i++) {
        const cur = addDays(startDate, i)
        xAxisData.push(cur)
      }

      let sumList = []
      const series = props.list.map(item => {
        const sum = sumBy(item.arrays, v => v.number)
        sumList.push(sum)
        return {
          ...baseSeries,
          name: item.alarmName || item.prioritiesName,
          data: item.arrays.map(s => {
            const index = xAxisData.findIndex(
              v => toDate(v, 'yyyy-MM-dd') === s.time,
            )
            return [index, Number(s.number)]
          }),
        }
      })
      total.value = sum(sumList)

      // // : ['#0EAA9F', '#dbf2f1']

      // const colorList = chroma
      //   .scale(baseColor)
      //   .mode('lch')
      //   .correctLightness()
      //   .colors(6)
      // const colorList = chroma.scale(baseColor).classes(5).colors(6)
      const colorList =
        props.type === 'alert'
          ? store.state.appConfig.appPrimary === 'lotus'
            ? ['#FCFBDA', '#F9F5AA', '#F2EB54', '#FFF200']
            : ['#D9ECF9', '#A6D3F1', '#4DA7E2', '#0082D6']
          : ['#E7F7F6', '#CFEEEC', '#9FDDD9', '#6ECCC5', '#3EBBB2', '#0EAA9F']

      return {
        color: colorList,
        tooltip: {
          ...tooltip,
          formatter: '',
        },
        textStyle,
        // 图例设置
        legend: {
          show: false,
          right: 0,
          icon: 'circle',
          itemHeight: 6, // 修改icon图形大小
          itemGap: 24,
          textStyle: {
            padding: [0, 0, 0, -8], // 修改文字和图标距离
          },
        },
        grid,
        xAxis: [
          {
            type: 'category',
            data: xAxisData.map(date => toDate(date, 'MM/dd')),
            // 隐藏坐标轴刻度
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
          },
        ],
        yAxis: [
          {
            type: 'value',
            itemStyle: {
              borderRadius: 50,
            },
          },
        ],
        series,
      }
    })
    return {
      chartOption,
      params,
      total,
    }
  },
}
</script>
