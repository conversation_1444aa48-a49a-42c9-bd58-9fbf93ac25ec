<template>
  <div class="c-box pb-0">
    <div class="c-box-header">{{ $t('screen.cloud.left1.title') }}</div>

    <v-row
      no-gutters
      class="box-chart d-flex justify-space-around w-100"
      style="margin-top: 16%; margin-left: 1%; margin-bottom: 2%"
    >
      <v-col cols="6">
        <!-- Attacks Today -->
        <div class="fs-18 opacity-8">安全告警</div>
        <div>
          <!-- <count-to
            class="c-left1-num"
            :startVal="0"
            :endVal="risks.today.num"
            :duration="3600"
          ></count-to> -->
          <!-- <span class="c-left1-num">{{ risks.today.num }}</span>
          <span class="c-left1-unit">{{ risks.today.unit }}</span> -->
          <dv-digital-flop
            v-resize="onResize"
            ref="today"
            :config="todayConfig"
          ></dv-digital-flop>
        </div>
      </v-col>

      <v-col cols="6">
        <div class="fs-18 opacity-8">失陷资产</div>
        <dv-digital-flop ref="day7" :config="day7Config"></dv-digital-flop>
        <!-- <div>
          <span class="c-left1-num">{{ risks.day7.num }}</span>
          <span class="c-left1-unit">{{ risks.day7.unit }}</span>
        </div> -->
      </v-col>
      <v-col cols="6">
        <div class="fs-18 opacity-8">跨境通联</div>
        <dv-digital-flop
          ref="leakedDomain"
          :config="leakedDomainConfig"
        ></dv-digital-flop>
        <!-- <div>
          <span class="c-left1-num">{{ risks.leakedDomain.num }}</span>
          <span class="c-left1-unit">{{ risks.leakedDomain.unit }}</span>
        </div> -->
      </v-col>

      <v-col cols="6">
        <div class="fs-18 opacity-8">风险企业</div>
        <!-- <div>
          <count-to
            ref="count1"
            class="c-left1-num ml-0"
            :startVal="0"
            :endVal="risks.events.num"
            :duration="duration"
          ></count-to>
          <span class="c-left1-unit">{{ risks.events.unit }}</span>
        </div> -->
        <dv-digital-flop ref="events" :config="eventsConfig"></dv-digital-flop>
      </v-col>
      <v-col cols="6">
        <div class="fs-18 opacity-8">安全漏洞</div>
        <dv-digital-flop ref="events" :config="safeConfig"></dv-digital-flop>
        <!-- <div class="d-flex align-center">
          <dv-digital-flop class="w-50" :config="safeConfig"></dv-digital-flop>
          <div class="c-left1-unit">{{ risks.safe.unit }}</div>
        </div> -->
      </v-col>
      <v-col cols="6"></v-col>
    </v-row>
  </div>
</template>

<script>
import { numberToFormat } from '@/util/filters'
import countTo from 'vue-count-to'
import { getRoundSize, primary } from './chart'
export default {
  name: 'LeftCloud1',
  props: {
    list: {
      type: Object,
      default: () => {
        return {
          attacksToday: 3.748 * Math.pow(10, 3), //今日告警条数
          attacksInSevenDays: 1.23 * Math.pow(10, 6), //近7天告警条数
          leakedInternetDomain: 1.56 * Math.pow(10, 6), //互联网暴漏的有效域名条数
          securityBaselineItems: 0, //安全基线配置项总数
          continuousDaysOfSafeOperations: 256, //持续安全运营天数
        }
      },
    },
  },
  components: {
    countTo,
  },
  computed: {
    risks() {
      if (this.$refs.count1 && this.$refs.count2) {
        this.start()
      }
      return {
        today: {
          ...numberToFormat(this.list.attacksToday, 'Object'),
        },
        day7: {
          ...numberToFormat(this.list.attacksInSevenDays, 'Object'),
        },
        leakedDomain: {
          ...numberToFormat(this.list.leakedInternetDomain, 'Object'),
        },
        events: {
          num: this.list.securityBaselineItems,
          // unit: 'Events',
        },
        safe: {
          num: this.list.continuousDaysOfSafeOperations,
          unit: this.$t('screen.cloud.days'),
        },
      }
    },
    baseConfig() {
      return {
        content: '{nt}',
        formatter: numberToFormat,
        textAlign: 'left',
        style: {
          fontSize: getRoundSize(40),
          lineHeight: getRoundSize(56),
          fill: primary,
          fontWeight: 600,
          textAlign: 'center',
          fontStyle: 'normal',
          fontFamily:
            '"PingFang SC", "Source Han Sans CN", "STHeiti", "Microsoft YaHei"',
        },
      }
    },
    todayConfig() {
      return {
        number: [this.attacksToday],
        ...this.baseConfig,
      }
    },
    day7Config() {
      return {
        number: [this.attacksInSevenDays],
        ...this.baseConfig,
      }
    },
    leakedDomainConfig() {
      return {
        number: [this.leakedInternetDomain],
        ...this.baseConfig,
      }
    },
    eventsConfig() {
      return {
        number: [this.securityBaselineItems],
        ...this.baseConfig,
      }
    },
    safeConfig() {
      return {
        number: [this.continuousDaysOfSafeOperations],
        ...this.baseConfig,
        content: '{nt}',
      }
    },
  },
  watch: {
    list() {
      this.toggleNum()
    },
  },
  data() {
    return {
      duration: 2500,
      attacksToday: 3.748 * Math.pow(10, 3), //今日告警条数
      attacksInSevenDays: 1.23 * Math.pow(10, 6), //近7天告警条数
      leakedInternetDomain: 1.56 * Math.pow(10, 6), //互联网暴漏的有效域名条数
      securityBaselineItems: 0, //安全基线配置项总数
      continuousDaysOfSafeOperations: 256, //持续安全运营天数
    }
  },
  methods: {
    start() {
      this.$refs.count1.start()
      this.$refs.count2.start()
    },
    onResize() {
      this.toggleResize('today')
      this.toggleResize('leakedDomain')
      this.toggleResize('day7')
      this.toggleResize('events')
      this.toggleNum()
    },
    toggleResize(refName) {
      const ref = this.$refs[refName]
      if (ref) {
        ref.renderer = null
        ref.init()
      }
    },
    async toggleNum() {
      this.attacksToday = 0
      this.attacksInSevenDays = 0
      this.leakedInternetDomain = 0
      this.securityBaselineItems = 0
      this.continuousDaysOfSafeOperations = 0

      this.attacksToday = await Promise.resolve(this.list.attacksToday)
      this.attacksInSevenDays = await Promise.resolve(
        this.list.attacksInSevenDays,
      )
      this.leakedInternetDomain = await Promise.resolve(
        this.list.leakedInternetDomain,
      )
      this.securityBaselineItems = await Promise.resolve(
        this.list.securityBaselineItems,
      )
      this.continuousDaysOfSafeOperations = await Promise.resolve(
        this.list.continuousDaysOfSafeOperations,
      )
    },
  },
}
</script>
