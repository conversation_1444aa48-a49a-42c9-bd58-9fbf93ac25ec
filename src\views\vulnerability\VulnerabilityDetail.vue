<!-- CNVD 漏洞详情 -->
<template>
  <div>
    <bread-crumb>
      <template slot="title">
        <div class="d-flex align-center color-base text-title">
          <v-chip
            v-if="vulnerabilityLevelEnums[form.vulnerabilityLevel]"
            :color="vulnerabilityLevelEnums[form.vulnerabilityLevel].color"
            class="rounded-lg"
          >
            <vsoc-icon
              size="x-large"
              icon="icon-loudongdengjibiaozhi"
              type="fill"
            ></vsoc-icon>
            <span class="ml-1">{{
              vulnerabilityLevelEnums[form.vulnerabilityLevel].text
            }}</span>
          </v-chip>

          <span class="ml-2 text-no-wrap">{{ form.cnvdId }}</span>
          <span class="ml-1 text-no-wrap">漏洞详情</span>
        </div>
      </template>
      <template slot="left">
        <div
          class="d-flex justify-space-between align-center pl-8 text-overflow-hide"
        >
          <!-- <v-chip
            class="rounded-sm font-weight-bold"
            :color="vulnerabilityLevelEnums[form.vulnerabilityLevel].color"
            text-color="white"
          >
            {{ form.vulnerabilityLevelName }}
          </v-chip> -->
          <div
            class="ml-2 text-title font-weight-medium color-base text-overflow-hide"
          >
            {{ form.vulnerabilityName }}
          </div>
        </div>
      </template>
      <v-btn
        color="primary"
        width="76"
        min-width="76"
        elevation="0"
        @click="addTicket"
      >
        转工单
      </v-btn>
    </bread-crumb>

    <v-card tile class="h-100 overflow-y-auto" elevation="0">
      <v-card-text class="pa-0">
        <!-- <hr class="horizontal dark" /> -->
        <div
          class="overflow-y px-10"
          :style="{ height: `calc(100vh - ${topHeaderHeight}px)` }"
        >
          <h6
            class="d-flex align-center text-sm-h6 text-typo font-weight-bolder mb-2 my-6"
          >
            <span class="text-title font-weight-medium">{{
              $t('global.drawer.baseInfo')
            }}</span>
          </h6>

          <div class="mx-6 mt-6">
            <v-form>
              <v-row>
                <v-col cols="4">
                  <!-- <v-text-field
                    v-model="form.cnvdId"
                    :label="$t('vulnerability.headers.cnvdId')"
                    hide-details
                    disabled
                  ></v-text-field> -->
                  <div class="text-root-base accent--text">
                    {{ $t('vulnerability.headers.cnvdId') }}
                  </div>
                  <div class="text-content color-base mt-1 font-weight-medium">
                    {{ form.cnvdId }}
                  </div>
                </v-col>
                <v-col cols="4">
                  <!-- <v-text-field
                    v-model="form.cveId"
                    :label="$t('vulnerability.headers.cveId')"
                    hide-details
                    disabled
                  ></v-text-field> -->
                  <div class="text-root-base accent--text">
                    {{ $t('vulnerability.headers.cveId') }}
                  </div>
                  <div class="text-content color-base mt-1 font-weight-medium">
                    {{ form.cveId }}
                  </div>
                </v-col>

                <v-col cols="4">
                  <!-- <v-text-field
                    :value="form.cvss | dataFilter"
                    :label="$t('vulnerability.headers.cvss')"
                    hide-details
                    disabled
                  ></v-text-field> -->
                  <div class="text-root-base accent--text">
                    {{ $t('vulnerability.headers.cvss') }}
                  </div>
                  <div class="text-content color-base mt-1 font-weight-medium">
                    {{ form.cvss | dataFilter }}
                  </div>
                </v-col>

                <v-col cols="4">
                  <!-- <v-text-field
                    :value="form.loopholeTypeName | dataFilter"
                    :label="$t('vulnerability.headers.loopholeType')"
                    hide-details
                    disabled
                  ></v-text-field> -->
                  <div class="text-root-base accent--text">
                    {{ $t('vulnerability.headers.loopholeType') }}
                  </div>
                  <div class="text-content color-base mt-1 font-weight-medium">
                    {{ form.loopholeTypeName | dataFilter }}
                  </div>
                </v-col>

                <v-col cols="4">
                  <!-- <v-text-field
                    :value="form.recordTime | toDate | dataFilter"
                    :label="$t('vulnerability.headers.recordTime')"
                    hide-details
                    disabled
                  ></v-text-field> -->
                  <div class="text-root-base accent--text">
                    {{ $t('vulnerability.headers.recordTime') }}
                  </div>
                  <div class="text-content color-base mt-1 font-weight-medium">
                    {{ form.recordTime | toDate | dataFilter }}
                  </div>
                </v-col>
                <v-col cols="4">
                  <!-- <v-text-field
                    :value="form.publicTime | toDate | dataFilter"
                    :label="$t('vulnerability.headers.publicTime')"
                    hide-details
                    disabled
                  ></v-text-field> -->
                  <div class="text-root-base accent--text">
                    {{ $t('vulnerability.headers.publicTime') }}
                  </div>
                  <div class="text-content color-base mt-1 font-weight-medium">
                    {{ form.publicTime | toDate | dataFilter }}
                  </div>
                </v-col>

                <v-col cols="4">
                  <!-- <v-text-field
                    :value="form.updateTime | toDate | dataFilter"
                    :label="$t('vulnerability.headers.updateTime')"
                    hide-details
                    disabled
                  ></v-text-field> -->
                  <div class="text-root-base accent--text">
                    {{ $t('vulnerability.headers.updateTime') }}
                  </div>
                  <div class="text-content color-base mt-1 font-weight-medium">
                    {{ form.updateTime | toDate | dataFilter }}
                  </div>
                </v-col>
                <v-col cols="4">
                  <!-- <v-text-field
                    v-model="form.dataSourceName"
                    :label="$t('vulnerability.headers.dataSource')"
                    hide-details
                    disabled
                  ></v-text-field> -->
                  <div class="text-root-base accent--text">
                    {{ $t('vulnerability.headers.dataSource') }}
                  </div>
                  <div class="text-content color-base mt-1 font-weight-medium">
                    {{ form.dataSourceName }}
                  </div>
                </v-col>

                <v-col cols="4">
                  <!-- <v-text-field
                    :value="form.createTime | toDate | dataFilter"
                    :label="$t('global.createDate')"
                    hide-details
                    disabled
                  ></v-text-field> -->
                  <div class="text-root-base accent--text">
                    {{ $t('global.createDate') }}
                  </div>
                  <div class="text-content color-base mt-1 font-weight-medium">
                    {{ form.createTime | toDate | dataFilter }}
                  </div>
                </v-col>
                <v-col cols="12">
                  <!-- <v-text-field
                    :value="form.createUser | dataFilter"
                    :label="$t('global.createUser')"
                    hide-details
                    disabled
                  ></v-text-field> -->
                  <div class="text-root-base accent--text">
                    {{ $t('global.createUser') }}
                  </div>
                  <div class="text-content color-base mt-1 font-weight-medium">
                    {{ form.createUser | dataFilter }}
                  </div>
                </v-col>
              </v-row>
            </v-form>
            <v-divider class="mt-4 divider--dashed"></v-divider>
            <h6
              class="d-flex align-center text-sm-h6 text-typo font-weight-bolder mb-2 mt-6"
            >
              <span class="text-root-base accent--text">{{
                $t('vulnerability.edit.desc')
              }}</span>
            </h6>
            <p class="text-content color-base font-weight-medium">
              {{ form.description }}
            </p>
            <v-divider class="mt-6 divider--dashed"></v-divider>
            <h6
              class="d-flex align-center text-sm-h6 text-typo font-weight-bolder mb-2 mt-6"
            >
              <span class="text-root-base accent--text">{{
                $t('vulnerability.edit.title2')
              }}</span>

              <v-icon
                class="ml-2"
                v-show-tips="$t('vulnerability.tip')"
                size="16"
                >mdi-help-circle-outline</v-icon
              >
            </h6>

            <v-chip
              v-for="(item, index) in form.influenceProduct"
              :key="index + 'influenceProduct'"
              class="mr-3 mt-3"
            >
              {{ item }}
            </v-chip>
            <v-divider class="mt-6 divider--dashed"></v-divider>

            <h6
              class="d-flex align-center text-sm-h6 text-typo font-weight-bolder mb-2 mt-6"
            >
              <span class="text-root-base accent--text">{{
                $t('vulnerability.repair')
              }}</span>
            </h6>
            <p class="text-content color-base font-weight-medium">
              {{ form.solution }}
            </p>
            <v-divider class="mt-6 divider--dashed"></v-divider>

            <h6
              class="d-flex align-center text-sm-h6 text-typo font-weight-bolder mb-2 mt-6"
            >
              <span class="text-root-base accent--text">{{
                $t('vulnerability.cvssInfo')
              }}</span>
            </h6>
            <span class="color-base text-content font-weight-medium">{{
              form.cvss || $t('vulnerability.cvssHint')
            }}</span>
            <!-- <v-chip class="mr-3"> 评分：{{ form.cvss | dataFilter }} </v-chip>
            <v-chip class="mr-3">
              评分向量：{{ form.cvssScore | dataFilter }}
            </v-chip>
            <v-chip class="mr-3">
              版本：{{ form.cvssVersion | dataFilter }}
            </v-chip> -->
            <v-divider class="mt-6 divider--dashed"></v-divider>

            <h6
              class="d-flex align-center text-sm-h6 text-typo font-weight-bolder mb-2 mt-6"
            >
              <span class="text-root-base accent--text">{{
                $t('vulnerability.link')
              }}</span>
            </h6>
            <p v-for="(url, i) in form.referenceUrl" :key="i + 'url'">
              <a
                v-if="showUrl(url)"
                class="text-content font-weight-medium d-block"
                :href="url"
                target="_blank"
                >{{ url | dataFilter }}</a
              >
              <span
                v-else
                class="text--primary text-content font-weight-medium"
                >{{ url | dataFilter }}</span
              >
            </p>
            <v-divider class="mt-6 divider--dashed"></v-divider>

            <h6
              class="d-flex align-center text-sm-h6 text-typo font-weight-bolder mb-2 mt-6"
            >
              <span class="text-root-base accent--text">{{
                $t('vulnerability.patch')
              }}</span>
            </h6>
            <p>
              <a
                v-if="showUrl(form.patchUrl)"
                class="text-content font-weight-medium"
                :href="form.patchUrl"
                target="_blank"
                >{{ form.patch | dataFilter }}</a
              >
              <span
                v-else
                class="text--primary text-content font-weight-medium"
                >{{ form.patch | dataFilter }}</span
              >
            </p>
            <v-divider class="mt-6 divider--dashed"></v-divider>

            <h6
              class="d-flex align-center text-sm-h6 text-typo font-weight-bolder mb-2 mt-6"
            >
              <span class="text-root-base accent--text">{{
                $t('vulnerability.cveLink')
              }}</span>
            </h6>
            <p class="mb-6">
              <a
                v-if="showUrl(form.cveIdUrl)"
                class="text-content font-weight-medium"
                :href="form.cveIdUrl"
                target="_blank"
                >{{ form.cveId | dataFilter }}</a
              >
              <span
                v-else
                class="text--primary font-weight-medium text-content"
                >{{ form.cveId | dataFilter }}</span
              >
            </p>
          </div>
        </div>
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
import { getVulnerabilityDetail } from '@/api/vulnerability/index'
import breadCrumb from '@/components/bread-crumb/index'
import { setLocalStorage } from '@/util/localStorage'
export default {
  name: 'VulnerabilityDetail',
  components: {
    breadCrumb,
  },
  data() {
    return {
      form: {
        vulnerabilityName: 'MediaWiki限制绕过漏洞',
        description:
          "MediaWiki是用PHP编写的免费软件开源wiki包，最初用于维基百科，现在也被非营利Wikimedia基金会的若干其他项目和许多其他wikis使用。 MediaWiki存在限制绕过漏洞，该漏洞源于'user'的'$wgRateLimits' rate limiter条目会覆盖'newbie'用户的设置，远程认证用户可利用该漏洞绕过预期的限制。",
        loopholeType: '0',
        publicDate: '2019-06-24T00:00:00.000+0000',
        patch: 'MediaWiki限制绕过漏洞的补丁',
        patchUrl:
          '厂商已发布了漏洞修复程序，请及时关注更新： https://lists.wikimedia.org/pipermail/mediawiki-announce/2018-September/000223.html',
        solution:
          "MediaWiki是用PHP编写的免费软件开源wiki包，最初用于维基百科，现在也被非营利Wikimedia基金会的若干其他项目和许多其他wikis使用。 MediaWiki存在限制绕过漏洞，该漏洞源于'user'的'$wgRateLimits' rate limiter条目会覆盖'newbie'用户的设置，远程认证用户可利用该漏洞绕过预期的限制。目前，供应商发布了安全公告及相关补丁信息，修复了此漏洞。",
        attachment: null,
        cvssScore: null,
        cvssVersion: null,
        vulnerabilityLevel: '1',
        id: 1,
        verification: null,
        cnvdId: 'CNVD-2019-18907',
        cveIdUrl: 'null',
        cveId: 'CVE-2018-0503',
        loopholeTypeName: '通用软硬件漏洞',
        updateTime: null,
        vulnerabilityLevelName: '中危',
        dataSourceName: 'cnvd',
        referenceUrl: 'https://securitytracker.com/id/1041695',
        submitTime: '2018-09-21T00:00:00.000+0000',
        recordTime: null,
        influenceProduct: [
          'MediaWiki Mediawiki 1.31',
          'MediaWiki Mediawiki 1.30.1',
          'MediaWiki Mediawiki 1.29.3',
          'MediaWiki Mediawiki 1.27.5',
        ],
        createTime: '2023-05-19T02:41:16.000+0000',
        createUser: null,
        dataSource: '1',
        cvss: null,
      },
      recordShow: true,
      bugInfo: {
        value: 1,
        level: 0,
        levelText: '超危',
        title: 'Google Chrome 资源管理错误漏洞',
        qnx: 'QNX SDP <= 6.5.0SP1',
      },

      // 漏洞影响详情表格信息
      impactAssetsHeader: [
        {
          text: '影响资产',
          value: 'assetsId',
          width: '20%',
        },
        {
          text: '影响模块',
          value: 'model',
          width: '20%',
        },
        {
          text: '供应商',
          value: 'vendor',
          width: '20%',
        },
        {
          text: '版本',
          value: 'version',
          width: '15%x',
        },
        {
          text: '扫描时间',
          value: 'time',
        },
      ],

      // 漏洞影响详情搜索
      impactSearchVal: '',
      showImpactSearch: false,
      page: 0,
      bugProof: {
        prove: '',
      },
      bugStatus: ['待处置', '处置中', '复测通过', '已处置'],
      showEditBug: false,
      editBugInfo: {
        status: '待处置',
        user: 'Gideon Ge',
      },
      editorOption: {
        placeholder: '',
        modules: {
          toolbar: [
            ['image'],
            // ['bold', 'italic', 'underline'], // 加粗，斜体，下划线
            // [{ list: 'ordered' }, { list: 'bullet' }], // 列表
            // [{ align: [] }], // 对齐方式
            // [{ 'size': ['small', false, 'large', 'huge'] }], // 字体大小
            // [{ 'font': [false, 'serif', 'monospace'] }] // 字体
          ],
        },
      },
      timeline: [
        {
          name: 'FZ',
          title: '漏洞举证',
          date: '2022/5/20 16:03',
          content:
            '这个漏洞已经修复，这个漏洞已经修复，这个漏洞已经修复，这个漏洞已经修复，这个漏洞已经修复，这个漏洞已经修复，这个漏洞已经修复，这个漏洞已经修复，',
        },
        {
          name: 'FZ',
          title: '漏洞举证',
          date: '2022/5/20 16:03',
          content:
            '漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，',
        },
        {
          name: 'GQ',
          title: '漏洞举证',
          date: '2022/5/20 16:03',
          content:
            '漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，',
        },
        {
          name: 'GQ',
          title: '漏洞举证',
          date: '2022/5/20 16:03',
          content:
            '漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，',
        },
        {
          name: 'GQ',
          title: '漏洞举证',
          date: '2022/5/20 16:03',
          content:
            '漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，漏洞证明及原理分析，',
        },
      ],

      query: {
        page: 1,
        size: 10,
        loading: false,
      },
      tableDataTotal: 10,
    }
  },
  computed: {
    topHeaderHeight() {
      return this.$store.getters['global/getTopHeaderHeight']
    },
    vulnerabilityLevelEnums() {
      return this.$store.state.enums.enums.VulnerabilityLevel
    },
  },
  created() {
    this.loadDetail()
  },
  methods: {
    //转工单
    addTicket() {
      // 0高1中2低 0严重 1高 2中 3低
      const params = {
        priority: (Number(this.form.vulnerabilityLevel) + 1).toString(),
        title:
          this.form.cnvdId +
          '_' +
          this.form.vulnerabilityLevelName +
          '_' +
          this.form.vulnerabilityName, //漏洞编号_漏洞等级_漏洞名称
        ticketContent: this.form.description,
        dataSource: '1',
        relationId: this.form.cnvdId,
      }
      setLocalStorage('alertTicket', JSON.stringify(params))
      // localStorage.setItem('alertTicket', JSON.stringify(params))
      this.$router.push('/ticket/addTicket?type=1')
    },
    showUrl(url) {
      return url && url.trim().startsWith('http')
    },
    async loadDetail() {
      try {
        const params = {
          id: this.$route.query.id,
        }
        const { data } = await getVulnerabilityDetail(params)
        this.form = data
      } catch (err) {
        console.log('漏洞详情报错', err)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .v-text-field > .v-input__control > .v-input__slot:before {
  width: 0;
}
::v-deep .v-input--is-disabled input {
  color: var(--v-color-base);
}
.col-6 {
  padding: 0 1rem 1rem;
}
</style>
