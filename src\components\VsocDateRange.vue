<!-- 日期选择器 -->
<template>
  <div class="vsoc-date-range">
    <!-- 默认显示内容 -->
    <template v-if="!$slots.default">
      <div
        class="cursor-pointer text-fb"
        @click="show"
      >
        <v-icon
          v-if="showSuffixIcon"
          size="1.25rem"
          color="black"
        >
          mdi-calendar-blank-outline
        </v-icon>
        <span
          class="text-sm text--primary pl-2"
        >{{ `${value[0]} - ${value[1]}` }}</span>
        <!--<v-icon v-if="showSuffixIcon" size="1.5rem" class="material-icons-round rotate-icon mt-n1" :class="{'active': menu}">expand_more</v-icon>-->
      </div>
    </template>
    <!-- 插槽 -->
    <slot></slot>
    <!-- 选择器组件 -->
    <el-date-picker
      ref="datePicker"
      v-model="date"
      :value-format="valueFormat"
      :picker-options="pickerOptions"
      :type="type"
      :unlink-panels="unlinkPanels"
      :align="align"
      :default-time="['00:00:00', '23:59:59']"
      @blur="$_blur"
    >
    </el-date-picker>
  </div>
</template>

<script>
export default {
  name: 'VsocDateRange',
  components: {},
  props: {
    // 绑定的数据
    value: [Array, String],

    // 选择器类型
    type: {
      type: String,
      default: 'date',
    },

    // 详细配置（看饿了么文档）
    pickerOptions: Object,

    // 绑定数据格式
    valueFormat: {
      type: String,
      default: 'yyyy/MM/dd HH:mm:ss',
    },

    // 取消两个面板联动
    unlinkPanels: {
      type: Boolean,
      default: false,
    },
    align: {
      type: String,
      default: 'left',
    },
    showSuffixIcon: {
      type: Boolean,
      default: true,
    },

    // 最多选择多少天
    maxScope: {
      type: Number,
      default: 31,
    },

    // 最少选择多少天
    minScope: {
      type: Number,
      default: 7,
    },
  },
  data() {
    return {
      menu: false,
      date: [],
      dateToString: '',
    }
  },
  watch: {
    menu() {
      if (typeof this.value === 'object') {
        this.date = Object.assign([], this.value)
        this.dateToString = this.date.toString()
      } else {
        this.date = this.value || ''
        this.dateToString = this.date
      }
    },

    date(newVal) {
      if (this.type === 'daterange' || this.type === 'datetimerange') {
        const res = this.$_handleDateScope(newVal[0], newVal[1])
        if (!res) return
      }
      this.$emit('input', newVal)
      if (newVal.toString() !== this.dateToString) {
        this.$emit('change', newVal)
      }
      this.dateToString = newVal.toString()
    },
  },
  created() {},
  methods: {
    show() {
      if (!this.menu) {
        this.$refs.datePicker.focus()
        this.menu = true
      }
    },
    $_blur() {
      // 让这个方法执行顺序在show之后
      setTimeout(() => {
        this.$emit('blur')
        this.menu = false
      }, 0)
    },
    $_handleDateScope(start, end) {
      const days = parseInt((new Date(end).getTime() - new Date(start).getTime()) / (1000 * 60 * 60 * 24))
      if (days > this.maxScope) {
        this.$notify.info('error', `日期范围应在${this.maxScope}天内`)

        return false
      }

      if (days < this.minScope) {
        this.$notify.info('error', `日期范围应大于${this.minScope}天`)

        return false
      }

      return true
    },
  },
}
</script>

<style lang="scss" scoped>
.vsoc-date-range {
  position: relative;
  // 把原生饿了么组件从视觉上隐藏
  ::v-deep .el-date-editor {
    position: absolute;
    left: 0;
    bottom: 0;
    z-index: -1;
    opacity: 0;
  }
}
</style>
