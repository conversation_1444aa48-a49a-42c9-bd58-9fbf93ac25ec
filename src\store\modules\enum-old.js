import { getEnumDt } from '@/api/system/index-dt'
import { activeColor, alertColor, inactiveColor } from '@/plugins/systemColor'

const enums = {
  // 操作状态
  OperateStatus: {
    0: {
      text: '成功',
      value: '0',
      icon: 'mdi-check-circle',
      color: activeColor,
    },
    1: {
      text: '失败',
      value: '1',
      icon: 'mdi-close-circle',
      color: inactiveColor,
    },
  },
  // 操作类型
  OperateTypes: {
    0: {
      text: '新增',
      value: '0',
      icon: 'mdi-plus',
    },
    1: {
      text: '修改',
      icon: 'mdi-pencil',
      value: '1',
    },
    2: {
      text: '删除',
      icon: 'mdi-delete',
      value: '2',
    },
    3: {
      text: '授权',
      icon: 'mdi-account-details',
      value: '3',
    },
    4: {
      text: '导出',
      icon: 'mdi-export',
      value: '4',
    },
    5: {
      text: '导入',
      icon: 'mdi-import',
      value: '5',
    },
    6: {
      text: '强退',
      icon: 'mdi-database-export',
      value: '6',
    },
    7: {
      text: '查询',
      icon: 'mdi-magnify',
      value: '7',
    },
    8: {
      text: '其他',
      icon: 'mdi-dots-horizontal',
      value: '8',
    },
  },
  // 0是vehicle 1是app
  AssetType: {
    0: {
      value: '0',
      icon: 'mdi-car',
      text: 'Vehicle',
      key: '0',
      color: '#16B1FF',
    },
    1: {
      value: '1',
      icon: ' mdi-cellphone-link',
      text: 'App',
      key: '1',
      color: '#3caea3',
    },
  },
  AssetType2: {
    0: {
      value: '0',
      icon: 'mdi-car',
      text: 'Vehicles',
      key: '0',
      color: '#16B1FF',
    },
    1: {
      value: '1',
      icon: ' mdi-cellphone-link',
      text: 'Apps',
      key: '1',
      color: '#3caea3',
    },
    2: {
      value: '2',
      icon: ' mdi-cellphone-link',
      text: 'Servers',
      key: '2',
      color: '#3caea3',
    },
    3: {
      value: '3',
      icon: ' mdi-cellphone-link',
      text: 'Rsu',
      key: '3',
      color: '#3caea3',
    },
  },

  // 是否加密
  Encrypt: {
    1: {
      value: '1',
      icon: 'mdi-check',
      text: '加密',
    },
    0: {
      value: '0',
      icon: 'mdi-window-close',
      text: '不加密',
    },
  },

  ActiveStatus: {
    0: {
      text: '无效',
      key: '0',
      value: '0',
      icon: 'mdi-close-circle',
      color: inactiveColor,
    },
    1: {
      text: '有效',
      key: '1',
      value: '1',
      icon: 'mdi-check-circle',
      color: activeColor,
    },
  },

  // 0-严重/1-高/2-中/3-低/4-信息/5-测试
  AlarmLevel: {
    0: {
      color: alertColor[0],
      text: '严重',
      value: '0',
      svgUrl: require(`@/assets/images/svg/local-icon-${alertColor[1].slice(
        1,
      )}.svg`),
    },
    1: {
      color: alertColor[1],
      text: '高',
      value: '1',
      svgUrl: require(`@/assets/images/svg/local-icon-${alertColor[1].slice(
        1,
      )}.svg`),
    },
    2: {
      color: alertColor[2],
      text: '中',
      value: '2',
      svgUrl: require(`@/assets/images/svg/local-icon-${alertColor[2].slice(
        1,
      )}.svg`),
    },
    3: {
      color: alertColor[3],
      text: '低',
      value: '3',
      svgUrl: require(`@/assets/images/svg/local-icon-${alertColor[3].slice(
        1,
      )}.svg`),
    },
    4: {
      color: alertColor[4],
      text: '信息',
      value: '4',
      svgUrl: require(`@/assets/images/svg/local-icon-${alertColor[4].slice(
        1,
      )}.svg`),
    },
    5: {
      color: alertColor[5],
      text: '测试',
      value: '5',
      svgUrl: require(`@/assets/images/svg/local-icon-${alertColor[5].slice(
        1,
      )}.svg`),
    },
  },
  //工单优先级
  TicketLevel: {
    0: {
      color: alertColor[0],
      text: 'P0-严重',
      value: '0',
      svgUrl: require(`@/assets/images/svg/local-icon-${alertColor[1].slice(
        1,
      )}.svg`),
    },
    1: {
      color: alertColor[1],
      text: 'P1-高',
      value: '1',
      svgUrl: require(`@/assets/images/svg/local-icon-${alertColor[1].slice(
        1,
      )}.svg`),
    },
    2: {
      color: alertColor[2],
      text: 'P2-中',
      value: '2',
      svgUrl: require(`@/assets/images/svg/local-icon-${alertColor[2].slice(
        1,
      )}.svg`),
    },
    3: {
      color: alertColor[3],
      text: 'P3-低',
      value: '3',
      svgUrl: require(`@/assets/images/svg/local-icon-${alertColor[3].slice(
        1,
      )}.svg`),
    },
  },
  // 工单的状态 1待处理 2处理中 3已处理 4已驳回 5已取消 6已关闭
  TicketStatus: {
    0: {
      text: '待处理',
      color: 'rgb(0, 97, 250)',
      background: 'rgba(0, 97, 250, 0.2)',
      value: '0',
    },
    1: {
      text: '处理中',
      color: 'rgb(144, 20, 146)',
      background: 'rgba(144, 20, 146, 0.2)',
      value: '1',
    },
    2: {
      text: '已处理',
      color: 'rgb(3, 140, 214)',
      background: 'rgb(3, 140, 214,0.2)',
      value: '2',
    },
    3: {
      text: '已驳回',
      color: 'rgb(255, 76, 81)',
      background: 'rgb(255, 76, 81,0.2)',
      value: '3',
    },
    4: {
      text: '已取消',
      color: 'rgba(94, 86, 105);',
      background: 'rgb(94, 86, 105,0.2)',
      value: '4',
    },
    5: {
      text: '已关闭',
      color: 'rgb(60,175,163)',
      background: 'rgb(60,175,163,0.2)',
      value: '5',
    },
  },

  // 警告的状态：0待处理，1处理中，2有效事件，3误报，4不是问题，5重复警告
  AlarmStatus: {
    0: {
      text: '待处理',
      color: 'rgb(0, 97, 250)',
      background: 'rgba(0, 97, 250, 0.2)',
      num: 20,
    },
    1: {
      text: '处理中',
      color: 'rgb(144, 20, 146)',
      background: 'rgba(144, 20, 146, 0.2)',
      num: 85,
    },
    2: {
      text: '有效事件',
      color: 'rgb(0, 151, 167)',
      background: 'rgba(0, 151, 167, 0.2)',
      num: 30,
    },
    3: {
      text: '误报',
      color: 'rgb(68, 68, 68)',
      background: 'rgba(68, 68, 68, 0.2)',
      num: 30,
    },
    4: {
      text: '不是问题',
      color: 'rgb(97, 97, 97)',
      background: 'rgba(97, 97, 97, 0.2)',
      num: 50,
    },
    5: {
      text: '重复告警',
      color: 'rgb(209, 100, 152)',
      background: 'rgba(209, 100, 152, 0.2)',
      num: 2,
    },
  },

  // 0-单项资产，1-多项资产
  DetectorExtent: {
    0: {
      value: '0',
      text: '单资产',
      icon: 'mdi-checkbox-blank-circle',
    },
    1: {
      value: '1',
      text: '多资产',
      icon: 'mdi-checkbox-multiple-blank-circle',
    },
  },

  // 0-警报/1-高频
  DetectorType: {
    0: {
      value: '0',
      text: '警报',
      icon: 'mdi-flash-outline',
    },
    1: {
      value: '1',
      text: '高频',
      icon: 'mdi-apache-kafka',
    },
  },
  DisposeActionLogStatus: {
    fail: {
      text: '失败',
      color: '#f44335',
      background: 'rgba(244,67,53, 0.2)',
    },
    init: {
      text: '处理中',
      color: '#1a73e8',
      background: 'rgba(26,115,232, 0.2)',
    },
    success: {
      text: '成功',
      color: '#4caf50',
      background: 'rgba(76,175,80, 0.2)',
    },
  },
  HealthStatus: {
    0: {
      colorCss: 'success',
      color: alertColor[3],
      text: 'A',
      value: '0',
      text1: 'Strong (A)',
    },
    1: {
      colorCss: 'warning',
      color: alertColor[2],
      text: 'B',
      value: '1',
      text1: 'Good (B)',
    },
    2: {
      colorCss: 'danger',
      color: alertColor[1],
      text: 'C',
      value: '2',
      text1: 'Fair (C)',
    },
    3: {
      colorCss: 'error',
      color: alertColor[0],
      text: 'D',
      value: '3',
      text1: 'Poor (D)',
    },
  },

  KafkaType: {
    Internal: {
      text: '是',
      value: 'Internal',
    },
    External: {
      text: '否',
      value: 'External',
    },
  },

  // SignalType: {
  //   FieldMetadata: {
  //     text: '信号状态',
  //     value: 'FieldMetadata',
  //   },
  //   EventField: {
  //     text: '行为事件',
  //     value: 'EventField',
  //   },
  // },
  SignalType: {
    0: {
      text: '信号状态',
      value: 'FieldMetadata',
      key: '0',
    },
    1: {
      text: '行为事件',
      value: 'EventField',
      key: '1',
    },
  },
  SignalValueType: {
    STRING: {
      text: 'Text',
      value: 'STRING',
    },
    BOOLEAN: {
      text: 'Boolean',
      value: 'BOOLEAN',
    },
    DOUBLE: {
      text: 'Numeric',
      value: 'DOUBLE',
    },
    LONG: {
      text: 'Whole number',
      value: 'LONG',
    },
    TIMESTAMP: {
      text: 'Timestamp',
      value: 'TIMESTAMP',
    },
    DICTIONARY: {
      text: 'Structured',
      value: 'DICTIONARY',
    },
    OBJECT_ARRAY: {
      text: 'Structured list',
      value: 'OBJECT_ARRAY',
    },
    ARRAY: {
      text: 'Array',
      value: 'ARRAY',
    },
  },

  // 分类
  Classification: {
    'Telematics server message': {
      text: '远程信息服务器消息',
      value: 'Telematics server message',
    },
    'Vehicle message': {
      text: '车辆消息',
      value: 'Vehicle message',
    },
    'Update client action': {
      text: '程序更新行为',
      value: 'Update client action',
    },
    'Diagnostics device action': {
      text: '诊断设备行为',
      value: 'Diagnostics device action',
    },
    'Vehicle attribute': {
      text: '车辆特征',
      value: 'Vehicle attribute',
    },
    'In-vehicle action': {
      text: '车载式系统行为',
      value: 'In-vehicle action',
    },
    'Charging station action': {
      text: '充电桩行为',
      value: 'Charging station action',
    },
    'Remote access action': {
      text: '远程访问行为',
      value: 'Remote access action',
    },
  },

  // 流量管理-采集类型
  CollectionType: {
    0: {
      value: '0',
      text: '网联车辆消息',
    },
    1: {
      value: '1',
      text: '网联服务指令',
    },
    2: {
      value: '2',
      text: '移动app',
    },
  },
}
export default {
  namespaced: true,
  state: {
    enums,
  },
  getters: {
    getEncrypt(state) {
      return Object.assign([], state.enums.Encrypt).reverse()
    },
    getActiveStatus(state) {
      return Object.assign([], state.enums.ActiveStatus).reverse()
    },
  },
  actions: {
    /*
                async getEnums({ commit }) {
                    try {
                        const resEnums = {}
                        const res = await getEnum()
                        res.data.forEach(item => {
                            if (enums[item.type]) {
                                let obj = {}
                                item.values.forEach(v => {
                                    if (enums[item.type][v]) {
                                        obj[v] = enums[item.type][v]
                                    }
                                })
                                Object.keys(obj).length ? resEnums[item.type] = obj : ''
                            }
                        })
                        commit('setEnums', resEnums)
                    } catch (e) {
                        console.error(`获取枚举值错误：${e}`)
                    }
                }
        */
    async getEnums({ commit }) {
      try {
        const resEnums = {}
        const data = await getEnumDt()
        console.log('getEnums-store')
        data.forEach(item => {
          if (enums[item.type]) {
            const obj = {}
            item.values.forEach(v => {
              if (enums[item.type][v]) {
                obj[v] = enums[item.type][v]
              }
            })
            Object.keys(obj).length ? (resEnums[item.type] = obj) : ''
          }
        })
        commit('setEnums', resEnums)
      } catch (e) {
        console.error(`获取枚举值错误：${e}`)
      }
    },
  },
  mutations: {
    setEnums(state, val) {
      state.enums = val
    },
  },
}
