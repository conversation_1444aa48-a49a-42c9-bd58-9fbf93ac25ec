const menu = {
  currentTitle: 'Menu',
  headers: {
    name: 'Name',
    code: 'Coding',
    type: 'Type',
    sort: 'Sort',
    icon: 'Icon',
    path: 'Path',
  },
  drawer: {
    theme: 'Theme',
    show: 'Display State',
    type: 'Permission Type',
    enName: 'English Name',
    parent: 'Parent Node',
    component: 'Component',
    description: 'Description',
  },
  hint: {
    path: 'The url in the router needs to start with /',
    component: 'Component in router, do not start with /',
  },
  swal: {
    title: 'Delete Menu',
    text: 'Are you sure to delete the menu:{0}?',
  },
  boundMenu: 'Linked Menu',
}

export default menu
