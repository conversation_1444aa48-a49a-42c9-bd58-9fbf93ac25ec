<template>
  <v-card class="pa-2 pt-0 pr-0 d-flex flex-column h-full">
    <v-card-title :class="{ 'pb-0': type === 'ring' }">
      <span class="font-weight-semibold">{{ title }}</span>
      <span class="ml-2 text--secondary text-base">/</span>
      <span class="ml-2 text--secondary text-base">
        <slot name="subTitle"></slot>
      </span>
      <v-spacer></v-spacer>
      <span>
        <v-menu offset-y left nudge-bottom="5">
          <template v-slot:activator="{ on, attrs }">
            <v-btn
              elevation="0"
              rounded
              small
              v-bind="attrs"
              class="d-flex align-center text-base secondary--text bg-card"
              v-on="on"
            >
              <template>
                <span>{{ currentText }}</span>
                <v-icon class="ml-1">mdi-chevron-down</v-icon>
              </template>
            </v-btn>
          </template>
          <v-list class="pa-0">
            <v-list-item-group :value="currentDate" @change="onSelectData">
              <v-list-item
                active-class="bg-btn"
                v-for="item in dateOption"
                :key="item.value"
                :value="item.value"
              >
                <v-list-item-title>{{ item.text }}</v-list-item-title>
              </v-list-item>
            </v-list-item-group>
          </v-list>
        </v-menu>
      </span>
    </v-card-title>

    <v-card-text class="flex-1" :class="{ 'pa-0': type === 'ring' }">
      <slot name="chart"></slot>
    </v-card-text>
  </v-card>
</template>

<script>
import { toRecentSubDate } from '@/util/timezone'
import { ISO_FORMAT, defaultDay } from './constant'
export default {
  props: {
    title: {
      type: String,
    },
    type: {
      type: String,
    },
  },

  data() {
    return {
      currentDate: defaultDay,
      timer: null,
    }
  },
  watch: {
    '$i18n.locale': {
      handler() {
        this.onSelectData(this.currentDate)
      },
      deep: true,
    },
  },

  computed: {
    currentText() {
      return this.dateOption.find(v => v.value === this.currentDate)?.text
    },
    dateOption() {
      return [
        {
          text: this.$t('enums.datePresets.last7'),
          value: 7,
        },
        {
          text: this.$t('enums.datePresets.last15'),
          value: 15,
        },
        {
          text: this.$t('enums.datePresets.last30'),
          value: 30,
        },
      ]
    },
    // dateOption1() {
    //   return [
    //     {
    //       text: this.$t('enums.datePresets.last24'),
    //       value: 24,
    //     },
    //     {
    //       text: this.$t('enums.datePresets.last7'),
    //       value: 7,
    //     },
    //     {
    //       text: this.$t('enums.datePresets.last15'),
    //       value: 15,
    //     },
    //     {
    //       text: this.$t('enums.datePresets.last30'),
    //       value: 30,
    //     },
    //   ]
    // },
  },
  mounted() {
    this.currentDate = defaultDay
    let time =
      this.$store.getters['enums/getPollingInterval'](
        this.$generateMenuTitle(this.$route.meta),
      ) || 15
    this.timer = setInterval(() => {
      this.onSelectData(this.currentDate)
    }, time * 1000)
  },
  beforeDestroy() {
    clearInterval(this.timer)
  },
  methods: {
    onSelectData(value) {
      if (!value) return
      this.currentDate = value
      // const params = {
      //   startDate: format(subDays(new Date(), value - 1), ISO_FORMAT),
      //   endDate: format(new Date(), ISO_FORMAT),
      // }

      // const params = {
      //   startDate: toDateByTimeZone(
      //     subDays(new Date(), value - 1),
      //     -1,
      //     ISO_FORMAT,
      //   ),
      //   endDate: toDateByTimeZone(new Date(), -1, ISO_FORMAT),
      // }
      let params =
        this.currentDate === 24
          ? toRecentSubDate(value, 'Hours', '', '_format')
          : toRecentSubDate(value, 'Days', ISO_FORMAT)
      this.$emit('refresh', params, this.currentDate)
    },
  },
}
</script>
