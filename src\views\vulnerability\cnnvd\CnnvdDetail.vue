<!-- 漏洞详情 -->
<template>
  <div>
    <bread-crumb>
      <template slot="title">
        <div class="d-flex align-center color-base text-title">
          <v-chip
            class="rounded-lg"
            :color="cnnvdVulnerabilityLevelEnum[form.cnnvdLevel].color"
          >
            <vsoc-icon
              size="x-large"
              icon="icon-loudongdengjibiaozhi"
              type="fill"
            ></vsoc-icon>
            <span class="ml-1">{{
              cnnvdVulnerabilityLevelEnum[form.cnnvdLevel].text
            }}</span>
          </v-chip>

          <span class="ml-2 text-no-wrap">{{ form.cnnvdId }}</span>
          <span class="ml-1 text-no-wrap">{{
            $t('vulnerability.edit.title3')
          }}</span>
        </div>
      </template>
      <template slot="left">
        <div
          class="d-flex justify-space-between align-center pl-8 text-overflow-hide"
        >
          <!-- <v-chip
            class="rounded-sm font-weight-bold"
            :color="cnnvdVulnerabilityLevelEnum[form.cnnvdLevel].color"
            text-color="white"
          >
            {{ form.vulnerabilityLevelName }}
          </v-chip> -->
          <div
            class="ml-2 text-title font-weight-medium color-base text-overflow-hide"
          >
            {{ form.cnnvdName }}
          </div>
        </div>
      </template>
      <v-btn
        color="primary"
        width="76"
        min-width="76"
        elevation="0"
        @click="addTicket"
      >
        {{ $t('alert.btn.ticket') }}
      </v-btn>
    </bread-crumb>

    <v-card tile class="h-100 overflow-y-auto" elevation="0">
      <v-card-text class="pa-0">
        <!-- <hr class="horizontal dark" /> -->
        <div
          class="overflow-y px-10"
          :style="{ height: `calc(100vh - ${topHeaderHeight}px)` }"
        >
          <h6
            class="d-flex align-center text-sm-h6 text-typo font-weight-bolder mb-2 my-6"
          >
            <span class="text-title font-weight-medium">{{
              $t('global.drawer.baseInfo')
            }}</span>
          </h6>

          <div class="mx-6 mt-6">
            <v-form>
              <v-row>
                <v-col cols="4">
                  <!-- <v-text-field
                    v-model="form.cnnvdId"
                    :label="$t('vulnerability.headers.cnnvdId')"
                    hide-details
                    disabled
                  ></v-text-field> -->
                  <div class="text-root-base accent--text">
                    {{ $t('vulnerability.headers.cnnvdId') }}
                  </div>
                  <div class="text-content color-base mt-1 font-weight-medium">
                    {{ form.cnnvdId }}
                  </div>
                </v-col>
                <v-col cols="4">
                  <!-- <v-text-field
                    v-model="form.cveId"
                    :label="$t('vulnerability.headers.cveId')"
                    hide-details
                    disabled
                  ></v-text-field> -->
                  <div class="text-root-base accent--text">
                    {{ $t('vulnerability.headers.cveId') }}
                  </div>
                  <div class="text-content color-base mt-1 font-weight-medium">
                    {{ form.cveId }}
                  </div>
                </v-col>

                <v-col cols="4">
                  <div class="text-root-base accent--text">
                    {{ $t('vulnerability.headers.loopholeType') }}
                  </div>
                  <div class="text-content color-base mt-1 font-weight-medium">
                    {{ form.cnnvdTypeName | dataFilter }}
                  </div>
                </v-col>

                <v-col cols="4">
                  <div class="text-root-base accent--text">
                    {{ $t('vulnerability.cnnvd.updateDate') }}
                  </div>
                  <div class="text-content color-base mt-1 font-weight-medium">
                    {{ form.updateTime | toDate | dataFilter }}
                  </div>
                </v-col>
                <v-col cols="4">
                  <div class="text-root-base accent--text">
                    {{ $t('vulnerability.cnnvd.publicDate') }}
                  </div>
                  <div class="text-content color-base mt-1 font-weight-medium">
                    {{ form.publishedTime | toDate | dataFilter }}
                  </div>
                </v-col>

                <v-col cols="4">
                  <div class="text-root-base accent--text">
                    {{ $t('vulnerability.headers.createTime') }}
                  </div>
                  <div class="text-content color-base mt-1 font-weight-medium">
                    {{ form.createTime | toDate | dataFilter }}
                  </div>
                </v-col>

                <v-col cols="4">
                  <div class="text-root-base accent--text">
                    {{ $t('global.createUser') }}
                  </div>
                  <div class="text-content color-base mt-1 font-weight-medium">
                    {{ form.createUser | dataFilter }}
                  </div>
                </v-col>
              </v-row>
            </v-form>
            <v-divider class="mt-4 divider--dashed"></v-divider>
            <h6
              class="d-flex align-center text-sm-h6 text-typo font-weight-bolder mb-2 mt-6"
            >
              <span class="text-root-base accent--text">{{
                $t('vulnerability.edit.desc')
              }}</span>
            </h6>
            <p class="text-content color-base font-weight-medium">
              {{ form.description }}
            </p>
            <v-divider class="mt-6 divider--dashed"></v-divider>

            <h6
              class="d-flex align-center text-sm-h6 text-typo font-weight-bolder mb-2 mt-6"
            >
              <span class="text-root-base accent--text">{{
                $t('vulnerability.link')
              }}</span>
            </h6>
            <p>
              <a
                class="text-content font-weight-medium"
                v-if="showUrl(form.referenceWebsite)"
                :href="form.referenceWebsite"
                target="_blank"
                >{{ form.referenceWebsite }}
              </a>
              <span
                v-else
                class="text--primary font-weight-medium text-content"
                >{{ form.referenceWebsite | dataFilter }}</span
              >
            </p>

            <v-divider class="mt-6 divider--dashed"></v-divider>

            <h6
              class="d-flex align-center text-sm-h6 text-typo font-weight-bolder mb-2 mt-6"
            >
              <span class="text-root-base accent--text">{{
                $t('vulnerability.patch')
              }}</span>
            </h6>
            <p>
              <a
                class="text-content font-weight-medium"
                v-if="showUrl(form.patch)"
                :href="form.patch"
                target="_blank"
                >{{ form.patch }}
              </a>
              <span
                v-else
                class="text--primary font-weight-medium text-content"
                >{{ form.patch | dataFilter }}</span
              >
            </p>
            <v-divider class="mt-6 divider--dashed"></v-divider>
          </div>
        </div>
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
import { getCnnvdDetail } from '@/api/vulnerability/index'
import breadCrumb from '@/components/bread-crumb/index'
import { setLocalStorage } from '@/util/localStorage'
export default {
  name: 'CnnvdDetail',
  components: {
    breadCrumb,
  },
  data() {
    return {
      form: {
        cnnvdName: 'MediaWiki限制绕过漏洞',
        description:
          "MediaWiki是用PHP编写的免费软件开源wiki包，最初用于维基百科，现在也被非营利Wikimedia基金会的若干其他项目和许多其他wikis使用。 MediaWiki存在限制绕过漏洞，该漏洞源于'user'的'$wgRateLimits' rate limiter条目会覆盖'newbie'用户的设置，远程认证用户可利用该漏洞绕过预期的限制。",
        loopholeType: '0',
        publicDate: '2019-06-24T00:00:00.000+0000',
        patch: 'MediaWiki限制绕过漏洞的补丁',
        patchUrl:
          '厂商已发布了漏洞修复程序，请及时关注更新： https://lists.wikimedia.org/pipermail/mediawiki-announce/2018-September/000223.html',
        solution:
          "MediaWiki是用PHP编写的免费软件开源wiki包，最初用于维基百科，现在也被非营利Wikimedia基金会的若干其他项目和许多其他wikis使用。 MediaWiki存在限制绕过漏洞，该漏洞源于'user'的'$wgRateLimits' rate limiter条目会覆盖'newbie'用户的设置，远程认证用户可利用该漏洞绕过预期的限制。目前，供应商发布了安全公告及相关补丁信息，修复了此漏洞。",
        attachment: null,
        cvssScore: null,
        cvssVersion: null,
        cnnvdLevel: '1',
        id: 1,
        verification: null,
        cnnvdId: 'CNVD-2019-18907',
        cveIdUrl: 'null',
        cveId: 'CVE-2018-0503',
        loopholeTypeName: '通用软硬件漏洞',
        updateTime: null,
        vulnerabilityLevelName: '中危',
        dataSourceName: 'cnvd',
        referenceUrl: 'https://securitytracker.com/id/1041695',
        submitTime: '2018-09-21T00:00:00.000+0000',
        recordTime: null,
        influenceProduct: [
          'MediaWiki Mediawiki 1.31',
          'MediaWiki Mediawiki 1.30.1',
          'MediaWiki Mediawiki 1.29.3',
          'MediaWiki Mediawiki 1.27.5',
        ],
        createTime: '2023-05-19T02:41:16.000+0000',
        createUser: null,
        dataSource: '1',
        cvss: null,
      },
    }
  },
  computed: {
    topHeaderHeight() {
      return this.$store.getters['global/getTopHeaderHeight']
    },
    cnnvdVulnerabilityLevelEnum() {
      return this.$store.getters['enums/getCnnvdVulnerabilityLevel']
    },
  },
  created() {
    this.loadDetail()
  },
  methods: {
    //转工单
    addTicket() {
      const params = {
        priority:
          Number(this.form.cnnvdLevel) > 0
            ? (Number(this.form.cnnvdLevel) - 1).toString()
            : '3',
        title:
          this.form.cnnvdId +
          '_' +
          this.$toItem(this.cnnvdVulnerabilityLevelEnum, this.form.cnnvdLevel)
            .text +
          '_' +
          this.form.cnnvdName, //漏洞编号_漏洞等级_漏洞名称
        ticketContent: this.form.description,
        dataSource: '1',
        relationId: this.form.cnnvdId,
      }
      setLocalStorage('alertTicket', JSON.stringify(params))
      // localStorage.setItem('alertTicket', JSON.stringify(params))
      this.$router.push('/ticket/addTicket?type=1')
    },
    showUrl(url) {
      return url && url.trim().startsWith('http')
    },
    async loadDetail() {
      try {
        const params = {
          cnnvdId: this.$route.query.cnnvdId,
        }
        const { data } = await getCnnvdDetail(params)
        this.form = data
        console.log('form', this.form)
      } catch (err) {
        console.log('漏洞详情报错', err)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .v-text-field > .v-input__control > .v-input__slot:before {
  width: 0;
}
::v-deep .v-input--is-disabled input {
  color: var(--v-color-base);
}
.col-6 {
  padding: 0 1rem 1rem;
}
</style>
