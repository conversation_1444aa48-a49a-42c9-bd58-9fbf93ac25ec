import Vue from 'vue'

// 告警状态色 #4caf50
// export let alertColor = ['#e91e63', '#fb8c00', '#f8d61c', '#3caea3', '#7b809a', '#1a73e8'] TODO @Henry20220927
export const strategyStatusColor = ['#06309B', '#20CC1D', '#686E7C', '#DA1F1F']
export const alertColor = [
  '#DA1F1F',
  '#FF910F',
  '#FBD82C',
  '#0EAA9F',
  '#B4BBCC',
  '#0F7EFF',
]
export const activeColor = '#21D95D'
export const disableColor = '#DA1F1F'
export const inactiveColor = '#7A8599'
export const alarmStatusColor = [
  '#FF910F',
  '#06309B',
  '#DA1F1F',
  '#8F9AB2',
  '#8F9AB2',
  '#8F9AB2',
]
export const alarmStatusColor1 = [
  '#FF910F',
  '#06309B',
  '#DA1F1F',
  '#3caea3',
  '#7b809a',
  '#038cd6',
]
export const ticketStatusColor = [
  '#FF910F',
  '#06309B',
  '#20CC1D',
  '#DA1F1F',
  '#A1A6B1',
  '#686E7C',
  '#F5D018',
]
export const userColor = [
  inactiveColor,
  disableColor,
  activeColor,
  alertColor[0],
  alertColor[1],
]

// 主题色
// Vue.prototype.$primaryColor = '#e91e63'  TODO @Henry20220927
Vue.prototype.$primaryColor = '#344767'

// 主题黑色
Vue.prototype.$typoColor = '#344767'

// 主题灰色
Vue.prototype.$bodyColor = '#7b809a'
Vue.prototype.$defaultColode = 'rgba(0,0,0,0.87)'

// 告警状态色
// eslint-disable-next-line prefer-destructuring
Vue.prototype.$alertColor0 = alertColor[0]
Vue.prototype.$alertColor1 = alertColor[1]
Vue.prototype.$alertColor2 = alertColor[2]
Vue.prototype.$alertColor3 = alertColor[3]
Vue.prototype.$alertColor4 = alertColor[4]
Vue.prototype.$alertColor5 = alertColor[5]

// 活跃状态颜色
Vue.prototype.$activeColor = activeColor
Vue.prototype.$inactiveColor = inactiveColor
Vue.prototype.$disableColor = disableColor

// 获取响应字体大小
Vue.prototype.$getRemFont = function (type) {
  const fontSize = +getComputedStyle(window.document.documentElement)[
    'font-size'
  ].replace('px', '')
  let x = 0.75
  if (type === 'sm') {
    x = 0.875
  } else if (type === 'md') {
    x = 1
  } else if (type === 'lg') {
    x = 1.25
  } else if (type === 'xs') {
    x = 0.75
  } else if (typeof type === 'number') {
    return fontSize * type
  }

  return fontSize * x
}

// 获取状态对应的颜色
export const getStatusColor = {
  0: {
    color: alertColor[0],
    text: '严重',
  },
  1: {
    color: alertColor[1],
    text: '高',
  },
  2: {
    color: alertColor[2],
    text: '中',
  },
  3: {
    color: alertColor[3],
    text: '低',
  },
  4: {
    color: alertColor[4],
    text: '信息',
  },
  5: {
    color: alertColor[5],
    text: '测试',
  },
}

// 获取状态对应的颜色0-高危，1-中危，2-低危，3-超危
export const getLevelColor = {
  3: {
    color: alertColor[0],
    text: '超危',
  },
  0: {
    color: alertColor[1],
    text: '高危',
  },
  1: {
    color: alertColor[2],
    text: '中危',
  },
  2: {
    color: alertColor[3],
    text: '低危',
  },
}
