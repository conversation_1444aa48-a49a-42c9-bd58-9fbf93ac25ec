<template>
  <v-card tile class="main-content">
    <v-card-text class="pa-0">
      <div class="d-flex justify-space-between align-center flex-row-reverse">
        <div class="d-flex align-end">
          <table-search
            :searchList="searchList"
            :searchQuery="query"
            @search="$_search"
          ></table-search>
        </div>
      </div>

      <v-data-table
        fixed-header
        :items-per-page="query.pageSize"
        item-key="id"
        :height="tableHeight"
        hide-default-footer
        :headers="headers"
        :items="tableData"
        class="table border-radius-xl mt-3 thead-light"
        :loading="tableLoading"
        @click:row="$_viewDetails"
      >
        <template v-slot:item.id="{ item }">
          <div class="py-2">
            <div>
              <v-chip label x-small class="mb-1" color="secondary">CAVD</v-chip
              >{{ item.cavdNo | dataFilter }}
            </div>
            <div class="d-flex">
              <v-chip label x-small text-color="accent">CVE</v-chip>
              <span
                class="accent--text"
                v-html="item.cveContent || 'N/A'"
              ></span>
            </div>
          </div>
        </template>
        <template v-slot:item.cnnvdName="{ item }">
          <div>
            <div
              v-show-tips="item.vulName"
              style="width: 26rem"
              class="text-overflow-hide"
            >
              {{ item.vulName }}
            </div>
            <div class="mt-1">
              <div
                v-if="$toItem(cavdVulnerabilityLevelEnum, item.vulLevel)"
                class="d-flex"
                :style="`color:${
                  $toItem(cavdVulnerabilityLevelEnum, item.vulLevel).color
                }`"
              >
                <vsoc-icon
                  class="mr-1"
                  type="fill"
                  size="middle"
                  icon="icon-loudongdengjibiaozhi"
                ></vsoc-icon>
                <span class="font-weight-medium">{{
                  $toItem(cavdVulnerabilityLevelEnum, item.vulLevel).text
                }}</span>
              </div>
              <span v-else>N/A</span>
            </div>
          </div>
        </template>

        <template v-slot:item.publishedTime="{ item }">
          <div>
            <div>
              {{
                item.publishDateStr
                  | toDate(item.publishDateStr, 'YYYY-MM-DD')
                  | dataFilter
              }}
            </div>
            <div class="accent--text mt-1">
              {{ item.updateDate | toDate | dataFilter }}
            </div>
          </div>
        </template>

        <template v-slot:item.createTime="{ item }">
          <div>
            <div>
              {{ item.createTime | toDate | dataFilter }}
            </div>
            <div class="accent--text mt-1">
              {{ item.updateTime | toDate | dataFilter }}
            </div>
          </div>
        </template>

        <template v-slot:item.createUser="{ item }">
          <div>
            <div class="mb-1">{{ item.syncUser | dataFilter }}</div>
            <div class="accent--text">
              {{ item.syncTime | dataFilter }}
            </div>
          </div>
        </template>
      </v-data-table>
      <!-- 分页器 -->
      <vsoc-pagination
        :page.sync="query.pageNum"
        :size.sync="query.pageSize"
        :total="tableDataTotal"
        @change-size="$_search"
        @change-page="getTableData"
      />
    </v-card-text>
  </v-card>
</template>

<script>
import { getCavdList } from '@/api/vulnerability'
import TableSearch from '@/components/TableSearch/index.vue'
import VsocPagination from '@/components/VsocPagination.vue'
import VsocDateRange from '@/components/vsoc-date-range/VsocDateRange.vue'

export default {
  name: 'CavdIndex',
  props: {
    tableHeight: [String, Number],
  },
  components: {
    VsocPagination,
    VsocDateRange,
    TableSearch,
  },
  data() {
    return {
      range: {
        start: '',
        end: '',
      },

      // 查询内容
      queryKey: 'cnnvdId',
      tableLoading: false,

      // 查询条件下拉选择
      query: {
        cavdNo: '',
        vulName: '',
        cveContent: '',
        publishDateStart: null,
        publishDateEnd: null,
        vulLevelList: [],
        createTimeStart: null,
        createTimeEnd: null,
        syncTimeStart: null,
        syncTimeEnd: null,
        pageSize: 200,
        pageNum: 1,
      },

      tableData: [],
      tableDataTotal: 0,
    }
  },
  computed: {
    searchList() {
      return [
        {
          type: 'multiSearch',
          value: 'vulLevelList',
          conditions: [
            {
              type: 'autocomplete',
              value: 'vulLevelList',
              text: this.$t('vulnerability.headers.alarmLevel'),
              itemList: this.$store.getters['enums/getCavdVulnerabilityLevel'],
            },
            {
              type: 'input',
              value: 'vulName',
              text: this.$t('vulnerability.headers.vulnerabilityName'),
            },
            {
              type: 'input',
              value: 'cveContent',
              text: this.$t('vulnerability.headers.cveId'),
            },
            {
              type: 'input',
              value: 'cavdNo',
              text: this.$t('vulnerability.cavd.id'),
            },
          ],
        },
      ]
    },
    // 查询条件列表
    searchConditions() {
      return [
        {
          text: this.$t('vulnerability.headers.alarmLevel'),
          value: 'vulnerabilityLevelList',
        },
        {
          text: this.$t('vulnerability.headers.vulnerabilityName'),
          value: 'vulName',
        },
        {
          text: this.$t('vulnerability.headers.cveId'),
          value: 'cveContent',
        },
        {
          text: this.$t('vulnerability.cavd.id'),
          value: 'cavdNo',
        },
      ]
    },
    headers() {
      return [
        {
          text: this.$t('vulnerability.headers.idInfo'),
          value: 'id',
          width: 220,
          sortable: false,
        },

        {
          text: this.$t('vulnerability.headers.vulnerInofo'),
          value: 'cnnvdName',
          width: 160,
          sortable: false,
        },

        {
          text: this.$t('vulnerability.cavd.publicTime'),
          value: 'publishedTime',
          width: 160,
          sortable: false,
        },
        {
          text: this.$t('vulnerability.cavd.info'),
          value: 'createTime',
          width: 160,
          sortable: false,
        },
        {
          text: this.$t('vulnerability.headers.entryInfo'),
          value: 'createUser',
          width: 160,
          sortable: false,
        },
      ]
    },
    cavdVulnerabilityLevelEnum() {
      return this.$store.getters['enums/getCavdVulnerabilityLevel']
    },
    vulnerabilityTypeEnum() {
      return Object.assign([], this.$store.state.enums.enums.VulnerabilityType)
    },
  },
  // mounted() {
  //   this.$_search()
  // },
  methods: {
    onReset() {
      Object.assign(this.$data.query, this.$options.data.call(this).query)
      this.$_search()
    },
    $_viewDetails(record) {
      if (this.$route.meta.buttons.includes('cnnvd-detail')) {
        this.$router.push(`/bugManagement/cavdDetail?cavdNo=${record.cavdNo}`)
      }
    },

    $_search() {
      this.query.pageNum = 1
      this.getTableData()
      this.$emit('filter', this.query)
    },

    // 获取表格
    async getTableData() {
      try {
        this.tableLoading = true
        const { data } = await getCavdList(this.query)
        this.tableData = data.records
        this.tableDataTotal = data.total
      } catch (e) {
        console.error(`获取漏洞管理：${e}`)
      }
      this.tableLoading = false
    },
  },
}
</script>
<style scoped lang="scss">
.v-chip.v-size--x-small {
  display: inline-block;
  width: 5rem;
  height: 1.8333rem;
  padding: 0;
  margin-right: 4px;
  text-align: center;
}
</style>
