<template>
  <v-card tile class="main-content">
    <v-card-text class="pa-0">
      <div class="d-flex justify-space-between align-center flex-row-reverse">
        <div class="d-flex align-end">
          <table-search
            :searchList="searchList"
            :searchQuery="query"
            @search="$_search"
          ></table-search>
          <!-- <v-select
            v-model="queryKey"
            small
            color="primary"
            :menu-props="{ auto: true, offsetY: true }"
            append-icon="mdi-chevron-down"
            hide-details
            outlined
            dense
            class="mr-3 select-width"
            :items="searchConditions"
            :label="$t('action.queryConditions')"
          ></v-select>

          <v-select
            v-if="queryKey === 'vulnerabilityLevelList'"
            v-model="query[queryKey]"
            clearable
            small
            multiple
            color="primary"
            :menu-props="{ auto: true, offsetY: true }"
            append-icon="mdi-chevron-down"
            outlined
            dense
            class="text-width"
            hide-details
            :items="vulnerabilityLevelEnum"
            :label="$t('action.queryContent')"
          ></v-select>
          <vsoc-date-range
            v-else-if="queryKey === 'startDate'"
            ref="dateInput"
            v-model="range"
            @input="onChangeDate"
          >
            <template v-slot:text="{ inputValue }">
              <v-text-field
                class="date-width"
                clearable
                readonly
                color="primary"
                outlined
                dense
                hide-details
                label="发现日期"
                append-icon="mdi-calendar-range-outline"
                :value="inputValue"
                @click:clear="onChangeDate({ start: '', end: '' })"
              />
            </template>
          </vsoc-date-range>
          <v-autocomplete
            v-else-if="queryKey === 'findUser'"
            :label="$t('action.queryContent')"
            v-model="query[queryKey]"
            :items="userList"
            hide-details
            dense
            outlined
            class="text-width"
            clearable
            :menu-props="{ offsetY: true }"
          ></v-autocomplete>
          <v-text-field
            v-else
            v-model="query[queryKey]"
            class="text-width"
            color="primary"
            dense
            outlined
            hide-details
            :label="$t('action.queryContent')"
          ></v-text-field>
          <v-btn
            color="primary--text bg-btn ml-3"
            elevation="0"
            @click="$_search"
          >
            <span>{{ $t('action.search') }}</span>
          </v-btn> -->
        </div>

        <div class="d-flex justify-end align-center">
          <v-btn
            width="76"
            min-width="76"
            elevation="0"
            color="primary"
            class="me-2"
            @click="goAdd"
            v-has:self-vulner-add
          >
            <span>{{
              $generateMenuTitle($route.meta.buttonInfo['self-vulner-add'])
            }}</span>
          </v-btn>
        </div>
      </div>

      <!--$_showBugDetails-->
      <!-- loading-text="查询中... 请等待" -->
      <v-data-table
        ref="bugTable"
        fixed-header
        :items-per-page="query.pageSize"
        item-key="id"
        :height="tableHeight"
        hide-default-footer
        :headers="headers"
        :items="tableData"
        class="table border-radius-xl mt-3 thead-light"
        :loading="tableLoading"
        @click:row="$_viewDetails"
      >
        <template v-slot:item.vulnerabilityName="{ item }">
          <div
            class="mb-1 text-overflow-hide"
            style="max-width: 25rem"
            v-show-tips
          >
            {{ item.vulnerabilityName | dataFilter }}
          </div>
        </template>

        <template v-slot:item.vulnerabilityLevel="{ item }">
          <div>
            <div
              v-if="vulnerabilityLevelEnum[item.vulnerabilityLevel]"
              class="d-flex"
              :style="`color:${
                vulnerabilityLevelEnum[item.vulnerabilityLevel].color
              }`"
            >
              <vsoc-icon
                class="mr-1"
                type="fill"
                size="middle"
                icon="icon-loudongdengjibiaozhi"
              ></vsoc-icon>
              <span class="font-weight-medium">{{
                vulnerabilityLevelEnum[item.vulnerabilityLevel].text
              }}</span>
            </div>
            <span v-else>N/A</span>
          </div>
        </template>

        <template v-slot:item.loopholeType="{ item }">
          <v-chip
            small
            label
            color="accent"
            style="max-width: 12rem"
            class="text-overflow-hide"
            v-show-tips
            v-if="vulnerabilityTypeEnum[item.loopholeType]"
          >
            {{ vulnerabilityTypeEnum[item.loopholeType].text }}
          </v-chip>
        </template>

        <template v-slot:item.impactManufacturer="{ item }">
          <div v-show-tips style="max-width: 10rem" class="text-overflow-hide">
            {{ item.impactManufacturer | dataFilter }}
          </div>
        </template>

        <template v-slot:item.impactProduct="{ item }">
          <div v-show-tips style="max-width: 10rem" class="text-overflow-hide">
            {{ item.impactProduct | dataFilter }}
          </div>
        </template>

        <template v-slot:item.actions="{ item }">
          <v-btn icon @click.stop="goEdit(item)">
            <vsoc-icon
              v-has:self-vulner-edit
              v-show-tips="
                $generateMenuTitle($route.meta.buttonInfo['self-vulner-edit'])
              "
              type="fill"
              icon="icon-bianji"
              class="action-btn"
              size="x-large"
            ></vsoc-icon>
          </v-btn>
          <v-btn icon @click.stop="onDel(item)">
            <vsoc-icon
              v-has:self-vulner-del
              v-show-tips="
                $generateMenuTitle($route.meta.buttonInfo['self-vulner-del'])
              "
              type="fill"
              class="action-btn"
              icon="icon-shanchu"
              size="x-large"
            ></vsoc-icon>
          </v-btn>
        </template>
      </v-data-table>
      <!-- 分页器 -->
      <vsoc-pagination
        :page.sync="query.pageNum"
        :size.sync="query.pageSize"
        :total="tableDataTotal"
        @change-size="$_search"
        @change-page="getTableData"
      />
    </v-card-text>
  </v-card>
</template>

<script>
import {
  delSelfVulnerability,
  getSelfVulnerabilityList,
} from '@/api/vulnerability'
import TableSearch from '@/components/TableSearch/index.vue'
import VsocPagination from '@/components/VsocPagination.vue'
import VsocDateRange from '@/components/vsoc-date-range/VsocDateRange.vue'
import { clearFilterItem } from '@/util/utils'
import { cloneDeep } from 'lodash'

export default {
  name: 'SelfVulnerability',
  props: {
    tableHeight: [String, Number],
  },
  components: {
    VsocPagination,
    VsocDateRange,
    TableSearch,
  },
  data() {
    return {
      userList: [],
      range: {
        start: '',
        end: '',
      },
      currentTab: 0,
      isDrawerShow: false,
      // 编辑漏洞弹框
      showEditBug: false,

      // 漏洞详情弹框
      showBugDetails: false,

      // 漏洞详情弹框标题
      bugDetailsTitile: '',

      // 查询内容
      queryKey: 'vulnerabilityLevelList',
      tableLoading: false,

      // 查询条件下拉选择
      query: {
        vulnerabilityLevelList: [],
        vulneraNumber: '',
        findUser: '',
        startDate: null,
        endDate: null,
        vulnerabilityName: '',
        impactManufacturer: '',
        loopholeTypeList: [],
        pageNum: 1,
        pageSize: 200,
      },

      // 查询条件展示列表
      filterList: [],

      // 漏洞管理表格信息
      tableData: [],
      tableDataTotal: 0,
    }
  },
  computed: {
    searchList() {
      return [
        {
          type: 'multiSearch',
          value: 'vulnerabilityName',
          conditions: [
            {
              type: 'input',
              value: 'vulnerabilityName',
              text: this.$t('vulnerability.headers.vulnerabilityName'),
            },
            {
              type: 'autocomplete',
              value: 'vulnerabilityLevelList',
              text: this.$t('vulnerability.headers.alarmLevel'),
              itemList:
                this.$store.getters['enums/getLaboratoryVulnerabilityLevel'],
            },
            {
              type: 'select',
              value: 'findUser',
              text: this.$t('vulnerability.headers.findUser'),
              itemList: this.userList,
            },
            {
              type: 'date',
              value: ['startDate', 'endDate'],
              text: this.$t('vulnerability.headers.findDate'),
              dateRange: {
                range: {
                  start: '',
                  end: '',
                },
                menuProps: { offsetY: true, closeOnContentClick: false },
              },
            },
          ],
        },
      ]
    },
    // 查询条件列表
    searchConditions() {
      return [
        {
          text: this.$t('vulnerability.headers.vulnerabilityName'),
          value: 'vulnerabilityName',
        },
        {
          text: this.$t('vulnerability.headers.alarmLevel'),
          value: 'vulnerabilityLevelList',
        },
        {
          text: this.$t('vulnerability.headers.findUser'),
          value: 'findUser',
        },
        {
          text: this.$t('vulnerability.headers.findDate'),
          value: 'startDate',
        },
      ]
    },
    headers() {
      return [
        {
          text: this.$t('vulnerability.headers.vulnerId'),
          value: 'vulneraNumber',
          width: 160,
          sortable: false,
        },
        {
          text: this.$t('vulnerability.headers.vulnerabilityName'),
          value: 'vulnerabilityName',
          width: 200,
        },
        {
          text: this.$t('vulnerability.headers.alarmLevel'),
          value: 'vulnerabilityLevel',
          width: 100,
        },
        {
          text: this.$t('vulnerability.headers.loopholeType'),
          value: 'loopholeType',
          width: 140,
        },
        {
          text: this.$t('vulnerability.headers.affectManufacturers'),
          value: 'impactManufacturer',
          width: 180,
        },
        {
          text: this.$t('vulnerability.edit.title2'),
          value: 'impactProduct',
          width: 160,
        },
        {
          text: this.$t('vulnerability.headers.findUser'),
          value: 'findUser',
          width: 100,
        },
        {
          text: this.$t('vulnerability.headers.findDate'),
          value: 'findDate',
          width: 160,
        },
        {
          text: this.$t('global.createUser'),
          value: 'createUser',
          width: 160,
        },
        {
          text: this.$t('global.createDate'),
          value: 'createDate',
          width: 160,
        },
        {
          text: this.$t('global.updateUser'),
          value: 'updateUser',
          width: 160,
        },
        {
          text: this.$t('global.updateDate'),
          value: 'updateDate',
          width: 160,
        },
        {
          text: '',
          value: 'actions',
          width: 120,
        },
      ]
    },
    // vulnerabilityLevelEnum() {
    //   return this.$store.state.enums.enums.VulnerabilityLevel
    // },
    // laboratoryVulnerabilityLevelEnum() {
    //   return this.$store.getters['enums/getLaboratoryVulnerabilityLevel']
    // },
    vulnerabilityLevelEnum() {
      return this.$store.getters['enums/getLaboratoryVulnerabilityLevel']
    },
    vulnerabilityTypeEnum() {
      return Object.assign([], this.$store.state.enums.enums.VulnerabilityType)
    },
  },
  mounted() {
    // this.$_search()
    this.loadUserList()
  },
  methods: {
    $_viewDetails(record) {
      if (this.$route.meta.buttons.includes('self-detail')) {
        this.$router.push({
          path: '/vulnerability/detail',
          query: { id: record.id },
        })
      }
    },
    async loadUserList() {
      this.userList = await this.$store.dispatch('global/loadUserList')
    },
    onChangeDate(range) {
      this.range = range
      this.query.startDate = range.start
      this.query.endDate = range.end
    },
    onDel(record) {
      this.$swal({
        title: this.$t('vulnerability.swal.del.title'),
        text: this.$t('vulnerability.swal.del.text', [
          record.impactManufacturer,
        ]),
        icon: 'warning',
        reverseButtons: true,
        showCancelButton: true,
        confirmButtonText: this.$t('action.confirm'),
        cancelButtonText: this.$t('action.cancel'),
        customClass: {
          confirmButton: 'sweet-btn-primary',
        },
      }).then(async result => {
        if (result.isConfirmed) {
          const params = {
            id: record.id,
          }
          await delSelfVulnerability(params)
          this.$notify.info('success', this.$t('global.hint.del', ['']))
          this.getTableData()
        }
      })
    },
    goAdd() {
      this.$router.push(`/vulnerability/new`)
    },
    goEdit(record) {
      this.$router.push({
        path: '/vulnerability/edit',
        query: { id: record.id },
      })
    },
    // 清除某个查询条件
    $_clearFilter(item, index) {
      let bool = clearFilterItem.call(this, item)
      if (item.type === 'Date') {
        this.query[`${item.key}Start`] = null
        this.query[`${item.key}End`] = null
        this.filterList.splice(index, 1)
        this.$forceUpdate()
        bool = false
      }
      if (!bool) {
        this.$_search()
      }
    },
    doQuery(advanceQuery) {
      this.query = Object.assign(this.query, cloneDeep(advanceQuery))
      this.$_search()
    },
    onReset() {
      Object.assign(this.$data.query, this.$options.data.call(this).query)
      this.$_search()
    },
    $_showBugDetails(item) {
      this.showBugDetails = true
      this.bugDetailsTitile = item.name
    },

    $_search() {
      this.query.pageNum = 1
      this.getTableData()
      this.$emit('filter', this.query)
    },

    // 获取表格
    async getTableData() {
      try {
        this.tableLoading = true

        const { data } = await getSelfVulnerabilityList(this.query)
        this.tableData = data.records
        this.tableDataTotal = data.total
      } catch (e) {
        console.error(`获取漏洞管理：${e}`)
      }
      this.tableLoading = false
    },

    $_setTableHeight() {
      // this.$nextTick(() => {
      //   const filterFn = () => {
      //     return -this.$refs.filterList.offsetHeight + 4
      //   }
      //   this.tableHeight = setRemainingHeight(filterFn)
      // })
    },

    $_setStatus() {
      this.showEditBug = true
    },
  },
}
</script>
<style scoped lang="scss">
.v-chip.v-size--x-small {
  display: inline-block;
  width: 3.6667rem;
  height: 1.8333rem;
  padding: 0;
  margin-right: 4px;
  text-align: center;
}
</style>
