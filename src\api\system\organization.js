import { request, vsocPath } from '../../util/request'

// 查询组织架构层级树
export const querySysGroupList = function (params) {
  return request({
    url: `${vsocPath}/sysDepartment/querySysDepartmentList`,
    method: 'post',
    data: params,
    loading: true,
  })
}

// 组织架构新增接口
export const addDepartment = function (params) {
  return request({
    url: `${vsocPath}/sysDepartment/addDepartment`,
    method: 'post',
    data: params,
  })
}

// 组织架构修改接口
export const updateDepartment = function (params) {
  return request({
    url: `${vsocPath}/sysDepartment/updateDepartment`,
    method: 'post',
    data: params,
  })
}

// 组织架构删除接口
export const delDepartment = function (params) {
  return request({
    url: `${vsocPath}/sysDepartment/delDepartment`,
    method: 'post',
    data: params,
  })
}
