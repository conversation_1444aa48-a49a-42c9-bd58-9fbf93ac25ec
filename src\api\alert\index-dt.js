// import { cmdbPath, request } from '../../util/request'

// 获取告警列表
export function getAlertsDt(params) {
  return {
    content: [
      {
        createDate: 1676523570624,
        updateDate: 1676523570624,
        createUser: null,
        updateUser: null,
        id: 112969016,
        name: '车辆远程启动异常',
        alarmLevel: 'Medium',
        alarmDescription:
          '车辆远程启动异常，当前发动机状态为：启动，消息指令：remote_start_error',
        alarmDate: 1676523570000,
        alarmLocation: '',
        alarmLocationCity: '',
        alarmLocationLng: 113.880318,
        alarmLocationLat: 23.234261,
        status: 'waitHandle',
        tags: null,
        vehicleId: 'acTaaesCsZTRcsaW1',
        vehicleAssetVin: 'N/A',
        vehicleAssetType: null,
        vehicleAssetYear: 2020,
        vehicleAssetEngine: null,
        detectorId: '1555364832725925890',
        detectorName: '车辆远程启动异常',
      },
      {
        createDate: 1676520125020,
        updateDate: 1676520125020,
        createUser: null,
        updateUser: null,
        id: 112969007,
        name: '车辆远程启动异常',
        alarmLevel: 'Medium',
        alarmDescription:
          '车辆远程启动异常，当前发动机状态为：启动，消息指令：remote_start_error',
        alarmDate: 1676520124000,
        alarmLocation: '',
        alarmLocationCity: '',
        alarmLocationLng: 113.886804,
        alarmLocationLat: 23.156117,
        status: 'waitHandle',
        tags: null,
        vehicleId: 'sdAtcoWNsZTAwM02',
        vehicleAssetVin: 'N/A',
        vehicleAssetType: null,
        vehicleAssetYear: 2020,
        vehicleAssetEngine: null,
        detectorId: '1555364832725925890',
        detectorName: '车辆远程启动异常',
      },
      {
        createDate: 1676529633470,
        updateDate: 1676529633470,
        createUser: null,
        updateUser: null,
        id: 112969008,
        name: '车辆远程启动异常',
        alarmLevel: 'Medium',
        alarmDescription:
          '车辆远程启动异常，当前发动机状态为：启动，消息指令：remote_start_error',
        alarmDate: 1676529632000,
        alarmLocation: '',
        alarmLocationCity: '',
        alarmLocationLng: 113.900602,
        alarmLocationLat: 23.127896,
        status: 'waitHandle',
        tags: null,
        vehicleId: 'acVmVsZTAwM04',
        vehicleAssetVin: 'N/A',
        vehicleAssetType: null,
        vehicleAssetYear: 2020,
        vehicleAssetEngine: null,
        detectorId: '1555364832725925890',
        detectorName: '车辆远程启动异常',
      },
      {
        createDate: 1676529299425,
        updateDate: 1676529299425,
        createUser: null,
        updateUser: null,
        id: 112968999,
        name: '未知的服务器指令',
        alarmLevel: 'Medium',
        alarmDescription: '未知的服务器指令，指令：SQL',
        alarmDate: 1676529298000,
        alarmLocation: '',
        alarmLocationCity: '',
        alarmLocationLng: 113.937792,
        alarmLocationLat: 23.231841,
        status: 'waitHandle',
        tags: ',1553998378264924161,',
        vehicleId: 'sdAtcoWNsZTAwM01',
        vehicleAssetVin: 'N/A',
        vehicleAssetType: null,
        vehicleAssetYear: 2020,
        vehicleAssetEngine: null,
        detectorId: '1555101527948099586',
        detectorName: '未知的服务器指令',
      },
      {
        createDate: 1676527816809,
        updateDate: 1676527816809,
        createUser: null,
        updateUser: null,
        id: 112968997,
        name: '发动机过热',
        alarmLevel: 'Medium',
        alarmDescription: '发动机过热',
        alarmDate: 1676527816000,
        alarmLocation: '',
        alarmLocationCity: '',
        alarmLocationLng: 113.872328,
        alarmLocationLat: 23.236943,
        status: 'waitHandle',
        tags: ',1544875746406092801,',
        vehicleId: 'sdAtcoWNsZTAwM03',
        vehicleAssetVin: 'N/A',
        vehicleAssetType: null,
        vehicleAssetYear: 2020,
        vehicleAssetEngine: null,
        detectorId: '1555092893268672513',
        detectorName: '发动机过热',
      },
      {
        createDate: 1676526027622,
        updateDate: 1676526027622,
        createUser: null,
        updateUser: null,
        id: 112968992,
        name: '车辆远程启动异常',
        alarmLevel: 'Medium',
        alarmDescription:
          '车辆远程启动异常，当前发动机状态为：启动，消息指令：remote_start_error',
        alarmDate: 1676526027000,
        alarmLocation: '',
        alarmLocationCity: '',
        alarmLocationLng: 113.071531,
        alarmLocationLat: 23.224314,
        status: 'waitHandle',
        tags: null,
        vehicleId: 'acVmVsZTAwM04',
        vehicleAssetVin: 'N/A',
        vehicleAssetType: null,
        vehicleAssetYear: 2020,
        vehicleAssetEngine: null,
        detectorId: '1555364832725925890',
        detectorName: '车辆远程启动异常',
      },
      {
        createDate: 1676525880157,
        updateDate: 1676525880157,
        createUser: null,
        updateUser: null,
        id: 112968991,
        name: '车辆远程启动异常',
        alarmLevel: 'Medium',
        alarmDescription:
          '车辆远程启动异常，当前发动机状态为：启动，消息指令：remote_start_error',
        alarmDate: 1676525879000,
        alarmLocation: '',
        alarmLocationCity: '',
        alarmLocationLng: 113.058991,
        alarmLocationLat: 23.096162,
        status: 'waitHandle',
        tags: null,
        vehicleId: 'sdAtcoWNsZTAwM02',
        vehicleAssetVin: 'N/A',
        vehicleAssetType: null,
        vehicleAssetYear: 2020,
        vehicleAssetEngine: null,
        detectorId: '1555364832725925890',
        detectorName: '车辆远程启动异常',
      },
      {
        createDate: 1676522911330,
        updateDate: 1676522911330,
        createUser: null,
        updateUser: null,
        id: 112968985,
        name: '未知的服务器指令',
        alarmLevel: 'Medium',
        alarmDescription: '未知的服务器指令，指令：SQL',
        alarmDate: 1676522910000,
        alarmLocation: '',
        alarmLocationCity: '',
        alarmLocationLng: 113.880318,
        alarmLocationLat: 23.234261,
        status: 'waitHandle',
        tags: ',1553998378264924161,',
        vehicleId: 'sdAtcoWNsZTAwM01',
        vehicleAssetVin: 'N/A',
        vehicleAssetType: null,
        vehicleAssetYear: 2020,
        vehicleAssetEngine: null,
        detectorId: '1555101527948099586',
        detectorName: '未知的服务器指令',
      },
      {
        createDate: 1676522404902,
        updateDate: 1676522404902,
        createUser: null,
        updateUser: null,
        id: 112968984,
        name: '车辆远程启动异常',
        alarmLevel: 'Medium',
        alarmDescription:
          '车辆远程启动异常，当前发动机状态为：启动，消息指令：remote_start_error',
        alarmDate: 1676522404000,
        alarmLocation: '',
        alarmLocationCity: '',
        alarmLocationLng: 113.880318,
        alarmLocationLat: 23.234261,
        status: 'waitHandle',
        tags: null,
        vehicleId: 'acVmVsZTAwM04',
        vehicleAssetVin: 'N/A',
        vehicleAssetType: null,
        vehicleAssetYear: 2020,
        vehicleAssetEngine: null,
        detectorId: '1555364832725925890',
        detectorName: '车辆远程启动异常',
      },
      {
        createDate: 1676522274568,
        updateDate: 1676522274568,
        createUser: null,
        updateUser: null,
        id: 112968983,
        name: '车辆远程启动异常',
        alarmLevel: 'Medium',
        alarmDescription:
          '车辆远程启动异常，当前发动机状态为：启动，消息指令：remote_start_error',
        alarmDate: 1676522273000,
        alarmLocation: '',
        alarmLocationCity: '',
        alarmLocationLng: 113.001535,
        alarmLocationLat: 23.23523,
        status: 'waitHandle',
        tags: null,
        vehicleId: 'sdAtcoWNsZTAwM02',
        vehicleAssetVin: 'N/A',
        vehicleAssetType: null,
        vehicleAssetYear: 2020,
        vehicleAssetEngine: null,
        detectorId: '1555364832725925890',
        detectorName: '车辆远程启动异常',
      },
    ],
    pageable: {
      sort: { empty: false, sorted: true, unsorted: false },
      offset: 0,
      pageNumber: 0,
      pageSize: 10,
      paged: true,
      unpaged: false,
    },
    last: false,
    totalPages: 2,
    totalElements: 20,
    size: 10,
    number: 0,
    sort: { empty: false, sorted: true, unsorted: false },
    first: true,
    numberOfElements: 10,
    empty: false,
  }
}

// 获取告警属性列表
export function getAlertFieldsDt(params) {
  return [
    { displayName: '消息ID', id: 'alarm.messageId' },
    {
      displayName: '告警ID',
      id: 'alarm.alarmId',
    },
    { displayName: '告警url', id: 'alarm.alarmDetailUrl' },
    {
      displayName: '告警类型',
      id: 'alarm.alarmName',
    },
    { displayName: '告警级别', id: 'alarm.alarmLevel' },
    {
      displayName: '告警描述',
      id: 'alarm.alarmDescription',
    },
    { displayName: '告警地址', id: 'alarm.alarmLocation' },
    {
      displayName: '告警城市',
      id: 'alarm.alarmLocationCity',
    },
    { displayName: '告警位置：经度', id: 'alarm.alarmLocationLng' },
    {
      displayName: '告警位置：纬度',
      id: 'alarm.alarmLocationLat',
    },
    { displayName: '告警时间', id: 'alarm.alarmDate' },
    {
      displayName: '告警标签',
      id: 'alarm.tags',
    },
    { displayName: '资产编号', id: 'alarm.vehicleId' },
    {
      displayName: '检测器Id',
      id: 'alarm.detectorId',
    },
    { displayName: '检测器名字', id: 'alarm.detectorName' },
    { displayName: '检测器类型', id: 'alarm.detectionType' },
  ]
}

// 修改告警状态
export function updateStatusDt(data) {
  return request({
    url: `${cmdbPath}/asset-alarm/update-status`,
    method: 'post',
    data,
  })
}

// 获取告警详情
export function getAlertDetailsDt(id) {
  return {
    assetAlarm: {
      createDate: 1676529633470,
      updateDate: 1676529633470,
      createUser: null,
      updateUser: null,
      id: 112969016,
      name: '里程欺诈',
      alarmLevel: '0',
      alarmDescription:
        '车辆疑似发生里程欺诈行为，当前的里程数为：6534km，最新消息的里程数为：1000km',
      alarmDate: 1676529632000,
      alarmLocation: '',
      alarmLocationCity: '',
      alarmLocationLng: 113.900602,
      alarmLocationLat: 23.127896,
      status: '0',
      tags: null,
      vehicleId: 'acVmVsZTAwM04',
      vehicleAssetVin: 'N/A',
      vehicleAssetType: null,
      vehicleAssetYear: 2020,
      vehicleAssetEngine: null,
      detectorId: '1555364832725925890',
      detectorName: '车辆远程启动异常',
    },
    vehicleAsset: {
      createDate: 1676525304727,
      updateDate: 1676521768308,
      createUser: null,
      updateUser: null,
      id: 'acVmVsZTAwM04',
      vin: 'N/A',
      vehicleType: 'BYD',
      vehicleGroups: [],
      registrationDate: 1665921581603,
      lastActiveDate: 1676521768000,
      location: '113.884001,23.183599',
      locationDate: null,
      healthStatus: 'poor',
      healthStatusDate: 1676520837151,
      alarmInfo: {
        disasterNumber: 4,
        majorNumber: 0,
        minorNumber: 0,
        warningNumber: 0,
        infoNumber: 0,
      },
      odometer: null,
      odometerLastWeekPercent: null,
      batteryTemperature: null,
      batteryTemperatureLastWeekPercent: null,
    },
    // 资产事件轴数据
    timeLine: [
      {
        id: null,
        name: '里程欺诈',
        type: 'Statistics', // 最近六个月活动
        date: 0,
        description: null,
        count: 1,
        firstDate: 0,
        lastDate: 0,
        statisticsData: [
          { x: '2022-12', y: 25 },
          { x: '2022-11', y: 10 },
          {
            x: '2022-10',
            y: 6,
          },
          { x: '2022-09', y: 0 },
          { x: '2022-08', y: 5 },
          { x: '2022-07', y: 1 },
        ],
        fullDigitalTwin: null,
        alarmData: null,
        specialFields: null,
        defaultFields: null,
      },
      {
        id: 'doorunlockcommand',
        name: '车门解锁指令',
        type: 'Event',
        date: 1676520246000,
        description: '已发送车门解锁指令',
        count: 312,
        firstDate: 1676525304000,
        lastDate: 1676520246000,
        statisticsData: null,
        fullDigitalTwin: [
          {
            id: 228,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_motion_speed',
            value: '0',
            valueUpdateDate: 1676520246000,
            createDate: 1676525304732,
            updateDate: 1676520246779,
            displayName: '速度',
            valueType: 'DOUBLE',
            unit: 'km/h',
            unitSymbol: 'km/h',
            description: '当前车速，由内部车辆系统感知',
            constraints: {
              enumValues: null,
              max: 400.0,
              min: -100.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 229,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_message_name',
            value: 'Door Opened',
            valueUpdateDate: 1676520246000,
            createDate: 1676525304732,
            updateDate: 1676520246779,
            displayName: '消息名称',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: '消息名称',
            constraints: {
              enumValues: [
                'Driving Start',
                'Driving Stop',
                'Tester Present',
                'Unlock Doors',
                'Doors Unlocked',
                'Crash Detected',
                'System Status',
                'Reset',
                'Door Opened',
                'ECU Authentication Request',
                'DOOR_EVENT',
                'ALARM_EVENT',
                'MOTION_EVENT',
                'IGNITION_EVENT',
                'DIAGNOSTIC_EVENT',
                'GEAR_CHANGE_EVENT',
                'remote_start_error',
                'HARSH_BRAKING_EVENT',
                'TIRE_PRESSURE_EVENT',
                'HARSH_ACCELERATION_EVENT',
                'FIRMWARE_UPGRADE_TRIGGER',
                'FIRMWARE_UPGRADE_STATUS',
              ],
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 230,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_vehicleidentification_shortvin',
            value: 'N/A',
            valueUpdateDate: 1676520246000,
            createDate: 1676525304733,
            updateDate: 1676520246779,
            displayName: '短VIN',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: '短车辆识别',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 231,
            vehicleId: 'acVmVsZTAwM04',
            name: 'throttle_opening',
            value: '0',
            valueUpdateDate: 1676520246000,
            createDate: 1676525304733,
            updateDate: 1676520246779,
            displayName: 'vehicle_throttle_opening',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: 'vehicle_throttle_opening',
            constraints: null,
          },
          {
            id: 232,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_location_currentlocation_longitude',
            value: '113.860501',
            valueUpdateDate: 1676520246000,
            createDate: 1676525304733,
            updateDate: 1676520246779,
            displayName: '经度',
            valueType: 'DOUBLE',
            unit: 'deg',
            unitSymbol: 'DEGREES',
            description: '车辆的经度',
            constraints: {
              enumValues: null,
              max: 180.0,
              min: -180.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 242,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_vehicleidentification_year',
            value: '2020',
            valueUpdateDate: 1676520246000,
            createDate: 1676525304737,
            updateDate: 1676520246781,
            displayName: '车辆型号年份',
            valueType: 'LONG',
            unit: null,
            unitSymbol: null,
            description: '车辆型号年份',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 233,
            vehicleId: 'acVmVsZTAwM04',
            name: 'body_domain_bus_load',
            value: '0',
            valueUpdateDate: 1676520246000,
            createDate: 1676525304734,
            updateDate: 1676520246779,
            displayName: '车身域总线负载',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: 'vehicle_body_domain_bus_load',
            constraints: null,
          },
          {
            id: 234,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_odometer',
            value: '496716',
            valueUpdateDate: 1676520246000,
            createDate: 1676525304734,
            updateDate: 1676520246780,
            displayName: '里程表',
            valueType: 'DOUBLE',
            unit: 'km',
            unitSymbol: 'km',
            description: '里程表当前读数',
            constraints: {
              enumValues: null,
              max: null,
              min: 0.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 236,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_location_currentlocation_latitude',
            value: '23.217343',
            valueUpdateDate: 1676520246000,
            createDate: 1676525304735,
            updateDate: 1676520246780,
            displayName: '纬度',
            valueType: 'DOUBLE',
            unit: 'deg',
            unitSymbol: 'DEGREES',
            description: '车辆的纬度',
            constraints: {
              enumValues: null,
              max: 90.0,
              min: -90.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 237,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_vehicleidentification_model',
            value: 'BYD',
            valueUpdateDate: 1676520246000,
            createDate: 1676525304735,
            updateDate: 1676520246780,
            displayName: '车辆型号',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: '车辆的型号',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 238,
            vehicleId: 'acVmVsZTAwM04',
            name: 'power_domain_bus_load',
            value: '0',
            valueUpdateDate: 1676520246000,
            createDate: 1676525304735,
            updateDate: 1676520246780,
            displayName: '动力域总线负载',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: 'vehicle_power_domain_bus_load',
            constraints: null,
          },
          {
            id: 239,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_message_senttimestamp',
            value: '1676520246000',
            valueUpdateDate: 1676520246000,
            createDate: 1676525304736,
            updateDate: 1676520246780,
            displayName: '发送时间戳',
            valueType: 'TIMESTAMP',
            unit: null,
            unitSymbol: null,
            description: '消息源发送消息的时间戳',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 240,
            vehicleId: 'acVmVsZTAwM04',
            name: 'intelligent_driving_domain_bus_load',
            value: '0',
            valueUpdateDate: 1676520246000,
            createDate: 1676525304736,
            updateDate: 1676520246780,
            displayName: '智驾域总线负载',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: 'vehicle_intelligent_driving_domain_bus_load',
            constraints: null,
          },
          {
            id: 241,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_vehicleidentification_id',
            value: 'acVmVsZTAwM04',
            valueUpdateDate: 1676520246000,
            createDate: 1676525304736,
            updateDate: 1676520246780,
            displayName: '车辆ID',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: '车辆的唯一标识符',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 243,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_engine_speed',
            value: '0',
            valueUpdateDate: 1676520246000,
            createDate: 1676525304737,
            updateDate: 1676520246781,
            displayName: '引擎转速',
            valueType: 'LONG',
            unit: 'rpm',
            unitSymbol: 'rpm',
            description: '发动机转速，以每分钟旋转量计',
            constraints: {
              enumValues: null,
              max: 20000.0,
              min: 0.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 253,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_state',
            value: 'false',
            valueUpdateDate: 1676520246000,
            createDate: 1676528619880,
            updateDate: 1676520246781,
            displayName: '发动机状态',
            valueType: 'BOOLEAN',
            unit: null,
            unitSymbol: null,
            description: '当前发动机或电动机状态。',
            constraints: {
              enumValues: ['关闭', '开启'],
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 456,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_battery_temperature',
            value: '37',
            valueUpdateDate: 1676520246000,
            createDate: 1660702009253,
            updateDate: 1676520246779,
            displayName: '电池温度',
            valueType: 'DOUBLE',
            unit: 'C',
            unitSymbol: '°C',
            description: '电池的估计温度',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
        ],
        alarmData: null,
        specialFields: [
          {
            id: 253,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_state',
            value: 'false',
            valueUpdateDate: 1676520246000,
            createDate: 1676528619880,
            updateDate: 1676520246781,
            displayName: '发动机状态',
            valueType: 'BOOLEAN',
            unit: null,
            unitSymbol: null,
            description: '当前发动机或电动机状态。',
            constraints: {
              enumValues: ['关闭', '开启'],
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
        ],
        defaultFields: [
          {
            id: 228,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_motion_speed',
            value: '0',
            valueUpdateDate: 1676520246000,
            createDate: 1676525304732,
            updateDate: 1676520246779,
            displayName: '速度',
            valueType: 'DOUBLE',
            unit: 'km/h',
            unitSymbol: 'km/h',
            description: '当前车速，由内部车辆系统感知',
            constraints: {
              enumValues: null,
              max: 400.0,
              min: -100.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 243,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_engine_speed',
            value: '0',
            valueUpdateDate: 1676520246000,
            createDate: 1676525304737,
            updateDate: 1676520246781,
            displayName: '引擎转速',
            valueType: 'LONG',
            unit: 'rpm',
            unitSymbol: 'rpm',
            description: '发动机转速，以每分钟旋转量计',
            constraints: {
              enumValues: null,
              max: 20000.0,
              min: 0.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 234,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_odometer',
            value: '496716',
            valueUpdateDate: 1676520246000,
            createDate: 1676525304734,
            updateDate: 1676520246780,
            displayName: '里程表',
            valueType: 'DOUBLE',
            unit: 'km',
            unitSymbol: 'km',
            description: '里程表当前读数',
            constraints: {
              enumValues: null,
              max: null,
              min: 0.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
        ],
      },
      {
        id: 'driverStart',
        name: '开始驾驶',
        type: 'Event',
        date: 1676525804000,
        description: '一次新的驾驶已启动',
        count: 287,
        firstDate: 1659928570000,
        lastDate: 1676525804000,
        statisticsData: null,
        fullDigitalTwin: [
          {
            id: 474,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_energystorage_fuelsystem_fuellevel',
            value: '47',
            valueUpdateDate: 1676525804000,
            createDate: 1660724495084,
            updateDate: 1676525804945,
            displayName: '燃油表',
            valueType: 'DOUBLE',
            unit: '%',
            unitSymbol: '%',
            description: '当前燃油液位占油箱总容量的百分比 ',
            constraints: {
              enumValues: null,
              max: 100.0,
              min: 0.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 228,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_motion_speed',
            value: '0',
            valueUpdateDate: 1676525804000,
            createDate: 1676525304732,
            updateDate: 1676525804945,
            displayName: '速度',
            valueType: 'DOUBLE',
            unit: 'km/h',
            unitSymbol: 'km/h',
            description: '当前车速，由内部车辆系统感知',
            constraints: {
              enumValues: null,
              max: 400.0,
              min: -100.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 231,
            vehicleId: 'acVmVsZTAwM04',
            name: 'throttle_opening',
            value: '0',
            valueUpdateDate: 1676525804000,
            createDate: 1676525304733,
            updateDate: 1676525804946,
            displayName: 'vehicle_throttle_opening',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: 'vehicle_throttle_opening',
            constraints: null,
          },
          {
            id: 242,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_vehicleidentification_year',
            value: '2020',
            valueUpdateDate: 1676525804000,
            createDate: 1676525304737,
            updateDate: 1676525804947,
            displayName: '车辆型号年份',
            valueType: 'LONG',
            unit: null,
            unitSymbol: null,
            description: '车辆型号年份',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 234,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_odometer',
            value: '15883',
            valueUpdateDate: 1676525804000,
            createDate: 1676525304734,
            updateDate: 1676525804946,
            displayName: '里程表',
            valueType: 'DOUBLE',
            unit: 'km',
            unitSymbol: 'km',
            description: '里程表当前读数',
            constraints: {
              enumValues: null,
              max: null,
              min: 0.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 236,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_location_currentlocation_latitude',
            value: '23.175256',
            valueUpdateDate: 1676525804000,
            createDate: 1676525304735,
            updateDate: 1676525804946,
            displayName: '纬度',
            valueType: 'DOUBLE',
            unit: 'deg',
            unitSymbol: 'DEGREES',
            description: '车辆的纬度',
            constraints: {
              enumValues: null,
              max: 90.0,
              min: -90.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 237,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_vehicleidentification_model',
            value: 'BYD',
            valueUpdateDate: 1676525804000,
            createDate: 1676525304735,
            updateDate: 1676525804946,
            displayName: '车辆型号',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: '车辆的型号',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 456,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_battery_temperature',
            value: '51',
            valueUpdateDate: 1676525804000,
            createDate: 1660702009253,
            updateDate: 1676525804945,
            displayName: '电池温度',
            valueType: 'DOUBLE',
            unit: 'C',
            unitSymbol: '°C',
            description: '电池的估计温度',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 229,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_message_name',
            value: 'Driving Stop',
            valueUpdateDate: 1676525804000,
            createDate: 1676525304732,
            updateDate: 1676525804946,
            displayName: '消息名称',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: '消息名称',
            constraints: {
              enumValues: [
                'Driving Start',
                'Driving Stop',
                'Tester Present',
                'Unlock Doors',
                'Doors Unlocked',
                'Crash Detected',
                'System Status',
                'Reset',
                'Door Opened',
                'ECU Authentication Request',
                'DOOR_EVENT',
                'ALARM_EVENT',
                'MOTION_EVENT',
                'IGNITION_EVENT',
                'DIAGNOSTIC_EVENT',
                'GEAR_CHANGE_EVENT',
                'remote_start_error',
                'HARSH_BRAKING_EVENT',
                'TIRE_PRESSURE_EVENT',
                'HARSH_ACCELERATION_EVENT',
                'FIRMWARE_UPGRADE_TRIGGER',
                'FIRMWARE_UPGRADE_STATUS',
              ],
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 230,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_vehicleidentification_shortvin',
            value: 'N/A',
            valueUpdateDate: 1676525804000,
            createDate: 1676525304733,
            updateDate: 1676525804946,
            displayName: '短VIN',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: '短车辆识别',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 239,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_message_senttimestamp',
            value: '1676525804000',
            valueUpdateDate: 1676525804000,
            createDate: 1676525304736,
            updateDate: 1676525804947,
            displayName: '发送时间戳',
            valueType: 'TIMESTAMP',
            unit: null,
            unitSymbol: null,
            description: '消息源发送消息的时间戳',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 232,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_location_currentlocation_longitude',
            value: '113.056655',
            valueUpdateDate: 1676525804000,
            createDate: 1676525304733,
            updateDate: 1676525804946,
            displayName: '经度',
            valueType: 'DOUBLE',
            unit: 'deg',
            unitSymbol: 'DEGREES',
            description: '车辆的经度',
            constraints: {
              enumValues: null,
              max: 180.0,
              min: -180.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 233,
            vehicleId: 'acVmVsZTAwM04',
            name: 'body_domain_bus_load',
            value: '0',
            valueUpdateDate: 1676525804000,
            createDate: 1676525304734,
            updateDate: 1676525804946,
            displayName: '车身域总线负载',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: 'vehicle_body_domain_bus_load',
            constraints: null,
          },
          {
            id: 466,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_trip_journey_state',
            value: 'Driving Stop',
            valueUpdateDate: 1676525804000,
            createDate: 1660717231793,
            updateDate: 1676525804947,
            displayName: '旅程当前状态',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: '旅程的当前状态',
            constraints: {
              enumValues: ['Drive Start', 'In Progress', 'Drive End'],
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 238,
            vehicleId: 'acVmVsZTAwM04',
            name: 'power_domain_bus_load',
            value: '0',
            valueUpdateDate: 1676525804000,
            createDate: 1676525304735,
            updateDate: 1676525804947,
            displayName: '动力域总线负载',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: 'vehicle_power_domain_bus_load',
            constraints: null,
          },
          {
            id: 241,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_vehicleidentification_id',
            value: 'acVmVsZTAwM04',
            valueUpdateDate: 1676525804000,
            createDate: 1676525304736,
            updateDate: 1676525804947,
            displayName: '车辆ID',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: '车辆的唯一标识符',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 243,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_engine_speed',
            value: '0',
            valueUpdateDate: 1676525804000,
            createDate: 1676525304737,
            updateDate: 1676525804947,
            displayName: '引擎转速',
            valueType: 'LONG',
            unit: 'rpm',
            unitSymbol: 'rpm',
            description: '发动机转速，以每分钟旋转量计',
            constraints: {
              enumValues: null,
              max: 20000.0,
              min: 0.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 253,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_state',
            value: 'false',
            valueUpdateDate: 1676525804000,
            createDate: 1676528619880,
            updateDate: 1676525804947,
            displayName: '发动机状态',
            valueType: 'BOOLEAN',
            unit: null,
            unitSymbol: null,
            description: '当前发动机或电动机状态。',
            constraints: {
              enumValues: ['关闭', '开启'],
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 240,
            vehicleId: 'acVmVsZTAwM04',
            name: 'intelligent_driving_domain_bus_load',
            value: '0',
            valueUpdateDate: 1676525804000,
            createDate: 1676525304736,
            updateDate: 1676525804947,
            displayName: '智驾域总线负载',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: 'vehicle_intelligent_driving_domain_bus_load',
            constraints: null,
          },
        ],
        alarmData: null,
        specialFields: [
          {
            id: 229,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_message_name',
            value: 'Driving Stop',
            valueUpdateDate: 1676525804000,
            createDate: 1676525304732,
            updateDate: 1676525804946,
            displayName: '消息名称',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: '消息名称',
            constraints: {
              enumValues: [
                'Driving Start',
                'Driving Stop',
                'Tester Present',
                'Unlock Doors',
                'Doors Unlocked',
                'Crash Detected',
                'System Status',
                'Reset',
                'Door Opened',
                'ECU Authentication Request',
                'DOOR_EVENT',
                'ALARM_EVENT',
                'MOTION_EVENT',
                'IGNITION_EVENT',
                'DIAGNOSTIC_EVENT',
                'GEAR_CHANGE_EVENT',
                'remote_start_error',
                'HARSH_BRAKING_EVENT',
                'TIRE_PRESSURE_EVENT',
                'HARSH_ACCELERATION_EVENT',
                'FIRMWARE_UPGRADE_TRIGGER',
                'FIRMWARE_UPGRADE_STATUS',
              ],
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
        ],
        defaultFields: [
          {
            id: 253,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_state',
            value: 'false',
            valueUpdateDate: 1676525804000,
            createDate: 1676528619880,
            updateDate: 1676525804947,
            displayName: '发动机状态',
            valueType: 'BOOLEAN',
            unit: null,
            unitSymbol: null,
            description: '当前发动机或电动机状态。',
            constraints: {
              enumValues: ['关闭', '开启'],
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 228,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_motion_speed',
            value: '0',
            valueUpdateDate: 1676525804000,
            createDate: 1676525304732,
            updateDate: 1676525804945,
            displayName: '速度',
            valueType: 'DOUBLE',
            unit: 'km/h',
            unitSymbol: 'km/h',
            description: '当前车速，由内部车辆系统感知',
            constraints: {
              enumValues: null,
              max: 400.0,
              min: -100.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 243,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_engine_speed',
            value: '0',
            valueUpdateDate: 1676525804000,
            createDate: 1676525304737,
            updateDate: 1676525804947,
            displayName: '引擎转速',
            valueType: 'LONG',
            unit: 'rpm',
            unitSymbol: 'rpm',
            description: '发动机转速，以每分钟旋转量计',
            constraints: {
              enumValues: null,
              max: 20000.0,
              min: 0.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 234,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_odometer',
            value: '15883',
            valueUpdateDate: 1676525804000,
            createDate: 1676525304734,
            updateDate: 1676525804946,
            displayName: '里程表',
            valueType: 'DOUBLE',
            unit: 'km',
            unitSymbol: 'km',
            description: '里程表当前读数',
            constraints: {
              enumValues: null,
              max: null,
              min: 0.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 474,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_energystorage_fuelsystem_fuellevel',
            value: '47',
            valueUpdateDate: 1676525804000,
            createDate: 1660724495084,
            updateDate: 1676525804945,
            displayName: '燃油表',
            valueType: 'DOUBLE',
            unit: '%',
            unitSymbol: '%',
            description: '当前燃油液位占油箱总容量的百分比 ',
            constraints: {
              enumValues: null,
              max: 100.0,
              min: 0.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
        ],
      },
      {
        id: 'DriverEnd',
        name: '结束驾驶',
        type: 'Event',
        date: 1676526916000,
        description: '一次驾驶已结束',
        count: 318,
        firstDate: 1676525384000,
        lastDate: 1676526916000,
        statisticsData: null,
        fullDigitalTwin: [
          {
            id: 474,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_energystorage_fuelsystem_fuellevel',
            value: '47',
            valueUpdateDate: 1676525906000,
            createDate: 1660724495084,
            updateDate: 1676525907159,
            displayName: '燃油表',
            valueType: 'DOUBLE',
            unit: '%',
            unitSymbol: '%',
            description: '当前燃油液位占油箱总容量的百分比 ',
            constraints: {
              enumValues: null,
              max: 100.0,
              min: 0.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 228,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_motion_speed',
            value: '10',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304732,
            updateDate: 1676525907159,
            displayName: '速度',
            valueType: 'DOUBLE',
            unit: 'km/h',
            unitSymbol: 'km/h',
            description: '当前车速，由内部车辆系统感知',
            constraints: {
              enumValues: null,
              max: 400.0,
              min: -100.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 231,
            vehicleId: 'acVmVsZTAwM04',
            name: 'throttle_opening',
            value: '32',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304733,
            updateDate: 1676525907159,
            displayName: 'vehicle_throttle_opening',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: 'vehicle_throttle_opening',
            constraints: null,
          },
          {
            id: 242,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_vehicleidentification_year',
            value: '2020',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304737,
            updateDate: 1676525907161,
            displayName: '车辆型号年份',
            valueType: 'LONG',
            unit: null,
            unitSymbol: null,
            description: '车辆型号年份',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 234,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_odometer',
            value: '15883',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304734,
            updateDate: 1676525907160,
            displayName: '里程表',
            valueType: 'DOUBLE',
            unit: 'km',
            unitSymbol: 'km',
            description: '里程表当前读数',
            constraints: {
              enumValues: null,
              max: null,
              min: 0.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 236,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_location_currentlocation_latitude',
            value: '23.175256',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304735,
            updateDate: 1676525907160,
            displayName: '纬度',
            valueType: 'DOUBLE',
            unit: 'deg',
            unitSymbol: 'DEGREES',
            description: '车辆的纬度',
            constraints: {
              enumValues: null,
              max: 90.0,
              min: -90.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 237,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_vehicleidentification_model',
            value: 'BYD',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304735,
            updateDate: 1676525907160,
            displayName: '车辆型号',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: '车辆的型号',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 456,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_battery_temperature',
            value: '34',
            valueUpdateDate: 1676525906000,
            createDate: 1660702009253,
            updateDate: 1676525907159,
            displayName: '电池温度',
            valueType: 'DOUBLE',
            unit: 'C',
            unitSymbol: '°C',
            description: '电池的估计温度',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 229,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_message_name',
            value: 'Driving Start',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304732,
            updateDate: 1676525907159,
            displayName: '消息名称',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: '消息名称',
            constraints: {
              enumValues: [
                'Driving Start',
                'Driving Stop',
                'Tester Present',
                'Unlock Doors',
                'Doors Unlocked',
                'Crash Detected',
                'System Status',
                'Reset',
                'Door Opened',
                'ECU Authentication Request',
                'DOOR_EVENT',
                'ALARM_EVENT',
                'MOTION_EVENT',
                'IGNITION_EVENT',
                'DIAGNOSTIC_EVENT',
                'GEAR_CHANGE_EVENT',
                'remote_start_error',
                'HARSH_BRAKING_EVENT',
                'TIRE_PRESSURE_EVENT',
                'HARSH_ACCELERATION_EVENT',
                'FIRMWARE_UPGRADE_TRIGGER',
                'FIRMWARE_UPGRADE_STATUS',
              ],
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 230,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_vehicleidentification_shortvin',
            value: 'N/A',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304733,
            updateDate: 1676525907159,
            displayName: '短VIN',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: '短车辆识别',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 239,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_message_senttimestamp',
            value: '1676525906000',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304736,
            updateDate: 1676525907161,
            displayName: '发送时间戳',
            valueType: 'TIMESTAMP',
            unit: null,
            unitSymbol: null,
            description: '消息源发送消息的时间戳',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 232,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_location_currentlocation_longitude',
            value: '113.056655',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304733,
            updateDate: 1676525907160,
            displayName: '经度',
            valueType: 'DOUBLE',
            unit: 'deg',
            unitSymbol: 'DEGREES',
            description: '车辆的经度',
            constraints: {
              enumValues: null,
              max: 180.0,
              min: -180.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 233,
            vehicleId: 'acVmVsZTAwM04',
            name: 'body_domain_bus_load',
            value: '73',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304734,
            updateDate: 1676525907160,
            displayName: '车身域总线负载',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: 'vehicle_body_domain_bus_load',
            constraints: null,
          },
          {
            id: 466,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_trip_journey_state',
            value: 'Driving Start',
            valueUpdateDate: 1676525906000,
            createDate: 1660717231793,
            updateDate: 1676525907160,
            displayName: '旅程当前状态',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: '旅程的当前状态',
            constraints: {
              enumValues: ['Drive Start', 'In Progress', 'Drive End'],
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 238,
            vehicleId: 'acVmVsZTAwM04',
            name: 'power_domain_bus_load',
            value: '52',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304735,
            updateDate: 1676525907160,
            displayName: '动力域总线负载',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: 'vehicle_power_domain_bus_load',
            constraints: null,
          },
          {
            id: 241,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_vehicleidentification_id',
            value: 'acVmVsZTAwM04',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304736,
            updateDate: 1676525907161,
            displayName: '车辆ID',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: '车辆的唯一标识符',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 243,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_engine_speed',
            value: '1645',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304737,
            updateDate: 1676525907161,
            displayName: '引擎转速',
            valueType: 'LONG',
            unit: 'rpm',
            unitSymbol: 'rpm',
            description: '发动机转速，以每分钟旋转量计',
            constraints: {
              enumValues: null,
              max: 20000.0,
              min: 0.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 253,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_state',
            value: 'true',
            valueUpdateDate: 1676525906000,
            createDate: 1676528619880,
            updateDate: 1676525907161,
            displayName: '发动机状态',
            valueType: 'BOOLEAN',
            unit: null,
            unitSymbol: null,
            description: '当前发动机或电动机状态。',
            constraints: {
              enumValues: ['关闭', '开启'],
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 240,
            vehicleId: 'acVmVsZTAwM04',
            name: 'intelligent_driving_domain_bus_load',
            value: '57',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304736,
            updateDate: 1676525907161,
            displayName: '智驾域总线负载',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: 'vehicle_intelligent_driving_domain_bus_load',
            constraints: null,
          },
        ],
        alarmData: null,
        specialFields: [
          {
            id: 229,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_message_name',
            value: 'Driving Start',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304732,
            updateDate: 1676525907159,
            displayName: '消息名称',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: '消息名称',
            constraints: {
              enumValues: [
                'Driving Start',
                'Driving Stop',
                'Tester Present',
                'Unlock Doors',
                'Doors Unlocked',
                'Crash Detected',
                'System Status',
                'Reset',
                'Door Opened',
                'ECU Authentication Request',
                'DOOR_EVENT',
                'ALARM_EVENT',
                'MOTION_EVENT',
                'IGNITION_EVENT',
                'DIAGNOSTIC_EVENT',
                'GEAR_CHANGE_EVENT',
                'remote_start_error',
                'HARSH_BRAKING_EVENT',
                'TIRE_PRESSURE_EVENT',
                'HARSH_ACCELERATION_EVENT',
                'FIRMWARE_UPGRADE_TRIGGER',
                'FIRMWARE_UPGRADE_STATUS',
              ],
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
        ],
        defaultFields: [
          {
            id: 253,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_state',
            value: 'true',
            valueUpdateDate: 1676525906000,
            createDate: 1676528619880,
            updateDate: 1676525907161,
            displayName: '发动机状态',
            valueType: 'BOOLEAN',
            unit: null,
            unitSymbol: null,
            description: '当前发动机或电动机状态。',
            constraints: {
              enumValues: ['关闭', '开启'],
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 228,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_motion_speed',
            value: '10',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304732,
            updateDate: 1676525907159,
            displayName: '速度',
            valueType: 'DOUBLE',
            unit: 'km/h',
            unitSymbol: 'km/h',
            description: '当前车速，由内部车辆系统感知',
            constraints: {
              enumValues: null,
              max: 400.0,
              min: -100.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 243,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_engine_speed',
            value: '1645',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304737,
            updateDate: 1676525907161,
            displayName: '引擎转速',
            valueType: 'LONG',
            unit: 'rpm',
            unitSymbol: 'rpm',
            description: '发动机转速，以每分钟旋转量计',
            constraints: {
              enumValues: null,
              max: 20000.0,
              min: 0.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 234,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_odometer',
            value: '15883',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304734,
            updateDate: 1676525907160,
            displayName: '里程表',
            valueType: 'DOUBLE',
            unit: 'km',
            unitSymbol: 'km',
            description: '里程表当前读数',
            constraints: {
              enumValues: null,
              max: null,
              min: 0.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 474,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_energystorage_fuelsystem_fuellevel',
            value: '47',
            valueUpdateDate: 1676525906000,
            createDate: 1660724495084,
            updateDate: 1676525907159,
            displayName: '燃油表',
            valueType: 'DOUBLE',
            unit: '%',
            unitSymbol: '%',
            description: '当前燃油液位占油箱总容量的百分比 ',
            constraints: {
              enumValues: null,
              max: 100.0,
              min: 0.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
        ],
      },
      {
        id: 'OBD',
        name: 'Tester present',
        type: 'Event', // 无产生告警的事件
        date: 1676526996000,
        description: '已连接OBD2设备',
        count: 1,
        firstDate: 1676525314000,
        lastDate: 1676526996000,
        statisticsData: null,
        fullDigitalTwin: [
          {
            id: 474,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_energystorage_fuelsystem_fuellevel',
            value: '47',
            valueUpdateDate: 1676525906000,
            createDate: 1660724495084,
            updateDate: 1676525907159,
            displayName: '燃油表',
            valueType: 'DOUBLE',
            unit: '%',
            unitSymbol: '%',
            description: '当前燃油液位占油箱总容量的百分比 ',
            constraints: {
              enumValues: null,
              max: 100.0,
              min: 0.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 228,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_motion_speed',
            value: '10',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304732,
            updateDate: 1676525907159,
            displayName: '速度',
            valueType: 'DOUBLE',
            unit: 'km/h',
            unitSymbol: 'km/h',
            description: '当前车速，由内部车辆系统感知',
            constraints: {
              enumValues: null,
              max: 400.0,
              min: -100.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 231,
            vehicleId: 'acVmVsZTAwM04',
            name: 'throttle_opening',
            value: '32',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304733,
            updateDate: 1676525907159,
            displayName: 'vehicle_throttle_opening',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: 'vehicle_throttle_opening',
            constraints: null,
          },
          {
            id: 242,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_vehicleidentification_year',
            value: '2020',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304737,
            updateDate: 1676525907161,
            displayName: '车辆型号年份',
            valueType: 'LONG',
            unit: null,
            unitSymbol: null,
            description: '车辆型号年份',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 234,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_odometer',
            value: '15883',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304734,
            updateDate: 1676525907160,
            displayName: '里程表',
            valueType: 'DOUBLE',
            unit: 'km',
            unitSymbol: 'km',
            description: '里程表当前读数',
            constraints: {
              enumValues: null,
              max: null,
              min: 0.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 236,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_location_currentlocation_latitude',
            value: '23.175256',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304735,
            updateDate: 1676525907160,
            displayName: '纬度',
            valueType: 'DOUBLE',
            unit: 'deg',
            unitSymbol: 'DEGREES',
            description: '车辆的纬度',
            constraints: {
              enumValues: null,
              max: 90.0,
              min: -90.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 237,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_vehicleidentification_model',
            value: 'BYD',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304735,
            updateDate: 1676525907160,
            displayName: '车辆型号',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: '车辆的型号',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 456,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_battery_temperature',
            value: '34',
            valueUpdateDate: 1676525906000,
            createDate: 1660702009253,
            updateDate: 1676525907159,
            displayName: '电池温度',
            valueType: 'DOUBLE',
            unit: 'C',
            unitSymbol: '°C',
            description: '电池的估计温度',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 229,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_message_name',
            value: 'Driving Start',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304732,
            updateDate: 1676525907159,
            displayName: '消息名称',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: '消息名称',
            constraints: {
              enumValues: [
                'Driving Start',
                'Driving Stop',
                'Tester Present',
                'Unlock Doors',
                'Doors Unlocked',
                'Crash Detected',
                'System Status',
                'Reset',
                'Door Opened',
                'ECU Authentication Request',
                'DOOR_EVENT',
                'ALARM_EVENT',
                'MOTION_EVENT',
                'IGNITION_EVENT',
                'DIAGNOSTIC_EVENT',
                'GEAR_CHANGE_EVENT',
                'remote_start_error',
                'HARSH_BRAKING_EVENT',
                'TIRE_PRESSURE_EVENT',
                'HARSH_ACCELERATION_EVENT',
                'FIRMWARE_UPGRADE_TRIGGER',
                'FIRMWARE_UPGRADE_STATUS',
              ],
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 230,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_vehicleidentification_shortvin',
            value: 'N/A',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304733,
            updateDate: 1676525907159,
            displayName: '短VIN',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: '短车辆识别',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 239,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_message_senttimestamp',
            value: '1676525906000',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304736,
            updateDate: 1676525907161,
            displayName: '发送时间戳',
            valueType: 'TIMESTAMP',
            unit: null,
            unitSymbol: null,
            description: '消息源发送消息的时间戳',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 232,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_location_currentlocation_longitude',
            value: '113.056655',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304733,
            updateDate: 1676525907160,
            displayName: '经度',
            valueType: 'DOUBLE',
            unit: 'deg',
            unitSymbol: 'DEGREES',
            description: '车辆的经度',
            constraints: {
              enumValues: null,
              max: 180.0,
              min: -180.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 233,
            vehicleId: 'acVmVsZTAwM04',
            name: 'body_domain_bus_load',
            value: '73',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304734,
            updateDate: 1676525907160,
            displayName: '车身域总线负载',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: 'vehicle_body_domain_bus_load',
            constraints: null,
          },
          {
            id: 466,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_trip_journey_state',
            value: 'Driving Start',
            valueUpdateDate: 1676525906000,
            createDate: 1660717231793,
            updateDate: 1676525907160,
            displayName: '旅程当前状态',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: '旅程的当前状态',
            constraints: {
              enumValues: ['Drive Start', 'In Progress', 'Drive End'],
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 238,
            vehicleId: 'acVmVsZTAwM04',
            name: 'power_domain_bus_load',
            value: '52',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304735,
            updateDate: 1676525907160,
            displayName: '动力域总线负载',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: 'vehicle_power_domain_bus_load',
            constraints: null,
          },
          {
            id: 241,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_vehicleidentification_id',
            value: 'acVmVsZTAwM04',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304736,
            updateDate: 1676525907161,
            displayName: '车辆ID',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: '车辆的唯一标识符',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 243,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_engine_speed',
            value: '1645',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304737,
            updateDate: 1676525907161,
            displayName: '引擎转速',
            valueType: 'LONG',
            unit: 'rpm',
            unitSymbol: 'rpm',
            description: '发动机转速，以每分钟旋转量计',
            constraints: {
              enumValues: null,
              max: 20000.0,
              min: 0.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 253,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_state',
            value: 'true',
            valueUpdateDate: 1676525906000,
            createDate: 1676528619880,
            updateDate: 1676525907161,
            displayName: '发动机状态',
            valueType: 'BOOLEAN',
            unit: null,
            unitSymbol: null,
            description: '当前发动机或电动机状态。',
            constraints: {
              enumValues: ['关闭', '开启'],
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 240,
            vehicleId: 'acVmVsZTAwM04',
            name: 'intelligent_driving_domain_bus_load',
            value: '57',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304736,
            updateDate: 1676525907161,
            displayName: '智驾域总线负载',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: 'vehicle_intelligent_driving_domain_bus_load',
            constraints: null,
          },
        ],
        alarmData: null,
        specialFields: [
          {
            id: 229,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_message_name',
            value: 'Driving Start',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304732,
            updateDate: 1676525907159,
            displayName: '消息名称',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: '消息名称',
            constraints: {
              enumValues: [
                'Driving Start',
                'Driving Stop',
                'Tester Present',
                'Unlock Doors',
                'Doors Unlocked',
                'Crash Detected',
                'System Status',
                'Reset',
                'Door Opened',
                'ECU Authentication Request',
                'DOOR_EVENT',
                'ALARM_EVENT',
                'MOTION_EVENT',
                'IGNITION_EVENT',
                'DIAGNOSTIC_EVENT',
                'GEAR_CHANGE_EVENT',
                'remote_start_error',
                'HARSH_BRAKING_EVENT',
                'TIRE_PRESSURE_EVENT',
                'HARSH_ACCELERATION_EVENT',
                'FIRMWARE_UPGRADE_TRIGGER',
                'FIRMWARE_UPGRADE_STATUS',
              ],
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
        ],
        defaultFields: [
          {
            id: 253,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_state',
            value: 'true',
            valueUpdateDate: 1676525906000,
            createDate: 1676528619880,
            updateDate: 1676525907161,
            displayName: '发动机状态',
            valueType: 'BOOLEAN',
            unit: null,
            unitSymbol: null,
            description: '当前发动机或电动机状态。',
            constraints: {
              enumValues: ['关闭', '开启'],
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 228,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_motion_speed',
            value: '10',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304732,
            updateDate: 1676525907159,
            displayName: '速度',
            valueType: 'DOUBLE',
            unit: 'km/h',
            unitSymbol: 'km/h',
            description: '当前车速，由内部车辆系统感知',
            constraints: {
              enumValues: null,
              max: 400.0,
              min: -100.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 243,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_engine_speed',
            value: '1645',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304737,
            updateDate: 1676525907161,
            displayName: '引擎转速',
            valueType: 'LONG',
            unit: 'rpm',
            unitSymbol: 'rpm',
            description: '发动机转速，以每分钟旋转量计',
            constraints: {
              enumValues: null,
              max: 20000.0,
              min: 0.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 234,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_odometer',
            value: '15883',
            valueUpdateDate: 1676525906000,
            createDate: 1676525304734,
            updateDate: 1676525907160,
            displayName: '里程表',
            valueType: 'DOUBLE',
            unit: 'km',
            unitSymbol: 'km',
            description: '里程表当前读数',
            constraints: {
              enumValues: null,
              max: null,
              min: 0.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 474,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_energystorage_fuelsystem_fuellevel',
            value: '47',
            valueUpdateDate: 1676525906000,
            createDate: 1660724495084,
            updateDate: 1676525907159,
            displayName: '燃油表',
            valueType: 'DOUBLE',
            unit: '%',
            unitSymbol: '%',
            description: '当前燃油液位占油箱总容量的百分比 ',
            constraints: {
              enumValues: null,
              max: 100.0,
              min: 0.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
        ],
      },
      {
        id: null,
        name: '里程欺诈',
        type: 'Alarm', // 产生告警事件
        date: 1676529632000,
        description: null,
        count: 1,
        firstDate: 1676529632000,
        lastDate: 1676529632000,
        statisticsData: null,
        fullDigitalTwin: [
          {
            id: 474,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_energystorage_fuelsystem_fuellevel',
            value: '9',
            valueUpdateDate: 1676529632000,
            createDate: 1660724495084,
            updateDate: 1676529633127,
            displayName: '燃油表',
            valueType: 'DOUBLE',
            unit: '%',
            unitSymbol: '%',
            description: '当前燃油液位占油箱总容量的百分比 ',
            constraints: {
              enumValues: null,
              max: 100.0,
              min: 0.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 228,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_motion_speed',
            value: '113',
            valueUpdateDate: 1676529632000,
            createDate: 1676525304732,
            updateDate: 1676529633128,
            displayName: '速度',
            valueType: 'DOUBLE',
            unit: 'km/h',
            unitSymbol: 'km/h',
            description: '当前车速，由内部车辆系统感知',
            constraints: {
              enumValues: null,
              max: 400.0,
              min: -100.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 231,
            vehicleId: 'acVmVsZTAwM04',
            name: 'throttle_opening',
            value: '79',
            valueUpdateDate: 1676529632000,
            createDate: 1676525304733,
            updateDate: 1676529633129,
            displayName: 'vehicle_throttle_opening',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: 'vehicle_throttle_opening',
            constraints: null,
          },
          {
            id: 242,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_vehicleidentification_year',
            value: '2020',
            valueUpdateDate: 1676529632000,
            createDate: 1676525304737,
            updateDate: 1676529633131,
            displayName: '车辆型号年份',
            valueType: 'LONG',
            unit: null,
            unitSymbol: null,
            description: '车辆型号年份',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 234,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_odometer',
            value: '16006',
            valueUpdateDate: 1676529632000,
            createDate: 1676525304734,
            updateDate: 1676529633129,
            displayName: '里程表',
            valueType: 'DOUBLE',
            unit: 'km',
            unitSymbol: 'km',
            description: '里程表当前读数',
            constraints: {
              enumValues: null,
              max: null,
              min: 0.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 236,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_location_currentlocation_latitude',
            value: '23.127896',
            valueUpdateDate: 1676529632000,
            createDate: 1676525304735,
            updateDate: 1676529633129,
            displayName: '纬度',
            valueType: 'DOUBLE',
            unit: 'deg',
            unitSymbol: 'DEGREES',
            description: '车辆的纬度',
            constraints: {
              enumValues: null,
              max: 90.0,
              min: -90.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 237,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_vehicleidentification_model',
            value: 'BYD',
            valueUpdateDate: 1676529632000,
            createDate: 1676525304735,
            updateDate: 1676529633130,
            displayName: '车辆型号',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: '车辆的型号',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 456,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_battery_temperature',
            value: '33',
            valueUpdateDate: 1676529632000,
            createDate: 1660702009253,
            updateDate: 1676529633128,
            displayName: '电池温度',
            valueType: 'DOUBLE',
            unit: 'C',
            unitSymbol: '°C',
            description: '电池的估计温度',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 229,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_message_name',
            value: 'remote_start_error',
            valueUpdateDate: 1676529632000,
            createDate: 1676525304732,
            updateDate: 1676529633128,
            displayName: '消息名称',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: '消息名称',
            constraints: {
              enumValues: [
                'Driving Start',
                'Driving Stop',
                'Tester Present',
                'Unlock Doors',
                'Doors Unlocked',
                'Crash Detected',
                'System Status',
                'Reset',
                'Door Opened',
                'ECU Authentication Request',
                'DOOR_EVENT',
                'ALARM_EVENT',
                'MOTION_EVENT',
                'IGNITION_EVENT',
                'DIAGNOSTIC_EVENT',
                'GEAR_CHANGE_EVENT',
                'remote_start_error',
                'HARSH_BRAKING_EVENT',
                'TIRE_PRESSURE_EVENT',
                'HARSH_ACCELERATION_EVENT',
                'FIRMWARE_UPGRADE_TRIGGER',
                'FIRMWARE_UPGRADE_STATUS',
              ],
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 230,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_vehicleidentification_shortvin',
            value: 'N/A',
            valueUpdateDate: 1676529632000,
            createDate: 1676525304733,
            updateDate: 1676529633129,
            displayName: '短VIN',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: '短车辆识别',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 239,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_message_senttimestamp',
            value: '1676529632000',
            valueUpdateDate: 1676529632000,
            createDate: 1676525304736,
            updateDate: 1676529633130,
            displayName: '发送时间戳',
            valueType: 'TIMESTAMP',
            unit: null,
            unitSymbol: null,
            description: '消息源发送消息的时间戳',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 232,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_location_currentlocation_longitude',
            value: '113.900602',
            valueUpdateDate: 1676529632000,
            createDate: 1676525304733,
            updateDate: 1676529633129,
            displayName: '经度',
            valueType: 'DOUBLE',
            unit: 'deg',
            unitSymbol: 'DEGREES',
            description: '车辆的经度',
            constraints: {
              enumValues: null,
              max: 180.0,
              min: -180.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 233,
            vehicleId: 'acVmVsZTAwM04',
            name: 'body_domain_bus_load',
            value: '58',
            valueUpdateDate: 1676529632000,
            createDate: 1676525304734,
            updateDate: 1676529633129,
            displayName: '车身域总线负载',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: 'vehicle_body_domain_bus_load',
            constraints: null,
          },
          {
            id: 466,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_trip_journey_state',
            value: 'Driving Start',
            valueUpdateDate: 1676529632000,
            createDate: 1660717231793,
            updateDate: 1676529633130,
            displayName: '旅程当前状态',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: '旅程的当前状态',
            constraints: {
              enumValues: ['Drive Start', 'In Progress', 'Drive End'],
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 238,
            vehicleId: 'acVmVsZTAwM04',
            name: 'power_domain_bus_load',
            value: '44',
            valueUpdateDate: 1676529632000,
            createDate: 1676525304735,
            updateDate: 1676529633130,
            displayName: '动力域总线负载',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: 'vehicle_power_domain_bus_load',
            constraints: null,
          },
          {
            id: 241,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_vehicleidentification_id',
            value: 'acVmVsZTAwM04',
            valueUpdateDate: 1676529632000,
            createDate: 1676525304736,
            updateDate: 1676529633131,
            displayName: '车辆ID',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: '车辆的唯一标识符',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 243,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_engine_speed',
            value: '1653',
            valueUpdateDate: 1676529632000,
            createDate: 1676525304737,
            updateDate: 1676529633131,
            displayName: '引擎转速',
            valueType: 'LONG',
            unit: 'rpm',
            unitSymbol: 'rpm',
            description: '发动机转速，以每分钟旋转量计',
            constraints: {
              enumValues: null,
              max: 20000.0,
              min: 0.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 253,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_state',
            value: 'true',
            valueUpdateDate: 1676529632000,
            createDate: 1676528619880,
            updateDate: 1676529633131,
            displayName: '发动机状态',
            valueType: 'BOOLEAN',
            unit: null,
            unitSymbol: null,
            description: '当前发动机或电动机状态。',
            constraints: {
              enumValues: ['关闭', '开启'],
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 240,
            vehicleId: 'acVmVsZTAwM04',
            name: 'intelligent_driving_domain_bus_load',
            value: '69',
            valueUpdateDate: 1676529632000,
            createDate: 1676525304736,
            updateDate: 1676529633130,
            displayName: '智驾域总线负载',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: 'vehicle_intelligent_driving_domain_bus_load',
            constraints: null,
          },
        ],
        alarmData: {
          'currentMessage.vehicle_message_name': 'remote_start_error',
          'currentState.vehicle_location_currentlocation_latitude': 23.127896,
          'currentState.vehicle_location_currentlocation_longitude': 113.900602,
          'currentState.vehicle_message_senttimestamp': 1676529632000,
          'currentState.vehicle_powertrain_state': true,
          'currentState.vehicle_vehicleidentification_id': 'acVmVsZTAwM04',
        },
        specialFields: [
          {
            id: 229,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_message_name',
            value: 'remote_start_error',
            valueUpdateDate: 1676529632000,
            createDate: 1676525304732,
            updateDate: 1676529633128,
            displayName: '消息名称',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: '消息名称',
            constraints: {
              enumValues: [
                'Driving Start',
                'Driving Stop',
                'Tester Present',
                'Unlock Doors',
                'Doors Unlocked',
                'Crash Detected',
                'System Status',
                'Reset',
                'Door Opened',
                'ECU Authentication Request',
                'DOOR_EVENT',
                'ALARM_EVENT',
                'MOTION_EVENT',
                'IGNITION_EVENT',
                'DIAGNOSTIC_EVENT',
                'GEAR_CHANGE_EVENT',
                'remote_start_error',
                'HARSH_BRAKING_EVENT',
                'TIRE_PRESSURE_EVENT',
                'HARSH_ACCELERATION_EVENT',
                'FIRMWARE_UPGRADE_TRIGGER',
                'FIRMWARE_UPGRADE_STATUS',
              ],
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 253,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_state',
            value: 'true',
            valueUpdateDate: 1676529632000,
            createDate: 1676528619880,
            updateDate: 1676529633131,
            displayName: '发动机状态',
            valueType: 'BOOLEAN',
            unit: null,
            unitSymbol: null,
            description: '当前发动机或电动机状态。',
            constraints: {
              enumValues: ['关闭', '开启'],
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
        ],
        defaultFields: [
          {
            id: 228,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_motion_speed',
            value: '113',
            valueUpdateDate: 1676529632000,
            createDate: 1676525304732,
            updateDate: 1676529633128,
            displayName: '速度',
            valueType: 'DOUBLE',
            unit: 'km/h',
            unitSymbol: 'km/h',
            description: '当前车速，由内部车辆系统感知',
            constraints: {
              enumValues: null,
              max: 400.0,
              min: -100.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 243,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_engine_speed',
            value: '1653',
            valueUpdateDate: 1676529632000,
            createDate: 1676525304737,
            updateDate: 1676529633131,
            displayName: '引擎转速',
            valueType: 'LONG',
            unit: 'rpm',
            unitSymbol: 'rpm',
            description: '发动机转速，以每分钟旋转量计',
            constraints: {
              enumValues: null,
              max: 20000.0,
              min: 0.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 456,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_battery_temperature',
            value: '33',
            valueUpdateDate: 1676529632000,
            createDate: 1660702009253,
            updateDate: 1676529633128,
            displayName: '电池温度',
            valueType: 'DOUBLE',
            unit: 'C',
            unitSymbol: '°C',
            description: '电池的估计温度',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 474,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_energystorage_fuelsystem_fuellevel',
            value: '9',
            valueUpdateDate: 1676529632000,
            createDate: 1660724495084,
            updateDate: 1676529633127,
            displayName: '燃油表',
            valueType: 'DOUBLE',
            unit: '%',
            unitSymbol: '%',
            description: '当前燃油液位占油箱总容量的百分比 ',
            constraints: {
              enumValues: null,
              max: 100.0,
              min: 0.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
        ],
      },
      {
        id: 'RecentActivity',
        name: '最近活动',
        type: 'RecentActivity', // 最近活动
        date: 1676521768000,
        description: null,
        count: 1,
        firstDate: 1676521768000,
        lastDate: 1676521768000,
        statisticsData: null,
        fullDigitalTwin: [
          {
            id: 474,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_energystorage_fuelsystem_fuellevel',
            value: '82',
            valueUpdateDate: 1676521768000,
            createDate: 1660724495084,
            updateDate: 1676521768241,
            displayName: '燃油表',
            valueType: 'DOUBLE',
            unit: '%',
            unitSymbol: '%',
            description: '当前燃油液位占油箱总容量的百分比 ',
            constraints: {
              enumValues: null,
              max: 100.0,
              min: 0.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 228,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_motion_speed',
            value: '101',
            valueUpdateDate: 1676521768000,
            createDate: 1676525304732,
            updateDate: 1676521768242,
            displayName: '速度',
            valueType: 'DOUBLE',
            unit: 'km/h',
            unitSymbol: 'km/h',
            description: '当前车速，由内部车辆系统感知',
            constraints: {
              enumValues: null,
              max: 400.0,
              min: -100.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 231,
            vehicleId: 'acVmVsZTAwM04',
            name: 'throttle_opening',
            value: '9',
            valueUpdateDate: 1676521768000,
            createDate: 1676525304733,
            updateDate: 1676521768242,
            displayName: 'vehicle_throttle_opening',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: 'vehicle_throttle_opening',
            constraints: null,
          },
          {
            id: 242,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_vehicleidentification_year',
            value: '2020',
            valueUpdateDate: 1676521768000,
            createDate: 1676525304737,
            updateDate: 1676521768245,
            displayName: '车辆型号年份',
            valueType: 'LONG',
            unit: null,
            unitSymbol: null,
            description: '车辆型号年份',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 234,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_odometer',
            value: '16082',
            valueUpdateDate: 1676521768000,
            createDate: 1676525304734,
            updateDate: 1676521768243,
            displayName: '里程表',
            valueType: 'DOUBLE',
            unit: 'km',
            unitSymbol: 'km',
            description: '里程表当前读数',
            constraints: {
              enumValues: null,
              max: null,
              min: 0.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 236,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_location_currentlocation_latitude',
            value: '23.183599',
            valueUpdateDate: 1676521768000,
            createDate: 1676525304735,
            updateDate: 1676521768243,
            displayName: '纬度',
            valueType: 'DOUBLE',
            unit: 'deg',
            unitSymbol: 'DEGREES',
            description: '车辆的纬度',
            constraints: {
              enumValues: null,
              max: 90.0,
              min: -90.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 237,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_vehicleidentification_model',
            value: 'BYD',
            valueUpdateDate: 1676521768000,
            createDate: 1676525304735,
            updateDate: 1676521768243,
            displayName: '车辆型号',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: '车辆的型号',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 239,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_message_senttimestamp',
            value: '1676521768000',
            valueUpdateDate: 1676521768000,
            createDate: 1676525304736,
            updateDate: 1676521768245,
            displayName: '发送时间戳',
            valueType: 'TIMESTAMP',
            unit: null,
            unitSymbol: null,
            description: '消息源发送消息的时间戳',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 232,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_location_currentlocation_longitude',
            value: '113.884001',
            valueUpdateDate: 1676521768000,
            createDate: 1676525304733,
            updateDate: 1676521768242,
            displayName: '经度',
            valueType: 'DOUBLE',
            unit: 'deg',
            unitSymbol: 'DEGREES',
            description: '车辆的经度',
            constraints: {
              enumValues: null,
              max: 180.0,
              min: -180.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 233,
            vehicleId: 'acVmVsZTAwM04',
            name: 'body_domain_bus_load',
            value: '57',
            valueUpdateDate: 1676521768000,
            createDate: 1676525304734,
            updateDate: 1676521768243,
            displayName: '车身域总线负载',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: 'vehicle_body_domain_bus_load',
            constraints: null,
          },
          {
            id: 466,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_trip_journey_state',
            value: 'Driving Start',
            valueUpdateDate: 1676521768000,
            createDate: 1660717231793,
            updateDate: 1676521768243,
            displayName: '旅程当前状态',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: '旅程的当前状态',
            constraints: {
              enumValues: ['Drive Start', 'In Progress', 'Drive End'],
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 238,
            vehicleId: 'acVmVsZTAwM04',
            name: 'power_domain_bus_load',
            value: '44',
            valueUpdateDate: 1676521768000,
            createDate: 1676525304735,
            updateDate: 1676521768243,
            displayName: '动力域总线负载',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: 'vehicle_power_domain_bus_load',
            constraints: null,
          },
          {
            id: 241,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_vehicleidentification_id',
            value: 'acVmVsZTAwM04',
            valueUpdateDate: 1676521768000,
            createDate: 1676525304736,
            updateDate: 1676521768245,
            displayName: '车辆ID',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: '车辆的唯一标识符',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 243,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_engine_speed',
            value: '2061',
            valueUpdateDate: 1676521768000,
            createDate: 1676525304737,
            updateDate: 1676521768245,
            displayName: '引擎转速',
            valueType: 'LONG',
            unit: 'rpm',
            unitSymbol: 'rpm',
            description: '发动机转速，以每分钟旋转量计',
            constraints: {
              enumValues: null,
              max: 20000.0,
              min: 0.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 253,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_state',
            value: 'true',
            valueUpdateDate: 1676521768000,
            createDate: 1676528619880,
            updateDate: 1676521768245,
            displayName: '发动机状态',
            valueType: 'BOOLEAN',
            unit: null,
            unitSymbol: null,
            description: '当前发动机或电动机状态。',
            constraints: {
              enumValues: ['关闭', '开启'],
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 456,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_battery_temperature',
            value: '60',
            valueUpdateDate: 1676521768000,
            createDate: 1660702009253,
            updateDate: 1676521768242,
            displayName: '电池温度',
            valueType: 'DOUBLE',
            unit: 'C',
            unitSymbol: '°C',
            description: '电池的估计温度',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 229,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_message_name',
            value: 'remote_start_error',
            valueUpdateDate: 1676521768000,
            createDate: 1676525304732,
            updateDate: 1676521768242,
            displayName: '消息名称',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: '消息名称',
            constraints: {
              enumValues: [
                'Driving Start',
                'Driving Stop',
                'Tester Present',
                'Unlock Doors',
                'Doors Unlocked',
                'Crash Detected',
                'System Status',
                'Reset',
                'Door Opened',
                'ECU Authentication Request',
                'DOOR_EVENT',
                'ALARM_EVENT',
                'MOTION_EVENT',
                'IGNITION_EVENT',
                'DIAGNOSTIC_EVENT',
                'GEAR_CHANGE_EVENT',
                'remote_start_error',
                'HARSH_BRAKING_EVENT',
                'TIRE_PRESSURE_EVENT',
                'HARSH_ACCELERATION_EVENT',
                'FIRMWARE_UPGRADE_TRIGGER',
                'FIRMWARE_UPGRADE_STATUS',
              ],
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 230,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_vehicleidentification_shortvin',
            value: 'N/A',
            valueUpdateDate: 1676521768000,
            createDate: 1676525304733,
            updateDate: 1676521768242,
            displayName: '短VIN',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: '短车辆识别',
            constraints: {
              enumValues: null,
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 240,
            vehicleId: 'acVmVsZTAwM04',
            name: 'intelligent_driving_domain_bus_load',
            value: '73',
            valueUpdateDate: 1676521768000,
            createDate: 1676525304736,
            updateDate: 1676521768245,
            displayName: '智驾域总线负载',
            valueType: 'STRING',
            unit: null,
            unitSymbol: null,
            description: 'vehicle_intelligent_driving_domain_bus_load',
            constraints: null,
          },
        ],
        alarmData: null,
        specialFields: [
          {
            id: 253,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_state',
            value: 'true',
            valueUpdateDate: 1676521768000,
            createDate: 1676528619880,
            updateDate: 1676521768245,
            displayName: '发动机状态',
            valueType: 'BOOLEAN',
            unit: null,
            unitSymbol: null,
            description: '当前发动机或电动机状态。',
            constraints: {
              enumValues: ['关闭', '开启'],
              max: null,
              min: null,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
        ],
        defaultFields: [
          {
            id: 228,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_motion_speed',
            value: '101',
            valueUpdateDate: 1676521768000,
            createDate: 1676525304732,
            updateDate: 1676521768242,
            displayName: '速度',
            valueType: 'DOUBLE',
            unit: 'km/h',
            unitSymbol: 'km/h',
            description: '当前车速，由内部车辆系统感知',
            constraints: {
              enumValues: null,
              max: 400.0,
              min: -100.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 243,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_engine_speed',
            value: '2061',
            valueUpdateDate: 1676521768000,
            createDate: 1676525304737,
            updateDate: 1676521768245,
            displayName: '引擎转速',
            valueType: 'LONG',
            unit: 'rpm',
            unitSymbol: 'rpm',
            description: '发动机转速，以每分钟旋转量计',
            constraints: {
              enumValues: null,
              max: 20000.0,
              min: 0.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 234,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_odometer',
            value: '16082',
            valueUpdateDate: 1676521768000,
            createDate: 1676525304734,
            updateDate: 1676521768243,
            displayName: '里程表',
            valueType: 'DOUBLE',
            unit: 'km',
            unitSymbol: 'km',
            description: '里程表当前读数',
            constraints: {
              enumValues: null,
              max: null,
              min: 0.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
          {
            id: 474,
            vehicleId: 'acVmVsZTAwM04',
            name: 'vehicle_powertrain_energystorage_fuelsystem_fuellevel',
            value: '82',
            valueUpdateDate: 1676521768000,
            createDate: 1660724495084,
            updateDate: 1676521768241,
            displayName: '燃油表',
            valueType: 'DOUBLE',
            unit: '%',
            unitSymbol: '%',
            description: '当前燃油液位占油箱总容量的百分比 ',
            constraints: {
              enumValues: null,
              max: 100.0,
              min: 0.0,
              maxLength: null,
              __typename: 'Constraints',
            },
          },
        ],
      },
    ],
    alarmStatisticsInTheLastSixMonths: [
      { x: '2022-12', y: 609 },
      { x: '2022-11', y: 100 },
      {
        x: '2022-10',
        y: 50,
      },
      { x: '2022-09', y: 30 },
      { x: '2022-08', y: 20 },
      { x: '2022-07', y: 100 },
    ],
    vehicleAssetStatisticsInTheLastSixMonths: [
      { x: '2022-12', y: 16 },
      {
        x: '2022-11',
        y: 0,
      },
      { x: '2022-10', y: 0 },
      { x: '2022-09', y: 0 },
      { x: '2022-08', y: 0 },
      { x: '2022-07', y: 0 },
    ],
    alarmStatisticsOfTheLastDay: 48,
    vehicleAssetStatisticsOfTheLastDay: 2,
  }
}

// 获取全部告警标签
export function getAllAlertTagsDt() {
  return [
    { id: '1544875746406092801', name: 'testing' },
    {
      id: '1549300336701947906',
      name: 'test tag',
    },
    { id: '1549301748529840129', name: 'low' },
    {
      id: '1550006281442557954',
      name: 'test2 tag',
    },
    { id: '1550006663942111234', name: 'test3' },
    {
      id: '1550006825921937410',
      name: 'test4 tag',
    },
    { id: '1550008012461178882', name: 'test5' },
    {
      id: '1550008250399850497',
      name: 'test6 tag',
    },
    { id: '1551382960341696514', name: '12345' },
    {
      id: '1551383184212672514',
      name: 'sssssss',
    },
    { id: '1551383434369351681', name: 'test' },
    {
      id: '1553998174136537089',
      name: 'aaa',
    },
    { id: '1553998378264924161', name: '指令异常' },
    { id: '1554000799804395522', name: '12' },
  ]
}

// 新增告警标签
export function addAlertTagDt(name) {
  return request({
    url: `${cmdbPath}/alarm-tag/add`,
    method: 'post',
    data: {
      description: '',
      name,
    },
  })
}
