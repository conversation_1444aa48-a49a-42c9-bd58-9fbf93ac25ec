<template>
  <vsoc-drawer
    :title="$t('action.advanced')"
    :value="value"
    @click:confirm="doQuery"
    @input="close"
    @click:close="close"
    @click:cancel="close"
  >
    <template #right-title>
      <v-btn icon class="no-hb ml-4" text @click="clearAdvanceQuery">
        <v-icon size="16">mdi-filter-variant-remove</v-icon>
      </v-btn>
    </template>
    <!-- <div class="text-lg font-weight-semibold mb-4">登录时间</div> -->
    <vsoc-date-range
      v-model="dateRange"
      no-title
      :menu-props="menuProps"
      @input="onChangeDate"
    >
      <template v-slot:text="{ on, attrs }">
        <v-text-field
          clearable
          class="append-icon-max pt-4"
          readonly
          color="primary"
          :label="$t('loginLog.headers.accessDate')"
          append-icon="mdi-calendar-range-outline"
          :value="RANGE_STR(advanceQuery.startDate, advanceQuery.endDate)"
          v-bind="attrs"
          v-on="on"
          @click:clear="onChangeDate({ start: '', end: '' })"
        ></v-text-field>
      </template>
    </vsoc-date-range>

    <!-- <div class="text-lg font-weight-semibold my-4">用户名称</div> -->
    <v-text-field
      v-model="advanceQuery.userId"
      color="primary"
      :label="$t('loginLog.headers.name')"
      large
    ></v-text-field>

    <!-- <div class="text-lg font-weight-semibold my-4">登录地址</div> -->
    <v-text-field
      v-model="advanceQuery.ipAddress"
      color="primary"
      :label="$t('loginLog.headers.ip')"
    ></v-text-field>

    <!-- <div class="text-lg font-weight-semibold mb-4">状态</div> -->
    <!-- <v-radio-group
      v-model="advanceQuery.status"
      dense
      hide-details
      color="primary"
    >
      <v-radio
        v-for="item in statusEnum"
        :key="item.value"
        :value="item.value"
        class="mb-4"
      >
        <template #label>
          <div>
            <v-icon size="1.25rem" class="mr-2" :color="item.color">
              {{ item.icon }}
            </v-icon>
            <span class="text-sm">{{ item.text }}</span>
          </div>
        </template>
      </v-radio>
    </v-radio-group> -->
    <v-select
      v-model="advanceQuery.status"
      clearable
      color="primary"
      :items="statusEnum"
      :label="$t('loginLog.headers.status')"
      :multiple="false"
      id="value"
      text="text"
      :menu-props="{ offsetY: true }"
    >
    </v-select>
  </vsoc-drawer>
</template>

<script>
import { RANGE_STR } from '@/components/vsoc-date-range/constants'
import VsocDateRange from '@/components/vsoc-date-range/VsocDateRange.vue'
import VsocDrawer from '@/components/VsocDrawer.vue'

export default {
  name: 'AssetDrawer',
  components: {
    VsocDrawer,
    VsocDateRange,
  },
  props: {
    value: Boolean,
  },
  data() {
    return {
      menuProps: { offsetY: true, closeOnContentClick: false },
      advanceQuery: {
        startDate: null,
        endDate: null,
        userId: '',
        status: '',
        ipAddress: '',
      },
    }
  },
  computed: {
    statusEnum() {
      return Object.assign([], this.$store.getters['enums/getOperateStatus'])
    },

    dateRange: {
      get() {
        return {
          start: this.advanceQuery.startDate,
          end: this.advanceQuery.endDate,
        }
      },
      set() {
        return {
          start: this.advanceQuery.startDate,
          end: this.advanceQuery.endDate,
        }
      },
    },
  },

  methods: {
    setFilterList(query) {
      let filterList = []
      for (const [key, value] of Object.entries(query)) {
        if (value) {
          let text = ''
          let name = ''
          let type = 'String'
          switch (key) {
            case 'ipAddress':
              text = this.$t('loginLog.headers.ip')
              name = value
              break

            case 'status':
              text = this.$t('loginLog.headers.status')
              name = this.statusEnum.find(v => v.value === value)?.text
              break
          }
          filterList.push({
            label: text,
            text: name,
            value,
            key,
            type,
          })
        }
      }
      return filterList
    },
    RANGE_STR,

    // 首次登记时间改变
    onChangeDate(range) {
      this.advanceQuery.startDate = range.start
      this.advanceQuery.endDate = range.end
    },

    close(bool) {
      if (!bool) {
        this.$emit('input', false)
      }
    },

    clearAdvanceQuery() {
      Object.assign(
        this.$data.advanceQuery,
        this.$options.data.call(this).advanceQuery,
      )
    },

    //  高级查询
    doQuery(callback) {
      this.$emit('do-query', this.advanceQuery)
      callback()
    },
  },
}
</script>
