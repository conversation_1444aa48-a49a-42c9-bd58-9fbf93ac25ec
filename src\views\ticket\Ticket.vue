<template>
  <div class="ticket-box">
    <bread-crumb
      class="breamd-box"
      :showDoQuery="showDoQuery"
      :showAdvance="true"
    >
      <!--      <template>-->
      <!--        <div class="d-flex align-center justify-end" @click="showDoQuery">-->
      <!--          <v-btn icon>-->
      <!--            <v-icon size="16"> mdi-filter-variant </v-icon>-->
      <!--          </v-btn>-->
      <!--          <span>{{ $t('action.advanced') }}</span>-->
      <!--        </div>-->
      <!--      </template>-->
    </bread-crumb>
    <!-- class="ticket-fiter-box" -->
    <vsoc-filter
      :filterList="filterList"
      ref="filterList"
      mode="multiple"
      :isShowReset="
        (!query.showSelectFlag && filterList.length) ||
        (query.showSelectFlag && filterList.length > 1)
          ? true
          : false
      "
      @clear="$_clearFilter"
      @reset="onReset"
    >
      <template v-slot:text="{ item }">{{ dealText(item) }}</template>
    </vsoc-filter>
    <!-- <div
      ref="filterList"
      class="rounded-0 d-flex align-center justify-lg-space-between px-4"
      :style="{
        paddingBottom: filterList.length === 0 ? '0px' : '10px',
        paddingTop: filterList.length === 0 ? '0px' : '10px',
      }"
    >
      <v-chip-group v-if="filterList.length" class="w-90 pa-0 ma-0">
        <v-chip
          v-for="(item, index) in filterList"
          :key="index"
          small
          pill
          dense
          class="pa-0 active-chip rounded-pill color-base bg-white px-4 mx-0 my-0 mr-4"
          :close="item.close"
          close-icon="mdi-close"
          @click:close="$_clearFilter(item, index)"
        >
          {{ dealText(item) }}
        </v-chip>
      </v-chip-group>

      <v-btn
        v-if="
          (!query.showSelectFlag && filterList.length) ||
          (query.showSelectFlag && filterList.length > 1)
        "
        color="primary"
        x-small
        rounded
        class="px-4 py-1 rounded-pill text-base"
        @click="onReset"
      >
        <v-icon left> mdi-eraser </v-icon>
        {{ $t('action.clear') }}
      </v-btn>
    </div> -->

    <vue-tabs-chrome
      ref="refTab"
      v-model="changeValue"
      :tabs="changeList"
      :render-label="dealLabel"
      @click="changeReportTitle"
    />
    <v-card tile class="px-4 pt-4">
      <v-card-text class="pa-0">
        <div v-if="query.showSelectFlag" class="d-flex align-center">
          <div>
            <v-btn
              elevation="0"
              @click="allComfirm"
              color="primary"
              :disabled="selectedArray.length ? false : true"
            >
              <span>{{ $t(opItem.title) }}</span>
            </v-btn>
            <v-btn
              class="primary--text bg-btn ml-3"
              elevation="0"
              @click="reback"
            >
              <span>
                {{ $t('ticket.btn.reback') }}
              </span>
            </v-btn>
          </div>
          <div class="ml-6 tip-box text--secondary">
            {{ $t('ticket.hint.can', [$t(opItem.title)]) }}
            <span class="mx-1 note">{{ tableDataTotal }}</span
            >， {{ $t('global.pagination.selected')
            }}<span class="mx-1 note">{{ selectedArray.length }}</span>
          </div>
        </div>
        <div v-else class="d-flex justify-space-between align-center">
          <div class="d-flex align-center">
            <v-btn
              class="mr-3"
              elevation="0"
              color="primary"
              v-has:all-export
              @click="exportTicket"
            >
              {{ $t('action.export') }}
            </v-btn>
            <v-menu
              offset-y
              left
              nudge-bottom="4"
              transition="scale-transition"
              content-class="menu-list"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-btn
                  v-if="isAllShow"
                  class="d-flex align-center"
                  color="primary"
                  v-bind="attrs"
                  v-on="on"
                >
                  <span class="mr-3"> {{ $t('action.batchOps') }} </span>
                  <v-icon size="12px">mdi-chevron-down</v-icon>
                </v-btn>
              </template>
              <v-list>
                <v-list-item
                  v-for="(item, i) in menuItems"
                  :key="i"
                  class="list-hover"
                  v-has="item.code"
                  @click="onBatch(item)"
                >
                  <v-list-item-title>
                    {{ $t(item.title) }}
                  </v-list-item-title>
                </v-list-item>
              </v-list>
            </v-menu>
          </div>
          <div class="d-flex align-center">
            <table-search
              :searchList="searchList"
              :searchQuery="query"
              @search="$_search"
            ></table-search>
            <!-- <div class="d-flex align-end">
              <v-select
                outlined
                dense
                v-model="queryKey"
                small
                color="primary"
                class="me-3 select-width"
                :menu-props="{ offsetY: true }"
                append-icon="mdi-chevron-down"
                hide-details
                :items="searchConditions"
                :label="$t('action.queryConditions')"
                @change="changeCondition"
              ></v-select>
              <v-select
                v-if="queryKey === 'priorities'"
                v-model="query[queryKey]"
                multiple
                clearable
                small
                color="primary"
                :menu-props="{ offsetY: true }"
                append-icon="mdi-chevron-down"
                class="me-3 text-width"
                outlined
                dense
                hide-details
                :items="levelList"
                :label="$t('action.queryContent')"
                @change="$_search"
              >
                <template v-slot:selection="{ index }">
                  <span v-if="index === 0">
                    {{ $t('global.pagination.selected') }}：{{
                      query[queryKey].length
                    }}
                  </span>
                </template>
              </v-select>
              <v-text-field
                v-else
                v-model="query[queryKey]"
                class="me-3 text-width"
                color="primary"
                outlined
                dense
                :label="$t('action.queryContent')"
                hide-details
                clearable
                @click:clear="onClear"
                @keyup.enter.native="$_search"
              ></v-text-field>
            </div>
            <vsoc-date-range
              v-model="dateRange.range"
              no-title
              :menu-props="dateRange.menuProps"
              @input="onChangeDate"
              @search="$_search"
            >
              <template v-slot:text="{ on, attrs }">
                <v-text-field
                  type="button"
                  clearable
                  outlined
                  dense
                  class="append-icon-max me-3 date-width"
                  readonly
                  hide-details
                  color="primary"
                  large
                  :label="$t('ticket.headers.createDate')"
                  prepend-inner-icon="mdi-calendar-range-outline"
                  :value="RANGE_STR(query.startDate, query.endDate)"
                  @click:clear="onChangeDate({ start: '', end: '' })"
                ></v-text-field>
              </template>
            </vsoc-date-range>
            <v-btn
              class="primary--text bg-btn me-3"
              elevation="0"
              @click="$_search"
            >
              <span>
                {{ $t('action.search') }}
              </span>
            </v-btn> -->
            <v-btn
              elevation="0"
              width="34"
              max-width="34"
              min-width="34"
              class="ml-3 primary--text bg-btn"
              @click="setHeader"
            >
              <vsoc-icon
                v-show-tips="$t('action.setHeader')"
                type="fill"
                icon="icon-shezhibiaotou"
                size="x-large"
              ></vsoc-icon>
            </v-btn>
          </div>
        </div>

        <v-data-table
          ref="ticketList"
          fixed-header
          class="table border-radius-xl mt-3 thead-light"
          :height="tableHeight"
          :items-per-page="query.pageSize"
          :show-select="query.showSelectFlag"
          checkbox-color="primary"
          hide-default-footer
          :headers="headers"
          item-key="id"
          :items="tableData"
          :loading="tableLoading"
          @item-selected="$_tableSelected"
          @toggle-select-all="$_tableSelected"
          @click:row="onEdit"
        >
          <!-- <template
            v-for="item in headers"
            v-slot:[`header.${item.value}`]="{ header }"
          >
            <div :key="item.value">{{ $t(header.text) }}</div>
          </template> -->
          <template v-slot:item.actions1="{ item }">
            <v-btn icon @click.stop="onInfo(item)">
              <vsoc-icon
                v-show-tips="$t('action.detail')"
                type="fill"
                icon="icon-xiangqing2"
                class="action-btn"
                size="x-large"
              ></vsoc-icon>
            </v-btn>
          </template>
          <template v-slot:item.priority="{ item }">
            <div
              v-if="item.priority && ticketLevel[Number(item.priority)]"
              class="d-flex align-center"
            >
              <v-icon
                :color="ticketLevel[Number(item.priority)].color"
                size="1.1667rem"
                left
              >
                mdi-alert-circle
              </v-icon>
              <span>{{ ticketLevel[Number(item.priority)].text }}</span>
            </div>
            <span v-else>N/A</span>
          </template>
          <template v-slot:item.title="{ item }">
            <div v-show-tips style="width: 320px">
              {{ item.title }}
            </div>
          </template>
          <template v-slot:item.affectModelNameList="{ item }">
            <div
              v-if="item.affectModelNameList && item.affectModelNameList.length"
              class="d-flex"
            >
              <v-chip
                v-for="v in item.affectModelNameList"
                :key="v"
                small
                color="primary"
                class="badge-font-size text-xxs ml-0 mr-2"
              >
                {{ v }}
              </v-chip>
            </div>
            <span v-else>N/A</span>
          </template>
          <template v-slot:item.status="{ item }">
            <div
              v-if="item.status && ticketStatus[Number(item.status)]"
              class="d-flex align-center"
            >
              <!-- <v-icon
                :color="ticketStatus[Number(item.status)].color"
                size="1.1667rem"
                left
              >
                mdi-circle-medium
              </v-icon> -->
              <v-badge
                dot
                inline
                offset-x="10"
                :offset-y="-18"
                :color="ticketStatus[Number(item.status)].color"
                class="mr-1"
              ></v-badge>
              <span>{{ ticketStatus[Number(item.status)].text }}</span>
            </div>
            <span v-else>N/A</span>
          </template>
          <template v-slot:item.classifyName="{ item }">
            <div style="width: 140px" v-show-tips>
              {{ item.classifyName | dataFilter }}
            </div>
          </template>
          <template v-slot:item.impactName="{ item }">
            <div>
              {{ item.impactName | dataFilter }}
            </div>
          </template>
          <template v-slot:item.eta="{ item }">
            <div>
              {{ item.eta | dataFilter }}
            </div>
          </template>
          <!-- 剩余时间 -->
          <template v-slot:item.timeLeft="{ item }">
            <!-- item.timeLeft | dataFilter -->
            <div>
              {{ item.timeLeft ? formatSeconds1(item.timeLeft) : 'N/A' }}
            </div>
          </template>
          <!-- 已用时间 -->
          <template v-slot:item.ticketElapsedTime="{ item }">
            <div>
              {{ formatSeconds1(item.ticketElapsedTime) }}
            </div>
          </template>
          <template v-slot:item.elapsedPercentage="{ item }">
            <div v-if="item.totalTime" class="d-flex align-center">
              <v-progress-linear
                style="width: 130px"
                :value="
                  getElapsedPercentageV3(item.totalTime, item.ticketElapsedTime)
                "
                :color="
                  progressColorFilter(
                    getElapsedPercentageV3(
                      item.totalTime,
                      item.ticketElapsedTime,
                    ),
                  )
                "
              ></v-progress-linear>
              <div class="ml-2">{{ item.elapsedPercentage + '%' }}</div>
            </div>
            <div v-else>N/A</div>
          </template>
          <template v-slot:item.assignedToName="{ item }">
            <div>
              {{ item.assignedToName | dataFilter }}
            </div>
          </template>
          <template v-slot:item.assignedGroupName="{ item }">
            <div>
              {{ item.assignedGroupName | dataFilter }}
            </div>
          </template>
          <template v-slot:item.dataSourceName="{ item }">
            <div>
              {{ item.dataSourceName | dataFilter }}
            </div>
          </template>
          <template v-slot:item.relationId="{ item }">
            <div>
              {{ item.relationId | dataFilter }}
            </div>
          </template>
        </v-data-table>
        <!-- 分页器 -->
        <vsoc-pagination
          :page.sync="query.pageNum"
          :size.sync="query.pageSize"
          :total="tableDataTotal"
          @change-page="$_getTableData"
          @change-size="$_getTableData"
        >
          <!-- <template #prev>
            <span class="pr-0">
              已选择<span
                :class="
                  selectedArray.length > 0
                    ? 'text-primary font-weight-semibold'
                    : 'text-typo'
                "
                class="px-1 font-weight-semibold text-base"
                >{{ selectedArray.length }}</span
              >条
            </span>
          </template> -->
        </vsoc-pagination>
      </v-card-text>

      <ticket-drawer
        ref="advancedDrawer"
        v-model="showAdvanceSearch"
        :user-list="userList"
        :group-list="userGroupList"
        :ticket-list="ticketList"
        :level-list="levelList"
        :data-list="dataList"
        :model-list="modelList"
        :ticket-classify="ticketClassify"
        @do-query="doQuery"
      ></ticket-drawer>
      <batch-edit
        ref="batchEdit"
        :group-list="userGroupList"
        :user-list="userList"
      ></batch-edit>

      <vsoc-dialog
        v-model="showUpload"
        dense
        width="480"
        :title="$t('action.export')"
        @click:confirm="$_confirmEdit"
      >
        <v-form ref="form" v-model="valid" class="pa-4">
          <vsoc-date-range
            ref="refRange"
            v-model="dateRange1.range"
            no-title
            :menu-props="dateRange1.menuProps"
            @input="onChangeDate1"
            class="date-input"
          >
            <template v-slot:text="{ on, attrs }">
              <v-text-field
                clearable
                dense
                class="me-3 pa-0"
                readonly
                color="primary"
                large
                :label="$t('ticket.hint.time')"
                append-icon="mdi-calendar-range-outline"
                v-bind="attrs"
                v-on="on"
                :value="RANGE_STR(exportQuery.startDate, exportQuery.endDate)"
                @click:clear="onChangeDate1({ start: '', end: '' })"
                :rules="[
                  v =>
                    !!v ||
                    $t('validation.required', [$t('ticket.headers.time')]),
                ]"
              ></v-text-field>
            </template>
          </vsoc-date-range>
        </v-form>
      </vsoc-dialog>
    </v-card>
    <!-- 表头设置 -->
    <set-drawer ref="refSet"></set-drawer>
    <batch-import ref="batchImport" @refresh="$_search"></batch-import>
  </div>
</template>

<script>
import { getAllClassify } from '@/api/classify/index'
import { RANGE_STR } from '@/components/vsoc-date-range/constants'

import {
  exportBatch,
  getTicketList,
  updateStatusBatch,
} from '@/api/ticket/index'
import VsocPagination from '@/components/VsocPagination.vue'

import VsocDateRange from '@/components/vsoc-date-range/VsocDateRange.vue'

import { querySysGroupList } from '@/api/system/organization'
import { findAllUsers } from '@/api/system/user'
import VsocDialog from '@/components/VsocDialog.vue'
import VsocFilter from '@/components/VsocFilter.vue'
import breadCrumb from '@/components/bread-crumb/index'
import setDrawer from '@/components/header-set/index.vue'
import {
  differenceInMonths,
  endOfDay,
  format,
  startOfDay,
  subDays,
} from 'date-fns'
import VueTabsChrome from 'vue-tabs-chrome'
import BatchEdit from './BatchEdit.vue'
import TicketDrawer from './TicketDrawer.vue'

import TableSearch from '@/components/TableSearch/index.vue'
import { getLocalStorage } from '@/util/localStorage'
import {
  clearFilterItem,
  deepClone,
  formatSeconds1,
  getElapsedPercentageV2,
  getElapsedPercentageV3,
  handleFilterItem,
  handleQueryParams,
  progressColorFilter,
  setRemainingHeight,
  updateURL,
} from '@/util/utils'
import BatchImport from './BatchImport.vue'
export default {
  name: 'TicketIndex',
  components: {
    VsocPagination,
    VsocDateRange,
    TicketDrawer,
    breadCrumb,
    VueTabsChrome,
    BatchEdit,
    VsocDialog,
    BatchImport,
    VsocFilter,
    setDrawer,
    TableSearch,
  },
  data() {
    return {
      queryKey: 'ticketId',
      opItem: {
        title: '',
      },
      isAllShow: true,
      menuItems: [
        {
          type: '1',
          title: 'ticket.btn.assign',
          isParent: true,
          statuses: ['0', '1'], //待处理/处理中
          code: 'all-assign',
        },
        {
          type: '2',
          title: 'ticket.btn.resolve',
          isParent: true,
          statuses: ['1'], //处理中
          code: 'all-resolve',
        },
        {
          type: '4',
          title: 'ticket.btn.cancel',
          isParent: true,
          statuses: ['0', '3'], //待处理/已驳回
          code: 'all-cancel',
        },
        {
          type: '5',
          title: 'ticket.btn.import',
          isParent: true,
          statuses: [], //导入
          code: 'all-import',
        },
      ],
      dateRange: {
        range: {},
        menuProps: { offsetY: true, closeOnContentClick: false },
      },
      dateRange1: {
        range: {},
        menuProps: { offsetY: true, closeOnContentClick: false },
      },
      changeValue: 'open0',

      // 展示高级查询
      showAdvanceSearch: false,
      advanceQuery: {},

      // 查询条件下拉选择
      query: {
        ticketId: '', // 工单编号
        title: '',
        startDate: '', // 起始时间
        endDate: '', // 结束时间
        classifies: [],
        affectModel: '',
        priorities: [],
        statuses: [], //0-待处理(Open)、1-处理中(Handling)、2-已处理(Resovled)、3-已驳回(Rejected)、4-已取消(Cancelled)、5-已关闭(Closed)
        createUsers: [],
        assignedGroups: [],
        assignedTos: [],
        dataSourceList: [],
        pageShowType: '0', // pageShowType  0-待认领 1-我的代办 2-我的申请单  3-全部工单
        pageNum: 1, // 当前页码
        pageSize: 10,
        showSelectFlag: false,
        etaStartDate: '',
        etaEndDate: '',
        watchList: '',
        // allowLookAll: false,
      },

      filterList: [],

      tableData: [],
      selectedArray: [],
      tableHeight: '34.5rem',
      tableDataTotal: 0,
      tableLoading: false,
      userList: [],
      userGroupList: [],
      userGroupListMap: {},
      ticketClassify: [],
      ticketClassifyMap: {},
      userListMap: {},
      affectModelMap: {},
      modelList: [],
      valid: true,
      showUpload: false,
      exportQuery: {
        startDate: '',
        endDate: '',
      },
      changeList: [
        {
          label: 'ticket.changeHeaders.claimed',
          key: 'open0',
          value: '0',
          closable: false,
          dragable: false,
        },
        {
          label: 'ticket.changeHeaders.list',
          key: 'open1',
          value: '1',
          closable: false,
          dragable: false,
        },
        {
          label: 'ticket.changeHeaders.follow',
          key: 'open2',
          value: '4',
          closable: false,
          dragable: false,
        },
        {
          label: 'ticket.changeHeaders.Application',
          key: 'open3',
          value: '2',
          closable: false,
          dragable: false,
        },
        {
          label: 'ticket.changeHeaders.tickets',
          key: 'open4',
          value: '3',
          closable: false,
          dragable: false,
        },
      ],
      oldHeaders: [
        {
          text1: 'ticket.headers.models',
          value: 'affectModelNameList',
          width: '140px',
          sortable: true,
        },
        {
          text1: 'ticket.headers.classifyName',
          value: 'classifyName',
          width: '140px',
          sortable: true,
        },
        {
          text1: 'ticket.headers.priority',
          value: 'priority',
          width: '130px',
          sortable: true,
        },
        {
          text1: 'ticket.headers.ticketId',
          value: 'ticketId',
          width: '140px',
          sortable: true,
        },
        {
          text1: 'ticket.headers.title',
          value: 'title',
          width: '320px',
          sortable: true,
        },

        {
          text1: 'ticket.headers.createUser',
          value: 'createUser',
          width: '160px',
          sortable: true,
        },
        {
          text1: 'ticket.headers.impact',
          value: 'impactName',
          width: '120px',
          sortable: true,
        },

        {
          text1: 'ticket.headers.status',
          value: 'status',
          width: '120px',
          sortable: true,
        },

        {
          text1: 'ticket.headers.assignedGroupName',
          value: 'assignedGroupName',
          width: '180px',
          sortable: true,
        },
        {
          text1: 'ticket.headers.assignedToName',
          value: 'assignedToName',
          width: '180px',
          sortable: true,
        },

        {
          text1: 'ticket.headers.createDate',
          value: 'createDate',
          width: '180px',
          sortable: true,
        },
        {
          text1: 'ticket.headers.eta',
          value: 'eta',
          width: '220px',
          sortable: true,
        },
        {
          text1: 'ticket.headers.TimeLeft',
          value: 'timeLeft',
          width: '240px',
          sortable: true,
        },
        {
          text1: 'ticket.headers.ElapsedTime',
          value: 'ticketElapsedTime',
          width: '240px',
          sortable: true,
        },
        {
          text1: 'ticket.headers.ElapsedPercentage',
          value: 'elapsedPercentage',
          width: '160px',
          sortable: true,
        },
        {
          text1: 'ticket.headers.dataSourceName',
          value: 'dataSourceName',
          width: '120px',
          sortable: true,
        },
        {
          text1: 'ticket.headers.relationId',
          value: 'relationId',
          width: '140px',
          sortable: true,
        },
      ],
    }
  },
  computed: {
    searchList() {
      return [
        {
          type: 'multiSearch',
          value: 'ticketId',
          conditions: [
            {
              type: 'input',
              value: 'ticketId',
              text: this.$t('ticket.headers.ticketId'),
            },
            {
              type: 'input',
              value: 'title',
              text: this.$t('ticket.headers.title'),
            },
            {
              type: 'autocomplete',
              value: 'priorities',
              text: this.$t('ticket.headers.priority'),
              itemList: this.$store.getters['enums/getTicketLevel'],
            },
          ],
        },
        {
          type: 'date',
          value: ['startDate', 'endDate'],
          text: this.$t('ticket.headers.createDate'),
          dateRange: {
            range: {
              start: '',
              end: '',
            },
            menuProps: { offsetY: true, closeOnContentClick: false },
          },
        },
      ]
    },
    headers: {
      get() {
        return this.oldHeaders.map(v => {
          return {
            ...v,
            text: this.$t(v.text1),
          }
        })
      },
      set(newVal) {
        this.oldHeaders = newVal
        return this.oldHeaders
      },
    },
    // 查询条件列表
    searchConditions() {
      return [
        {
          text: this.$t('ticket.headers.ticketId'),
          value: 'ticketId',
        },
        {
          text: this.$t('ticket.headers.title'),
          value: 'title',
        },
        {
          text: this.$t('ticket.headers.priority'),
          value: 'priorities',
        },
      ]
    },
    ticketLevel() {
      return this.$store.state.enums.enums.TicketLevel
    },
    ticketStatus() {
      return this.$store.state.enums.enums.TicketStatus
    },
    ticketList() {
      return this.$store.getters['enums/getTicketStatus']
    },
    levelList() {
      return this.$store.getters['enums/getTicketLevel']
    },
    dataList() {
      return this.$store.getters['enums/getTicketDataSource']
    },
    dataListMap() {
      return this.$store.state.enums.enums.TicketSource
    },
  },
  watch: {
    filterList() {
      this.$_setTableHeight()
    },
  },
  created() {
    if (this.$route.meta.buttonInfo['ticket-detail']) {
      this.headers.unshift({
        text1: '',
        value: 'actions1',
        sortable: false,
      })
    }
    const jsonList = getLocalStorage(this.$route.path + '_headerList')
    const list = JSON.parse(jsonList)
    if (list && list.length > 0) {
      this.headers = list
    }
    if (
      this.menuItems.filter(
        v => this.$route.meta.buttons.indexOf(v.code) !== -1,
      ).length
    ) {
      this.isAllShow = true
    } else {
      this.isAllShow = false
    }
    this.init()
    // if (JSON.stringify(this.$route.query) !== '{}') {
    //   Object.keys(this.$route.query).forEach(key => {
    //     this.query[key] = JSON.parse(this.$route.query[key])
    //   })
    // }
  },
  beforeDestroy() {
    this.$bus.$off('resize', this.$_setTableHeight)
  },
  methods: {
    init() {
      //查询route是否有参数
      if (
        JSON.stringify(this.$route.query) !== '{}' &&
        this.$route.query.isQuery
      ) {
        const keyList = Object.keys(this.$route.query).filter(
          v => v !== 'isQuery',
        )
        keyList.forEach(key => {
          if (Array.isArray(this.query[key])) {
            this.query[key] = JSON.parse(this.$route.query[key])
          } else {
            this.query[key] = this.$route.query[key]
          }
        })
        this.initSearch()
      } else {
        const query = handleQueryParams({ type: 'get', key: 'ticket' })
        if (query) {
          this.query = query
        }
        let isNotNull = Object.keys(this.query).find(
          key =>
            (Array.isArray(this.query[key]) && this.query[key].length) ||
            (typeof this.query[key] === 'string' && this.query[key]),
        )
        //查询是否都为空
        if (isNotNull && isNotNull !== 'pageShowType') {
          this.changeValue =
            this.changeList.find(v => v.value === this.query['pageShowType'])
              ?.key || 'open0'
          this.initSearch('pageNum')
        } else {
          this.initQuery(() => {
            this.initSearch()
          })
        }
      }
    },
    initQuery(callBack) {
      this.changeValue = 'open0'
      this.searchList[1].dateRange.range.start = format(
        startOfDay(subDays(new Date(), 30)),
        'yyyy-MM-dd HH:mm:ss',
      )
      this.searchList[1].dateRange.range.end = format(
        endOfDay(new Date()),
        'yyyy-MM-dd HH:mm:ss',
      )
      this.query.startDate = format(
        startOfDay(subDays(new Date(), 30)),
        'yyyy-MM-dd HH:mm:ss',
      )
      this.query.endDate = format(endOfDay(new Date()), 'yyyy-MM-dd HH:mm:ss')
      callBack && callBack()
    },
    initSearch(type) {
      Promise.all([
        this.fetchAllClassify(),
        this.getAllUser(),
        this.getUserGroup(),
        this.getModels(),
      ]).then(e => {
        this.$_search(type)
      })
      this.$_setTableHeight()
      this.$bus.$on('resize', this.$_setTableHeight)
    },
    formatSeconds1,
    getElapsedPercentageV3,
    progressColorFilter,
    onClear() {
      this.query[this.queryKey] = ''
      this.$_search()
    },
    setHeader() {
      const headers = deepClone(this.headers)
      this.$refs['refSet'].open(headers, res => {
        this.headers = res
      })
    },
    changeCondition(item) {
      this.searchConditions.forEach(v => {
        if (v.value !== item) {
          this.query[v.value] = v.value === 'priorities' ? [] : ''
        }
      })
    },
    // 处理时间
    handleTime() {
      const defalutTime = [subDays(new Date(), 30), new Date()]
      const start = format(defalutTime[0], 'yyyy-MM-dd')
      const end = format(defalutTime[1], 'yyyy-MM-dd')
      const range = {
        start: [start, '00:00:00'].join(' ').trim(),
        end: [end, '23:59:59'].join(' ').trim(),
      }
      this.onChangeDate(range)
    },
    dealLabel(item, index) {
      return this.$t(item.label)
    },
    dealText(item) {
      const text =
        item.type === 'Array' && item.value.length === 1
          ? this[item.mapKey][item.value].text
          : item.text
      return this.$t(item.label) + '：' + text
    },
    onRefresh(newHeaderList) {
      this.headers = newHeaderList
    },
    //退出
    reback() {
      this.query.statuses = []
      this.query.showSelectFlag = false
      this.$_search()
    },
    //确认
    allComfirm() {
      const params = {
        item: this.opItem,
        num: this.selectedArray.length,
      }
      this.$refs.batchEdit.open(params, res => {
        const params = Object.assign(res, {
          ticketIdList: this.selectedArray.map(v => v.ticketId),
        })
        updateStatusBatch(params)
          .then(r => {
            if (r.code === 200) {
              this.$notify.info(
                'success',
                this.$t(this.opItem.title) +
                  ' ' +
                  this.$t('ticket.hint.suceess'),
              )
              this.reback()
            }
          })
          .catch(() => {})
      })
    },
    //导出
    exportTicket() {
      this.showUpload = true
      this.onChangeDate1({ start: '', end: '' })
      this.$nextTick(() => {
        this.$refs['refRange'].isPresetActive = -1
        this.$refs.form.resetValidation()
      })
    },
    // 导出时间改变
    onChangeDate1(range) {
      const month = differenceInMonths(
        new Date(range.end),
        new Date(range.start),
      )
      if (month > 3) {
        this.$notify.info('error', this.$t('ticket.hint.timeTip1'))
        return
      }
      this.dateRange1.range = range
      this.exportQuery.startDate = range.start
      this.exportQuery.endDate = range.end
    },
    //全部导出
    $_confirmEdit(callBack) {
      if (!this.$refs.form.validate()) return callBack(false, true)

      const params = {
        startDate: this.exportQuery.startDate,
        endDate: this.exportQuery.endDate,
        pageShowType: this.query.pageShowType,
      }
      exportBatch(params).then(res => {
        let url = window.URL.createObjectURL(new Blob([res]))
        let link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute(
          'download',
          params.startDate + '~' + params.endDate + '.xls',
        )
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
        callBack()
      })
    },

    onBatch(item) {
      if (item.type === '5') {
        this.$refs.batchImport.isShow = true
      } else {
        this.opItem = item
        this.query.showSelectFlag = true
        this.query.statuses = item.statuses
        // this.query.pageNum = 1
        // this.$_getTableData()
        this.$_search()
      }
    },

    // 表格选择事件
    $_tableSelected({ item, value, items }) {
      if (items) {
        this.selectedArray = value ? deepClone(items) : []
      } else if (value) {
        this.selectedArray.push(item)
      } else {
        const index = this.selectedArray.findIndex(v => v.id === item.id)
        this.selectedArray.splice(index, 1)
      }
    },
    changeReportTitle(e, item) {
      this.query.showSelectFlag = false
      // this.query.statuses = []
      this.query.pageShowType = item.value
      this.$_search()
    },
    // 清除表格当前选择项
    $_clearTableSelected() {
      this.$refs.ticketList && this.$refs.ticketList.toggleSelectAll()
      this.selectedArray = []
    },
    //获取车型
    async getModels() {
      const data = await this.$store.dispatch('global/loadAllAutomaker', {
        is_supervise: '0',
      })
      this.modelList = data || []
      this.affectModelMap = this.dealMap(data, {
        value: 'value',
        text: 'text',
      })
    },
    //获取用户
    async getAllUser() {
      const res = await findAllUsers({ status: '1' })
      this.userList = res.data.map(v => {
        return {
          ...v,
          value: v.userId,
          text: v.userName,
        }
      })

      this.userListMap = this.dealMap(this.userList, {
        value: 'value',
        text: 'text',
      })
    },
    dealMap(arr, params) {
      const obj = {}
      arr.forEach(item => {
        obj[item[params.value]] = {
          text: item[params.text],
        }
      })
      return obj
    },
    //获取组织架构
    async getUserGroup() {
      const res = await querySysGroupList({ state: '0' })
      this.userGroupList = res.data || []

      const flatList = this.flat(this.userGroupList) || []
      this.userGroupListMap = this.dealMap(flatList, {
        value: 'id',
        text: 'name',
      })
    },

    flat(arr) {
      let newArr = []
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].children) {
          newArr.push(...this.flat(arr[i].children))
        }
        newArr.push({ ...arr[i] })
      }
      return newArr
    },
    //分类
    async fetchAllClassify() {
      try {
        const res = await getAllClassify({ type: '1' })
        this.ticketClassify = res.data || []
        this.ticketClassifyMap = this.dealMap(this.ticketClassify, {
          value: 'id',
          text: 'name',
        })
      } catch (error) {
        console.error(`获取全部分类错误：${error}`)
      }
    },
    //获取数据来源
    // async getTicketDataSource() {
    //   const res = await ticketDataSource()
    //   this.dataList = res.data || []
    //   this.dataListMap = this.dealMap(this.dataList, {
    //     value: 'dictId',
    //     text: 'dictName',
    //   })
    // },
    RANGE_STR,

    // 首次登记时间改变
    onChangeDate(range) {
      this.dateRange.range = range
      this.query.startDate = range.start
      this.query.endDate = range.end
      this.$_search()
    },

    // 查询按钮
    $_search(type) {
      if (type !== 'pageNum') {
        this.query.pageNum = 1
      }
      if (JSON.stringify(this.$route.query) !== '{}') {
        updateURL.call(this)
      }
      this.$_getTableData()
      this.$_appendFilterListItem()
    },
    // 添加查询条件
    $_appendFilterListItem() {
      const searchKeyList = [
        {
          key: 'priorities',
          type: 'Array',
          mapKey: 'ticketLevel',
          label: 'ticket.headers.priority',
          close: true,
        },
        {
          key: 'statuses',
          type: 'Array',
          mapKey: 'ticketStatus',
          label: 'ticket.headers.status',
          close: !this.query.showSelectFlag,
        },
        {
          key: 'classifies',
          type: 'Array',
          mapKey: 'ticketClassifyMap',
          label: 'ticket.headers.classifyName',
          close: true,
        },
        {
          key: 'affectModel',
          type: 'String',
          mapKey: 'affectModelMap',
          label: 'ticket.headers.models',
          close: true,
        },
        {
          key: 'createUsers',
          type: 'Array',
          mapKey: 'userListMap',
          label: 'ticket.headers.createUser',
          close: true,
        },
        {
          key: 'watchList',
          type: 'String',
          mapKey: 'userListMap',
          label: 'ticket.headers.WatchList',
          close: true,
        },
        {
          key: 'assignedGroups',
          type: 'Array',
          mapKey: 'userGroupListMap',
          label: 'ticket.headers.assignedGroupName',
          close: true,
        },
        {
          key: 'assignedTos',
          type: 'Array',
          mapKey: 'userListMap',
          label: 'ticket.headers.assignedToName',
          close: true,
        },
        {
          key: 'dataSourceList',
          type: 'Array',
          mapKey: 'dataListMap',
          label: 'ticket.headers.dataSourceName',
          close: true,
        },
        {
          key: 'dateTime',
          type: 'time',
          mapKey: ['etaStartDate', 'etaEndDate'],
          label: 'ticket.headers.eta',
          close: true,
        },
      ]
      this.$_setTableHeight()
      handleFilterItem.call(this, searchKeyList)
      //关注人
      const wactchlist = this.filterList.find(v => v.key === 'watchList')
      if (wactchlist) {
        wactchlist.text = this.userListMap[wactchlist.value].text
      }
      //车型
      const affectModel = this.filterList.find(v => v.key === 'affectModel')
      if (affectModel) {
        affectModel.text = this.affectModelMap[affectModel.value].text
      }
    },
    // 获取后台数据 TODO
    async $_getTableData() {
      try {
        // 保存查询参数
        this.tableLoading = true
        handleQueryParams({ type: 'set', query: this.query, key: 'ticket' })

        const params = deepClone(this.query)

        const res = await getTicketList(params)
        this.tableData = res.data.records || []
        this.tableDataTotal = res.data.total

        this.tableData.forEach(v => {
          //已用时间elapsedTime ticketElapsedTime
          //剩余时间timeLeft:预期完成时间 - 当前时间
          //预期完成时间eta
          if (v.resolveTime) {
            // v.ticketElapsedTime =
            //   (new Date(v.resolveTime) - new Date(v.createDate)) / 1000
            v.timeLeft = '0'
          } else {
            // v.ticketElapsedTime = (new Date() - new Date(v.createDate)) / 1000
            v.timeLeft = v.eta ? (new Date(v.eta) - new Date()) / 1000 : ''
          }

          //所用时间：预期完成时间-创建时间
          //已用百分比 = 工单已用时间/（预期完成时间-工单创建时间） *100%
          v.totalTime = v.eta
            ? (new Date(v.eta) - new Date(v.createDate)) / 1000
            : ''

          v.elapsedPercentage = getElapsedPercentageV2(
            v.totalTime,
            v.ticketElapsedTime,
          )
        })
        this.$_clearTableSelected()
      } catch (error) {
        console.error(`获取工单列表信息：${error}`)
      } finally {
        this.tableLoading = false
      }
    },
    //  高级查询
    doQuery(advanceQuery) {
      this.query = deepClone(advanceQuery)
      this.$_search()
    },
    // 展示高级查询
    showDoQuery() {
      this.showAdvanceSearch = true
      const data = deepClone(this.query)
      this.$nextTick(() => {
        this.$refs.advancedDrawer.setModel(data)
      })
    },
    onReset() {
      this.filterList.forEach(v => {
        if (v.close) {
          if (v.type === 'time') {
            this.query[v.mapKey[0]] = ''
            this.query[v.mapKey[1]] = ''
          } else if (v.type === 'Array') {
            this.query[v.key] = []
          } else {
            this.query[v.key] = ''
          }
        }
      })
      this.initQuery(() => {
        this.$_search()
      })
    },
    // 设置表格高度
    $_setTableHeight() {
      this.$nextTick(() => {
        const fn = () => {
          let height = 0
          if (this.$refs.filterList.offsetHeight) {
            height = this.$refs.filterList.offsetHeight
          }
          return -height - 42
        }
        this.tableHeight = setRemainingHeight(fn)
      })
    },

    // 清除某个查询条件
    $_clearFilter(item) {
      const bool = clearFilterItem.call(this, item)
      if (!bool) {
        this.$_search()
      }
    },
    //新增工单
    // add() {
    //   localStorage.removeItem('alertTicket')
    //   this.$router.push('/ticket/edit')
    // },
    onInfo(item) {
      if (this.query.showSelectFlag) return
      this.$router.push(`/ticket/detail?id=${item.ticketId}`)
    },
    // 单击表格行
    onEdit(item) {
      if (this.query.showSelectFlag) return
      if (this.$route.meta.buttonInfo['ticket-edit']) {
        this.$router.push(`/ticket/edit?id=${item.ticketId}`)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
@import '@core/preset/preset/apps/user.scss';
.ticket-fiter-box {
  padding-top: 6px !important;
  padding-bottom: 6px !important;
}
::v-deep .breamd-box .bar-box {
  margin-bottom: 0 !important;
}
::v-deep .breamd-box .filterList-box {
  padding: 0 !important;
}
::v-deep .tabs-content .tabs-divider {
  width: 1px;
  height: 12px;
  background: var(--v-backgroundColor-base) !important;
  z-index: 1;
}
::v-deep .tabs-background-after {
  fill: var(--v-bgCard-base) !important;
}
::v-deep .tabs-background-before {
  fill: var(--v-bgCard-base) !important;
}
::v-deep .tabs-item.active .tabs-background-before {
  fill: var(--v-backgroundColor-base) !important;
}
::v-deep .tabs-item.active .tabs-background-after {
  fill: var(--v-backgroundColor-base) !important;
}

::v-deep .tab-box {
  width: 152px !important;
}
::v-deep .tabs-content .tabs-divider:nth-child(1) {
  left: 138px !important;
}
::v-deep .tabs-content .tabs-divider:nth-child(2) {
  left: 297px !important;
}
::v-deep .tabs-content .tabs-divider:nth-child(3) {
  left: 457px !important;
}
::v-deep .tabs-content .tabs-divider:nth-child(4) {
  left: 601px !important;
}
::v-deep .vue-tabs-chrome {
  height: 42px !important;
  padding-top: 0 !important;
}
::v-deep .vue-tabs-chrome .tabs-content div:nth-child(5) {
  width: 0px !important;
}
::v-deep .tabs-main {
  margin: 0 !important;
}
::v-deep .vue-tabs-chrome .tabs-content {
  height: 42px !important;
  overflow: visible !important;
  background: var(--v-bgColor-base) !important;
}

::v-deep .vue-tabs-chrome .tabs-background {
  padding: 0 !important;
}
::v-deep .vue-tabs-chrome .tabs-footer {
  height: 0 !important;
}
::v-deep .vue-tabs-chrome .tabs-item {
  cursor: pointer !important;
  width: 166px !important;
}
::v-deep .vue-tabs-chrome .tabs-item.tab-open0 {
  width: 144px !important;
}
::v-deep .vue-tabs-chrome .tabs-item.tab-open1 {
  left: 138px !important;
}
::v-deep .vue-tabs-chrome .tabs-item.tab-open2 {
  left: 296px !important;
}
::v-deep .vue-tabs-chrome .tabs-item.tab-open3 {
  left: 453px !important;
}
::v-deep .vue-tabs-chrome .tabs-item.tab-open4 {
  left: 591px !important;
}
::v-deep .vue-tabs-chrome .tabs-item .tabs-background-content {
  // background: #eceef3 !important;
  background: var(--v-bgCard-base) !important;
}
::v-deep .vue-tabs-chrome .tabs-item:hover .tabs-background-content {
  background-color: rgba(236, 238, 243, 0.3) !important;
}
::v-deep .vue-tabs-chrome .tabs-item.active .tabs-background-content {
  border-top-left-radius: 0 !important;
  border-top-right-radius: 12px !important;
  color: $primary !important;
  background: var(--v-backgroundColor-base) !important;
}
::v-deep .vue-tabs-chrome .tabs-label {
  margin-left: 0 !important;
  margin-right: 0 !important;
  text-align: center !important;
  color: var(--v-color-base) !important;

  font-size: 16px !important;
}
::v-deep .vue-tabs-chrome .tabs-item.active {
  height: 46px !important;
  top: -4px !important;
}
::v-deep .vue-tabs-chrome .tabs-item.active .tabs-label {
  color: $primary !important;
  font-weight: $font-weight-semibold-light !important;
}
// ::v-deep .vue-tabs-chrome .tabs-content div:nth-child(4) {
//   width: 1px !important;
// }

.ticket-box {
  .tip-box {
    font-size: 14px;
    // color: #686e7c;
    .note {
      font-size: 16px;
      color: var(--v-color-base) !important;
    }
  }
}

.status-color {
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  margin: 0 1rem;
}

.flex-column {
  display: flex;
  flex-direction: column;
  height: 100%;
}
</style>
