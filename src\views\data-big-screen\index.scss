$primary: #44e2fe;
$white: #fff;
@function font($num: 16) {
  @return 100vw / 1920 * $num;
}
.ffRoboto {
  font-family: 'Roboto';
  letter-spacing: 0.08em;
}

.ffDef {
  font-family: $body-font-family;
  letter-spacing: 0.08em;
}
.cloud-fs-22 {
  @extend .ffRoboto;
  font-size: font(20);
  // line-height: font(26);
}
.color--primary {
  color: $primary;
}
.fs-16 {
  font-size: font(16);
  line-height: font(22);
  opacity: 0.8;
}

.fs-14 {
  font-size: font(14);
  line-height: font(22);
  opacity: 0.7;
}

.fs-12 {
  font-size: font(12);
  line-height: font(17);
  opacity: 0.6;
}

.fs-21 {
  font-size: font(21);
  line-height: font(21);
}

.fs-18 {
  font-size: font(18);
}

.fs-22 {
  font-size: font(22);
}

.fs-33 {
  font-size: font(33);
  // line-height: font(46);
  // line-height: 46px;
  // line-height: 0.8;
  color: $primary;
}

.fs-64 {
  font-size: font(64);
  line-height: font(89.6);
}

$langs: (
  en: (
    size: font(18),
    family: 'Roboto',
    weight: $font-weight-semibold,
  ),
  zh-CN: (
    size: font(22),
    family: 'Fontquan-XinYiGuanHeiTi',
    weight: $font-weight-normal,
  ),
);
@mixin mixinLang($class: null) {
  @each $key, $value in $langs {
    [data-lang='#{$key}'] & {
      font-size: map-get($value, 'size');
      font-family: map-get($value, 'family');
      font-weight: map-get($value, 'weight');
    }
    @if ($class) {
      [data-lang='#{$key}'] .#{$class} {
        @content ($value);
      }
    }
  }
}
.dv-loading {
  background: url('./images/loading-bg.svg');
  background-position: center;
  background-size: cover;
  // background: #010620;
}
#data-view {
  width: 100vw;
  height: 100vh;
  color: $white;
  position: relative;
  line-height: 1.5;

  #dv-full-screen-container {
    // resize: both;
    width: 100vw;
    height: 100vh;
    background-image: url('./images/<EMAIL>');
    background-position: center;
    background-size: cover;
    display: flex;
    flex-direction: column;
  }

  .cloud-bg {
    background-image: url('./images/loading-bg.svg') !important;
  }

  .main-header {
    position: relative;
    width: 100%;
    color: $primary;
    height: 10%;
    display: flex;
    justify-content: space-between;
    flex-shrink: 0;
    padding: 0 2%;
    margin-top: 0.5%;

    .header-center-decoration {
      width: 45%;
      height: 60%;
      padding-top: 3%;
    }

    .header-left-decoration,
    .header-right-decoration {
      width: 24%;
      height: 55%;
      padding-top: calc(2% + 1px);
    }

    .mh-logo {
      // width: 167px;
      // height: 29px;
      // background: url('./images/yq-logo.png') 70% 30% no-repeat;
      // background-size: auto;
      position: absolute;
      width: 24%;
      left: 4%;
      top: 33%;
    }
    .mh-logo,
    .mh-date {
      transform: translateY(-50%);
    }

    .mh-title {
      position: absolute;
      font-size: font(28);
      font-weight: $font-weight-semibold;
      left: 50%;
      // top: 50%;
      top: 40%;
      transform: translate(-50%, -50%);
    }

    .mh-date {
      position: absolute;
      right: 5%;
      @extend .fs-18;
      width: 20%;
      height: 60%;
      top: 30%;
    }
  }

  .main-container {
    position: relative;
    padding: 1.5% 1.5% 1%;
    display: grid;
    height: calc(100vh - 10%);
    grid-template-columns: 30% auto 30%;
    grid-template-rows: repeat(3, 31.66%);
    gap: 2% 1%;
    .item8 {
      grid-area: 1 / 2 / 3 / 3;
    }
  }
}

#dv-full-screen-container .box {
  width: 100%;
  height: 100%;
  display: flex;
  padding: 4% 5%;
  flex-direction: column;
  position: relative;
  font-style: normal;
  font-family: $body-font-family;
}

.box-header {
  @include mixinLang;
  line-height: font(26);
  letter-spacing: 0.08em;
  color: $white;

  // padding-left: 1.8%;
  padding-left: 2.5%;
  padding-bottom: 1%;
  // margin-left: 2%;

  background: url('./images/subtext.svg') no-repeat;
  background-position: left bottom;
  background-size: contain;
  min-width: font(287);
  max-width: font(360);
  min-height: font(12);
}

.box-header-num {
  @extend .ffDef;
  @extend .fs-21;
  color: $primary;
  font-weight: 500 !important;
  margin-left: font(12);
  vertical-align: bottom;
}

.box-chart {
  width: 100%;
  flex: 1;
}

.dv9 {
  color: $primary;
  font-weight: $font-weight-semibold;
  font-size: font(19);
  line-height: font(27);
}

.digital {
  @extend .fs-33;
  position: absolute;
  top: 5%;
  left: 56%;
  transform: translateX(-50%);
  width: 80%;
  height: 100%;
}

.box-position-top {
  position: absolute;
  width: 100%;
}

$box-left-top: 16%;
.box-left {
  margin-top: $box-left-top !important;
  margin-left: 2%;
  // padding: 18px 27px;
  // padding-left: 30px;
  // width: 190px;
  // height: 106px;
  // filter: drop-shadow(0px 4px 4px rgba(0, 0, 0, 0.25));

  // background: url('./images/box1.png') no-repeat;
  // background-size: contain;
  // background-position: left top;
  // height: font(106);
  // min-width: font(190);

  // display: flex;
  // flex-direction: column;
  // justify-content: center;
}

.img-left {
  margin-top: $box-left-top + 22%;
  // margin-top: $box-left-top + 47px;
  margin-left: -2px;
}

.justify-space-evenly {
  justify-content: space-evenly;
}

.font-fx-16 {
  font-family: 'Fontquan-XinYiGuanHeiTi';
  font-style: normal;
  font-size: font(16);
  line-height: font(19);
  opacity: 0.8;
  @include mixinLang;
}

.box-center-right-text {
  @extend .font-fx-16;
  position: absolute;
  width: 50%;
  left: 35%;
  bottom: 0%;
}

.box-center-left-text {
  @extend .font-fx-16;

  color: $white;
  padding-left: 2%;
  padding-bottom: 1.5%;

  // position: absolute;
  // top: 50%;
  // transform: translateY(-50%);
  // width: 46%;
  // text-align: right;
}

.box-center-left-num {
  font-style: normal;
  font-weight: $font-weight-semibold;
  font-size: font(24);
  line-height: font(34);
  color: $primary;

  width: 40%;
  position: absolute;
  height: 100%;
  top: 4%;
  right: 10%;
}

$box-right-top: 0%;
.img-right {
  // margin-top: 112px;
  // margin-top: $box-right-top + 14%;
  margin-top: -32%;
}

.box-h {
  height: font(112);
}

.box-right {
  margin-right: 3%;
  margin-top: $box-right-top;
  padding-left: 10%;
  padding-top: 9.5%;

  // width: 180px;
  // height: 112px;
  background: url('./images/box2.svg') no-repeat;
  background-size: contain;
  background-position: left bottom;
  min-width: font(210);
  min-height: font(132);
}

.box-right-text {
  @extend .font-fx-16;
  margin-left: font(24);
}

.box-right-num {
  font-style: normal;
  font-weight: $font-weight-semibold;
  font-size: font(40);
  line-height: font(60);
  color: #ff385d;
}

.progress-linear {
  border: 1px solid #ffffff4d !important;
  border-radius: 10px;
  box-shadow: inset 0px 0px 4px #ffffff4d, 0px 0px 4px #ffffff4d;
  // filter: blur(2px);
  height: font(10) !important;
  .v-progress-linear__determinate {
    border-radius: 10px;
    height: 100%;
    // margin: 1px;
    // filter: contrast(200%);
    // background: linear-gradient(270deg, #fe9837 0%, rgba(255, 164, 78, 0) 100%);
  }
}

.progress-gradient {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
}

$progress-color: (
  '0': #32fdb8,
  '1': #21cbff,
  '2': #fe9837,
  '3': #44e2fe,
  '4': #ffe436,
);

@each $name, $val in $progress-color {
  .progress-#{$name} {
    .v-progress-linear__determinate {
      border-color: transparent !important;
      background: linear-gradient(
        270deg,
        $val 0%,
        rgba(255, 164, 78, 0) 100%
      ) !important;
    }
  }
}
// .progress-0 {
//   .v-progress-linear__determinate {
//     background: linear-gradient(270deg, #fe9837 0%, rgba(255, 164, 78, 0) 100%);
//   }
// }

.right-chart1-value {
  @extend .fs-14;
  display: inline-block;
  min-width: font(90);
  max-width: font(100);
  padding-left: 3%;
  // min-width: 8.3333rem;
  white-space: nowrap;
}

.right-chart1-text {
  @extend .fs-14;
  width: 40%;
  min-width: 6rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: font(5);
}

.v-btn:not(.v-btn--round).v-size--small.date-option {
  background: rgba($primary, 0.15) !important;
  color: $primary !important;
  font-size: font(18) !important;
  line-height: font(26) !important;
  padding: font(6) font(16) !important;
  height: auto !important;
}

.v-menu__content:not(.list-style) .v-list-item {
  height: font(45);
}
// .v-btn.btn-icon {
//   background: url('./images/btn.svg') no-repeat center / 100%;
//   // height: font(32) !important;
//   width: font(32) !important;
//   margin-right: 3%;
//   background-size: contain;
// }

.date-list {
  background: rgba($white, 0.1) !important;
  backdrop-filter: blur(6px);
  .v-list-item:not(.v-list-item--active):not(.v-list-item--disabled) {
    color: $white;
  }
  .v-list-item--active {
    color: $primary !important;
  }
}

// dv-border-box-14---start
.mh-tab {
  // top: font(-55);
  // height: font(60.5);
  position: absolute;
  transform: translateY(50%);
  top: font(-45);
  height: font(52);
}
.tab-content {
  height: 100%;
  margin-top: 4%;
}
.border-box {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: font(16);
  margin: 0 font(8);
  border: 1px solid #1b6e9a;
  border-radius: 8px;
  background: #002058;

  color: $primary;
  backdrop-filter: blur(6px);
  @include mixinLang;
}

.border-box2 {
  width: font(220);
  height: font(55);
  background: rgba(68, 226, 254, 0.08) !important;
  margin-bottom: font(16) !important;
}

.left-top-radius {
  position: absolute;
  top: font(-9.625);
  left: font(-10);
  background: url('./images/radius1.svg') no-repeat top left;
  background-size: contain;
  width: font(24);
  height: font(24);
}
.right-top-radius {
  position: absolute;
  top: font(-9.625);
  right: font(-10);
  background: url('./images/radius2.svg') no-repeat top left;
  background-size: contain;
  width: font(24);
  height: font(24);
}
.left-bottom-radius {
  position: absolute;
  bottom: font(-8.625);
  left: font(-10);
  background: url('./images/radius4.svg') no-repeat top left;
  background-size: contain;
  width: font(24);
  height: font(24);
}

.right-bottom-radius {
  position: absolute;
  bottom: font(-8.625);
  right: font(-10);
  background: url('./images/radius3.svg') no-repeat top left;
  background-size: contain;
  width: font(24);
  height: font(24);
}
// dv-border-box-14---end

.box-header2 {
  font-size: font(22);
}

.cloud-container {
  position: relative;
  padding: 1.5% 1.5% 1%;
  display: grid;
  height: calc(100vh - 10%);
  grid-template-columns: repeat(4, 24.2%);
  grid-template-rows: 66% 32%;
  gap: 2% 1%;
  .item7 {
    grid-area: 1 / 2 / 2 / 4;
  }
}
.c-box {
  @extend .box;
  padding: 7.5% 7% !important;
}
.c-box-header {
  font-size: font(20);
  line-height: font(26);
  letter-spacing: -0.04em;
  color: $white;

  // padding-left: 1.8%;
  padding-left: 2.5%;
  padding-bottom: 1%;
  // margin-left: 2%;

  background: url('./images/box-header.svg') no-repeat;
  background-position: left bottom;
  background-size: contain;
  min-width: font(287);
  min-height: font(12);
  max-width: 100%;

  @extend .ffRoboto;
}
.c-left1-num {
  font-weight: $font-weight-semibold;
  font-size: font(40);
  line-height: font(65);
  color: $primary;
}
.c-left1-unit {
  color: $primary;
  margin-left: 2%;
  font-size: font(24);

  opacity: 0.6;
  height: font(112);
  line-height: font(112);
}
.c-center2-text {
  font-size: font(18);
  line-height: font(26);
  opacity: 0.8;
  margin-top: 11%;
  margin-left: font(2);
}
.c-center2-num {
  color: #32fdb8;
  font-size: font(40);
  line-height: font(56);
  font-weight: $font-weight-semibold-light;
  margin: font(16) 0 font(32) font(4);
}
.c-center2-unit {
  font-size: font(24);
  line-height: font(44);
  opacity: 0.6;
}
.shield {
  width: font(275);
  height: font(400);
  background: url('./images/<EMAIL>') center no-repeat;
  background-size: contain;
}
.animate {
  animation: shake-y 4s linear infinite;
}
@keyframes shake-y {
  0% {
    transform: translate(0px, 0px);
  }
  50% {
    transform: translate(0, font(15));
  }
  100% {
    transform: translate(0px, 0px);
  }
}
.c1-posture {
  text-shadow: 0 0 font(10) $primary;
}
.c1-text {
  font-size: font(28);
  line-height: font(39.2);
  font-weight: 500;
  color: #cdeeff;
  margin: font(16) 0 font(32);
  opacity: 0.6;
  text-shadow: 0 0 font(3) #cdeeff;
}
// demo css
.main-header-1 {
  .title-date1 {
    position: absolute;
    top: 30%;
    // width: 24%;
    left: 6%;
    transform: translateY(-50%);
    font-size: font(18);
    line-height: font(22);
    font-weight: $font-weight-semibold-light;
  }
  .title-header-day {
    font-size: font(28);
    font-weight: $font-weight-semibold;
    color: #32fdb8;
  }
}
.table-card.table-card-1 {
  margin-top: font(12);
}
.table-card .risk-num {
  width: font(28);
  height: font(18);
}
.table-card .risk-num-sort {
  width: font(24);
  height: font(24);
  border-radius: 50%;
  text-align: center;
  line-height: font(24);
}
.table-card.table-card-1 tbody {
  visibility: collapse !important;
}
.table-card.table-card-2 .v-data-table-header {
  visibility: collapse !important;
}
.table-card.table-card-1 .v-data-table__wrapper {
  overflow: hidden !important;
}
.table-card.v-data-table.v-data-table--fixed-header thead th {
  background: #0d3f87;
  color: #21cbff !important;
  border-bottom: thin solid transparent !important;
  box-shadow: none !important;
  font-size: font(14) !important;
}
.table-card.v-data-table .v-data-table__wrapper > table > thead > tr > th {
  height: font(26) !important;
  padding: 0 font(0) !important;
}
.table-card.v-data-table > .v-data-table__wrapper > table > tbody > tr > td {
  color: #fff !important;
  border-bottom: thin solid transparent !important;
  font-size: font(14) !important;
  height: font(38) !important;
  padding: 0 font(0) !important;
}
