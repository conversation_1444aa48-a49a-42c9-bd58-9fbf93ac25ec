<template>
  <div class="auth-wrapper auth-v2">
    <div class="auth-inner d-flex justify-center align-center">
      <!-- brand logo -->
      <!-- <router-link to="/" class="brand-logo d-flex align-center">
        <v-img
          :src="require('@/assets/images/svg/logo-primary.svg')"
          max-height="30px"
          max-width="30px"
          alt="logo"
          contain
          class="me-3"
        ></v-img>

        <h2 class="text--primary">
          Automotive Cybersecurity
        </h2>
      </router-link> -->
      <!--/ brand logo -->
      <div class="login">
        <v-card dark color="primary" class="left pa-10">
          <div>
            <!-- <v-img
              class="ml-n5"
              max-height="80"
              contain
              src="@/assets/images/svg/logo2.png"
            ></v-img> -->
            <!-- src="@/assets/images/logo.png" -->
            <v-img
              min-height="100%"
              min-width="100%"
              class="ml-n2"
              contain
              :src="appLogo"
            ></v-img>
            <!-- <v-img
              min-height="100%"
              min-width="100%"
              class="ml-n5"
              contain
              :src="appLogo"
            ></v-img> -->
          </div>
          <div class="left-bottom">
            <div class="nice-name">{{ appName }}</div>
            <div class="name">{{ $t('projectName') }}</div>
          </div>
        </v-card>
        <div class="auth-card right bg-transparent">
          <div class="d-flex justify-end mb-2">
            <app-bar-i18n mode="login"></app-bar-i18n>
          </div>
          <div>
            <div>
              <p
                class="text-3xl font-weight-normal text-white mb-3 text-uppercase"
              >
                {{ $t('login.name') }}
              </p>
              <p class="mb-6">{{ $t('login.hint') }}</p>
            </div>

            <!-- login form -->
            <div>
              <v-form ref="loginForm" @submit.prevent="handleFormSubmit">
                <!-- 用户名 -->
                <v-text-field
                  v-model="userName"
                  :label="$t('login.userName')"
                  :placeholder="$t('login.userName')"
                  hide-details="auto"
                  class="mb-6"
                  color="#ffffff9e"
                  :rules="[required(userName, $t('login.userName'))]"
                >
                  <template #prepend-inner>
                    <v-icon class="mt-1" size="16">mdi-account-outline</v-icon>
                  </template>
                </v-text-field>
                <!-- 密码 -->
                <v-text-field
                  v-model="password"
                  :type="isPasswordVisible ? 'text' : 'password'"
                  :label="$t('login.password')"
                  :placeholder="$t('login.password')"
                  hide-details="auto"
                  class="mb-2"
                  color="#ffffff9e"
                  :rules="[required(password, $t('login.password'))]"
                >
                  <template #prepend-inner>
                    <v-icon class="mt-1" size="16">mdi-lock-outline</v-icon>
                  </template>
                  <template #append>
                    <v-icon
                      class="mt-1"
                      size="16"
                      @click="isPasswordVisible = !isPasswordVisible"
                      >{{
                        isPasswordVisible
                          ? icons.mdiEyeOffOutline
                          : icons.mdiEyeOutline
                      }}</v-icon
                    >
                  </template>
                </v-text-field>

                <div
                  class="d-flex align-center justify-space-between flex-wrap text-sm"
                >
                  <!-- <v-checkbox
                  style="display: none !important"
                  hide-details
                  label="保持登录"
                  color="primary"
                  class="text-sm"
                  dense
                  :value="checked"
                >
                </v-checkbox> -->
                  <div></div>
                  <div class="ms-3 my-1 router-link opacity-0">
                    {{ $t('login.forget') }}
                  </div>
                  <!-- <router-link
                    :to="{ name: '忘记密码' }"
                    class="ms-3 my-1 router-link opacity-0"
                  >
                    {{ $t('login.forget') }}
                  </router-link> -->
                </div>

                <v-btn
                  block
                  :loading="loginLoading"
                  color="white"
                  type="submit"
                  class="mt-7"
                >
                  {{ $t('login.name') }}
                </v-btn>
              </v-form>
            </div>

            <!-- create new account  -->
            <div class="d-flex flex-row-reverse mt-4">
              <p class="mb-0 me-2">{{ $t('login.noAccount') }}</p>
              <!-- <a href="#">  申请试用 </a> -->
            </div>
          </div>
        </div>
      </div>
    </div>
    <slide-box ref="slideblock" :successFun="successVerify" />
  </div>
</template>

<script>
import AppBarI18n from '@/@core/layouts/components/app-bar/AppBarI18n.vue'
// eslint-disable-next-line object-curly-newline
import { resetRouter } from '@/router'
import { useRouter } from '@core/utils'
import {
  mdiEyeOffOutline,
  mdiEyeOutline,
  mdiFacebook,
  mdiGithub,
  mdiGoogle,
  mdiTwitter,
} from '@mdi/js'
import themeConfig from '@themeConfig'
import {
  getCurrentInstance,
  onMounted,
  reactive,
  ref,
} from '@vue/composition-api'
import md5 from 'md5'
// import Cookies from 'js-cookie'

import { required } from '@/@core/utils/validation'
import SlideBox from './SlideBox'

export default {
  components: {
    SlideBox,
    AppBarI18n,
  },
  setup() {
    const addActiveClass = () => {
      const targetNode = document.querySelectorAll(
        'input:-internal-autofill-selected',
      )

      targetNode.forEach(el => {
        const textField = el.parentNode
        // el.parentNode
        const label = textField.querySelector('.v-label')
        if (label) {
          label.classList.add('v-label--active')
        }
        if (textField.MDCTextField) {
          textField.MDCTextField.foundation_.notchOutline(true)
        }
      })
    }
    onMounted(async () => {
      let timeId = setTimeout(() => {
        clearTimeout(timeId)
        addActiveClass()
      }, 600)
    })
    const loginLoading = ref(false)

    const vm = getCurrentInstance().proxy

    const checked = ref(false)

    // Template Ref
    const loginForm = ref(null)
    const { router } = useRouter()

    const isPasswordVisible = ref(false)

    const alertObj = reactive({
      type: 'success',
      text: '',
      isShow: false,
    })

    const userName = ref('')
    const password = ref('')

    const socialLink = [
      {
        icon: mdiFacebook,
        color: '#4267b2',
        colorInDark: '#4267b2',
      },
      {
        icon: mdiTwitter,
        color: '#1da1f2',
        colorInDark: '#1da1f2',
      },
      {
        icon: mdiGithub,
        color: '#272727',
        colorInDark: '#fff',
      },
      {
        icon: mdiGoogle,
        color: '#db4437',
        colorInDark: '#db4437',
      },
    ]
    const slideblock = ref(null)
    const handleFormSubmit = () => {
      const isFormValid = loginForm.value.validate()

      if (!isFormValid) return
      slideblock.value.isDialogVisible = true
      slideblock.value.onReset()
    }
    const successVerify = () => {
      slideblock.value.isDialogVisible = false
      loginLoading.value = true

      vm.$store.commit('enums/clearEnums')
      vm.$store.commit('enums/clearConfigs')

      const md5Password = md5(password.value)
      const formData = new FormData()

      formData.append('username', userName.value)
      formData.append('password', `${md5Password.slice(0, 10)}vsoc`)
      vm.$store
        .dispatch('user/logIn', formData)
        .then(res => {
          vm.$store.dispatch('permission/clearPermission')
          resetRouter()
          vm.$notify.info('success', vm.$t('login.successHint'))
          // router.push('/')
          // router.go(-1)
        })
        .catch(err => {
          vm.$notify.info('error', err)
        })
        .finally(() => {
          loginLoading.value = false
        })
    }
    // 记住密码功能
    // const handleChecked = checked => {
    //   this.checked = checked

    //   // 将密码存入缓存中
    //   if (checked) {
    //     Cookies.set('userName', userName.value)
    //     Cookies.set('password', escape(password.value))
    //   } else {
    //     // 清除缓存中的密码
    //     Cookies.remove('userName')
    //     Cookies.remove('password')
    //   }
    // }

    return {
      loginLoading,

      // handleChecked,
      handleFormSubmit,
      isPasswordVisible,
      userName,
      password,
      socialLink,

      required,

      // Icons
      icons: {
        mdiEyeOutline,
        mdiEyeOffOutline,
      },

      // themeConfig
      appName: themeConfig.app.name,
      appLogo: themeConfig.app.logo,
      appTitle: themeConfig.app.title,

      // Template Refs
      loginForm,
      checked,
      alertObj,
      slideblock,
      successVerify,
    }
  },
}
</script>

<style lang="scss" scoped>
@import '@core/preset/preset/pages/auth.scss';
.left {
  // background: url('../../assets/images/loginBg/left.png') no-repeat center/100%;
  position: absolute;
  letter-spacing: -0.5px;
  width: 26.6667rem;
  height: 43.3333rem;

  border-radius: 8px;
  top: -5.5%;
  left: 3%;

  background: linear-gradient(
    92.38deg,
    #0f3480 0.9%,
    #224fa9 99.43%
  ) !important;
  // background: url('../../assets/images/loginBg/left.svg') no-repeat center/cover;
  // background-color: #001e50;

  // mix-blend-mode: overlay;
  // opacity: 0.18;
}
.right {
  position: absolute;
  width: 23.3333rem;
  height: 35rem;
  top: 10%;
  right: 8%;
}
.left-bottom {
  position: absolute;
  bottom: 10%;
  .nice-name {
    width: 5.9167rem;
    height: 2.6667rem;

    font-family: 'Aldrich';
    font-style: normal;
    font-weight: 400;
    font-size: 2rem;
    line-height: 2.6667rem;
    /* identical to box height, or 133% */
    /* #fff */
    color: #ffffff;
    margin-bottom: 10px;
  }
  .name {
    width: 14.6667rem;
    height: 2rem;

    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 400;
    font-size: 1.3333rem;
    line-height: 2rem;
    white-space: nowrap;
    /* identical to box height, or 150% */

    /* #fff */

    color: $white;

    opacity: 0.62;
  }
}
.login {
  // background: url('../../assets/images/loginBg/login-2x.png') no-repeat;
  position: relative;
  width: 63.3333rem;
  height: 39.1667rem;
  background-color: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(12px);
  /* Note: backdrop-filter has minimal browser support */

  border-radius: 8px;
}
.mar-tb {
  margin-bottom: 16px !important;
}
.v-application.theme--light .auth-wrapper {
  background: url('../../assets/images/loginBg/car.png') no-repeat center/cover;
}
// .auth-wrapper.auth-v2 .auth-inner .auth-bg .v-card {
//   padding: 0.9375rem 0.875rem;
//   box-shadow: 0 2px 10px 0 rgb(94 86 105 / 10%) !important;
// }
.mar-tb {
  margin: 18px 0 !important;
}
// 当屏幕宽度小于1260px时，添加背景图片
// @media (max-width: 1260px) {
//   .v-application.theme--light .auth-wrapper.auth-v2 .auth-inner .auth-bg {
//     background: url('../../assets/images/misc/vsoc_bg_1.png') no-repeat fixed
//       center;
//   }
//   .auth-wrapper.auth-v2 .auth-inner .auth-bg .v-card {
//     padding: 0.9375rem 0.875rem;
//     box-shadow: 0 2px 10px 0 rgb(94 86 105 / 10%) !important;
//   }
//   .mar-tb {
//     margin: 18px 0 !important;
//   }
// }
// @media (min-width: 1260px) {
//   ::v-deep .v-btn:not(.v-btn--round).v-size--default {
//     height: 3.25rem !important;
//   }
// }
// 继承屏幕高度，防止登录页面出现滚动条
.auth-wrapper.auth-v2 .auth-inner {
  height: 100%;
}
.auth-wrapper {
  min-height: auto;
  height: 100%;
}
.mdc-text-field__input:-webkit-autofill + .mdc-floating-label {
  transform: translateY(-50%) scale(0.75);
  cursor: auto;
}
.text-note {
  font-size: 12px;
  line-height: 18px;
  font-weight: 400;
}
.color-base {
  color: #1f2533;
}
</style>
