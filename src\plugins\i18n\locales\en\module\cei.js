const cei = {
  currentTitle: 'CEI Strategy',
  title: {
    securityPolicy: 'Security Strategy',
    pending: 'Pending Review Strategy',
    review: 'CEI Review',
  },
  reviewfailed: 'Review failed',
  reviewSuccess: 'Review successfully',
  headers: {
    id: 'ID',
    ruleVersion: 'Version',
    status: 'Status',
    strategyType: 'Strategy type',
    ruleContent<PERSON>son: 'Rule Content',
    platformType: 'Platform Type',
    updateDate: 'Update Time',
    updateUser: 'Update User',
    createUser: 'Create User',
    createDate: 'Create Time',
    reason: 'Reason',
    oldVersion: 'Old Version',
    newVersion: 'New Version',
  },
  tip: 'Rule Content is required',
}

export default cei
