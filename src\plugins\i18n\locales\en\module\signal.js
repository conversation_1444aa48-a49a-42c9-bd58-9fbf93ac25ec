const signal = {
  currentTitle: 'Signal',
  headers: {
    name: 'Name',
    desc: 'Description',
    encryption: 'Encryption',
    valueType: 'Value Type',
    modelType: 'Signal Type',
    intervalSize: 'Interval Size',
    intervalCount: 'Interval Count',
    lastUpdate: 'Last Updated',
    dataType: 'Data Type',
  },
  ttl: {
    maximum:
      'Maximum retention time (the maximum time before the value will be removed from the Digital Twin of the asset)',
    value0: '0 - Kept until the next message',
    value1: '-1 - Kept until the next update of the same signal',
  },
  id: 'Signal Unique Identifier',
  monitoringPeriod: 'Monitoring Period',
  period1:
    'The event histogram keeps track of occurrences count at each interval',
  period2:
    'Select the interval size of the histogram and the interval count to effectively support detectio',
  size1:
    'Select the interval size to best support the granularity required for detection.',
  size2:
    'Select an interval that is shorter than the timeframe required for detection.',
  count1:
    'Select number of historical intervals required to be available for detection',
  count2: 'Present on the alert investigation timeline',
  category: 'Category',
  trueEscape: 'True escape value:',
  falseEscape: 'False escape value:',
  setOptionalValue: 'Set optional value',
  optionalValue: 'Optional value',
  selectSub: 'Select sub-signal',
  noData: 'No matching data',
  unit: 'Signal Unit',
  max: 'Max',
  min: 'Min',
  customize: 'Customize',
  selectUnit: 'Select unit',
  valueAlready: 'The value already exists',
  subHint: 'Select at least one sub-signal',
  new: 'Add @:signal.currentTitle',
  edit: 'Edit @:signal.currentTitle',
  swal: {
    title: 'Delete Signal',
    hint: 'Are you sure you want to delete signal {name}',
  },
  enum: 'Enum Value',
  enumMap: 'Mapping of enumeration values',
  ttlTip: 'TTL cannot exceed 15 days, please select a new time limit!',
}

export default signal
