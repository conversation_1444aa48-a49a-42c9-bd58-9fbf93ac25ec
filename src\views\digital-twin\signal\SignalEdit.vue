<!-- 资产模型修改/新增页面 -->
<template>
  <edit-page class="text-base" :popsName="$t(`signal.${editType}`)">
    <template slot="BreadBtn">
      <div>
        <v-btn
          class="ms-3"
          color="primary"
          :loading="confirmLoading"
          @click="confirmEditSignal"
        >
          {{ $t('action.confirm') }}
        </v-btn>
      </div>
    </template>
    <template>
      <v-row class="ma-0">
        <v-col cols="12">
          <v-form ref="form" v-model="valid">
            <v-row class="mt-2 color-base">
              <v-col cols="12">
                <div class="text-title">{{ $t('global.drawer.baseInfo') }}</div>
              </v-col>

              <v-col cols="6" class="pt-0 pl-12">
                <v-text-field
                  class="is-required"
                  v-model="editSignal.id"
                  :disabled="editType === 'edit'"
                  color="primary"
                  :label="$t('signal.id')"
                  :rules="signalRules.id"
                ></v-text-field>
              </v-col>
              <v-col cols="6" class="pt-0 pr-12">
                <v-text-field
                  class="is-required"
                  v-model="editSignal.name"
                  color="primary"
                  :label="$t('signal.headers.name')"
                  :rules="signalRules.name"
                ></v-text-field>
              </v-col>
              <v-col cols="12" class="pt-0 px-12">
                <v-textarea
                  v-model="editSignal.description"
                  outlined
                  :rows="$AREA_ROWS"
                  color="primary"
                  :label="$t('signal.headers.desc')"
                ></v-textarea>
                <!-- :rules="signalRules.max" -->
              </v-col>
              <v-col cols="12" class="pt-0 mt-n1">
                <div class="text-title">{{ $t('global.status') }}</div>
              </v-col>
              <v-col cols="12" class="pt-0 px-12">
                <v-btn-toggle
                  v-model="editSignal.active"
                  mandatory
                  color="primary"
                  class="my-3 mt-sm-0 toggle-btn"
                >
                  <v-btn
                    v-for="item in activeEnum"
                    :key="item.value"
                    class="px-6"
                    :value="item.value"
                    elevation="0"
                    disabled
                  >
                    <v-icon size="1.25rem" :color="item.color" left>
                      {{ item.icon }}
                    </v-icon>
                    <span> {{ item.text }}</span>
                  </v-btn>
                </v-btn-toggle>
              </v-col>
              <v-col cols="12" class="pt-0">
                <div class="text-title mt-4">{{ $t('global.assetType') }}</div>
              </v-col>
              <v-col cols="12" class="pt-0 px-12">
                <v-btn-toggle
                  v-model="editSignal.assetType"
                  mandatory
                  color="primary"
                  class="my-3 mt-sm-0 toggle-btn"
                  @change="assetTypeChange"
                >
                  <v-btn
                    elevation="0"
                    v-for="item in assetTypeEnum"
                    :key="item.value"
                    :disabled="editType === 'edit'"
                    :value="item.key"
                  >
                    <!-- <v-icon size="1.5rem" class="mr-2">
                        {{ item.icon }}
                      </v-icon> -->
                    <vsoc-icon
                      type="fill"
                      :icon="item.icon"
                      class="mr-2"
                    ></vsoc-icon>
                    <span>{{ item.text }}</span>
                  </v-btn>
                </v-btn-toggle>
              </v-col>

              <v-col cols="12" class="pt-0">
                <div class="text-title mt-4">
                  {{ $t('signal.headers.modelType') }}
                </div>
              </v-col>
              <v-col cols="12" class="pt-0 px-12">
                <v-btn-toggle
                  v-model="editSignal.signalType"
                  mandatory
                  color="primary"
                  class="m-y3 mt-sm-0 toggle-btn"
                  @change="signalTypeChange"
                >
                  <template>
                    <v-btn
                      v-for="item in signalTypeList"
                      :key="item.value"
                      :disabled="editType === 'edit'"
                      elevation="0"
                      :value="item.key"
                    >
                      <v-icon size="1.5rem" class="mr-2">
                        {{ item.icon }}
                      </v-icon>
                      <span>{{ item.text }}</span>
                    </v-btn>
                  </template>
                </v-btn-toggle>
              </v-col>
              <v-col cols="12">
                <template v-if="editSignal.signalType === '1'">
                  <div class="text-title">
                    {{ $t('signal.monitoringPeriod') }}
                  </div>
                  <div class="mt-3 px-12">
                    <div class="my-2">
                      <!-- The event histogram keeps track of occurrences count at each interval. -->
                      {{ $t('signal.period1') }}
                    </div>
                    <div class="my-2">
                      <!-- Select the interval size of the histogram and the interval count to effectively support detection -->
                      {{ $t('signal.period2') }}
                    </div>
                  </div>
                  <div class="text-title mt-6">
                    {{ $t('signal.headers.intervalSize') }}
                  </div>
                  <div class="mt-3 px-12">
                    <div class="my-2">
                      <!-- Select the interval size to best support the granularity required for detection. -->
                      {{ $t('signal.size1') }}
                    </div>
                    <div class="my-2">
                      <!-- Select an interval that is shorter than the time frame required for detection. -->
                      {{ $t('signal.size2') }}
                    </div>
                    <v-btn-toggle
                      v-model="editSignal.intervalSize"
                      mandatory
                      color="primary"
                      class="mt-3 mt-sm-0 toggle-btn"
                      @change="signalTypeChange"
                    >
                      <v-btn
                        v-for="item in intervalSizeList"
                        :key="item.value"
                        elevation="0"
                        :value="item.value"
                      >
                        <v-icon size="1.5rem" class="mr-2">
                          {{ item.icon }}
                        </v-icon>
                        <span>{{ item.text }}</span>
                      </v-btn>
                    </v-btn-toggle>
                    <!-- <v-select
                        v-model="editSignal.intervalSize"
                        dense
                        color="primary"
                        append-icon="mdi-chevron-down"
                        :items="intervalSizeList"
                        :menu-props="{offsetY: true}"
                        class=" placeholder-light select-style"
                        outlined
                        label="间隔大小"
                        @change="signalTypeChange"
                      ></v-select> -->
                  </div>
                  <div class="text-title mt-6">
                    {{ $t('signal.headers.intervalCount') }}
                  </div>
                  <div class="px-12 mt-3">
                    <div class="my-2">
                      <!-- Select number of historical intervals required to be available for detection. -->
                      {{ $t('signal.count1') }}
                    </div>
                    <div class="w-50">
                      <v-slider
                        v-model="editSignal.intervalCount"
                        :thumb-size="23"
                        min="1"
                        max="144"
                        thumb-label="always"
                        class="mb-0 pb-0 mt-4"
                        hide-details
                      ></v-slider>
                      <div class="d-flex justify-space-between px-1">
                        <div>1</div>
                        <div>144</div>
                      </div>
                    </div>

                    <!-- v-model="checkbox" -->
                    <div>
                      <!-- 默认勾选 -->
                      <v-checkbox
                        hide-details
                        v-model="editSignal.isShow"
                        true-value="1"
                        false-value="0"
                      >
                        <template #label>
                          <div class="text-base font-weight-bolder">
                            {{ $t('signal.count2') }}
                          </div>
                        </template>
                      </v-checkbox>
                    </div>

                    <!-- <div>
                        Confidence Level
                      </div> -->
                  </div>
                  <div class="text-title mt-6 mb-2">
                    {{ $t('signal.category') }}
                  </div>
                  <div class="px-12">
                    <v-radio-group
                      v-model="editSignal.classification"
                      dense
                      class="mt-0 text-base font-weight-bolder"
                      hide-details
                    >
                      <v-radio
                        v-for="item in classification"
                        :key="item.value"
                        :value="item.value"
                      >
                        <template #label>
                          <span class="text-base font-weight-bolder">{{
                            item.text
                          }}</span>
                        </template>
                      </v-radio>
                    </v-radio-group>
                  </div>
                </template>
                <template v-if="editSignal.signalType === '0'">
                  <v-col cols="12" class="pt-0 px-0">
                    <div class="text-title mt-4">
                      {{ $t('signal.headers.dataType') }}
                    </div>
                  </v-col>
                  <v-col cols="12" class="pt-0 px-9">
                    <v-btn-toggle
                      v-model="editSignal.dataType"
                      mandatory
                      color="primary"
                      class="toggle-btn"
                    >
                      <template>
                        <v-btn
                          v-for="item in signalDataTypeEnum"
                          :key="item.value"
                          elevation="0"
                          :value="item.value"
                          :disabled="editType === 'edit'"
                        >
                          <span>{{ item.text }}</span>
                        </v-btn>
                      </template>
                    </v-btn-toggle>
                  </v-col>
                  <!-- 值类型 -->
                  <div class="text-title mt-5">
                    {{ $t('signal.headers.valueType') }}
                  </div>
                  <div class="w-100 px-12 mt-5">
                    <div class="w-50">
                      <v-select
                        v-model="editSignal.signalValueType"
                        :disabled="editType === 'edit'"
                        dense
                        color="primary"
                        append-icon="mdi-chevron-down"
                        :items="signalValueTypeList"
                        :menu-props="{ offsetY: true }"
                        outlined
                        :label="$t('detector.hint.tip16')"
                        :rules="signalRules.signalValueType"
                        @change="signalValueTypeChange"
                        item-text="text"
                        item-value="key"
                      ></v-select>
                    </div>
                    <!-- Boolean类型出现true和false的枚举值配置 -->
                    <template v-if="editSignal.signalValueType === '1'">
                      <div class="d-flex">
                        <div class="flex-1 mr-4">
                          <v-text-field
                            v-model="booleanEnum.true"
                            outlined
                            dense
                            color="primary"
                            class="is-required"
                            :label="$t('signal.trueEscape')"
                          ></v-text-field>
                          <!-- :rules="signalRules.boolTrueRules" -->
                        </div>
                        <div class="flex-1">
                          <v-text-field
                            v-model="booleanEnum.false"
                            outlined
                            dense
                            class="is-required"
                            color="primary"
                            :label="$t('signal.falseEscape')"
                          ></v-text-field>
                          <!-- :rules="signalRules.boolFalseRules" -->
                        </div>
                      </div>
                    </template>
                    <!-- Text 类型出现可选值 -->
                    <template v-if="editSignal.signalValueType === '0'">
                      <div
                        class="text-base text-primary font-weight-semibold mb-4"
                      >
                        {{ $t('signal.setOptionalValue') }}
                      </div>
                      <div class="d-flex align-start flex-wrap">
                        <v-chip
                          v-for="(tag, i) in editSignal.constraints.enumValues"
                          :key="tag"
                          class="mr-2 mb-2"
                          close
                          color="primary"
                          label
                          @click:close="handleCloseEnumVal(i)"
                        >
                          {{ tag }}
                        </v-chip>
                        <div v-if="showAddEnumValues" class="w-100">
                          <v-text-field
                            ref="enumInput"
                            v-model="enumVal"
                            outlined
                            dense
                            :label="$t('signal.optionalValue')"
                            color="primary"
                            hide-details
                            autofocus
                            :rules="enumInput"
                            @keyup.enter.native="handleAddEnumVal"
                            @blur="handleAddEnumVal"
                          ></v-text-field>
                        </div>
                        <v-btn
                          v-else
                          height="2rem"
                          width="72px"
                          elevation="0"
                          class="primary--text bg-btn"
                          @click="showInput"
                        >
                          <v-icon>mdi-plus</v-icon>
                          {{ $t('action.add') }}
                        </v-btn>
                      </div>
                    </template>
                    <!-- 5 类型出现选择子信号 -->
                    <template v-if="editSignal.signalValueType === '5'">
                      <v-autocomplete
                        v-model="editSignal.subSignalIdList"
                        multiple
                        chips
                        color="primary"
                        append-icon="mdi-chevron-down"
                        :items="childSignals"
                        :menu-props="{ offsetY: true }"
                        item-text="displayName"
                        item-value="id"
                        outlined
                        :label="$t('signal.selectSub')"
                        :rules="signalRules.childSignals"
                      >
                        <template v-slot:selection="{ item, index }">
                          <v-chip
                            label
                            color="primary"
                            close
                            @click:close="
                              clearChip(editSignal.subSignalIdList, index)
                            "
                          >
                            <span>{{
                              item.displayName ? item.displayName : item
                            }}</span>
                          </v-chip>
                        </template>
                        <template #no-data>
                          <p class="text-sm text-body py-1 text-center">
                            {{ $t('signal.noData') }}
                          </p>
                        </template>
                      </v-autocomplete>
                    </template>
                    <!-- 6 类型出现选择Structured Id -->
                    <template v-if="editSignal.signalValueType === '6'">
                      <v-autocomplete
                        v-model="editSignal.structureId"
                        dense
                        color="primary"
                        append-icon="mdi-chevron-down"
                        :items="structuredSignals"
                        :menu-props="{ offsetY: true }"
                        item-text="displayName"
                        item-value="id"
                        outlined
                        :label="$t('signal.selectSub')"
                        :rules="signalRules.childSignals"
                      ></v-autocomplete>
                    </template>
                    <template
                      v-if="['3', '2'].includes(editSignal.signalValueType)"
                    >
                      <v-text-field
                        v-model="editSignal.constraints.symbol"
                        outlined
                        dense
                        color="primary"
                        :label="$t('signal.unit')"
                      ></v-text-field>
                      <v-text-field
                        v-model="editSignal.constraints.min"
                        outlined
                        dense
                        color="primary"
                        type="number"
                        :label="$t('signal.min')"
                        :rules="
                          editSignal.signalValueType == '3' && signalRules.int
                        "
                      ></v-text-field>
                      <v-text-field
                        v-model="editSignal.constraints.max"
                        outlined
                        dense
                        color="primary"
                        type="number"
                        :label="$t('signal.max')"
                        :rules="
                          editSignal.signalValueType == '3' && signalRules.int
                        "
                      ></v-text-field>
                    </template>
                  </div>
                </template>
              </v-col>
            </v-row>
          </v-form>
        </v-col>
      </v-row>
    </template>
  </edit-page>
</template>

<script>
import { integerValidator, max, required } from '@/@core/utils/validation'
import {
  alterTableField,
  digitalTwinDetails,
  saveDigitalTwin,
  updateDigitalWin,
} from '@/api/signal'
import EditPage from '@/components/EditPage.vue'
// import { signalValueTypeList } from '@/components/logic-engine/lib/tools'
import { formatSeconds, toSeconds } from '@/util/utils'
import { cloneDeep } from 'lodash'

export default {
  name: 'SignalEdit',
  components: {
    EditPage,
  },
  data() {
    return {
      // 信号值类型可选
      signalValueTypeList: [
        {
          text: '字符型(String)',
          oldText: 'Text',
          value: 'STRING',
          key: '0',
        },
        {
          text: '布尔型(Boolean)',
          oldText: 'Boolean',
          value: 'BOOLEAN',
          key: '1',
        },
        {
          text: '小数型(Double)',
          oldText: 'Numeric',
          value: 'DOUBLE',
          key: '2',
        },
        {
          text: '整数型(Long)',
          oldText: 'Whole number',
          value: 'LONG',
          key: '3',
        },
        {
          text: '时间戳(Timestamp)',
          oldText: 'Timestamp',
          value: 'TIMESTAMP',
          key: '4',
        },
        {
          text: '字典型(Dictionary)',
          oldText: 'Structured',
          value: 'DICTIONARY',
          key: '5',
        },
        {
          text: '对象数组(Object array)',
          oldText: 'Structured list',
          value: 'OBJECT_ARRAY',
          key: '6',
        },
        {
          text: '数组型(Array)',
          oldText: 'Array',
          value: 'ARRAY',
          key: '7',
        },
      ],
      editType: 'new',
      valid: false,
      showAddEnumValues: false,
      isRepeatId: false,
      signalRules: {
        boolTrueRules: [v => required(v, this.$t('signal.trueEscape'))],
        boolFalseRules: [v => required(v, this.$t('signal.falseEscape'))],
        name: [v => required(v, this.$t('signal.headers.name'))],
        id: [
          v => required(v, this.$t('signal.id')),
          v =>
            /^[0-9a-z_]{2,99}$/.test(v) || this.$t('validation.underValidate'),
        ],
        signalValueType: [
          v => required(v, this.$t('signal.headers.valueType')),
        ],
        intervalSize: [
          v => required(v, this.$t('signal.headers.intervalSize')),
        ],
        int: [v => integerValidator(v)],

        // id: [
        //   v => !!v || '信号编号是必填的',
        //   () => !this.isRepeatId || '信号编号重复',
        // ],

        // ttl: [v => !!v || 'TTL是必填的', v => (v >= -1 && v % 1 === 0) || '有效值范围：大于等于-1的整数'],
        ttl: [
          v => required(v, 'TTL'),
          v =>
            (v >= 1 && v <= 999999) ||
            this.$t('validation.intRange2', { range: '1-999999' }),
        ],
        max: [v => max(v, 255)],

        childSignals: [v => (!!v && v.length > 0) || this.$t('signal.subHint')],
      },
      enumInput: [
        value =>
          !this.editSignal.constraints.enumValues.includes(value) ||
          this.$t('signal.valueAlready'),
      ],
      editSignal: {
        id: '',
        dataType: '0',
        assetType: '0',
        isEncrypt: '0',
        description: '',
        signalType: '0',
        signalValueType: '0',
        intervalSize: '',
        intervalCount: '',
        ttl: '',
        ttlType: '1',
        ttlUnit: '',
        name: '',
        unit: '',
        unitSymbol: '',
        subSignalIdList: [],
        structureId: '',
        isShow: '1',
        active: '1',
        constraints: {
          enumValues: [],
          max: '',
          symbol: '',
          maxLength: '',
          min: '',
        },
      },
      booleanEnum: {
        true: '',
        false: '',
      },

      // signalValueTypeList: signalValueTypeList.filter(v => v.key !== '8'),
      enumVal: '',
      confirmLoading: false,
      detailLoading: true,
    }
  },
  computed: {
    encryptionEnum() {
      return this.$store.state.enums?.configs?.['Encryption_Mode_Flag']?.[
        'Encryption_Mode_Flag'
      ].propertyValue
    },
    signalDataTypeEnum() {
      return this.$store.state.enums.enums.SignalDataType
    },
    intervalSizeList() {
      return [
        {
          text: this.$tc('global.time.min', 1, [1]),
          value: 60,
        },
        {
          text: this.$tc('global.time.min', 2, [10]),
          value: 600,
        },
        {
          text: this.$tc('global.time.hours', 1, [1]),
          value: 3600,
        },
        {
          text: this.$tc('global.time.day', 1, [1]),
          value: 86400,
        },
      ]
    },
    // 分类
    classification() {
      return this.$store.state.enums.enums.Classification
    },

    // 子信号，筛选
    childSignals() {
      // 数据类型为子信号：dataType为2
      return this.$store.getters['global/getSignals'](
        this.editSignal.assetType,
      ).filter(
        v => !['5', '6', '7'].includes(v.signalValueType) && v.dataType === '2',
      )
    },

    // 筛选结构化信号(属于结构化列表的子信号)
    structuredSignals() {
      return this.$store.getters['global/getSignals'](
        this.editSignal.assetType,
      ).filter(v => v.signalValueType === '5' && v.dataType === '2')
    },
    assetTypeEnum() {
      return this.$store.state.enums.enums.AssetType
    },
    activeEnum() {
      return this.$store.getters['enums/getActiveStatus']
    },
    signalTypeList() {
      const list = []
      for (const key in this.$store.state.enums.enums.SignalType) {
        list.push(this.$store.state.enums.enums.SignalType[key])
      }

      return list
    },
    assetType() {
      return this.$store.state.enums.enums.AssetType
    },
    encrypt() {
      return this.$store.getters['enums/getEncrypt']
    },
  },
  created() {
    if (this.$route.query.id) {
      this.editType = 'edit'
      this.getSignalById(this.$route.query.id)
    } else {
      this.editType = 'new'
      this.editSignal = {
        id: '',
        assetType: '0',
        isEncrypt: '0',
        description: '',
        signalType: '0',
        signalValueType: '0',
        intervalSize: '',
        intervalCount: '',
        ttl: '',
        ttlType: '1',
        ttlUnit: 'S',
        name: '',
        unit: '',
        unitSymbol: '',
        subSignalIdList: [],
        structureId: '',
        isShow: '1',
        active: '1',
        constraints: {
          enumValues: [],
          max: '',
          symbol: '',
          maxLength: '',
          min: '',
        },
        classification: '',
      }
      this.$store.dispatch('global/searchSignal')
    }

    // Promise.all([this.loadSubSignals(), this.loadStructureInfo()])
  },
  methods: {
    // async loadSubSignals() {
    //   this.childSignals = await this.$store.dispatch('global/loadSubSignals')
    // },
    // async loadStructureInfo() {
    //   this.structuredSignals = await this.$store.dispatch(
    //     'global/loadStructureInfo',
    //   )
    // },
    showInput() {
      this.showAddEnumValues = true
    },
    handleAddEnumVal() {
      if (this.enumVal) {
        if (this.editSignal.constraints.enumValues.includes(this.enumVal)) {
          // this.enumVal = ''
          this.$notify.info('error', this.$t('signal.valueAlready'))

          return
        }
        this.editSignal.constraints.enumValues.push(this.enumVal)
        this.enumVal = ''
      }

      this.showAddEnumValues = false
    },
    handleCloseEnumVal(index) {
      this.editSignal.constraints.enumValues.splice(index, 1)
    },

    async getSignalById(id) {
      this.detailLoading = true
      try {
        const res = await Promise.all([
          digitalTwinDetails({
            id,
          }),
          this.$store.dispatch('global/searchSignal'),
        ])

        // res[0].data.content[0]
        this.editSignal = res[0].data
        // const selectValueType = this.signalValueTypeList.find(
        //   v => v.key === this.editSignal.signalValueType,
        // )
        const second = formatSeconds(this.editSignal.ttl)
        this.editSignal.ttl = second?.num
        this.editSignal.ttlUnit = second?.type
        // this.editSignal.signalValueType = selectValueType?.value

        // this.editSignal.subSignalIdList =
        //   this.editSignal.subSignalIdList?.split(',')
        if (this.editSignal.signalValueType === '1') {
          this.booleanEnum = this.editSignal.constrainsObject
        }

        // if (['3', '2'].includes(this.editSignal.signalValueType)) {
        //   this.editSignal.constraints = this.editSignal.constrainsObject
        // }
        this.editSignal.constraints = this.editSignal.constrainsObject

        // this.enumVal = this.editSignal.constrainsList
        // this.editSignal.constraints.enumValues = this.editSignal.constrainsList

        // this.editSignal.constraints ? '' : (this.editSignal.constraints = { enumValues: [] })
        // if (this.editSignal.signalValueType === '1') {
        //   if (this.editSignal.constraints.enumValues.length >= 2) {
        //     this.booleanEnum = {
        //       true: this.editSignal.constraints.enumValues[1],
        //       false: this.editSignal.constraints.enumValues[0],
        //     }
        //   }
        // }
      } catch (e) {
        console.error(`获取数字孪生信号管理：${e}`)
      } finally {
        this.detailLoading = false
      }
    },
    //同步
    async cloudSignal() {
      this.confirmLoading = true
      try {
        const res = await alterTableField({ id: this.$route.query.id })
        if (res.code === 200) {
          this.$notify.info('success', this.$t('cloud.swal.hint'))
        }
      } catch (e) {
        console.error(`同步失败：${e}`)
      } finally {
        this.confirmLoading = false
      }
    },
    // 确认编辑信号
    async confirmEditSignal() {
      const bool = this.$refs.form.validate()
      if (!bool) return
      if (!this.editSignal.description) {
        this.editSignal.description = this.editSignal.name
      }
      if (this.editSignal.signalValueType === '1') {
        // this.editSignal.constraints.enumValues = [this.booleanEnum.false, this.booleanEnum.true]
        this.editSignal.constrainsObject = this.booleanEnum
      }
      let allSecond = 0
      if (this.editSignal.signalType === '0') {
        if (this.editSignal.ttl && this.editSignal.ttlType == '2') {
          allSecond = toSeconds(this.editSignal.ttl, this.editSignal.ttlUnit)
        }
      } else if (this.editSignal.signalType === '1') {
        allSecond =
          Number(this.editSignal.intervalCount) *
          Number(this.editSignal.intervalSize)
      }
      if (allSecond > 1296000) {
        return this.$notify.info('error', this.$t('signal.ttlTip'))
      }
      if (['3', '2', '0'].includes(this.editSignal.signalValueType)) {
        this.editSignal.constrainsObject = this.editSignal.constraints
        this.editSignal.constrainsObject.max =
          this.editSignal.constraints.max &&
          Number(this.editSignal.constraints.max)
        this.editSignal.constrainsObject.min =
          this.editSignal.constraints.min &&
          Number(this.editSignal.constraints.min)
      }

      const form = cloneDeep(this.editSignal)

      // if (this.editSignal.subSignalIdList.length > 0) {
      //   form.subSignalIdList = this.editSignal.subSignalIdList.join(',')
      // }
      // form.signalValueType = signalValueTypeList.find(
      //   v => v.value === this.editSignal.signalValueType,
      // )?.key
      this.confirmLoading = true

      delete form.constraints
      if (form.ttl && this.editSignal.ttlType == '2') {
        form.ttl = toSeconds(this.editSignal.ttl, this.editSignal.ttlUnit)
      }
      if (this.editSignal.ttlType === '0') {
        form.ttl = '0'
      }
      if (this.editSignal.ttlType === '1') {
        form.ttl = '-1'
      }
      try {
        if (this.editType === 'new') {
          const res = await saveDigitalTwin(form)
          if (res.code === 200) {
            this.$notify.info(
              'success',
              this.$t('global.hint.add', [this.$t('signal.currentTitle')]),
            )
            this.$router.go(-1)
          }
        } else if (this.editType === 'edit') {
          const res = await updateDigitalWin(form)
          if (res.code === 200) {
            this.$notify.info(
              'success',
              this.$t('global.hint.edit', [this.$t('signal.currentTitle')]),
            )
            this.$router.go(-1)
          }
        }
        this.$store.dispatch('global/searchSignal', { isReset: true })
      } catch (e) {
        console.error(`编辑信号错误：${e}`)
      }
      this.confirmLoading = false

      // try {
      // } catch (e) {
      //   console.error(`编辑信号错误：${e}`)
      // }
    },

    signalTypeChange(e) {
      if (e === '0') {
        this.editSignal.intervalSize = ''
        this.editSignal.intervalCount = ''
        this.editSignal.signalValueType = ''
        this.editSignal.isEncrypt = '0'
        this.editSignal.dataType = '0'
      } else if (e === '1') {
        this.editSignal.constraints = {
          enumValues: [],
          max: '',
          maxLength: '',
          min: '',
        }

        this.editSignal.signalValueType = '8'
        this.editSignal.isEncrypt = ''
        this.editSignal.dataType = ''
        this.editSignal.ttl = ''
        this.editSignal.ttlType = '1'
        this.editSignal.intervalSize = 3600
        this.editSignal.intervalCount = '24'
      }
    },

    // 资产类型切换更新这两个值
    assetTypeChange() {
      this.editSignal.subSignalIdList = []
      this.editSignal.structureId = ''
    },

    signalValueTypeChange(e) {
      // console.log(this.editSignal.signalValueType)
      this.editSignal.constraints = {
        enumValues: [],
      }
      if (e === '1') {
        this.editSignal.constraints = {
          enumValues: ['', ''],
        }
      }
    },

    clearChip(arr, index) {
      arr.splice(index, 1)
    },
  },
}
</script>

<style lang="scss" scoped>
// ::v-deep .v-btn.v-btn--disabled {
//   color: rgba(94, 86, 105, 1) !important;
// }
</style>
