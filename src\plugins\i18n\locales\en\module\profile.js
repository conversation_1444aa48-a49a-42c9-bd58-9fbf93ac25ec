const profile = {
  currentTitle: 'Profile',
  headers: {
    signalName: 'Profile name',
    dataType: 'Data type',
    featureType: 'Feature type',
    assertCount: 'Total assets',
    differentNum: 'Distinct values',
    cronExpression: 'Execution cycle',
    lastStudy: 'Last learning process',
    active: 'Activity',
    dataRangeUnit: 'Data Range',
    rangeValue: 'Range value',
    day: 'Day',
    totalSample: 'Total sample',
    vehicleModels: 'Model Range',
    timeRange: 'Time Range',
    unlimited: 'Unlimited',
  },
  lastDay: 'Last day',
  lastDay1: 'Last {0} days',
  historyHeaders: {
    id: 'ID',
    signalName: 'Signal Name',
    analyseStatus: 'Analyze Status',
    startDate: 'Start Time',
    endDate: 'End Time',
    remark: 'Remark',
    proportion: 'Proportion',
    count: 'Count',
    uniqueVin: 'Unique Vin',
    categorical: 'Categorical',
    numeric: 'Numeric',
    model: 'Model',
    emptyData: 'Filter empty data',
  },
  btn: {
    add: 'Add Profile',
    edit: 'Edit Profile',
    detail: 'Profile Detail',
    history: 'View History',
  },
  hint: {
    tip: 'In this section, you can observe the value distribution of each selected signal in the digital twin. For each configuration file, you can define unexpected values that will trigger exceptions in a given asset.',
    tip1: 'Please specify the minimum percentile of assets in total assets',
    tip2: 'This minimum percentile will be considered as expected by the configuration file',
    tip3: 'Any signal value below this threshold will trigger an "unexpected value" exception',
    tip4: 'Maximum length is 100',
  },
  detail: {
    signal: 'Select signal',
    thresholdSetting: 'Anomaly threshold',
    cronExpression: 'Cron expression',
    profile: 'Profile list',
    unexpectedvalue: 'Unexpected value',
    expectedvalue: 'Expected value',
    automatic: 'Automatic',
    thresholdbaseline: 'Threshold baseline',
    value: 'Value',
    classified: 'Classified as',
    Assets: 'Assets',
    tip1: 'This section introduces the values of signals in the queue',
    tip2: 'Display "unexpected values" as anomalies by analyzing potential outliers based on a determined baseline',
    tip3: 'Then, the team can decide whether to execute the disposal or allow the activity',
    tip4: 'And choose how to manage this detected value in the future',
  },
  table: {
    tip1: 'Values observed in vehicle communication',
    tip2: 'Will determine whether the collected signal data values are "unexpected value" or "expected value"',
    tip3: 'Note: You can manually define classification values to determine if the values are "expected"/"abnormal"',
    tip4: "Alternatively, keep the defined threshold consistent with the platform's recommendation ('automatic')",
    tip5: 'Enumerated values: 1. Unexpected value/2. Expected value/3. Unexpected value (Auto)/4. Expected value (Auto)',
    tip6: 'Recommended value classification: Values are classified by the backend through defined thresholds or machine learning',
    tip7: 'The number of observed values reported under a given classification or grouping',
  },
  notity: {
    tip1: 'Please select the signal first',
    tip2: 'Value already exists',
  },
  delete: {
    title: 'Delete Value (Staging Operation)',
    text: 'Are you sure to delete the value:{0}?',
    tip: 'Removed from staging table',
  },
  detele1: {
    title: 'Delete Profile',
    text: 'Are you sure to delete the profile:{0}?',
  },
}
export default profile
