const dict = {
  currentTitle: '字典类型',
  currentTitle1: '字典数据',
  headers: {
    dictId: '字典主键',
    dictName: '字典名称',
    dictEnName: '字典类型',
    dictDataCount: '字典数量',
    status: '状态',
    remark: '备注',
    isDefault: '系统内置',
  },
  headers1: {
    dictType: '字典类型', //字典类型id
    dictName: '字典标签(中文)', //字典标签(中文)
    dictName1: '字典标签', //字典标签(中文)
    dictEnName: '字典标签(英文)', //字典标签(英文)
    dictId: '字典键值', //字典键值
    sort: '字典排序', //排序
    status: '状态', //状态（0正常 1停用）
    isDefault: '系统默认', //系统默认（0是 1否）
    remark: '备注', //
  },
  tip: '数据存储中的key值',
  tip1: '该字典类型存在关联的字典数据',
  tip2: '请选择字典类型',
  del: {
    title: '删除字典类型',
    text: '确认删除字典类型：{0}？',
  },
  del1: {
    title: '删除字典数据',
    text: '确认删除字典数据：{0}？',
  },
}
export default dict
