const alert = {
  currentTitle: 'Alert',
  headers: {
    severity: 'Severity',
    id: 'Alert ID',
    name: 'Alert Name',
    type: 'Alert Type',
    description: 'Description',
    asset: 'Affected asset',
    vin: 'VIN',
    deviceType: 'Device Type',
    model: 'Car companies',
    status: 'Status',
    tag: 'Alert Tags',
    triggered: 'Triggered on',
    update: 'Last update',
    location: 'Location',
    year: 'Vehicle Year',
    engine: 'Engine Type',
    unhandledAlertStatus: 'Unhandled Alert Status',
    handledAlertStatus: 'Handled Alert Status',
    extent: 'Alert Extent',
    assertId: 'Asset ID',
    occurrenceTime: 'Occurrence Time',
    suppressionFrequency: 'Suppression',
  },
  btn: {
    status: 'Update Status',
    status1: 'Update',
    more: 'More',
    advanced: 'Advanced investigation',
    ticket: 'New ticket',
    dispose: 'View dispose',
    export1: 'Selected Export',
    export2: 'Time Period Export',
    changeStatus: 'Status change',
    detection: 'View detection',
  },
  hint: {
    tip: 'The time range cannot exceed 60 days!',
    update: 'Update successfully！',
    updateTip: '{0} alarm messages will be updated',
    updateTip1:
      'The alarm information with alarm number 【{0}】 will be updated',
    detailTip: 'Time based on messages received by the platform',
    detailTip1: 'Updated on {0}',
    tip: 'Click to create query value',
  },
  detail: {
    last6Months: 'Last 6 Months',
    last24Hours: 'Last 24 Hours',
    occurrences: 'Occurrences',
    assets: 'Affected assets',
    info: 'Asset Info',
    posture: 'Posture',
    alerts: 'Unhandled alerts',
    firstTime: 'First seen',
    lastTime: 'Last seen',
    lastLocation: 'Last location',
    startTime: 'Start Time',
    endTime: 'End Time',
    timeLine: 'Asset Timeline',
    table: 'Asset Table',
    mapBox: {
      tips1: 'Not Found In Search“ {0} ”Related Content',
      tips2: 'Please Try Another Search',
      tips3: 'N/A -- Address unresolved',
    },
  },
}

export default alert
