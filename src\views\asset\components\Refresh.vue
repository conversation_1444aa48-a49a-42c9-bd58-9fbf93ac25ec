<template>
  <v-row justify="end" class="d-flex align-center px-0 pt-1">
    <v-col cols="12" class="d-flex justify-end w-100">
      <div class="d-flex align-center">
        <div class="d-flex justify-end w-100">
          <!-- v-if="!refreshFrequency" -->
          <div class="d-flex align-center justify-end">
            <v-btn icon>
              <v-icon size="1.5rem"> mdi-filter-variant </v-icon>
            </v-btn>
          </div>
          <div class="d-flex align-center">
            <vsoc-date-range
              :value="value"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              align="right"
              :show-suffix-icon="false"
              :min-scope="0"
              :picker-options="pickerOptions"
              @change="dateChange"
            >
            </vsoc-date-range>
          </div>

          <div class="d-flex align-center pl-1">
            <v-tooltip bottom open-delay="300">
              <template v-slot:activator="{ on, attrs }">
                <v-btn icon>
                  <!-- class="icon-hover-bg-round" -->

                  <v-icon
                    size="1.25rem"
                    v-bind="attrs"
                    @click="refresh"
                    @mouseenter="$_setTips"
                    v-on="on"
                  >
                    mdi-refresh
                  </v-icon>
                </v-btn>
              </template>
              <span>{{ showTips }}</span>
            </v-tooltip>
          </div>
        </div>
      </div>
    </v-col>
  </v-row>
</template>

<script>
import VsocDateRange from '@/components/VsocDateRange.vue'
import { getDateDiff, toDate } from '@/util/filters'

export default {
  name: 'Refresh',
  components: {
    VsocDateRange,
  },
  props: {
    value: Array,
    page: Number,
    refreshDate: Number,
  },
  data() {
    return {
      refreshInterval: null,
      refreshFrequencyList: [0, 2, 5, 30, 60],
      refreshFrequencyTimeVal: 1,
      refreshFrequencyTime: [
        {
          text: '最近10分钟',
          value: 1,
        },
        {
          text: '最近30分钟',
          value: 2,
        },
        {
          text: '最近1小时',
          value: 3,
        },
        {
          text: '最近2小时',
          value: 4,
        },
        {
          text: '最近1天',
          value: 5,
        },
      ],
      refreshFrequency: 0,
      showTips: '',
      pickerOptions: {
        shortcuts: [
          {
            text: '最近7天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 1000 * 60 * 60 * 24 * 7)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '最近15天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 1000 * 60 * 60 * 24 * 15)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '最近30天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 1000 * 60 * 60 * 24 * 30)
              picker.$emit('pick', [start, end])
            },
          },
        ],
      },
    }
  },
  watch: {
    refreshFrequency() {
      clearInterval(this.refreshInterval)
      this.createInterval()
    },
  },
  created() {},
  methods: {
    // 创建定时任务
    createInterval() {
      if (this.refreshFrequency === 0) return
      this.refreshInterval = setInterval(() => {
        this.refreshEvent()
      }, this.refreshFrequency * 1000)
    },

    changeRefreshFrequency(item) {
      this.refreshFrequency = item
    },
    refreshEvent() {
      this.$emit('input', this.handleTime())
      this.$emit('refresh')
    },

    handleTime() {
      const start = new Date(this.value[0])
      const end = new Date(this.value[1])
      const reduce = end.getTime() - start.getTime()
      const newDate =
        new Date(this.value[1]).getTime() + this.refreshFrequency * 1000

      return [
        toDate(newDate - reduce, 'yyyy-MM-dd hh:mm:ss'),
        toDate(newDate, 'yyyy-MM-dd hh:mm:ss'),
      ]
    },

    // 点击刷新，重启定时
    refresh() {
      clearInterval(this.refreshInterval)
      this.$emit('refresh')
      this.createInterval()
    },

    // 更新时间
    dateChange(e) {
      this.$emit('input', e)
      this.refresh()
    },

    $_setTips() {
      this.showTips = this.$t('global.refreshDiff', {
        diff: getDateDiff(this.refreshDate),
      })
    },

    changeRefreshTime(item) {
      this.refreshFrequencyTimeVal = item.value
    },
  },
}
</script>

<style scoped></style>
