<!--  -->
<template>
  <vsoc-dialog
    custom-class="select-email-dialog"
    :value="value"
    :max-height="600"
    @input="$emit('input', $event)"
    @click:confirm="confirm"
  >
    <template #title>
      <div class="font-weight-semibold text-h6 w-50">
        {{ `选择接收人(${selectedData.length})` }}
      </div>
    </template>
    <v-data-table
      ref="editEmail"
      fixed-header
      height="400"
      :items-per-page="query.pageSize"
      item-key="id"
      show-select
      hide-default-footer
      :headers="headers"
      :items="tableData"
      class="table v-data-table--select"
      :loading="tableDataLoading"
      @item-selected="$_tableSelected"
      @toggle-select-all="$_tableSelected"
      @click:row="$_clickRow"
    >
    </v-data-table>
  </vsoc-dialog>
</template>

<script>
import VsocDialog from '@/components/VsocDialog.vue'
import { PAGESIZE_MAX } from '@/util/constant'
import { deepClone } from '@/util/throttle'

export default {
  components: {
    VsocDialog,
  },
  props: {
    value: Boolean,
    tableData: Array,
  },
  data() {
    return {
      query: {
        pageNum: 1,
        pageSize: PAGESIZE_MAX,
      },
      headers: [
        {
          text: '用户ID',
          value: 'id',
        },
        {
          text: '用户名',
          value: 'name',
        },
        {
          text: '邮箱',
          value: 'email',
        },
        {
          text: '角色',
          value: 'role',
        },
      ],
      tableDataTotal: 0,
      tableDataLoading: false,
      selectedData: [],
    }
  },
  created() {},
  methods: {
    confirm(cb) {
      cb()
      this.$emit('confirm', deepClone(this.selectedData))
    },

    // 表格选择事件
    $_tableSelected({ item, value, items }) {
      if (items) {
        this.selectedData = value ? deepClone(items) : []
      } else if (value) {
        this.selectedData.push(item)
      } else {
        const index = this.selectedData.findIndex(v => v.id === item.id)
        this.selectedData.splice(index, 1)
      }
    },

    setSelecteds(array) {
      this.clearTableSelected()
      if (!array) return
      array.forEach(item => {
        this.$refs.editEmail.select(item, true)
      })
    },

    $_clickRow(item, e) {
      e.select(!e.isSelected)
    },

    // 清除表格当前选择项
    clearTableSelected() {
      if (this.$refs.editEmail) {
        this.$refs.editEmail.toggleSelectAll(false)
      }
      this.selectedData = []
    },
  },
}
</script>

<style lang="scss">
.select-email-dialog {
  .vsoc-dialog__title {
    border-bottom: 0px solid rgba(0, 0, 0, 0.05) !important;
  }
  .vsoc-dialog__content {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
}
</style>
