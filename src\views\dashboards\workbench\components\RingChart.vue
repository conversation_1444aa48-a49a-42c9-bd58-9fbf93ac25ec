<template>
  <common :title="title" v-on="$listeners" type="ring">
    <template #subTitle>
      {{ total || 0 | numberToFormat }}
    </template>
    <template #chart>
      <vsoc-chart
        ref="echart"
        :echart-id="echartId"
        :option="chartOption"
        @highlight="onHighlight"
        @finished="onFinished"
        @mouseout="onMouseout"
      ></vsoc-chart>
    </template>
  </common>
</template>

<script>
import VsocChart from '@/components/VsocChart.vue'
import store from '@/store'
import { numberToFormat } from '@/util/filters'
import themeConfig from '@themeConfig'
import { orderBy, sumBy } from 'lodash'
import { computed, ref, watch } from 'vue-demi'
import Common from './Common.vue'
export default {
  components: {
    VsocChart,
    Common,
  },
  props: {
    title: {
      type: String,
    },
    list: {
      type: Array,
      default: () => [],
      // [
      //   { name: '低', value: 2, color: '#3caea3', format: '0.31%' },
      //   { name: '严重', value: 481, color: '#FF4C51', format: '75.51%' },
      //   { name: '中', value: 98, color: '#EEC900', format: '15.38%' },
      //   { name: '高', value: 49, color: '#fb8c00', format: '7.69%' },
      //   { name: '信息', value: 7, color: '#7b809a', format: '1.10%' },
      // ],
    },
    echartId: {
      type: String,
      default: () => 'echart',
    },
    padding: {
      type: [Number, Array],
      default: () => [10, 0],
    },
    formatter: {
      type: Function,
      default(params) {
        return `\n{value|${params.data.format}}\n{name|${params.name}}`
      },
    },
  },
  setup(props, ctx) {
    const alarmStatusEnum = computed(() => {
      return store.state.enums.enums.AlarmStatus
    })
    let selected = {}
    let myChart = ''
    let dataIndex = 0

    let rich = {
      name: {
        fontSize: '1.125rem',
        fontWeight: 400,
        padding: props.padding,
        color: themeConfig.themes.light.secondary,
      },
      value: {
        lineHeight: 28,
        fontSize: '2rem',
        fontWeight: 600,
        padding: props.padding,
        verticalAlign: 'bottom',
      },
    }
    let emphasis = {
      show: true,
      scale: false,
      // scaleSize: 1.5,
      label: {
        // formatter: props.formatter,
        formatter: `\n{value|{d}%}\n{name|{b}}`,
      },
    }
    let label = {
      show: true,
      position: 'center',
      lineHeight: 16,
      formatter: '',
      // 文字超出省略
      width: 100,
      overflow: 'truncate',
    }
    // let itemStyle = {
    //   borderWidth: 0.5,
    //   borderColor: '#fff',
    // }
    let chartOption = ref({
      tooltip: {
        trigger: 'item',
        // formatter: '{b}: {d}%',
        formatter: `{b}:\t{c}({d}%)`,
        // params => {
        //   return `${params.name}:\t${numberToFormat(params.data.value)}(${
        //     params.data.format
        //   })`
        // },
        backgroundColor: '',
        padding: [6, 10],
        textStyle: {
          color: '#fff',
        },
      },

      legend: {
        type: 'scroll',
        orient: 'vertical',
        // bottom: '4%',
        top: 'center',
        left: '68%',
        // right: '10%',
        show: true,
        icon: 'circle',
        itemHeight: 10, // 修改icon图形大小
        itemGap: 24,
        textStyle: {
          // width: getRoundSize(170),
          // overflow: 'truncate',
          padding: [0, 0, 0, -4], // 修改文字和图标距离
        },
        selectedMode: false,
      },

      series: [
        {
          name: '',
          type: 'pie',
          radius: ['60%', '85%'],
          center: ['35%', '60%'],
          avoidLabelOverlap: true,
          percentPrecision: 0,
          minAngle: 20,
          stillShowZeroSum: true,
          bottom: 40,
          top: 0,
          label: {},
          labelLine: {
            show: true,
          },
          data: props.list,
        },
      ],
    })

    const onHighlight = obj => {
      selected = Object.assign(obj.data, { color: obj.color })
      if (dataIndex !== obj.dataIndex) {
        // 取消前一条高亮
        myChart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: dataIndex,
        })
        dataIndex = obj.dataIndex
      }
      chartOption.value.tooltip.backgroundColor = obj.color
      chartOption.value.series[0].emphasis.scale = true
      myChart.setOption(chartOption.value)
    }

    const onMouseout = event => {
      // 取消动画
      chartOption.value.series[0].emphasis.scale = false
      myChart.setOption(chartOption.value)
    }

    const onFinished = chart => {
      myChart = chart
      // 默认第一条数据高亮
      myChart.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: dataIndex,
      })
      resetColor()
    }

    const resetColor = () => {
      rich.name.backgroundColor = ''
      rich.value.backgroundColor = ''

      Object.assign(label, { rich: rich })
      Object.assign(emphasis, { rich: rich })
      Object.assign(
        chartOption.value.series[0],
        { label: label },
        // { itemStyle: itemStyle },
        { emphasis: emphasis },
      )
    }

    const colorList = [
      '#313CA6',
      '#0082D6',
      '#55D1FD',
      '#3D4A66',
      '#8F9AB2',
      '#B4BBCC',
    ]
    let total = ref(0)
    watch(
      () => props.list,
      newArr => {
        const sortArr = orderBy(newArr, ['value'], ['desc']).map(
          (item, index) => {
            return {
              ...item,
              color: colorList[index],
            }
          },
        )
        // 总数
        total.value = sumBy(sortArr, v => v.value)
        chartOption.value.series[0].data = sortArr
        selected = sortArr[0]
        chartOption.value.color = sortArr.map(v => v.color)
        if (myChart) {
          // 取消前一条高亮
          myChart.dispatchAction({
            type: 'downplay',
            seriesIndex: 0,
            dataIndex: dataIndex,
          })
          dataIndex = 0
        }
      },
      { immediate: true },
    )
    return {
      total,
      chartOption,
      onHighlight,
      onFinished,
      onMouseout,
    }
  },
}
</script>
