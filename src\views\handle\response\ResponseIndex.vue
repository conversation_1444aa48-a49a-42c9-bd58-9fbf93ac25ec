<template>
  <div>
    <bread-crumb class="mb-3"></bread-crumb>

    <v-card tile class="main-content">
      <v-card-text class="pa-0">
        <div class="d-flex justify-space-between align-center flex-row-reverse">
          <div class="d-flex align-center">
            <table-search
              :searchList="searchList"
              :searchQuery="query"
              @search="$_search"
            ></table-search>
            <!-- <v-text-field
              v-model="query.responseName"
              color="primary"
              hide-details
              :label="$t('response.headers.responseName')"
              dense
              outlined
              class="me-3 text-width"
              clearable
              @click:clear="onClear"
              @keyup.enter.native="$_search"
            ></v-text-field>
            <v-btn color="primary--text bg-btn" elevation="0" @click="$_search">
              <span>
                {{ $t('action.search') }}
              </span>
            </v-btn> -->
          </div>
          <div class="d-flex justify-end align-center">
            <v-btn
              elevation="0"
              color="primary"
              v-has:response-new
              @click="addList"
            >
              <span> {{ $t('action.add') }} </span>
            </v-btn>
          </div>
        </div>

        <v-data-table
          ref="collectTable"
          fixed-header
          :items-per-page="999"
          item-key="id"
          :height="tableHeight"
          hide-default-footer
          :headers="headers"
          :items="tableData"
          class="table border-radius-xl mt-3 thead-light pb-5"
          :loading="tableDataLoading"
          id="tableRowDrop"
        >
          <!-- <template
            v-for="item in headers"
            v-slot:[`header.${item.value}`]="{ header }"
          >
            <div :key="item.value">{{ $t(header.text) }}</div>
          </template> -->
          <template v-slot:body="props">
            <draggable
              :list="props.items"
              tag="tbody"
              handle=".move"
              animation="300"
              @end="eve => ondragend(eve, props.items)"
            >
              <tr
                v-for="(item, index) in props.items"
                :key="index"
                chosenClass="chosen"
              >
                <td class="move" style="cursor: all-scroll">
                  <vsoc-icon
                    type="fill"
                    icon="icon-move-full"
                    class="action-btn"
                    size="16"
                  ></vsoc-icon>
                </td>
                <td>{{ item.responseName }}</td>
                <td>{{ item.responseEventName }}</td>
                <td>
                  <v-badge
                    dot
                    inline
                    offset-x="10"
                    offset-y="-18"
                    :color="
                      responseStatusList[item.status]
                        ? responseStatusList[item.status].color
                        : ''
                    "
                    class="mr-1"
                  ></v-badge>
                  {{ responseStatus[item.status].text }}
                </td>
                <td>{{ item.updateUser }}</td>
                <td>{{ item.updateDate }}</td>
                <!-- <td>{{ item.createUser }}</td>
                <td>{{ item.createDate }}</td> -->
                <td>
                  <v-btn icon @click="onInfo(item)" v-has:response-detail>
                    <vsoc-icon
                      v-show-tips="$t('action.detail')"
                      type="fill"
                      icon="icon-xiangqing"
                      class="action-btn"
                      size="x-large"
                    ></vsoc-icon>
                  </v-btn>

                  <v-btn
                    v-if="item.status === '0'"
                    icon
                    v-has:response-disable
                    @click="handleStatus(item)"
                  >
                    <vsoc-icon
                      v-show-tips="responseStatus['1'].text"
                      type="fill"
                      :icon="'icon-tingyong'"
                      class="action-btn"
                      size="x-large"
                    ></vsoc-icon>
                  </v-btn>

                  <v-btn
                    v-if="item.status === '1'"
                    icon
                    v-has:response-enable
                    @click="handleStatus(item)"
                  >
                    <vsoc-icon
                      v-show-tips="responseStatus['0'].text"
                      type="fill"
                      :icon="'icon-qiyong'"
                      class="action-btn"
                      size="x-large"
                    ></vsoc-icon>
                  </v-btn>

                  <v-btn icon @click="editList(item)" v-has:response-edit>
                    <vsoc-icon
                      v-show-tips="$t('action.edit')"
                      type="fill"
                      icon="icon-bianji"
                      class="action-btn"
                      size="x-large"
                    ></vsoc-icon>
                  </v-btn>
                  <v-btn icon @click="delList(item)" v-has:response-del>
                    <vsoc-icon
                      type="fill"
                      class="action-btn"
                      size="x-large"
                      v-show-tips="$t('action.del')"
                      icon="icon-shanchu"
                    ></vsoc-icon>
                  </v-btn>
                </td>
              </tr>
            </draggable>
          </template>
        </v-data-table>
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
import {
  delResponse,
  getLinkages,
  linkageSort,
  updateLinkageStatus,
} from '@/api/response/index'
import VsocPagination from '@/components/VsocPagination.vue'
import breadCrumb from '@/components/bread-crumb/index'
import { responseStatusList } from '@/util/enum'
import { dataFilter } from '@/util/filters'
import { deepClone } from '@/util/throttle'
import { setRemainingHeight } from '@/util/utils'
import draggable from 'vuedraggable'
import TableSearch from '@/components/TableSearch/index.vue'

export default {
  name: 'ResponseIndex',
  components: {
    VsocPagination,
    breadCrumb,
    draggable,
    TableSearch,
  },
  filters: {
    dataFilter,
  },
  data() {
    return {
      responseStatusList,
      rowSort: null,
      tableDataLoading: false,

      // 分页参数
      query: {
        responseName: '',
      },
      tableHeight: '34.5rem',
      tableData: [],
    }
  },
  computed: {
    searchList() {
      return [
        {
          type: 'input',
          value: 'responseName',
          text: this.$t('response.headers.responseName'),
        },
      ]
    },
    responseStatus() {
      return this.$store.state.enums.enums['Response Status']
    },
    headers() {
      return [
        {
          text: this.$t('response.headers.id'),
          value: 'id',
          width: '80px',
          sortable: false,
        },
        {
          text: this.$t('response.headers.responseName'),
          value: 'responseName',
          width: '260px',
          sortable: true,
        },
        {
          text: this.$t('response.headers.responseEventName'),
          value: 'responseEventName',
          width: '140px',
          sortable: true,
        },

        {
          text: this.$t('response.headers.statusName'),
          value: 'statusName',
          width: '140px',
          sortable: true,
        },
        {
          text: this.$t('global.updateUser'),
          value: 'updateUser',
          sortable: true,
          width: '120px',
        },
        {
          text: this.$t('global.updateDate'),
          value: 'updateDate',
          sortable: true,
          width: '160px',
        },
        // {
        //   text: this.$t('response.headers.createUser'),
        //   value: 'createUser',
        //   sortable: false,
        //   width: '120px',
        // },
        // {
        //   text: this.$t('response.headers.createDate'),
        //   value: 'createDate',
        //   sortable: true,
        //   width: '160px',
        // },
        {
          text: '',
          value: 'actions',
          sortable: false,
          width: '200px',
        },
      ]
    },
  },

  created() {
    this.$_setTableHeight()
    this.$bus.$on('resize', this.$_setTableHeight)
    this.$_search()
  },
  beforeDestroy() {
    this.$bus.$off('resize', this.$_setTableHeight)
  },
  methods: {
    onClear() {
      this.query.responseName = ''
      this.$_search()
    },
    async ondragend(event, list) {
      if (this.$route.meta.buttonInfo['response-sort']) {
        const params = {
          fromId: Number(event.oldDraggableIndex) + 1,
          toId: Number(event.newDraggableIndex) + 1,
        }
        const res = await linkageSort(params)
        if (res.code === 200) {
          this.$_search()
        }
      }
    },
    // 新增
    addList() {
      this.$router.push('/response/add')
    },

    // 修改
    editList(item) {
      this.$router.push(`/response/edit?id=${item.id}`)
    },

    // 详情
    onInfo(item) {
      this.$router.push(`/response/detail?id=${item.id}&isDetail=1`)
    },

    $_search() {
      this.getTableData()
    },

    $_setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = setRemainingHeight() + 17
      })
    },

    // 获取列表
    async getTableData() {
      try {
        // 保存查询参数
        this.tableDataLoading = true
        const data = deepClone(this.query)
        const res = await getLinkages(data)
        this.tableData = res.data || []
      } catch (e) {
        console.error(`获取响应错误：${e}`)
      } finally {
        this.tableDataLoading = false
      }
    },

    handleStatus(item) {
      const text =
        item.status === '0'
          ? this.responseStatus['1'].text
          : this.responseStatus['0'].text
      this.$swal({
        title:
          item.status === '0'
            ? this.$t('response.swal.stop')
            : this.$t('response.swal.start'),
        text: this.$t('response.swal.sure', [text, item.responseName]),
        icon: item.status === '0' ? 'error' : 'success',
        reverseButtons: true,
        showCancelButton: true,
        confirmButtonText: this.$t('action.confirm'),
        cancelButtonText: this.$t('action.cancel'),
        customClass: {
          confirmButton: 'sweet-btn-primary',
        },
      }).then(async result => {
        if (result.isConfirmed) {
          try {
            const res = await updateLinkageStatus({
              id: item.id,
              status: item.status === '0' ? '1' : '0',
            })
            if (res.code === 200) {
              this.$notify.info('success', this.$t('response.swal.tip', [text]))
            }
            this.$_search()
          } catch (e) {
            console.error(text + `错误：${e}`)
          }
        }
      })
    },

    delList(item) {
      if (item.status === '0') {
        return this.$notify.info('info', this.$t('response.swal.delTip'))
      }
      this.$swal({
        title: this.$t('response.btn.del'),
        text: this.$t('response.swal.text', [item.responseName]),
        icon: 'error',
        reverseButtons: true,
        showCancelButton: true,
        confirmButtonText: this.$t('action.confirm'),
        cancelButtonText: this.$t('action.cancel'),
        customClass: {
          confirmButton: 'sweet-btn-primary',
        },
      }).then(async result => {
        if (result.isConfirmed) {
          try {
            const res = await delResponse({ id: item.id })
            if (res.code === 200) {
              this.$notify.info(
                'success',
                this.$t('global.hint.del', {
                  cur: this.$t('response.currentTitle'),
                }),
              )
            }
            this.$_search()
          } catch (e) {
            console.error(`删除错误：${e}`)
          }
        }
      })
    },
  },
}
</script>
<style lang="scss" scoped>
// .chosen {
//   background-color: red !important;
//   color: #fff;
// }
</style>
