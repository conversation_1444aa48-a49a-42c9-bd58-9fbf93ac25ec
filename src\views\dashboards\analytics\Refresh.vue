<template>
  <v-row justify="end" class="d-flex align-center px-0 pt-1">
    <v-col cols="12" class="d-flex justify-end w-100">
      <div class="d-flex align-center">
        <div class="d-flex justify-end w-100">
          <!-- v-if="!refreshFrequency" -->
          <div class="d-flex align-center justify-end">
            <v-btn icon @click="onShowDrawer">
              <vsoc-icon
                size="large"
                type="fill"
                class="text--secondary"
                icon="icon-gaojichaxun"
              >
              </vsoc-icon>
            </v-btn>
            <v-divider></v-divider>
          </div>
          <div class="d-flex align-center">
            <vsoc-date-range
              v-if="modeType === 1"
              v-model="dateRange"
              :timeDisabled="true"
              no-title
              @input="dateChange"
              :presetFn="pickerFn2"
            >
              <template v-slot:text="{ on, attrs }">
                <div v-bind="attrs" v-on="on" class="mx-2 text-content">
                  {{ dateRange.start }} ~ {{ dateRange.end }}
                </div>
              </template>
            </vsoc-date-range>
            <vsoc-date-range
              v-else
              v-model="dateRange"
              no-title
              @input="dateChange"
              :presetFn="currentTab == 1 ? pickerFn2 : pickerFn1"
            >
              <template v-slot:text="{ on, attrs }">
                <v-text-field
                  style="width: 17rem"
                  class="mb-1 font-size-input-sm text--secondary"
                  color="primary"
                  large
                  hide-details
                  dense
                  full-width
                  readonly
                  :value="RANGE_STR(dateRange.start, dateRange.end)"
                  @click:clear="dateChange({ start: '', end: '' })"
                  v-bind="attrs"
                  v-on="on"
                ></v-text-field>
              </template>
            </vsoc-date-range>
          </div>
          <div class="d-flex align-center pl-1">
            <v-tooltip bottom open-delay="300">
              <template v-slot:activator="{ on, attrs }">
                <v-btn
                  icon
                  @click="refresh"
                  @mouseenter="$_setTips"
                  v-bind="attrs"
                  v-on="on"
                >
                  <!-- class="icon-hover-bg-round" -->
                  <vsoc-icon
                    size="large"
                    class="text--secondary"
                    type="fill"
                    icon="icon-shuaxin"
                  >
                  </vsoc-icon>
                </v-btn>
              </template>
              <span>{{ showTips }}</span>
            </v-tooltip>
          </div>
        </div>
      </div>
    </v-col>
  </v-row>
</template>

<script>
import { RANGE_STR } from '@/components/vsoc-date-range/constants'
import VsocDateRange from '@/components/vsoc-date-range/VsocDateRange.vue'
import { toDate } from '@/util/filters'
import { getDateDiff } from '@/util/filters.js'
import {
  differenceInDays,
  endOfDay,
  startOfDay,
  subDays,
  subHours,
} from 'date-fns'

export default {
  name: 'Refresh',
  components: {
    VsocDateRange,
  },
  props: {
    // value: Object,
    page: Number,
    refreshDate: Number,
    currentTab: Number,
    modeType: Number,
  },
  data() {
    return {
      showAdvanceSearch: false,
      dateRange: {
        start: toDate(startOfDay(subDays(new Date(), 6))),
        end: toDate(endOfDay(new Date())),
      },
      refreshInterval: null,
      refreshFrequencyList: [0, 2, 5, 30, 60],
      refreshFrequencyTimeVal: 1,
      refreshFrequencyTime: [
        {
          text: '最近10分钟',
          value: 1,
        },
        {
          text: '最近30分钟',
          value: 2,
        },
        {
          text: '最近1小时',
          value: 3,
        },
        {
          text: '最近2小时',
          value: 4,
        },
        {
          text: '最近1天',
          value: 5,
        },
      ],
      refreshFrequency: 0,
      showTips: '',
      pickerFn1: newDate => {
        return [
          {
            label: '最近7天',
            range: [
              startOfDay(subDays(endOfDay(newDate), 6)),
              endOfDay(newDate),
            ],
          },
          {
            label: '最近30天',
            range: [
              startOfDay(subDays(endOfDay(newDate), 29)),
              endOfDay(newDate),
            ],
          },
          {
            label: '最近60天',
            range: [
              startOfDay(subDays(endOfDay(newDate), 59)),
              endOfDay(newDate),
            ],
          },
          {
            label: '最近90天',
            range: [
              startOfDay(subDays(endOfDay(newDate), 89)),
              endOfDay(newDate),
            ],
          },
        ]
      },
      pickerFn2: newDate => {
        return [
          {
            label: '最近24小时',
            range: [subHours(newDate, 24), newDate],
          },
          {
            label: '最近7天',
            range: [
              startOfDay(subDays(endOfDay(newDate), 6)),
              endOfDay(newDate),
            ],
          },
          {
            label: '最近30天',
            range: [
              startOfDay(subDays(endOfDay(newDate), 29)),
              endOfDay(newDate),
            ],
          },
        ]
      },
    }
  },
  watch: {
    refreshFrequency() {
      clearInterval(this.refreshInterval)
      this.createInterval()
    },
    currentTab() {
      console.log('currentTab')
      this.dateRange = {
        start: toDate(startOfDay(subDays(new Date(), 6))),
        end: toDate(endOfDay(new Date())),
      }
    },
    // value: {
    //   handler(val) {
    //     this.dateRange = val
    //   },
    //   immediate: true,
    // },
  },
  methods: {
    RANGE_STR,
    doQuery() {},
    onShowDrawer() {
      this.$emit('showDrawer')
    },
    // 创建定时任务
    createInterval() {
      if (this.refreshFrequency === 0) return
      this.refreshInterval = setInterval(() => {
        this.refreshEvent()
      }, this.refreshFrequency * 1000)
    },

    changeRefreshFrequency(item) {
      this.refreshFrequency = item
    },
    refreshEvent() {
      this.$emit('input', this.handleTime())
      this.$emit('refresh', this.dateRange)
    },

    handleTime() {
      const start = new Date(this.dateRange.start)
      const end = new Date(this.dateRange.end)
      const reduce = end.getTime() - start.getTime()
      const newDate =
        new Date(this.dateRange.end).getTime() + this.refreshFrequency * 1000

      return [
        toDate(newDate - reduce, 'yyyy-MM-dd hh:mm:ss'),
        toDate(newDate, 'yyyy-MM-dd hh:mm:ss'),
      ]
    },

    // 点击刷新，重启定时
    refresh() {
      clearInterval(this.refreshInterval)
      this.$emit('refresh', this.dateRange)
      this.createInterval()
    },

    // 修改时间
    dateChange(dateObj) {
      if (this.modeType !== 1) {
        const diffDays = differenceInDays(
          new Date(dateObj.end),
          new Date(dateObj.start),
        )
        if (this.currentTab === 0) {
          if (diffDays > 90) {
            this.$notify.info('error', '时间范围不能超过90天')
            Object.assign(
              this.$data.dateRange,
              this.$options.data.call(this).dateRange,
            )
            return
          }
        } else {
          if (diffDays > 30) {
            this.$notify.info('error', '时间范围不能超过30天')
            Object.assign(
              this.$data.dateRange,
              this.$options.data.call(this).dateRange,
            )
            return
          }
        }
      }
      this.$emit('input', this.dateRange)
      this.refresh()
    },

    $_setTips() {
      this.showTips = this.$t('global.refreshDiff', {
        diff: getDateDiff(this.refreshDate),
      })
      // console.log(
      //   toDate(
      //     new Date(this.dateRange.end).getTime() +
      //       (new Date().getTime() - new Date(this.refreshDate).getTime()),
      //     'yyyy-MM-dd hh:mm:ss',
      //   ),
      // )
    },

    changeRefreshTime(item) {
      this.refreshFrequencyTimeVal = item.value
    },
  },
}
</script>

<style scoped lang="scss">
::v-deep .v-input input {
  color: inherit !important;
}
</style>
