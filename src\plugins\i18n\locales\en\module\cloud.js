const cloud = {
  currentTitle: 'Cloud',
  headers: {
    itemKey: 'Configuration',
    itemName: 'Statistical indicators',
    itemEnName: 'Statistical Index English Name',
    staticValue: 'Static Data',
    method: 'Method',
    readMethod: 'Data Source',
    chart: 'Charts',
    initialWidth: 'Initial Width',
    initialHeight: 'Initial Height',
    minWidth: 'Min Width',
    minHeight: 'Min Height',
    status: 'Valid or not',
    border: 'Borders or not',
  },
  static: {
    added: 'Added Item',
    handling: 'Adding Item',
    hint: 'Enter to create a label:',
  },
  swal: {
    title1: 'Synchronize',
    title2: 'Asynchronize',
    title: 'Synchronize updates',
    text: 'This statistical indicator is currently used in {0} canvas instances. Do you want to synchronize and update the data of these canvas instances?',
    hint: 'Synchronization successfully',
  },
  tip: 'Enter integers from 1 to 24',
  exampleHint: 'Click to download sample data',
  empty: 'The content is empty, the download has been canceled!',
}

export default cloud
