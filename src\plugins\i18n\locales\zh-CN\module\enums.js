const enums = {
  active: {
    valid: '有效',
    invalid: '无效',
  },
  encrypt: {
    encryption: '加密',
    unencryption: '不加密',
  },
  datePresets: {
    today: '当天',
    last1: '最近1小时',
    last24: '最近24小时',
    last7: '最近7天',
    last30: '最近30天',
    last15: '最近15天',
    lastxm: '最近{num}个月',
    lastxh: '最近{num}小时',
    lastxd: '最近{num}天',
    all: '所有时段',
    highActivity: '活跃资产',
    moderateActivity: '一般活跃',
    lowActivity: '沉寂资产',
  },
  operateStatus: {
    success: '成功',
    error: '失败',
  },
  softwareType: {
    application: '应用程序',
    system: '操作系统',
    equipment: '硬件设备',
  },
  reference: {
    yes: '包含',
    no: '不包含',
  },
}

export default enums
