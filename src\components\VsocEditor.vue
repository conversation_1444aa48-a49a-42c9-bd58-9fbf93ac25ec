<template>
  <!-- 富文本编辑器 -->
  <div ref="editor"></div>
</template>

<script>
import Editor from '@toast-ui/editor'
import '@toast-ui/editor/dist/toastui-editor.css'
import '@toast-ui/editor/dist/i18n/zh-cn'
export default {
  name: 'VsocEditor',
  props: {
    isDark: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      editor: '',
      content: '',
      height: '',
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.$nextTick(() => {
        this.height =
          this.$refs.editor.parentElement.parentElement.parentElement.offsetHeight
        this.editor = new Editor({
          el: this.$refs.editor,
          previewStyle: 'tab',
          language: 'zh-CN',
          height: this.height + 'px',
          initialEditType: 'wysiwyg',
          theme: this.isDark,
          hideModeSwitch: true,
          toolbarItems: [
            ['heading', 'bold', 'italic'],
            ['hr', 'quote'],
            ['ul', 'indent', 'outdent'],
            // ['table', 'image', 'link'],
            ['table', 'link'],
            ['code', 'codeblock'],
            // Using Option: Customize the last button
          ],
          mode: false,
        })
        this.$emit('ready', this.editor)
      })
    },
    getMarkdown() {
      return this.editor.getMarkdown()
    },
    // insertText(value) {
    //   this.editor.setMarkdown(value)
    // },
    setMarkdown(value) {
      this.editor.setMarkdown(value)
    },
  },
}
</script>

<style scoped lang="scss">
// ::v-deep .toastui-editor-mode-switch {
//   display: none !important;
// }
::v-deep {
  .toastui-editor-contents {
    font-size: 16px !important;
    color: var(--v-color-base) !important;
    p,
    strong,
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      font-size: 16px !important;
      color: var(--v-color-base) !important;
    }
  }
}
</style>
