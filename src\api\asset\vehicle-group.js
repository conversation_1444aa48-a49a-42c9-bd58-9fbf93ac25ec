import { request, vsocPath } from '../../util/request'

// 根据资产组id添加资产
export const addRelationByGroupId = function (data) {
  return request({
    url: `${vsocPath}/assetGroupRelation/addRelationByGroupId`,
    method: 'post',
    data,
  })
}

// 根据资产id添加资产组
export const addRelationByAssetId = function (data) {
  return request({
    url: `${vsocPath}/assetGroupRelation/addRelationByAssetId`,
    method: 'post',
    data,
  })
}

// 查询资产id个数接口
export const getAssetCountById = function (data) {
  return request({
    url: `${vsocPath}/vehicleGroup/selectAssetCountById/` + data,
    method: 'post',
    // params: data,
  })
}

// 校验name唯一接口
export const getVehicleGroupName = function (data) {
  return request({
    url: `${vsocPath}/vehicleGroup/findVehicleGroupName`,
    method: 'post',
    data,
  })
}

// 查询所有资产组
export const queryAllVehicleGroup = function (data) {
  return request({
    url: `${vsocPath}/vehicleGroup/queryAllVehicleGroup`,
    method: 'post',
    data,
  })
}

// 资产组查询接口
export const groups = function (data) {
  return request({
    url: `${vsocPath}/vehicleGroup/vehicleGroups`,
    method: 'post',
    data,
  })
}

// 资产组添加接口
export const addVehicleGroup = function (data) {
  return request({
    url: `${vsocPath}/vehicleGroup/addVehicleGroup`,
    method: 'post',
    data,
  })
}

// 资产组修改接口
export const updateVehicleGroup = function (data) {
  return request({
    url: `${vsocPath}/vehicleGroup/updateVehicleGroup`,
    method: 'post',
    data,
  })
}

// 资产组删除接口
export const delVehicleGroup = function (data) {
  return request({
    url: `${vsocPath}/vehicleGroup/delVehicleGroup`,
    method: 'post',
    data,
  })
}
