<template>
  <div
    class="app-content-container mx-auto h-full overflow-auto"
    :class="{ 'app-content-container-boxed': appContentWidth === 'boxed' }"
    id="app-content-container"
    v-scroll:#app-content-container="onScroll"
  >
    <slot></slot>
  </div>
</template>

<script>
import { toggleLotus } from '@/util/utils'
import useAppConfig from '@core/@app-config/useAppConfig'
import { getCurrentInstance, onMounted } from '@vue/composition-api'
export default {
  setup() {
    const { appContentWidth } = useAppConfig()
    const vm = getCurrentInstance().proxy
    const onScroll = e => {
      let isScroll = false
      if (e.target.scrollTop > 900) {
        isScroll = true
      }
      vm.$store.commit('global/setScroll', isScroll)
    }

    onMounted(() => {
      vm.$nextTick(() => {
        toggleLotus()
        // const globalThemeMode = vm.$store.state.appConfig.globalThemeMode
        // if (globalThemeMode === 'semi-dark') {
        // }
      })
    })
    return {
      appContentWidth,
      onScroll,
    }
  },
}
</script>

<style lang="scss">
@import '~@core/layouts/styles/_variables';

.app-content-container {
  &.app-content-container-boxed {
    // max-width: $content-max-width;
  }
}
</style>
