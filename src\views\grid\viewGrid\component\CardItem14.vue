<template>
  <!-- 安徽定制化地图-->
  <div class="box">
    <div v-if="imageType !== '1'" class="d-flex align-center justify-end">
      <img
        class="map-image"
        src="../images/map11.svg"
        @click.stop="changeMap(1)"
      />
      <img
        class="map-image"
        src="../images/map22.svg"
        @click.stop="changeMap(2)"
      />
      <img
        class="map-image"
        src="../images/map33.svg"
        @click.stop="changeMap(3)"
      />
      <!-- <v-btn @click.stop="changeMap(1)" icon>
        <img class="map-image" src="../images/map11.svg" />
      </v-btn>
      <v-btn class="map-image-1 mx-4" @click.stop="changeMap(2)" icon>
        <img class="map-image" src="../images/map22.svg" />
      </v-btn>
      <v-btn @click.stop="changeMap(3)" icon>
        <img class="map-image" src="../images/map33.svg" />
      </v-btn> -->
    </div>

    <div ref="echartBox" class="h-100">
      <vsoc-chart
        :echartId="echartId"
        :option="mapType === 1 ? option1 : mapType === 2 ? option2 : option3"
        :isShowEmpty="false"
      ></vsoc-chart>
    </div>
  </div>
</template>

<script>
import VsocChart from '@/components/VsocChart.vue'
import { num, numberToFormat } from '@/util/filters'
import { getRoundSize, primary, tooltip } from './chart'
import anhui from './map/anhui.json'
import china from './map/china'
import * as geoChinaMap from './map/geoChinaMapEn.json'

import { geoCoordMap } from './map/geoCoordMapEn.js'
import worldChinaCenter from './map/world-china-center'

const convertData = data => {
  var res = []

  for (var i = 0; i < data.length; i++) {
    var dataItem = data[i]

    if (!geoCoordMap[dataItem[0].name]) {
      geoCoordMap[dataItem[0].name] = dataItem[0].location
    }
    if (!geoCoordMap[dataItem[1].name]) {
      geoCoordMap[dataItem[1].name] = dataItem[1].location
    }
    var fromCoord = geoCoordMap[dataItem[0].name]
    var toCoord = geoCoordMap[dataItem[1].name]
    if (fromCoord && toCoord) {
      let xFrom = fromCoord[0]
      if (xFrom <= -30) {
        fromCoord[0] = 360 + xFrom
      }
      let xTo = toCoord[0]
      if (xTo <= -30) {
        toCoord[0] = 360 + xTo
      }
      res.push({
        fromName: dataItem[0].name,
        toName: dataItem[1].name,
        coords: [fromCoord, toCoord],
        value: dataItem[1].value,
      })
    }
  }
  return res
}
var convertChinaData = function (data) {
  var res = []
  for (var i = 0; i < data.length; i++) {
    var dataItem = data[i]
    var fromCoord = geoChinaMap.default[dataItem[0].name]
    var toCoord = geoChinaMap.default[dataItem[1].name]
    if (fromCoord && toCoord) {
      res.push({
        fromName: dataItem[0].name,
        toName: dataItem[1].name,
        coords: [fromCoord, toCoord],
      })
    }
  }
  return res
}
export default {
  name: 'CardItem14',
  props: {
    title: {
      type: String,
      default: '',
    },
    toData: {
      type: Array,
      default: () => {
        return []
      },
    },
    echartId: {
      type: String,
      default: '',
    },
    imageType: {
      type: String,
      default: '',
    },
    height: {
      type: Number,
      default: 0,
    },
    width: {
      type: Number,
      default: 0,
    },
    showType: {
      type: Number,
      default: 1,
    },
  },
  components: {
    VsocChart,
  },
  created() {
    this.initMap()
    // this.$echarts.registerMap('anhui', anhui)
  },
  data() {
    return {
      mapType: this.showType,
    }
  },
  computed: {
    option1() {
      let dataValue = []
      this.toData.forEach(v => {
        if (v.longitude && v.latitude) {
          dataValue.push({
            name: anhui.features.find(x =>
              v.cityName.includes(x.properties && x.properties.name),
            )?.properties.name,
            value: [v.longitude, v.latitude, v.count],
            carModel: v.carModel,
          })
        }
      })
      let pieces = []
      let normal = {}

      normal = {
        show: false,
      }
      if (this.echartId === 'C051') {
        pieces = [
          {
            gt: 600000,
            label: `数量 > ${num(600000)}`,
          },
          {
            gt: 1000,
            lte: 600000,
            label: `数量  ${num(1000)}-${num(600000)}`,
          },
          {
            gt: 300,
            lte: 1000,
            label: `数量  ${num(300)}-${num(1000)}`,
          },
          {
            gt: 1,
            lte: 300,
            label: '数量 < 300',
          },
        ]
      } else {
        pieces = [
          {
            gt: 2000,
            label: `数量 > ${num(2000)}`,
          },
          {
            gt: 1000,
            lte: 2000,
            label: `数量  ${num(1000)}-${num(2000)}`,
          },
          {
            gt: 500,
            lte: 1000,
            label: `数量  ${num(500)}-${num(1000)}`,
          },
          {
            gt: 1,
            lte: 500,
            label: '数量 < 500',
          },
        ]
      }
      let _this = this
      return {
        visualMap: {
          // 视觉映射组件
          type: 'piecewise',
          show: true,
          hoverLink: false,
          bottom: 0,
          left: 0,
          textStyle: {
            color: 'rgba(255, 255, 255, 0.8)',
            fontsize: getRoundSize(14),
          },
          itemSymbol: 'circle',
          itemWidth: 8,
          itemHeight: 8,
          itemGap: getRoundSize(14),
          min: 1,
          max: 500,
          //gt最小 lte最大
          pieces: pieces,
          color: ['#FF385D', '#FF9229', '#F0DA4C', '#21CBFF'], //自定义范围的颜色
        },
        // 底图样式
        geo: {
          show: true,
          roam: false,
          map: 'anhui',
          top: 0,
          left: getRoundSize(140),
          right: getRoundSize(140),
          bottom: 0,
          itemStyle: {
            borderColor: '#0385D3',
            borderWidth: 0.7,
            areaColor: 'rgba(255, 255, 255, 0.05)',
          },
          emphasis: {
            itemStyle: {
              areaColor: 'rgba(0,162,248, .6)',
            },
            label: {
              show: false,
              color: '#fff',
              fontSize: getRoundSize(14),
            },
          },
        },
        tooltip: {
          ...tooltip(),
          trigger: 'item',
          formatter(val) {
            if (!(val.data && val.data.value)) return
            return (
              val.marker +
              '\t' +
              val.data.name +
              ':\t' +
              num(val.data.value[2]) +
              '辆'
            )
          },
        },
        series: [
          {
            // zlevel: 1,
            // selectedMode: false,
            type: 'map',
            data: dataValue,
            mapType: 'anhui',
            showLegendSymbol: false,
            top: 0,
            left: getRoundSize(140),
            right: getRoundSize(140),
            bottom: 0,
            itemStyle: {
              borderColor: '#0385D3',
              borderWidth: 0.7,
              areaColor: 'rgba(255, 255, 255, 0.05)',
            },
            label: {
              show: true,
              textStyle: {
                color: '#fff',
                fontSize: getRoundSize(14),
                fontWeight: 400,
              },
              formatter(val) {
                // if (_this.echartId !== 'C051') return
                if (!(val.data && val.data.value)) return
                return `{name|${val.data.name}}\n{value|${numberToFormat(
                  val.data.value[2],
                )}}`
              },
              rich: {
                name: {
                  fontSize: getRoundSize(14),
                },
                value: {
                  fontSize: getRoundSize(12),
                },
              },
            },
          },
          {
            coordinateSystem: 'geo',
            type: 'scatter',
            data: dataValue,
            symbolSize: getRoundSize(20),
            symbol(val) {
              return 'image://' + require('../images/P1.png')
            },
            label: {
              normal: normal,
            },
            labelLine: {
              show: _this.echartId === 'C051' ? false : true, // 显示引导线
              lineStyle: {
                type: 'solid', // 线的类型
                color: '#fff', // 线的颜色
              },
            },
          },
        ],
      }
    },

    option2() {
      var hefeiData = [
        [{ name: '合肥' }, { name: '合肥' }],
        [{ name: '长春' }, { name: '合肥' }],
        [{ name: '包头' }, { name: '合肥' }],
        [{ name: '拉萨' }, { name: '合肥' }],
        [{ name: '乌鲁木齐' }, { name: '合肥' }],
        [{ name: '北海' }, { name: '合肥' }],
        [{ name: '北京' }, { name: '合肥' }],
      ]

      let datas = [['合肥', hefeiData]]
      let series = []
      datas.forEach((item, i) => {
        series.push(
          {
            name: item[0],
            type: 'lines',
            zlevel: 1,
            // 飞行线特效
            effect: {
              show: true, // 是否显示
              period: 2, // 特效动画时间
              trailLength: 0.4, // 特效尾迹长度。取从 0 到 1 的值，数值越大尾迹越长
              symbol: 'circle', // 特效图形标记
              // symbol: planePath,
              color: primary,
              symbolSize: 4, // 特效图标大小
              loop: true,
            },
            // 线条样式
            lineStyle: {
              curveness: -0.2, // 飞线弧度
              type: 'solid', // 飞线类型
              color: primary, // 飞线颜色
              width: 2, // 飞线宽度
              opacity: 0,
            },
            itemStyle: {
              borderColor: '#0385D3',
              borderWidth: 0.7,
              areaColor: 'rgba(255, 255, 255, 0.05)',
            },

            data: convertChinaData(item[1]),
          },
          {
            type: 'effectScatter', // 带有涟漪特效动画的散点（气泡）图
            coordinateSystem: 'geo',
            markPoint: {
              symbol:
                'path://M9 20C7.75 20 0 13.9705 0 9C0 4.0295 4.0295 0 9 0C13.9705 0 18 4.0295 18 9C18 13.9705 10.25 20 9 20ZM9 11.75C10.5188 11.75 11.75 10.5187 11.75 9C11.75 7.48125 10.5188 6.25 9 6.25C7.48125 6.25 6.25 7.48125 6.25 9C6.25 10.5187 7.48125 11.75 9 11.75Z',
            },
            zlevel: 2,
            symbol: 'circle',
            symbolSize: 10,
            // 涟漪特效
            rippleEffect: {
              period: 4,
              scale: 4,
              brushType: 'stroke',
            },
            itemStyle: {
              color: '#FF385D',
            },
            label: {
              show: false,
              // color: '#fff',
              position: 'bottom',
              // fontSize: getRoundSize(16),
              textStyle: {
                color: '#fff',
                fontSize: getRoundSize(14),
                fontWeight: 400,
              },
              formatter: function (item) {
                return item.name
              },
            },

            // 这里用来组装自定义数据，以便在tooltip中取得。
            data: item[1].map(dataItem => {
              return {
                name: dataItem[0].name,
                value: geoChinaMap.default[dataItem[0].name],
              }
            }),
          },
        )
      })
      return {
        // 底图样式
        geo: {
          map: 'china', // 地图类型
          roam: false, // 如果只想要开启缩放或者平移，可以设置成 'scale' 或者 'move'。设置成 true 为都开启
          // zoom: 1.2, // 初始缩放大小
          top: getRoundSize(25),
          left: getRoundSize(50),
          right: getRoundSize(50),
          bottom: getRoundSize(30),
          // center: [11.3316626, 19.5845024], // 地图中心点
          // scaleLimit: {
          //   // 缩放等级
          //   min: 1,
          //   max: 15,
          // },
          // nameMap:
          //   this.$i18n.locale === 'en'
          //     ? require('./map/nameMapEn.json')
          //     : require('./map/nameMapZh.json'), // 自定义地区的名称映射
          // 三维地理坐标系样式
          itemStyle: {
            borderColor: '#0385D3',
            borderWidth: 0.7,
            areaColor: 'rgba(255, 255, 255, 0.05)',
          },
          // 鼠标悬浮样式
          emphasis: {
            itemStyle: {
              areaColor: 'rgba(0,162,248, .6)',
            },
            label: {
              show: true,
              color: '#fff',
              fontSize: getRoundSize(14),
            },
          },
          label: {
            show: false,
            textStyle: {
              color: '#fff',
              fontSize: getRoundSize(14),
              fontWeight: 400,
            },
            // formatter(val) {
            //   console.log(val, 11)
            //   return `{name|${val.data.fromName}}`
            //   // if (_this.echartId !== 'C051') return
            //   // if (!(val.data && val.data.value)) return
            //   // return `{name|${val.data.name}}\n{value|${numberToFormat(
            //   //   val.data.value[2],
            //   // )}}`
            // },
            rich: {
              name: {
                fontSize: getRoundSize(14),
              },
              value: {
                fontSize: getRoundSize(12),
              },
            },
          },
        },

        series: series,

        legend: {
          show: false,
        },

        tooltip: {
          show: false,
          trigger: 'item',
          triggerOn: 'click', // 提示框触发的条件
          enterable: false, // 鼠标是否可进入提示框浮层中，默认为false，如需详情内交互，如添加链接，按钮，可设置为 true
          backgroundColor: 'rgba(0,0,0,0.8)',
          borderColor: 'rgba(0,0,0,0.2)',
          textStyle: {
            color: '#fff',
          },
          formatter: function (params) {
            if (params.seriesType == 'lines') {
              return params.data.fromName + '>' + params.data.toName
            } else if (params.seriesType === 'effectScatter') {
              return params.name
            } else {
              return ''
            }
          },
        },
      }
    },

    option3() {
      var toData = [
        [
          { name: 'China,anhui', location: [112.936271, 28.235399] },
          { name: 'China,anhui', location: [112.936271, 28.235399] },
        ],
        [{ name: 'Albania' }, { name: 'China,anhui' }],
        // [{ name: 'Burundi' }, { name: 'China,anhui' }],
        // [{ name: 'Belarus' }, { name: 'China,anhui' }],
        [{ name: 'Bhutan' }, { name: 'China,anhui' }],
        [{ name: 'United States, Washington' }, { name: 'China,anhui' }],
        // [{ name: 'Canada, Winnipeg' }, { name: 'China,anhui' }],
        // [{ name: 'Russia, Nizhny Novgorod' }, { name: 'China,anhui' }],
        [{ name: 'Norway' }, { name: 'China,anhui' }],
        [
          {
            name: 'Australia',
            location: [133.565867, -25.303147],
          },
          { name: 'China,anhui' },
        ],
        [
          {
            name: 'Brazil',
            location: [-54.013916, -8.270681],
          },
          { name: 'China,anhui' },
        ],
      ]
      let datas = [['China,anhui', toData]]
      let series = []
      datas.forEach((item, i) => {
        series.push(
          {
            name: item[0],
            type: 'lines',
            zlevel: 1,
            // 飞行线特效
            effect: {
              show: true, // 是否显示
              period: 2, // 特效动画时间
              trailLength: 0.4, // 特效尾迹长度。取从 0 到 1 的值，数值越大尾迹越长
              symbol: 'circle', // 特效图形标记
              // symbol: planePath,
              color: primary,
              symbolSize: 4, // 特效图标大小
              loop: true,
            },
            // 线条样式
            lineStyle: {
              curveness: -0.2, // 飞线弧度
              type: 'solid', // 飞线类型
              color: primary, // 飞线颜色
              width: 2, // 飞线宽度
              opacity: 0,
            },
            data: convertData(item[1]),
          },
          {
            type: 'effectScatter', // 带有涟漪特效动画的散点（气泡）图
            coordinateSystem: 'geo',
            markPoint: {
              symbol:
                'path://M9 20C7.75 20 0 13.9705 0 9C0 4.0295 4.0295 0 9 0C13.9705 0 18 4.0295 18 9C18 13.9705 10.25 20 9 20ZM9 11.75C10.5188 11.75 11.75 10.5187 11.75 9C11.75 7.48125 10.5188 6.25 9 6.25C7.48125 6.25 6.25 7.48125 6.25 9C6.25 10.5187 7.48125 11.75 9 11.75Z',
            },
            zlevel: 2,
            symbol: 'circle',
            // symbol:
            //   'path://M9 20C7.75 20 0 13.9705 0 9C0 4.0295 4.0295 0 9 0C13.9705 0 18 4.0295 18 9C18 13.9705 10.25 20 9 20ZM9 11.75C10.5188 11.75 11.75 10.5187 11.75 9C11.75 7.48125 10.5188 6.25 9 6.25C7.48125 6.25 6.25 7.48125 6.25 9C6.25 10.5187 7.48125 11.75 9 11.75Z',
            symbolSize: 10,
            // 涟漪特效
            rippleEffect: {
              period: 4,
              scale: 4,
              brushType: 'stroke',
            },
            itemStyle: {
              color: '#FF385D',
            },
            label: {
              show: true,
              color: '#fff',
              position: 'bottom',
              fontSize: getRoundSize(16),
              formatter: function (item) {
                return ''
              },
            },
            // 这里用来组装自定义数据，以便在tooltip中取得。
            data: item[1].map(dataItem => {
              return {
                name: dataItem[0].name,
                value: geoCoordMap[dataItem[0].name],
              }
            }),
          },
        )
      })

      return {
        // 底图样式
        geo: {
          map: 'world', // 地图类型
          roam: false, // 如果只想要开启缩放或者平移，可以设置成 'scale' 或者 'move'。设置成 true 为都开启
          // top: getRoundSize(24),
          // left: getRoundSize(48),
          // right: getRoundSize(48),
          // bottom: getRoundSize(30),
          zoom: 1.2, // 初始缩放大小
          // center: [11.3316626, 19.5845024], // 地图中心点
          scaleLimit: {
            // 缩放等级
            min: 1,
            max: 15,
          },
          // nameMap:
          //   this.$i18n.locale === 'en'
          //     ? require('./map/nameMapEn.json')
          //     : require('./map/nameMapZh.json'), // 自定义地区的名称映射
          // 三维地理坐标系样式
          itemStyle: {
            borderColor: '#0385D3',
            borderWidth: 0.7,
            areaColor: 'rgba(255, 255, 255, 0.05)',
          },
          // 鼠标悬浮样式
          emphasis: {
            itemStyle: {
              areaColor: 'rgba(0,162,248, .6)',
            },
            label: {
              show: true,
              color: '#fff',
              fontSize: getRoundSize(14),
            },
          },
        },

        series: series,

        legend: {
          show: false,
        },

        tooltip: {
          show: false,
          trigger: 'item',
          triggerOn: 'click', // 提示框触发的条件
          enterable: false, // 鼠标是否可进入提示框浮层中，默认为false，如需详情内交互，如添加链接，按钮，可设置为 true
          backgroundColor: 'rgba(0,0,0,0.8)',
          borderColor: 'rgba(0,0,0,0.2)',
          textStyle: {
            color: '#fff',
          },
          formatter: function (params) {
            if (params.seriesType == 'lines') {
              return params.data.fromName + '>' + params.data.toName
            } else if (params.seriesType === 'effectScatter') {
              return params.name
            } else {
              return ''
            }
          },
        },
      }
    },
  },
  methods: {
    changeMap(type) {
      this.$emit('changeMap', type)
      this.mapType = type
      this.initMap()
    },
    initMap() {
      if (this.mapType === 1) {
        this.$echarts.registerMap('anhui', anhui)
      } else if (this.mapType === 2) {
        this.$echarts.registerMap('china', china)
      } else if (this.mapType === 3) {
        this.$echarts.registerMap('world', worldChinaCenter)
      }
    },
  },
}
</script>
