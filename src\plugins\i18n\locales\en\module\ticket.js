const ticket = {
  currentTitle: 'Ticket',
  headers: {
    ticketType: 'Type',
    ticketId: 'ID',
    title: 'Title',
    name: 'Short description',
    priority: 'Priority',
    createDate: 'Create Time',
    createUser: 'Create User',
    classifyName: 'Category',
    assignedToName: 'Assigned To',
    assignedGroupName: 'Assignment group',
    status: 'Status',
    dataSourceName: 'Channel',
    relationId: 'Linked Data ID',
    time: 'Time',
    reason: 'Reason',
    content: 'Description',
    impact: 'Impact',
    eta: 'Expected completion time',
    TimeLeft: 'Time Left',
    ElapsedTime: 'Elapsed Time',
    ElapsedPercentage: 'Elapsed Percentage',
    WatchList: 'Watch List',
    Operation: 'Operation Type',
    models: 'Affected Models',
  },
  changeHeaders: {
    claimed: 'Pending Tickets',
    list: 'Assigned To Me',
    Application: 'My Created',
    tickets: 'All Tickets',
    follow: 'My Following',
  },
  btn: {
    reback: 'Exit',
    assign: 'Assign',
    cancel: 'Cancel',
    resolve: 'Resolve',
    upLoad: 'Upload attachments',
    import: 'Batch Import',
    detail: 'Ticket Details',
    add: 'New Ticket',
    edit: 'Edit Ticket',
  },
  title: {},
  hint: {
    updateTips: 'Display Update Operation',
    can: 'Can {0} Ticket',
    time: 'Select Time',
    suceess: 'successfully！',
    timeTip1: 'Time span not allowed to exceed 3 months',
    opInfo: 'Are you sure  to {0} {1} tickets？',
    attachments: 'Attachment',
    tracks: 'Activities',
    solution: 'Resolution Information',
    follow: 'Work Notes',
    analy: 'Vulnerability Analysis',
    attachmentsTip:
      'The maximum number of attachments is {0}, and the size of a single attachment does not exceed {1}; Attachment format requirements',
    Details: 'Details',
    please: 'Please input',
    upLoad: 'Incorrect file format!',
    upLoad1: 'The size of the uploaded file cannot exceed {0}!',
    upLoad2: 'Upload failed',
    upLoad3: 'Upload up to {0} attachments',
    comfirmInfo:
      'The Assigned User has not changed and does not need to be assigned！',
    successfully: 'successfully',
    tips: 'After clicking confirm, the original content will be immediately overwritten. Please choose carefully！',
    timeTip:
      'The expected completion time should be greater than the current time！',
  },
  template: {
    hint: 'Download the ticket Import template',
    text: 'The ticket Import template',
  },
  fileHint:
    'The size of a single attachment should not exceed 5M; attachment format requirements: xls,xlsx',
  remarkHint:
    'The maximum allowed input character is 5W. If it exceeds the limit, please use the attachment to upload it!',
  remarkHint1:
    'The input exceeds the maximum limit. It is recommended to upload it as an attachment!',
  dashboardVulner: {
    attackCharacter: 'Attack Character',
    attackVector: 'Attack Vector',
    importProport: 'Impact',
    accessType: 'Access',
    attackDistance: 'Distance',
    vulnerabilityAssessment: 'Vulnerability Assessment',
  },
  completeAlertTip1:
    'Before the completion of the work order, please ensure that the alarms associated with the work order have been properly handled or clearly marked as not requiring handling.',
  completeAlertTip2:
    'Please note that there are still unresolved alerts for this work order. Are you sure you want to force the closure of the ticket?',
  completeVulTip1:
    'Before the completion of the work order, please ensure that the vulnerabilities associated with the work order have been properly addressed or clearly marked as not requiring resolution.',
  completeVulTip2:
    'Please note that there are still unresolved vulnerabilities in this ticket. Are you sure you want to force the closure of the ticket?',
}

export default ticket
