import { getLocalStorage } from '@/util/localStorage'
import themeConfig from '@themeConfig'
require('./overrides.scss')

// Skins
require('./skins/bordered.scss')
require('./skins/semi-dark.scss')
require('./skins/lotus.scss')

export default {
  theme: {
    themes: {
      ...themeConfig.themes,
    },
    dark:
      getLocalStorage('materio-active-theme') === null
        ? themeConfig.app.isDark
        : getLocalStorage('materio-active-theme') === 'dark',
  },
  rtl: themeConfig.app.isRtl,
}
