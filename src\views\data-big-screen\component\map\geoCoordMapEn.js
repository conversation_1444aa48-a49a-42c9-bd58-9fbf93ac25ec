export const geoCoordMap = {
  Afghanistan: [67.709953, 33.93911],
  Angola: [17.873887, -11.202692],
  Albania: [20.168331, 41.153332],
  UAE: [53.847818, 23.424076],
  Argentina: [-63.61667199999999, -38.416097],
  Armenia: [45.038189, 40.069099],
  'French Southern Hemisphere and Antarctic Territories': [
    69.348557, -49.280366,
  ],
  Australia: [133.775136, -25.274398],
  Austria: [14.550072, 47.516231],
  Azerbaijan: [47.576927, 40.143105],
  Burundi: [29.918886, -3.373056],
  Belgium: [4.469936, 50.503887],
  Benin: [2.315834, 9.30769],
  'Burkina Faso': [-1.561593, 12.238333],
  Bangladesh: [90.356331, 23.684994],
  Bulgaria: [25.48583, 42.733883],
  Bahamas: [-77.39627999999999, 25.03428],
  'Bosnia and Herzegovina': [17.679076, 43.915886],
  Belarus: [27.953389, 53.709807],
  Belize: [-88.49765, 17.189877],
  Bermuda: [-64.7505, 32.3078],
  Bolivia: [-63.58865299999999, -16.290154],
  Brazil: [-51.92528, -14.235004],
  Brunei: [114.727669, 4.535277],
  Bhutan: [90.433601, 27.514162],
  Botswana: [24.684866, -22.328474],
  'Central African Republic': [20.939444, 6.611110999999999],
  Canada: [-106.346771, 56.130366],
  Switzerland: [8.227511999999999, 46.818188],
  Chile: [-71.542969, -35.675147],
  China: [104.195397, 35.86166],
  'Ivory Coast': [-5.547079999999999, 7.539988999999999],
  Cameroon: [12.354722, 7.369721999999999],
  'Democratic Republic of the Congo': [21.758664, -4.038333],
  'Republic of the Congo': [15.827659, -0.228021],
  Columbia: [-74.297333, 4.570868],
  'Costa Rica': [-83.753428, 9.748916999999999],
  Cuba: [-77.781167, 21.521757],
  'Northern Cyprus': [33.429859, 35.126413],
  Cyprus: [33.429859, 35.126413],
  'Czech Republic': [15.472962, 49.81749199999999],
  Germany: [10.451526, 51.165691],
  Djibouti: [42.590275, 11.825138],
  Denmark: [9.501785, 56.26392],
  'Dominican Republic': [-70.162651, 18.735693],
  Algeria: [1.659626, 28.033886],
  Ecuador: [-78.18340599999999, -1.831239],
  Egypt: [30.802498, 26.820553],
  Eritrea: [39.782334, 15.179384],
  Spain: [-3.74922, 40.46366700000001],
  Estonia: [25.013607, 58.595272],
  Ethiopia: [40.489673, 9.145000000000001],
  Finland: [25.748151, 61.92410999999999],
  Fei: [178.065032, -17.713371],
  'Falkland Islands': [-59.523613, -51.796253],
  France: [2.213749, 46.227638],
  Gabon: [11.609444, -0.803689],
  'United Kingdom': [-3.435973, 55.378051],
  Georgia: [-82.9000751, 32.1656221],
  Ghana: [-1.023194, 7.946527],
  Guinea: [-9.696645, 9.945587],
  Gambia: [-15.310139, 13.443182],
  'Guinea-Bissau': [-15.180413, 11.803749],
  'Equatorial Guinea': [10.267895, 1.650801],
  Greece: [21.824312, 39.074208],
  Greenland: [-42.604303, 71.706936],
  Guatemala: [-90.23075899999999, 15.783471],
  'French Guiana': [-53.125782, 3.933889],
  Guyana: [-58.93018, 4.860416],
  Honduras: [-86.241905, 15.199999],
  Croatia: [15.2, 45.1],
  Haiti: [-72.285215, 18.971187],
  Hungary: [19.503304, 47.162494],
  Indonesia: [113.921327, -0.789275],
  Ireland: [-8.24389, 53.41291],
  Iran: [53.688046, 32.427908],
  Iraq: [43.679291, 33.223191],
  Iceland: [-19.020835, 64.963051],
  Israel: [34.851612, 31.046051],
  Italy: [12.56738, 41.87194],
  Jamaica: [-77.297508, 18.109581],
  Jordan: [36.238414, 30.585164],
  Japan: [138.252924, 36.204824],
  'Japan,AA': [138.252924, 36.204824],
  Kazakhstan: [66.923684, 48.019573],
  Kenya: [37.906193, -0.023559],
  Kyrgyzstan: [74.766098, 41.20438],
  Cambodia: [104.990963, 12.565679],
  'South Korea': [127.766922, 35.907757],
  'Korea,BB': [127.766922, 35.907757],
  Kosovo: [20.902977, 42.6026359],
  Kuwait: [47.481766, 29.31166],
  Laos: [102.495496, 19.85627],
  Lebanon: [35.862285, 33.854721],
  Liberia: [-9.429499000000002, 6.428055],
  Libya: [17.228331, 26.3351],
  'Sri Lanka': [80.77179699999999, 7.873053999999999],
  Lesotho: [28.233608, -29.609988],
  Lithuania: [23.881275, 55.169438],
  Luxembourg: [6.129582999999999, 49.815273],
  Latvia: [24.603189, 56.879635],
  Morocco: [-7.092619999999999, 31.791702],
  Moldova: [28.369885, 47.411631],
  Madagascar: [46.869107, -18.766947],
  Mexico: [-102.552784, 23.634501],
  Macedonia: [21.745275, 41.608635],
  Mali: [-3.996166, 17.570692],
  Myanmar: [95.956223, 21.913965],
  Montenegro: [19.37439, 42.708678],
  Mongolia: [103.846656, 46.862496],
  Mozambique: [35.529562, -18.665695],
  Mauritania: [-10.940835, 21.00789],
  Malawi: [34.301525, -13.254308],
  Malaysia: [101.975766, 4.210484],
  Namibia: [18.49041, -22.95764],
  'New Caledonia': [165.618042, -20.904305],
  Niger: [8.081666, 17.607789],
  Nigeria: [8.675277, 9.081999],
  Nicaragua: [-85.207229, 12.865416],
  Netherlands: [5.291265999999999, 52.132633],
  Norway: [8.468945999999999, 60.47202399999999],
  Nepal: [84.12400799999999, 28.394857],
  'New Zealand': [174.885971, -40.900557],
  Oman: [55.923255, 21.512583],
  Pakistan: [69.34511599999999, 30.375321],
  Panama: [-80.782127, 8.537981],
  Peru: [-75.015152, -9.189967],
  Philippines: [121.774017, 12.879721],
  'Papua New Guinea': [143.95555, -6.314992999999999],
  Poland: [19.145136, 51.919438],
  'Puerto Rico': [-66.590149, 18.220833],
  'North Korea': [127.510093, 40.339852],
  Portugal: [-8.224454, 39.39987199999999],
  Paraguay: [-58.443832, -23.442503],
  Qatar: [51.183884, 25.354826],
  Romania: [24.96676, 45.943161],
  Russia: [105.318756, 61.52401],
  Rwanda: [29.873888, -1.940278],
  'Western Sahara': [-12.885834, 24.215527],
  'Saudi Arabia': [45.079162, 23.885942],
  Sudan: [30.217636, 12.862807],
  'South Sudan': [31.3069788, 6.876991899999999],
  Senegal: [-14.452362, 14.497401],
  'Solomon Islands': [160.156194, -9.64571],
  'Sierra Leone': [-11.779889, 8.460555],
  'El Salvador': [-88.89653, 13.794185],
  Somaliland: [46.8252838, 9.411743399999999],
  Somalia: [46.199616, 5.152149],
  'Republic of Serbia': [21.005859, 44.016521],
  Suriname: [-56.027783, 3.919305],
  Slovakia: [19.699024, 48.669026],
  Slovenia: [14.995463, 46.151241],
  Sweden: [18.643501, 60.12816100000001],
  Swaziland: [31.465866, -26.522503],
  Syria: [38.996815, 34.80207499999999],
  Chad: [18.732207, 15.454166],
  Togo: [0.824782, 8.619543],
  Thailand: [100.992541, 15.870032],
  Tajikistan: [71.276093, 38.861034],
  Turkmenistan: [59.556278, 38.969719],
  'East Timor': [125.727539, -8.874217],
  'Trinidad and Tobago': [-61.222503, 10.691803],
  Tunisia: [9.537499, 33.886917],
  Turkey: [35.243322, 38.963745],
  'United Republic of Tanzania': [34.888822, -6.369028],
  Uganda: [32.290275, 1.373333],
  Ukraine: [31.16558, 48.379433],
  Uruguay: [-55.765835, -32.522779],
  'United States': [-95.712891, 37.09024],
  Uzbekistan: [64.585262, 41.377491],
  Venezuela: [-66.58973, 6.42375],
  Vietnam: [108.277199, 14.058324],
  Vanuatu: [166.959158, -15.376706],
  'West Bank': [35.3027226, 31.9465703],
  Yemen: [48.516388, 15.552727],
  'South Africa': [22.937506, -30.559482],
  Zambia: [27.849332, -13.133897],
  Zimbabwe: [29.154857, -19.015438],
  Comores: [43.872219, -11.875001],
  Indonesia: [106.515414, -6.10304],
  India: [78.96288, 20.593684],
  'Albania, Tirana': [19.48, 41.2],
  'Algeria, Algiers': [3.13, 36.42],
  'Afghanistan, Kabul': [69.1, 34.3],
  'Argentina, La Plata': [-57.57, -34.55],
  'Argentina, Buenos Aires': [-58.3, -34.2],
  'Argentina,Tucumán': [-65.2, -26.3],
  'Argentina, Rosario': [-60.4, -32.57],
  'Argentina, Mar del Plata': [-57.32, -38],
  'Argentina, Neuquen': [-68.04, -38.57],
  'Argentina, Mendoza': [-68.5, -32.54],
  'Argentina, Salta': [-65.24, -24.47],
  'United Arab Emirates, Abu Dhabi': [54.23, 24.27],
  'UAE,Dubai': [55.17, 25.13],
  'Oman, Muscat': [58.37, 23.36],
  'Azerbaijan, Baku': [49.53, 40.22],
  'Egypt, Cairo': [31.17, 30],
  'Egypt, Alexandria': [29.55, 31.13],
  'Egypt, Giza': [31.12, 30.01],
  'Ethiopia, Addis Ababa': [38.42, 9.03],
  'Ireland, Dublin': [-6.15, 53.26],
  'Estonia, Tallinn': [24.48, 59.22],
  'Andorra,Andorra': [1.31, 42.3],
  'Angola,Luanda': [13.2, -8.5],
  'Anguilla, Valley': [-63.04, -18.13],
  'Antigua and Barbuda, Saint John': [-61.51, 17.07],
  'Austria, Salzburg': [13.03, 47.54],
  'Austria, Vienna': [16.22, 48.13],
  'Australia, Lord Howe Island': [159, -33.3],
  'Australia, Darwin': [130.51, -12.28],
  'Australia, Kingston': [167.58, -29.03],
  'Australia, Hobart': [147.18, -42.54],
  'Australia,Adelaide': [138.36, -34.56],
  'Australia,Sydney': [151.17, -33.55],
  'Australia,Perth': [115.49, -31.58],
  'Australia, New Village': [105.4, -10.3],
  'Australia,Brisbane': [153.02, -27.28],
  'Australia,Melbourne': [144.58, -37.49],
  'Australia,Canberra': [149.08, -35.18],
  'Bridgetown, Barbados': [-59.37, 13.06],
  'Papua New Guinea, Port Moresby': [147.07, -9.3],
  'Bahamas,Nassau': [-77.2, 25.03],
  'Pakistan, Peshawar': [71.4, 34.01],
  'Pakistan, Karachi': [67.02, 24.51],
  'Pakistan, Sialkot': [74.35, 32.29],
  'Pakistan,Islamabad': [73.08, 33.4],
  'Pakistan, Faisalabad': [73.09, 31.25],
  'Pakistan, Lahore': [74.22, 31.34],
  'Paraguay, Asunción': [-57.4, -25.15],
  'Bahrain, Manama': [50.36, 26.12],
  'Panama,Panama': [-79.3, 8.57],
  'Brazil, El Salvador': [-38.29, -12.58],
  'Brazil, Rio Blanco': [-67.49, -9.59],
  'Brazil, Sao Paulo': [-46.38, -23.34],
  'Brazil, Recife': [-34.53, -8.06],
  'Brazil,Porto Alegre': [-51.14, -30.02],
  'Brazil, Rio de Janeiro': [-43.15, -22.54],
  'Brazil, Brasilia': [-47.57, -15.45],
  'Brazil, Fernando de Noronha': [-32.25, -3.54],
  'Brazil, Manaus': [-60, -3.06],
  'Belarus, Minsk': [27.3, 53.51],
  'Hamilton, Bermuda': [-64.47, 32.18],
  'Bulgaria,Sofia': [23.2, 42.43],
  'Benin, Porto-Novo': [2.47, 6.3],
  'Belgium, Brussels': [4.21, 50.51],
  'Iceland, Reykjavik': [-21.58, 64.09],
  'Bolivia, Sucre': [-65.16, -19.02],
  'Bolivia, La Paz': [-68.09, -16.3],
  'San Juan, Puerto Rico': [-66.08, 18.29],
  'Bosnia and Herzegovina, Sarajevo': [18.26, 43.52],
  'Poland, Poznan': [16.53, 52.25],
  'Poland, Wroclaw': [17, 51.05],
  'Poland, Krakow': [19.55, 50.03],
  'Poland, Gdansk': [18.38, 54.22],
  'Poland, Warsaw': [21, 52.15],
  'Poland, Lodz': [19.28, 51.49],
  'Poland, Szczecin': [14.32, 53.25],
  'Botswana, Gaborone': [25.55, -24.45],
  'Belize, Belmopan': [-88.46, 17.25],
  'Bhutan, Thimphu': [89.4, -27.29],
  'Ouagadougou, Burkina Faso': [-1.4, 12.2],
  'Burundi, Bujumbura': [29.21, -3.22],
  'North Korea, Pyongyang': [125.47, 39],
  'Equatorial Guinea, Malabo': [8.48, 3.45],
  'Denmark, Nuuk': [-51.4, 64.1],
  'Denmark, Torshafen': [-6.46, 62.01],
  'Denmark, Copenhagen': [12.34, 55.43],
  'Germany, Munich': [11.35, 48.08],
  'Germany, Hamburg': [10, 53.33],
  'Germany, Dusseldorf': [6.47, 51.13],
  'Germany, Berlin': [13.2, 52.31],
  'Germany, Frankfurt': [8.34, 50.02],
  'Germany, Braunschweig': [10.45, 52.29],
  'East Timor, Dili': [125.35, -8.35],
  'Togo, Lomé': [1.21, 6.1],
  'Dominica, Santo Domingo': [-69.57, 18.3],
  'Dominique, Rosso': [-61.23, 15.18],
  'Russia, Kamchatka': [158.39, 53.01],
  'Russia, Kazan': [49.1, 55.45],
  'Russia, Krasnoyarsk': [92.46, 56.05],
  'Russia, Kaliningrad': [20.3, 54.43],
  'Russia, Chelyabinsk': [61.25, 55.1],
  'Russia, Barnaul': [83.47, 53.21],
  'Russia, Anadyr': [177.32, 64.4],
  'Russia, Nizhny Novgorod': [44.01, 56.2],
  'Russia, Yekaterinburg': [60.35, 56.52],
  'Russia, Yakutsk': [129.51, 62.1],
  'Russia, Vladivostok': [131.53, 43.09],
  'Russia,Moscow': [37.37, 55.45],
  'Russia,Murmansk': [33.08, 68.59],
  'Russia, Yuzhno-Sakhalinsk': [142.44, 46.58],
  'Russia, Novgorod': [31.2, 58.3],
  'Russia, Novosibirsk': [82.55, 55.04],
  'Russia, Omsk': [73.22, 55],
  'Russia, Ryazan': [39.45, 54.37],
  'Russia, Saint Petersburg': [30.25, 59.55],
  'Russia, Samara': [50.15, 53.1],
  'Russia, Tyumen': [65.32, 57.09],
  'Russia, Ufa': [55.58, 54.45],
  'Ecuador, Galapagos Islands': [-89.36, -0.54],
  'Quito, Ecuador': [-78.3, -0.14],
  'Ecuador, Guayaquil': [-79.54, -2.13],
  'Eritrea, Asmara': [-38.58, 15.2],
  'France, Basseterre': [-61.32, 16.14],
  'France, Theohay': [-139.3, -9],
  'France, Cayenne': [-52.18, 4.55],
  'France, Bastia': [9.26, 42.41],
  'France, Saint-Denis': [55.28, -20.52],
  'France,Port-France': [70.13, -49.21],
  'France,Paris': [2.2, 48.51],
  'France,Fort-de-France': [-61.05, 14.36],
  'France, Gambier Islands': [-134.57, -23.08],
  'France, Mamouchu': [45.14, -12.47],
  'France, Nice': [7.16, 43.42],
  'France, Noumea': [166.27, -22.16],
  'France, Papeete': [-149.34, -17.32],
  'Philippines, Manila': [121, 14.37],
  'Philippines, Cebu': [123.54, 10.17],
  'Finland, Helsinki': [24.53, 60.1],
  'Cape Verde, Praia': [-23.31, 14.55],
  'Gambia, Banjul': [-16.39, 13.28],
  'Congo, Brazzaville': [15.14, -4.14],
  'Democratic Republic of the Congo, Kinshasa': [15.18, -4.18],
  'Democratic Republic of the Congo, Lubumbashi': [27.29, -11.44],
  'Colombia,Medellin': [-75.36, 6.15],
  'Colombia, Cali': [-76.3, 3.24],
  'Colombia, Bogota': [-74.05, 4.38],
  'San Jose, Costa Rica': [-84.04, 9.59],
  'Grenada, Saint George': [-61.44, 12.04],
  'Cuba, Havana': [-82.23, 23.08],
  'Georgetown, Guyana': [-58.1, 6.46],
  'Kazakh, Astana': [71.3, 51.1],
  'Kazakh, Akto': [50.16, 44.31],
  'Kazakh, Aktobe': [57.1, 50.17],
  'Kazakhstan, Almaty': [76.55, 43.19],
  'Haiti,Port-au-Prince': [-72.2, 18.32],
  'Korea,Incheon': [126.38, 37.3],
  'Korea, Busan': [129.02, 35.05],
  'Korea, Daegu': [128.36, 35.52],
  'Korea,Seoul': [127.03, 37.35],
  'Netherlands, Rotterdam': [4.29, 51.55],
  'Netherlands, Willemstad': [-68.56, 12.12],
  'Netherlands,Amsterdam': [4.52, 52.21],
  'Netherlands, Oranjestak': [-69.58, 12.3],
  'Honduras, Tegucigalpa': [-87.14, 14.05],
  Kiribati: [173.039003, 1.350198],
  'Christmas Island, Kiribati': [-157.2, 1.52],
  'Kiribati, Tarawa': [173, 1.25],
  'Lawaki, Kiribati': [-171.05, -3.08],
  'Djibouti,Djibouti': [42.5, 12],
  'Kyrgyzstan, Bishkek': [74.46, 42.53],
  'Guinea, Conakry': [-13.43, 9.3],
  'Guinea-Bissau,Bissau': [-15.39, 11.52],
  'Canada, Charlottetown': [-63.09, 46.14],
  'Canada,Calgary': [-114.05, 51.05],
  'Canada, Brampton': [-79.46, 43.41],
  'Canada,Mont-Sablon': [-57.08, 51.26],
  'Canada, Longueuil': [-73.3, 45.32],
  'Canada, Winnipeg': [-97.1, 49.53],
  'Canada, Windsor': [-83.01, 42.18],
  'Canada, Whitehorse': [-135.08, 60.41],
  'Canada, Edmonton': [-113.25, 53.34],
  'Canada, Gatineau': [-75.4, 45.29],
  'Canada, Halifax': [-63.35, 44.38],
  'Canada, Hamilton': [-79.51, 43.15],
  'Canada, Icaret': [-68.3, 63.45],
  'Canada, Laval': [-73.45, 45.35],
  'Canada,London': [-81.14, 42.59],
  'Canada,Yellowknife': [-114.29, 62.3],
  'Canada, Markham': [-79.19, 43.49],
  'Canada, Mississauga': [-79.36, 43.41],
  'Canada, Montreal': [-73.35, 45.3],
  'Canada, Ottawa': [-75.43, 45.25],
  'Canada,Quebec': [-71.15, 46.5],
  'Canada, Regina': [-104.38, 50.3],
  'Canada, Saint John': [-52.41, 47.34],
  'Canada,Saskatoon': [-106.4, 52.1],
  'Canada, Surrey': [-122.51, 49.11],
  'Canada,Toronto': [-79.22, 43.4],
  'Canada,Vancouver': [-123.06, 49.13],
  'Canada,Victoria': [-123.21, 48.25],
  'Ghana, Accra': [0.15, 5.33],
  'Gabon, Libreville': [9.25, 0.3],
  'Gaza Strip,Gaza': [34.28, 31.3],
  'Cambodia, Phnom Penh': [104.55, 11.35],
  'Czech Republic, Prague': [14.25, 50.05],
  'Zimbabwe, Harare': [31.04, -17.49],
  'Cameroon, Yaounde': [11.31, 3.51],
  'Qatar, Doha': [51.34, 25.15],
  'Comores, Moroni': [43.19, -11.4],
  'Kosovo,Pristina': [21.1, 42.39],
  "Côte d'Ivoire, Abidjan": [4.01, 5.19],
  "Côte d'Ivoire, Yamoussoukro": [-5.18, 6.51],
  'Kuwait, Governorate of Kuwait': [48, 29.2],
  'Croatia,Zagreb': [15.58, 45.49],
  'Kenya,Nairobi': [36.49, -1.17],
  'Cook Islands, Rarotonga': [-160.16, -21.2],
  'Latvia, Riga': [24.05, 56.53],
  'Lesotho, Maseru': [27.28, -29.18],
  'Laos, Vientiane': [102.48, 18.01],
  'Lebanon, Beirut': [35.3, 33.52],
  'Liberia,Monrovia': [-10.46, 6.2],
  'Libya, Tripoli': [13.12, 32.58],
  'Lithuania, Kaunas': [23.54, 54.54],
  'Lithuania, Vilnius': [25.19, 54.4],
  'Federal Government, Perm': [56.1, 58.01],
  'Liechtenstein,Vaduz': [9.31, 47.09],
  'Luxembourg,Luxembourg': [6.08, 49.37],
  'Rwanda, Kigali': [30.05, -1.59],
  'Romania, Bucharest': [26.1, 44.23],
  'Madagascar, Antananarivo': [47.31, -18.55],
  'Malta, Valletta': [14.31, 35.53],
  'Maldives,Male': [73.28, 4.1],
  'Malawi, Lilongwe': [33.49, -13.58],
  'Malaysia, Kuala Lumpur': [101.42, 3.08],
  'Mali, Bamako': [-7.59, 12.4],
  'Macedonia, Skopje': [21.3, 41.35],
  'Marshall Islands, Majuro': [171.12, 7.09],
  'Mauritius, Port Louis': [57.29, -20.09],
  'Mauritania, Nouakchott': [-15.58, 18.09],
  'United States, Washington': [-77.02, 38.53],
  'USA, Wichita': [-97.2, 37.43],
  'USA,Los Angeles': [-118.22, 34.05],
  'United States, Louisville': [-85.48, 38.13],
  'USA, Madison': [-89.23, 43.05],
  'USA,Memphis': [-90, 35.05],
  'USA,Mesa': [-111.44, 33.25],
  'USA,Miami': [-80.13, 25.47],
  'USA,Midland': [-102.05, 32],
  'USA, Midway': [-177.22, 28.13],
  'United States, Milwaukee Bucks': [-87.57, 43.03],
  'United States, Minneapolis': [-93.15, 45],
  'Mobile, USA': [-88.05, 30.4],
  'America, Montgomery': [-86.2, 32.22],
  'USA,Montpellier': [-72.34, 44.16],
  'USA, Nashville': [-86.46, 36.1],
  'USA,New Orleans': [-90.05, 29.58],
  'United States, New York': [-73.55, 40.44],
  'USA,Newark': [-74.1, 40.43],
  'America, Norm': [-165.24, 64.32],
  'United States, Norfolk': [-76.18, 36.54],
  'United States, Oakland': [-122.13, 37.47],
  'USA, Oklahoma City': [-97.32, 35.29],
  'USA, Orlando': [-81.22, 28.3],
  'USA, Pago Pago': [-170.42, -14.16],
  'USA,Pensacola': [87.12, 30.3],
  'USA, Philadelphia': [-75.09, 40],
  'USA,Phoenix': [-112.05, 33.3],
  'United States, Peel': [-100.2, 44.22],
  'United States, Pittsburgh': [-80, 40.26],
  'United States, Portland': [-122.39, 45.31],
  'United States, Providence': [-71.25, 41.49],
  'United States, Ruili': [-78.39, 35.47],
  'rapid city, usa': [-103.13, 44.05],
  'United States, Richmond': [-77.28, 37.32],
  'USA, Riverside': [-117.23, 33.56],
  'USA, Rochester': [-77.37, 43.12],
  'USA, Sacramento': [-121.28, 38.34],
  'USA,Saipan': [145.45, 15.12],
  'America, Salem': [-123.02, 44.56],
  'United States, Salt Lake City': [-111.52, 40.46],
  'United States, San Antonio': [-98.3, 29.25],
  'USA,San Bernardino': [-117.17, 34.06],
  'United States, San Diego': [-117.09, 32.43],
  'United States, San Francisco': [-122.26, 37.46],
  'United States, San Jose': [-121.53, 37.2],
  'USA,Santa Fe': [-105.57, 35.4],
  'USA,Seattle': [-122.2, 47.38],
  'USA, Sioux Falls': [-96.42, 43.34],
  'United States, St.Louis': [-90.15, 38.4],
  'United States, Sao Paulo': [-93.1, 45],
  'United States, St. Petersburg': [-82.38, 27.45],
  'USA, Stanley': [-57.52, -51.42],
  'United States, Stockton': [-121.18, 37.58],
  'USA,Tampa': [-82.38, 27.58],
  'USA,Toledo': [-83.35, 41.4],
  'USA,Topeka': [-95.41, 39.02],
  'United States, Trenton': [-74.46, 40.13],
  'United States, Tucson': [-110.58, 32.13],
  'America, Analaska': [-166.43, 53.51],
  'USA,Virginia Shores': [-76.02, 36.44],
  'USA, Adak Island': [176.39, 51.52],
  'USA, Akron': [-81.31, 41.04],
  'United States, Albany': [-73.47, 42.4],
  'United States, Albuquerque': [-106.4, 35.07],
  'Anaheim, USA': [-117.52, 33.5],
  'USA, Anchorage': [-149.52, 61.13],
  'Arlington, USA': [-97.07, 32.41],
  'USA, Atlanta': [-84.25, 33.46],
  'America, Oakstar': [-69.46, 44.19],
  'Aurora, USA': [-104.43, 39.42],
  'Austin, USA': [-97.44, 30.17],
  'USA,Baltimore': [-76.37, 39.17],
  'United States, Baton Rouge': [-91.08, 30.27],
  'United States, Billings': [-108.27, 45.47],
  'USA,Birmingham': [-86.55, 33.3],
  'USA, Bismarck': [-100.47, 46.49],
  'USA,Boise': [-116.13, 43.37],
  'USA,Boston': [-71.05, 42.19],
  'United States, Buffalo': [-78.55, 42.52],
  'Carson City, USA': [-118.46, 39.1],
  'USA,Charleston': [-81.4, 38.23],
  'America, Charlotte': [-80.5, 35.05],
  'United States, Cheyenne': [-104.49, 41.08],
  'United States,Chicago': [-87.41, 41.51],
  'USA,Cincinnati': [-84.3, 39.1],
  'USA,Cleveland': [-81.41, 41.3],
  'United States, Columbia': [-81, 34.01],
  'USA, Columbus': [-82.59, 39.59],
  'Concord, USA': [-71.32, 43.13],
  'USA,Dallas': [-96.47, 32.47],
  'United States, Denver': [-104.59, 39.43],
  'USA, Des Moines': [-93.38, 41.36],
  'United States, Detroit': [-83.05, 42.23],
  'USA, Dover': [-75.32, 39.1],
  'United States, El Paso': [-106.29, 31.45],
  'United States, Fairbanks': [-147.43, 64.5],
  'United States, Fort Worth': [-97.2, 32.45],
  'USA,Frankfurt': [-84.52, 38.12],
  'United States, Fresno': [-119.45, 36.45],
  'United States, Guam': [144.4, 13.3],
  'USA,Harrisburg': [-76.53, 40.16],
  'United States, Hartford': [-72.41, 41.46],
  'America, Helena': [-112.02, 46.35],
  'United States, Honolulu': [-157.5, 21.19],
  'USA,Houston': [-95.23, 29.45],
  'United States, Indianapolis': [-86.08, 39.47],
  'USA,Jackson': [-90.11, 32.2],
  'USA,Jacksonville': [-81.4, 30.2],
  'United States, Jefferson City': [-92.11, 38.34],
  'United States, Jersey City': [-74.03, 40.42],
  'America, Juno': [-134.25, 58.18],
  'United States, Kansas City': [-94.33, 39.02],
  'United States, Knoxville': [-83.56, 35.58],
  'USA,Las Vegas': [-115.1, 36.1],
  'United States, Fayette': [-84.27, 38.02],
  'America, Lincoln': [-96.4, 40.49],
  'Little Rock, USA': [-92.19, 34.44],
  'USA, Long Beach': [-118.09, 33.47],
  'Gate of Montenegro, Podgorica': [19.28, 42.27],
  'Mongolia, Khovd': [90.45, 46.4],
  'Mongolia, Choibalsan': [114.3, 48.04],
  'Mongolia, Ulaanbaatar': [106.53, 47.55],
  'Bangladesh, Chittagong': [91.48, 22.2],
  'Bangladesh, Khulna': [89.34, 22.49],
  'Bangladesh, Dhaka': [90.24, 23.51],
  'Peru, Lima': [-76.55, -12.06],
  'Micronesia, Pohern': [158.1, 6.55],
  'Myanmar, Yangon': [96.09, 16.46],
  'Moldova, Chisinau': [28.5, 47],
  'Morocco, Rabat': [-6.51, 34.02],
  'Morocco, Tangier': [-5.45, 35.48],
  'Morocco,Casablanca': [-7.37, 33.36],
  'Monaco,Monaco': [7.25, 43.4],
  'Mozambique, Maputo': [32.35, -25.58],
  'Mexico, Guadalajara': [-103.21, 20.4],
  'Mexico, Chihuahua': [-106.05, 28.38],
  'Mexico,Cancun': [-86.51, 21.1],
  'Mexico, Aguascalientes': [-102.18, 21.51],
  'Mexico, Acapulco': [-99.56, 16.51],
  'Mexico, Veracruz': [-96.1, 19.11],
  'Mexico, Tijuana': [-117.01, 32.32],
  'Mexico, San Luis Potosi': [-100.59, 22.09],
  'Monterey, Mexico': [-100.2, 25.4],
  'Mexico,Mexico City': [-99.09, 19.28],
  'Mexico, Mexicali': [-115.27, 32.38],
  'Mexico, Merida': [-89.37, 20.58],
  'Mexico, Mazatlan': [-106.25, 23.13],
  'Mexico, Leon': [-101.42, 21.1],
  'Namibia, Windhoek': [17.06, -22.34],
  'South Africa, Pretoria': [28.11, -25.43],
  'South Africa, Port Elizabeth': [25.36, -33.57],
  'South Africa, Cape Town': [18.27, -33.55],
  'South Africa, Durban': [31.03, -29.53],
  'South Africa, Johannesburg': [27.54, -26.08],
  'Nepal, Kathmandu Restaurant': [85.19, 27.42],
  'Nicaragua, Managua': [-86.18, 12.06],
  'Niger, Niamey': [2.05, 13.32],
  'Nigeria, Abuja': [7.11, 9.12],
  'Nigeria, Lagos': [3.02, 6.35],
  'Nigeria,Nigeria Kano': [8.31, 12],
  'Nigeria, Lagos': [3.02, 6.35],
  'Niue, Alofi': [-169.55, -19.03],
  'Norway, Oslo': [10.41, 59.56],
  'Palau, Koror': [134.3, 7.3],
  'Portugal, Azores': [-28, 38.3],
  'Portugal,Porto': [-8.37, 41.09],
  'Portugal, Lisbon': [-9.05, 38.42],
  'Portugal, Funchal': [-16.54, 32.38],
  'Georgia, Tbilisi': [44.48, 41.43],
  'Japan, Osaka': [135.3, 34.4],
  'Japan, Okayama': [133.54, 34.4],
  'Japan, Sapporo': [141.21, 43.05],
  'Japan, Nagoya': [136.55, 35.1],
  'Japan, Fukuoka': [130.21, 33.39],
  'Japan, Sendai': [140.52, 38.16],
  'Japan, Yokohama': [139.39, 35.27],
  'Japan,Tokyo': [139.44, 35.41],
  'Japan, Hiroshima': [132.27, 34.23],
  'Japan, Kawasaki': [139.43, 35.32],
  'Japan, Kitakyushu': [130.49, 33.52],
  'Japan, Kobe': [135.1, 34.41],
  'Japan,Kyoto': [135.45, 35],
  'Sweden, Stockholm': [18, 59.23],
  'Switzerland, Lausanne': [6.39, 46.32],
  'Switzerland, Geneva': [6.04, 46.14],
  'Switzerland,Bern': [7.26, 46.57],
  'Switzerland, Zurich': [8.32, 47.22],
  'Switzerland, Basel': [7.36, 47.34],
  'El Salvador, Santa Ana': [-89.31, 14],
  'El Salvador, San Salvador': [-89.1, 13.4],
  'Samoa, Apia': [-171.45, -13.48],
  'Serbia, Belgrade': [20.28, 44.49],
  'Sierra Leone, Freetown': [-13.17, 8.3],
  'Senegal,Dakar': [-17.27, 14.38],
  'Cyprus,Nicosia': [33.23, 35.11],
  'Seychelles, Victoria': [55.28, -4.4],
  'Jeddah, Saudi Arabia': [39.1, 21.3],
  'Saudi Arabia, Riyadh': [46.44, 24.39],
  'Saudi Arabia, Mecca': [39.49, 21.26],
  'Sao Tome and Principe, Sao Tome': [6.44, 0.2],
  'Saint Lucia, Castries': [-60.59, 14.01],
  'San Marino,San Marino': [12.28, 43.55],
  'Saints and Nevis, Basseterre': [-62.43, 17.18],
  'Saint Vincent and the Grenadines, Kingston': [-61.14, 13.12],
  'Sri Lanka, Colombo': [79.52, 6.55],
  'Slovak Republic, Bratislava': [17.07, 48.09],
  'Slovenia, Ljubljana': [14.31, 46.03],
  'Mbabane, Swaziland': [31.08, -26.19],
  'Sudan, Khartoum': [32.36, 15.34],
  'Suriname, Paramaribo': [-55.14, 5.52],
  'Somalia, Mogadishu': [45.21, 2.02],
  'Solomon Islands, Honiara': [160.12, -9.32],
  'Tajikistan, Dushanbe': [68.51, 38.38],
  'Thailand, Khon Kaen': [102.5, 16.25],
  'Thailand,Bangkok': [100.29, 13.5],
  'Tanzania, Dar es Salaam': [39.18, -6.51],
  'Tanzania, Dodoma': [35.4, -6.1],
  "Tonga, Nuku'alofa": [-175.12, -21.07],
  'Trinidad and Tobago, Port of Spain': [-61.31, 10.38],
  'Tunisia,Tunisia': [10.1, 36.47],
  'Tuvalu, Funaforti': [179.13, -8.31],
  'Turkey, Ankara': [32.54, 40.02],
  'Turkey, Istanbul': [28.58, 41.02],
  'Turkey, Izmir': [27.09, 38.24],
  'Turkmenistan, Ashgabat': [-58.24, 37.58],
  'Vanuatu, Port Vila': [168.19, -17.44],
  'Guatemala, Guatemala': [-90.22, 14.38],
  'Venezuela,Caracas': [-66.58, 10.3],
  'Brunei, Bandar Seri Begawan': [114.58, 4.56],
  'Uganda, Kampala': [32.35, 0.19],
  'Ukraine, Kiev': [30.29, 50.28],
  'Ukraine, Odessa': [30.46, 46.3],
  'Uruguay, Montevideo': [-56.11, -34.53],
  'Uzbekistan, Tashkent': [69.13, 41.16],
  'West Bank, Bethlehem': [35.12, 31.42],
  'Spain, Madrid': [-3.42, 40.26],
  'Spain, Las Palmas': [-15.27, 28.08],
  'Spain, La Coruna': [-8.24, 43.22],
  'Spain, Córdoba': [-4.46, 37.53],
  'Spain, Barcelona': [2.06, 41.18],
  'Spain, Palma': [2.39, 39.26],
  'Western Sahara, Laayoune': [-13.12, 27.09],
  'Greece,Athens': [23.44, 38.02],
  'Greece, IKARIA Island': [25.15, 39.55],
  'Singapore,Singapore': [103.45, 1.22],
  'New Zealand, Chatham Island': [-176.35, -44],
  'New Zealand, Wellington': [174.47, -41.17],
  'New Zealand, Auckland': [174.45, -36.55],
  'Christchurch, New Zealand': [172.37, -43.32],
  'Hungary, Budapest': [19.15, 47.26],
  'Syria, Damascus': [36.19, 33.3],
  'Kingston, Jamaica': [-76.48, 17.58],
  'Communist Party of Armenia, Yerevan': [44.31, 40.1],
  'Yemen, Sanaa': [44.14, 15.23],
  'Yemen, Aden': [45, 12.5],
  'Iraq, Baghdad': [44.22, 33.14],
  'Iraq, Basra': [47.49, 30.3],
  'Iran, Isfahan': [51.4, 32.42],
  'Iran, Tehran': [51.3, 35.45],
  'Israel,Tel Aviv': [34.46, 32.05],
  'Israel,Jerusalem': [35.13, 31.47],
  'Italy, Turin': [7.4, 45.04],
  'Italy, Rome': [12.37, 41.52],
  'Italy,Venice': [12.2, 45.26],
  'Italy, Milan': [9.1, 45.28],
  'Italy, Naples': [14.14, 40.5],
  'India,Agra': [78, 27.09],
  'India,Varanasi': [83, 25.2],
  'India, Vadodara': [73.14, 22.19],
  'India, Trivandrum': [76.57, 8.3],
  'India, Surat': [72.54, 21.1],
  'India,Pune': [73.58, 18.34],
  'India, Patna': [85.12, 25.37],
  'India, New Delhi': [77.13, 28.37],
  'India, Nagpur': [79.12, 21.1],
  'India, Mumbai': [72.51, 18.56],
  'India, Madurai': [78.07, 9.55],
  'India, Rudhiana': [75.52, 30.56],
  'India, Lucknow': [80.54, 26.5],
  'India, Calcutta': [88.2, 22.34],
  'India, Kanpur': [80.14, 26.27],
  'India, Jaipur': [75.5, 26.53],
  'Indore, India': [75.54, 22.42],
  'India, Hyderabad': [78.26, 17.22],
  'India,Delingha': [77.14, 28.4],
  'India, Chennai': [80.18, 13.05],
  'India, Bhubanes': [85.5, 20.15],
  'India, Bangalore': [77.34, 12.58],
  'India, Ahmedabad': [72.4, 23.03],
  'India, Visakhapatnam': [83.24, 17.42],
  'Indonesia, Jayapura': [140.38, -2.28],
  'Indonesia, Surakarta': [110.5, -7.32],
  'Indonesia, Malang': [112.45, -7.59],
  'Indonesia, Ambon': [128.1, -4.5],
  'Indonesia, Balikpapan': [116.5, -1.15],
  'Indonesia, Bandung': [107.34, -6.57],
  'Indonesia, Manado': [124.58, 1.3],
  'Indonesia, Mataram': [116.07, -8.36],
  'Indonesia, Medan': [98.39, 3.35],
  'Indonesia, Semarang': [110.29, -6.58],
  'Indonesia, Denpasar': [115.14, -8.4],
  'Indonesia, Anglo-German': [121.4, -8.51],
  'Indonesia, New Karaya': [115.07, -8.06],
  'Indonesia, Surabaya': [112.45, -7.14],
  'Indonesia, Ternate': [127.23, 0.48],
  'Indonesia, Kupang': [123.38, -10.23],
  'Indonesia, Laba': [118.45, -8.27],
  'Indonesia, Jakarta': [106.45, -6.08],
  'Indonesia, Palembang': [104.5, -2.59],
  'UK,Gibraltar': [-5.22, 36.07],
  'United Kingdom, Glasgow': [-4.15, 55.52],
  'United Kingdom, Cardiff': [-3.11, 51.28],
  'United Kingdom, Belfast': [-5.57, 54.36],
  'England, St. Helier': [-2.07, 49.11],
  'UK,Birmingham': [-1.55, 52.3],
  'UK, Adamstown': [-130.05, -25.04],
  'United Kingdom, Edinburgh': [-3.13, 55.57],
  'UK,London': [-0.07, 51.3],
  'UK,Liverpool': [-3, 53.25],
  'UK, Blades': [-62.12, 16.46],
  'Georgetown, UK': [-81.23, 19.2],
  'United Kingdom,Road Town': [-64.3, 18.3],
  'Jordan, Amman': [35.56, 31.57],
  'Vietnam, Hanoi': [105.53, 21.01],
  'Vietnam, Ho Chi Minh': [106.43, 10.46],
  'Zambia, Lusaka': [28.14, -15.2],
  "Chad, N'Djamena": [14.59, 12.1],
  'Chile, Easter Island': [-109.2, -27.05],
  'Chile, Santiago': [-70.4, -33.26],
  'Central African Republic, Bangui': [18.37, 4.23],
}
