<template>
  <v-card class="h-full d-flex flex-column">
    <v-card-title class="text-xxl">{{ title }}</v-card-title>
    <v-card-text class="flex-1">
      <div
        v-for="(item, index) in list"
        :key="index"
        class="d-flex align-center"
        style="height: 20%"
      >
        <div
          style="max-width: 8rem; min-width: 8rem"
          class="text-overflow-hide text-right mr-3"
          v-show-tips
        >
          {{ item.title }}
        </div>
        <v-progress-linear
          :value="item.progress"
          :color="item.color"
          background-color="bg-body"
          rounded
          :height="6 | getRoundSize"
        ></v-progress-linear>
        <div style="width: 3rem" class="color-base mx-3">{{ item.count }}</div>
        <div style="width: 5rem">{{ item.progress }}%</div>
      </div>
    </v-card-text>
  </v-card>
</template>

<script>
export default {
  name: 'EventTable',
  props: {
    title: String,
    list: Array,
  },
  computed: {
    radioList() {
      return [
        {
          title: '发动机控制单元/变速器控制单元/网关',
          progress: 12.2,
          count: 87,
          color: '#533DF1',
        },
        {
          title: '移动应用',
          progress: 7.3,
          count: 75,
          color: '#40CD6E',
        },
        {
          title: '信息娱乐系统',
          progress: 5.7,
          count: 65,
          color: '#FBD82C',
        },
        {
          title: 'OBD接口-OBD',
          progress: 5.4,
          count: 43,
          color: '#44E2FE',
        },
        {
          title: 'IT网络',
          progress: 5.1,
          count: 32,
          color: '#1B84FF',
        },
      ]
    },
  },
}
</script>
