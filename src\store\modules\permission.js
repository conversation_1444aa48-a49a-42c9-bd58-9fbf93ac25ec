// import Layout from '@/views/Layout/Layout.vue'
import { isExternal } from '@/@core/utils/validation'
import { queryMenuListByRole } from '@/api/system/menu'
import { i18n } from '@/plugins/i18n'
import router from '@/router'

function myCamelCase(inputStr) {
  const re = /-(\w)/g

  return inputStr.replace(re, ($0, $1) => $1.toUpperCase())
}

function convertToMenu(permissions, parentIcon) {
  const menuList = []
  if (permissions && permissions.length > 0) {
    permissions.forEach(item => {
      if (item.isShow && item.uiType !== 'Button') {
        const menus = {
          id: item.id,
          title: item.name,
          code: item.menuCode,
          enTitle: item.englishName,
          action: item.iconCls,
          link: item.url,
          iconType: 'svg',
          to: isExternal(item.url) ? '' : item.name,
          icon: item.iconCls,
          parentIcon: parentIcon,
          uiType: item.uiType,
          badge:
            item.uiType === 'Catalogue' &&
            item.children &&
            item.children.filter(v => v.isShow).length,
        }
        if (item.children && item.children.length > 0) {
          const children = convertToMenu(item.children, item.iconCls)
          if (children && children.length > 0) {
            menus.children = children
          }
        }
        menuList.push(menus)
      }
    })
  }
  return menuList
}

function convertToRouter(permissions, level) {
  const routerList = []
  const layoutList = []
  if (permissions && permissions.length > 0) {
    permissions.forEach(item => {
      if (item.uiType !== 'Button') {
        if (item.uiType === 'Menu') {
          let router = mappingMeneToRouter(item)
          routerList.push(router)
        }
        if (
          item.uiType === 'Catalogue' ||
          (item.children && item.children.length > 0)
        ) {
          let routerArr = convertToRouter(item.children)
          routerList.push(...routerArr)
        }
      }
    })
  }
  return routerList
}

function mappingMeneToRouter(menu) {
  const r = {
    name: menu.name,
    path: menu.url || '/',
    hidden: !menu.isShow,
    iconCls: menu.iconCls,
    alwaysShow: true,
    meta: {
      code: menu.menuCode,
      icon: menu.iconCls,
      parentId: menu.parentId,
      id: menu.id,
      layout: [
        'vehiclePosture',
        'cloudPosture',
        'shareCanvas',
        'viewGrid',
      ].includes(menu.menuCode)
        ? 'blank'
        : menu?.layout || 'content',
      isDark: menu.isDark,
      title: menu.name,
      enTitle: menu.englishName,
      buttons: convertToButtons(menu.children),
      buttonInfo: convertToButtonInfo(menu.children),
      navActiveLink: !menu.isShow && menu.parentName,
    },
  }
  if (menu.component) {
    r.component = loadView(menu.component)
  }
  // else {
  //   r.component = () => import('@/components/router-view.vue')
  // }

  return r
}

function allButtons(permissions, arr = []) {
  if (permissions && permissions.length) {
    permissions.forEach(v => {
      if (v.uiType === 'Button') {
        arr.push(v.menuCode)
      }
      allButtons(v.children, arr)
    })
  }
  return arr
}

const loadView = component => {
  if (process.env.NODE_ENV === 'development') {
    return resolve => require([`@/views/${component}`], resolve)
  } else {
    return () => import(`@/views/${component}`)
  }
}

function convertToButtons(permissions) {
  let permissionList = []
  if (permissions && permissions.length > 0) {
    permissions.forEach(item => {
      if (item.uiType === 'Button') {
        permissionList.push(item.menuCode)
      }
      // if (item.children && item.children.length > 0) {
      //   let children = convertToButtons(item.children)
      //   permissionList = permissionList.concat(children)
      // }
    })
  }
  return permissionList
}

function convertToButtonInfo(permissions) {
  let permissionList = {}
  if (permissions && permissions.length > 0) {
    permissions.forEach(item => {
      if (item.uiType === 'Button') {
        permissionList[item.menuCode] = {
          title: item.name,
          enTitle: item.englishName,
        }
      }
      // if (item.children && item.children.length > 0) {
      //   let children = convertToButtons(item.children)
      //   permissionList = permissionList.concat(children)
      // }
    })
  }
  return permissionList
}

const permission = {
  namespaced: true,
  state: {
    userInfo: [],
    permissions: [],
    menus: [],
    buttons: [],
    routes: [],
    menuCodes: [],
  },
  mutations: {
    setUserInfo(state, userInfo) {
      state.userInfo = userInfo
    },
    setPermissions(state, permissions) {
      state.permissions = permissions
    },
    setMenus(state, menu) {
      state.menus = menu
    },
    setButtons(state, buttons) {
      state.buttons = buttons
    },
    setRoutes(state, routes) {
      state.routes = routes
    },
    setMenuCodes(state, menuCodes) {
      state.menuCodes = menuCodes
    },
  },
  actions: {
    async generateRoutes({ commit, state }) {
      return new Promise((resolve, reject) => {
        queryMenuListByRole()
          .then(res => {
            if (res.code === 200) {
              if (res.data.length === 0) {
                reject(i18n.t('global.grant'))
              }
              commit('setPermissions', res.data)
              commit('setMenus', convertToMenu(res.data))
              commit('setButtons', allButtons(res.data))
              let dynamicRoutes = convertToRouter(res.data, 1)
              let menuCodes = dynamicRoutes.map(v => v.meta?.code)
              commit('setMenuCodes', menuCodes)
              dynamicRoutes.push({
                path: '*',
                redirect: '/error-404',
                hidden: true,
              })
              commit('setRoutes', dynamicRoutes)
              dynamicRoutes.forEach(item => {
                router.addRoute(item)
              })
              resolve(dynamicRoutes)
            }
          })
          .catch(error => {
            reject(error)
          })
      })
    },

    clearPermission({ commit }) {
      commit('setPermissions', [])
      commit('setMenus', [])
      commit('setButtons', [])
    },

    clearUserInfo({ commit }) {
      commit('setUserInfo', {})
    },
  },
}

export default permission
