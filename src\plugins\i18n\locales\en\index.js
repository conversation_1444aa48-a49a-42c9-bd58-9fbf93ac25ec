import themeConfig from '@themeConfig'
import elementEnLocale from 'element-ui/lib/locale/lang/en'
import vuetifyEn from 'vuetify/lib/locale/en'

// 读取module文件夹下面所有文件并引入
const path = require('path')
const files = require.context('./module', false, /\.js$/)
const modules = {}
files.keys().forEach(key => {
  const name = path.basename(key, '.js')
  modules[name] = files(key).default || files(key)
})

export default {
  projectName: themeConfig.app.enTitle,
  $vuetify: vuetifyEn,

  ...elementEnLocale,

  ...modules,
}
