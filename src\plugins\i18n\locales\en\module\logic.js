const logic = {
  source: 'Source',
  signal: 'Signal',
  sub: 'Sub signal',
  function: 'Function',
  argument: 'Argument',
  operator: 'Operator',
  value: 'Value',
  formula: 'Formula',
  abnormal: 'Select abnormal signal',
  timeFrame: 'Time Frame',
  numberOfOccurrences: 'Number of occurrences',
  grammar: 'Grammar',
  description: 'Description',
  event: 'Event',
  input: 'Input',
  btn: {
    add: 'New',
    add1: 'New the variable',
    inset: 'Insert',
    validate: 'Validation expression',
  },
}
export default logic
