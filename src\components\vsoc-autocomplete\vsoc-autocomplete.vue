<template>
  <v-autocomplete v-model="list" v-bind="attrs" v-on="$listeners">
    <template v-slot:selection="{ item, index }">
      <v-chip color="primary" close @click:close="removeItem(index)">
        <v-badge
          v-if="item.assetGroupType === '0'"
          dot
          inline
          offset-x="10"
          :offset-y="-18"
          color="success"
          class="mr-1"
        ></v-badge>
        <span>{{ item.text }}</span>
      </v-chip>
    </template>
    <template v-slot:item="{ item, on, attrs }">
      <v-list-item v-bind="attrs" v-on="on">
        <v-list-item-action>
          <v-checkbox
            @click="activeBox(item, attrs)"
            v-bind="attrs"
            v-on="on"
          />
        </v-list-item-action>
        <v-list-item-content style="margin-top: -2px">
          <v-list-item-title>
            <v-badge
              v-if="item.assetGroupType === '0'"
              dot
              inline
              offset-x="10"
              :offset-y="-18"
              color="success"
              class="mr-1"
            ></v-badge>
            <span style="vertical-align: middle">{{ item.text }}</span>
          </v-list-item-title>
        </v-list-item-content>
      </v-list-item>
    </template>
  </v-autocomplete>
</template>

<script>
export default {
  name: 'VsocAutocomplete',
  inheritAttrs: false,
  props: {
    value: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    attrs() {
      return this.$attrs
    },
  },
  data() {
    return {
      list: [],
    }
  },
  watch: {
    value: {
      handler(val) {
        this.list = val
      },
      deep: true,
    },
  },
  methods: {
    activeBox(item, attrs) {
      this.list.push(item.text)
    },
    toggleItem(item) {
      const index = this.value.indexOf(item)
      if (index > -1) {
        this.value.splice(index, 1)
      } else {
        this.value.push(item)
      }
    },
    removeItem(index) {
      this.list.splice(index, 1)
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .v-list-item__title > ::v-deep .v-badge {
  margin-top: 0 !important;
}

::v-deep .v-application--is-ltr .v-list-item__action:first-child {
  margin-right: 0 !important;
}
::v-deep .v-input--selection-controls__ripple:before {
  opacity: 0 !important;
}

::v-deep .v-list-item__title > .v-badge {
  margin-top: 0;
}

::v-deep .v-list .v-list-item--active input,
::v-deep .v-input--selection-controls__input input {
  display: none;
}

::v-deep .v-input--selection-controls__input {
  display: flex;
  justify-content: center;
}
</style>
