<template>
  <div>
    <bread-crumb></bread-crumb>
    <v-card tile class="main-content">
      <v-card-text class="pa-0">
        <div class="d-flex justify-space-between align-center flex-row-reverse">
          <div class="d-flex align-center">
            <table-search
              :searchList="searchList"
              :searchQuery="query"
              @search="$_search"
            ></table-search>
          </div>
          <div class="d-flex justify-end align-center">
            <v-btn
              v-has:params-add
              elevation="0"
              color="primary"
              @click="$_add"
            >
              <span>
                {{ $t('action.add') }}
              </span>
            </v-btn>
            <v-btn
              v-has:params-refresh-cache
              class="ml-3"
              elevation="0"
              color="primary"
              :loading="loading"
              @click="$_refreshCache"
            >
              <span>
                {{ $t('action.refreshCache') }}
              </span>
            </v-btn>
          </div>
        </div>
        <v-data-table
          fixed-header
          :items-per-page="query.pageSize"
          item-key="code"
          :height="tableHeight"
          hide-default-footer
          :headers="headers"
          :items="tableData"
          class="table border-radius-xl mt-3 thead-light"
          :loading="tableLoading"
        >
          <template v-slot:item.propertyName="{ item }">
            <div v-show-tips style="width: 140px" class="text-overflow-hide">
              {{ item.propertyName | dataFilter }}
            </div>
          </template>
          <template v-slot:item.groupName="{ item }">
            <div v-show-tips style="width: 100px" class="text-overflow-hide">
              {{ item.groupName | dataFilter }}
            </div>
          </template>
          <template v-slot:item.propertyValue="{ item }">
            <div v-show-tips style="width: 260px" class="text-overflow-hide">
              {{ item.propertyValue | dataFilter }}
            </div>
          </template>
          <template v-slot:item.description="{ item }">
            <div v-show-tips style="width: 140px" class="text-overflow-hide">
              {{ item.description | dataFilter }}
            </div>
          </template>
          <template v-slot:item.actions="{ item }">
            <v-btn v-has:params-edit icon @click.stop="$_edit(item)">
              <vsoc-icon
                v-show-tips="$t('action.edit')"
                type="fill"
                icon="icon-bianji"
                class="action-btn"
                size="x-large"
              ></vsoc-icon>
            </v-btn>
            <v-btn
              v-has:params-del
              v-if="item.isDefault !== '0'"
              icon
              @click.stop="$_del(item)"
            >
              <vsoc-icon
                v-show-tips="$t('action.del')"
                type="fill"
                icon="icon-shanchu"
                class="action-btn"
                size="x-large"
              ></vsoc-icon>
            </v-btn>
          </template>
        </v-data-table>

        <!-- 分页器 -->
        <vsoc-pagination
          :page.sync="query.pageNum"
          :size.sync="query.pageSize"
          :total="tableDataTotal"
          @change-page="getTableData"
          @change-size="$_search"
        >
        </vsoc-pagination>
      </v-card-text>
    </v-card>
    <parameter-edit ref="refParamsEdit" :mode="drawMode" @save="$_search" />
  </div>
</template>
<script>
import { deleteProperty, getPropertiesList } from '@/api/system/parameter'

import VsocPagination from '@/components/VsocPagination.vue'

import breadCrumb from '@/components/bread-crumb/index'
import TableSearch from '@/components/TableSearch/index.vue'
import { activeColor, inactiveColor } from '@/plugins/systemColor'
import { setRemainingHeight } from '@/util/utils'
import parameterEdit from './parameterEdit.vue'
export default {
  name: 'ParameterIndex',
  components: {
    VsocPagination,
    breadCrumb,
    TableSearch,
    parameterEdit,
  },
  data() {
    return {
      activeColor,
      inactiveColor,
      // 分页参数
      query: {
        propertyName: '', //参数名称
        propertyKey: '', //参数键名
        isDefault: '', //系统内置（0是 1否）
        pageNum: 1, //当前页
        pageSize: 10, //每页多少条
      },
      tableLoading: true,
      tableDataTotal: 0,
      tableData: [],
      tableHeight: '34.5rem',
      drawMode: '',
      loading: false,
    }
  },
  created() {
    this.$_setTableHeight()
    this.$bus.$on('resize', this.$_setTableHeight)
    this.$_search()
  },
  computed: {
    searchList() {
      return [
        {
          type: 'input',
          value: 'propertyName',
          text: this.$t('parameter.headers.propertyName'),
        },
        {
          type: 'input',
          value: 'propertyKey',
          text: this.$t('parameter.headers.propertyKey'),
        },
      ]
    },
    headers() {
      return [
        {
          text: this.$t('parameter.headers.id'),
          value: 'id',
          width: '100px',
        },

        {
          text: this.$t('parameter.headers.propertyName'),
          value: 'propertyName',
          width: '140px',
        },
        {
          text: this.$t('parameter.headers.groupName'),
          value: 'groupName',
          width: '100px',
        },
        {
          text: this.$t('parameter.headers.propertyType'),
          value: 'propertyType',
          width: '100px',
        },
        {
          text: this.$t('parameter.headers.propertyKey'),
          value: 'propertyKey',
          width: '100px',
        },
        {
          text: this.$t('parameter.headers.propertyValue'),
          value: 'propertyValue',
          width: '260px',
        },
        {
          text: this.$t('parameter.headers.isDefault'),
          value: 'isDefaultName',
          width: '100px',
        },
        {
          text: this.$t('parameter.headers.description'),
          value: 'description',
          width: '140px',
        },
        {
          text: this.$t('global.createDate'),
          value: 'createDate',
          width: '160px',
        },
        {
          text: '',
          value: 'actions',
          sortable: false,
          width: '120',
        },
      ]
    },
  },
  beforeDestroy() {
    this.$bus.$off('resize', this.$_setTableHeight)
  },
  methods: {
    //刷新缓存
    $_refreshCache() {
      this.loading = true
      setTimeout(() => {
        this.loading = false
        location.reload()
      }, 100)
    },
    //新增
    $_add() {
      this.drawMode = 'new'
      this.$refs['refParamsEdit'].open()
    },
    //编辑
    $_edit(item) {
      this.drawMode = 'edit'
      this.$refs['refParamsEdit'].open(item)
    },
    //删除
    async $_del(item) {
      this.$swal({
        title: this.$t('parameter.del.title'),
        text: this.$t('parameter.del.text', [item.propertyName]),
        icon: 'warning',
        showCancelButton: true,
        reverseButtons: true,
        confirmButtonText: this.$t('action.confirm'),
        cancelButtonText: this.$t('action.cancel'),
        customClass: {
          confirmButton: 'sweet-btn-primary',
        },
      }).then(async result => {
        if (result.isConfirmed) {
          try {
            const res = await deleteProperty({
              id: item.id,
            })
            if (res.code === 200) {
              this.$notify.info(
                'success',
                this.$t('global.hint.del', [this.$t('parameter.currentTitle')]),
              )
            }
            this.$_search()
          } catch (e) {
            console.error(`删除参数设置错误：${e}`)
          }
        }
      })
    },
    $_setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = setRemainingHeight(
          this.$store.state.global.subMarginFn,
        )
      })
    },
    $_search() {
      this.query.pageNum = 1
      this.getTableData()
    },
    // 加载表格数据
    async getTableData() {
      this.tableLoading = true
      try {
        const res = await getPropertiesList(this.query)
        this.tableDataTotal = res.data.total
        this.tableData = res.data.records
      } catch (e) {
        console.error(`获取参数设置错误：${e}`)
      } finally {
        this.tableLoading = false
      }
    },
  },
}
</script>
