<template>
  <div class="d-flex align-center">
    <div>{{ $t('infer.expression.every') }}</div>
    <div style="width: 370px">
      <v-select
        v-model="weeks"
        class="mx-2"
        color="primary"
        dense
        outlined
        solo
        multiple
        chips
        :items="weekOptions"
        hide-details
        item-text="label"
        item-value="value"
        @change="emitChange"
        :menu-props="{ offsetY: true }"
      >
        <template v-slot:selection="{ item, index }">
          <v-chip
            v-if="index <= 2"
            color="primary"
            class="chip-box"
            close
            label
            size="x-small"
            @click:close="removeTags(index)"
          >
            <span class="text-caption">{{ item.label }}</span>
          </v-chip>
          <span v-if="index === 3" class="grey--text text-caption">
            (+{{ weeks.length - 3 }})
          </span>
        </template>
      </v-select>
    </div>
    <!-- <el-select
      v-model="weeks"
      class="mx-2"
      multiple
      filterable
      style="width: 30rem"
      @change="emitChange"
    >
      <el-option
        v-for="item in weekOptions"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select> -->
    <!-- <div>的</div> -->
    <v-icon size="1.25rem">mdi-clock-outline</v-icon>
    <div style="width: 100px">
      <v-select
        v-model="hour"
        class="mx-2"
        color="primary"
        dense
        outlined
        solo
        :items="hourOptions"
        item-text="label"
        item-value="value"
        hide-details
        @change="emitChange"
        :menu-props="{ offsetY: true }"
      >
      </v-select>
    </div>
    <!-- <el-select
      v-model="hour"
      class="mx-2"
      filterable
      style="width: 6rem"
      @change="emitChange"
    >
      <el-option
        v-for="item in hourOptions"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select> -->
    <div>{{ $t('infer.expression.hour') }}</div>
    <div style="width: 100px">
      <v-select
        v-model="minute"
        class="mx-2"
        color="primary"
        dense
        outlined
        disabled
        solo
        :items="minuteOptions"
        item-text="label"
        item-value="value"
        hide-details
        @change="emitChange"
        :menu-props="{ offsetY: true }"
      >
      </v-select>
    </div>
    <!-- <el-select
      v-model="minute"
      class="mx-2"
      filterable
      style="width: 6rem"
      @change="emitChange"
    >
      <el-option
        v-for="item in minuteOptions"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select> -->
    <div>{{ $t('infer.expression.minute') }}</div>
  </div>
</template>

<script>
export default {
  name: 'CronWeek',
  data() {
    return {
      weeks: ['MON'],
      hour: 0,
      minute: 0,
    }
  },
  computed: {
    weekOptions() {
      return [
        {
          value: 'SUN',
          label: '周天',
        },
        {
          value: 'MON',
          label: '周一',
        },
        {
          value: 'TUE',
          label: '周二',
        },
        {
          value: 'WED',
          label: '周三',
        },
        {
          value: 'THU',
          label: '周四',
        },
        {
          value: 'FRI',
          label: '周五',
        },
        {
          value: 'SAT',
          label: '周六',
        },
      ]
    },
    hourOptions() {
      return Array.from(Array(24), (_, i) => {
        if (i < 10) {
          return {
            value: i,
            label: `0${i}`,
          }
        }

        return {
          value: i,
          label: i,
        }
      })
    },
    minuteOptions() {
      return Array.from(Array(60), (_, i) => {
        if (i < 10) {
          return {
            value: i,
            label: `0${i}`,
          }
        }

        return {
          value: i,
          label: i,
        }
      })
    },
    cronExp() {
      if (this.weeks.length === 0) {
        return `0 ${this.minute} ${this.hour} ? * *`
      }

      return `0 ${this.minute} ${this.hour} ? * ${this.weeks.join(',')}`
    },
  },
  methods: {
    init(value) {
      const tempArr = value.split(' ')
      this.minute = Number(tempArr[1])
      this.hour = Number(tempArr[2])
      if (tempArr[5] === '*') {
        this.weeks = []
      } else {
        const weekArr = tempArr[5].split(',')
        this.weeks = weekArr.filter(v => v !== '').map(v => v)
      }
    },
    emitChange() {
      this.$emit('change', this.cronExp)
    },
    removeTags(index) {
      this.weeks.splice(index, 1)
      this.$emit('change', this.cronExp)
    },
  },
}
</script>
<style lang="scss" scoped></style>
