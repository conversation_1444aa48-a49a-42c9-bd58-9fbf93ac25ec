<template>
  <div class="edit-page" ref="refEditPage">
    <bread-crumb
      v-if="breadShow"
      :popsName="popsName"
      :propsBreadList="propsBreadList"
    >
      <div
        v-if="ticketId"
        slot="left"
        class="ml-6 primary--text text-content d-flex align-center"
      >
        <span>{{ ticketId }}</span>
        <v-btn v-show-tips="$t('action.copy')" icon small>
          <i
            v-copy="ticketId"
            class="iconfont icon-fuzhi text-root primary--text"
          ></i>
        </v-btn>
      </div>
      <slot name="BreadBtn"></slot>
    </bread-crumb>
    <div>
      <slot name="Content"></slot>
    </div>
    <v-card
      tile
      class="edit-center"
      :style="{
        padding: isUsePadding
          ? appContentWidth === 'full'
            ? '0 16px'
            : '0 96px'
          : '0px',
        height: `calc(100vh - ${topHeaderHeight}px)`,
      }"
    >
      <slot></slot>
    </v-card>
    <!-- 回到顶部 -->
    <v-fab-transition>
      <v-btn
        v-show="backTopShow"
        color="primary"
        fab
        small
        dark
        bottom
        right
        fixed
        elevation="3"
        @click="$vuetify.goTo(0, { container: '.edit-center' })"
      >
        <v-icon> mdi-chevron-up</v-icon>
      </v-btn>
    </v-fab-transition>
  </div>
</template>
<script>
import breadCrumb from '@/components/bread-crumb/index'
import useAppConfig from '@core/@app-config/useAppConfig'

export default {
  props: {
    propsBreadList: {
      type: Array,
      default: () => [],
    },
    breadShow: {
      type: Boolean,
      default: true,
    },
    popsName: {
      type: String,
      default: '',
    },
    isTopShow: {
      type: Boolean,
      default: false,
    },
    ticketId: {
      type: String,
      default: '',
    },
    isUsePadding: {
      type: Boolean,
      default: true,
    },
    contentHeight: {
      type: Number,
      default: 0,
    },
  },
  computed: {
    topHeaderHeight() {
      if (Number(this.contentHeight) > 0) {
        return (
          this.$store.getters['global/getTopHeaderHeight'] +
          this.contentHeight -
          12
        )
      } else {
        return this.$store.getters['global/getTopHeaderHeight']
      }
    },
    appContentWidth() {
      return useAppConfig().appContentWidth.value
    },
  },
  components: {
    breadCrumb,
  },
  data() {
    return {
      backTopShow: false,
      // appContentWidth: useAppConfig().appContentWidth.value,
    }
  },
  mounted() {
    document
      .querySelector('.edit-center')
      .addEventListener('scroll', this.handleScroll)
  },
  beforeDestroy() {
    if (document.querySelector('.edit-center')) {
      document
        .querySelector('.edit-center')
        .removeEventListener('scroll', this.handleScroll)
    }
  },
  methods: {
    handleScroll(event) {
      //滚动条在该元素中与该元素顶部的距（滚动距离）
      let scrollTop = event.target.scrollTop
      if (scrollTop > 900) {
        this.backTopShow = true
      } else {
        this.backTopShow = false
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.edit-page {
  .edit-center {
    // background: #fff;
    overflow-y: auto;
    // height: calc(100vh - 160px);
    // padding-left: 96px;
    // padding-right: 96px;
  }
}
</style>
