<template>
  <v-card class="pl-2 pr-0 d-flex flex-column">
    <v-card-title class="pb-0">
      <span class="font-weight-semibold">{{
        $t('workbench.quickData.title')
      }}</span>
      <v-spacer></v-spacer>
      <v-btn
        icon
        class="text--primary"
        @click="isShow = true"
        v-show-tips="$t('workbench.quickData.configMenus')"
      >
        <vsoc-icon type="fill" icon="icon-qiehuan"></vsoc-icon>
      </v-btn>
    </v-card-title>
    <v-card-text
      class="d-flex flex-wrap justify-center pb-1"
      style="height: 38%"
    >
      <v-row
        v-if="!selectMenu || selectMenu.length === 0"
        align="center"
        justify="center"
      >
        {{ $t('$vuetify.noDataText') }}
      </v-row>
      <v-row v-else no-gutters align="center">
        <v-col
          cols="4"
          v-for="(item, index) in selectMenu"
          :key="`${index}menu`"
          class="text-center"
        >
          <v-chip class="pa-3 chip chip-box" @click="goMenu(item.menuName)">
            <vsoc-icon size="x-large" :icon="item.iconCls"></vsoc-icon>
          </v-chip>
          <div class="mt-2">
            {{ $generateName(item.menuName, item.englishName) }}
          </div>
        </v-col>
      </v-row>
    </v-card-text>
    <v-card-title class="pb-3 pt-2">
      <span class="font-weight-semibold">{{
        $t('workbench.quickData.message')
      }}</span>
      <v-spacer></v-spacer>
      <v-btn
        icon
        class="text--primary"
        @click="goMessage"
        v-show-tips="$t('workbench.quickData.lookMore')"
      >
        <vsoc-icon type="fill" icon="icon-gengduoqita"></vsoc-icon>
      </v-btn>
    </v-card-title>
    <v-card-text class="flex-1 pb-1 d-flex flex-column">
      <template v-if="quickData.myMessages.length > 0">
        <div
          v-for="(message, index) in quickData.myMessages"
          :key="index"
          class="d-flex flex-fill"
        >
          <v-chip
            v-if="$toItem(messageTypeEnum, message.messageType)"
            label
            small
            class="mr-2"
            :class="`chip-${message.messageType}`"
          >
            {{ $toItem(messageTypeEnum, message.messageType).text2 }}
          </v-chip>

          <p class="text-ml color-base text-overflow-hide flex-1 mb-0">
            {{ message.title }}
          </p>
          <span class="color-base ml-1 text-base"
            >{{ message.createDate | toDate('yyyy-MM-dd') }}
          </span>
        </div>
      </template>
      <v-row v-else justify="center" align="center">
        <v-img
          max-width="40%"
          max-height="50%"
          contain
          :src="require('@/assets/images/tree-empty.png')"
        ></v-img>
        <v-col style="margin-top: -5rem" cols="12">
          <div class="no-text text-center">
            {{ $t('notices.hint.nodata') }}
          </div>
        </v-col>
      </v-row>
    </v-card-text>
    <vsoc-drawer
      :title="$t('workbench.quickData.title')"
      v-model="isShow"
      @click:cancel="isShow = false"
      @click:confirm="onConfirm"
    >
      <v-form>
        <v-row class="pl-2 pr-2 mx-0 my-0">
          <v-col cols="12" class="px-0 py-0">
            <el-tree
              ref="elTree"
              :labelText="$t('menu.headers.name')"
              class="mt-n2"
              :organizationFlag="false"
              :showCheckbox="true"
              :treeData="menuList"
              :default-props="{
                children: 'children',
                label: 'title',
              }"
              :expandFlag="false"
              :isIcon="true"
            >
            </el-tree>
            <!-- @check="onSelect" -->
          </v-col>
        </v-row>
      </v-form>
      <!-- <div>
        <v-menu
          bottom
          :close-on-content-click="false"
          content-class="bg-white mx-2"
          transition="slide-y-transition"
          offset-y
          auto
        >
          <template v-slot:activator="{ on, attrs }">
            <v-text-field
              v-model="treeSelectName"
              :label="$t('workbench.quickData.menu')"
              color="primary"
              v-on="on"
              v-bind="attrs"
              outlined
              hide-details
              dense
            >
            </v-text-field>
          </template>

          <v-treeview
            v-model="treeSelect"
            return-object
            color="primary"
            item-key="id"
            :item-text="$i18n.locale === 'en' ? 'enTitle' : 'title'"
            selectable
            :items="menuList"
            selection-type="leaf"
            @input="onSelect"
          ></v-treeview>
        </v-menu>
      </div> -->
    </vsoc-drawer>
  </v-card>
</template>

<script>
import VsocDrawer from '@/components/VsocDrawer.vue'
import VsocIcon from '@/components/VsocIcon.vue'
import elTree from '@/components/el-tree/index'
import { getLocalStorage, setLocalStorage } from '@/util/localStorage'
export default {
  props: {
    quickData: {
      type: Object,
      default() {
        return {
          // selectMenu: [],
          myMessages: [],
        }
      },
    },
  },
  data() {
    return {
      isShow: false,
      treeSelect: [],
      newMenuList: [],
    }
  },
  computed: {
    selectMenu() {
      if (this.newMenuList && this.newMenuList.length > 0) {
        return this.newMenuList
      }
      // computed无法观察localStorage
      const jsonList = JSON.parse(getLocalStorage('quickEntry'.toUpperCase()))
      if (jsonList && jsonList.length > 0) {
        return jsonList
      }

      return this.loadMyMenus(this.menuList)
    },
    treeSelectName() {
      return this.treeSelect
        .map(v => (this.$i18n.locale === 'en' ? v.enTitle : v.title))
        ?.join(',')
    },
    menuList() {
      return this.$store.state.permission.menus
    },
    messageTypeEnum() {
      // return this.$store.state.enums.enums.MessageType
      return this.$store.getters['enums/getMessageType']
    },
  },
  components: { VsocIcon, VsocDrawer, elTree },

  methods: {
    loadMyMenus(list) {
      if (!list || list.length === 0) {
        return []
      }
      let myMenus = []
      for (const key in list) {
        let x = list[key]
        if (x.uiType === 'Menu') {
          let obj = {
            englishName: x.enTitle,
            iconCls: x.icon || x.parentIcon,
            menuName: x.title,
          }
          myMenus.push(obj)
          if (myMenus.length >= 6) {
            break
          }
        }
        myMenus.push(...this.loadMyMenus(x.children))
      }

      return myMenus.slice(0, 6)
    },
    onConfirm(cb) {
      try {
        this.onSelect()
        let newMenuList = this.treeSelect.map(item => {
          return {
            englishName: item.enTitle,
            iconCls: item.icon || item.parentIcon,
            menuName: item.title,
          }
        })

        // localStorage.setItem(
        //   'quickEntry'.toUpperCase(),
        //   JSON.stringify(newMenuList),
        // )
        setLocalStorage('quickEntry'.toUpperCase(), JSON.stringify(newMenuList))
        this.newMenuList = newMenuList
        cb()
        this.isShow = false
      } catch (err) {
        console.log('配置报错', err)
        cb(false, true)
      }
    },
    onSelect() {
      const list = this.$refs.elTree.$refs.tree.getCheckedNodes(true, true)
      if (list.length > 6) {
        this.treeSelect = list.slice(0, 6)
        this.$notify.info('warning', this.$t('workbench.quickData.hint'))
        return
      }
      this.treeSelect = list
    },
    goMenu(menu) {
      this.$router.push({ name: menu })
    },
    goMessage() {
      this.$router.push({ name: '我的消息' })
    },
  },
}
</script>
<style scoped lang="scss">
.chip {
  height: auto;
  border-radius: 50%;
}
$chip-color: (
  0: #da1f1f,
  1: #2940cb,
  2: #0eaa9f,
);
@each $name, $val in $chip-color {
  .chip-#{$name} {
    color: $val;
    background: rgba($val, 10%) !important;
    border-color: rgba($val, 10%) !important;
  }
}
</style>
