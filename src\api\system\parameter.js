import { request, vsocPath } from '../../util/request'

// 获取参数设置列表
export const getPropertiesList = function (params) {
  return request({
    url: `${vsocPath}/configProperty/properties`,
    method: 'post',
    data: params,
  })
}

// 获取所有组名
export const queryGroupNames = function (params) {
  return request({
    url: `${vsocPath}/configProperty/queryGroupNames`,
    method: 'post',
    data: params,
  })
}

// 新增参数设置
export const addProperty = function (params) {
  return request({
    url: `${vsocPath}/configProperty/addProperty`,
    method: 'post',
    data: params,
  })
}

// 修改参数设置
export const updateProperty = function (params) {
  return request({
    url: `${vsocPath}/configProperty/updateProperty`,
    method: 'post',
    data: params,
  })
}

// 删除参数设置
export const deleteProperty = function (params) {
  return request({
    url: `${vsocPath}/configProperty/deleteProperty`,
    method: 'post',
    data: params,
  })
}
