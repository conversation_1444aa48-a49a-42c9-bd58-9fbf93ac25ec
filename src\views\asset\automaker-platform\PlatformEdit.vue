<template>
  <edit-page :popsName="mode === 'add' ? '新增平台' : '编辑平台'">
    <div slot="BreadBtn">
      <v-btn
        color="primary"
        elevation="0"
        class="me-2"
        :loading="confirmLoading"
        @click="onConfirm"
      >
        保存
      </v-btn>
      <v-btn color="grey" elevation="0" outlined @click="onCancel">
        取消
      </v-btn>
    </div>

    <div class="edit-center-box pb-9">
      <v-form ref="form" v-model="valid">
        <!-- 基础信息 -->
        <div class="text-title color-base font-weight-semibold-light mb-4 mt-6">
          基础信息
          <span
            class="pl-2 text-caption text--secondary"
            v-if="mode === 'edit'"
            >{{ formData.id }}</span
          >
        </div>
        <div class="px-9">
          <v-row>
            <!-- 第一行：平台名称、平台编码 -->
            <v-col class="mr-6">
              <v-text-field
                v-model="formData.platform_name"
                label="平台名称"
                color="primary"
                :rules="[rules.required]"
                required
              ></v-text-field>
            </v-col>
            <v-col>
              <v-text-field
                v-model="formData.platform_code"
                label="平台编码"
                color="primary"
                :rules="[rules.required]"
                required
              ></v-text-field>
            </v-col>
          </v-row>
          <v-row>
            <!-- 第二行：备案号、所属车企 -->
            <v-col class="mr-6">
              <v-text-field
                v-model="formData.registration_number"
                label="备案号"
                color="primary"
                :rules="[rules.required]"
                required
              ></v-text-field>
            </v-col>
            <v-col>
              <v-select
                v-model="formData.automaker_id"
                :items="automakerOptions"
                item-text="automaker_name"
                item-value="id"
                label="所属车企"
                :rules="[rules.required]"
                required
                :loading="automakerLoading"
              ></v-select>
            </v-col>
          </v-row>

          <v-row>
            <!-- 第三行：平台类型、定级级别 -->
            <v-col class="mr-6">
              <v-select
                v-model="formData.platform_type"
                :items="platformTypeOptions"
                item-text="text"
                item-value="value"
                label="平台类型"
                multiple
                chips
                :rules="[rules.required]"
                required
              >
                <template v-slot:selection="{ item, index }">
                  <v-chip
                    v-if="index < 2"
                    small
                    :color="item.color"
                    text-color="white"
                  >
                    {{ item.text }}
                  </v-chip>
                  <span v-if="index === 2" class="grey--text text-caption">
                    (+{{ formData.platform_type.length - 2 }} 其他)
                  </span>
                </template>
              </v-select>
            </v-col>
            <v-col>
              <v-select
                v-model="formData.classification_level"
                :items="classificationOptions"
                item-text="text"
                item-value="value"
                label="定级级别"
                :rules="[rules.required]"
                required
              ></v-select>
            </v-col>
          </v-row>
          <v-row>
            <v-col class="mr-6">
              <!-- 第四行：监管状态 -->
              <v-select
                v-model="formData.is_supervise"
                label="已监管"
                color="primary"
                :items="superviseOptions"
                item-text="text"
                item-value="value"
                :rules="[rules.required]"
                required
              ></v-select>
            </v-col>
            <v-col>
              <!-- 空列，保持布局对称 -->
            </v-col>
          </v-row>

          <!-- 描述（单独一行，因为是多行文本） -->
          <v-textarea
            v-model="formData.description"
            label="描述"
            :rows="3"
            color="primary"
            outlined
          ></v-textarea>
        </div>

        <!-- 平台LOGO上传 -->
        <!-- <div class="text-title color-base font-weight-semibold-light mb-5 mt-8">
          平台LOGO
          <v-btn
            icon
            small
            class="ml-2"
            @click="showLogoTipsDialog = true"
            v-show-tips="'查看上传要求'"
          >
            <v-icon size="18" color="grey">mdi-information-outline</v-icon>
          </v-btn>
        </div>
        <div class="px-9">
          <div class="d-flex justify-start">
            <el-upload
              ref="logoUpload"
              class="logo-uploader"
              :action="vsocPath + '/file/upload'"
              :headers="headers"
              :show-file-list="false"
              :before-upload="beforeLogoUpload"
              accept=".jpg,.jpeg,.png,.svg"
            >
              <div v-if="formData.picture_code" class="logo-preview">
                <img
                  :src="formData.picture_code"
                  alt="LOGO"
                  class="logo-image"
                />
                <div class="logo-overlay">
                  <v-icon color="white" size="24">mdi-camera</v-icon>
                </div>
              </div>
              <div v-else class="logo-placeholder">
                <v-icon size="28" color="primary">mdi-plus</v-icon>
                <div
                  class="mt-1 text-caption text--secondary"
                  style="font-size: 10px"
                >
                  点击上传
                </div>
              </div>
            </el-upload>
          </div>
        </div> -->

        <!-- LOGO上传要求Dialog -->
        <!-- <v-dialog v-model="showLogoTipsDialog" max-width="400">
          <v-card>
            <v-card-title class="text-h6">
              LOGO上传要求
              <v-spacer></v-spacer>
              <v-btn icon @click="showLogoTipsDialog = false">
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </v-card-title>
            <v-card-text>
              <div class="upload-requirements">
                <div class="requirement-item mb-3">
                  <v-icon size="20" color="success" class="mr-2"
                    >mdi-check-circle</v-icon
                  >
                  <span>支持 JPG、JPEG、PNG 格式</span>
                </div>
                <div class="requirement-item mb-3">
                  <v-icon size="20" color="success" class="mr-2"
                    >mdi-check-circle</v-icon
                  >
                  <span>文件大小不超过 150KB</span>
                </div>
                <div class="requirement-item mb-3">
                  <v-icon size="20" color="success" class="mr-2"
                    >mdi-check-circle</v-icon
                  >
                  <span>建议尺寸：200x200像素</span>
                </div>
                <div class="requirement-item">
                  <v-icon size="20" color="info" class="mr-2"
                    >mdi-lightbulb-outline</v-icon
                  >
                  <span>建议使用透明背景的PNG格式</span>
                </div>
              </div>
            </v-card-text>
          </v-card>
        </v-dialog> -->
      </v-form>
    </div>
  </edit-page>
</template>

<script>
import {
  addAutomakerPlatform,
  editAutomakerPlatform,
  getAutomakerPlatformDetail,
  getAutomakerListForSelect,
} from '@/api/asset/automaker-platform'
import editPage from '@/components/EditPage.vue'
import { vsocPath } from '@/util/request'
import { uploadImgToBase64 } from '@/util/utils'

export default {
  name: 'PlatformEdit',
  components: {
    editPage,
  },
  data() {
    return {
      vsocPath,
      valid: false,
      confirmLoading: false,
      mode: 'add', // add 或 edit
      formData: {
        id: '',
        platform_name: '',
        platform_code: '',
        registration_number: '',
        description: '',
        platform_type: [],
        //picture_code: '',
        is_supervise: '',
        classification_level: '',
        automaker_id: '',
      },
      rules: {
        required: value => !!value || '此字段为必填项',
      },
      // 平台类型选项
      platformTypeOptions: [
        { value: '0', text: '联网车辆监控管理平台', color: 'primary' },
        { value: '1', text: '车联网信息服务平台', color: 'success' },
        { value: '2', text: '车联网OTA升级诊断服务平台', color: 'info' },
      ],
      // 已监管选项
      superviseOptions: [
        { text: '是', value: '0' },
        { text: '否', value: '1' },
      ],
      // 定级级别选项
      classificationOptions: [
        { text: '1级', value: '1' },
        { text: '2级', value: '2' },
        { text: '3级', value: '3' },
      ],
      // 车企选项
      automakerOptions: [],
      automakerLoading: false,
      showLogoTipsDialog: false,
      logoUploading: false,
    }
  },
  computed: {
    headers() {
      return this.$store.getters['global/getFileHeaders']
    },
  },
  created() {
    this.initPage()
  },
  methods: {
    async initPage() {
      // 获取路由参数
      const { id } = this.$route.query
      if (id) {
        this.mode = 'edit'
        await this.loadPlatformDetail(id)
      }

      // 加载车企列表
      await this.loadAutomakerList()
    },

    // 加载车企列表
    async loadAutomakerList() {
      try {
        this.automakerLoading = true
        const res = await getAutomakerListForSelect()

        if (res && res.code === 200 && res.data && Array.isArray(res.data)) {
          // 处理接口返回的数据，转换字段名以匹配前端显示
          this.automakerOptions = res.data.map(item => ({
            id: item.id,
            automaker_name: item.automakerName,
          }))
        } else if (res && res.code !== 200) {
          // 接口返回错误码
          const errorMsg = res.msg || '加载车企列表失败'
          this.$notify.info('error', errorMsg)
          this.automakerOptions = []
        } else {
          // 数据为空或格式异常
          this.automakerOptions = []
          if (!res?.data || res.data.length === 0) {
            this.$notify.info('info', '暂无车企数据')
          }
        }
      } catch (error) {
        const errorMsg =
          error?.message || error || '加载车企列表失败，请稍后重试'
        this.$notify.info('error', errorMsg)
        this.automakerOptions = []
      } finally {
        this.automakerLoading = false
      }
    },

    // 加载平台详情（编辑模式）
    async loadPlatformDetail(id) {
      try {
        const res = await getAutomakerPlatformDetail({ id })

        if (res && res.code === 200 && res.data) {
          // 处理接口返回的数据，转换字段名以匹配前端表单
          this.formData = {
            id: res.data.id,
            platform_name: res.data.platformName,
            platform_code: res.data.platformCode,
            registration_number: res.data.registrationNumber,
            description: res.data.description,
            platform_type: res.data.platformTypes || [], // 平台类型数组
            //picture_code: res.data.pictureCode,
            is_supervise: res.data.isSupervise,
            classification_level: res.data.classificationLevel,
            automaker_id: res.data.automakerId,
          }
        } else if (res && res.code !== 200) {
          // 接口返回错误码
          const errorMsg = res.msg || '加载平台详情失败'
          this.$notify.info('error', errorMsg)
          // 返回列表页面
          this.$router.push('/automaker-platform')
        } else {
          // 数据为空或格式异常
          this.$notify.info('error', '平台详情数据为空')
          // 返回列表页面
          this.$router.push('/automaker-platform')
        }
      } catch (error) {
        const errorMsg =
          error?.message || error || '加载平台详情失败，请稍后重试'
        this.$notify.info('error', errorMsg)
        // 返回列表页面
        this.$router.push('/automaker-platform')
      }
    },

    // LOGO上传前验证
    // async beforeLogoUpload(file) {
    //   const isValidType = [
    //     'image/jpeg',
    //     'image/jpg',
    //     'image/png',
    //     'image/svg+xml',
    //   ].includes(file.type)
    //   //const isLt2M = file.size / 1024 / 1024 < 2
    //   const isValidSize = file.size / 1024 < 150 // 150KB限制

    //   if (!isValidType) {
    //     this.$notify.info('error', '只能上传 JPG、JPEG、PNG、SVG 格式的图片!')
    //     return false
    //   }
    //   if (!isValidSize) {
    //     this.$notify.info('error', '上传图片大小不能超过 150KB!')
    //     return false
    //   }

    //   try {
    //     this.logoUploading = true

    //     // 直接转换为Base64编码
    //     let base64 = await uploadImgToBase64(file)
    //     this.formData.picture_code = base64
    //     //this.$notify.info('success', 'LOGO上传成功')

    //     return false // 阻止Element UI的自动上传
    //   } catch (error) {
    //     const errorMsg = error?.message || error || 'LOGO上传失败，请稍后重试'
    //     this.$notify.info('error', errorMsg)
    //     return false
    //   } finally {
    //     this.logoUploading = false
    //   }
    // },

    // 保存
    async onConfirm() {
      if (!this.$refs.form.validate()) {
        //this.$notify.info('warning', '请完善必填信息')
        return
      }

      this.confirmLoading = true
      try {
        const submitData = { ...this.formData }

        if (this.mode === 'add') {
          // 调用新增平台接口
          const res = await addAutomakerPlatform(submitData)
          if (res && res.code === 200) {
            this.$notify.info('success', '新增平台成功')
            // 返回列表页面
            this.$router.push('/automaker-platform')
          } else {
            const errorMsg = res?.msg || '新增平台失败'
            this.$notify.info('error', errorMsg)
          }
        } else {
          // 调用编辑平台接口
          const res = await editAutomakerPlatform(submitData)
          if (res && res.code === 200) {
            this.$notify.info('success', '编辑平台成功')
            // 返回列表页面
            this.$router.push('/automaker-platform')
          } else {
            const errorMsg = res?.msg || '编辑平台失败'
            this.$notify.info('error', errorMsg)
          }
        }
      } catch (error) {
        const errorMsg =
          error?.message ||
          error ||
          (this.mode === 'add'
            ? '新增平台失败，请稍后重试'
            : '编辑平台失败，请稍后重试')
        this.$notify.info('error', errorMsg)
      } finally {
        this.confirmLoading = false
      }
    },

    // 取消
    onCancel() {
      // 返回列表页面
      this.$router.push('/automaker-platform')
    },
  },
}
</script>

<style scoped>
/* 整体布局样式 */
.platform-center-box {
  max-width: 1200px;
  margin: 0 auto;
}

/* 标题样式 */
.text-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  border-left: 4px solid #1976d2;
  padding-left: 12px;
  margin-top: 32px;
}

/* 上传区域样式 */
.upload-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 120px;
}

.upload-tips {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #1976d2;
}

/* LOGO上传样式 */
.logo-uploader {
  display: inline-block;
}

.logo-preview {
  position: relative;
  width: 80px;
  height: 80px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
}

.logo-preview:hover {
  border-color: #1976d2;
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.2);
}

.logo-preview:hover .logo-overlay {
  opacity: 1;
}

.logo-image {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
}

.logo-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.logo-placeholder {
  width: 80px;
  height: 80px;
  border: 2px dashed #d0d0d0;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #fafafa;
}

.logo-placeholder:hover {
  border-color: #1976d2;
  background-color: #f0f7ff;
}

/* Dialog样式优化 */
.upload-requirements {
  padding: 8px 0;
}

.requirement-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  line-height: 1.5;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .platform-center-box {
    padding: 0 16px;
  }

  .upload-tips {
    margin-top: 16px;
  }

  .logo-preview,
  .logo-placeholder {
    width: 70px;
    height: 70px;
  }
}
</style>
