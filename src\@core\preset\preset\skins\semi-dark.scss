$nav-menu-bg-color: #171b34;

@mixin semi-dark--bg($component, $white-opacity: 0.76, $style: 'all') {
  .#{$component} {
    @if ($style == 'all' or $style == 'background') {
      background: $nav-menu-bg-color;
    }
    @if ($style == 'all' or $style == 'color') {
      // color: rgba(#fff, $white-opacity) !important;
      color: map-get($shades, 'white') !important;
    }
  }
}

.v-application.skin-variant--semi-dark {
  .theme--light.app-system-bar {
    background: linear-gradient(90deg, #171b34 12.99%, #33395d 100%) !important;
  }
  .v-navigation-drawer__border {
    box-shadow: -1px 0px 0px 0px #1d233f inset !important;
  }
  @include semi-dark--bg(app-navigation-menu);

  //   Style App Title
  @include semi-dark--bg(
    'app-navigation-menu .app-title.text--primary',
    $white-opacity: 1,
    $style: 'color'
  );

  //   Style all lists
  @include semi-dark--bg('app-navigation-menu .v-list-item', $style: 'color');

  //   Style all icons inside nav menu
  @include semi-dark--bg('app-navigation-menu  .v-icon', $style: 'color');

  //   Style all list titles
  @include semi-dark--bg(
    'app-navigation-menu .v-list-item .v-list-item__title',
    $style: 'color'
  );

  // Style active list item item
  @include semi-dark--bg(
    'app-navigation-menu .v-list-item .v-list-item__title.white--text',
    $white-opacity: 0.87,
    $style: 'color'
  );

  .app-navigation-menu {
    .v-list-item .v-list-item__title,
    .v-icon {
      color: inherit !important;
      caret-color: inherit !important;
    }
    .v-list-item--active,
    .primary--text {
      color: #0f7eff !important;
      caret-color: #0f7eff !important;
    }
    .bg-btn {
      color: #0f7eff !important;
      caret-color: #0f7eff !important;
      background: color-mix(
        in srgb,
        var(--v-primary-base) 10%,
        transparent
      ) !important;
      // background: $blue-white-color !important;
    }
    .v-list-item:hover {
      background: $blue-white-color !important;
    }
  }

  //   Style nav menu section title
  @include semi-dark--bg(
    'app-navigation-menu .v-subheader .title-wrapper span'
  );

  .vertical-nav-menu-container {
    .shadow-bottom {
      background: linear-gradient(
        $nav-menu-bg-color 40%,
        rgba($nav-menu-bg-color, 0.1) 95%,
        rgba($nav-menu-bg-color, 0.05)
      );
    }
  }
}
