import { request, vsocPath } from '../../util/request'

export const login = function (params) {
  return request({
    url: `${vsocPath}/user/login`,
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data',
      Authorization: 'Basic dnNvYzp2c29jMjAyMg==',
    },
    data: params,
  })
}

// 用户退出接口，无参数
export const logout = function (params) {
  return request({
    url: `${vsocPath}/auth/logout`,
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    data: params,
  })
}
