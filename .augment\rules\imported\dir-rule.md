---
type: "agent_requested"
---

# 项目文件应该遵循的目录结构规范
├src/
├── uiue/                # UIUE原型界面html
├── @fake-db/            # 模拟后端数据
│   └── data/            # 静态模拟数据集
├── api/                 # API模块（与views 1:1对应）
├── assets/              # 静态资源
│   ├── icons/           # SVG图标库
│   └── images/          # 压缩后的图片资源
├── components/          # 全局公共组件
├── layouts/variants/    # 自定义布局
│   ├── blank/           # 空白布局
│   └── content/         # 内容布局
├── navigation/          # 导航配置
│   ├── horizontal/      # 水平导航
│   └── vertical/        # 垂直导航
├── views/               # 页面视图
├── router/              # 路由配置
├── store/               # Vuex状态管理
│   ├── modules/         # 自动加载模块
│   └── index.js         # 主入口（手动/自动混合模式）
├── styles/              # 全局样式
│   ├── styles.scss      # 主样式文件（禁止重命名）
│   └── variables.scss   # 变量定义（禁止重命名）
├── util/                # 工具函数
│   ├── constant.js      # 常量定义
│   ├── enum.js          # 枚举值
│   ├── filter.js        # 全局过滤器
│   ├── plugins.js       # 插件加载
│   └── utils.js         # 通用工具函数
│   └── request.js       # axios请求和响应拦截器
├── App.vue              # 根组件
└── main.js              # 应用入口


