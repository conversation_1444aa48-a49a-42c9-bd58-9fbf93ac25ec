const analyseTotal = {
  ac: 'Analysis and Comment',
  jd: 'Justification and Details',
  yes: 'Yes',
  no: 'No',
  general1: 'General',
  cwe1: 'Select CWE',
  vo: 'Vulnerability Overview',
  allComponet: 'Total',
  vulnerableComponet: 'vulnerable',
  404: {
    action: 'Go Back',
    heading: 'Oops! This is awkward',
    message: "The page you're looking for could not be found",
  },
  admin: {
    access_management: 'Access Management',
    alert_created: '<PERSON><PERSON> created',
    alert_deleted: 'Alert deleted',
    alert_log_successful_publish: 'Log successful publish',
    alert_log_successful_publish_help:
      'Emit a log message upon successful publishing of the alert to the destination, instead of only emitting logs when issues are encountered. Aids in debugging missing alerts, or making alert publishing auditable.',
    alerts: 'Alerts',
    analyzer_internal_desc:
      'The internal analyzer evaluates components against an internal vulnerability database derived from the National Vulnerability Database, GitHub Advisories (if enabled) and VulnDB (if enabled). This analyzer makes use of the Common Platform Enumeration (CPE) defined in components. Components with a valid CPE will be evaluated with this analyzer.',
    analyzer_internal_enable: 'Enable internal analyzer',
    analyzer_internal_fuzzy_enable:
      'Enable fuzzy CPE matching. Helps with inconsistent NVD data, highlighting missing risks but also increasing false positives',
    analyzer_internal_fuzzy_exclude_internal:
      'Enable fuzzy CPE matching on internal components',
    analyzer_internal_fuzzy_exclude_purl:
      'Enable fuzzy CPE matching on components that have a Package URL (PURL) defined',
    analyzer_ossindex_desc:
      'OSS Index is a service provided by Sonatype which identifies vulnerabilities in third-party components. Dependency-Track integrates natively with the OSS Index service to provide highly accurate results. Use of this analyzer requires a valid PackageURL for the components being analyzed.',
    analyzer_ossindex_enable: 'Enable OSS Index analyzer',
    analyzer_snyk_alias_sync_warning:
      'Snyk does not differentiate between related and identical vulnerabilities. Proceed with caution.',
    analyzer_snyk_api_version: 'API Version',
    analyzer_snyk_api_version_warning:
      'Changing the default version may break the integration!',
    analyzer_snyk_desc:
      "Snyk's security intelligence database, also known as the Snyk Intel Vulnerability Database, is maintained by a dedicated research team that combines public sources, contributions from the developer community, proprietary research, and machine learning to continuously adapt to the changing and expanding nature of security threats. Dependency-Track integrates natively with Snyk's REST API to provide highly accurate results. Use of this analyzer requires a valid PackageURL for the components being analyzed.",
    analyzer_snyk_enable: 'Enable Snyk analyzer',
    analyzer_snyk_how_to_api_token_help: 'How do I get an API token?',
    analyzer_snyk_how_to_api_version_help:
      'Where do I find available versions?',
    analyzer_snyk_how_to_org_id_help: 'How do I find my Organization ID?',
    analyzer_snyk_multiple_tokens_info:
      'Multiple tokens may be provided by separating them with semicolons, e.g. ',
    analyzer_snyk_org_id: 'Organization ID',
    analyzer_snyk_why_multiple_cvss:
      'Why are there multiple CVSS Scores for the same vulnerability?',
    analyzer_trivy_enable: 'Enable Trivy analyzer',
    analyzer_trivy_ignore_unfixed: 'Ignore unfixed vulnerabilities',
    analyzer_vulndb_desc:
      'VulnDB is a commercial service from Risk Based Security which identifies vulnerabilities in third-party components. Dependency-Track integrates natively with the VulnDB service to provide highly accurate results. Use of this analyzer requires a valid CPE for the components being analyzed.',
    analyzer_vulndb_enable: 'Enable VulnDB analyzer',
    analyzers: 'Analyzers',
    api_key_comment: 'API Key Comment',
    api_key_comment_updated: 'API Key Comment Updated',
    api_key_created_tooltip: 'When the API Key was created',
    api_key_last_used_tooltip: 'Approximate last usage of the API Key',
    api_keys: 'API Keys',
    api_token: 'API token',
    api_token_header: 'API token header',
    base_url: 'Base URL',
    bearer_token_auth_enable: 'Authenticate with a personal access token',
    bom_formats: 'BOM Formats',
    bom_formats_desc:
      'Enables support for processing BOMs of various formats. Only BOM formats which are enabled will be processed.',
    bom_validation: 'BOM Validation',
    bom_validation_info:
      'Historically, Dependency-Track did not validate uploaded BOMs and VEXs against the CycloneDX schema. While this allowed BOMs to be processed that did not strictly adhere to the schema, it could lead to confusion when uploaded files were accepted, but then failed to be ingested during asynchronous processing. Starting with this release, uploaded files will be rejected if they fail schema validation. Note that this may reveal issues in BOM generators that currently produce invalid CycloneDX documents',
    cargo: 'Cargo',
    change_password: 'Change Password',
    change_password_next_login: 'User must change password at next login',
    clone_template: 'Clone Template',
    composer: 'Composer',
    configuration: 'Configuration',
    configuration_saved: 'Configuration saved',
    configuration_test: 'Configuration Test',
    consumer_key: 'Consumer key',
    consumer_secret: 'Consumer secret',
    cpan: 'CPAN',
    create_alert: 'Create Alert',
    create_ldap_user: 'Create LDAP User',
    create_managed_user: 'Create Managed User',
    create_oidc_group: 'Create Group',
    create_oidc_user: 'Create OpenID Connect User',
    create_repository: 'Create Repository',
    create_team: 'Create Team',
    create_template: 'Create Template',
    create_user: 'Create User',
    default: 'Default',
    default_template_restored: 'Default templates restored',
    defectdojo: 'DefectDojo',
    delete_alert: 'Delete Alert',
    delete_oidc_group: 'Delete Group',
    delete_repository: 'Delete Repository',
    delete_team: 'Delete Team',
    delete_template: 'Delete Template',
    delete_user: 'Delete User',
    destination: 'Destination',
    distinguished_name: 'Distinguished name',
    edit_api_key_comment: 'Edit API Key Comment',
    email: 'Email',
    email_address: 'Email address',
    email_enable_ssltls: 'Enable SSL/TLS encryption',
    email_from_address: 'From email address',
    email_prefix: 'Subject prefix',
    email_smtp_password: 'SMTP password',
    email_smtp_port: 'SMTP server port',
    email_smtp_server: 'SMTP server',
    email_smtp_username: 'SMTP username',
    email_trust_cert: 'Trust the certificate provided by the SMTP server',
    enable_acl: 'Enable portfolio access control (beta)',
    enable_bom_cyclonedx: 'Enable CycloneDX',
    enable_default_template_override: 'Enable default template override',
    enable_email: 'Enable email',
    enable_index_consistency_check: 'Enable periodic consistency check',
    enable_svg_badge: 'Enable SVG badge support (unauthenticated)',
    enabled: 'Enabled',
    experimental: 'Experimental',
    experimental_bom_upload_v2: 'BOM Processing V2',
    experimental_bom_upload_v2_info:
      'The logic how uploaded BOMs are processed and ingested into Dependency-Track has been overhauled to be more reliable and efficient. Further, BOM processing is now an atomic operation, such that errors occurring midway do not cause a partial state to be left behind. De-duplication of components and services is more predictable, and log messages emitted during processing contain additional context, making them easier to correlate. Because the new implementation can have a big impact on how Dependency-Track behaves regarding BOM uploads, it is disabled by default for this release.',
    experimental_info:
      'The following options are experimental options and should be used with precaution.',
    fortify_ssc: 'Fortify SSC',
    gem: 'Gem',
    general: 'General',
    general_template_configuration: 'General template configuration',
    github: 'GitHub',
    github_advisories: 'GitHub Advisories',
    go_modules: 'Go Modules',
    group: 'Group',
    hackage: 'Hackage',
    hex: 'Hex',
    identifier: 'Identifier',
    include_active_children: 'Include active children of projects',
    include_children: 'Include children of projects',
    index_consistency_check_cadence: 'Cadence (in minutes)',
    index_consistency_check_description:
      'You can enable a periodic background task that will check that all indexes exists, are not corrupted and that their difference with Dependency Track database is under a defined threshold percentage. Any check failure will trigger a rebuild of the corresponding index. A restart is required to take cadence modification into account.',
    index_consistency_check_threshold: 'Delta threshold (in percentage)',
    index_general_description:
      'Dependency Track uses Apache Lucene to enable full-text search on various entities like projects or vulnerabilities.',
    index_issues_description:
      'The lucene indexes can degrade or drift from Dependency Track database over time. Even though DT does its best to minimize the drift, the administrative features below are provided to check or restore the indexes if need be. It must be used with caution.',
    index_rebuild_description:
      'You can selectively trigger an immediate rebuild of some or all indexes. The index rebuild will be perform by an asynchronous tasks. You can check the progress using Dependency Track logs.',
    index_use_cases:
      'The full-text search feature is principally used for the search API (i.e. all the indexes) and internal analyzer fuzzy matching on CPE (i.e. the vulnerable software index).',
    integration_defectdojo_enable: 'Enable DefectDojo integration',
    integration_defectdojo_reimport_enable: 'Enable reimport',
    integration_fortify_ssc_enable: 'Enable Fortify SSC integration',
    integration_kenna_connector_id: 'Connector ID',
    integration_kenna_enable: 'Enable Kenna Security integration',
    integrations: 'Integrations',
    internal: 'Internal',
    internal_analyzer: 'Internal',
    internal_component_desc:
      "Internal components will be excluded from vulnerability scans and version checks that rely on external systems.\n Note that the regular expressions below must be compliant with Java's regex syntax.\nThe regex must match the whole string. To match all namespaces with 'example' in it, write '.*example.*'.",
    internal_components: 'Internal Components',
    internal_identification_error:
      'An error occurred queueing internal component identification. Check server logs for details',
    internal_identification_queued: 'Internal component identification queued',
    jira: 'Jira',
    jira_auth_with_token: 'Jira personal access token',
    jira_desc: 'Jira service authentication',
    jira_password: 'Jira password',
    jira_ticket_type: 'Jira ticket type',
    jira_url: 'Jira base URL',
    jira_username:
      'Jira user (service account). The user must be provisioned to the appropriate jira instance and permitted to create issues.',
    kenna_security: 'Kenna Security',
    ldap_users: 'LDAP Users',
    limit_to: 'Limit To',
    limit_to_projects: 'Limit to projects',
    limit_to_tags: 'Limit to Tags',
    managed_users: 'Managed Users',
    mapped_ldap_groups: 'Mapped LDAP groups',
    mapped_oidc_groups: 'Mapped OpenID Connect Groups',
    mapped_teams: 'Mapped Teams',
    maven: 'Maven',
    members: 'Members',
    mime_type: 'Mime type',
    name_regex: 'Component name regex',
    name_regex_desc:
      'Specifies a regular expression that identifies internal components by the components name.',
    namespace_regex: 'Component namespace regex',
    namespace_regex_desc:
      "Specifies a regular expression that identifies internal components by namespace. The namespace is often referred to as 'group', 'organization', or 'vendor' in various ecosystems.",
    national_vulnerability_database: 'National Vulnerability Database',
    nixpkgs: 'Nixpkgs',
    notification_level: 'Notification level',
    notifications: 'Notifications',
    npm: 'NPM',
    nuget: 'NuGet',
    nvd: 'NVD',
    nvd_additionally_download_feeds: 'Additionally download feeds',
    nvd_additionally_download_feeds_help:
      'Feeds will not be parsed, but made available to other clients at',
    nvd_api_last_modification: 'Last Modification (UTC)',
    nvd_api_last_modification_help:
      'After mirroring the NVD database once completely, all following mirror operations will only request data that was modified since its last successful execution.',
    nvd_api_last_modification_warning:
      "Changing the last modification datetime manually is generally not recommended, but may be used to force re-ingestion of NVD data. Note that due to a limitation in the NVD's REST API, only data for 120 consecutive days can be requested when a last modification datetime is configured. Resetting the last modification datetime will cause the entire NVD database to be re-mirrored.",
    nvd_enable_mirroring_via_api: 'Enable mirroring via API',
    nvd_request_api_key_help: 'How do I get an API key?',
    nvd_why_enable_api_help: 'Why should I enable API mirroring?',
    oidc_group_created: 'OpenID Connect group created',
    oidc_group_deleted: 'OpenID Connect group deleted',
    oidc_group_name: 'Group Name',
    oidc_groups: 'OpenID Connect Groups',
    oidc_users: 'OpenID Connect Users',
    oss_index: 'Sonatype OSS Index',
    osv_advisories: 'Google OSV Advisories (Beta)',
    password: 'Password (or access token)',
    password_confirm: 'Confirm password',
    password_never_expires: 'Password never expires',
    password_updated: 'Password updated',
    perform_identification: 'Perform Identification',
    perform_test: 'Perform Test',
    permissions: 'Permissions',
    personal_access_token: 'Personal Access Token',
    portfolio_access_control: 'Portfolio Access Control',
    project_access: 'Project access',
    publisher: 'Publisher',
    publisher_class: 'Publisher class',
    python: 'Python',
    registered_email_address: 'Registered email address',
    reindex_components: 'Components',
    reindex_cpes: 'CPE',
    reindex_error: 'Error encountered while submitting reindex request',
    reindex_licenses: 'Licenses',
    reindex_projects: 'Projects',
    reindex_service_components: 'Service components',
    reindex_submitted: 'Reindex request submitted',
    reindex_vulnerabilities: 'Vulnerabilities',
    reindex_vulnerable_software: 'Vulnerable software',
    remove_api_key: 'Remove API Key',
    repositories: 'Repositories',
    repository_authentication: 'Authentication required',
    repository_created: 'Repository created',
    repository_deleted: 'Repository deleted',
    repository_type: 'Repository Type',
    required_confirmPassword:
      'Password confirmation is required and passwords must match',
    required_email: 'Email address is required',
    required_fullname: 'Fullname is required',
    required_oidc_group_name: 'Name is required',
    required_password: 'Password is required',
    required_team_name: 'Team name is required',
    required_username: 'Username is required',
    restore_default_template: 'Restore default templates',
    scope: 'Scope',
    select_ecosystem: 'Select Ecosystems',
    select_ldap_group: 'Select LDAP Group',
    select_oidc_group: 'Select OpenID Connect Group',
    select_permission: 'Select permission',
    select_project: 'Select Project',
    select_team: 'Select Team',
    select_team_as_recipient: 'Select team as recipient',
    snyk: 'Snyk (Beta)',
    subject_identifier: 'Subject Identifier',
    submit: 'Submit',
    suspended: 'Suspended',
    synchronization_cadence_minutes: 'Synchronization cadence (in minutes)',
    synchronization_cadence_restart_required:
      'Restarting Dependency-Track is required for cadence changes to take effect',
    task_scheduler: 'Task scheduler',
    task_scheduler_component_analysis_cache_clear:
      'Component analysis cache clear',
    task_scheduler_description:
      'Dependency Track task scheduler perform various background tasks at a fixed interval. You can modify the fixed interval for each task using the form below. Each interval is expressed in hours. A Dependency Track restart is needed to reschedule the tasks with the updated cadence.',
    task_scheduler_internal_component_identification:
      'Internal component identification',
    task_scheduler_ldap_sync: 'LDAP sync',
    task_scheduler_portfolio_metrics_update: 'Portfolio metrics',
    task_scheduler_portfolio_vulnerability_analysis:
      'Portfolio vulnerability analysis',
    task_scheduler_repository_metadata_fetch: 'Repository metadata fetch',
    task_scheduler_vulnerability_metrics_update: 'Vulnerability metrics',
    team_created: 'Team created',
    team_deleted: 'Team deleted',
    team_membership: 'Team membership',
    team_name: 'Team Name',
    teams: 'Teams',
    template: 'Template',
    template_basedir: 'Template base directory',
    template_basedir_tooltip:
      'This property is used as base directory for notification templates search',
    template_created: 'Template created',
    template_deleted: 'Template deleted',
    template_override_description:
      'Switching the template override control on and providing a template base directory allow you to override Dependency Track default notification publisher templates.',
    template_override_file_hierarchy:
      'Any Pebble templates available in the template base directory with the appropriate directory hierarchy and naming scheme (e.g ${base directory}/templates/notification/publisher/email.peb) will override Dependency Track default one.',
    template_override_restart_needed:
      'Dependency Track restart is required for the modifications to be taken into account.',
    template_override_security_warning:
      'You must set appropriate rights to the template base directory to prevent untrusted third party from supplying fraudulent Pebble templates that could lead to potential remote code execution.',
    templates: 'Templates',
    test_notification_queued: 'A test notification has been queued',
    token: 'Token',
    trivy: 'Trivy',
    url: 'URL',
    user_created: 'User created',
    user_deleted: 'User deleted',
    username: 'Username',
    vuln_sources: 'Vulnerability Sources',
    vulndb: 'VulnDB',
    vulnsource_alias_sync_enable: 'Enable vulnerability alias synchronization',
    vulnsource_alias_sync_enable_tooltip:
      "Alias data can help in identifying identical vulnerabilities across multiple databases. If the source provides this data, synchronize it with Dependency-Track's database.",
    vulnsource_github_advisories_desc:
      "GitHub Advisories (GHSA) is a database of CVEs and GitHub-originated security advisories affecting the open source world. Dependency-Track integrates with GHSA by mirroring advisories via GitHub's public GraphQL API. The mirror is refreshed daily, or upon restart of the Dependency-Track instance. A personal access token (PAT) is required in order to authenticate with GitHub, but no scopes need to be assigned to it.",
    vulnsource_github_advisories_enable: 'Enable GitHub Advisory mirroring',
    vulnsource_nvd_desc:
      'The National Vulnerability Database (NVD) is the largest publicly available source of vulnerability intelligence. It is maintained by a group within the National Institute of Standards and Technology (NIST) and builds upon the work of MITRE and others. Vulnerabilities in the NVD are called Common Vulnerabilities and Exposures (CVE). There are over 100,000 CVEs documented in the NVD spanning from the 1990’s to the present.',
    vulnsource_nvd_enable: 'Enable National Vulnerability Database mirroring',
    vulnsource_nvd_feeds_url: 'NVD Feeds URL',
    vulnsource_nvd_notice:
      'This product uses data from the NVD API but is not endorsed or certified by the NVD.',
    vulnsource_osv_advisories_desc:
      'Google OSV is a distributed vulnerability and triage infrastructure for open source projects aimed at helping both open source maintainers and consumers of open source. It serves as an aggregator of vulnerability databases that have adopted the OpenSSF Vulnerability format.',
    vulnsource_osv_advisories_enable:
      'Select ecosystem to enable Google OSV Advisory mirroring',
    vulnsource_osv_alias_sync_warning:
      'OSV may report non-identical vulnerabilities as aliases. Proceed with caution.',
    vulnsource_osv_base_url: 'OSV Base URL',
  },
  condition: {
    forbidden: 'Forbidden (403)',
    http_request_error: 'HTTP Request Error',
    server_error: 'Server Error',
    successful: 'successful',
    unsuccessful_action: 'Could not perform the requested action',
  },
  hashes: {
    blake3: 'BLAKE3',
    blake_256: 'BLAKE2b-256',
    blake_384: 'BLAKE2b-384',
    blake_512: 'BLAKE2b-512',
    md5: 'MD5',
    sha3_256: 'SHA3-256',
    sha3_384: 'SHA3-384',
    sha3_512: 'SHA3-512',
    sha_1: 'SHA-1',
    sha_256: 'SHA-256',
    sha_384: 'SHA-384',
    sha_512: 'SHA-512',
  },
  language: {
    de: 'German',
    en: 'English',
    es: 'Spanish',
    fr: 'French',
    hi: 'Hindi',
    it: 'Italian',
    ja: 'Japanese',
    pl: 'Polish',
    pt: 'Portuguese (Portugal)',
    'pt-BR': 'Portuguese (Brazil)',
    ru: 'Russian',
    zh: 'Chinese',
  },
  message: {
    about: 'About',
    active: 'Active',
    active1: 'Active',
    add: 'Add',
    add_affected_component: 'Add Affected Component',
    add_comment: 'Add Comment',
    add_component: 'Add Component',
    add_license: 'Add License',
    add_tag: 'Add Tag',
    add_version: 'Add Version',
    administration: 'Administration',
    affected_components: 'Affected Components',
    affected_projects: 'Affected Projects',
    age: 'Age',
    age_tooltip:
      'Age in ISO-8601 period format (e.g. P1Y = 1 Year; P2Y3M = 2 Years, 3 Months)',
    aliases: 'Aliases',
    analysis: 'Analysis',
    analysis_details_tooltip:
      'Details (explanation, workaround details, and other impact information)',
    analysis_state: 'Analysis State',
    analysis_status: 'Analysis Status',
    analysis_tooltip: 'The current state of an occurrence of a vulnerability',
    analyzer: 'Analyzer',
    apply_vex: 'Apply VEX',
    apply_vex_tooltip:
      'Apply analyses from a Vulnerability Exploitability eXchange (VEX) document to this project.',
    approved: 'Approved',
    attributed_on: 'Attributed On',
    audit_trail: 'Audit Trail',
    audit_vulnerabilities: 'Audit Vulnerabilities',
    auditing_progress: 'Auditing Progress',
    authenticated: 'Authenticated',
    authors: 'Authors',
    bom: 'BOM',
    bom_format: 'BOM Format',
    bom_uploaded: 'BOM uploaded',
    browse: 'Browse',
    can_not_fix: 'Can not fix',
    cancel: 'Cancel',
    change_password: 'Change Password',
    classification: 'Classification',
    classifier: 'Classifier',
    classifier1: 'Classifier',
    clear_all: 'Clear all',
    close: 'Close',
    code_not_present: 'Code not present',
    code_not_reachable: 'Code not reachable',
    comment: 'Comment',
    comments: 'Comments',
    component: 'Component',
    component_application: 'Application',
    component_author: 'Author',
    component_author_desc: 'The author of the component',
    component_classification: 'Classification',
    component_classification_desc:
      'Classifies whether a component is considered internal or external to an organization',
    component_classifier_desc:
      'Specifies the type of component: Assets (applications, operating systems, and hardware) and non-assets (libraries, frameworks, and files)',
    component_container: 'Container',
    component_cpe_desc:
      'The CPE v2.2 or v2.3 URI as provided by MITRE or NIST. All assets (applications, operating systems, and hardware) should have a CPE specified',
    component_created: 'Component created',
    component_deleted: 'Component deleted',
    component_details: 'Component Details',
    component_device: 'Device',
    component_file: 'File',
    component_filename_desc: 'Specifies the observed filename of the component',
    component_firmware: 'Firmware',
    component_framework: 'Framework',
    component_group_desc:
      'The suppliers higher-level namespace, group, or vendor identifier',
    component_hash: 'Component Hash',
    component_hash_desc:
      'Specifies the observed hash value of the component using the preceding algorithm',
    component_identifier_desc:
      "Identifies the component by Package URL (PURL) or CPE. If the version type is 'Exact', the PURL or CPE must contain the version. If a range is specified, version information in the identifier will be ignored.",
    component_library: 'Library',
    component_license_expression_desc:
      'Specifies license information for the component in the form of an SPDX expression',
    component_license_url_desc:
      'Specifies the URL to the license of the component',
    component_name: 'Component Name',
    component_name_desc:
      'The name of the component as provided by the supplier',
    component_namespace_group_vendor: 'Namespace / group / vendor',
    component_operating_system: 'Operating system',
    component_package_url_desc:
      'A Valid Package URL is required for libraries and frameworks. PURL syntax: pkg:type/namespace/name@version?qualifiers#subpath',
    component_properties: 'Component Properties',
    component_search: 'Component Search',
    component_spdx_license_desc:
      'Specifies the SPDX license ID of the component',
    component_supplier_name_desc:
      'The organization that supplied the component',
    component_swid_tagid_desc:
      'The ISO/IEC 19770-2:2015 (SWID) tag ID provided by the software vendor',
    component_updated: 'Component updated',
    component_version_desc:
      'The version of the component as provided by the supplier',
    component_vulnerabilities: 'Component Vulnerabilities',
    components: 'Components',
    components1: 'Components',
    components2: 'Components',
    condition: 'Condition',
    condition_deleted: 'Condition deleted',
    conditions: 'Conditions',
    connected_as: 'Connected as',
    contacts: 'Contacts',
    coordinates: 'Coordinates',
    coordinates_version_tooltip:
      'You can use the comparison operators >, <, >=, <=, == and != to match specific versions or version ranges',
    copyright: 'Copyright',
    cpe: 'CPE',
    cpe_full: 'Common Platform Enumeration (CPE)',
    create: 'Create',
    create_component_property: 'Create Component Property',
    create_license_group: 'Create License Group',
    create_policy: 'Create Policy',
    create_project: 'Create Project',
    create_project_property: 'Create Project Property',
    create_property: 'Create Property',
    create_vulnerability: 'Create Vulnerability',
    created: 'Created',
    credits: 'Credits',
    custom_license: 'Custom License',
    custom_license_deleted: 'Custom License Deleted',
    cvss: 'CVSS',
    cvss_access_complexity: 'Access Complexity',
    cvss_adjacent: 'Adjacent',
    cvss_attack_complexity: 'Attack Complexity',
    cvss_attack_vector: 'Attack Vector',
    cvss_authentication: 'Authentication',
    cvss_availability_impact: 'Availability Impact',
    cvss_base_score: 'CVSS Base Score',
    cvss_base_score_short: 'Base Score',
    cvss_changed: 'Changed',
    cvss_complete: 'Complete',
    cvss_confidentiality_impact: 'Confidentiality Impact',
    cvss_exploitability: 'Exploitability',
    cvss_exploitability_subscore: 'CVSS Exploitability Subscore',
    cvss_high: 'High',
    cvss_impact: 'Impact',
    cvss_impact_subscore: 'CVSS Impact Subscore',
    cvss_integrity_impact: 'Integrity Impact',
    cvss_local: 'Local',
    cvss_low: 'Low',
    cvss_medium: 'Medium',
    cvss_multiple: 'Multiple',
    cvss_network: 'Network',
    cvss_none: 'None',
    cvss_none1: 'None',
    cvss_partial: 'Partial',
    cvss_physical: 'Physical',
    cvss_privileges_required: 'Privileges Required',
    cvss_required: 'Required',
    cvss_scope: 'Scope',
    cvss_single: 'Single',
    cvss_source: 'Select source priority for CVSS',
    cvss_unchanged: 'Unchanged',
    cvss_user_interaction: 'User Interaction',
    cvss_v2: 'CVSSv2',
    cvss_v2_vector: 'CVSSv2 Vector',
    cvss_v3: 'CVSSv3',
    cvss_v3_vector: 'CVSSv3 Vector',
    cwe: 'CWE',
    cwe_full: 'Common Weakness Enumeration (CWE)',
    cwe_id: 'CWE ID',
    dashboard: 'Dashboard',
    data: 'Data',
    dates: 'Dates',
    delete: 'Delete',
    delete_license_group: 'Delete License Group',
    delete_policy: 'Delete Policy',
    dependency_graph: 'Dependency Graph',
    deprecated: 'Deprecated',
    description: 'Description',
    details: 'Details',
    direct_only: 'Direct only',
    direction: 'Direction',
    download_bom: 'Download BOM',
    email: 'Email',
    endpoints: 'Endpoints',
    epss: 'EPSS',
    epss_percentile: 'EPSS Percentile',
    epss_score: 'EPSS Score',
    exact: 'Exact',
    exploit_predictions: 'Exploit Predictions',
    exploitable: 'Exploitable',
    export_vdr: 'Export VDR',
    export_vdr_tooltip:
      'Export a Vulnerability Disclosure Report (VDR), as defined in NIST SP 800-161.',
    export_vex: 'Export VEX',
    export_vex_tooltip:
      'Export a Vulnerability Exploitability eXchange (VEX) document.',
    extended: 'Extended',
    external_references: 'External References',
    false_positive: 'False Positive',
    filename: 'Filename',
    filters: 'Filters',
    findings: 'Findings',
    findings_audited: 'Findings Audited',
    findings_unaudited: 'Findings Unaudited',
    first_seen: 'First Seen At',
    from: 'From',
    fsf_libre: 'FSF Libre',
    fullname: 'Full name',
    general: 'General',
    global_audit: 'Global Audit',
    group: 'Group',
    group_name: 'Group Name',
    grouped_vulnerabilities: 'Grouped Vulnerabilities',
    hashes: 'Hashes',
    hashes_short_desc: 'Hash (MD5, SHA, SHA3, Blake2b, Blake3)',
    home: 'Home',
    identifier: 'Identifier',
    identifier_type: 'Identifier Type',
    identity: 'Identity',
    in_triage: 'In Triage',
    inactive: 'Inactive',
    inactive_active_children:
      'The project cannot be set to inactive if it has active children',
    inactive_versions: 'Inactive Versions',
    include_acl: 'Include access control list',
    include_audit_history: 'Include audit history',
    include_components: 'Include components',
    include_policy_violations: 'Include Policy Violations',
    include_properties: 'Include properties',
    include_services: 'Include services',
    include_tags: 'Include tags',
    inherited_risk_score: 'Inherited Risk Score',
    internal: 'Internal',
    inventory: 'Inventory',
    inventory_with_vulnerabilities: 'Inventory with Vulnerabilities',
    justification: 'Justification',
    justification_tooltip:
      'The rationale of why the impact analysis state was asserted to be "Not Affected"',
    language: 'Language',
    last_bom_import: 'Last BOM Import',
    last_measurement: 'Last Measurement',
    last_seen: 'Last Seen At',
    latest_version: 'Latest Version',
    legal: 'Legal',
    license: 'License',
    license_comments: 'License Comments',
    license_expression: 'SPDX Expression',
    license_group: 'License group',
    license_group_created: 'License group created',
    license_group_deleted: 'License group deleted',
    license_groups: 'License Groups',
    license_id: 'License ID',
    license_id_desc:
      'The SPDX defined ID of the license. The ID may only contain alpha, numeric, and specific symbols: _ - . +',
    license_name: 'License Name',
    license_name_desc: 'The name of the license as provided by the supplier',
    license_risk: 'License Risk',
    license_text: 'License Text',
    license_url: 'License URL',
    licenses: 'Licenses',
    login: 'Login',
    login_desc: 'Sign In to your account',
    login_forbidden: 'This account is inactive or has been suspended',
    login_more_options: 'More options',
    login_unauthorized: 'Invalid username or password',
    logout: 'Logout',
    manufacturer: 'Manufacturer',
    manufacturer_name: 'Manufacturer name',
    manufacturer_name_desc:
      'The organization that manufactured the component that the project describes',
    matrix: 'Matrix',
    metric_refresh_requested:
      'A refresh has been requested. Metrics will be updated when the refresh task has completed.',
    name: 'Name',
    no_file_chosen: 'No file chosen',
    non_vulnerable: 'Non Vulnerable',
    not_affected: 'Not Affected',
    not_found_in_dependency_graph:
      'Dependency could not be found in dependency graph',
    not_set: 'Not Set',
    notes: 'Notes',
    object_identifier: 'Object Identifier',
    object_identifier_desc:
      'A unique identifier (UUID) that Dependency-Track automatically assigns to every object',
    occurred_on: 'Occurred On',
    occurrences_in_projects: 'Occurrences in projects',
    oidc_availability_check_failed:
      'Failed to determine availability of OpenID Connect',
    oidc_redirect_failed:
      'An error occurred while redirecting to the OpenID Connect identity provider',
    only_direct_tooltip:
      'Only show direct dependencies, hiding transitive dependencies',
    only_outdated_tooltip:
      'Only show dependencies that have newer stable versions available',
    operational_risk: 'Operational Risk',
    operator: 'Operator',
    osi_approved: 'OSI Approved',
    outdated_only: 'Outdated only',
    overview: 'Overview',
    owasp_rr: 'OWASP Risk Rating',
    owasp_rr_awareness: 'Awareness',
    owasp_rr_awareness_hidden: 'Hidden',
    owasp_rr_awareness_obvious: 'Obvious',
    owasp_rr_awareness_public_knowledge: 'Public',
    owasp_rr_awareness_unknown: 'Unknown',
    owasp_rr_business_impact_factor: 'Business impact Factor',
    owasp_rr_business_impact_score: 'OWASP RR Business impact score',
    owasp_rr_business_impact_score_short: 'Business impact',
    owasp_rr_ease_of_discovery: 'Ease of discovery',
    owasp_rr_ease_of_discovery_automated_tools_available: 'Automated',
    owasp_rr_ease_of_discovery_difficult: 'Difficult',
    owasp_rr_ease_of_discovery_easy: 'Easy',
    owasp_rr_ease_of_discovery_impossible: 'Impossible',
    owasp_rr_ease_of_exploit: 'Ease of exploit',
    owasp_rr_ease_of_exploit_automated_tools_available: 'Automated',
    owasp_rr_ease_of_exploit_difficult: 'Difficult',
    owasp_rr_ease_of_exploit_easy: 'Easy',
    owasp_rr_ease_of_exploit_theoritical: 'Theoritical',
    owasp_rr_financial_damage: 'Financial Damage',
    owasp_rr_financial_damage_bankruptcy: 'Bankruptcy',
    owasp_rr_financial_damage_less_than_cost_to_fix: 'Less than fix',
    owasp_rr_financial_damage_minor: 'Minor',
    owasp_rr_financial_damage_significant: 'Significant',
    owasp_rr_intrusion_detection: 'Intrusion detection',
    owasp_rr_intrusion_detection_active: 'Active',
    owasp_rr_intrusion_detection_logged_reviewed: 'Logged & Reviewed',
    owasp_rr_intrusion_detection_logged_unreviewed: 'Logged & Unreviewed',
    owasp_rr_intrusion_detection_not_logged: 'Not logged',
    owasp_rr_likelihood_score: 'OWASP RR Likelihood score',
    owasp_rr_likelihood_score_short: 'Likelihood',
    owasp_rr_loss_of_accountability: 'Loss of accountability',
    owasp_rr_loss_of_accountability_completely_anonymous:
      'Completely anonymous',
    owasp_rr_loss_of_accountability_fully_traceable: 'Fully traceable',
    owasp_rr_loss_of_accountability_possibly_traceable: 'Possibly traceable',
    owasp_rr_loss_of_availability: 'Loss of availibility (services)',
    owasp_rr_loss_of_availability_all: 'All',
    owasp_rr_loss_of_availability_extensive_primary_services:
      'Extensive primary',
    owasp_rr_loss_of_availability_extensive_secondary_services:
      'Extensive secondary',
    owasp_rr_loss_of_availability_minimal_primary_services: 'Minimal primary',
    owasp_rr_loss_of_availability_minimal_secondary_services:
      'Minimal secondary',
    owasp_rr_loss_of_confidentiality: 'Loss of confidentiality (data)',
    owasp_rr_loss_of_confidentiality_all: 'All',
    owasp_rr_loss_of_confidentiality_extensive_critical: 'Extensive critical',
    owasp_rr_loss_of_confidentiality_extensive_non_sensitive:
      'Extensive non sensitive',
    owasp_rr_loss_of_confidentiality_minimal_critical: 'Minimal critical',
    owasp_rr_loss_of_confidentiality_minimal_non_sensitive:
      'Minimal non sensitive',
    owasp_rr_loss_of_integrity: 'Loss of integrity (corruption)',
    owasp_rr_loss_of_integrity_all: 'All',
    owasp_rr_loss_of_integrity_extensive_seriously_corrupt:
      'Extensive seriously',
    owasp_rr_loss_of_integrity_extensive_slightly_corrupt: 'Extensive slightly',
    owasp_rr_loss_of_integrity_minimal_seriously_corrupt: 'Minimal seriously',
    owasp_rr_loss_of_integrity_minimal_slightly_corrupt: 'Minimal slightly',
    owasp_rr_motivation: 'Motivation',
    owasp_rr_motivation_high_reward: 'High reward',
    owasp_rr_motivation_low: 'Low',
    owasp_rr_motivation_possible_reward: 'Possible reward',
    owasp_rr_non_compliance: 'Non compliance',
    owasp_rr_non_compliance_clear: 'Clear',
    owasp_rr_non_compliance_high_profile: 'High profile',
    owasp_rr_non_compliance_minor: 'Minor',
    owasp_rr_opportunity: 'Opportunity / Access',
    owasp_rr_opportunity_full: 'Full',
    owasp_rr_opportunity_none: 'None',
    owasp_rr_opportunity_some: 'Some',
    owasp_rr_opportunity_special: 'Special',
    owasp_rr_privacy_violation: 'Privacy violation',
    owasp_rr_privacy_violation_hundreds: 'Hundreds',
    owasp_rr_privacy_violation_millions: 'Millions',
    owasp_rr_privacy_violation_one_individual: 'One',
    owasp_rr_privacy_violation_thousands: 'Thousands',
    owasp_rr_reputation_damage: 'Reputation Damage',
    owasp_rr_reputation_damage_brand: 'Brand',
    owasp_rr_reputation_damage_goodwill: 'Goodwill',
    owasp_rr_reputation_damage_major_accounts: 'Major accounts',
    owasp_rr_reputation_damage_minimal: 'Minimal',
    owasp_rr_skill_level: 'Skill level',
    owasp_rr_skill_level_advanced: 'Advanced',
    owasp_rr_skill_level_network_and_programming: 'Programming',
    owasp_rr_skill_level_none: 'None',
    owasp_rr_skill_level_security_penetration_testing: 'Pentest',
    owasp_rr_skill_level_some: 'Some',
    owasp_rr_technical_impact_factor: 'Technical impact Factor',
    owasp_rr_technical_impact_score: 'OWASP RR Technical impact score',
    owasp_rr_technical_impact_score_short: 'Technical impact',
    owasp_rr_threat_agent_factor: 'Threat Agent Factor',
    owasp_rr_threat_size: 'Threat Size',
    owasp_rr_threat_size_anonymous_internet_users: 'Internet users',
    owasp_rr_threat_size_auth_users: 'Authenticated users',
    owasp_rr_threat_size_dev_sa: 'Dev/Sysadmin',
    owasp_rr_threat_size_intranet: 'Intranet',
    owasp_rr_threat_size_partners: 'Partners',
    owasp_rr_vulnerability_factor: 'Vulnerability Factor',
    package_url: 'Package URL (PURL)',
    package_url_full: 'Package URL (PURL)',
    password: 'Password',
    password_change: 'Change password',
    password_change_success: 'Password changed successfully',
    password_confirm: 'Confirm new password',
    password_current: 'Current password',
    password_force_change: 'Update Password',
    password_force_change_desc: 'You need to change your password',
    password_new: 'New password',
    password_not_acceptable:
      "Check that new password and confirm password match. You can't re-use your old password",
    password_unauthorized: 'Check current login credentials and try again',
    phone: 'Phone',
    policies: 'Policies',
    policy_created: 'Policy created',
    policy_deleted: 'Policy deleted',
    policy_management: 'Policy Management',
    policy_name: 'Policy Name',
    policy_violations: 'Policy Violations',
    policy_violations_by_classification: 'Policy Violations by Classification',
    policy_violations_by_state: 'Policy Violations by State',
    portfolio: 'Portfolio',
    portfolio_statistics: 'Portfolio Statistics',
    portfolio_vulnerabilities: 'Portfolio Vulnerabilities',
    profile_update: 'Update Profile',
    profile_updated: 'Profile updated',
    project_cloning_in_progress:
      'The project is being created with the cloning options specified',
    project_created: 'Project created',
    project_deleted: 'Project deleted',
    project_details: 'Project Details',
    project_metadata_supplier_name_desc:
      'The organization that supplied the BOM',
    project_name: 'Project Name',
    project: 'Project',
    project_name_desc:
      'The name of the project or component as provided by the supplier',
    project_properties: 'Project Properties',
    project_reanalyze: 'Reanalyze',
    project_reanalyze_requested:
      'A Project Vulnerability Analysis has been requested. Project vulnerability data will be updated when the reanalysis task has completed.',
    project_reanalyze_tooltip:
      "Runs configured analyzers to detect vulnerabilities in this project's components. Will use any cached results that haven't expired yet",
    project_supplier_name_desc:
      'The organization that supplied the component that the project describes',
    project_updated: 'Project updated',
    project_vulnerabilities: 'Project Vulnerabilities',
    projects: 'Projects',
    projects2: 'Projects',
    projects_at_risk: 'Projects at Risk',
    properties: 'Properties',
    property_created: 'Property created',
    property_deleted: 'Property deleted',
    property_name: 'Property Name',
    property_type: 'Property Type',
    property_value: 'Property Value',
    protected_at_perimeter: 'Protected at perimeter',
    protected_at_runtime: 'Protected at runtime',
    protected_by_compiler: 'Protected by compiler',
    protected_by_mitigating_control: 'Protected by mitigating control',
    provider: 'Provider',
    provider_name: 'Provider name',
    published: 'Published',
    published1: 'Published',
    purl: 'purl',
    range: 'Range',
    recommendation: 'Recommendation',
    references: 'References',
    reindex: 'Rebuild index(es)',
    rejected: 'Rejected',
    remove_component: 'Remove Component',
    reported_by: 'Reported By',
    required_component_identifier: 'A component identifier is required',
    required_component_name: 'The component name is required',
    required_component_version: 'The component version is required',
    required_license_id: 'The license ID is required',
    required_license_name: 'The license name is required',
    required_project_name: 'The project name is required',
    required_service_name: 'The service name is required',
    required_vulnerability_vuln_id:
      'A unique vulnerability identifier is required',
    requires_configuration: 'Requires configuration',
    requires_dependency: 'Requires dependency',
    requires_environment: 'Requires environment',
    reset: 'Reset',
    resolved: 'Resolved',
    response: 'Vendor Response (project)',
    response_tooltip:
      'A response to the vulnerability by the manufacturer, supplier, or project responsible for the affected component or service',
    risk_score: 'Risk Score',
    risk_type: 'Risk Type',
    rollback: 'Rollback',
    search: 'Search',
    search_parent: 'Type to search parent',
    security_risk: 'Security Risk',
    see_also: 'See Also',
    select: 'Select',
    select_cwe: 'Select CWE',
    select_license: 'Select License',
    select_project: 'Select Project',
    select_tag: 'Select Tag',
    service_deleted: 'Service deleted',
    service_details: 'Service Details',
    service_name: 'Service name',
    service_name_desc: 'The name of the service as described by the provider',
    service_provider_name_desc: 'The name of the provider',
    service_updated: 'Service updated',
    service_version_desc:
      'The version of the service as described by the provider',
    service_vulnerabilities: 'Service Vulnerabilities',
    services: 'Services',
    severity: 'Severity',
    show_complete_graph: 'Show complete graph',
    show_flat_view: 'Show flat project view',
    show_in_dependency_graph: 'Show in dependency graph',
    show_inactive_projects: 'Show inactive projects',
    show_suppressed_findings: 'Show suppressed findings',
    show_suppressed_violations: 'Show suppressed violations',
    show_update_information: 'Highlight outdated components',
    snapshot_notification: 'Snapshot Notification',
    source_header: 'Source Header',
    spdx_license_id: 'SPDX License ID',
    state: 'State',
    subtitle: 'Subtitle',
    supplier: 'Supplier',
    supplier_name: 'Supplier name',
    suppress: 'Suppress',
    suppressed: 'Suppressed',
    suppressed2: 'Suppressed',
    swid: 'swid',
    swid_tagid: 'SWID Tag ID',
    switch_view: 'Cannot switch view while searching',
    tag_name: 'Tag Name',
    tags: 'Tags',
    template: 'Template',
    text_search: 'Text Search',
    title: 'Title',
    to: 'To',
    total: 'Total',
    total_findings: 'Total Findings',
    total_findings_excluding_aliases: 'Total Findings (excluding aliases)',
    total_findings_including_aliases: 'Total Findings (including aliases)',
    type: 'Type',
    update: 'Update',
    update_details: 'Update Details',
    updated: 'Updated',
    upload: 'Upload',
    upload_bom: 'Upload BOM',
    upload_bom_tooltip:
      'Upload BOM, all components will be analyzed for vulnerabilities',
    upload_vex: 'Upload VEX',
    url: 'URL',
    urls: 'URLs',
    username: 'Username',
    value: 'Value',
    vendor_response: 'Vendor Response',
    version: 'Version',
    version_distance: 'Version Distance',
    version_distance_epoch: 'epoch',
    version_distance_major: 'major',
    version_distance_minor: 'minor',
    version_distance_patch: 'patch',
    version_distance_tooltip:
      'Specify the difference between version numbers, or empty to ignore',
    version_type: 'Version Type',
    vex_uploaded: 'VEX uploaded',
    view_details: 'View Details',
    violation_state: 'Violation State',
    violations_audited: 'Violations Audited',
    violations_unaudited: 'Violations Unaudited',
    vulnerabilities: 'Vulnerabilities',
    vulnerabilities_by_occurrence: 'Vulnerabilities By Occurrence',
    vulnerability: 'Vulnerability',
    vulnerability2: 'Portfolio Vulnerabilities',
    vulnerability_audit: 'Vulnerability Audit',
    vulnerability_created: 'Vulnerability created',
    vulnerability_created_desc:
      'The date the vulnerability record was originally created',
    vulnerability_deleted: 'Vulnerability deleted',
    vulnerability_details: 'Vulnerability Details',
    vulnerability_published_desc:
      'The date the vulnerability record was originally published',
    vulnerability_title: 'Title',
    vulnerability_title_desc: 'The optional title of the vulnerability',
    vulnerability_updated: 'Vulnerability updated',
    vulnerability_updated_desc:
      'The date the vulnerability record was last updated',
    vulnerability_vuln_id: 'Vulnerability ID',
    vulnerability_vuln_id_desc:
      'The identifier which uniquely identifies this vulnerability within the same source',
    vulnerable: 'Vulnerable',
    vulnerable_components: 'Vulnerable Components',
    vulnerable_projects: 'Vulnerable Projects',
    weakness: 'Weakness',
    will_not_fix: 'Will not fix',
    workaround_available: 'Workaround available',
    x_trust_boundary: 'Cross Trust Boundary',
  },
  operator: {
    contains_all: 'contains all',
    contains_any: 'contains any',
    is: 'is',
    is_not: 'is not',
    matches: 'matches',
    no_match: 'does not match',
  },
  policy_violation: {
    fails: 'Violation Failures',
    infos: 'Informational Violations',
    license: 'License Violations',
    operational: 'Operational Violations',
    security: 'Security Violations',
    total: 'Total Violations',
    warns: 'Violation Warnings',
  },
  severity: {
    critical: 'Critical',
    critical_severity: 'Critical Severity',
    cvss_severity: 'CVSS Severity',
    derive_from_cvss_or_owasp_rr: 'Derive from CVSS or OWASP RR',
    high: 'High',
    high_severity: 'High Severity',
    info: 'Info',
    info_severity: 'Informational',
    low: 'Low',
    low_severity: 'Low Severity',
    medium: 'Medium',
    medium_severity: 'Medium Severity',
    owasp_rr_severity: 'OWASP Risk Rating Severity',
    unassigned: 'Unassigned',
    unassigned_severity: 'Unassigned Severity',
  },
  validation: {
    confirmed: "{_field_} doesn't match",
    max_value: '{_field_} value should be under {max}',
    min_value: '{_field_} value should be above {min}',
    required: '{_field_} is required',
  },
  violation: {
    fail: 'Fail',
    info: 'Inform',
    warn: 'Warn',
  },
  vulnerability: {
    critical: 'Critical Severity Vulnerabilities',
    high: 'High Severity Vulnerabilities',
    low: 'Low Severity Vulnerabilities',
    medium: 'Medium Severity Vulnerabilities',
    unassigned: 'Unassigned Vulnerabilities',
  },
}

export default analyseTotal
