<template>
  <vsoc-drawer
    v-model="isDrawerShow"
    :title="
      mode === 'new'
        ? $t('global.drawer.addTitle', { cur: $generateMenuTitle($route.meta) })
        : $t('global.drawer.editTitle', {
            cur: $generateMenuTitle($route.meta),
          })
    "
    @click:confirm="onSave"
  >
    <v-form ref="form" v-model="valid" lazy-validation>
      <div class="mt-4">
        <!-- <v-row class="pl-6 pr-6 mb-6">
          <span class="text-base font-weight-medium color-base">
            {{ $t('global.drawer.baseInfo') }}
          </span>
        </v-row> -->
        <v-row class="pl-6 pr-6">
          <v-text-field
            v-model="editForm.project"
            :rules="[
              v => max(v, 200),
              v => required(v, $t('domain.headers.project')),
            ]"
            :label="$t('domain.headers.project')"
            required
            autocomplete="off"
            color="primary"
          >
          </v-text-field>
        </v-row>
        <v-row class="pl-6 pr-6">
          <v-text-field
            v-model="editForm.environment"
            :label="$t('domain.headers.environment')"
            required
            color="primary"
            :rules="[
              v => max(v, 100),
              v => required(v, $t('domain.headers.environment')),
            ]"
          >
          </v-text-field>
        </v-row>
        <v-row class="pl-6 pr-6">
          <v-text-field
            v-model="editForm.domainName"
            :label="$t('domain.headers.domain')"
            required
            color="primary"
            :rules="[
              v => max(v, 200),
              v => required(v, $t('domain.headers.domain')),
            ]"
          >
          </v-text-field>
        </v-row>
        <v-row class="pl-6 pr-6">
          <v-text-field
            v-model="editForm.ipAddress"
            :label="$t('domain.headers.ipAddress')"
            required
            color="primary"
            :rules="[v => max(v, 100)]"
          >
          </v-text-field>
        </v-row>
        <v-row class="pl-6 pr-6">
          <v-text-field
            v-model="editForm.domainPort"
            :label="$t('domain.headers.port')"
            required
            color="primary"
            :rules="[v => max(v, 50)]"
          >
          </v-text-field>
        </v-row>
        <v-row class="pl-6 pr-6">
          <v-text-field
            v-model="editForm.domainType"
            :label="$t('domain.headers.type')"
            required
            color="primary"
            :rules="[v => max(v, 50)]"
          >
          </v-text-field>
        </v-row>
        <v-row class="pl-6 pr-6">
          <v-text-field
            v-model="editForm.domainManager"
            :label="$t('domain.headers.domainUser')"
            required
            color="primary"
            :rules="[v => max(v, 50)]"
          >
          </v-text-field>
        </v-row>
        <v-row class="pl-6 pr-6">
          <v-text-field
            v-model="editForm.notes"
            :label="$t('domain.headers.remark')"
            required
            color="primary"
            :rules="[v => max(v, 256)]"
          >
          </v-text-field>
        </v-row>
        <v-row class="pl-6 pr-6">
          <v-select
            v-model="editForm.status"
            :label="$t('global.status')"
            required
            color="primary"
            :items="activeEnum"
            :menu-props="{ offsetY: true }"
          >
          </v-select>
        </v-row>
      </div>
    </v-form>
  </vsoc-drawer>
</template>

<script>
import { max, required } from '@/@core/utils/validation'
import { addDomain, editDomain } from '@/api/system/domain'
import VsocDrawer from '@/components/VsocDrawer.vue'
export default {
  name: 'DomainEdit',
  components: {
    VsocDrawer,
  },
  props: {
    item: {
      type: Object,
      required: false,
    },
    mode: {
      type: String,
      required: false,
      default: () => 'new',
    },
  },
  computed: {
    activeEnum() {
      return this.$store.getters['enums/getActiveStatus']
    },
  },
  data() {
    return {
      max,
      required,
      editForm: this.item,

      valid: true,
      isDrawerShow: false,
    }
  },
  watch: {
    isDrawerShow: {
      handler(newVal) {
        if (newVal) {
          this.$refs.form.resetValidation()
          if (this.mode === 'new') {
            this.editForm = {
              project: '',
              environment: '',
              domainName: '',
              ipAddress: '',
              domainPort: '',
              domainType: '',
              domainManager: '',
              notes: '',
              status: '1',
            }
          } else {
            this.editForm = { ...this.item }
          }
        }
      },
      immediate: true,
    },
  },
  methods: {
    async onSave(callback) {
      try {
        const bool = this.$refs.form.validate()
        if (!bool) {
          callback(false, true)
          return
        }
        if (this.mode === 'new') {
          await addDomain(this.editForm)
          this.$notify.info(
            'success',
            this.$t('global.hint.add', [this.$t('domain.currentTitle')]),
          )
        }
        if (this.mode === 'edit') {
          await editDomain(this.editForm)
          this.$notify.info(
            'success',
            this.$t('global.hint.edit', [this.$t('domain.currentTitle')]),
          )
        }
        this.$emit('refresh')
        callback()
      } catch (err) {
        callback(false, true)
      }
    },
  },
}
</script>
