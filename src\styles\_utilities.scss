@each $name, $val in $flexGrou {
  .flex-#{$name} {
    flex: $val !important;
    flex-shrink: 0;
    // flex-val 不生效的时候，可以加类名：flex-width-auto
  }
}

@each $name, $val in $width {
  .w-#{$name} {
    width: $val !important;
  }
}

@each $name, $val in $width {
  .m-height-#{$name} {
    min-height: $val !important;
  }
}

@each $name, $val in $height {
  .height-#{$name} {
    height: $val !important;
  }

  .min-width-#{$name} {
    min-width: $val !important;
  }
}

@each $name, $val in $position {
  .position-top-right-#{$name} {
    position: absolute !important;
    right: $val;
    top: $val;
  }

  .position-top-left-#{$name} {
    position: absolute !important;
    left: $val;
    top: $val;
  }

  .position-bottom-left-#{$name} {
    position: absolute !important;
    left: $val;
    bottom: $val;
  }

  .position-bottom-right-#{$name} {
    position: absolute !important;
    right: $val;
    bottom: $val;
  }
}

.position-center {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

@each $name, $val in $theme-colors {
  .text-#{$name} {
    color: $val !important;
  }

  .bg-#{$name} {
    background-color: $val !important;
  }

  .border-#{$name} {
    border-color: $val !important;
  }
}

@each $name, $val in $colors-simple {
  .text-#{$name} {
    color: $val !important;
  }
}

@each $name, $val in $opacity {
  .opacity-#{$name} {
    opacity: $val !important;
  }
}

@each $name, $val in $opacity {
  .opacity-#{$name} {
    opacity: $val !important;
  }
}

@each $name, $val in $min-height {
  .min-height-#{$name} {
    min-height: $val !important;
  }
}

// @each $name, $val in $theme-colors {
//     .badge-#{$name} {
//         @if $name == 'info' {
//             color: darken($val, 8%) !important;
//         } @else if $name == 'success' {
//             color: darken($val, 11%) !important;
//         } @else {
//             color: $val !important;
//         }
//     }
// }

// @each $name, $val in $letter-spacing {
//   .v-application .ls-#{$name} {
//     letter-spacing: $val !important;
//   }
// }

@each $name, $val in $bg-gradient-variant {
  .bg-gradient-#{$name} {
    @include bg-gradient-variant(nth($val, 1), nth($val, -1));
  }
}

.text-gradient {
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  @each $name, $val in $bg-gradient-variant {
    &.text-#{$name} {
      @include bg-gradient-variant(nth($val, 1), nth($val, -1));
    }
  }
}

@each $name, $val in $colors-simple {
  .bg-#{$name} {
    background-color: $val !important;
  }
}

.mb-30 {
  margin-bottom: $mb-30 !important;
}

@each $name, $val in $section-height {
  .min-vh-#{$name} {
    min-height: $val !important;
  }
}

@each $name, $val in $section-height {
  .max-vh-#{$name} {
    max-height: $val !important;
  }
}

// 以下为新增样式
$direction: (
  'top': top,
  'bottom': bottom,
  'left': left,
  'right': right,
);

@each $name, $val in $direction {
  @include theme--child(border-#{$name}) using ($material) {
    border-#{$val}: 1px solid map-deep-get($material, 'dividers') !important;
  }
}

@each $name, $val in $opacity {
  .opacity-b#{$name} {
    position: relative !important;
    &::before {
      background: currentColor !important;
      width: 100%;
      height: 100%;
      content: '';
      position: absolute !important;
      top: 0;
      left: 0;
      opacity: $val !important;
      border-radius: inherit;
    }
  }
}
