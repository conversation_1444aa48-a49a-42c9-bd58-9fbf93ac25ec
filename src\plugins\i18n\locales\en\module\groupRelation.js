const groupRelation = {
  currentTitle: 'Group Relation',
  assetCount: 'Total Assets',
  dangerCount: 'Risk Assets',
  groupName: 'Group Name',
  selected: 'Selected assets ({count})',
  unSelected: 'Unselected assets ({count})',
  unbind: 'Unbind',
  binding: 'Bind assets',
  selectedTotal: 'Selected/Total:',
  selectedHint: 'Confirm unbinding {count} assets from asset group 【{name}】?',
  unSelectedHint: 'Confirm to add {count} assets to asset group 【{name}】?',
  cancel: 'Cancelled:\t\t',
  hint: {
    unbind: 'The number of assets is 0 and cannot be untied!',
    success: 'Unbinding successful!',
  },
}

export default groupRelation
