/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/

(function (root, factory) {if (typeof define === 'function' && define.amd) {define(['exports', 'echarts'], factory);} else if (typeof exports === 'object' && typeof exports.nodeName !== 'string') {factory(exports, require('echarts'));} else {factory({}, root.echarts);}}(this, function (exports, echarts) {var log = function (msg) {if (typeof console !== 'undefined') {console && console.error && console.error(msg);}};if (!echarts) {log('ECharts is not Loaded');return;}if (!echarts.registerMap) {log('ECharts Map is not loaded');return;}echarts.registerMap('world', {"type":"FeatureCollection","crs":{"type":"name","properties":{"name":"urn:ogc:def:crs:OGC:1.3:CRS84"}},"features":[{"geometry":{"type":"Polygon","coordinates":["@@࠿@ᠳ࡚с̶ʩт˷˔ƹͮɠͲ˖рԜ׷҄̕ҴNܾͰؼ÷۸ήԪҮƴ̮¿ঞɪֶϤϲŹƛе²֭ĝǱƊĠǔōϭñėށ؅ਓɁȥԯ঻ƻԓࣗඩअ৯۝ڻो֑য়ߝล໥ϓ؇UļāļƫȖɩ̌]ᶼ̈́͘ɮ͜ɪ͚®ĞĚ۪ƼɦːϪǰ;Ąߞᢌᣮ"],"encodeOffsets":[[49130,8189]]},"properties":{"name":"Somalia","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@ƛ¬Ð@ĞÆÒªǱ"],"encodeOffsets":[[9810,48188]]},"properties":{"name":"Liechtenstein","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@⌗øĒ୬ͪࣆيʊˆ҄ؔ^˶ȇɪÈצϲ֌ĐФ؜ְദ׼ҢڴήࡔլİtʍԾ΃ʤ՘ĲʢË΢ǒŎɽĠPĀmǶVǢgΤ̑ĮவňȵǑ¼ýĈå²yĞÇÞ³ëąÍĥʹ೵]É³Ɨ{ΡŉƑcăZÉSÍƫH˃ǨǓѭǇƣġǥÕȿÕǃñÿśıƇħěŽđұĿЍUƉÝåSĵ^čfʉdʇǍ·ণ֣oۋ@Ĺ"],"encodeOffsets":[[-8891,28445]]},"properties":{"name":"Morocco","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@⌘@ķœ@@@Ŕ@@̷@̩@ǕBȍ@ȋ@ƍ@ĩ᫫@@ɋ@΃@ɭ@ŕ@қ@΃Oх̉űƽw½gΉɩķƯtįQJǳ՟TšRšPėRŏᇷCฝCċα`ý§¨Ƙ࢘Ӕ׎ŰшΞ׎ǇĹèƤ࠴݂Ģָ͌܌۴КͲޞŚƀ","@@i˷@@j˸"],"encodeOffsets":[[-13351,28480],[-8984,28120]]},"properties":{"name":"W. Sahara","childNum":2}},{"geometry":{"type":"Polygon","coordinates":["@@A@ƟwđĵȏÒʁč«Ǔƅ@ã̌Ο˘SǴʒÊÆ`Å_֡˰ϟʊkIDZʪĠɁ˔˶àϷˈȘѬ˻W²ɤ˲ÚͩǀºĬȋːÈĆѠŎǊĘȘoø`Ȓg¤_cº·¶ÙȈėÒ÷´fFfV\\T^Qhɿ՘ɳkĝƱƨ÷YißMµGuODƎĨãɰİÈßĜhŴĬŬ~Ũ±âZ]ãNÇk¹ėǌƽɣȭkěHãƺ˝Ǵč¬­¡İÃƩǝÿďȫẸ̀Qßço}ß]ĕUΥ@Ʌ±@@"],"encodeOffsets":[[22080,43262]]},"properties":{"name":"Serbia","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@хǑޟܯőࡡԧ¡á͘з¤̧սշŊƉˏͭąٝ°̺׿ٓɥɓӱз·ʫɫƚν@ӧʟƆƋʇğԻzЛϭĭ޿ॕʳכ]ʛŷѿĘࣻõЍĺٵȀ͋ĠШъȒȢȌȜxèFƆ·ǴăĆˁȡ|ɍZ{ƒZØaŤDĠƲDèĳ̎őͨaŦ̰̚˽êóļ@͒ƂǲȸZíĊ´ubSv\\vÀTÎ hňǞjΔö´ƌFgÈèTɶǓЀ«Ȁǫ͊î̲ǆzĺîĢƘ؜Ɣ˒Ř˞ǈ°òńƲъÖŖ¾ŚǶRɄJǮˊרȋ¦HʲpȮńċľsĺ¤ÚR|¼[þJ¬wNwNÌÛļûĢmǌÂpp^ªô¤ʂĸ®T¸SZ^b\\~VvL¤QȂƓî§R¦¼¶^ê}ĞlôÒÈŒǐ`ƐǢbÀèHĎ±ĸ^ǠŶˌ̆ѨīÞŗ­ɃaÎJĊtÄB~oűځĈʁŔů࠰˨ȦȨˈŢƠ®ĈRľsª_ÂW\\a÷ËvkĄOΘĈ²ʹkÒČÑ¼­§MÕryWJ·oğ}ƽáHiĸã|u^Q"],"encodeOffsets":[[76331,37911]]},"properties":{"name":"Afghanistan","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ÂR̮cҮƠ͎Åǖ@ǔ|ɔDªĞƶHǊŖ̈ƄĺTǊ|ʖĞGR_¿ƽA½n½¦ʬGɼEҾi°oČ଩ýٵ͔ӑĠճēśnʕ¬ÓIàþÖÚ~ɈqɘM͚Ĳ̴ŐŢdܓ×މoěG±ðƁIĵY൑@ȗ@@ڟ@ˑ@ˑBౝǶ֡া࡙ࢳǑ೟Ƶҧƀ਩¼ҹͺሹTᅍeғ̴ʟÌՋȭǃNOėFǗģµƇo͆ƖËܼɠˌ҆ლζ֌θɌƴȞǀЊ¢ݐс޴˫ߚ̢̤hˊүஒґۤݴɦƌOՎNዲQĮĘÇƈǕϡÐŻĚƉÆ˕Ԃۻ"],["@@̆Ƅ_øęÞËr___ğıͅ˭DeÌ·ӏɫθƅːОΖ"]],"encodeOffsets":[[[18002,-8293]],[[12802,-4697]]]},"properties":{"name":"Angola","childNum":2}},{"geometry":{"type":"Polygon","coordinates":["@@Π˗ä̋ÝԍȄ́ɊßQÙÚŷűŻȉ̍ǝ©¡aquwW@yÐğBƧůÙPÑXkAűˬѽ̨ŚAĹɐȨࠐDŮȡæ½ʮ̾ϦŒŷɴÒ"],"encodeOffsets":[[20546,43569]]},"properties":{"name":"Albania","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@Ł^ĊĤ³"],["@@ɨ÷ϫƯħƾƊ¡sƜƖ"]],"encodeOffsets":[[[20135,61632]],[[20470,61800]]]},"properties":{"name":"Aland","childNum":2}},{"geometry":{"type":"Polygon","coordinates":["@@Ɵ½íIir[ò\\¢Ö ÈOŢ~y@esQm"],"encodeOffsets":[[1747,43524]]},"properties":{"name":"Andorra","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ʙOǘøĂç"],["@@šȆĶãŷ"],["@@ø֟˗ȫÁǸǣïԿƖw´ŷѡĹÂĹ˧څ[˥ççᒣʾ߳४oǪǘÚȱɴѼƸॺí̆ĐȾĚ̌҂ৎࠠŞɦò_m̙ź"]],"encodeOffsets":[[[55223,24758]],[[54613,24841]],[[57650,26267]]]},"properties":{"name":"United Arab Emirates","childNum":3}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ؠQۓƫĵÜǪĢ"],["@@nᇜ͠έǇvµǥˠŸɏ׶УࢸНࢴŧʕȣ࢑įԭņ౳¦"],["@@ȴǛߖȡפϿବӗɒ˳͉ӡȧɇ˽ͫЛoÁıξบɿɮȜǴGƌùÚDæ¶ÌEæ¶Ì rºʒàǞȔɌͦʮbΰƠ¥ʐĒɜũȈԵƛ۩ЧʩхŃɫˇԽ˳ƩǟXࢣ਍ʉƽƯɃƅę¡ě½õĳģă¾ɣÙßdƕqùşȅißlgĝëĿēk©z¿HÃiÉZÇÁEÕáųeû¤ͩɅŷĻҩžѻőēȰ͗آ˯͆˿Ʒ̛fʳɔɧ˜¥ÞщܿਟՍ̍൱ͷઃŻأУƊϋȰŉŁMĒå¯̩ŽÍīʩžБůǻӃǿ֋¥਻ΤɛŧŬਅϘŷƋŴcˎňƗ¢θĖƒƙZ΍Εƻϟ̲ЛŹ³ýתǭѫȁ΋͏Yש͏͍R̅ҧكʗցڛþؖ͟ӁٺóɖɁÛʗΉǁʔ@ľĩൿࡕǿʛş֡γƻ·̒LƷˋÉ͎KƨůԹ˻˃ѹƄƒеίăрýѬ֯ౡ̊࿩fЩϘÊܞƛØգŋ̉Ԩ«֊þŴ˜\\Đʊ͐ƢÌ̮ȒĠvɌǃȞƠ͒ψʔ̶ȬưƧЮȴǨī̀ͮȺȹ̒ϏÚmŎڬRĐǠ«Ĵԛ¶ŞϢȋӨżĢͩȺب̜ǤƉͬiߪȊʼğǤƄž»͖ƲŚ֖Ҙˤʷ۾qޚŘˎմϲUҐŝɶƌČ˺܆ǚÜ߆ɝèÀˊ˻ڨĠł˥͚ÆΞǰłƀ֌ǒFĦƶīĆÉيǘȄƞׄМЪˠ٪Ɉ\\ɰƖɭцƪˈƫ؞Ǫˠƫ̂ʾ̘ݨ̞̈ࢂƽưǢʾɼʚ̆Ē¼ĘİxzEÜ}ǘşöãîSƖ`Ѷ_͘ĳèţħčÔƧ¢ÇdǊʒѾ°࣠Ǽʗî·ƚ÷ƄƣΔӍ׶Ћ"]],"encodeOffsets":[[[-66098,-56029]],[[-70300,-56170]],[[-62550,-24224]]]},"properties":{"name":"Argentina","childNum":3}},{"geometry":{"type":"Polygon","coordinates":["@@́{ř̮ǯŔfƖ˟§ʭôķƶɛ½Г˲Իðą̘ŸȦʁ͚՚Ú؞ÞǆǀťıÑѢȓǫƣÜşєʣßǫʳ¡Ժ͏ɼīŷȀƭƹúʇ"],"encodeOffsets":[[47607,39841]]},"properties":{"name":"Armenia","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@āɆƃë"],"encodeOffsets":[[-174823,-14695]]},"properties":{"name":"American Samoa","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@īƨǜMïƙ"],["@@΂ŜªūȩÃŚÕٸǔȢťƙțȃúͽñȐŵɖ¨ĻƣࠅƦƇƥƫJÌ҈ħĊȜͼŒǗëȃŐy"],["@@ƥÈè þħ"]],"encodeOffsets":[[[70946,-50236]],[[70846,-50288]],[[53079,-47554]]]},"properties":{"name":"Fr. S. Antarctic Lands","childNum":3}},{"geometry":{"type":"Polygon","coordinates":["@@ťowîÐÒĎŏ"],"encodeOffsets":[[-63197,17446]]},"properties":{"name":"Antigua and Barb.","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ēǡÎƮŸÔı"],["@@Ȫb࢈ͥ;ðŀùŢƨϸQƖƂɂyĄþȺÙńଡŇȶʏԑlԟƇñĈ§ĐŸþȏêķǁŻȲ­ΧȻºæĳǡ˱˙Ĕщ£ƞǴ¼˫p͕̮ʏאɨɗ¢ŚƹǪą¥FƪЅՊĵΆÔʸ˚ĩ"],["@@£ÝÏÒĴL"],["@@éżBÑ"],["@@ÞƯ͑êʴĆ"],["@@ʠȓbƿǩõ˵͢ȞĨ"],["@@÷ǲƌ˖Š̻ǳƋ"],["@@Çȏ|ƾ"],["@@˪ZǎŁϟȅƩȩüϡ¥ǷŤĈŢגƀɀyXı"],["@@ĥɩRʨĔ}"],["@@ǿɪcɂȤѫ"],["@@čǘ¾̞ǂǀéǆĨðþ˫ɭֿ"],["@@þCȷǡȸĨ"],["@@ŅÁĮƶXĳ"],["@@ĕPŊÀř"],["@@jɇǗȞƮj"],["@@ľàēģi"],["@@̉ƙŒɲʚÔĢĵǃµ"],["@@ųÈÈĮìƵ"],["@@ÕgIżàœ"],["@@ěWÌŖĽ"],["@@ƪdũʉǤģ³ğҷèö̆Țǂ¸Ņ"],["@@īPþŸnƇ"],["@@ƃ×ʤǮşŕ"],["@@Ŭřҿ|żňƴŢŠƒ˽"],["@@ͼȶŨɪȹӳЩρʸŅΨȠƹ"],["@@Ñ»ɆΪƳ̭"],["@@ğɠĂÌ^˫"],["@@Í̉ʖ˱ƺڭʟǪԝǤĩјȚƦɕ՞ϙŜ௳ˠЉƂǶ؇ÿՙʪɽ¦ΣٌΓɼ^˘͡ࡢѧŀǇŻZĥѪқȾϟɀܥĴ¨ŶƇȰŔŦϸͧxƦĔƞ࢑ʼ˙֬˵ʤЋ˜ǣǔͱɔȗÒ͑ưů¡ਗϬ࢝εӯཝñܹ͓ԡõͥ˫ȩzá̏ťѩ֯W̃ſáĜq̡ЁÉћԟۏɗාϝȯകƍࡧؕ՝ÁĴƕŊ®ÇʱȭɾȉŧȢͥŌŪŢÇŴƷL˩əȯƈǎGǪɆȓǈеȥɨÕृԡࣩκؗĬŋįƛr΍ʌԷưԗնÔӪŅʔٓڔǬ¶ĤóMȢٕɡ˳|˞ӶkɖͳԀͭࠉڷąŌ˘̈́DĒلςӨã͈ŠĚǫѠNȳ̷̉ǡλڵͳҏՋàˑƩĴŧÝѳ̖ĀĊȞÿȗԸˣ˪ĉʢƵŜʳtŅƼÕȘńdNƶӋ˺ϟpϣȒҗ§࣏ј̣ĝഁ±༱ԕ୷ঃӭџŏإݵଗÙ÷İտ¢ಧů͹ͥұģٓэҭģࣃĢѽǊΟ̴ұƞ_ج̬Ŀ˘ϔ`఼Է॔ȃ஖ׇࣀř؞۽঒Ś½ƎȎБŸĒĂbƘϩԴ´ĪɘșVʣŊĬŚʵĊÓŀĖAπڟஔÔӈɤϜшĥɶ˸َĎ¬fջȢŢ˨Ѥغ˨Ұϰא̈́טÉښ̄ҪĨ̒ɢМஊκ˸ʆи۾ј̬ǕӀĪΎϬ̊ɀ̎Ӿ࣫~ЬȨ÷Ǟ͑άŔØeǊĺŊǃƼİɌĻІ]̯ĔXƔƈhÔȲŧďĻǶ¸ɞĺEŌȈ̶ƗTŔƍEÏŲǘƀˊīƫ;ͶȚ¢ȐÞȧƄ¼ÐůŌļѤŊsØƉŨŨƖšǰǤIƖńҰʍ؈֯ġԱðĖĊÕáɊʹɦМğƂɵ¬ʐ̮ɥFʐǲnȳƴþøˍƒ˦Όň΂ΠɪĿƂǀȚɨǾJǺȶŃLǆΘʦŬǫՂGǄĂƂğČƈǞXçö͖ǗƂͷbȿƢŜĸǎŉƬô̌ʁɺĈȊʡ؀ŁќȅˀÌʦǫǂwעͪǽ͑ʮdÖȗƮWÌňáŎǌȆɶɱƬc¨ęΉι¶ǿŏȅƕò̯ƓxՋҥە¤ƑلЗðƓΎīäǫǾ`ל̕ҔС٠ƱȨίڌ̽ЎàˎǮϰߜˬ஀đӲÒˊĹ̘ƢҎˆˈǢɃȼVŠƴ̀úßǨजʎȪŰ©ŁȐȭþץʴŏ"],["@@ëµ¹ĸÈ¤Þĥ"]],"encodeOffsets":[[[150893,-44438]],[[148525,-41765]],[[151795,-41487]],[[148260,-41478]],[[151887,-41274]],[[151553,-40711]],[[147383,-41078]],[[148803,-39414]],[[140899,-36596]],[[157224,-28094]],[[115900,-26678]],[[156752,-26368]],[[154775,-24054]],[[118217,-21286]],[[152621,-20778]],[[149789,-18668]],[[142807,-17525]],[[142857,-16970]],[[140384,-16156]],[[127588,-15771]],[[128204,-14929]],[[139996,-14135]],[[139508,-14156]],[[139611,-11880]],[[133591,-11959]],[[133754,-11649]],[[139877,-11651]],[[135776,-11574]],[[146616,-12241]],[[145690,-10961]]]},"properties":{"name":"Australia","childNum":30}},{"geometry":{"type":"Polygon","coordinates":["@@ñǱʀ͋ãʣթɊśȝŕV̡ͥťŕƻࣃÑ˿ǥ܋ĶǭśFƵ^׃Čù~·¶½ÚĩČo¦I\\wPͥąȻXā]ǁMġÑÝ½iÇOĉLÍfÏ¬ğPčBuTDÆÅjǹőҫǦ«ǶĈǒď´ȌªúoŎÀvqL±Ŕ{cíƼò¨ŔSƨLêoŌGƚŃ२ȐdxMÀZR¸_ð@Ŝcǌ_î}t_aDuvkÎø}Dd~bÒLÚſÊ²ƘǋǼϤǐĜ|òÀ¦ƞXN`TŲAŲNZJłXhȠƑբE͂ͰࠬȱФΆǓ"],"encodeOffsets":[[17360,49766]]},"properties":{"name":"Austria","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@Փň֍ը£¬ɜ¾ĸƵʮóˠ¨eƕǰœŚ̭"],["@@ݒ࢙ˎǻ΀áƸȋϓ̯ĻȏӯɑǓˋŷĔƃʝ\\͹ɵqԃόʞƒǁȆǾƄ˛ʈ౉ٷùʈƺǿƮĬŸɻԹ͐ʴ¢àǬѓʤÛŠǬƤѡȔĲÒƿŦǺŐÂvڲɟ̂ěĔXüÒðT­´ˁƐķǎȸǄˎßӌѽЈéڂը"]],"encodeOffsets":[[[47222,39811]],[[49739,42849]]]},"properties":{"name":"Azerbaijan","childNum":2}},{"geometry":{"type":"Polygon","coordinates":["@@guûƿcy¤«£ŇR§èöcĘÐOōiƃ͇͟ϟչȕů˃LǇԮZپǭ˪ôľʎǳʶ^ƜàÀ̈́ǀÝʔĔŨñ"],"encodeOffsets":[[31287,-2457]]},"properties":{"name":"Burundi","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@ʦq̈ʣȋ˥wAá\\ğí£Í}©͋ʁw՝ɸ£̪ɯǉͫVtɴСºÿƞ˓°Ɠȼ̇{ȟ̆ۚɾèōЂ˘ƮڰêƸǅӨŁđͅ"],"encodeOffsets":[[5831,51994]]},"properties":{"name":"Belgium","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@ÁÁŏEĕŶǽbmlʻàƋ¿ǃĿQͻɷȍɽ׏ɧÓ֩aлɍÙÃAck_wL˝óVŃjån»k¥cÇ{ȍ࣫ŹžàȹזĀJqႬǷʾ±ՔӇ͞ŚԜѴϠҮyΆДo˚τƌڒׁ"],"encodeOffsets":[[3682,11977]]},"properties":{"name":"Benin","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@­ΏȀɃ ɯǂɥӪ̃ǱNC˕ӠͿҒÈĸȡŹƁˈ΋΅Гҭzѳϟ΃ҽƊȗ¨˟ȩΑê຋WíͿļƇVֽ^óºĽV«ko¥¥D·ØsÜÜ­­ÃĆ¿ÇYʕǫM͟ŕïÅćÕDķÚĉSąґӂɗĚԮȬ˪ϞӶȼɪɘ©ˮɈĸď˚ƬǀǠĄΜǿȎĈ®͆ʢUÞʬʌɸЊ¹Şʎޤ̨ʐȒѲXϠŭ"],"encodeOffsets":[[223,15270]]},"properties":{"name":"Burkina Faso","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@÷r¢ǆǷ"],["@@ç\\ƪnā"],["@@ęÏΒÒ́"],["@@ũĀŠĐȟ"],["@@əqƒɚƫѐĀzɮ͇ùͩ"],["@@|JeƊÊAɪ̻ɖnńĊæŶɫD͍oȉZ¬WɆÕв«ாDΚȍéāőo[Aġğɷ­ĳǽǗĳ|̓ŧǃͱǖӻÞŌİǩÈWŎÖFȠÖŰŦQĀ@ô¦LÆ[ǞLòχzˁĒǍx¥Î՗\\ÏPVЇƉʧŒŵūŨуɣ͐ǉনÇ«ˉӴɛȱɩʑή©ӆ£ƛʉ˔ǃſѝƲ͓͝ҁƇ¶ŜɨɉőþÈ̎ơЛȱǻïђḍūǋɓŶ͸ŉ̜iǰ¥ǔàŬǡÌuȦ¿¶ËľŐˠ\\Ê\\ĮNUå¨ťjƉĜĥNátÉÄ¯ÞL`¤ĖǜLúAºäìȐİ@̚\\ÊõïƂɡ̗ɲ~ȸƶƜĚĦ­ĐXfæŰƒȏɐ"]],"encodeOffsets":[[[94157,22025]],[[94079,22357]],[[93339,22708]],[[93755,22920]],[[92957,22620]],[[91076,26876]]]},"properties":{"name":"Bangladesh","childNum":6}},{"geometry":{"type":"Polygon","coordinates":["@@ĵ̓˧NǡǭνͻɿѼнҏmƱŐ¥\\ȝéɧuÇËÏŵsySyFyĵxÿckkgÞŵXñg¥ÍţśfɏŧŋiБĦƫòwr§î}FγyoµkQ́ÕċAđZ½SōGëÁȫ^ôͺƕɈЯːà^p~àèRQnół~ǴˬŦƪǞįÄ¢«®ǳĎƹ˞GäĜlĄÀŠŠ˸ƩƇŉ¨ħᓞƏ؀˲ܸƈ˶ŭϬ˴Ƿ̨}"],"encodeOffsets":[[29272,44793]]},"properties":{"name":"Bulgaria","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@ÛĝƄHɜĬXl̛"],"encodeOffsets":[[51822,26505]]},"properties":{"name":"Bahrain","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@řǵѡGBņƂôʎƪƄĵ"],["@@ʚďΏ¬Ķ¤"],["@@Ï{̶̀¡ǀĖ`\\ǉ̵˙"],["@@ǽWņɀĭ"],["@@͏ɬþĒƻʹЎٱ"],["@@̻źxÌ̄ȅ"],["@@ĹĉrŖĈ"],["@@ćïŞ¬®áWͯǓ\\ȓϾ˼|"],["@@Ǐ»Ăńˋ΢ΚЩ"],["@@Ƿ`ʊvÑ"],["@@CȳʣƧďǦȃ¨ËêǊvҌȤęȚ̓"],["@@ʰſǾǇЭųƚĚJ¬ʠǫǌɁĴƇIÐŚ¾Ý"],["@@ӐۑǅȯǄǀěǜǺĖÍ"],["@@ƩĸƀŎPͼɫʈη¶Έ@Ͼ̽xǑŇý·˱"]],"encodeOffsets":[[[-74779,21702]],[[-74794,22968]],[[-75987,22747]],[[-75834,23269]],[[-76636,23444]],[[-77480,24013]],[[-76215,24646]],[[-79521,24832]],[[-77115,24781]],[[-79203,25615]],[[-79609,25301]],[[-78488,26100]],[[-80376,27371]],[[-79079,26526]]]},"properties":{"name":"Bahamas","childNum":14}},{"geometry":{"type":"Polygon","coordinates":["@@˼Xȗѫϸˇ˵ßɂ˓ʩğ{D©OS«^­To_ðƛSÃ¡M®ëNď¿Ù«§ǟœ_nǇäŕġå٥˲çÆÌ̓τࠉ֮Ṷ̌ͬȶªΎȬVɞƱȦǨ͚¼ݨǗ܌@ƤǍƞ["],"encodeOffsets":[[19464,45947]]},"properties":{"name":"Bosnia and Herz.","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@ŘñࣸįkŻº¹ÎŰÊ®fº@ät¶²ZĘQǈR͢ƭʀąKơãÇUoHyÂ±æïh¥@ůŕs­[¯PmƼóŚ~oÝőĢĪƉĖăȌíƎ¯sX}WÅãyÈeƞJǬ_ɀŅɈˇ»OÇaĝ˝ƭʕ^ƳĂɁI{Uo½ßw¶³øÑüÁ~jȏOȃpw¤mÊāJqǃNȑEŅ¯ȥGŕÿ÷µóåëĿ§ýzÃ»TÃj©ƵåʏǢۃăȳȶC¡uw§SßkıěLd£ðÉh֡~ƅĩSĂͥrubŅŜËRķMƯbࠥŸண±çýŹġůÕwKñÓ\\íHýĭQM¢ѮБȸجОαজØiƦMϨ}æwʬòŮIlpĶvƪWÆlÊºʂàwl¥{ÚǊ¿Ħïd¹BSjj¬êÄÚzNvSņŘǜĘxň`èÚĸ^ʺQ ØņÌ_jáZƻXrX|¦Ü¢ĊpÜFϬƎѲӒ̂"],"encodeOffsets":[[28824,57491]]},"properties":{"name":"Belarus","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ÝŝĔɜuĽ"],["@@˵CÒྪzƀ˞KʄИȒv­ħȀOĿƽԭÌÿğݑӥد"]],"encodeOffsets":[[[-89961,17841]],[[-91027,16272]]]},"properties":{"name":"Belize","childNum":2}},{"geometry":{"type":"Polygon","coordinates":["@@ŏgǎĞ½õ"],"encodeOffsets":[[-66283,33069]]},"properties":{"name":"Bermuda","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@ĭއҀޕU฿̇ѥޗAх̽গǉȤࣟ¯ʑѽǉc¡ÈÓƨČĪçŤ͗Ĵѵ`ƕ_íTõäǗŠÛ|Hwyįė»̅đɻʙǡʽƕǽǷZí´Cˢʯ݀Uˌ̣ͰØɰɗƜ[˾ǖŰDÄgįĮEhļƊĦńMpkx̭ʐġŀUž¡ƀÅ̾«Ŝ¨Āǁî}nǕξ@B@UÒĽŠl¢ԔҸWƔǄŚ̽ǄǡѐȾͼǝɰ^ƘЪՂǟФĂڬʚ͆ݥನ̌G͌±ƜďɾĀ˜ˆä|ƀXĨiʲʘۺЬরǀĈι]ęçś³S·Ħ͕VōÇȅǢх͸ʹؚ̨͋˼ǝɼVʬ˟ծƋnçŴƑÐÁî±ǶBÄaĂ^ʌz¬RӐʥŒՇČ֛ŇIƳI¬ŴƥŚƋΟhȗXŗ൚¹DÖf¢r~AIǥĵƅO§fǭƛlŹ¢¡̜șðgÔj»RĉĲɵÚƥúûßÅŵϟµƳÆŉΏיɪȡuÕi»]ÕăÅ["],"encodeOffsets":[[-59555,-20648]]},"properties":{"name":"Bolivia","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ËXȦņƦÑ̯"],["@@åĦłĴș"],["@@ǇMŲƘƉ"],["@@ȗ}İĊĨË"],["@@×§àĬFÓ"],["@@ÚǼÊĥţĕ"],["@@ĉćĮːcȇ"],["@@ř¯ĘĄ§"],["@@ėeɐ̮ŒξʖĞĀĿī̗ҳ΋"],["@@јÆ؊őυࣽǣĽőĐ½ƽɭôçǛǻßɕòχëɅҢĨŸpŏtñƞŴՒͮƀԶĥ"],["@@ɯǑģĐǐƨǄå"],["@@͕f˜ƬŌçÑĩ"],["@@ƯʓΟÈÈǂ҈"],["@@cūƯ¾ɂҌ˧ÿĵ"],["@@ĝiƂȈĺÿȵ"],["@@ƁyǾŰ\\ǟ"],["@@ŁĲǡÀ¿Nwʫ̃şݭĘ˝ŒõXŷOƅI½øŻŌʰ˕ΆƥN¶ƢHLżĢ`¢¶ʲÉªňĠfàǜH¤nȒȦGðnÒvQ°ÍÌgФtϴċ´^~PøLÀ²µÀ­âÁnPptzvĖdffHȐĩ̄¢¸VĈ¢ØORÄvJV¥§¦żQrçArYi¡èʬñ̦ȠܪťːˊɌպ֦݈Ęɀ˞ǽь࿫Ɍʯтƛ¸йͩ˝ϯԧѷʵוઁȻJλǃ׼ࢬհǤەʌɇͺƸɾā;ǎ˓ޥƮŸȸӴȂòʀ˼ȺŁĨŎıôfȾˮ΂ҶÌŜąjŞ࿄ؕṇ̌Ɇɂ̘ɣµÅņ®ĨʳǿǳĀĝȒǤ®ǓśÍƗőՋʈɔǬЊļmñ˅ɔȎВæÚłόõ׀˝̼ͬƛࡌĸєÇనܗΪн΢́˚ķńǗӬƹ࢔ƂķҸ๱Aߑэ੓ΡЅŃ`LƉљՕ̵ƿΑͭơ˭EŶˣ܇ˣЭ͏̿ʏ˾ƉƕôįÑȩǏǽ·ȉĎu¹ίĚ\\įԽƮ৫˓ૃ¤ѳйңŏ્ǽƛͧ۟ͥ˯Ƒ˧ýɣ гףʻɱɩ¿̭ޓiǑȰÍɡ֥ğÙʢ¢ǃĊ٫ŧŵĔÉً˛śǝяNݩχषڣºŏɯȻbĚǻÕƝɋØĻʔėɝȹöùȳŇ÷ǎީÇ݉ƭпЅʝЋЭҭࣁԹۧऱܙoʆ͐²΄ʢĢ̞ƴrǆɄǘwɺŔYdƼϯīɯˬŠˣĿ͗ÛÞŭαϹɹȅуÈʁѱࠃ׿ԕwJÅzË®@РhÀ|ô¸îêĄĚöì´ύɸƇ̄ƃĖʥĈ̶̯ϕǦ±ç͑ͺtqMo]ţģÅßU«B[ôhż΅͞Υ˺ʍÿŝ˳n³Ö¢ƆĚưɄʊƾࢤ਎ǠWƪԾ˴ɬˈцńШʪƜ۪ȇԶɛŪʏđƟ¦JŤŚǴĊžۆƬ¨ÀtfǕŪƻơ̈ƉĉoħgûZã\\£wð@Ö_Ę¹ºi áކŇƆMĐăĒţxǕ@±bűƠ«`˝ƹ঱ŸZʼĦڨíʶÁoǊm¦|¬[²kÖÆ\\ÖĄ¼^ÖjvƙŮďôΒך]ń©F¶ƴŶϠàÆùüÙƦıɶQĊ¼ÓiïhǷŴţæ¡¢kźƜeǮP¨ĶƆJǦ}B¡qÕeC൙ºWŘgȘΠřƌųƦ«ʼTċ֜őՈӏʦ«Qʋyā]ɹ`í²ÏÂųƒmèխƌȿǊ«ŖɹU˽Ǟؙ̧͌ͷʺǡц²̔ĥ͖T¸´èŜ^Ěćκযƿ۹ЫʱʗħjſWã{˛˅ɽÿƛĐ͋²ȝPĭGăEƑlķFė«ŻÃÏa©E¥R·éà¿}୞¶ƶÇs{]ͻ˹×ȉŗۻ]ǕвۣĊĴŪĶŪGńąĢŏƐĉÆ·ƦjŵĦƵЄXþ|¯^Õì©ti|jjp|KńǸ|vŘȖԸτĉ̲vÎ Ąƶʢþњ²zŐxú¶ŜĞĮĬĆΊȴܸżĞĶÈ´à^ǆQłd¾v«à¯Ģ^Ú¤LvLÆfɺ಄ɨఈȟּν̶b׾ӨŶˎÑƅ͢ґRHղචXãŀÀŢɐȧ҈̲ʀ΍ѥǦѼϥʌf͈ǖƄşŽêF̴ΖݲΠǒͶӜȌɌցúƥ࣮ϕΦĳɜǲŕ͒[ƞǓָ^К̍ŨΦʪŜɐ·˨ĊҔːɸfͰΐËǸx{TøhǊ¨nÖUĀ¸ČgŶſƃңϘƱ"]],"encodeOffsets":[[[-49649,-28433]],[[-49750,-27035]],[[-46346,-24462]],[[-45188,-23697]],[[-39837,-13796]],[[-39673,-13411]],[[-45567,-3010]],[[-45960,-1349]],[[-53076,-1468]],[[-50819,-234]],[[-51868,-134]],[[-50630,-115]],[[-50931,275]],[[-51636,143]],[[-51356,403]],[[-51506,1986]],[[-61132,4458]]]},"properties":{"name":"Brazil","childNum":17}},{"geometry":{"type":"Polygon","coordinates":["@@ıjǜȂųÇÑ"],"encodeOffsets":[[-60921,13396]]},"properties":{"name":"Barbados","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@Ĩ@ŴҟƷãє"],["@@ɽƳĀ˙ƽ̗ӷҰ̢ÌӒ̤Ĺ"]],"encodeOffsets":[[[117788,5018]],[[117788,5018]]]},"properties":{"name":"Brunei","childNum":2}},{"geometry":{"type":"Polygon","coordinates":["@@ǝŰīĞGČhQ®í×«ÛyÉSLiÅL»q§gſCƝ§ŻJ½º{@ŝÝҟcͥĲşéýƯ}ƭmŻdoºɇÎƹě¤ƵĆĳǶlĸâTf´Ű҄Ԅ² ĮªĆ|ÊÌæêb̰É^w{Ĺh[ľSźPîMʲď´HŰĆ P]¼ɲĉSƍ"],"encodeOffsets":[[93832,28427]]},"properties":{"name":"Bhutan","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@_ƧגࠁȖԋࡔҿĒͅͰqSһ̂Ыࡘȱbȃǎ½àß¦ȡgĭÅÉĭęïՇǳͭΏֵέȿ֍ǽŽƓƽ¯ɡµĉĵͥޑʯÿݏ\\řĠȉNÙtщʺȫFɩƙɿ֥£ÝɇƛŁǇã±ȇ×§ąߣOĉĚŸ۞̧٦ϙ̶EᙶࡄIᵦሶˎ͌ϯզξʌ{͆Ț̌z"],"encodeOffsets":[[25865,-18220]]},"properties":{"name":"Botswana","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@ڊअoЛŻȍÎʣԠųŨ̽ҾŻ̈ˡgɁĈŉ࣪ڕ­ƯǦ͗Պ̃ɒԩ˥úȿřટɀϭ́صvݷˣҫ¼ωӵ༩˞˧̰ΛɔʱγȣЋқ`ܱŕŨˁĻ֙ǀ݋Ƴţ֣ʇҽĵ՜޹ޘÃϪ˩ˊƗ֬®ӮƻƤʶȮϾުɰˊВaΞƨƖƶȪ˃ܪζޒ¾ҜԢȇƲÐĒୖǂβɺވݒô̔؈̮̔Ý"],"encodeOffsets":[[23409,11182]]},"properties":{"name":"Central African Rep.","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ˣ\\͠¤»¿"],["@@œ­ĂƪĻ"],["@@ĵÌƚR£Ý"],["@@ƍWʈĸĹğ"],["@@ͥyɰĶǰŦùȡ"],["@@ȬǍɽ˒¢ɸǼɭWκ̖Ôī̤nǽۥ̍ԡcƓŤϢҎՂϲǨÜɡƋϻԡ˙"],["@@ŊĀҪŝघAуǿKȍЫøń´ƉĎàüȡſΫÐįƘ̑xsƦɃͨΞÿʉɶʵ"],["@@ŢqȡuìǈѺǲѥ̑"],["@@ĉǔȪĉǥ"],["@@ॴΠͰӮࢨҔ܈Ǥذɶࢢ޺ਞֆႺӲૼkԶˋʡɦųēɣއͷ֕Ƙٹƕ̂іųȰȫԘƪʢĹǫ̙ͿɣЦ§ȧ˺ӧՂÙì·ȋï̔ƍࡄį¥ýɤuѸȸƮǱʆŤħÌŏƯ×ΰÙቱկʡKǟ͝ƒďƁÇĊıTǹ৥ۇɋűȠȧfÉˤ˚Φɹŏ²Î൤ۨˀ˃ǎ؎ŮోSҸИƕ{ųŠūʷगΑȍĀêĎ΍ˑε{ɃĒȯHÇΰ̳ľIଘέɜכœħǸǉٝىʏګӋՓϳhǃʃᥫEA"],["@@zùƓŚ¸"],["@@țûÝâʺZ"],["@@ĦéʯÇPĮƺ"],["@@ʡĦZĴǂLĆȥ"],["@@ਫŌோ֌̔°੮ȣ࢖Οĸŧė"],["@@įˈȒǋġĻ"],["@@෺˕նڋۮʱоաĚôâɓɗó˿Âৡ̀ɪɢƺĩǕҿã˵ƄƺĀʵÀbźӫImǌ΂è͟ ͇ȌǯÃœɒċïıĤͫˊΚjƮӻíʍȸάĮ؄ȃ"],["@@Ŀůϓ¹Șʛ޳ࡵ÷ͥդтdƥֆՙ̧ɪ¨ǽǿײįĪŖĻέ܎ϔ°ǓЬĦ˴ĵƱӳ̓ʸpħĕɔ§΃ɗࣲȬĝțЉǍĳʥËȚIȍȓǈś̌͞ΖĬʭԍȌƌǺňȁϯݥǩÀɡáOю΋ʳƏ˘ҺŝˈǟĔʝϥ\\Ŧ͏ƋϫЕЇŋŲάŦרшӧGǫǏҩɖøRȐޓɇಿƔٽƇǡƔ¤ɒ߈Ѷ۱˚ǂÃąǆ©ˀФΎąĝøĴŸǯzmņǔƀȌ«œǰвڰɈĠĳìɚˀ̔˒րȤˮćrĜǪ"],["@@ȋŸİèĜȟ"],["@@ɓ¸̼Üħœ"],["@@ÑʞǊôķ͑"],["@@વʬǊŖײhͺϩ"],["@@Ĵɯ˳ѢƫœĻ˦ƍ»ĥƪWßřߍՊlǺͷŚژ¸"],["@@ΝƙȕÉЊŹõɵʔŤ˄Ͷƕäơ"],["@@ſrðǂÐǳ"],["@@֛̈́ÈòюǏǆǇÿÝ"],["@@ůĩˍ¢Ɍǔǲŋ"],["@@̒yŘṯ̌ʭ̈¬̌ϼĚʑۧ͝ĵͳĸ˔xΛƜȟˤ˒ͤ"],["@@jŸŰcƙœ"],["@@̤Ɍœǯȏ"],["@@áäВƮͯɑ"],["@@ȩ͋M˞ˁ˳ɧÉȄ˒ϋȣѼծß̟ɞΜ˰˫"],["@@Ěř̽ŎɤL"],["@@ûÓÆƒvý"],["@@ǋã¦ƺŦĕ"],["@@Íʝàĉłΰœ"],["@@őØɾɮɦřΑǫ"],["@@ŗWΪĐʑ÷"],["@@ԋʆդZʟ"],["@@Œū˳ƒǢe"],["@@ɒƏȅЯγϗȊJʔ˪ʆ͎f"],["@@̞oYıΡHÞŚ"],["@@έ¤ϡɶٸŝĘƻ"],["@@sȋҕ˝ϕǋֵ£Ǳʚ҆Ϛୂ¦"],["@@̀ȣ·ĳӿʸֈ "],["@@ֿǎȚĀИƭqğ"],["@@ΘǔΖȫႆӗ̾γǯǑࢸØѶ˟سʧ૧ȶõȄ۱Ǽૡۭٓǃ́Ӧ୏īȂˈְȢĹήʦ߾Ѩ͔͈ƟÁȣːȽ"],["@@Οƴ­ɒͤſêʅ"],["@@аåĉëډŠʍ̠ԶƓ¼ƙ"],["@@ʌMśşůŮ"],["@@ǅNϤłɝŏ"],["@@§ëǛĤĒȢŲə"],["@@ߝŏȨ৴ÇòǧǷS"],["@@ʟĎÉɜɐǤɾĹƣϓ"],["@@ԈűĻ׵ӟƧএ̡κƮɘѼ˨ޜ"],["@@ǇÒȆö}Ƈ"],["@@ѕźϘD¾Ž"],["@@ѥƪĦƌʮĒʣ"],["@@ϱĄʜŦɾÓħƕ"],["@@͇~ɖ̊ȁ"],["@@ǛkŚƄĭ"],["@@Ǣވ˲ϑλγė"],["@@ŋƕƉİʖ¦"],["@@͑ŊǈƔѨ×˝ȅ"],["@@šƮˀĩƝÃ"],["@@ÜǏʗÛǴñǩdɈ̦"],["@@ʳÜˀÆKš"],["@@UěڷĿێȜ"],["@@ĹſгFÎĿۇư̶Ğ࠲S"],["@@फ़ʣҤϑОÓ࣏॑̕Ô܉ʤ͏Ǜň̋ÍȫĆƠࡀǠùȨъĭƅ˺ΈÄ̎ǿ"],["@@̖įٟîΊ"],["@@­Gȟ{ͻǕΗʭ̯ȍՑԩٵ@ܥFɗMĿ˝įöœr­MƟõÆÓɏƇࣗ͏ٽıЕŧϻǵϷǵɁ@ϝʨɲӄ̘̾܌ªĆ̅ౚÇɜ̱ƞ֡ʤļƬūĺ̥ũΘˉGȋǭ̚ǍäӧȬࡩΔ҇ǲ҉ǴՓȰࣝʳૻŔȷÀ˿ŕԱȴ˽sÇ@ïƄ˯ŐΟ¼ĳWˣùŧæ٥ŖûĸùŜµɌNʧÆMͅȣBܑ@Ω@ܑ@੻@⿿@、@、@ᅍ@Χ@Ω@੻@ີ@ć@čąƓʂ̌Ŝͭ¯ö˰˽ʃлǦŐƨűȠĂʕ²¤΂ĵʷʟēсȼɱͰǔϢǗ̝ΕaÁŶďƵϟ̓øҚİԡsŞƄŭzJǈĩšΧ¡յʄʐϨؒĄիHˣȕƳǐ°Ѥ̜ƒǐѺ˫ЕͬȰʚÉúǃ؍̧ʻсɋ̶ːƆĈȒ˧ƵȀҢʽíƇˆұȐùƔˈƞ§ų̃śΈEǢţɻǪϽÒõǊȜĤΫaʣΡʱƕ٫ЈʖΆΖĆϩ£̃Ȭܴ࢔ȟĈɅхݦëƄ˅\\ÁĞƟەʘ̑ǞȧÄĆɳł¸Æ¢®ÍÞšŌěþϛϘɯɲɫɾ֟̆ģøÁÎʅǆɡöěʒˇƒ˭]ԳĽĿǁiĥʵȟCˁğŵ«ƉíçZÓʼ؋͔ҧˮˇŚ¢ö¶Ĝ@ÂŽGχUʟũЕĮÕåȥȩ@Ӭ@݄@௰@݄@௰@݄@௰@݂@݄໒ŕྞҧग़çܨǕ֯ʒ̨¬˭îȀǘͲEŶƈֺT¢ǢѼƥʛɉࡖƴȼǐј|íâ˚ࡸǞЄȄ؄ɨĐxǏহ̃गį݉ˡԍ̣ɾćǕöْZšȚ޲̒϶Ñɘøþȭܒі਀ȸɑƓĔċǜ¨চђУƄưžٞɷࠎف؎ǋ̒ÜƹǖӶ̨ȇμľŰ͑ʂÝǵ˙ںrцΞߦLजŽ۔ɽណϻঘÛƹŘڪےǏր̅ōō֙F̥řʳȕʨëᕊŗ౜ĞޔǞόɓӾƼɥȨƨՀ˻EƉчEਤ׋ϛ˺]ȴӌđک߀ƈǾାƐ֤͢שÛųƅࡕuĩƁӵgͩŦ;˂ᅰʸДùʸΑ،ƷƔƕ࡚ªॺ͇٠ૼŪઘĥђæȁɠপϥȼȨҁļϡ¯ϩφζΤÎŋŘўwθȭҠ²ʕɭلǰͯةȒȧΞµō̦{EţࡇՔιȽː՘ZŢˀͣЎǆʄ׺¦ઞҢ͹͎ǳǹ҇fѢːƝǆՔĔŇǇǺɊاşǎǁÓॣȎӯ΂ɈʨΚOŨ̹©ʓƖĒβ͔ƤмÝƂžϹæग़ΈΪǽҾmڢЗ·஖ٍ֣´Ǉŋ҈Åމ̵ӜJʹţ׮ĪȧĳࡲÍЏǃ́fِͻāͿ̴ǽަ࠼ࢴ˹ΆӍáąϡ˳ߢٯڤʰޘ৾ٴÖȩÒȶȖѳƄĵԨ᝜ŻȔ÷̩ęώíêƉ܌¿ӣʫӜČǉՏǗՙWˤʩǮ¤˧ִ̩ơΫ࿃ו्Ԑʵ²ɣĹ͂xўɃ̖ϿΥà˭ëࠍ̢कpƋؐȯళٻѣT๻ӲşÉಋâ͖ı¶øࢺśߚε࿒ųࣟ࡝ױǅ͏ĸÎũПÀǏŲȗƼƹʿn\\ʏցğছȰ̙ଝ̠ŘɿʒqĝĜਆɍʷŗԼŤܺɉɬ͇ؿɓދ`ΦȑԫǷǑȨƳ֫ǒʦűΫʁȻRɨƥԍʇԅ݃ɃęwਗƙǗ֞̑ΙǬτߎUظ୿ུ̫ͭˮ࿘Ϳܾ͑ӾϑྡྷЗ˺ǇŭɧʰǦॴ|౤ȹƤɉǣӋʄΩųڋ׊ԇȓǫ̤g݆ҋŪɻ΃ɛ̼ƶ̾qٌѩ͝ΤͲϊʢƑŚəƠʠɁɸ҆Фʝ̙ͨࡂĖ̄ȷĆĂÐӅф࿀Զঞބà׊ʅ٠̤̅ଝ֎يזʞ̈́ćŕĚǐ˲ʝÈŐ¶ȓʀƬĬʋĈǜƚӻT֖ئэɶĥϚٖʒಲɍxŗϖŐ׬Ǒ[Ą߈ɘࡤ̉íʅɢèɼùƾŵƩý̎ĐȒÓǹƩδɿ०÷ʊǱЪƸĊǋ̕΁ð̩࠙¥ޞěªӝ˴ƿηĭ£ŕŔ̷Ǐ̬ÑѦǐӖjˈƉŸɑŅˑַɕՄŰͤθŐǃƧſʶɠàΏĞȈ܈ɸ̠ʨ̢̡͞˔Ǡ˴ˇźʎĐÁžξç̳ŶsȦ;KťŘ̖΢ˊ¿äą̉ƅРª¶˓ƜæʲʻǏōƜēȤ²ľřұş؈\\ǙȚVƪǥ֍˽˞ŚԬNǽɯϗƻٴŜƲƳ̆»SʥѷĻӘË±ơզȥˡुĶٔɳǝđў¿ʍŉ̊]Âƙů©̢Ň̄ÚƴǷȄ¶ÉœǼƙΥΝܞȾÎĥɂiͱ̥Ӡ͖ɘ¥Ǿʽ͎ÃμĐҶʅʛǣءƃǕƭৃǷ˓ȧȿ¶ʜƕȕȃඬײ׺Ĩ͍płĨٴÅʀ̷͛ƯǊţЬɪ͂tоʙƘɑIӍѫĉӠÏ}ġȟ~ˈɋ੿ֱಅƋୁࠓ໵ǳǍňڝÌṽ÷Ϗǽαթއŵୣ࠻૿Ɏॸʃú×ȁ̝ׯԋҿ˿ԣğएБзЅһȣɷ|˺ƻ؟˻"],["@@ʻºȘǊäɃ"],["@@ߎ¸ʈʢҐఞɭԿ̛Ȃ±ం̤ϣ̆ոq੆εӊ۝̀ćюɦ޳੊ǬǘಠŹࣈ΁ࡼৱŇљ޲εюĹzŪ଼̓ĪţѨq¼αуƜƏŵ҅ŮΏŅqƕ܁þΔƟÑɣ֤ɬʹWǄυࢍǙඩ¤҅¢ðƊਿĨ͛ȶٿοޥÉछɑᵽƳЩ̀Ý̚ྣôݩŢֵՊᔔɐ࿢ñࡌìᄿ͎ምĕഅ֩ɒIŬ៺ϬᕏďʑÚОǊ۹UͻĬȆؚɸɣƐ̈ǦᰶتδÙžȏЃБ"],["@@൏ҼʶǺઞÒؘǃɡ֕͡"],["@@ੂVࣤƓࡰտ൹®ఝŝсÎ̥ͰӿĠÓ͘ાÇ"],["@@ߐŢҤAɬĻľԒ՝ÈӓʐӸȸࢎǁҷɜȀÌ׽ʷʨ͚ȚࡌƁ৕ʺࢎ̎ՔªƆຆżҒăैܩߝϩ԰ŜˁҗزʨʢȋžɸĪۦƥOȕ΢ƋɷȄƎɀসơ߽ʖؘɊႤŹո̃ܵёࡨ͐׌ķęŗݛŏшʝϧђΰ֤ļ̯͑ТƸ҂ʯćɊ͂Š੖ƋΌƫɟȝϋU׍ɛݞǺ̺ÑҜŒź³õɓे̣ԪÎŹ̻ϘϠࡾɞ௢ȡŎŇ෍ρ੾Ɗȗȋɞֺ͂אʁŤʭڣਗɏئÆ੶įѪůRť෗ÂՑőՀØӪę§ʁ஻ÖञŻɬƭ৒ÇʑōĠɓǎÂÈāƨΚÐșϓтȦàů̘Ğʗ]ǢвŸĉȳ͐¬ڎɱƃǿׇFܨïȶĨҨţ։΋تńӪɌŠ٪̛ƥŋԃîҬɩ͕ÁԅĈhƯҖǇՏgȰƵɵǋѓdʑǐŧȗ¡˂u͗ȱʩةȴÜȔʇʅ܅ծߦҬҷȩۑåƚǪ΁Óޡж÷ďȗèɓփƂךεƕĕБՍʆʏ¥Ҁķ~ǩːąÏǫ̰àϞǟǿÿ՜ѿÀƼΞɹɴƐ֐ɉɉŃϤÉśő̯SΊɟЮ@ĵĥ͚ǫɇϯѷѦļсͬ˹ЫV®ʳफИIĭƻȫȨǏ৏֐ƞʽ٫ʬΕk٨ѿϸá»ë๎ڧť˅ᎋ̦ӷƚЀՋIӅŮʯüƈĲ͵¯թ̎ԤƐޅŮĮƒ˗ÍߥӚƸƸ՝IŻŹÅƸ˥ȁƼȧÅȌɥࡩx©ōࣵŻݻĺʇŞ¥τؠɐɌ఼ȍ͕̈́ĂƖχʐՎ[ࡺȂӲeܫֶ୐ҼȶʬӌȊࣃऒ҉LA˖ʝ^ņŷɽMԻɢƪȦ༳̅zʼԔlʲƴۉɎȚǌё÷̇ÀȚŞ؛ÐƵͦёSڃ̀˵ǟҖƅwɝԿ÷ᅗĶؘ̌եǲؓėܙƠ׫ೕŲ̇ÙҕĞÙːேǿߍȼ֡ӊഢįפÈᖑϜ³ߘঀ࠮ࠢː๨ńࡈŇࢭ΃տӵςۙ৐ҝೇ˻"],["@@܀ǵదƀрÁƚƃԽƏ̒ī९ϑڈòѐͳҊêŬƃ̗ȕǀЩ߿ǡԉšǄľΉϵśһîଷ٠ԩƐџCࠟϘъ˂޲̙ڎªöǢɤÏ΃ʔײ`ғŪѯëՑǲҠĨϢųχ˼ࠈHUŴ"],["@@ס³ϋêขǶӓȫ"],["@@ߔǁഄiȸŃร࣑ჿHφõǺȹЛͣࠃ_ӿܸھ޼νǾƊƈຬĴ"],["@@ѾîôƵӖȖࡼY၊ԧÂƯᱝىҩ͍֕÷σקܑĭɷ²ଃ̍ދӪએʞңLႢຐݯӤᧈǔ࿈ʿơĵ"],["@@ͧ_ˬĮ¼č"],["@@ҙÞ˺°Ǡō"],["@@٣ĤҔʂנȱЏų"],["@@}̟߱ჃͪըΌࣜƾࠂǩ̮ͧ"],["@@۱ŋÉŌԬŨʐŧ"],["@@̽ƴ̨ÆVȹ"],["@@ЧµӟĄ๘к׏҇"],["@@ӛéՊʬ­ȁ"],["@@ȀÛӑƅء_ɰżߋ^ƺĔωVĝŔ഼ŀтʕ"],["@@ࠆőɓđ࢟Yȑɮіºªé"],["@@ǃץМóÒɅˇôȍùǢʏ̷ÖÙƗᄩsȽƲ˴¸Ӈƈ౤ʖ᭣ƹǭðոɴঠÝχ¢ϩȦ̀ŒӛĪׄĀದсȹǌӒzܻĆξÎ܏ǲࡠĞޤƕưȔࡔȑ"],["@@͗t଀ňߧŻ"],["@@ӎ½ʡǅڰðʬǱȤ̂࢜ßĨˁӕҗࢽƋ૑h̙Ĳᶿկॷաɚఐɔ঒ӠȦᔱƿšǆ͖ƂңdŷǍ̃ÀǁӋģͥŜ˅ūعǠࣵ¾ʶȐল޺Ƽઃýاº̤ńຼľඡ¾ϤȦலmࢗĈۖȒڰ̶ȵूI൪չᄘMDƂާȘ͒Ǻ۟ȌৼϊՌ×Ʀֱ"],["@@ۣȒΜĒφą}ȝ"],["@@ੱ¨ߌĎ˦ŵ"],["@@ࣆţʯ˓ҐƢෆ¼ٚƻܻᅌŻĹĥ၁ᅊΥ̱ȗؖģɬǤόūІÂǆùʂŐલȇkŒ႔ʒאË࣪æ໨ǝۀ̳ܻȍ࠘įҗë͹Îîˋඩģڝ´ډ̺SˇݥÝՕĊÓ÷ʷḙ̂ďᔫIģ˴ࡱɵ࠳¶˝ǤɇƗΑL۷ԜǢӪݿѐᆵ÷ٟȈ͜ĈਕżД¸̙ƦކĀಮŹ"],["@@ٹȍѨǕωʲǅࢫƯȟȿّľĦͲމʕʏ͛пǞ°ˡ͗śзσ̜̫Ǘ્¹ǰʻw͒ȀߪĎᐄܐሺǑłѺè߮ƹ"],["@@ͧ±ٟİs˄রŷLȉ"],["@@ցۏҶϖG࢜ͯ_Ǐ"],["@@ᆸ«ȯɯᏋdǩǼ׮ü"],["@@ַÏŸ੊§ǆāׇ"],["@@ϣ׉ǎղƌм˕"],["@@۰y਱ȫ״ÑȧᆣƉڕƔ§̾ᙎǘ"],["@@˦˭ඹÐ̇ģԩúࣻḚ́ƚနɤࢾë"],["@@ઐšΏƑͪűႱɧ֗ʆ״¾कƤʃϖߚRবɁ"],["@@܎νܺǘӚɷ׌fۚȣό؏झqڣ̤ಃ°Ľĺ৹ġۇüūǚ૶YϙĄղĐہǪرƳŰȀڏLlǮńæ࿰O"],["@@ŹɍथƤHǐݴ̤ť"],["@@ਰґஂé͈˿ւ¯˙ʴ܌ȹɱߐǡˋˁߪ°ˤſ̠ŐԘ˝ྑʹևʣΡˎ¶ϫّ¢åͯ৷Ξјϓ٫ƘˉƫรƎйŮࠚèୱÆ̱ŤЌtߣźೊ̔਺ø౫ÞणƅƩĤ࠱ĥҹȦ੖Ǭ૳Ù܋ɌƷȂᅄó͒Ŭॣ¹ࠩư܂ª̔ͅှƉೱɼ҆ǰ෎¨޷Ș়`ॲǳ"],["@@ᣈ÷႟ɯᦜŪˆĒЖĻܨ̧ͨ٬Ð࣪ßȉɏᛏχᇃŁჁ˕ᾆǘ♩टঋƀϔʱຓġద}ۍ˥ࢣÉ࿧ưޠıĢɃᅡ÷୩ĈઞɭऺSƇǍ῅טŏൂÄ๨ɋ͡ǥ಍ਈƷ֟ˉᅫÃÃΩՍƩ༩w࣯ǰ̜Ƴً¡ʨýቒOѦʫ׆îʲȇʩƹᑝͭƥʠرrڿȔʠɍ൷[˵Ȉ§ˋܭ«િɾȏǩ֣ÒŕŚOǗݯhċ̰¥̏ۗ¬ŉ̒ऐɸ೰æܳɂ˯̊੘âখͷ܌čࡤ®ࡖκĬŤহϫ౓vɏ̲޴ɦͤgֿÂņʎڭΗՍyʢɘ࠱ǻ՛¦شԈະŜผţ౪ǦׁÓဇþzĺڶÅᜁږßΎᙲ{ႊҗ৲ã̐ÂݕpဏՊㄾҖ༓ò໦ДℹٳགྷÝ֌ǎᇏȵ໩̑Ĉ߶ɖጎȊᐯħಣ̫֣L঍ɠᗄĂಊɂያǥጉ¡˗ŞتàϷàნǔݍ྿ƅԂȖఃÃ̗ĨᳮΌ࢜ř͢þ೨Ë೑Ȣ฀Țቜ̹ĆƜᣒͫ᠛Қ٢ľλĠੈ³ŭǨቘċ୿Ȝᒌ_ഞ̣όØಷͲᗮJ์ʕڃƸՊƪۢh࢘ǗŧƲपКĉ"]],"encodeOffsets":[[[-61222,44995]],[[-68364,45755]],[[-62424,46582]],[[-75463,46680]],[[-75332,46561]],[[-62571,47048]],[[-65342,47584]],[[-63400,48420]],[[-55528,48580]],[[-76501,46084]],[[-129118,50539]],[[-55863,50779]],[[-55391,50939]],[[-129680,50797]],[[-63284,50273]],[[-128188,51300]],[[-130250,51856]],[[-56789,52774]],[[-130994,52710]],[[-81289,53199]],[[-131449,53659]],[[-82669,54014]],[[-134915,54473]],[[-131637,54211]],[[-132267,54393]],[[-132964,54444]],[[-133361,55254]],[[-135839,55427]],[[-134069,55787]],[[-133709,56085]],[[-81897,57557]],[[-80830,57617]],[[-63225,58936]],[[-81629,58896]],[[-70819,60458]],[[-65952,61816]],[[-69871,61687]],[[-80416,62187]],[[-66388,62839]],[[-66591,63365]],[[-81454,63910]],[[-66379,64061]],[[-72025,64050]],[[-83968,64466]],[[-79745,64994]],[[-78517,64916]],[[-86957,66828]],[[-86706,67149]],[[-85735,67376]],[[-110686,68614]],[[-64185,68666]],[[-110489,69020]],[[-75388,69411]],[[-88673,69362]],[[-77492,69963]],[[-80878,69830]],[[-107049,70048]],[[-76678,69990]],[[-104290,70233]],[[-102622,70459]],[[-102394,70670]],[[-81111,70498]],[[-92364,71086]],[[-78843,70804]],[[-103599,71063]],[[-97805,71244]],[[-69544,71210]],[[-79901,71389]],[[-81337,71463]],[[-99778,71315]],[[-88998,71796]],[[-76501,46084]],[[-99105,74688]],[[-117270,74336]],[[-107815,74670]],[[-81446,75423]],[[-88667,72716]],[[-102401,75721]],[[-100628,75642]],[[-95406,75941]],[[-122609,75892]],[[-99692,76315]],[[-97594,76294]],[[-106618,76838]],[[-95787,76829]],[[-98384,77323]],[[-96795,77568]],[[-121168,77394]],[[-80960,77749]],[[-104680,77840]],[[-106519,78422]],[[-100045,78302]],[[-103655,78418]],[[-110891,77883]],[[-91879,78344]],[[-116286,78586]],[[-96558,78759]],[[-118324,79220]],[[-91989,79123]],[[-107067,78994]],[[-97776,79660]],[[-104134,79562]],[[-116564,79621]],[[-113109,79978]],[[-112451,80539]],[[-98513,80417]],[[-105908,81220]],[[-101162,81901]],[[-94090,83081]],[[-71156,85010]]]},"properties":{"name":"Canada","childNum":110}},{"geometry":{"type":"Polygon","coordinates":["@@ÅÑ@ĝ«ÏƜBAҬǥǺŒÆiÅCŉOÏĩȟÐ·K¥]u­mÁNwzµfĿ¡STďľ¯Ląků§¹B§TďƨyAā^čɵíǡťǕȍǦÎ®̡ƚóǘÓÇ¿ųíõȫ֑ēǕÕplÉü³ĿĪ[ɀƯlűMěñ½ŝħãIRAÂÆp¼ʌɾǂhÞxĮƘ²nlŐĘŌĔvnj@bč°ÌºƘǨ³ɮƆ۴HĘxX^G\\]NčMVStŘߜɃĐ³ćǑ@A"],"encodeOffsets":[[9757,48405]]},"properties":{"name":"Switzerland","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ɫªƎŞĞǇ"],["@@ɓśŽĬәșȬࢲFƔƧ"],["@@ڪĉό¸ŽǑˤċȠ͙ۋǊoǬʫÎƓŅƬƥȗӋʠĄɞƴ"],["@@ƾċ͚¸ĶǣƫÁ߱ʎϐl"],["@@̲áķɗˣŤЍÏɗɈǶƌԚŭ"],["@@ąʹSɶǿǟǑϱĻĚɘɳƑŉXēɎѿƸͰŖѰÓ"],["@@ؚŭѦʅγ ʥǮ͹`ω˪˞ƣ"],["@@mᇛ۩IȥŬٱą૫ƶąĈņĂ؄SʂĮĆƃ͞ҩΘIȐ˰ɎŸ̟ȿeĀŋ̮ȱάÖШɁǬŴߋʢƍȄºƔآʲɿè׉ħʖˀŤȿǆϐËѬˮȸƳ͆úƆÛ"],["@@ʊ˭ƃˁƁPɅМʂƄ"],["@@½ĳΩCȯȑŖ̆ΖÒƬÍ"],["@@ğë_ʄʾ{ƽƛ"],["@@ͥĺ˦Ϛćƭƽ"],["@@ʃ˕ɁźĨăȊАn"],["@@ıܝƙIįʈƀʪȣĝėˍН¼ȲʢƉŔ͌öĽĺɖǀ®Ȓ̌Ð͋"],["@@şƪâЬƲÜȜԑ̏ş"],["@@̙ʏР\\ƖʬŖˢم"],["@@űÖˮƻĩ"],["@@ŷÅȋĂ͈ЬCѧ"],["@@ƽƜĂĜüɷ"],["@@ȯãƙð¡ŐȦʴβƣǫɫ"],["@@ɣǑɲԕʍűԏהĮƦͼìĒȾʔőķ"],["@@ŧāƿ¦˨"],["@@˻Wɭł̎ாђčĖʟ¡ɯˋĳ̔̏ˁȅoƿ"],["@@ƻ_ĦðÖÏ"],["@@ŻĒĢÑ"],["@@ƾƯ̇ࢁݧ̝ʽ̗Ƭ́ǩ˟Ƭ؝Ʃˇɮхɯƕɇ[˟٩ЛЩƝ׃ǗȃÊىĬąĥƵǑEſ֋ǯŁÅΝ˦͙ğŁ˼ڧ¿ˉɞç߅ǙÛ˹܅ƋċŞɵVҏճϱŗˍrޙʸ۽җˣř֕Ʊ¼͕ƃŽĠǣȉʻjߩƊ̛ͫǣاͪȹŻġȌӧŝϡԜµ¬ĳďǟګQnōϐÙȺ̑ͭȹĬ̿ȳǧƨЭȫƯ̵χʓƟ͑ǄȝuɋȑğḘ̈͏ơďʉ˛[ýų¬։̊ԧդŌƜ×ÉܝЪϗ࿪eౢ̉ڟŶ͉ɑথʯǙࡓʩĕ݃ɂȧʂЄĮ¦ʑĎ¨ɄϞǘ¾ȈƉ ݝ̱ɫ˟хǼ˘Кɲ©˰ƐԚ׷ìǱĕɱø·ҍѭȤͼŊİȂĵŕ˻¿έ Ɂ҆βŇŴƀΤãϸǪȌȿęƹżňďʸŔƶɫǈ΁Ĵӌ̃ޗȭȟȶύĬɢÀS̐ܿǠɻ͎΄h÷ǈŎĠɺŃȐˣ˪ĵŶϐ˕˫ǩ˺Ȣq֭ˈʞȂ̀i̫ǰưϢKɏƲƆ΄ʉЕũČtڲ˨ĶϷVġϸৌūǿŸę˴ȋɯʉΫɢĸŔʔģĞö̉ƈ˘ȼŽͦşlµıгŨnȔҳƽĢȡɵȔڂҞţʈކĜɪƒüŐǏԟ̻ƉΨÂöʴŦvɑȫϱĔɆ֖śΚϸŒ˨ęѕǸèɔԾȴ¸ŞbƄԏɤɪžÔȘáʶˊʆ÷̶ØƐŢGşɄŢ¶Ȁǁñ΂˗ÔϴɔŴȋǉ·ƨʏɟ͏NġĤƊƿÄŽͰʜॾɖƤƺӆʙ׸²ЀǓ˼A̜¼Ÿ͖nĊДѾޮƤͲЪȆ޲˴њßҶʒμɋႀʢɨǀìвǟ؊ǲɈȄܞƈȎɼ੒ÇҞɤԤŅѺƴ࣠Ǜʼ~ǖȚǪɄరįՎࡨɕଢаŜľǺ¥ʌƖ®ĊÞÚÈǖν~mǂí§ÿ¬śÆ̽¢ſVŽĢĿȦǟÖ²lwPoħŃĻƉFgİĭhCÃǕů\\˽ɘƛ×ɯ̤ͯVˋʰܿDˡʦÍǾƖ"]],"encodeOffsets":[[[-68903,-57115]],[[-68689,-56477]],[[-71375,-56237]],[[-72695,-56184]],[[-73103,-55329]],[[-74673,-54765]],[[-76170,-54192]],[[-70277,-53916]],[[-76618,-52869]],[[-76348,-52507]],[[-77109,-51896]],[[-76856,-51503]],[[-76909,-50008]],[[-76263,-50327]],[[-77322,-49933]],[[-76356,-49758]],[[-76914,-48985]],[[-76096,-46788]],[[-75399,-45897]],[[-74737,-45854]],[[-75504,-45460]],[[-75582,-44879]],[[-75543,-44386]],[[-80695,-34453]],[[-111902,-27791]],[[-68807,-23369]]]},"properties":{"name":"Chile","childNum":26}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ŀ˯̻̩ǃҙ͓ʥҡȕۉˊ٦ԤϨċNðĪބƠΖßÂƌȦū"],["@@ŘScĭȉĖü"],["@@ĳaļòGÏ"],["@@QƜægÓų"],["@@ý§\\Ěâ±"],["@@ĵolǦĮ¡cœ"],["@@ĵoØŢı"],["@@ɭ¦¯ŊˆÙXĕ"],["@@˽´ʷɌնʿ"],["@@ɽǬKŮ˽ĚǙҧ͑̿݅͟§ȴ͉şȁ߿ŦȍȦǱهۏऋϏѷϣɋƍϣڻɽܝҡʯ¥wśУŏ³ǂӖǂ«ĤǈƔҗAQƆɀĂ@ǦʆĘЊӺυζėŅү°׏֛ࣳГΏӏ֯īɍƈƥȽ˳·ȑǬˁȼŇަşŲ˻ŗ̳ˮǋЈbҪϾ¯´аżݠ̻ΖÌԢŁȃ˭Öķƭĩ΃ń݇̇ȱjîǁƹ}ÕʃȵÛÅƤŧsõëǆőܗַɛϝčࣈЋԔ੏c̕ШȣÀǿϞ͝VħƻRˡŬ͇VϟȤПƛְ¢ÔƏܼпǾͷϭŅԅι̹ɉūʦ¾ŜƛՀƞΜ˙ͼĝӛ͕κĸoЃǙĸȗÁǊȫśiĺåƿǭŔ̉˳ÏŁĖƿʑǯuîĳȵʻjƗˡ˅ǉΧǡ»·ȢħįĹ²ʘϙΗɻ˗ÈǊơʌ°á΋êǙω¸ĘǽʷmËɽɫÿhǵӍLåũƏOǶȓΫ΅ƋHĉɃÝłǩŕƟÌ¥ǍșĉÎÛƇĉËȝǿͱƯǱªšőʫĈ˻ǙǝŜćȏʍXɁyͩ̌BɠčsǃǅȈˉG̳ͳų»ŐœʥũȅƗȏǜ¯ȣ̣Ğ˹ʓӯ¡ϙʟſľɍͥǲě«ƓǖŹƕȉϱÎðìʱЀfǸɀ̌ȳķƔkŹͩĥƒˣƭɪħHnɫƫ¼ßŕɱ¥ǳŬ΃͉ˆʵ°ħѮȖɸȧŊə§ŋĦʯ¡Ӊ΢Ήǫƫ˃˅ıȓĆǛɓˑȮŅǋƓƴ́˓ѕʬÃƛȭ»©£ʇRçĒkJQqs³uß©©mǲΩN£HƟqʓÞđaO±¡oMƋìɕ¥\\ovY®RÄÆlĪµ¹ǀЋʃϧRǧɸoɨ؋Ĉ˄۬õĠүĺθŽɾƄǼԻUխǙŦˌƩɚªʄżĂè͎ǆŐɺǆ`˔ɸûǬĸĮ®ԆóڌǕìŻęȣՖϳ˦½MęſÙċgŉėʛȅë»٫˼ϧÜԽ̳࢓ƇϫɝʱҍӁ¢׍rKºÄMlVzÊ¬ÜØ¯îRćgğHůĪǠTƎɻĎ÷°Oůą³GʱĐíNŹOĽTg\\|ĺ]xŃįrĻRéaåÉËą{ĭ©±ӷس· ÉÊaüæȶhĐŜȏðŵçŵői_B¿Ƚ`ȧÑ˧Nűbķøïûûn¤yrÛP»cĹĝãġÀåþ·]ůÍqd¾åŪåîå¶ɣCǕb÷p´ĤôAjVµNǵ³·NӉ̐­ŀ¡ĶuÂáïv̑ăΫή޹Ϝƫɚǁǳrȋ¥Ƌȳß­Ǉǔĥ®ֵɺbĬʃǤɓÜɵĊɿ˶̧áXȐ½ǆȲȗȚîƔy®̈b·ƒšɄŞqш¨Ķm®ƏĶëîå͆³ǢȲǸľÑҍǺƗ˺éǂ̂ȱ`̑gŷǡǯ¾ĥ¢bą¨ËWĪ£QĭœuÕA}fÝżƃÈ¾˪mÜeÂýņËĕ ÿdħZ¡Ū}~ʕÖėe¹_ǓêûB·]]R{vķäGjƾâĢ~¶pXIzÖq¨NȒźȋǺçܠ҅ǘɃĵȋÊėȺĠĪǕɖ|ƘIªǈìøPzĜ¤Gjzz¦°Ď¬Ä۶ʈ{Č°~VŜĚHŊvɾŮCĺˍǎE͘ņ¼áÞìºĈņîdäj¢ĬƐìâ`ԂŚĄŢIƖtȈǦbøäʦŤҨȈЈĮØĆÎhʴdfVXbŊMҜ˰ŜůƜͪĲα޼ń԰ԡƚǀļ൐˾Ɇǁ˒¸Ɍÿøʤʟƞ׸೺ࠤǹմDĴšטȞƠǆŋ֊ȊЂڦŦɒѪўĀQČȸªE¸ƋŇĳô÷ǒ±yãŷˤéĔËĔg¦ÇRćĚ¥ƀËâYŊñδvèqǼƫ®Eâj¢ÎQÚōôĺsdkĂųÒȁŬƹÔąìeÐ}ÑÚœ×Бÿ¤ÓJÍ˷ѕǺˡƢl҆ş۶ÉžDǬrؔù৒ӿ՚Õqȧǚïˬ֫ѴұېÎቚȅѠĠĒMζ­ӖÅͺYê]æo˂ʅƸ·ΘÅмï՜ɉʊʶ¤ǌ@ǿ̦]ĸ£ÌjÆ¢æØİҮȈݸʜ୶ƆѠոlĔdߨˌԜӊܸ˶®ŔѽҬиגŔdäeʤIôĈÿĠ˾ÕɄeǨ{âaÆdȌŐŸ ¨z˶ȚŠǀƮ|ƲȲXѦÌѢʀ´W¦_̌͸ʞĊЌup|AĎ\\`ĞDǎÏˤŰðXŸĄ[ƨWÌTØSĸÌĨsǢąƜqŤZŬmðDlĶ~ƼƽʖՉшÁřĵÍÄĻĸÍؗHΕ˯Å^ǹŞı~§bşbĝIśsœJ½`ïCſËåßăqɿǀûɌĪ²ŬÀVĈIĠ|ĺܦਸ৆ʻӠ˦ڸȈðǲƵńݺ৐ֶжÿЄӳÈvżݢкᔺɘৢ΋ɚÎӘƉ׊ֱӪ੗͐ͣ~̛Ɇş΁Ϙȝע̤ȍθ^הй̜AɑɄɕÑ˓Ȕʹ౞LȚȜξƄ˘Qۚɶ̺ĵċȗƾʵӭ΍ʱࢯΧգȿġÓ̩ƫč࣫ʠͽ˽Ϸŝɰ٭D׋ǁЏէƥĒǇ"],["@@ƅǦüĻ"],["@@ɂzDȵ˥ÎàŮ"],[],["@@těřlĦð"],["@@Ɨ׏ԛژƇδºцބ୊ҬȰ˰ʧѿຩ͝ҩ"]],"encodeOffsets":[[[113551,20472]],[[113035,21600]],[[115348,22160]],[[115498,22121]],[[121020,25085]],[[122697,26068]],[[124162,28761]],[[125232,30683]],[[124788,32249]],[[133660,43557]],[[116734,22744]],[[116752,23053]],[],[[121250,25111]],[[123913,23164]]]},"properties":{"name":"China","childNum":15}},{"geometry":{"type":"Polygon","coordinates":["@@ҒӁĆĊTĸÙÖCŶŖ͠ŖǬNʖÈZÀÄą®°òŷt¸×¨C¤lpT£gåʱ´iË¤ſĀӻÕoOwfY¯qķ÷ƛБɣףȔࢍǤģƗ»ɻ̑¦ŶŭȉٯưЧ×ӒKܥû͏Ôí¹ʲi܏ĳ്և{ȆÙƼՒ¯ʔƳÈÿXÑĴmÖîÿ¨ʱʧƠɜΰëЮkǆàWĶvøƞUĚĮǰúýXÛZżN¢ʄnŊUîû@oɤșŦZǌľÂkánȐȭäcВƐǄˢɞɶñ̲ǓÈǘɐNpmĶLÆT´nnƞăȜƂºˋC}£ùFcĺ¹èMńĦX¢ªŴŚNƤŁĶL"],"encodeOffsets":[[-5388,10568]]},"properties":{"name":"Côte d'Ivoire","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@ɯˉϽީʵȭƼƣ­ӭƘ֫˪ˉÄϩ޺ޗĶ՛ĽӿˉȨ۵Ĉ˗ƾઇÕĂඉǯEgōț@ʱ@ڻ@ġ|KIlsNlȢńҐɵϬĀðĽWïƾƸ°ċĦ¤úǣT·ťΥƚçϴɏİIŽĭ²ƨЌʂܸатʒ˼p®hƐĲĖÚrdjkô]ĶBǊƼ̊ʉàáÖȫ gìZƒêŲƎzǊàÀǢƒāžÖǨȴǼ׌ƞˀƮ|ÎŊŴrÌÎΖ®ļɨŚÒЈŨŔĸθƐɦǊɢöÒÜðHºh̜ȌºքčŮȷ¸óøķՎƲAǼ±͖ѧȠց©ؙĒϷŦʕ͈̋உwɡʍ̘Чڄիθ٧Íɛ"],"encodeOffsets":[[15852,7705]]},"properties":{"name":"Cameroon","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@͔ϵͼʛȢuʦƴмħȴȂǪsѤԕ˂ūàǯƞ@æőÌçŷ̯üǍį͡ϔƻÜȍહ਍Qˏǻͳşణ͉˧ÅЋɉǣdȡĶíǮ˩YٽǈԭBß˵CÁfëǚѡªŵZÓU«ėǃU¹vŭš¦ãĴȇöÙƎģŨĥĚĹĎƓĀɍ̂؝༓ʕyʥϿσǠϡܷɗࡡČȵӦҋΠÍnǆʌÔiઁȃǄ̑űƿ|ʅόƭöśˌ۵ʔΑծ˭͕ڡÚ֩ɢ»ΊމǣWʌͱɒÅģÉ̳͙ĳɗPɇrÙ}ýÕßJ«ÔmʖĔŜğմ͓Ӓþٴċବ¯pҽjɻFʫH¥m¾B¾ÀƾQ^ĝJʕǉ{ĹU̇ƁŕGǉĝƵC©{ɓǓǕ@͍ÆүƟ̫dÁQԁۼÅ˖ęƊÏżϢƇǖėÈĭዱRՍMƋPؙŧȩȰɬծ¨ÒnfȞȮĲĠ``^¤CɺȝȶĮ¶ɢȊ}ΖƼüųëŕȷʠ¥ંबȒآCܒ˚˴˶Ԫܼ֪ˎᓀΚފĴҮ_ܲЌҜδȤʲΜɓ˨̯༪˝ϊӶҬ»ݸˤضuϮ̂ઠȿɀŚ˦ù"],"encodeOffsets":[[28061,5232]]},"properties":{"name":"Dem. Rep. Congo","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@ĳҭΙމˍᒿܻ֩˵ԩ˙˳Dܑȑءઁफʟ¦ȸìŖûŴΕƻȉ~µɡȵĭɹȞ£D]q`ÝÊ÷Ĝ`̅ƃНΕȫςխղĖȎɨƞƴƋǌb°̆ǿŢǎȇŬÊдϬÏ͐ê]ʹƄĒǠėǖ͋ЊçɦɎź˝ĚsǦŘŘȆ˞øૈԩ̶øҶ̾ɪĚɸȽЀˣ¢ՓƛҒĸ̼ઈ˘ƽ۶ćˊȧľԀʈҾŤ֤݌ƴ֚ƿ˂ļŖŧ"],"encodeOffsets":[[19057,3562]]},"properties":{"name":"Congo","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@՛ǏʑЁχ̣Ư˿ŗם̟ԃʶÆɘųŌЙɴ˹çٙɞŹǜ˳݆͢ĔԜŗמۣߤԨŐ̈́ğPȿ̍և}ٙǌե̎ϕҡԹȬ@΄͹ˬকǥѦɿΎ̱҇ɏȨ¿šäĿඟWGձҒQƆ͡ˍÒӧŵa׽ξ̵ȠֻɧఇɹಃeǛƼÕƘµ~ͩ՜़Ëfã¥°ɿŒÓZµÂǫľ­X·gñgéÉǙkƋĢǋÒ¥[Å½Ã»©ȟ×؇N£°Ƒ¾ϔʥŚƗώǕªÃÒ¹xÉÎŃÅx§Þéɪ¡À£¢½¼ÙÁs®i˱Ųū³Яǒг̌ȉǟݵƄÑȰȡǜГŌ֭ЦƓƘȞȎǺÙ]ӪƦń̌tɆǨئ৔ƽi]ƒãħ§ʬĥƘƼÈ˘ñԐƉǴʈȲȁͮĎͰҁպŜЂȊŏ˾΀ʁѶĔƈѮӋÓįŚ^͊ůɚզΒɀ͚̈́îɼŢЎƛ¿ɚϾԐоќħƻőĬćɐҲۤsࢎԀŞʦΞȚϞíŀʏƵƹ"],"encodeOffsets":[[-73031,12147]]},"properties":{"name":"Colombia","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@Ð½ǏtĆƂ»"],["@@|ɳʇƼɌø"],["@@ȧŲÔ̈́ĀÔЯ"]],"encodeOffsets":[[[44840,-12602]],[[45544,-12371]],[[44509,-12186]]]},"properties":{"name":"Comoros","childNum":3}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ŏk±ŪƖlƍ"],["@@yƾú±¿ŋ"],["@@»ûǛ̰ʰˏ"],["@@ȌÉcŁȽDǈ"],["@@°§ǭxãęÙłʼH"],["@@ý¢ŠƵ"],["@@ǥ^Ƃø¤ĕ"],["@@śU{žʬî°åǃů"]],"encodeOffsets":[[[-24891,15213]],[[-23738,15501]],[[-24006,15369]],[[-23467,16627]],[[-24665,17022]],[[-23437,17059]],[[-25484,17222]],[[-25773,17354]]]},"properties":{"name":"Cape Verde","childNum":8}},{"geometry":{"type":"Polygon","coordinates":["@@ʜԑӌթǸýIecgµƁði_¹{}W]i@͛¸GĊÅZǃƧ¾ėVũaƓĭŰɡȳʂȢʵĲƮʧɃǅƚĶǄĻ̄܁Θő˦ѩ˾Aœ̎ʉǅǿȧǬʹüȍ͞ƶϞȫźƎƐƈń֦ȳɴĎ׸ʻǦ¨ÒŎ"],"encodeOffsets":[[-85649,11180]]},"properties":{"name":"Costa Rica","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ͭŋȋŸǰAğǘúŶɸ«Ÿˋ"],["@@ȽŘʈđ"],["@@ƋØĆĶƝ"],["@@ӚKİďж¢ʈƅҜ³ҚЕӸࢤӍŞQïĎðc̔ȑȅĨŇźĘȚɑ२ȁńĭŅɽۼÁ̴ɡ̄û\\Ľ߳ɡĚśīܱĆಳœԤҬŁȤݗĲ̽ʖȥҎՉMܻʌɉɂҥ¥űǎċƃӃĔȓƤ̰ĠŅƨݱbաϹѫ«ŉȑЅƕBż͕ÕҼǾʮˆʎڎʨஜǐ"]],"encodeOffsets":[[[-84543,22090]],[[-79533,22479]],[[-79748,22659]],[[-83801,23719]]]},"properties":{"name":"Cuba","childNum":4}},{"geometry":{"type":"Polygon","coordinates":["@@ȳèƍȨ΂ˏ"],"encodeOffsets":[[-70401,12350]]},"properties":{"name":"Curaçao","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ɚʡuÐ"],["@@çSƎ°å"]],"encodeOffsets":[[[-83322,19814]],[[-81739,20186]]]},"properties":{"name":"Cayman Is.","childNum":2}},{"geometry":{"type":"Polygon","coordinates":["@@ѹÃýƌϵÙǧìƖRÀǮ֔ߖʶԩ̳Àȑ"],"encodeOffsets":[[34821,35907]]},"properties":{"name":"N. Cyprus","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@Ǩë϶ÚþƋѺÄÛ̍eׇͳҹƆōȌ_ňΊö"],"encodeOffsets":[[33498,36016]]},"properties":{"name":"Cyprus","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@òFòrVņ࡜ͷɲŚçǫųήˑ̎Ŕğǆ҈ơʌ¬ÙǅȺŧźÂ҆ſɪ̱֟ɇÙǉ˝ƩԻƵȯ΅ǔУࠫȲ́ͯաFȟƒ¤í¼řêęGnП̒ȡìʓ˚ĺŚ΍θ@m´ÁzrȴǀϼĈƒhী͜£ŜȎ^zGS·oÆcFrXb"],"encodeOffsets":[[15165,52080]]},"properties":{"name":"Czech Rep.","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ɭćȼ̶ǩ"],["@@CďѣŬ̸˒řªƩ"],["@@ʂŵŹǝܨſʀÂIǕǓęĚáМ§װȊϸʤϠ³ΚɧɶXŢʥͦĻƣĮɭCÉCcýÙáŻÿU¯ŀăɖŗĘį¥ć_ÑhÄyÔƗųȽŘǣyé^iƲ»~yx˷ñēŗIYWaqEÅdpT¸yH]ȍ¤śি͛ƑgϻćȳƿyqÂ±n@SQ@ǤōǾɑĹřʔ˙ȢëР̑mĚHŚéî»£WgIŁYʣK]SYM¥Ɲñ¿ě{ϣǏǌǻ±ƗƀÉKÙaÑc}Cõ~ÏulCv`bsí~Џ¤YQN¿cw॥ȏ½JñÔo~DhŋHépƧKTő©ƻñdîœ|A~Itur¿ō ùpȋ©ߛɄŗTsUĎN^MH[W[åWqa۳GBӄѦۈֹƮ׷R͡ʖŤ̄ʁİÇ¸y¼NðȌ˦̇ʤ°żǅŀɮİÌʬȿ̒ܐƒÛĨʾʦʋìź˔ƒՎĻǐǰǠ؊TČȉŘmôƊƌǱļИӲeӠʩЅʰ͡Ě[ʞɿþ˜Ĩɿξ࣎ß"],["@@ŦɦÓŽɀOˑė"]],"encodeOffsets":[[[14539,55214]],[[14039,55688]],[[9974,56142]],[[8508,56102]]]},"properties":{"name":"Germany","childNum":4}},{"geometry":{"type":"Polygon","coordinates":["@@˕п̩æٓčKצӰپ~zv|OȌŏÂ¸Ŭ®dAĺĤȤ˻xɳثѿıX¾ÙϬüǠõ"],"encodeOffsets":[[44284,11776]]},"properties":{"name":"Djibouti","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@ÿké΀ƴęIɹ"],"encodeOffsets":[[-62752,15615]]},"properties":{"name":"Dominica","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@͆ívŅʷ§ΟŨpƘʬ£"],["@@α§žŲɴĉ"],["@@ɉhsƎɾƵ"],["@@ĥĎˢʰǻͽ"],["@@͹äæǎ̰ŵĻ"],["@@ŠЏ˽íϡĬŅ̐چĂ"],["@@ĩ¢ľÈë"],["@@̓ˋǖƯ˕ĉ̻ǁǩΐ̯dʵѢ̀ʰǢǊɑ ɪ̒Ŵ̠¯Sɻ"],["@@ƭTʨ¼ĹÏ"],["@@࣍à¯ўНƴࡎѐģǼȪƮ¸Ŋû¶ʼɗHȃʓʭ»ƹǀˮɒۄÂѼ΀ծƀ˝भզƫơǛι{ŕ̇ɻkĶçεɣäȑǽȏɺÑPţ"]],"encodeOffsets":[[[11635,56210]],[[12851,56285]],[[10303,56204]],[[10992,56065]],[[15450,56343]],[[10901,56945]],[[12970,56931]],[[12871,57124]],[[11318,58627]],[[9974,56142]]]},"properties":{"name":"Denmark","childNum":10,"cp":[10.2768332,56.1773879]}},{"geometry":{"type":"Polygon","coordinates":["@@ċøǜǜk~ĤŊ״ÆٖɳȦǼ̹юw̻Åcõ߂ǳ̄ʗ̇ͫȻǊ˷зH׫ȏƑĴ͏ÁΉՙǉŜŗɠHƐxÊsÐġÚÉĄ»ð\\XĶHļĴbÐM¬ÕŌƈǬ"],"encodeOffsets":[[-73366,19657]]},"properties":{"name":"Dominican Rep.","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@Ţŵ׳CɃēăçĳėȵؙչj̥˾ҝȢįȄїװЃĮϛÂȻʜ१ǫĩж޿ĢୡƗϣŖי͡ʟɡҬ؟ɊحŠėʒÆےȥϰڝ␣ᕧ൩୧ഝ˯ܹſȣĮŖʶĿτ௏Ѻ×ƪϏɚiʆ᣽ၮ᜿༸ἡኒ@̸@Ȳpیত֤¸ʈǎʊcĎeĶ]TÞæƊЎVҲŀžĒĨĜǲʤǄòɀÖǦÖƤĢѮǈǧǔG˄ÎƬÊTĄYƒd΢Ŋ|´ƘÊ೶^ĦʺªźÝ´ĝÈ±zćæ»þǒŇȶĭஶ˯Ȃ¹¸yØҞâ਺֮͆{̢ˤֆʀഺƂ͊ƸڲĤࠖ@ҌȻৈπφƓʾ¤ľצȣ֔ærēͥ˃"],"encodeOffsets":[[8405,37396]]},"properties":{"name":"Algeria","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@šm¤ȰŦîƠ³ȇȻ"],["@@āà¬ĳ"],["@@ǅn˒ǖŋȃ"],["@@ǩĄVĂɚĆÜƣšħ"],["@@ƹbƆƾ|ǣ"],["@@ʙÂòĢǨƣ"],["@@ЈٷęǁȍáʍpÛŖ̀ʪȽɬ¹ɞƗtȢļøč"],["@@çüÈSě"],["@@˻_KÒÐ£ÈƇƒƳṬĉ]vw`YųӍѥԏӷί঳Ι͉ЫĕÚʫ܏ˁƧđ¥e³X¡I±_i½kġåB¿|Ëô³ØĄ]Ō½»ÕYïQ¡~ı²čÀÙ·]ăė÷D¯S¸úńčFIa¦jº¬Œu´rf¥ˢåǂ̤ȘȞӢĥіéЗĕQpǠɹ̑խπƠˬţܚ̊ȘĊɚƢǗɄμѲՈূΠȾ֮ХДŋȢǛÒȯݶƃȊǠд̋аǑ"]],"encodeOffsets":[[[-82054,-3044]],[[-92594,-1372]],[[-91564,-932]],[[-92502,-790]],[[-93620,-471]],[[-92747,-342]],[[-93462,26]],[[-80803,1283]],[[-77091,-109]]]},"properties":{"name":"Ecuador","childNum":9}},{"geometry":{"type":"Polygon","coordinates":["@@քนƕǥ˱੉ƯɃϭʄљѪֳ஀ǥ˿Ҏࣅղ֕DʓʦΟĢ҉ਠᆣӸҋȱcšǌ۩҂ˉբՍ⮿AªŸï°ŝǙƗOᢱBᢳ@@᳾@ᴀɵࡦɐВĝրʢːȚšіĜଖȱʖƑࠎƋҴȁܞͬʖƢǘѺĠ̡ųςæŠψŧ̬êȺǙȳŢķǅ˦ǗưĜÅþ͘ȋࣂ¾ҀǊ¢ĩ"],"encodeOffsets":[[35068,31958]]},"properties":{"name":"Egypt","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ɐĭΣwêļgŋÌbŨƺǋ"],["@@ąħ´ä"],["@@ĳňԉˎ׃Ę˫µ¿Ē½ ¥@yƷĕʵµÕHǭȆǿò·Ö¹ÁťĥɁ¹œßƣqIZȿʠțƳǳŏOŗ£eCʎkǊoǠkǊŞȖ̈޺sˠúƨEÈhż\\ªDĞgȮj ¶ưȢŔЄł\\nj¶v¤ĚǀƴΦޛɞএФڟà̄˺ϥࠆͥКՙӲͩż̿ľRɰ̙Ƣ³~üêÙĭģĹcB«·ūÁȋŐ{Pu}yõĔÃüùĠƵĤûŦāƄͷˆΣО"]],"encodeOffsets":[[[41105,16073]],[[41039,16469]],[[41922,14319]]]},"properties":{"name":"Eritrea","childNum":3}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ąƏƧĬɮ¤"],["@@˯ɯwĉǰłƆʂM"],["@@ŝĄÈðżýåõ"],["@@ëȓȩŧȻ˨ڂȤǯǏ"],["@@ŕħƅɔĬȔЬŬÑПɥë"],["@@ƓɞÒðǞĵěȗ"],["@@ťŨ̢Ȋ]ȗȧā"],["@@ǁA~ĆƄă"],["@@ȅ]ŀƪǺºçƭĵ"],["@@ˈý͛ͫɭìċƂǛÃŉĀڐͰ[Ư"],["@@ΩĮ[Ė̼}Êǅ"],["@@͒Ɨ¥ƿƈ¤׶ʩ৾ğÆžؚȽ[¡ģîJƠ¾żÌĖÕমàХȱƩޟЕࢥȍˋȩƪčʛěޡँĺϧ΀ʭ؇ωʙҟĀśԙÏˁƣЃԡ቉·ʵǱйéǃʍɝğΑƌ˿ϘƖɶƭçϽϔҕċҤФϮůMȥɶ̄ԊҍԘΘjŤĮâȊŗŪȈƨı֔׺ѮˣŠÛɨѹɍŭؽkħ˚ѕǑġƐǒƚĿXðłçȔȃĘǢʵƮ´ǔްɞOŠɄĬтϊƻ৸ú૖ȕޘĔҺŭƜêҎŃҘä"]],"encodeOffsets":[[[-18317,28478]],[[-15770,28823]],[[-17597,28695]],[[-16726,29062]],[[-14537,28846]],[[-18262,29178]],[[-14045,29606]],[[1633,39601]],[[1480,39853]],[[3221,40746]],[[4397,40799]],[[-1837,44450]]]},"properties":{"name":"Spain","childNum":12,"cp":[-2.9366964,40.3438963]}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@צƝӿȁ̝[̿ʉǈȊʱĜgȢيľ"],["@@ȟĠȊUVĉ"],["@@͋ŗōƤ̕äӾŦɖą\\Ə"],["@@Ŝğ׻ӱÌӇɶ̍ȣÑţŏ¡ś͕æηßȟÐ՛ˎ͗öƋ®շŽăJǦОΏó̅ĂɑʢƠňȽŐÀɴӶĄʠƲࣂ¤ÆĲ௺ǁࢌ"]],"encodeOffsets":[[[23161,60029]],[[23904,59956]],[[23474,60239]],[[28685,60912]]]},"properties":{"name":"Estonia","childNum":4}},{"geometry":{"type":"Polygon","coordinates":["@@ÖGʶ¶ƸĖz¦@¾Àđˬ¶ׄėԊˍĴŇΤН͸˅ĂƃüťƶģúğÄûöēӯٽLץٔĎ̪åɟͱƺͭ˸˓ʪст̵ᠴ࡙ࡀ@ᢋᣭߝͽăϩǯɥˏ۩ƻĝę­ūBɋ_ğcŵCǙĨɋǺޡΟ©ąŏƩƍķݗŦſJȏ\\ś~޳ԘȽƂݑ´ɑ˸MŤnžl¨±ǀΙj½ @Hʅ̬ɯڬիӆƉʄݻ͠ʂҌ֢NĴĐJޔȞؖϾȒȆǘőƈĲƲߞҸڴϤŒͰాfDŘ¤ŐPǴȜƴɀʟYrJÞƤ¼ŔĤɂÄŦº¸ÕȀñǮȅ"],"encodeOffsets":[[39354,14775]]},"properties":{"name":"Ethiopia","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ȬÉ¯řʃĸĈì"],["@@ų¥ÙŠȎù"],["@@ǖƋƛłŒ"],["@@ɫªͦZĹÃ"],["@@ҩı̞Áʩ̏ǶʣֆŅզͥޟٵࢌও͗ďĝ́ȀēȇƩЀǟ˓ЈȱSƭљʉৄѵ̒˕̅ͻహ޹ལࡓਖ਼ĽǲǉǙΟ¦äŏɷ×ďÈ৯ʿࠛωƛƬǦʋǀƋŷʋyĺ̐९ǺƜ࠶̋ͰĥـюʦėƒٲĘǴ˴ࣈؘٖͤ߬¸ºԴ֡ǈǂЇ­ϣϔʎҸ̇ˌþʚɻƺдڇΎܙźିѠόJ@ƢѸºۊҭގÑѾƂࣴǳڴͬ˘מϠȊԐQԶƄ୨ͥǈǕХʝĶŽ"]],"encodeOffsets":[[[22708,61820]],[[21966,61983]],[[21727,64760]],[[25445,66551]],[[29662,70679]]]},"properties":{"name":"Finland","childNum":5}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ѻŭ͂ǀź"],["@@ăÒ¬òƃ"],["@@ʾɽÜέױƷՑƾµǶȬ˘˂Ŷϲb"],["@@đAĒƮ@ƫ"],["@@s@²@ê@ŞĈĩȥ"],["@@ÏĊĞyÑ"],["@@λϿHġ̦ȔCȍё½ǻĀǏƿʧÛǫǂðƔȆSغ͎ϔĆ@_"]],"encodeOffsets":[[[182772,-19429]],[[183654,-18536]],[[182559,-17788]],[[184320,-17370]],[[-184294,-17331]],[[-184247,-16898]],[[184320,-16556]]]},"properties":{"name":"Fiji","childNum":7}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ŤQÑŷǓĜł®"],["@@ݲĒĴÉչҕ˙cęŹ˩¡ɳŌ؈ʈ˿º˲Ö˕ʀʂĕ"],["@@ƍĬǤwó"],["@@Τ¯éƅȦùZƂʄƘőŷÅƚ«ݡʥ~ƍҗæņŽɯm¯ĥɅƤàǬјȬǌȸȆ"]],"encodeOffsets":[[[-62483,-53028]],[[-61733,-52697]],[[-61554,-52629]],[[-60262,-52500]]]},"properties":{"name":"Falkland Is.","childNum":4}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ιÆŉǺàż̎EƬșǛ"],["@@ġænȲŌċȋ"],["@@֥݇ɋչˏˉܩŦ̥ȟʫòç¢jqZXRNB¦ÖìŪɄ¼ĖtƨŨ`ŀƤ®ĄsƲˇΦŇڂҼ޶ࡪ˿ԞНƝĖüƌũ²̫ƦʄƦӗ"],["@@ȥw¬ŔǝȸʊýĎɕ"],["@@ãJ¼Ĥhĭ"],["@@ƛ°Xʘǐëɛ"],["@@ǍC¨ɺʤȿŽu"],["@@Ð֧̩س͇ǠäĦɟȘâƐœɚȾɀмĘ¦ʮİǱ"],["@@ñƥȞǮū"],["@@Þ_zY~mªQÀR~¾\\Ǝ͢ʕ׸QֺƭѥۇAӃɭƅǧ´ƗÉ¹Č¯@akiwŋēŏėkmƗ±cƣʹǩWĳkƗŁÅBÁQäJĨŞò¾ĜŲNưk\\ȿŀĩ´¦·dȗƹ̘̟ÉšϋƟ̜ȫýЯΚǓˌvŧͭޡӋϡŁ׫ž̅Ǽ̇ÑکǄյ̵ǧɷƆПভßĕÖËŻHĎšÇPÕؙȾÅŽ৽Ġ׵ʪƇ£¦ǀ͑ƘʺƀȪ࠸ƚŊÛØýćƒܮ҂ҁȯϾͽȸƐvĩӐ՟ǢɭʜȒƫŀƚĤʈµțĂѹBÜƆ˽cĤ౱ǔ̯Ǽ̌ŌȹĴˮ\\БºôȚƚદȆҐʯȸĤϊÅĈĪѼǃŸcͮɭКԈGĶʕࠌûӤŸʋº¶ǎࢸʞ̆ɈäՐܒǲȠ̅̈|ƔȻ˔¯ĀƝТ¹sɳͬUɰǊ¤̩՞ɷʂx"]],"encodeOffsets":[[[57137,-21851]],[[46265,-13288]],[[-52892,4159]],[[-62286,14843]],[[-62700,16272]],[[-63067,16392]],[[-62799,16620]],[[9708,43833]],[[-1206,47006]],[[5929,50728]]]},"properties":{"name":"France","childNum":10,"cp":[2.8719426,46.8222422]}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ÐýəzǊÄ"],["@@qœƹvĬƗϓˬҔµ"],["@@ś«SŴŰć"]],"encodeOffsets":[[[-7359,63631]],[[-6790,63722]],[[-6559,63753]]]},"properties":{"name":"Faeroe Is.","childNum":3}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ōY£ŦƈLjŗ"],["@@ÛåĴƤý"]],"encodeOffsets":[[[162115,6978]],[[141459,9729]]]},"properties":{"name":"Micronesia","childNum":2}},{"geometry":{"type":"Polygon","coordinates":["@@ķ̻ґՔƜˤ¡ȾϿęɷ̽ɩ÷ҵԪ̵÷ેȅ˝ŗǥŗętŹ˞ɥɍЉèǕ͌ǟĘƃđ^ͳ͏éϫÐÉгȈūǍȀš¯̅ǋaƳƌɧƝĕȍڃݺԿҞ˸éοƸ˙϶ƸPǽÐȓѠʂ˕ƶÞǥ¨ǆţµśì̥רȴć̌˴¸׎ĨƷˠīǤŴϣĐȅɌƞĦĺówϤ^zzJ²Iz£`wĒமEѼG՜hŎǰFඊÖā"],"encodeOffsets":[[13613,2214]]},"properties":{"name":"Gabon","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ƻďʽàɀĜȺë"],["@@Ů_˗ƟǍɄɆĲă"],["@@γ{̛̈ˉɯʁVїɪ̪ƲſĔ̢Î˨˘زŰτăˌ˝ƉƩʞnĦƍǛ¤Äɵ΋ȅǗ"],["@@ȏ\\ȔǴɕ"],["@@ÖȡȿófƔǣÙ|Ģˬĺ"],["@@ďà̆Ȏȵʭ"],["@@҇§ƤǔŭĸƸ˼Žaū"],["@@ƉɠŦSdɋ"],["@@TǇϴ½ɵǧïƀʍBρȪϤȘƊƕ"],["@@pű˩Ìʺæ"],["@@̗ɩňÓҵɫčÄȐĠȓɒ̠N§ĦҐǜś"],["@@đǍٷ̣ěƮɷȭؚŌૢKʘȓڽދ׽ǕΦǰŧֿȍΩÊجťϘÚϞĽЮ˧Τܯ८ӯķă˒΍͕ņ͝Y̮ӄ̙îƧʻɇȠąɼƄиOԐǥøʅŽ̝̋ůƗνÕŜƃϻęࠬđaƱοɋ։ų࣭UՋŎèÛƵÕћmIĹߵĨͽëɳͣѝź҃đ͟ɃчX஺ड़ࢪ`נҊܡ̍ԇǜ˿mŦúʛæџĩɡĪ]ǬߺˠȦɜķ̾ӧğΒ˴ۺǒˤãƖˆį˅ǠȀԺˍMͻ˶ĘʲάưћI΁ƕܡÆÿſķÒŝȌиѠǏ̺ʌÆɓĦĜΫɭŦʾ˛ǝũԙƥ¯ȐϾėɆΠנϷȏЗƦ͸Ŭŋ¦ʦˤțŪǴƘƍĺŘǴϜ@ɍǂÚƚˤpQʰӬ­তŘǦ]³ĵ"],["@@ɜĻҍÄlƆɆč"],["@@ɌýȹӇRɴ̻ňʐ¦ǓƌǪĬ²Ó"]],"encodeOffsets":[[[-1091,51907]],[[-4297,54602]],[[-6367,55387]],[[-5227,56780]],[[-6275,57273]],[[-6113,57155]],[[-5916,57697]],[[-7423,58487]],[[-6292,58886]],[[-7378,59068]],[[-6347,59765]],[[-3184,59920]],[[-3130,60447]],[[-1339,61991]]]},"properties":{"name":"United Kingdom","childNum":14,"cp":[-2.5830348,54.4598409]}},{"geometry":{"type":"Polygon","coordinates":["@@˂Ə®³SïûÑēẂĜڱɠÁuǹŏsYƑe؝Ýէuȑ«Wz~nmhûųĪítÒ^ÓNÙMaOoWčĳғ²ďÁRß~ĿvÇhǈǂ¾ɞɱׄշͺ̽êПɰÆ¼ÌӂgʚīŢɲĒµĶ³র¥ȚßRö»ЀŧÄƈ½`eEsUlmÆmŎI zĊlȐòƨɠōâŊjTÔuňöeƶũȂLŤloýȫH}ӘǷǾĉȷǃĸǍ"],"encodeOffsets":[[47417,42504]]},"properties":{"name":"Georgia","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@àāă̇ϼ͍ĭիàğşʘÃҡĭû˂ͻƽڷŔǉĝхǰͯϚ̧ȥɻׅ«ቕ࠵थ˜ʢÄ¼ɼƘǣĤȓࢎɤפƜВĸø°rZePxÖpÿӼ£ƀÌ³jʲTŊU¬¹ľ]ôU־Ļƈî΀ຌXΒéˠȪȘ§"],"encodeOffsets":[[-70,11383]]},"properties":{"name":"Ghana","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@ħǥǔƷ̺ǆȊʳκ˂ѪƵͬȎǬ̐¿ȎЕGȽ΢ʳɣ̛ˢeÐщɔƏbƅƏǃdБȮãmȏânĽÁYǋȚťpɣŅüŉVʃmO¡WŻÜþWùĭǯVě÷ƛuǕ³ơŊȗΧȑ̃ǘĀʎůդɛǈʁěéøï§GÛV]đĳ½ŇLs\\Æƪʌī̸ģng\\Ex¾ŜJzÙĄʏͶőŜq¬Ëf՗GșįʉnŉǋǕ˥ǝġĳǋǷiť̸ɉĬBͤŉȍɈ͍ĜƵʪbǼëßĉȰģóƑÛPĘª°äƒˬºÜvàFŸŀÖÀ~ŤOƚZȄÒDȊM¦ßŏľ¾Ìń`òǺ¹ĬHæլlťŊ҂ǉࡔÄ"],"encodeOffsets":[[-11662,12703]]},"properties":{"name":"Guinea","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@½ɸžŒɌǣࠤǖשiǻĕǭȴࢬAèŠˎîמʷήŴŕΛƟݻːŕƇѷÑiƗۃEĩ÷"],"encodeOffsets":[[-17165,13378]]},"properties":{"name":"Gambia","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ËovŒġ"],["@@Ĺ°Ɯê¡ř"],["@@ķX¸ĸÀŏ"],["@@©ĻĩİƔL"],["@@Ń_½ËǰƙN¥CȉȃÑʽI¿}ĿÕŷßEu·ÛƓ˫­á«§ÒŌƗģLŀƩ¾ƮĽ^΀ǀƁöƵÓõĠĴ͜łܧƵlŶǽiǃŒêƼǉɱŴ°TŖaĖhƦÚĈ^ʰgɞ¬͈Ǆ܆GʆA˰AGåºīñǹ"]],"encodeOffsets":[[[-16277,11349]],[[-16501,11325]],[[-16283,11741]],[[-16370,12168]],[[-14090,12557]]]},"properties":{"name":"Guinea-Bissau","childNum":5}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@Fѻ஭đ_xy¤±JyIy]Pnõ¼ĽrΞڎwŠlȤtMT¯{Ġڼ@ʲ@Ȝ@H՛"],["@@Ƥ@ŋȯ͹ȓUƼɬʶ"]],"encodeOffsets":[[[11605,1566]],[[8946,3849]]]},"properties":{"name":"Eq. Guinea","childNum":2}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@˄ºµċȶŕୂW¾ȍѺǊŻʝଫíûžȵÒܷĆmɸĒiÄŢĬĵ"],["@@ėŐŸȈ̗"],["@@ťƎĔǍ"],["@@ŅzBǶѠɚŧ̳ȳŕ"],["@@ĥĬØå"],["@@ǅČúú«"],["@@ɠƠŘ}ͷš"],["@@īA˒Ĩǥĥ"],["@@ċ]ŤŎŅ"],["@@ơňĪã"],["@@õāŢƎłjȕ"],["@@ĉN¸àí"],["@@÷ÐŴe»©"],["@@´Ęɞˑŧ"],["@@ċ¯ƇŎɔÝ"],["@@É£úžoę"],["@@ȘďǭÃɛâȲò"],["@@ĘąƥǗǆÔØǒĽ"],["@@{ÇəɦɎ³ũ"],["@@ŰʿȥÓńĉ­Ǯʜ¦û"],["@@ǝtČĠũȄ˄Ç˓"],["@@ŏuŞɘMȡ"],["@@ői¯ǈǂƝ"],["@@ĠũԒƏŰέˀą©ť˗ŚƟɮ͝¬̞͍ͯÎ͚ƊƂÕ"],["@@ƺɿęviď̡ĜƒİǙđȓĖ˒Ƥȶ"],["@@ǋzȧʲɾNãėȚȡ"],["@@åƥěðƿGŮ͊a"],["@@ɑĞŪŴƫ"],["@@޷ɤʽŗˇøͧȉˍpĔˡ̄ùŌƯίǰƻ{ȪǷiűʁɒȧǜ˷ؗЎtǈʗăϫآܣơ×Nƀȫ¤ŕƲŏлƛֺǥβʹʄÝ¶œIϳѥ̜ϷſϠρ˟ęνǬμँϋ̆ʏˋ̇ҬĿÁɅʹ̀ĆʴүъɼʰɊkƔŸ࡬̡˲Ɣٗʲďç̕²бùšƒwħǅV˿Ь˾lNŒͽL΋ʔġȀûĄćÌlBWÒÚOƨŰAÏĞ@zxXvr¢bǞªȊ̎ŲżÙŸRÚԤfªxŸ°ǶƲǆxʺĢlĸ¸ź̺SìÂŐH¼TĒYČB͂ÖlRp¶ˤEĐ~EɌǑȂRŞ£ĲÓ֠ÌŦÌh¦Uò·ƶllĀdĶwɖīǗʙƍBʅʗȑ"]],"encodeOffsets":[[[24425,36389]],[[27829,36317]],[[23608,37059]],[[28512,36792]],[[26095,37267]],[[27096,37464]],[[27597,37609]],[[26480,37674]],[[27669,37847]],[[25886,37959]],[[26159,37855]],[[25113,38017]],[[26013,38318]],[[26655,38431]],[[25863,38503]],[[24941,38479]],[[27469,38719]],[[21390,38713]],[[25592,38666]],[[21108,39305]],[[26721,39136]],[[21184,39536]],[[25267,39741]],[[23978,39894]],[[27045,40274]],[[20560,40380]],[[26049,40943]],[[25369,41590]],[[26664,41705]]]},"properties":{"name":"Greece","childNum":29}},{"geometry":{"type":"Polygon","coordinates":["@@@ƢĞĝǯ"],"encodeOffsets":[[-63196,12301]]},"properties":{"name":"Grenada","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ĩķ΁ĈӪƼ½Ƌ"],["@@ǧàǈĦ`ǅ"],["@@ǁwŗǨȴǊΒÛ˫ʝ"],["@@ּřŪǝත̉ԕŮѲ¾ƓÚँƢŎȤϸOяǈϬƸࣚģӆɕ"],["@@ύ`ZĈδħ"],["@@~ɥۇǗ͗ČறÇΘΪ࠸ڬƘ͘Ĺ"],["@@Οò̼Ɯ¤ɍ"],["@@ҥȑխž৔Ô"],["@@Ԟ̷੹ƻˊܘö"],["@@х͚Ǡɰʦ։"],["@@ۓĘࢴȟË"],["@@ࢣėЏŎॾǠјušǟ"],["@@བྷɞѩʸܼచǯ¬ͭ"],["@@ↂʭ㇓Ƨď㞆ƨպʁ᝚ȱಇʹ㏱ƍʵę̀ī෢Ġᗆ¡ܘʧৄʾ๠´ĜϽབྷەᰦܦᆦȯ૮͈཈ຶōࡈȍෑ̳૛ā§ȕቍǃۖūҩƧ೧áଝî؃ȷ঄۩ᅡѧԅߙܰǠ଺ǉpŹ׍ľѧÑϘȿ޴ūܜÉЙဏŤॅțӏĚΣ÷ؚŋ͞ʩ୒«ԂϱcӝߩŒএʓӫĆҎŽ׬Ģľρώ˶ζ@זέçəݙAͫũ઩ĜX˒͇ɛƾʽ๤ţš̗໳ȉݕňޥ̮ѧƋۿɀؖ˹੩ɏ৩ƶؔƿࡩġǶÏኔˠަ\\ᄄЭɍڣ๳͈ҽҐၷ˓༂ƾƎˍϽȡϜøᗨ׉ѩ̣ÀœĲƸҠȖߝܣÁ«ͮɇΡ՗\\եǘӹӖ૕̊ঢ়°gÿਫ਼«Čʟࠍțද΀ćßǑࡃɇᏚfęࠟțƪŏ̚Ǯ఼Ŷ༦ǵ୐[Cûعŏ_ĝۇ±łůҳEBūݥƭĆāԗɳڇǵᙡͅԳ\\ϗĳͷ¸Ġő̱³ଉ͂Ǝϑ࠯̩࢕ࠅࠫͻ֭ǆȦʧؽʹŭƪśǻͭæ̅Ƿқo֨֞݅æͼĽ̛͙͉ĘˈʡခŽԄǹ̟ǕˇýۥªĲțʼ ЊλԋɏڣÜҦƫΎVÊͿȋǇɯg˷ϝϥPˣŜҒȣɱȡࢃaڐŵƙΓǲʝЍŝȨċ̵܍˝ȱ݅Ò׬ǡúˣǓŏՓĈʠƕȽǭ`¨Ā̅Eǂ̐͛ɍ١ƼXƜͤîȀǨԽȁ֓ƞăƊɬψࡇ͵ળ¡ΊȔԭNƶгṷ́ˀûʦҪǄڛåÐȠٟȬjȾъ˔ԅȇ࣡ېß̪ઌǪȏK¦Ė७ǭĻĐʮɒތȖМȷĥɎͩÀϋκȪ͹нƙŊ֕ґ˱޾ɹƤޘ͔ѸÀՋ«۟ɗǭňοHþ͐ʉÒᆖ۪ົ֯х¿Ķє͊Ƭն°ࢳυǰðʄॎ˦ႬȅҵƔǞĨڝÍӣĊଢ଼ʋӪք଺ų˖¸ȸʮࠧǕহǛ˂κĈࠚƣ֬ŰŚŒχÝƒ͜ڌٻڈڰ࿳٥Ȳ࠭Ĵѡɺ̬ĶନÍྰ͕Ǭ¬օƞÈɞѣƂيQ̛İോ¾ĬɸऺǶඛýʜȔэҲɳϘϿҕǉ´Ǘ؍ùݽƼɺҶЎȶحƩ¯ĸ˺®ʳČ֪Ĳǀʨ˭Ťǹç͉Ú͊ˌۅȚɠǜ̶͗ࡋGѲĀUǮϲĈኻۜˈźɡƶᖟЀᄓǎҩœϵČࢿƽыĞӷċѷØˊȣఉø૭˨଴ɄಷîiɈޙƗѹǨɺŬཔj۾Ƹุĉʩʹ࠹Ɓ޷àؕœহȜΞŢঁyಝ͌̄ʎےŢᖂȞԮƲᒪŖࢄ۞ԨĊᖏmǧʶᒈԬژ¶׌Ɨŀˢ௾íӾ؆ಾ|ᖖѫᕭզ◴݆̂ƿĬѕ֚҄ᅈΩ଒p୧ϸݲö⪴׉ЮӴૉ͈ᶰÇюÎ♣ĠǤي፾ȸ෬ƍߌȚᇚ˫ӽ̺ࡶń㹪Ê"]],"encodeOffsets":[[[-47377,62241]],[[-37920,67105]],[[-52237,71222]],[[-53996,71624]],[[-52915,72556]],[[-26042,72624]],[[-54820,72746]],[[-56337,74539]],[[-18432,77218]],[[-19028,77868]],[[-73387,79182]],[[-18035,81742]],[[-45941,84054]],[[-30671,85571]]]},"properties":{"name":"Greenland","childNum":14}},{"geometry":{"type":"Polygon","coordinates":["@@˶DʔŁRƾ̮ȅÿáˍɉɷǩƳãáǧºǑQ¿īÝÛĩǯsvůс˳ƕїǊ٩¼ܛҲÜΘ^tr ĥȺĖƦŀǪȄ̮ʬBϢ@ɶ@ƴ@ÚDYøTĊø@­¤ĕz·@°sÈ§ÞɟƆη͚ǶAǠCHӂໞAÑྩ"],"encodeOffsets":[[-91374,16270]]},"properties":{"name":"Guatemala","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@ýƚȐƾÆǗʻ"],"encodeOffsets":[[148216,13578]]},"properties":{"name":"Guam","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@ĽǳĠŝķƧ͋{ǥƛÒŷǩӇ΂ש̀aĘѱвۇȆāУsËh¯ÎuRǗ§ā¯óœ¡£mǛGßğg©ŅʱÊµ_¡ġKŻơGµM΅Ʀʯ˖ŋ CĄÓ_vB̼œöĕ˞Šݮʬ̄Mx¿ÀıǢłϗƲƄҤŵƀċhÿ·ĽWǉ÷gՓפ]ôĢàäÆoƞB¾}ÈmÚ¶ĚάÖ̮Ƞ~øGg`ĵbµm»X¥²í˘ǤɸИȬĞt~Į´d¶ƻȲۈЋׄ֩~̟ǉխɒϮδÅϊϡˀŹՉ"],"encodeOffsets":[[-58567,5682]]},"properties":{"name":"Guyana","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@ȯǵǪӰŗŉq"],"encodeOffsets":[[75477,-54412]]},"properties":{"name":"Heard I. and McDonald Is.","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@ݷȱĝxǷŲĽµ×ƛűɩѳϟgMOǓĸ³îʓɽ]țhĽWsaW¡PÁȓhÿeyÁIå[ùǹ¥_ɹYŵ̾˙ĺÈʖazÏ¤÷Jĉ_ƇĞʡŗF]¨MºO^ԝ͢ȯǈǈRÀ¹ǒâǨƴäɸǪˎɊĀâĆyɎƮ౤ąΞƞ¥ÐфĽєøΐƿʤjиΕǩĪřw`ûƘMƖƣɚDůŬɶŇâƉÄÌ¥ɍ`Ĺĉč"],"encodeOffsets":[[-85642,15234]]},"properties":{"name":"Honduras","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ŘËͳøɜk"],["@@Ҍá˱mǙĐ"],["@@٦˱æŃ׋̪ٹǾԺı"],["@@ʛ`ĎϊÓė"],["@@Æ×ƕĤĐ"],["@@ƧȬhɞƀщ"],["@@̟ĒĶŪȪȻ"],["@@ÇąȌˏ¹īͪƿ˱Ù±ɣƝ\\ƣǎ܋@ݧǘ͙»ȥǧɝƲȫU©΍̮ȵǔͫࠊ֭̈́σÅËֵϢёŶ΃oڥոÁì̊`Ы˦ŁВʯǼȥϝп˿ը̤­Ю®¶pŢŶpK¶áŦÿƊlƀ£ŌSĈvYOÐäÝÖ̀ňpĺQÄÍ¼IbкǮǸŸĚUöq®]ƮËļÝτ˽иǥҨƨbĎĂȬr"]],"encodeOffsets":[[[18031,43796]],[[17051,44029]],[[18092,43927]],[[17189,44310]],[[15741,45030]],[[14836,45732]],[[15166,46057]],[[19360,47035]]]},"properties":{"name":"Croatia","childNum":8}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@cÏɋêǗƎЈƧ"],["@@ƇǫÖŋN«aÏĻĳĵG[W¼ïÊăĢÙtÏșíâýÊĩۇÝёČ̥½řůӽ΄úƦƆூȑΎŞ|ĎϵͬĜ̴؟ʀȂƊӦdΖſ͖¡²ll}ǛǛČ÷"]],"encodeOffsets":[[[-74551,19229]],[[-73366,19657]]]},"properties":{"name":"Haiti","childNum":2}},{"geometry":{"type":"Polygon","coordinates":["@@ĺDˢɯƾqĜƍ݃χ݅ਇ̣ūħėȑqd£`ȑh÷_ȗpǉėًſčāaƧҧзǦσ˾ĽÞƫÌ­^ț̜ǅL̢ŦUͦȞŖɉŜժäʤԬȣ߲dÊǴ۬Ȉ˚Û͐ŰƀȚ߲tˆǵ̪Ê"],"encodeOffsets":[[22663,49568]]},"properties":{"name":"Hungary","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ĹVhŘѴʌ ƧЁɑ"],["@@ƭʚĠīť"],["@@ùŐľƋ"],["@@ٮө¢ãœƙɑóʙĂҙΨџÊŃǀ˲ŶլÈÐÕ"],["@@՟՗֯ǕţÞĤǈŃĤĶ̒˞ɰȸñƌȨВȺ^ġǲV£ŭŝoĜʣ"],["@@Ň°ä¤č"],["@@õĺŜŤwƧÍ"],["@@áJ¾ʴĢ÷ýȅ"],["@@ʗǊƈĴeï"],["@@ŃǧÔÅ̅u͏ŀȂ_ʶ˺Ȝˊōßɷ"],["@@ŝǝǿêʾȶ Ł"],["@@̹ʙ˕ȸǪí͔úŲé"],["@@˧âȞŖĊǷ"],["@@ÅƺƤĝȁ"],["@@Ќæŭفõ¦ȜŪ"],["@@ʞÑăíǟÆFú"],["@@्ʋȣìïďʗeСŐ՟±ďƈÈɺآƮۮ˱Ѭĺ̚ơѸ˘ÎèƉƄäøȇȉʃ"],["@@ñȶɒƀ˃ǁ"],["@@̴ĐőǴİǠ̷͑Éòć̓mċƴǃƓेɑɭþ Њˢƒ͸·ȪʇƎaɠŖΛɀ¹Ǌ̦xŀǏ"],["@@ɎɃҹω÷ɶωǎũǲЄiǺĸʚõ"],["@@ĿïĕȦǢatœ"],["@@ˡʃγÄȯĉƪ˒ȨͬŔǈă"],["@@£ĢĖc±ý"],["@@ȩŋիfδېǶńЌ¤Ȁʱϟӟ"],["@@ǱDrˀъӔþëûÏg̟ʣ̻"],["@@șîƠZºć"],["@@ş¤ĈÆĩ"],["@@őFOĈŢč"],["@@׽ĳͳŶŢƢǚhނNĊñȕŧ"],["@@Ɓfjüʲßƙ"],["@@ŋtŜúOĭ"],["@@ǵ΁ț`b̦ôXãɦΠɁ"],["@@ʘǩ֐Ï̈щ฼Ɓ΄ѰŚWǬɁ̠ҠȵϞĠʛŪé@ȻȀėРÇӖú˶ƅmԥǒ̫ૣОӗĹঙĔݹƀ૙ώғʏŉ҅ÖՏɄۡĔÄˆ˓ŀ߉þĬƞĨįƞˀŎZè˸ǦǞـƷȆǎ˞ē"],["@@ƓSׄ¨ѯ"],["@@ŏPĔÞ|í"],["@@Ł½§ʲƪȳ"],["@@ӵɯʉɬŮ®ƪřŀŖIȖʈƨɻ"],["@@åïʘ֜eɏǋ˛"],["@@ɍƀɖGĥ"],["@@ăĆìA"],["@@ȟƴĘƺņñBɻ"],["@@åıɿÊĨɀwʬ˪Ű¸˅ūǡŋ"],["@@`ɫşØé͝ǸǍǿÕſɟƕP·ƦưɺòӼȆȚĘƏ"],["@@¨­ʧǂɀœ"],["@@ƕĩęǔȲ~ī"],["@@Ɲ«ºļĤÏ"],["@@ʡą«ÒͺƸ«ƃ"],["@@Ɵ½ĈĈØ"],["@@ȳŝJҦǦǦԭ"],["@@̮ʭFȯҙǅЅȀƃƸˆٮf"],["@@ťÔƘAqÑ"],["@@ՀĽǆšʒσ¯ʋ߯Ҁ˧CĿԕȀ̅ǵ͑ʔȗ˵qɲ˒̚ږƸĕʺŮɪã"],["@@ũɕ˼KǜɌǹ{Ƴ"],["@@ŷȕǓŠȱĻ§ʐĒ̖ƞ͆ƕÚŏéƗ"],["@@ǁáwʜǺǹ"],["@@ȣðȚɬˉ"],["@@ǉ̀ºĐŐЏ"],["@@ʶܑįûĆ՘®"],["@@ÆɽƛÛѕƲԬƨ"],["@@ȀMłƑݫŅóŬôǘҪ"],["@@சǧ֑āهʪ"],["@@īĀŰ©"],["@@ːڛϠğǧˑ²Ʃ֙ȺǳզշŜ˜Ͼł¢ĬǡhǔǄzŖƏ"],["@@ĩpƮÌǝ"],["@@ӹÉƕŜɄɎь˟"],["@@tǽǔŸĦčġȥVďƭmʾɕʃážĆɀʰV"],["@@ȹąZʖɤ­š"],["@@ēDŔƚ¯ç"],["@@˯ƘȍΌØȒɎbˬ֣ēų"],["@@ǿ¼ğʞ̠ͯ"],["@@є×М̱ǯĵȽƙ̘č¹əȘ"],["@@ťh¬ŢúƉ"],["@@ƉŴÎVģ"],["@@băȏoǮĴ"],["@@@ǋDᲗħͥĨƓBᇳࠉޢĪɮƳƫҥóŗǨ̝ɋǖմ˹˰ΰ¼˥°ȵǚɜàΧўţͨàĘƥŞŎɃuȴٿѨ։ń՛ʎڅäѡ̺xľưbԵOĳŲơkʝ˴ƎΦş¡ɷœįХŷƍ˫JƇ̔ĬŸıɎٽьɔĞдğϺ̐ђƽ®ƤŶºħĈŊǈ߫Ɲս~ʅɄýβ՗ƖʡǐɒÈʺҠŸӸ̈ϴ½ԬʷфeŚđŻǀ̣Ź̝öӳɞѯpɀŨØͫŢrʧ̪ƅȢp̺̌Ҁ؈ִƘČâ¡ƺֶ̞သܫ۪ćŀƉȎE"],["@@ĮōȑǉėǐǼň"],["@@ĬűÝšɴǗĳ×řİƉLƎƗŤrƒŌÑĤô"],["@@ŉqNȮƐKǯ"],["@@ɳSôŌǀķ"],["@@϶ũÀş˫Ï͏ɲɶɏůċ½ŮśÏˁƂłŘϚÌ"],["@@űAİʲĕȎȆɍíɯ"],["@@ȐęҷĀĄǨȤǍ"],["@@ũP¢ŴĈƃ"],["@@ҋĢ`Țиȕtĥ"],["@@ĝpÄ¾í"],["@@ϷƶpĘɲùƖǓ"],["@@Ĭ]×ƧőèþĞ"],["@@ÞƕñǽŗȀƿĢƔȊ"],["@@ŭðo˒ƾƍ_ȳ"],["@@ϚЭģ·ŗãȦǕĨ˛ДɶĪł×"],["@@PŇЍɸϾů"],["@@ϱѳ֣ƑЧlɥƖဝ©ιð͗ɓƭϵ¦̟ƬˁɶƝŴˣК~ΪіͦĕʼƨԠD¹Ć˼ôǪß°Ň³ȏЋĔշջ͡řǩǝʝb޾ࡻĜʗūΛπϵƄhr˝ŷªԗǙÝʕϗÖďƮŌШ؍Ӓƌ̔CΔơĘȍkͣʁƨُaއšѹŶϏГ\\ȟĭƇêǓʠȠڔ|ШſѬЇqŃŞë֪ʸȨȖҬYѬǘфǠȎŐƱĹٰŦň³ǖĖǦ̚Ѷ˨įɠͰǶÚ˔ëǀƧ܄¥ͶơȔĆܪûԒ͌ΎψƐ\\ļƷ́э"],["@@Vʏǿ³ùʐʤ´"],["@@Ű_ðǮǌĬ¾ȲϺƠ^ϻ͹ȕţ̎Ʒʐ˫ޕɌăƣúҟϔէ͇ǀ̕϶bѼƝ˖ÐȦƯʖǠנ;̘ṷ̋ľŋ@̵̝ˉäƓ"],["@@ȏŢƸaĿ"],["@@ƃcÝɚ͒ʦòĻÅȿƛƟ"],["@@սʞùƜƮÂѬ̽Ž"],["@@čiČĨBý"],["@@ËǘŨïÛħ"],["@@CËéĮî¡"],["@@ġŶŐqmŃ"],["@@ūtɬǂʟ"],["@@ōƽ¥óƒȢb"],["@@ǹ^ŦĘȭƢȸȠŨșÝʝ"],["@@ğāʾ͗؃ÇʒǑLțɒŉœň·½άяɷʓ৐࢛ϙƗ˵¼ʅȌ¬ǭȁƅȇг·ҹĊϓկϝƣƮ\\ʃБϧŰ@xśřǻɂƅǯǱ\\̋ƏúǇʧϿਖ਼ӗƗژãėď@ǮěŁŻèͅƩāɔɡ§ʹ˂¿ɇǳœƃá˅äΩșжŅƘٝƳčÚÎòǑĥŇŊˉÙƇ߸ŗŜpӴǏɜѹǢĨƚɁȺŖΠʿ˚yҴȆƢĴķ[ÆɌʬΪɂĘùōĬɿܐمԄǂתɨŎĜʞʒĶϼPÂŏԮǕɾǖԠdˌѺɪͤɮĉĤɖňŌǪRȌ঄ƴžռnӰЊƕ"],["@@ĥĠǀûðǢƐɏĕɍ"],["@@ࢰoלړ¸ˑ஦އږ࠳̪ȭ¹ʜǾ^βӃҘɁ̶ԋբɛqƍϧǗڐʢ˪ȍĪȭ̝ȩoƛĖēÏġʂʻ՞ŷžٿˢɯǍуːȌΨSծ؍Ǜяòǿĉˣ²܅śڷͱɦɣǑωȎ̻éW٧ۚઙެ·Ҭңδ׃ޚ˄ҟज՛ܺѕʴӁౚݧдġЦǗňΟԌѱȮܭ܄̹ԺlʀͶш˃̠Å"]],"encodeOffsets":[[[125900,-11171]],[[124809,-10844]],[[126379,-10549]],[[122893,-9599]],[[128070,-9740]],[[118385,-8980]],[[125929,-8750]],[[122332,-8950]],[[126278,-8555]],[[119441,-8820]],[[127270,-8529]],[[126900,-8471]],[[142229,-8589]],[[120378,-8568]],[[127566,-8336]],[[130892,-8293]],[[125730,-8818]],[[134003,-8518]],[[121081,-8517]],[[118219,-8350]],[[132955,-8145]],[[129845,-7851]],[[130478,-7806]],[[141861,-8472]],[[134478,-8191]],[[135150,-7374]],[[131759,-7355]],[[123674,-7289]],[[116577,-7275]],[[118147,-7138]],[[107779,-6799]],[[137766,-6596]],[[109951,-6151]],[[123421,-6449]],[[115425,-5950]],[[135995,-5991]],[[137981,-5843]],[[136117,-6043]],[[104825,-5610]],[[126594,-5398]],[[124972,-5568]],[[125589,-5395]],[[126137,-4660]],[[136777,-4347]],[[126201,-4211]],[[131649,-3671]],[[131355,-3762]],[[119219,-3547]],[[119095,-3960]],[[129906,-3161]],[[109452,-3077]],[[132869,-2934]],[[102836,-3259]],[[110805,-3069]],[[102609,-2806]],[[102240,-2399]],[[129081,-2510]],[[129049,-1832]],[[133482,-1731]],[[127969,-1746]],[[138726,-1630]],[[111568,-1658]],[[108591,-1709]],[[126564,-1745]],[[131229,-1700]],[[126170,-1199]],[[112344,-1209]],[[138205,-1142]],[[101544,-1820]],[[134146,-1347]],[[138633,-667]],[[130356,-799]],[[133762,-541]],[[124790,-416]],[[144357,-2672]],[[106982,-342]],[[130629,-326]],[[130304,-507]],[[106227,-356]],[[133953,-4]],[[100823,-543]],[[107294,-180]],[[105764,555]],[[105501,765]],[[105907,1074]],[[105643,892]],[[106522,1209]],[[107096,1246]],[[104886,1014]],[[99822,1501]],[[104952,1495]],[[127887,1020]],[[104150,2129]],[[130799,869]],[[99671,2126]],[[131537,2102]],[[98779,2417]],[[111501,2976]],[[108299,2932]],[[108837,3233]],[[120483,3360]],[[128674,3519]],[[120714,4287]],[[110916,3779]],[[120397,4271]],[[129861,4131]],[[98809,5355]]]},"properties":{"name":"Indonesia","childNum":107}},{"geometry":{"type":"Polygon","coordinates":["@@̽ĥ̤ˬZȅ"],"encodeOffsets":[[-4517,55486]]},"properties":{"name":"Isle of Man","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ʬ@ĤмӼþʜόώeլԟ͚ƺ̃͠đUz­ƓíȘșKąuū¾ǅWǫ̑âʀ˵ɶĉɔÛʄǣaīֶɹĦ­rÓ`·O¹yiŕĿœāŭƛíƃ½ůBėoĻÁÏqėĽhƀěĈ³Ā¯¼Mbn|Î_Ө˃ָϿP¢r^¤YÚƨĝƐíƬ`¢[ÛpßҲůä^ÀjĖQǐ»èeɚĪŜÁɂãň¥£\\Aĥ¥юɳĐsɴČâWªš´Ǵ׎ǵ˄ĶɬǏłÂμ\\Ŭ¯Zz´ƀ¦ŜGèĸįƄS¢ƎԬFĀxAÀ`ŒjŶŶèȐï±СbûÊÉ¸SeķákĴǵʒũƺɈÏp¹ĐI¬YƮnư~êþŠͦıҠdŞÞ|@À¹źIƞ¨ĐP°K¨hֶӠ£ʨҎϬɢࢎƈՎ̰Ϧ×٢˿áƷfÙǞɵC¡ůdſƎԙࠩԵšƕRʃċjiF{şɫ˗ʽĩȱŴįǭϯĭƍëŷǿϙ͡ŔȗeŁÜ޳aũ£Ŀˋê̝ùųęǥ¯]±léĂçv·ĵ±[ÐÍ՘w¦đǎy˂ñφǝIÅ\\¥K@óRÿƷǻEȟǕ½įǪÝŋǕӼǄͲ̈́ŨĴ{Ǿǘ®ĴĠɸBĢp\\ŒêĂΙȎ஽Cб¬ɅÖ«XYpȊC͎ŵɬĉåmŃɕɩ̼ÉBfƉ{IɏƑȐůeåW®ďęĥƵƛȷ}̘ɱɢðƁö[É̙į@ëȏ¹ãBùKĕǛ]£M²ÝÈÃâsĦMƊěŦiæ§VM[ĭ[Éō˟ÊĽÀµvȥƢoy˿jǯŊ̛m˳ßBĐʅǋúħƇGΞĉĮµϱśƃŦŕǑÒŜϐɇȢǾɅ˯ͧԻƵȷƭĥɓŐͿȋ͗̽ʳŋ őףȏßHPǈɋŋÉƋɖ֝с֕۩ำ৑č҉йȗЏPʳӡ˧Ř̓ƹȫ՗łԃÉɟǎؑųȔÿěɾȳħ̵֟כƓ֧Ět¦šUܽѯÇׅ̿äț͢şγÜҷƏȓǵŏэҕʱϝȦљӲȓҤçЎŐ͙êEĥ͖ųƐЅੴٹৠӁྰεӤů˔Ō@Ƈƨ¼òŷ¤ǡ̄ԍᓶĆіřƫĔRȂƦkǗîĵϾȐۼãϐǅɈĤČķSоʐӍ­ƂɴƭHƸɌÖՁļåċȥɡƢāǓȩ̿ࠫΕʱDӻ̔ঁ৒æŬȎŵݴʌˀѢųű֥Ʊέöԁ˼ȉ͎̠ɾҕɉÍɔİÒĊnĜbĈAŚCHʨXxh^lNhIzDvìgÈ\\ÌcĬAǬPĬEȸěʘĺǈÌɎÀfQ¯p«ä´O¨Pbææh°C¾B~ÙŢL vwÎÏŔÕƞȍ̢EȲáÁiµGƷtƑǠ¤πǜէɌĈ̶ˠʦżȄĂż¤¨ƆúîCŠïÀƗĚ¹ÜJʼİ̞˞þfî¸ŊƲƚƐ̎¦°ҾɸВݾάƆÞÌ ¼QºÐbêĢîˤ̼@ŊÈļÚO]ýÖbÚºǢY¸ÑǄ`ª|®æÒĔ¸˨ŠÊ`ĬâŜWN"]],"encodeOffsets":[[[77130,33061]]]},"properties":{"name":"India","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@¿µáƠĆŏAƨƁ"],"encodeOffsets":[[74232,-7554]]},"properties":{"name":"Br. Indian Ocean Ter.","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ˋÄɨt¤÷"],["@@Ňŭˎ࡛λӧŜĉӅñÙøࣽΣ̏Â¼űϵŽޣġǲǨԃãѬɰدÙδ˖ЗZÒӮőźɰŦ܊ŎǧÜƹĵحÅϜɆƔɲ̴Ģӫ~ӝƦsŖͬ²Ƴļ˖à@Ø˯_¸Țȣ¼ĒౖaßĘЬʚՋ͘ǪĒɎԜĢRʇ̌ͨ̈Ņɍũ˧˗̡Íƀē̩ƱјɩʂUˊɰ̜̇δ|"]],"encodeOffsets":[[[-10186,55208]],[[-6367,55387]]]},"properties":{"name":"Ireland","childNum":2}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ȝȃեĝϜȂIŖѮHû}"],["@@͂|ొٸ˜ʇǽƃǂȅʝƑԄϋɶrüқǰʑ߬ǋ͸ͣӰɫ࣒ķೠʸŮ@ȣÝʔ±ѴڈńǚʶКɰݠ|ƚƚنƈȱނʵҰdТơźɏԞʑɺ˙ٺLþϝ¹ǵČğTȻhÇE³ƋõiΓŇǝgĥã¿S[uTuvańŽȷYƁǱ@͑ôĻ˾é̙̯bťŒͧĴ̍CçƱCğHǻ|ƑYɎȢ{˄Ăą¸ǳEƅwçȋțȑȡЧщǈǏİķب࠷ܺ́j͵}҇ΐY|Q¼±HwÁRǕQ]±wcƟܙƥÓőĩXʃţ»{ƛuŷamÍ©McT·ĝqʡyë¯MĵÿؽƲŹżġĿߍƜ΅ÃȽŘ௳Ȧʣࡒʵ˞ԡ`ଝՙїǈҕ|࡛ԈǹʘϏǪχʻɤǻԂǏŨRǊǙìsɰЗтćɸѡƛѷʰǒŜȅé͕̻ƃËēȼ˅ưIѲ˥Z@ͰØǚÜǌīŢĻŸáÀƵʰfƏOҩ̰ǉĄálƻNgfYª@¸¼ôJ²ŁƼԻҲŪȴéɊĤŘOȌȲȘĶªü lÎŀşǞÈŲŦæVzė¢͡BΏǌķ˺©VD¹¾ʍ֞|ŰȃŨbɴ͇ǔȨΪŵªuϚȽҔ̮ǖ̆Ȗı֎էՔŇ"]],"encodeOffsets":[[[57537,27568]],[[47222,39811]]]},"properties":{"name":"Iran","childNum":2}},{"geometry":{"type":"Polygon","coordinates":["@@ŗPģêɉũȳԼұłƻI±»ó@·Z©heɞyǊăҪ̯ƎPeƶʯâ¿ļŷĬšÛǋ×Ǚ@ͯ˦YJѱˆƯǺ˕ûqċfǳìƅ{Gy˭ĪзąȯϳŗǅÙóƋƍƧ൝Ŋ߉վ෗ਚഃۀğ˻²˗¢ҷèŮĴĜǥɣ࣊ᇴࢴǪ˸ƆܸµزĶŌ̺êӐјĆhưĸĘĦTĀÌSžӪşƊGƪúĐWgbMųä×Ü¢ǢĲÂE¾Ooʎ֝º½CªUĸ˹ΐǋ͢AĘ¡U{åťÇűŠǝĿkÍûķ©ȯȗȋ"],"encodeOffsets":[[46733,35404]]},"properties":{"name":"Iraq","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@ߔźѫɋΜėŇɋκpåǱʴÈҤĻƿƏǦˍϏʙ˹H÷ɃϝƣݽťڵʻধŷŋƫקŇಙŲʝŌäĲȥÉуǨఽį¥Ɍǈû˔¢،˸ӷÅĒǜ̢Ĥџỵ̈ɚଏ³ǧŀਦƶݬDĦŞؕR֒ɂࢷŐ࠹ơӛİȒø͢ÛȡȨАćˤÐҟČʶɅŘ̀NeƂθǗ̠ƃƤGƊуŤϴ®ՍÔƠúѦBࡨͻʅʅeѼϧ˚̶̌ÅȺѼ׾˧ĚʶҌĞעϙŽΤ͠]̚ƭͮȨ˄Ý̔Ð±ɂʔÜɜQϊʉ"],"encodeOffsets":[[-15916,67818]]},"properties":{"name":"Iceland","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@ǷÿGkcȋÉNå¤açjģlŅ×¯Ĺ¯ƫȹWqsPclMÐlØkÜÉɅŭÏȟӐĤБྣÍßփบĒìDÐƨƎ£¸NPǎʮβীʬOϨ̚ЛŁǷ"],"encodeOffsets":[[36647,33521]]},"properties":{"name":"Israel","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@Бٗǒ͹Ƶ̯ԣĢ̝ʲȥEء͆ѽĬǣȎʤ̨ƚů͜Ƃкǯ૤Ơ͊ŚàÏ"],["@@µċ¯ŮĦ¡"],["@@ãĪľç"],["@@Ƣ͏ƋșåएыÔ÷ɳˉȗɼňՒŝàẦɿ̠ɘ̈Áր΂͚ȫbœ"],["@@rŧʍzɜĮ"],["@@ǰFgeqË½¥M}PmlYúLÂǁƘģģýûƸΟ@޵˙¡ǡʢɯɳȷŮлঘշϒݍѾϏՆʻ࠸ɛ͑ႮܛьηįͱɟĬǳ˾Ͽ¼ǹƌɃͿׯӾ̯º͍ҵǕÑ͡Чϻ˩EßĄȜӢˌŪ\\ƴѣࢤدȾEȘƳǘΧÍĶŊΓêʻΤթPϩȎ࠱یзĒvŪϫʹǋ˝࠘டЌٕҝљğŨͮˋuΙǔþа̛ȬόƠÊŢ̗̠ȘƺkÖoǖ֒ĔöȬŴîÈÀÔôǗ̢ƙP_£ÃƨƕÐ`ư̾ĎɶĂ]zBĐƧ¨SºAŰ¨Ćl°KĒĽS¢Teŀy¶xMnÂv®¦^¸LȠÏi¸W´PÎ ŊvSĎAĠOÐ«ÎeĊKÈPj¾ÐÞĢǂNĂ^ȼWͦĆxOJ[p¥Īċ¾Ù¸µú}ׄċƶ]ŜEǮgµ[×ƍćlė"]],"encodeOffsets":[[[15951,39138]],[[8683,40006]],[[8485,42025]],[[9864,41864]],[[10645,43887]],[[13744,47322]]]},"properties":{"name":"Italy","childNum":6}},{"geometry":{"type":"Polygon","coordinates":["@@ފʱŞȧ˃¡˟ĞùĿƧíƑƁĲͿɵʞʛĪļƈ˾ØԦÃ"],"encodeOffsets":[[-79115,18901]]},"properties":{"name":"Jamaica","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@ǻ¯`øǜ"],"encodeOffsets":[[-2067,50413]]},"properties":{"name":"Jersey","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@ůįYӱŭɛÇԱŽɹÍɍÃ࡬࠯į³ǋāgcacãƗ¿őka׵Ň©ĥƑīƟǋŝǵŻऱŸnǞВྤĸْiƂdȌHlǸĀӬ̇ϒÏ࿦ࢲɤࣉǦěŭĳ"],"encodeOffsets":[[40085,32896]]},"properties":{"name":"Jordan","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ǩúĆƐ«ç"],["@@Ɓƙ©öǾŶQÑ"],["@@ƥS^ŜƈŇ"],["@@͡ǭÁʑų·ɊԌǄuƂǸHȕ"],["@@ĉNPƜúƩ"],["@@ïåǝĺѴɤȥʷ"],["@@ƩDµļĠÞŪÛiŁ"],["@@õ Ǆ͔čγ"],["@@ŝȂ®ãý"],["@@Ĺ\\¤ɺƄhǛįġ"],["@@ȔōǛwƮ"],["@@áĤĦƲÔŝėŷ"],["@@ķǘƺßř"],["@@ķQĲàğ"],["@@Ѯ@ƅˡ̠g£ŗżȡ˻Ε˕ࢻɡxƱ΋ȭø׈ķXĉŽ͓͙Ĩ­İŮxŴőƘN˒ϐѼţ¦Ʀʹ̄ģÓ¤ɇŶD\\ŵʉÎʉǙ¸żŭˮˀǫΉ͔|ĜȠkeĬҐǔĴǚȚĀȦ³Ȇɥ"],["@@Ʈȣ¶Ô"],["@@ÿlzƨļµƉ"],["@@ɼ{ĐͿ̣ǳǏ̡ȉǜ˛À̅ŵʷѣơ÷ƋTÊŠǷ\\çќ͉÷Ԣ̌ʖ;ɐƋ͜ìlȐ̄ĖΆû"],["@@č»ÍĄƜ"],["@@ĝïƁü˲ɀÑɋ"],["@@ıbƸˈÅ˩"],["@@ōqĪö Úŗ"],["@@ĳPrȾɈȦ¥ȫĸSȗȳ"],["@@Ȑsֳ̰γưܥÛˋ̇̕á̟̝ÅūǃࢅΫݑʦыΓǷđˡѕɡc͎ɪʐəÔƹȽtɓǛŲȋiƍρȽȁ̘Đ¦ƹČѽѹթÌЙçǴǆʽ¾ûďtʬý^ɫ̹̈ȳuġѱū΍ԫȃÙȱÈ˙Ϣwʀˆˢ֧Ůб§ɹƿۛȋС±˿àƕУͽǶۓÛ{ʊĬŌ̌j೎ईࢶGৌȬŸȅ̔mȈĔǊƊẘՊՀŪՠЖŞΡͻİɽȖëɤǖڲɢӆՂДɊςڂɚزÏȘɅÆɨ˸ó˴̜ȼÂ̜ʄOĸ̫ǀĢƶ·ł˖ύÇDĪŔɄʖŏ"],["@@¥ĴńÝŻ"],["@@ų¤ĀĐŅ"],["@@Á¿ǒĐŏ"],["@@ݠǁռͲȕ֑ǚ̡аìމ͕ߝĳґϳǉΗ୕ӠύΝǣɵǴȓHŻɟۘЧŵÉ˷àɱʟʥí÷ŤĦ˰ɭ̬rʮӔ̼̊ظĻȤČ͘ৠƫ֢ìȶɪĞߒ۵߆ҭ"]],"encodeOffsets":[[[126862,24863]],[[127277,25105]],[[128455,25337]],[[131337,27293]],[[132095,28387]],[[132560,28886]],[[133758,30990]],[[134103,31127]],[[133511,33202]],[[133205,33004]],[[131754,33571]],[[132175,33629]],[[132600,34021]],[[132911,34559]],[[134323,34410]],[[135441,34760]],[[132383,34943]],[[137583,35079]],[[137577,35312]],[[138172,35112]],[[132491,35179]],[[136572,37073]],[[141665,38730]],[[144619,42366]],[[142829,43091]],[[144687,46203]],[[144459,46421]],[[147277,45176]]]},"properties":{"name":"Japan","childNum":28}},{"geometry":{"type":"Polygon","coordinates":["@@ķȤøÍŖøĤ£ǲ»ǢŸłEŶRÚ^́ǋɉŋĵ½"],"encodeOffsets":[[78898,35953]]},"properties":{"name":"Siachen Glacier","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ǁèĪŨÑŏŪÿ"],["@@ѝɑѩڥťȉЁŌ։ƟǅחȝĳŢճCࠣǺ׷೹ʠƝ÷ʣɋĀˑ·Ʌǂ൏˽ƿĻԢƙŃԯβ޻ͩıŰƛ˯śNқśĨűĢγ½vȍƴĀbᅛǔ˹řUӝTûR§GěđՓØ۳˰˕ĝˣŉęʥyğÒƹåVă¢Ϋ´ďÞǭɣâџÄȷ¯û`Ɠ£̧ͥ®ûທەӍңWɇɿȳźÆȂƩƘਏcǯۆЩVúࠢʟħʳζӹͼϳƍਡÞ৻ŗࠛݸᏟঊᓡһBᶡҗsӣ֚؝̶ࣃƿҿ̯ĒࠖוĠʁʐˏ`LʶЃװнŐáŖƂƌ॒ý҅͠΀ξຈuΩȮ̤ԴÅ֐ӱŔ́Ƌ߭ʀકѝҏÛīƳׅɦbŶͶdڕ޸ֻĄǃÁȑаͻưͤˢ̂ȕǼòˤҒ֊ɶjՊѩΤÐőֺזȪƦȚؼǦҠτ̶çƲƟ݀Ƞ̐ȑ٤@ڪχΒщòЎ࢚έڲξѨβƧͮǞѨwѲ̳ӎ×ÐŅʐöȸɦ̮ƝѤbϔƒƔ϶ࣹ˜ιʂ߸ͺȃ˶ɰʠ࢈ΡȈϯº~ƄʲÞұĊɄͮזÓᒂπՄ͜ʄᖮ̔¨Ƹ٪Ȕ৤ȏҲŤϖץý͗ފMƴĶɖ͑ƨǊԜƩМîͿ˭ĎɓϞƠвōČƆ٦ȺǼǐ୴̒ƵʟȝNÂŇୀهᅂᐳπƞrȦʌĢͤđ±ȇˈWàǻࡸWɤƒԄĐӴȁ͆҉֖ƫȲϏߖĭдɸ£ǵגБ"]],"encodeOffsets":[[[51389,45932]],[[89419,50264]]]},"properties":{"name":"Kazakhstan","childNum":2}},{"geometry":{"type":"Polygon","coordinates":["@@Κi²ƿk§mŽNţɒ˷ݒ³ȾƁڒяŢćŜ}Ȑ[ƀIݘťƎĸŐƪªĆޢΠɌǹǚħŶDĠdɌ`ŬAɩ͙ɭ͛̓͗^ᶻɪ̋ƬȕĂĻVĹɟȿ̓áPȷɇɧΟůęҿɉ˛̵ݵɥǷഩ৐Ϟḭ႘নǾδɀɚÒƦˈǞƬЌBŤYǚå̘nÀϩָǎO°ǌGŁďäj´­hſ˦ǜǘǠǜɔɊȨȠǦǞƸƲ¶Ņ"],"encodeOffsets":[[36174,5494]]},"properties":{"name":"Kenya","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@·ƭé¬¿¨ɛ×Ūȓ²\\Ú[zl֝ǫʡǊৡ¿«ʪúz¨¸kÈTIÒE~мƴՐȓ͢ɾ˂͜œςʌɐWZŴǞ©ѺʬЉʛƤÍGåsuXKþa°£\\ÁmÿnãPiĮÅĒ^³tôwO}Hŕ»¹AáWǥ_Yʁ|ȿÔŃƨҫƞ֨ϬƦ ǒîHpgjçā³ec­ų̈ͦƔ¤ü_ȸ°Ͷ½ĪEɤãǬĒÝά³Ą¡æUÑƺzĠĚʦˤŊ˖Ğ۴˯Ք×ĜĒ¨HüQӞSŘV˼ᅜǓÜġþ»Ôµ¼ÁвÏŲġŜħŉWaeUʳcÍg×ą࡯˵Ǘččãa÷ȇǥƕsšJăřԁ á_ëīƏ¡icãíćŅë¹Ý»â͗ŅǍFĹˎDɽŭŉuęGśÃÅ|ċΙǇƙƁcÿ«Ã¯ďy£iyH£ěOy¥ěuë[ƉXéc§}ďŁYϿr","@@ćXÍ`]@LgÐÓ¬Gø|","@@nƀů³ÎeFÁìăAaå¬SüC¨R"],"encodeOffsets":[[74384,40332],[72361,40812],[72916,40850]]},"properties":{"name":"Kyrgyzstan","childNum":3}},{"geometry":{"type":"Polygon","coordinates":["@@ұȲʃđĮƴȆǃɎƭȍǛOwҠƩ˂]ÉǓ͐f˲ɋȴƍݞǮt̨ҪʒǐೢĄʜǛ¬®²\\TFwl¹·ĖùƴǪÏüĝÖ«ƌE¶ņyàûŖsä|\\Ƃ`t`ôSòWü¼°Â|{uÈÔ««iĀ^rC°ÂS BƸƨôPĖŢƿӟɰׁŉ΁֓ħĕƱљ˙ʉ}]ɗͣÒŽĻJ˹ʰɍFȱʻÎćł̋ùęɧÈBǭǑƕΣĻ"],"encodeOffsets":[[106933,10662]]},"properties":{"name":"Cambodia","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ƔĭͷƌŤ¯ÒĊÝâĶ³iĥ"],["@@ÄŃőÞÎ¦"]],"encodeOffsets":[[[-161118,1901]],[[-163163,4018]]]},"properties":{"name":"Kiribati","childNum":2}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ƉöƠŰӂ°yƣҝı"],["@@ģÖȆĎġƣ"],["@@ƑŘÒ ĀƷ"],["@@UćƳvŞȞË"],["@@ÿ½ƁňƮŤÔǭ"],["@@IōýfWǘĠï"],["@@࢚ೃ]ࡍƚCŹҫ˽̿ӅkÉȗ΁Ŷ˓ÉBȝʽĐÚǓŻƋŵêÖǬ̋ˇţÖȇǕɣ̠ɖrÌńơAŇˤ˔ΖĿÜɞȌǳʜ«ѸʳCiä˜Ȣʢ·òĽĘĪȗɊĥӴϦфߖr˰ˆ"]],"encodeOffsets":[[[129359,34021]],[[129264,35196]],[[129201,35565]],[[131140,35642]],[[131831,35634]],[[129558,38643]],[[131456,39551]]]},"properties":{"name":"Korea","childNum":7}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@āYĉǀĔưƎǑÕƃ"],["@@٫SȧϨݵĚƌƎÚôŘǆȰϴиĆĠiȎÿvŭŚəƣȹȉˢ͢ڿ"]],"encodeOffsets":[[[49434,30336]],[[49606,29228]]]},"properties":{"name":"Kuwait","childNum":2}},{"geometry":{"type":"Polygon","coordinates":["@@ϤЋâȓÞDxödRŒŁĉΑÁȄʻĚ©Ɇ»ƠéϺȾĀżýȠƣ§û­sÑrcĖUĺJ`ÁlgŲEñAÃħĩYÉ·çŽŕOʉØǓMEe{çXå¿ă¯sI੊ջAµĵxù̀Ɂ¼ÝÎÿĒȷˆ˓ɚǕÄÕþšÜ£pÝP±ZƛĢƃêªfUıϸʹGû}ƟéoĪƷ¸·Ȏƙ¶å¯gÅÑ½ÙÝrÃjU­Jĥ­§ďOóƷƧAT­ÁqDÿ]j¬Ó¬Çv{|ɵđós_Ɓ_{[tãþŕxßµŅƋFÕ¬ûĞǩÐƳĕú¸mºCx¼lĊ¤ä°®¸l¼tɆpöYĠð@ňŀȚpĈDÒeȉŊNƄÙ¦Ƴ²ďÊȓʲËƴMȤ`Ǌ´ȊÛƊʽȦėİŃƢŭȒĉĒ§oęlƍÆšķJß]EauÁg§µëğȳé÷­JygK`eĀÿhę¾ǡƂNÇñóÓ³fóŧĻËQQšÿȷȁ¿eǑĔȢҺMŦñĶEȪ҈õ̎pƆQ¾חJīǠĺΠǝǪċßÿsXPºN²°ǼÆŠÜÆèzƂB¶IhËJw^ÐƔɒ͆Ǽ¸Č¶kĩÅQÃZ­pu[ƌ ĊFƌëpN²¢bPÝĒrʔGƠM¤ǱΪnªªªŔrRlIæđƸ¨Èu"],"encodeOffsets":[[104579,22917]]},"properties":{"name":"Lao PDR","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@ϧ̙ʫPҐ঒ˠ͌΄z¯ƃƦÉäǯʳʝòï͇ñ¤ƕƓŹ"],"encodeOffsets":[[36730,34235]]},"properties":{"name":"Lebanon","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@àūìЭɛίʨƟʲĀ§òǑÒĳĀWƴÇ°ʓƻՑV˿|ȅ׵Ȫܻ϶१ࡰ࡝ԢǹȀȪ˜͞ʲȘȄÞʾ®°ƂÀÜʠ¨Hðê÷ʂĜɜǇŰգÿʍ̄ǗȒȘΨƢŉ"],"encodeOffsets":[[-8690,7740]]},"properties":{"name":"Liberia","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@ʡˏĞտɏБɶࡥ@᳿@᳽Aေ࠽M@нủཤ↏Ⴒࡉϕٛ̓؟Ҽౙ˴ϯڞۑȦʑÅşĘɉخҫؠɢ͢ʠŕךƘϤġୢе߀ǬĪ͂ƄņżƀǠďֲƈȆǜ¨ŐǌǾŶƮüƎìǄöŮÌ~ºCªãİ@ŞLĬPÌǼŸÁƀsϼȟЊµѾĨཤѯѼ߷ᅀн۶їϢűӸȈͰЌƻܖǐʹо̦ࡠ̚Ҩeݺʥ`ʋƲı۾ǡؾsɮˡ"],"encodeOffsets":[[25755,32415]]},"properties":{"name":"Libya","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@³ĕķŰƒȒZɫ"],"encodeOffsets":[[-62356,14154]]},"properties":{"name":"Saint Lucia","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@|ÙſŊń¯"],["@@ɪaϬίѠܣŪ×¦ʃϞܟY͙ȋЍލϑϧ~ƟŦȣ֪ũ଒ĦɵŪ݌ƜʾY̰˼ą͏ƚÁƎ"]],"encodeOffsets":[[[81792,9268]],[[81902,10049]]]},"properties":{"name":"Sri Lanka","childNum":2}},{"geometry":{"type":"Polygon","coordinates":["@@ɉkɛ̓Ñęʭ͝˒ƛ˴ŗˆȦĚψӢȊÈŀĀŰàȪðĤlZ֨ёîȕĹ±Ŀŵőǿϛǧ"],"encodeOffsets":[[29335,-30849]]},"properties":{"name":"Lesotho","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@µPǸˤƁ˳"],["@@Ù~m¦xʁßÉ¹ÅkƩXuĵkoϙçåx~Ս×jǠë¶ñĥédJwfépÏLµïŖHÐŠʸɏǀгJ۱ǚDǆƧ̞OǮO ɦĲ˶ńβüڔUþ_Ú³ÜNÜަđڂŒƖǓԀăްοCqÛ¡ĉ¥ÛW{qƼWâY`iƥţʹR]ÙķçŇ_ėwŗǛTŅMuyÃÙ«éiTiŪeÀĥǉ"]],"encodeOffsets":[[[21461,56606]],[[26187,55440]]]},"properties":{"name":"Lithuania","childNum":2}},{"geometry":{"type":"Polygon","coordinates":["@@ţ̃ƍ½[}¿Q©R}nyZÝ`YĐɼ|¨¦ÐĠîĨYjNEMïz»È·ÊyǸõ"],"encodeOffsets":[[6643,50994]]},"properties":{"name":"Luxembourg","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@ӑ́ѱϫƍޯπӿĄƕǔځőʵÆԯÛÝMƕÔړV̓åÝU˵Ńɥırوքغۚź؞Փ̢ıؠɼ¹ԶIĄոžƌ­͘õ՜ˍȠÏθàȄÏŞYŤCÎÿɐħoTO«Ǉ̋Ɩl[ĥĬşĄ¯P¸ĻnÑT±­į"],"encodeOffsets":[[28824,57491]]},"properties":{"name":"Latvia","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@řŰƒ࢖Ɨ˲ϩ̦Տۄ̥ĘlǨþ®_˞@˒Ěڔ˫êFמƵTЋːǓĮ˟ŮåƄ¯ÆĕOċUïǨī±ǓcS͇ĔŹZå÷gFGł]fFƿËF¿\\ÕÀĥ£qÝĉÑġSí½·ėµÏÏNŭǁg]«"],"encodeOffsets":[[28890,46542]]},"properties":{"name":"Moldova","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ĥƷǘ͠ñǧ"],["@@ŉkkŀĲÀƓ"],["@@Ͱ՝ʢඇȼչɱәǁƄŃ̂ȓÃƦ߷Ó˟ˉΧzփīο஭₵́గ͋ލϗ̥Ӎãճ˩ʫOছӚ̫אª٪ϧ६tϜǰӜʤƪԖ੎дǭ̔KˬǋϞ»ݬϊׄ°Тζ˄ȊĴïɌĐƮϪÜȰƽËǰŦǚϼʚƂǷMʆɔ͒Ħ¬\\ʝʞ̺ǆǇʬȈΆĥź¶ŨĊÈǺĻҔϞļάŏ̲Ūɲ̠˨̑"]],"encodeOffsets":[[[51135,-17308]],[[49503,-13684]],[[50728,-12730]]]},"properties":{"name":"Madagascar","childNum":3}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ōAɼìŭé"],["@@ƾǸöƩɳ"],["@@ĕ¥ŘŘñ"],["@@zĥƉǆŐß"],["@@ȩȴŘиύŚʝ"],["@@ĿƻǤêg"],["@@ƵĴɞÂʣ"],["@@Ûȣȡàú̚Ŝèȡ"],["@@˹Ɋùɠ͖ɩȿ"],["@@ѫ೑ƥཉâͻϖҕÿʓtʚ̵Ϯӊહؘ܇Ɣѵјҍţ^ʚŇÿÞҎÉ͒ƽ˶̇ނɜ֘όǨϼ¼ƿ͊ÓɒƘËɶÿUזϲ͆ǜǼŜࣀ҄ɠଔʜϬَƁÀČƱxˬfǰƝªɩŻ̩еұi̳ȷȋéȄ|ȟșFÿż|ɕۻĵƟƣɂr˄ɛ̯ȑuʃЗ˝LyſໝBGӁǟDǵBθ͙ɠƅ¨ÝtÇ@¯¸Ėy®£@÷SĉZ÷ÙCƳ@ɵ@ϡ@ʫAȃ̭ĿǩĕƥĦȹqs]ÛΗඳ౐ϩȚ©·ΦǧևƶØĪǟyËŎƋšȆË˭࣡Пԁ̓ƸӍ¶ٟ˴ȯȖޭƺঁКْ࣏ٿĔدɚЃцࡵИҗ֖ƿΆΆǌÇƦƹ²ɲʼz̸ȇņǽ̴H˶ūʨ॥ੈۣӺȨĥ^ňρłˣϼȖ]թˈùżȭ³ŤɆʻšǁŔʲȎɮê«ƅʀǧƪɫOǃ̈́ηźǷʾÀˎӻĔ࢑ࢪݫൎXѺӝƖŕǲǁÒǛĹڅͦŦɇýЫ˨॓ڜջɌχʚŅĦʉȈóŘԉͼʩϼ܃ƎćăʤȎƟɺޗԖޟxѵʖȭÂȪʀƑ̆љɌƳÙˏѕɟ̙װ૏ߜĳ[ɩʢF؜ʵբե̴œ̦ĭŽ˛ÍȱȶԓɄāǴϡʰĚϼ·ɨöCąȜčõȴě^Ǿаݛިإͼ͙ࢢȟƔȜˡ̦¹ƜĐĮȑŬɽҊ፺ƴġǧẘষᛠJFϚ๠WʢˉচگЌ߿ͦʓߊ͙ɶǾɸҊ̄ĴۢāּԁϪܕڔٻ¤Э̊ԡผәȖø"]],"encodeOffsets":[[[-93884,19126]],[[-89026,20791]],[[-109058,22130]],[[-113220,25604]],[[-114746,25135]],[[-113766,26646]],[[-117934,28744]],[[-114895,29702]],[[-115871,29750]],[[-99477,26585]]]},"properties":{"name":"Mexico","childNum":10}},{"geometry":{"type":"Polygon","coordinates":["@@аˏƖɇó͹ōI·ŹķġkʹǅwǵƱŷ¯©wԣeɉàȃ̂ÞԎƆ@¬ǔʂĎȐÑĒĶƠxB@Ɇ²ѼV"],"encodeOffsets":[[22881,43330]]},"properties":{"name":"Macedonia","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@Nᅫħ՛ɹտ˕ǿ_ŃύÒ·ïෛÍ̑ʑؗÏϟŮѱWʏȑޣ̧ŝʍЉºʋɷÝʫʡV­ͅȍćΛȀǟăƫƿĐ˙ɇķª˭ɩɗӵȻϝȫ˩ԭ\\řMų©W¡ŃĥéNķºEd¤úD~¹ˌțƁçtõÐmoS±KÅnĵoɏMÇǗ̱ǔɵòˡɝaƆɓƐÏъˡfɤ̜ΡʴHȾȍЖ̏Àǫͫȍѩƶιˁȉʴ̹ǅǓƸĨǦAҌȵάǑ­ȇˊĀȰ·˦ǫɄ«̎ƨÁpjºɘԶìòƤŀ WюЋɄɬي£жîAĮl¢Ú°`ŵoẀKŸڊűƆĳńyȬyȾyȾyȼíظǙฮ̵᨜íظ³к{ȾuȒʴ@ˠ@ˌBی@ᝀ༷᣾ၭjʅϐəØƩௐѹŀσŕʵȤĭܺƀ"],"encodeOffsets":[[4330,19603]]},"properties":{"name":"Mali","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@ŉíƀǸĿ"],"encodeOffsets":[[14916,36714]]},"properties":{"name":"Malta","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@Ã±ƤƤğı"],["@@¾ǳǅŬňÈ"],["@@sǥÿz·ȌƬ"],["@@¥ưŦŅÿ©"],["@@ÁģÁÊń"],["@@ĽũřC˼Đã"],["@@KƏɤ¦ē"],["@@ÑHĀĢmĩ"],["@@ÅćoŪǴǲĽɓ"],["@@ā¬¾ǘɃ"],["@@ǟƸɎC­Ƴ"],["@@ȚŉąũKĥƚÐÂ"],["@@lřčǒâ·"],["@@ZăĝɐĄƋ"],["@@·ċͅǻõǉi}]ÏxÌIJgµƁAçyÛÅÅş¯ǻM±ĳÀ£ÃRƛĉɗxǳΉ§ƃ˛қë̙¼ǕʏÑܹ̹ÏɒȑØб؜ۑȆթƦò¬ÅÏƝȍŋֵ̭ƏVȑ̲ԭӊե[ֻʀЛǮ٣Υ֧εύ¯ЃŝŽćբǮʼÈԖŔňȫf¼ФąjàĒđۂ̿ی§˧ÆUϖǅЮĠ@ǧǦďࡢŁŨŢҤ̍ƧӔ̉ʲ¶̵ťˏ˫ǣȯɚŖ˕ҽɳ̽ϑó˺ƛɫȟO¥̬șɹԜɗΗǽß̬ಬΙ঎ÚRƧŸqͨĭ͙ȉńǳ˾˨ŋƤǞ͗ˮĄŘϩƂĵǱŅǼÆʬȧǙÀƎŽȈYɣͱиŧфŶŬʨőƊUЈOĪźB¦wǌďĚǦúŴé̞ŀˌŪ¤b ޴łÛȘf͢œȀϚìŸĮƎǮϰųİĪȲ˘ʾpŤàØ°E|kjČQʄŢƖࠪԶԚƀƍŰcD¢ǝɶeÚâƸò¾ʚȆĖjŎÚČĚƀ¾Nϴ˥ȤՕżĚǖëôڋ­ԅķĭüǫ˓ɷǅ_ŏɹǅç͍Żā©ʃƪəťˋծǚԼVƃǻžɽηҰĹöğ˃۫،ćpɧǨɷϨQЌʄºƿ"]],"encodeOffsets":[[[100539,10172]],[[100567,11216]],[[100920,12027]],[[100881,12191]],[[100420,12688]],[[100776,12901]],[[100675,13414]],[[97081,16199]],[[96745,16329]],[[99917,16644]],[[95940,19133]],[[95964,20028]],[[95736,20370]],[[95243,20403]],[[103567,22086]]]},"properties":{"name":"Myanmar","childNum":15}},{"geometry":{"type":"Polygon","coordinates":["@@Ϡʉ֢˯ʑÉTǳɳÑőŸ̽ϥ¾ʭݿ׈ĠæáŖmǈb|z^Ŕ`CĆ¬ĚÚ¬ĐÀìM­¢NTÄąŞV~p`®Sª]TªP|Crë"],"encodeOffsets":[[19680,44493]]},"properties":{"name":"Montenegro","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@ܷ˵ԛӉߧˋēcշkџ୵ƅݷʛҭȇįå×Å¡ËiНÂ@Ȁǋʵ£ʉ՛ɊлðΗÆƷ¸ˁʆåpé^͹ZӕÆε®đNџğ቙ȆۏÍѳҲ˫֬ǙðrȨՙÖ৑ԀؓúǫqŽCɁvţϏP҅Šơkǹˢ˸іIÎ£ÔĀØВÙŔÒÏ~ëfǿʀÑȂßþa¶clĹtóÙŎÍR¡ái­FǻƬçrγuÛ¢­áZſÌę¦QĈ¥ÈēhēÌˣêäŸȓìõøŊĴ³äRUĸD¬nòÞǐφǖâXroUĐzÐ~nÂŸ_â¤ĖØdSƼΆǆԐňº ɘĠǲÈVNØC®̜ƠʦCôRƎƎÒ»ǚī°I¶xÌÞVîWƌƣ॰¡Ĕ˫ɔǕ¼H`ǲ@ƴĝΊoҘòȾć֞eȈŵŶVȖƜΐĒȺʎ|ȚœÇ£ǡ˸ɸоҌˊɆˈࡺ̥ĨeǐMȪGÊY܂ɕǄ^ɀʔËH«cTĝµjȃĐģyŏࡠ͓ˢ¿ø`ânɶmરːۮƛŴ\\ǦVŘ_ȖĭČġĒ­øĪEʆGʈ¤ʙψɏĬ¯üeԼXǼÚu֞ċȔ¦ʀÝĲp̈́ŚŎªþ[մèΒĞǠsĞbĞŠ¢ÜÔĖżÞńÊņÆ؊ɢϐɖăɬǃøÄ_ƆSňKàtŌǪºèCŔǚµŎąܥ਷{ĹJğUćū¿ĩ±üɋʀƿĄræàƀÌðD¾_ŔIŜtĞJŠa¨aĲ}ǺŝÆ]Ζ˰ؘGÎļķÎÃĶŘÄՊчƾʕƻĳkïCūnţYƛrǡĆħt­[É¯×TËSƧXă\\ŷïWˣůǍÐĝC_[čBo{Ћvʝĉ̋ͷ`X¥³ѡɿѥËȱWƱƭ{şƿɃŷñáǟÙȋŏÅcҍÄ˽ÖğćĀó ʣJãfœcзבѾҫ­œ"],"encodeOffsets":[[114564,44729]]},"properties":{"name":"Mongolia","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@èĞ¦Ïō"],"encodeOffsets":[[149250,15497]]},"properties":{"name":"N. Mariana Is.","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@MˎÃȨfɜĥ¼఺ƽӌɇΈɏࣤŢĒࡂࡄÛƊŜАр֦ǷԄÔʈųƎʘӦĭఐÒƞࡏʄס;ڡz̒Əո᠂ࠒκүبŤŌơחʛҁʂЩ;χƮśƯº£ÞGÒZdlSʐ§ƚ©ÆU\\ÚƺĮǸȬĠ| ČĐࣈuzgǊƁǦǛɞħŢŅƌȝɈͷǤÇȆEĜƹߎȼЌ®ĐƖZŐEȈHȤQðkºFÊhżŊłCƞéþÑ`½Ī֤ŞͦťƚƸĂǐʊӒĳиȸۢɊƠüЌ̚ŰǓĿēĢÅǍуň֟ЧĀţť̇ŎCvख़ĶûÍ˗ňİÎʟȏͭũ֕ߵǋÍ®šٓҋ߃ɧݳйʱɇ҇ٵş¨wţ͍ōݿۍʣĤĚĵ§յɸ˩ʊ܁¢طÊµÖ˶Öŏ׷ǣۭƞu©ȭлӭၽ۷̷ћɒɛĘƺkƙӟEe҃YŹzñK"],"encodeOffsets":[[32884,-27483]]},"properties":{"name":"Mozambique","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ÃćĐŒƀ}Ƈ"],["@@ŷډṿL³ƦSk£Bĭгíً¤ɃɫэЌʯǙËƱȽűǅ¹oiʭŦ˛Ȱ̵φʯЦ̅ĨĥqγζׅĆٷǉЃ ǝ˝ěmƯá@ϖÔ˄ζઌƵबʧ̞ǬŴŝȠيȁΦǉeɇϞĹ¥ȝ_þČβพD६BϪBȖ@Ȗ@Ŷ@QŐOĘQŢSŢՠ·˚ĸưŖöƀĔĴà¾hƾx̌ŲNц@΄@Ҝ@Ŗ@ɮ@΄@Ɍ൞@޴@̼@ƾ@Ǡ@@Ī@Ǝ@ȌAȎ@ǖ@̪ἢኑۋ@ˋA˟@ʳ@ǔก̶ᨛǚอîطzȻzȽ´ЩĴŃŲƅ"]],"encodeOffsets":[[[-16766,20180]],[[-5488,16674]]]},"properties":{"name":"Mauritania","childNum":2}},{"geometry":{"type":"Polygon","coordinates":["@@Ó~ĖÁ"],"encodeOffsets":[[-63640,17143]]},"properties":{"name":"Montserrat","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@˩¶ĈȲȮǎŔȇşɭ"],"encodeOffsets":[[59035,-20976]]},"properties":{"name":"Mauritius","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@ŏFƕYYÍȻЋƺߍFěÈȅ͸ǣȞɇņƋĨšǜɝƂǥȃďࣇċğ{ȫĭǷÙƹV[ªÅ¨ƙTʏckÑYÝH¹¤ ưƭŜͽψʁЪʜ҂טŋƢاţЧՄƕËƷːŃĊʠ̬PѰҌ̔ƟóǐªΠÑɰ\\Ĥȸñʲ̴ʤĝƌ_QĮǻǔtì\\üďĒďžŷeÙǸࠀəĚƦ˚ȣȾёĈҩ¹ʍƔɕĮ« Ínī"],"encodeOffsets":[[35799,-11856]]},"properties":{"name":"Malawi","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ßnnˤ²̑"],["@@û²Ėŝ"],["@@ȡa¸þƪÛ"],["@@ćW°ƺþµ¥ī"],["@@ÐęƥăćžǞ "],["@@ȪÏǎʹӀϡˌҷpྕ̼̑΀ܛ˭ŕrŏǰTƙʟIǷĵĻǼՉʺளࡼJ̲ӭ״ä´Ʊ̎ɫഢȋϪŤȮìŏΆ÷ĴǷưEÑЅŎőϊɪĺŇǐ ȒΖ"],["@@ЉƖӯջmƳŽȋঃǩQŇŋɕĊģͣɭɩˋѹԟcɽǕԭǖÁŐϻOʑĵěʝɧōשԃǁ܏نīʀúŎȢɛРܾ˕ǍƌȪӨ¹ȪȢDÌ̤ఎΚ޾ࣨŞ̊Ӹүƾ̘ÿ˚ɾƴäѓƸųҠȠưƈœːƺǄŘÏŘæܺନĎų¿ȃ˺̨Đ{ÄəˆƑìƋ¥̉ţįфƚĦǅƱÙå͊ČȰǵՆƯĕůʳŗշÙƇ̈́˛[ĻґŇʽþĹơ"],["@@½«aŨǚüFŋĿ«"]],"encodeOffsets":[[[114063,2474]],[[106723,2798]],[[120714,4287]],[[102696,5422]],[[102245,6621]],[[104552,6393]],[[120397,4271]],[[119954,7341]]]},"properties":{"name":"Malaysia","childNum":8}},{"geometry":{"type":"Polygon","coordinates":["@@ݨƈϪɦ§Ɏȋ̋yͅșʋ|եν͋ϰስˍJᵥࡃFᙵ@ᲃ@šυĻȏƃÉƙë܍¬ܧǂċǒVȜƝŘōĐĻµ·ùmŋµŇŝíؓնͅӤǝ݆Ɵǆŉઆ˯ۨๆࡗಂʁؚ׭੐ֵިɀRфƈnĤ¸ǘĘEPǄMՌȬʠËҔ̱ᅌfሼSҺ͹ਨ»Ҫſೠƶࢴǒ"],"encodeOffsets":[[23942,-18064]]},"properties":{"name":"Namibia","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@č_@ĈĎç"],["@@ņ]eƝŹsŷȼƤ²ný"],["@@˟ƎÚƂĴőêȰYĒΫ"],["@@ȞٌХЄл੾ەxțǑ­ʱĶಓ߼ُںğ˶Ťĕ"]],"encodeOffsets":[[[171566,-23166]],[[172044,-21944]],[[171419,-21668]],[[168144,-20731]]]},"properties":{"name":"New Caledonia","childNum":4}},{"geometry":{"type":"Polygon","coordinates":["@@ǠఉΦӋgȅ͂͗ȓωɕᠵङঃޛଵ¶ȷĎίƭEēϝȅǻĹǇȝ౉ɺЛȷ¥UӍϫΏVϛðʛŸ̡Țŝƕ^ؙ˱ɅĜщҨīJ̃Đ̅ĜÛ[­mĵáӝqѡɳǙֵʧȵ©۫ڑׂσƋp˙ˇΌźƂķȢґÇӟ΀D˖ǲMө̄ǁɦɰǿɄ®ΐؘÐ̒ʒොÎ¸ðώÑ`ń˖ȀɺրĨ՜Mᅬഞ˰൪୨␤ᕨౚ˳ؠһٜ̈́"],"encodeOffsets":[[15339,23549]]},"properties":{"name":"Niger","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ƇoòŎÖĝ"],["@@Ɇěؚ˲Ɩ]Şվ͑ϜïΐUӎϬVȸ¦МొɹǈȞٮ̀ƮF˜ϑŎƯĄϹtƓô÷ȸ·Ďŭ¹փ̛ȋ¹gïGÙ÷ÑǉɡƏɥķηŧœÑЇɧř­ĻÍΕqËŉųȻÓýƧßŗǻ׋ǧȳÕĂŽǡƑÝ¿{ǉͭɑ¡hÕȬßẩʊǉƻĵAó^ilc oėÙƏı­go¯ġÅıƛħЯсʁܷǙˑɿƶº̋ձħŊȷěǩƚàǝșĻŽˬþ̏ȑ­ŖsœʓPļćŻǟ¼͏ʸŝʤÃƸþręCŤƴŘƍÓƽŎɎàŇĄǷÇɃ͔ί˔ࠗâɢƞʋÕāŅՇw|ȎdÈl¦k¼kæUńôK˞`xdlBçݨÔ֪ɨɾאɸȎͼŀRÀǄßƌʼk±´ŵǾFĖÂŐÄÎÆc٤ʨȶǚֶѢɴӞrĶâ®nÜ\\ڶǵъҧ"]],"encodeOffsets":[[[7476,4525]],[[6968,13423]]]},"properties":{"name":"Nigeria","childNum":2}},{"geometry":{"type":"Polygon","coordinates":["@@Ñōǥ§׷ʼɳč֥ȴƇŃྦྷེîàƊĹò¶ɺZ¦`úǺæ\\ÂJfz«˔OÂX¢tbľXȜg^ĀŊòĂ¾¤Z´íǔķPhNLdE²¤¨ЊˢŲɪǖȒǸűĞwݸȲĊĎĸɐ_wqÁĳÃöÍÛĪÝĤ΃ȋ˿Ż֩²ރĭ¢ɈƉǱØБñ½Ǖľ¬Ƶǹ˻Ȏ͏"],"encodeOffsets":[[-85649,11180]]},"properties":{"name":"Nicaragua","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@ũVĪĚį"],"encodeOffsets":[[-173878,-19541]]},"properties":{"name":"Niue","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@£ħīɪƐƁ"],["@@˗ƭЁçŎ݂R"],["@@łù́¼ɀ~"],["@@ƩƪŮ@ĥ"],["@@ٷżʜ²ѾĿɧŀƮ\\ȣǚҊоʊՎʖhЄʸѾŚو͎ƃƑՍ˓ŹʌëʽʥÜħ܏Ƒɀ̑ËʫɭįǆĿ¯ŻʥrĒ͆ӧłƷǆگé"],["@@ŕLͤɍ©"]],"encodeOffsets":[[[-69842,12437]],[[4328,52620]],[[4044,52982]],[[5004,54345]],[[4328,52620]],[[5454,54668]]]},"properties":{"name":"Netherlands","childNum":6,"cp":[5.0752777,52.358465]}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@Hıōɒņş"],["@@ƅCÀĤĆğ"],["@@ʣÜɨ¶|ő"],["@@ƷhВİlč˅"],["@@ЭJʀĞǮħ"],["@@ů­ȴɂăǓ"],["@@ȌܱȇÒƒӔĖ"],["@@^˛ۉ Ր˲Ɯµ"],["@@ӌˆǆȱҙȗ෽ǱॾΦÐάӆɒʍح"],["@@тù­ʵϳAͽƷɭ¶ǈʔ̠hŤǸ"],["@@ǅ{ÎŜĸğ"],["@@˃Yzńʘ`Mŉ"],["@@̒¡ɯǗҥȭջiȀǮ˚\\Ϫϴļƫ"],["@@Ȫŝ΁ǄƘ¥"],["@@ϥɡǿƌԘƠÎÉ"],["@@ʩIÀƼȪƱ"],["@@ԥɱЁu̩Ţ௒Ɔ"],["@@QȵֱĈýǟחœΟɵĵžЦʞǇǖ୧ͦԵƃԏRϟȉ˗םڳͫࣳǴѽƁލÒۉҮѷ¹@ơϋIы_ȚƟͅбඕǘ͗ӷӻŜғǫտϿɶ̷ࠗӅ¼Ʒ߅ƩÁۣ۟؁άįÂʓƱƅٍ¼ΉÙԯ͹ƧʯʮࡋŭӃ،̳ƣʕ΋µʚҩđ˿گѝİʫŽ̧ƟaéńҭŮȩӤΓى˱§ʇżČšఫۛঋķŃńʋ­ÎƔߟɖŵϜҚĕɞŊɝ£ǭŬÀǜ϶ȼਯ͓ÌͶȘŲʦ«ώƂУԴЬȌPǩЀ̠۽ƓࡍקºϘО͹èůȬіȰͻıŅÈě͸๨ĔɖǃDŜӄĴɑøĆŔɧɧӕľǻŽ޳́ðĕȶ˰¬·ȌDƘࣰù׸è௵ÈĽȊ٠ɎƦÉк¸Ħ¼ҳ»ǼǠદĩĞюĊ઻¥ǦǬՌƚѼOѮȵϹˆВƲɱƀǺĒҺOhőҔƒ˘ȝتÐƎժƲǏĀʂĖųͣŅjŵ݉ɏЧǎ࠾ײߪ˜ĠÎ˱¹ǌȆ֜Ǡ̊ã΂ȬлŽʃĂҰԼ̀°ȽŦੂǔޓÇþΈؼźʵĦϨǴ઴êࠉÌѪʮՎȏĈƢϋæ²Ǝϗď±ŪˮƐЦqʱĶ׊ƈʮ̇ĝμଌĠ࢏ĚೲԾƄ˂րœʧžӪɎѲåָ̛ѪɩԥζǮɆkįͤҔqɨľҖƭԳͰߺ̨Ģ֚͟sȊࣼдƁŀͨƦԜƃцT؋سലۦÛѽͼĐǎȜ̦¼ʽȚ̌Ũڜĭгʟ̾SáΛ՜Ԭހǻ˞²݈έֱű௫ڠīǈȽʞUŲŨǆŝψ¾"],["@@ҢūҩɣÖɬĞ"],["@@ߞ˴Aŕߛǝ"],["@@̝ļ˔HŃ"],["@@μeɌ̉ॸß˭ƃຈȡጇЩŌʊํÿ،϶஥Ұ୊İ"],["@@ܮͅ࠵ȘӁτ׊ʕ"],["@@ղMີ«ԺĬҊ±"],["@@ࢢęƓΏؔȀʀ˽৺ǥ׼¨ٺ̭෱ŋ᠏ၽይվ̵ɰטbȎƐᄨĞĳĎᛗŃ·ɎՄmఐ˒ਂүŒˬƈଫǅʢ̱ş˝ú§ʳѷť߯຅״ߪȩ̒щb̷ŻЃ͠Èɺ̄åҬĔӮěOĒஎîũਇũٺJגȗӂϢเۓѷצΰ̰Ҕţ"],["@@࢙UᅮĠओĉ"],["@@ୢǙƊ̒ҶÔĚʋশƤᓂǛ˴Ʌವч෋ǫ࢙Ʋ၉[؛üϡĐठƂႷXЁƚԎĜ߃Ěபṳ͞঒ȓ"]],"encodeOffsets":[[[5208,61755]],[[5078,62551]],[[8298,64858]],[[8675,65196]],[[11501,66423]],[[13284,69504]],[[14206,69904]],[[15573,70598]],[[16139,70207]],[[17924,71267]],[[30676,71472]],[[21278,71772]],[[19718,71749]],[[20242,71902]],[[24183,72243]],[[24595,72261]],[[24004,72516]],[[31611,71459]],[[26201,72850]],[[-9168,72540]],[[19681,76177]],[[22127,80482]],[[11521,80498]],[[29745,80806]],[[17190,81825]],[[33307,82042]],[[21400,82176]]]},"properties":{"name":"Norway","childNum":27}},{"geometry":{"type":"Polygon","coordinates":["@@R¡ĲƃķJç¥ś³ſyYקmɫǐ˃ĵ׍ǶǳµõǂáVɵĉčtэɲ¨BĦ[¤Ň¦ɁäśÂəĩçfǏ¼ĕR¿i]ãҳŰmÞÞ¡\\ƫ_ƏîƧĞÙ£Z]¡qOֹЀӥ˄Í`m{a»Nÿ°ĉ´ŽĜgľĘrÂÐpļCĘÀŰîƄŮƜɪȂzjº¸Pǰşà®ƌȴȌ¦Ǵqǂƪə޼ϛάί̒ĆðuâvÁ¢ĵ®Ŀӊ̏¸MǶ´¶MUBióģ³øoǖaɤDæµæíæũ½cÎr^Ű¸æýĢ¿äĞĺ¼dÜOzq£Ƹ­ðĸ÷Ųa˨MȨÒȾ_Łأ"],"encodeOffsets":[[90096,27786]]},"properties":{"name":"Nepal","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@§ÓėØƀC"],["@@lë̓ǲɨƔiǝ"],["@@ďÑǔŗ׿ǁɼ̐cǨƠJǄƕ"],["@@ĳŉ¸ð¼"],["@@ǡŁuĘĝoƶȫƳûĩäØƺʗzʂĚ͜o"],["@@őµƸǪ¥ų"],["@@ۦ̖ů̇ǜɺʬsɛȇ˨Śʧ˙\\ȭǘÏࢽਡԇʝĤéǩğƖjĨƩɢ±OǕӁmĮƷşɕƔŨǍ߃̱āկūBĪŅ΍էÚƽ̙Ï؛ԡ́Í߭`ǋȲ̅GʫǼƝý՗ƺȬɡëÌŸȍÙIŐѠĴǥÈǌĪɍWRÚƪ¼¥ĆʊċØĶǡƖȞÃĴǺŖ²ŋfǀʪƨĺÍ©ƐԎЂ؆İSŘ֐Ȭ΀̼ƀeýØʸ`óêŢ¢ÎËȢʾƕ`ǄʈȢƢ֔ϲɌǈӺтΰʮAǯqyĽʆƱńα"],["@@Ǔ¾ŞżǛ"],["@@ƮútđՌǑƁƬ®ɔҏƹW΀їãҁӞŷðǳƂJç؎ʾɑ˰އঔ̉آςѸŗɥܟʕƝÉѣ͕Ɣϑůř˩ƞıɣПܟࢱ֡ͭŧǌʅM_ǜȗÇѸ֘ü˨ĉƦūƎࣙϊƝǆæưҮƪǘƬǢݜńü˽צŰįǐŌŭŠʱDʋϘǴğQ̦ĿÜ]ïǻńĹǶFğȾʹĥաۊǶȊȿƳ¿¶ǓȦØǄСҼ˴vãęʲͱ"]],"encodeOffsets":[[[173239,-53757]],[[170211,-51979]],[[172181,-47986]],[[170987,-46264]],[[-180405,-44790]],[[178089,-41844]],[[177271,-42269]],[[179757,-37149]],[[177428,-35773]]]},"properties":{"name":"New Zealand","childNum":9}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@åO@ńȲ˾ÈƏȓʡ"],["@@ɄёОЩ൲ϵրܙͦŵmʏέ؅Ѝ͕ΟץɝdƦÙSʵ́ŭӇĈ։ࠣɛωٟݻŧȉɏhɝǯʁ࠷ϩȵѱĥछጊᠾ࠾֐၌ϣמ\\˦˨چÁĺѢĺ³ŸƕxՀǤðÂǷ˘Ȭ"],["@@Źn̚ñ`˨ʎĩי"]],"encodeOffsets":[[[60132,20704]],[[57742,25579]],[[57650,26267]]]},"properties":{"name":"Oman","childNum":3}},{"geometry":{"type":"Polygon","coordinates":["@@MΥ٫֕ۖߕÄ޽ںՓe˂ȕDƳ¦Ȟ_d[iűĩĒǖJ̆ƷáśīÉ_˧şĕ·ãÑ{­_©ÒǃZ·¹ǡaÙüÕ`NĻÙŇÇ@ˣ̻ġíaéÏR¹»ÝËΫƅГݽһɷ§¯ƍ̍Ʊƙ·ŉeí˟ý̛ʻįÛIęº¿Ƙȍôƅù§¥āŻŹȅ˟ʣć̵ըɋǛ£οƐǟ¼iľI´HÄjàHȱȎ̡ÖƟÐővÍuKÚšA}½¯Dgååa§O³Pão¬R°eɏ¿ǅËʗĹõNç¬Ù¢īDǫMīBËdÇ[ëhuC¡¦kMg_WuGʧȡFěaĉmıÑ£ēɋŲǇų̟̂řӨϿĖG̬̿͂ǉĝ˰£ළřıĻԋȀх¥ÃţྥqjbzêrʤĜSºNdÎªbnvŸ|Ɯ¼ʄŤĪWÔŒܚƦdƠ²xR^QǖÂ Gx²»{RΏZ~҈iͶܹ̂ا࠸ıĸǅǐ͌ğٶǿЎĹࣼöҀėʜŸל^ॖʴĮ߀МϮԼyʈĠƅƌӨʠξ@ɬƙΈʬиɔӲٔɦ̹؀ٞ¯ͮĆːŉƊվո£̨͗и¢âࡢԨ۸Ŋ"],"encodeOffsets":[[74844,37750]]},"properties":{"name":"Pakistan","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ȽĸŜê|łʏ"],["@@¿ƌÞr]ƽ"],["@@ēƇʂѵ˽ͿȉŐśЁѩۨŒ~Ūʜ͎Ʌ˵̨ĽċƧĞUųĕɰӓ̴ΝwƯĳŇǱÆ׫͍Bŗϔӛսɱȟ¨ƍԼǡɱȣĄȉΔγ¶ÛĮϋVƽÙ[ƧůɢƔĮbUŪ½ĘÄżĤ¯ØĉÆ·H@͜^jX|~`ºlƂïäÐÂ­ŊāǿŶc³ƇƖąʢnĩƶҐ̟ѢĚװ˔Ҧ͜శ̥תӝ"]],"encodeOffsets":[[[-83561,7509]],[[-80791,8473]],[[-79231,8867]]]},"properties":{"name":"Panama","childNum":3}},{"geometry":{"type":"Polygon","coordinates":["@@ԷσȕŗȳÏLŃo{iij{sªÖë°] {WýƶЃŶħgRI«°ñĊÅŐƏĆġHŃĵũĳũۤĉǖбۼ^ȊŘØͼ˺|^Èty©m­MÝଢ଼À~êß¸¦OªFÐbżÄĘ¬ĸEƒkĄFݦಧʙͅāګǠУЩՁ]ƗǞɯȽͻǢя̾ǃǃřXƓԓҷk¡ľşVÑ@@AÙÉĉÛƕ­¦ʋĽǹЯśޙ֎Ǐ͚޹Ӱ੕юୁڒίњͳɠω٪İºδԝ࡮ˏʐÉͬ΍͂đϜϙغӛఈԫڀԯࣈ७ײmƴȞ¤Ǧϡߺ¬̆پۜƮĞæǁ¦ˡeq³őv«i¹b¥JĎEùŃ·T°Cø ĄĘ¸^ÚĎ¿Ĳ±¢}ðRÖZ¼¾^ŋă²×ÎóÀ{æAĢl¾j²^Môf´¤ƨĔ˂ʬܐĖÙ͊Ь঴ΚӸΰѦԐŴӎZƄwS̤ƑƴÇƈÏ¤ÑLʊgǞü˰űlt­ÂÚ¾»¤¡¢¿êɩ¨ÝǊ·ÊÍºwÄÑǖ©Ƙύʦřϓƒ½¤¯؈MȠØªÄ¼Ä¾¨\\ǌÑƌġǚlêÊòh¸hɚŕ¶ÁÔYʀő¦¯äÌe՛ऻǤíEÄhJ}¶ÖƗŴģ¨×KÅu£KÙ ġ]ß°u¬½ŁcǅRß]ƥƩܷŻΉȳąĭīśĝùµŏw±yýљƵʡăuÍĊ̱"],"encodeOffsets":[[-74892,-6621]]},"properties":{"name":"Peru","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@έƽ̀ɚîÛ"],["@@ɂŕĭÏΗêʄļ"],["@@őYŃȼȎÞɠśȗƣ"],["@@ōýbĦĬg"],["@@©Û·ôržêfFƻ"],["@@ËoKľØč"],["@@šUWĀźé"],["@@éœķöǢ"],["@@ƾĤ˙Ƒ̙Ȑģþɿo̵ŔŽXίͧ͝Eҗ̩ࡰŕÏʷӕǪȋâУˑ͉ĩЄʯǍ׷̜œƠýӶȆ͸ғΠơSďͳʛʘɅƝ©ǊƧЉہƫfȚˎض֘ǲŜɸ͒ɨΒɩ¥͑ͰƲǨ̶˜ŚΆɮēÌƄʐWwخ҈λ"],["@@ÉÔèȄĊɑħ"],["@@Ѕǁƽrĳǌ̚ˌƌrȮŏdȩ"],["@@ǏǢƠ˒pѳ"],["@@Ļ}¸ƺƀÏûë"],["@@ģ¡AƶƐŐ©ɣ"],["@@ŗIŅɖʯƎƋ˸ÒŜ͔ŀΖƔ͐ɰĜɈÏ¶ţͿޓWɑƞ˩ǃǫ"],["@@`Ѥո઴ܽʯɫˣ֭"],["@@¸ŏáBjŎ"],["@@ýÓŎ̄༾๜èиȺ˺u̡ƈϟˉơƫɡ·ŧ̅ևऽۇ"],["@@űč´Ŏþ"],["@@̘|ĆƉ[ΧɊБŁıŻƬ˭ȷŖLԨĽƲǻīԦȴǉ"],["@@ĿMĻưŴlĈǍ"],["@@˼g´ŻɜĂȽˇʟƓۇ͇Ų৖ƿǰĨϾɽ"],["@@ÿYëɚƦFǵ"],["@@ņ¬òǩʯGĿʖǸŏ"],["@@șŪƲ¨Ƶ"],["@@äǥǺŉԇè̙ƌǁхĄǕǒįǼÞǨң͚ŵΖߐ"],["@@˘ˑJǵӑ϶ˇʓâքЖʝ"],["@@ãȽùȎàɢŦ°§ʡ"],["@@HñƁʸźȅ"],["@@ðŷ·΀˘ɇ"],["@@мːʧdбū˳Ɓå˃ʖɫքʿȲÀĖʮ"],["@@ǚÝġɑƥľîǲ"],["@@ƭčŗŎƾΐǊǧÁȧ"],["@@č̋ÿ͐ǎ"],["@@شˏǌĞƆĲyħڙ̰ґ͑߃ґɃXɛǷ́ʮӳËŽŜ΍ϐȧÜÈóŶάƜʴŉƘοǐðnȎиȋSīɍÛɨ̓ƃ̜ÙéϿƵļÒȐՙņŭͮӋϺŃkǘЭÉǛȉ˞ԉΖɵăƁǋȽ~ɉǤǟãi͒ʲʰfŸ̛ȤJͯű{ǁ˸Ɨ¶ʹ୼¶ÒˢɻǬÀU౺ȰܶтĜ"],["@@ă«ĲƘmī"],["@@TùſîŬL"]],"encodeOffsets":[[[123137,5383]],[[124068,6222]],[[125024,6583]],[[125888,7587]],[[119890,8073]],[[120172,8412]],[[127803,9363]],[[126667,9460]],[[129031,9545]],[[129085,10001]],[[127585,10023]],[[128707,10153]],[[122795,10738]],[[125594,10725]],[[126087,9282]],[[126332,9677]],[[126728,11555]],[[120127,8643]],[[122739,11802]],[[127565,11616]],[[127600,11769]],[[125437,11894]],[[122920,11985]],[[122983,12460]],[[125599,12605]],[[128246,12829]],[[126686,12583]],[[125025,12652]],[[126746,12753]],[[126241,13162]],[[123602,13803]],[[124841,13866]],[[127339,13960]],[[124963,15366]],[[124008,19063]],[[124848,19349]],[[124438,19827]]]},"properties":{"name":"Philippines","childNum":37}},{"geometry":{"type":"Polygon","coordinates":["@@áŦŔƾ±ˣ"],"encodeOffsets":[[137826,7560]]},"properties":{"name":"Palau","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ȌġǧɡĨéǌ˨ŷ"],["@@ƅÁćÞɎ["],["@@]ßĉĨ"],["@@ǺÂÅȧɫĒƷ͐ʰʻ"],["@@ɔÑĘȿϩÔöÑƨü\\"],["@@ȡŎĊžĤµtȕ"],["@@˒ġ^ŝϝǘĮ¨"],["@@dÑĳʀĦĘU˅"],["@@ɝĪɔoJù"],["@@ȵǘ\\Ŕɖĳ{Ƿ"],["@@ȧƧ̿ŢšΎϋ̈́}Ф̬Ƨ̼ѱ̘ȡŔʳ"],["@@ƝŀłĮȭ"],["@@ę˔üĆĂǡãǷ"],["@@ŕ¸Ħņpƽ"],["@@ǜìʐŅ¯Й̯ɅƆʁÇďǯę̍Zʉً̯˿ۏwɩǲʃٗϔĂǔުĥЈ¼ŌÜņμä·ćȧĢǷ׼ÞΪФ̂Þßֶ˔ă"],["@@qóđłĄ¬ù"],["@@AᇴħƔĨͦCᲘ@ǌྴׯԖġҶ̽ΊiԠѯȺζοkӣ໒ջȢɏ̇؏·ƑħȤҙ޾؍ĶϙǢşŐ̙ԬhÄϹؼǅȿıĨǧࠌȇ͹Ñǜǯ˝ŗʫÞəȌအʄكִ­ˢ˩Ğԇܾࢷɲġƞ̓Ä̯ǅЧʠʌНĝí˅zĄǙࣷķƩżşyɦƣ˖r˂ŇɠʍUǗ؁̭ΕƄअ©ƁĒ"],["@@ÕÛŷǒÑш͛Ҧ৳وˑøäƔ২֭ࠠܕĬȷƕͻ"],["@@ɫBǡƾɠøǞïPǇ"],["@@̶ĉșĻևuĠȠΌ\\"],["@@ȁèĲƺǙ"]],"encodeOffsets":[[[157221,-11751]],[[157984,-11634]],[[154521,-10818]],[[154707,-10260]],[[154142,-9570]],[[153954,-9721]],[[156295,-9174]],[[154734,-8943]],[[147033,-8685]],[[151579,-5966]],[[159701,-6847]],[[150709,-5562]],[[158359,-5563]],[[149524,-4839]],[[155562,-4399]],[[156335,-3208]],[[144360,-9337]],[[156637,-4870]],[[154048,-2725]],[[150598,-2007]],[[153360,-1590]]]},"properties":{"name":"Papua New Guinea","childNum":21}},{"geometry":{"type":"Polygon","coordinates":["@@βছثНВȷ¡ѭºǩδοċÁĀɹıķɕ֥ГˣʗµÉX¯ºŉX·uĽŢ÷ʷQÅRϟņ͋ǞҹNǯēыÐʱǡʧpdƘ˧ȎʗǕˇĤɩ̲҅ƀŹÁȹŨÚǆʋ«҇ƢĠǅ̍œέ˒ǬŴřèɱ࡛͸UŅñqñEJZĔŘòy˸{zƱ¼]jzêŗǤŴȾÓƘÃzg`Ò¦ĈėİɕŘĿĄV°żĀÚâþDdDÊŽϒ˘ùsƸ̅ņဌ˸̾ɜ๜ʆμŻ˕¼ʹ̿٤øԖ࿮»ӂSN¶ÐKêoexIêcĦòìµǟ"],"encodeOffsets":[[24049,55235]]},"properties":{"name":"Poland","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ūɸGŋw"],["@@тÁPś̍ɥ਍jÉ̶ĘŔࡼ¯"]],"encodeOffsets":[[[-66995,18541]],[[-67716,18888]]]},"properties":{"name":"Puerto Rico","childNum":2}},{"geometry":{"type":"Polygon","coordinates":["@@ƈȝϛĳЕЃ¡ܗ̯ŋۣԃߋ̉ơӗ͢ĻӴϯ˯˅ߕqϥуɝĆɅŕ̇ȲΉɕĲ˓ôǾŴџ¢̈́άФň͕ĴȮО£ȨӯȚŗťɵ͞ѸϤऌϐوېǲȎȥࠀťŠȂȳ݆͊῭͒͠ǚҨ˾ęLŭɾǫ"],"encodeOffsets":[[133660,43557]]},"properties":{"name":"Dem. Rep. Korea","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@мġ˙ăǩĖĐ"],["@@ϼGQÕҙAģŌǔ«"],["@@è͝GÿĢͶÇ"],["@@ǛÖŨ´ħ"],["@@ʻļɒpªū"],["@@ũYlĒľ÷"],["@@έƣٙĮͳõƸͰÉࡠǴmŋĀΏÏǬȖøȖ˨́˯ȃĻĎĘҒҌٸŸ՘ÏߒōÆĢƪіǒĨ˙ؾlɎŮѺÜɧˤş׹ѭĲ֓ȇƧŘũáȉţĭΗiҎԗ̃ԉȦɵŰNУϭң"]],"encodeOffsets":[[[-17603,33658]],[[-26264,38750]],[[-28822,39376]],[[-29328,39450]],[[-27725,39571]],[[-31884,40353]],[[-7583,38072]]]},"properties":{"name":"Portugal","childNum":7,"cp":[-8.7440694,39.9251454]}},{"geometry":{"type":"Polygon","coordinates":["@@lÕ\\±«{p¥pǉÂîʵĥڧYʻলŷ˞ƺ¬_ïĚñ²_ǖ@ŤwĄđNđňƃâޅjº¹`ė@Õxï¤ä[üYĨhĊp̜ƊƼõÎÍňÛɑŽۅĉřǳIţaίʭɋͥǝȓʑßq¹ËµFåËåµÙCƋúǳHɭțนɾνĲpÂͬМ˾ȨɈ͊Ӣɑ˴ଫӘףЀߕȢȳǜ׵ЌΓӎƃƤƙöíºB|sx̾ঘBцѦޘเ̈ޖVވѿj̅"],"encodeOffsets":[[-59555,-20648]]},"properties":{"name":"Paraguay","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@đëì]~ɼɘ D_ƫȝ"],["@@ÐȠɆŮÛÊ×lÏkkNOdÔȺ°Ƭ°ĺņØĤkêi`æ£ÊKjƃķّӏģ"]],"encodeOffsets":[[[35173,32044]],[[35718,32121]]]},"properties":{"name":"Palestine","childNum":2}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ŨIĿƅŊȥE³ƞɌ¶Ɨ"],["@@°£Ə¢ĠB"],["@@ǒʳóĢŔ"],["@@ËÈÖIć"],["@@ŧemŎƴG]ğ"]],"encodeOffsets":[[[-152905,-18114]],[[-146883,-17018]],[[-142360,-9927]],[[-143437,-9652]],[[-143434,-9124]]]},"properties":{"name":"Fr. Polynesia","childNum":5}},{"geometry":{"type":"Polygon","coordinates":["@@÷iíkÏ@³RlÓì©Ģ¼XýіȾӨɒƠʀȿ·ͅľЅƱ͓Ƈ½"],"encodeOffsets":[[52499,25198]]},"properties":{"name":"Qatar","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@ĘēŒ¯˲©MÎĦiˢĨƲbƐ£ŚġůΕёï Ǿǡ³ʱধ̧~˳Ǹϫ˵ŮܷƇ׿˱ᓝƐ§ĨƈŊ˗ƈaVǋƾºĘÆlæM^Yáŧ²ū}ųīěgƷŀɯħäƍCvP¶HàNZjƧøĞƲl՗ɴgʀ]R[SeUeE³ÑøȇĘįŒrȒĘŬĨ̤݆ਈ݄ψƮºìæÂEǤāȄLߚÃʹǧТƾȌlȂɈtÖÒ¤zúŘvƪ~̦ėՐۃϪ̥Ƙ˱Ƒ࢕Śů"],"encodeOffsets":[[28890,46542]]},"properties":{"name":"Romania","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ýÎɚbƛï"],["@@̢µջ˯˝ɑ}ƱāǦظՄ"],["@@ֻ˝ɋ[ٓӕ̊Ϟ՜̶ƂŎķɤjШȒtŭȧ"],["@@ȭ£ѠήӺƾܫӇ"],["@@ɻËӆ˂ʉȵ"],["@@ǗcǶ̒]˭"],["@@ѱɕŻ¬qǢϲĒΖ̸ƧЁ"],["@@ȥØǬŊĤeéƻ"],["@@ӂறƙ݉ŦϛѢଋݮൣ֑ԠˇÀՃħѿृa˫ֈߋɦĂ͡ů˧űѾףŢсڃȷ҄Ǫׄ×ϔǾΨ˅ئɴیÛࢾŠӼПќ½ҌǐѪҞˌƀ͒¡ưϖ̷͔ΪĨ"],["@@Ļƣ˹~϶Ŧ"],["@@ɠ·С̵ɑǮȕé̂΀̨ĕ"],["@@۲ǙдIɐƿşʷGÏðŕԇFࡹಉÊ̶ϲҞÖ͊ʖ¶O̥ʟʹáǌ ˢ"],["@@IũݥԎѲdyū͸˛"],["@@ĵǒĚÛš"],["@@ƏÿʒьھǦ˯ࠗʁ"],["@@ǣȿƤʌ"],["@@̳ÀűƾӘ±±ǋ"],["@@ަƻƲǩԄĹƅɔÖӝȒ܆ɫ¿Ïሴҝ{ˑ̺äŸóÙ؍ΠïĠǋʺňhȱʔǤϩǔŪʼϵŮܶ°ѮÛƓĩÄĩÈź֢ХĦ৲ŏઊӵāāʬ¶ĿϺu­ŧ׫ŏŁȳىǂˆʣڧ_݃ƼҨȍÛƯ̑w͂ÔƁܹșʆuċƋоǷ˱AƏĨµǕ˅©ɀͷƥ۹̒ۗîϫǬȩϮ߳ŤεġޣOѵҌǊeȂ͚˹Ƿɳǂƹǳ̗lęʃπ͑կϙ@ȶ@ȶ@ȴ@ȶ@ȶ@ȴ@ȶ@ȴ@ȶ@ȶ@ȴ@ȶ@ȶ@ȴ@ȶBȶǜ"],["@@՛˛׹įЅÀĳ϶ѴȎ͊b࢒˙"],["@@͓ъ΢Ȟا"],["@@ܑìНȤвĬࡢőƣʩ"],["@@UǫګIœŪҥvεΦѺŤ୸ѳ"],["@@ʐűƛȹٯˈռä"],["@@ƫɼ૆δ@қख़Ɠ"],["@@ȻëխĚن¸Ťå"],["@@ذģԢʣཡȹӫÖ@Ҝ़"],["@@ٗt݆˄Ԯů؛Ǉ"],["@@ܱňҀȲ˲̹"],["@@ЕĐdŒٚʧǁ"],["@@ϿŷâԸ"],["@@ठěʵͧ׿ɍėԩং۟ঞͅЕŝٕŨĪƕڿŀ࣯W৽ǌʌǶТطǸDǄఉ¯ΩƢÒ΢֜¸ΆǨɊ˨˱ļڒŀıɢಬǢѠá"],["@@Ցµ˒ߎž־˷ߝá"],["@@঄˝Ğ˳༕ä޹Š݃ąતіऎ~"],["@@ؙh˨¼Ͳã"],["@@ԍßǹˆ܂Lyȱ"],["@@ԕʱ੻ɐ͂VǤƼબů"],["@@ͲDĿŷ֙Àͨô"],["@@ǍƟ˕¶ƎϠÂNă"],["@@൘ƄƩሜãƩǱࢣƟધxᅭͰ͐͜ɐǯ"],["@@йiȺϦЂȩȁǑ"],["@@٤Ǖ֜юসɯࡢ@ඤ˩૷У܋ÔχȄǴ͂ՑkğʣޮΡջűСƞกŧӱİӧʵࡍŠ१Ѻ˼niͶπTūǒծƆײèै̱"],["@@țſࠩþÎØॸU"],["@@®ƝҿƪђK"],["@@فWࡐŒɍĹ"],["@@㎅ޡ࣡ʵȌđ۝ʛιêǗǧыZêɫ܍ǳUȵϋÎȖƯӟʕ҅Í஡ƀקđҋΒ݊ǌזϮڞÞүŌޔˤׁƖȎÎ҈ăɰɆٚSϜ˒ۖǊ୮Ȯ֎qɔǮ๨రŢᧂՐ۬ÝϦȏ঩Ё"],["@@࡟O੒ƾȱƭ"],["@@̹ЊìďĹ"],["@@ƇȞđǈըƦǂАC׌ɯٮϸŞ;˾࣬ʟƬĎÔ̪ɀĢΨդʲࢰӮΎƽʶČȘ̹Ķۙɵ˗RνƃșțౝKȓʺÒ˔ɃɖBɒ̛דкη]̣ȎסϗȞ΂ɅŠ}̜͏ͤө੘׉ֲӗƊəÍৡΌᔹɗݡйuŻӴÇĀЃֵеݹ৏ƶŃïǱڷȇӟ˥৅ʼōĆǙ¶őéDǩ·ŋÝsŉLƅTÃ`÷ɫǄɕĄϏ؉ɣŅÅŃÉŻÝÓĕÛş¡ĝĝaǟtΑĝճçý\\ō©̓řıoɿÞęGĹ֝ČÙvǻԻWùfĭ°χɐ£ʚʇʅHĩF÷đ®ċĢȕĮŗ`ǥUų[ۭƜયˏϏ_ŗvÙnį\\࡟͔zŐďĤiȄ¶SĞdG¬ŏ|ƃȿǃ]܁ɖÉZȩHǏNħfǝĮۛȸɅˇҋˉɷнńŉÞǭ¤ÈÚWºo{șȷʍէɭŵUȇŸ֝dȽĈҗñΉpƳĞĳJýI_»G¥ÄǭŒēˬ९¢ƋƤëXÝUËµw¯J¡ŷàÑ¼¹[ēű͙M̛Ɵ­×DMÇUǱƷûßc¹ԍŇ·ǅTƻ×cĕá£ŷ`mÁÏ}ďyVqpWáǕυµÏgĿñ«mķC©FȷċÿRבВ¤ǶгɷߕĮȱϐ֕ƬͅҊӳȂԃďɣƑࡷXßǼˇX²ȈͣĒʋġqȥοƝᅁᐴିوÁňȞMƶʠ୳̑ǻǏ٥ȹċƅбŎϝƟčɔ΀ˮЛíԛƪƧǉɕ͒ƳĵމNþ͘ϕצұţৣȐ٩ȓ§Ʒᖭ̓͛ʃՃᒁοוÔɃͭҲĉʱÝ}ƃϰ¹΢ȇࢇɯʟȄ˵߷͹κʁࣺ˛ƓϵϓƑѣa̭ƞȷɥʏõÏņӍØѱ̴ѧxͭǝαƨѧڱν࢙ήñЍΑъکψ٣@̏ȒܿȟƱƠ̵èҟσػǥƥșוȩŒֹΣÏՉѪɵiґ։ñˣȖǻˡ́ͣͼƯȒЯǄÂּăږ޷͵caŵ׆ɥҡɁǃȱܓ̍ƛĄÈųƭʿե׵ԌͳžҡƺˢƷ܋ठ৅ځէЇêӋѾˍàǽĊӗǺG|þȬkpţȁKƵŪõfŇÓviSáŉáþ¡tř\\ƩȏñĉkyōJÅnknVYƅ¾ÅϿŨõ¼Qșàয¦ĵ´đ¶ɱšş¾Ź®Ӂh»ËųWॿۆ҉Ōˣɼ̗GʓɸӝƮ˂ŜȃèŪ²̈ŽθĘǀːξæÆĥĔƈʺIӡˈŭȖؠx½ŲܖʘćƎƟFăą́sǮĤҡƛżѦҀȶ݈Ʈ͎Ĺɰǅ¤ŮǼǰ\\ˉƾ΢Ȳy˚ʥIԗʆɍևȒǱōԽЮګǝхǘѳɻ֦Ȼƈ܋ªýΤɶĐ֍ӄથʿɍ̟¨IrÉĂ¡nqxPȄiȐ}ûÂ÷Òµ´xĚĺp|Vζ÷ʖ]˞ƮĞÈb¼Pɇˈȿņǩ`ƟIÇfÒŀXÆW~tƍ°ȋîĕĄĩƊġäĴE^}př ƻôOn\\°t®ŰŖ@g¦åðÁ²GzVpäÈLƢɿĆ͡ƮǇQėR±YµãsħeůÉÍ¹ºlżࣷİŗò®İS²mÒ·ļOǯǐUê|\\ƕkǈ̌P¬SpɏĨwà`ıH¢ŜŤŐȤÒɵ̎Ëӈ׼ӲśĠʢɶõƸƀΰiǘƞࠎŁ¦ìϛȦշEΧƸŉʪ׹şཤࡔ఺޺̆ͼ̑˖ৃѶњʊTƮЇȲ˔ϿǠȈƪǿĔĞ͂͘Đࢋঔޠٶեͦօņǵʤʪ̝̐ÂҪĲΠɶטŔþǠֲćRȶ֬ñϚĨYŦ࡮șýĵإÔǚǏԺQiđЎ¼ˁ̟ҘȮᆤı༼пט˯ड़ʅɊ[]þӒˍӰıͤчƛ͏࣯ѵௗșᥟ˞ׁǸ˻»Ď઩ǌȁȰࢇÚ๖׿ʛħلĕӢɯîƷ̵̹Ոߥ٢ĕӀ˩ࣞǃѶŖĀŤĝȔ٧öҹ˼÷ǂ̈Ȟᝂԃʦǎ͘PիӒfȼආѴؠͬ࡮ĕΜƑģŃʪfɖő˚ްիˈΠ࠾ܷΜ׸ĿༀËڤՋঋûՃ˿ֈȑ˖ʛԜù঎ƨȀԞ߬ĆáǸͶsᛶ֘͸ƽ˚Ŏͅľ௪͚ׄE׍íŒЇ֝Ɨ৺x͖ýҴ̮Հæ࣪āࢠ˰ݒĮ˂ɣɫʛՄûƜ̖ӾnώɐٙԖبɌ᪂ʿפʋᄚ̽଴ҹ٦՘ԇbҋњ͋ĖҹĠƗŝ͚঺šĊυeÁɚൊԐ૾તłą๚zાȇȧ֭ץү۲гŅဵ࡞УϝʧÓΏਁٓЙƤɹۃƏ˽êʸŦ΁¹ͿƆևaǡƲǭ४ȣࡦdҸħ;ðĄȔୀκʂΚ׎̐ͅռǂ˦౺Ȍ̐DټёÃׅԈǫॴh૫ƌÜςˤÎ˿զ൳˨ӣHԗůࠓ¬ɑֈ٠ٴ੍ܮӮʹૐʦǣ֖̮ӶҳϽ˳¸ԯആǵֈؚȥԸHŢÌзQ̿ɮ๻ʊͳ̞۶ĺހǑϤĮǻŞѯikĶوƼ৔F૸ʓݲ̻आ¢Ўăމ΃ƅ֋¶ǝծҸƊ̧̽̇ͰĽMƸ֌ɨӭҶ͒Ӥ˫ǆұ¨ҩ˸੡Ȉ^ϰΕʂƆ˴┰Ÿ්Ɣऍϗݔ˥ا̌঴ƄϚǒ࠿ζӍÔͨŒфĽ˨௃ʀΊƞϚő͞ĆΨȠѹ°ÊĐ֒ᑜϲ὞˒হE̴Ǆዎк±ΟǃܖĨWĩᆒ̐ञǵȕȗʼŎsǦࡧ̂ႌيï֛Ĳ¾θശѤ୼Ȇႜ˷༙ʍᘪÉШĝࡡΏસTϮǺ᧮§מǧŵċ֪Fˣ԰ǖʔ̟ʘ|ʏ̑फɘцɑʌОƉڧλᣭ׋}ŷഡЍޭÕӻʑЫm࢛ѿӄº٨ʬ੮೤˄Ѳ_࡚ɒউZҰɰڜßÚĪΚq˓»ƢşظĳհǼǒ£͒ɟɓǁˬħ̧џҌǑϗƠ֞Ғ́ƆໞǔᲄƑΏÕuǽૐɣᑔĉаvřĸҞÕϲȨōЊࢨîࢴʭրĎʢǵЖǴ׬Åଢ̳уǽΘmȞƕۧƛܨćňɫЍñ्̖Ł࣐ҫʤƊɀƩԯÛޚҙٴȑȂÐɞǻ࡜мԲֶ࢈Я࡜ܚȈҀT༌УăĀЂĊͩ¸ˆǲҞΰƹٲ ɕ̩ͦÈۢȪࡗwƔϮƔఔúɫʰ੆ƛ⇈ɻcû௫ԙĻŀÇᒌĒ׽̍ĮǊЅöCʇ׉]ŀȗݒĦৄѨሪƍѠɥ߿čãĕൎƝхʙܶżԬÿєɛˣغů↨ɒଐ­ૄȽվЗɷσ঴ɝŀяɲĭϡϋ׮̮{ЌՈȜࢊĒᴴǷࢊȔϒϥࡐǉʤʙެϪȂ΍ҎΟ˜КᚔʅƊļᐜ©ᬐՕؐɳ@ᾕకϵ٫Īܱ̪ҏג¡ƼřŻÙࡏƤ౗ɵ౎ȄʜǗǭĵઊƬLˋɈŝ̜ïƺŪŸē̎ӿʓ½ʬǯ¤ŪԄͥŇśʾˑϙ̭ໟɺ ƶ˅IŖǹᕩإݟěЯʹɱÀ¨ƁїŧEėງՃɍЁ̱ĐًѼࣷmއƕߡҧŗļǺь੣͗ŋɉέǾΉƍlð͡͇ȷûЕ्ܕƱ̷β̟ɚȚӔƃϑεXЛŌŷˬâϣʍǁǧlǱƔΈʒ̯ťȕϋƝ̡ӽ͎سЙȣ܏tו̑șͽƴջρĞ۹ˡɯľŘƅĭЫ͛ӕ੍ڭċȏΊſैɇ̚ЫှÅОάଆܮԄņƨŭ˂ԸhǺȄԞU޼є՘Ԟྀࡲ˄˄ൢϚſĠϞˌǛİͺۦ͠ƬԢąǬÖ࠽ɐࣱŹˁևȪǇƟŇȕŒϳൃރӽīǦ͚ӵÍѦ߀ٍƥ̧ǮळƧԍ͓Ľ଱࠱صʱ֫җťʯےáǄɓםvϓşˇŲ˫UҗʍлĖțĵٕǣǨड़Šދ˴ܩÇƸ¾ࡳǶӝŝŞţ΁­ƅȄIȥġЁƀرūࠏǂЧʉеȲጥԣŉຑ޽˿Щ຅܉ࠃۛᎯ৑IǯԀůߒßۇξd]̢ѴĐ˝ƷжŹӥͳה¶Ӟ̘şˍʛǜZȾ˪ή֎ė͚ìֱּ࠘ʭǉɷ˻ÔΔȹĻͻ̒ɏıɏηʧ΋ۍĔ׽ƋƝ°˅࣓̅೫௻Ƚћշ؋ಗૣɣīر܇षӟ܍ɁϙƮͽPJβωǿÔǨٽ֧ϗ~ŀőŭȃ"],["@@ǵÕࣇè੾Q"],["@@ЅΙ୚˂նɥ׎½ƖˍӑƇ࿏ŵ೽Hෟǹჸା֊ÿźƪЖƭ"],["@@ૺű߃O͵Ƃ"],["@@ળŴŭǐᓘŁवȁ"],["@@ો܂ĨЊų"],["@@٧|ՀŒŨƍ"],["@@mͯߎʖ௸ɳ͉ϟ՝}ݚ˧ౣƣᝋʼѕک̌७ĀྔԐʜÇ፤ņ"],["@@зì˞ÒƚŽ"],["@@ûɻথ`ǴɈࢮT"],["@@ফzіǄ࠼œ˥é"],["@@Խɏ˰ᆲųܛqχǏ"],["@@ԓFÆňҎō"],["@@ࡈ¡ȨơࠓŎೃˋƟļࣃᒌȲ"],["@@ûǽࡱǕٯÜՅÁµ͖ᓚd"],["@@நȩᝣ˫ƖǇਭåƀƂݷ±̅İ੄ȔৈYƂʲࢄĘ"],["@@ޭĴŰࣀUɅď"],["@@ࢿ¤ୈĖˇŹ"],["@@ๆįञȱීŃ৷ĢԻŐ֤ȴ"],["@@ʶǭէÇࣙɄ۶ĐӖÝ"],["@@ٿźᇮ̀ՔɟჁș"],["@@˿Ý૓Ěඔ{"],["@@૾ʿ܁ȑɰəᵥɛᅋ̌ศζ֛UĲüᏘͰՠTؐǣ"],["@@وŅף§õǕႭfȿļጀȢ"],["@@ಛÐഔP·ß"],["@@ƉĦॆvĂāࢽ"]],"encodeOffsets":[[[150236,44794]],[[149717,45566]],[[152166,46406]],[[153281,46738]],[[155651,48023]],[[158526,50496]],[[159664,51510]],[[160159,51874]],[[146188,55700]],[[140471,56423]],[[141252,56415]],[[21746,56591]],[[170650,56156]],[[154205,60436]],[[167563,60010]],[[36676,66747]],[[71702,68099]],[[-184113,70595]],[[51472,70847]],[[165343,70555]],[[173262,71251]],[[61902,71614]],[[54174,73078]],[[183155,72527]],[[141271,73224]],[[-183169,73295]],[[79496,74027]],[[81410,74468]],[[76453,74623]],[[123148,74844]],[[56648,75068]],[[72371,74850]],[[145598,75670]],[[85555,75850]],[[144395,75776]],[[116109,76187]],[[88733,76781]],[[84145,77230]],[[150319,77180]],[[139212,77220]],[[143410,77649]],[[98850,78109]],[[115178,78460]],[[152730,78500]],[[69392,78068]],[[98597,78876]],[[91663,79042]],[[133824,43318]],[[110281,80007]],[[105354,81157]],[[78079,81563]],[[94908,81598]],[[52644,81863]],[[61122,81875]],[[100019,82083]],[[51254,81997]],[[58449,82280]],[[54806,82110]],[[59348,82047]],[[55722,82405]],[[48581,82795]],[[63660,82775]],[[51485,82870]],[[81948,82789]],[[62609,82894]],[[56033,83063]],[[60030,82987]],[[64895,82637]],[[93765,83089]],[[98844,83022]],[[59198,83504]],[[65179,83568]],[[59695,83677]]]},"properties":{"name":"Russia","childNum":73}},{"geometry":{"type":"Polygon","coordinates":["@@aM¡Ýľƅà{ª½¨ĕÂӣkĹuÕÇ¡PµSÁoEŧòʓēƿÞ¿̓ƛßʵ]ʍǴóĽĵîcȢɊǤÆЌ͊˨Ⱦ¬Ėœΰͪ¬X¶EA"],"encodeOffsets":[[31243,-1092]]},"properties":{"name":"Rwanda","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ÔôĈț̙ǪºȊńʋ"],["@@ݶęȨϧ٬T̮իWƟφș¿¯Ȗɍضѥš`Ǧ̵·ǩĽVѶࡱĞĻ¸ÿâãľǍk´QÐ@îløjÐÖNďȗɒ­pǩߴ३ᒤʽèèϤם֏။ᠽ࠽ុΑܵΥӽשř˱ʣƑƕNȻʾ৉͙ŀਙđлǈȑƁu֛̫ˉыࣂҙϚѧژЃ֭ࣞі̑ľϝӬƵ˰®Ǭƅ̦ØװԇऴқЊ́Ě̥ѺîŠį˦࿿ᘲϩĄćÅ˜઎लŷǶżǌŞĮƠĤƒª׶ňlbÀŒäƘbdhdǌĂİ´сѐ̽̈ūŘɎÄɺÎԲžɜÈӲŮZŰİȊ˰£˖¡˼±ĠഄڿෘਙߊսൠŉƦ"]],"encodeOffsets":[[[42996,17117]],[[47649,29795]]]},"properties":{"name":"Saudi Arabia","childNum":2}},{"geometry":{"type":"Polygon","coordinates":["@@²·˦БAÍęÄSĹªܣɺધड़ۃƿƳsWwWuµmi[ЃŃȡőµƯƋIá_ĝh©C[gŻFÇùƧt˟̇޹ŝȕlǉpǟ¬ЗͯఽϣőҷڳƱߝƇıǗŒȑȅϽȝؕǥü̘³ˎٵӄ³ܢłԤБL^ǵ֑EɢʝèԷ৏ઑЁËكҠͯǳōʝбƥŕɥڛFƙɰڳXΑŉةּ½ȦիÝŧɿѥɇݙ͓ȅԟŴÍʤżȎpМډआÀΨ˥ȠUΈĝàĵӾΣdđĦ͸ҐĹЂζ̦Ɓ͜ʐƠɘτD̸ƞƜܮfRṴ@о࠾NBဲᢴ@ᢲAƘPŞǚð¯©ŷ⯀B"],"encodeOffsets":[[37757,22525]]},"properties":{"name":"Sudan","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@Iޓĳď֡Mʁҋݼ͟ƊʃլӅɰګʆ̫ƷƱǥǝȧȟɓɉǟǛǛǗȟȡơơá³Яŀ֋ƣǗƽ˳ʂɁĿͩĞˁʛåŒƝ@ßǰˁŬѣԖǩtȳȁлĨʥƳȡvͻʜ͓϶ɑԪՉ̄ǥ͘®ưࣩږćŊhɂ̇ˢҽżŧ͔̾ȆɈݚʀѦŨլÞ¾ȥتֻΒŊڴWƚɯڜEŖɦвƦŎʞͰǴلҟЂÌ৐઒çԸɡʞ֒F]ǶВKŁԣ´ܡٶӃ´ˍû̗Ǧ"],"encodeOffsets":[[34896,9689]]},"properties":{"name":"S. Sudan","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@¬̍ǬɃ¸˥ÿȯȈˉǒ®ȶΫBҋࡓÃҁǊŉkŦի˯BʅB܅HƵùǑĉɝ«ʯhć]ƥÙĕgŕb¯S£ƞˊìŽĘũŉgДĪøۄFjƘѸÒŖƈݼˏΜƠųŖέםʸˍíçşࢫBǣˊŲŖƣ̓ՖƫöĉÝùĔ͜Ɩʮ̺ɮφĔânưĜǞ˞ЄٸǊ׆ąδεĦr̆ħʰХ̶υ˜ȯņã"],"encodeOffsets":[[-12575,15165]]},"properties":{"name":"Senegal","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@ˍKƘĸŶī"],"encodeOffsets":[[106465,1364]]},"properties":{"name":"Singapore","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@Ͳ´ŻːVѺїʋēڧѸصƠZŀͯސµ"],"encodeOffsets":[[-37993,-55363]]},"properties":{"name":"S. Geo. and S. Sandw. Is.","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@ùMÚĊ`û"],"encodeOffsets":[[-5828,-16381]]},"properties":{"name":"Saint Helena","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@őaϧːӺʭ"],["@@ȕÿĥÈȜļĠă"],["@@͞Çɦ̳ӱĘȽŶ§ǢǉVĺΈƵ"],["@@NŧŧôˢƸɭ"],["@@ȂƇ͔X϶χƛÅ܅ƒǅʘ\\Ɔŀ"],["@@ȪŧʫºÂî"],["@@ŷ¤þèĊÿ"],["@@ƛ«yƞǐ|Fŭ"],["@@ƩŰƖŨTʗ"],["@@ȼʡ«ƿǶƋǬձԃծµΤĽƖƆ¸"],["@@ŔɝßėșƔ}ƨɋÁíÐɰʢɮɵ"],["@@ǳĀÆŬƌÕ]ƕ"],["@@ƩǺ¦ŒȼűķǙ"],["@@޻дУиɶ¹הΥΌʡǩ"],["@@ƋP¾ĮĎĽ"],["@@ā˷ĐկּӠɇƌɥʚƛ"]],"encodeOffsets":[[[164431,-12081]],[[170121,-11015]],[[165597,-10636]],[[165425,-9856]],[[163585,-9495]],[[164013,-9211]],[[163010,-9342]],[[161903,-8892]],[[161167,-8922]],[[164608,-8513]],[[161550,-8439]],[[160944,-8302]],[[160449,-8113]],[[163717,-8739]],[[159580,-7267]],[[161267,-7506]]]},"properties":{"name":"Solomon Is.","childNum":16}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ΧŔ˲ÈĒ¯[ū"],["@@ÚăIy½śFwh[ĤmĬ̷ƩʋÅt[R¤UG¾ĒĴ^ÞUE{ţĻǯįÝʽȗȃ͝ʱȩ˛ࠓЖL̰ǽŹĒė˜ǉÎĽǺǀKǈǤʍ«qɢŮȝƘǸjĴǌǠĢǔ˦ŊǌʊmȚİ՘HÌer«Œśʐ͵"]],"encodeOffsets":[[[-12826,7615]],[[-11016,9611]]]},"properties":{"name":"Sierra Leone","childNum":2}},{"geometry":{"type":"Polygon","coordinates":["@@ȰԞ͡P]N¹^§ErŐÊŀƈĝĊ`øIÐ£byÇʕű˹ىĐƤćક͂ʓƪ Ɩт˴uŰǰt"],"encodeOffsets":[[-91507,14762]]},"properties":{"name":"El Salvador","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@įehɼĈɕ"],"encodeOffsets":[[-57617,47963]]},"properties":{"name":"St. Pierre and Miquelon","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ŝ¯©ƊȀƬÀƉ÷Ļ"],["@@ýìĄRŋ"]],"encodeOffsets":[[[6820,124]],[[7602,1606]]]},"properties":{"name":"São Tomé and Principe","childNum":2}},{"geometry":{"type":"Polygon","coordinates":["@@tƱ­ăĿƣ_ŧcĳO³»ĕũɃőıAMWQBqèŻR¥ ¨U¤uGÅQ×Pć¡·U¡̃ȏĪmuĕsyoQÄm®á¶¿ ±K¿O÷ùϳČȅĂбۈėѲ˿b΁תǪӈÑŸǦƜ͌|ĸƨğŞľǴȌώؠě˴ƻWǌɘþۄFټƱuʱͱ֓ňځˈΥ"],"encodeOffsets":[[-55331,3717]]},"properties":{"name":"Suriname","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@]œƃÿƿŷįWƍ̩É˅Ƕ߱sſș͏ů˙Ü۫ȇÉǳ߱cԫȤɿ͌òǲƶȰԼ ˞ƪÚǊ֠Ɉˈģʘǖ˨ȍcƗʨoʲǢьÏǰĔҺM͌ǝϠŅÆQ"],"encodeOffsets":[[23080,50251]]},"properties":{"name":"Slovakia","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@õręVǷŷйǭaJàĿoĹ̿ŇÞÕÏãPZćuŋTſ¤ƉkťĀ·âmLiyãíµqЭ«ʧöɢÜYòƗĤÁǂùKkZOnN~¾¦ÌfrEhǯkĘƎĈØ\\j¶܌ĵ̀ǦࣄÒŖƼǆKȜ̛"],"encodeOffsets":[[16913,47616]]},"properties":{"name":"Slovenia","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@će˚Ԧٶзࢯ"],["@@əŇĀʷٗБŞƎűЊͶɰ̨îƨã"],["@@Ïµ¤Įǒeƥ"],["@@ࢭÍכłūɝׁǃūǕ´Ǝǉλˇٍ͎̃ԃᅿ׃ʙƳŴčʗęɛĈŌʋǟćΣLȈɳɑ̭úƵɥÇůĸܿΆƑʜ®ݮӁİɥࡋͯԄɱʇવϣٛc׬ťɫ·@޷ʫړѭӭ࢛wŇ̑±ƽŧłʯƗŋઋ^ô˲хҖˤuŧƪǜXtƒϱʐࠅ৲eȆƵzďԶ¤øǊêŃƠbž̨įʬڰўḔʙҪΌ¶Ƥʖ؋̴Ůӄʭࡌƨʰ԰ͺΊÚَ»ƲƆÁʔΫİۤ؂Â۠߆ƪ»Ƹ࠘ӆɵ̸րЀҔǬӼś͘ӸඖǗ͆вșƠь`ୀџܚŹڈ΍ƹгɼýʙ̈ˋʍҷϤϓ"]],"encodeOffsets":[[[16926,57642]],[[19535,59224]],[[19617,59313]],[[24736,67385]]]},"properties":{"name":"Sweden","childNum":4}},{"geometry":{"type":"Polygon","coordinates":["@@Ħ»eɛÄȧNˍóz}MwţkǳZŏǹGɯ^Ǐ´ǯłĿǔ¯ļÛNYlR̎ʈζĴŚņô ZGьȇvX"],"encodeOffsets":[[32715,-26580]]},"properties":{"name":"Swaziland","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@DýƅƪÔ¦îő"],"encodeOffsets":[[56874,-4805]]},"properties":{"name":"Seychelles","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@ӏї̹éĵŋ¶رƅܷǩ˷ᇳࢳ࿥ࢱϑÐӫ̈łǸМƔź£Ɩ͈òñðʴʞãǰƥÊ°Ƅ΃yÕڔśŶÚɸ®ÊØMƈßvDǌƜzƘɖÀJ|ēǎR¤¨ǐÜ~rĲSƒ±æ¥̶SôǒÆƾ¨ʈĜӘǫŠDǜ]ȾBŨlƺˤòׄɮ঴¸Ƣ¤ĜêR_jIķ"],"encodeOffsets":[[43376,38000]]},"properties":{"name":"Syria","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@ǂ·ǇFþ"],"encodeOffsets":[[-74068,22376]]},"properties":{"name":"Turks and Caicos Is.","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@QṳܭeƝƛC̷ɗσʏƟƂ͛ε̥ĺЁͷҏĒĥΤcĶӽĞßV·˦ȟ¿Χ̭Þ؇̓ó̓އݑαɹ୕ǁÏđȈƱқԡޑ½ܩεȩ˄ƕƵΝƧБbÎɜη٨ڃլ̗Шɢʎஊx͇̌ťʖđϸªؚȟւ͕Ѩǻ²ƱBōư˛ϒčΰµȸޜଶच঄ɖᠶȔΐ͘hȆΥӌǟఊࡊϖ←ႱỨལ"],"encodeOffsets":[[24556,19965]]},"properties":{"name":"Chad","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@řԛӈ͝²ՓǸʽrႫÿIȺוŽßλŃϙ̨ǯͰĞцœǊƾڸˁͼĮüҢʗÄĠŠßĮլϻ͎Ą̈ßĂҾƉ΄"],"encodeOffsets":[[923,11258]]},"properties":{"name":"Togo","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ĥŁt͠ĨåuƷ"],["@@ĝƑmŜŌv"],["@@ĿOqƞŲƍ"],["@@O¹tWƬŠǞǩĹΟĬǟזIT½oƅö̍ȩ҇FòĵNťȡҹ Ų½ÀfȸȂŢĀRRļÌôŨ´eÔòôÈ MǢƁĚ½ĀgÄō_hL̸ǤĠ¶ì¨ÂhbvFà^ĸIŢƎÅĚk¨pĊđŮȑńơĘįʾȥÜƉ³ȉ_ǉNȣÎƳȒʱĐÉƴ±Ú¥MƃȊŉfCÏoćĿț@ŇïZğoõsɅk»­·ã¯ĉ£»k[S±«­ʛǜೡăʑǏ̧ҩǭsƎݝɌȳe˱ǔ͏˷и­ÿףҀ޿\\Äٺ̟Ŗ̯éȵȭČǕŁ̗rѧە།öࢯӦpżחŞƉäĮȫɠ࢓ę®·ɖąTwȊăƧɚΉԂ˝ЖJԔԻȑΕǏĹňωɩōŒÒІƯFĳǸ΅øëŐţȭΡκrȖƕĨŘȧbăȸƻČuƜəƠíǒĽôƃƓĭ¢ÁҖϰ஠°ЄζώΦ֨ǭ٤ɿМ\\ּӉզ̱ԮUȒ̮ƐֶȎŌÐƞ«Æƥñȅժ؛ے×вɑȒ̺ÐÒܺǖʐ̚»ҜìƄ˜Ί¨wǴɘƜĊÄQ¤Ĵ¿"]],"encodeOffsets":[[[100771,8092]],[[102473,9817]],[[104886,12277]],[[102526,20805]]]},"properties":{"name":"Thailand","childNum":4}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ĕÃÏÔMh^@Î_ĈW"],["@@̟ɝՏȔлƳF}åÇ·ly§ù¬ʩৢÀʢǉ֞Ǭk\\yWCȔ± ũɜØÀ§ê«ΈƮЀqłZĐ¨~êdƊWJ©{ƗǖɕğĩĘȹȌÉɄĶ҆ǗèܟȌǹȑŹ_TċÒÑʻr÷E±ΗćăPuløÌb[ÁX©`ĽtćQƟ­˅šȧȧ࠯˧œŰćʂŲڂ}pÃAćsÏIb®ɄÝŘѧĬˋ̅ǟŵ]²ķGč¿çÍœnƏǏ_őÑÇkó~ĝ]é»µ¥Qí¨ȁƔ£RuK}Ua[]Y·T­Sʁķó£]©oo£RŧÓġnĻüÙĞÔ`ȰҊ،əɘϢοÊŘʑîÌȾʘƀشíƨʪȖÎÁŤƖ˻Þը~Ë̴Ŵǚ̚ġׂ͆̌ʿ͉˳ʀƷʲ"]],"encodeOffsets":[[[72361,40812]],[[72662,41205]]]},"properties":{"name":"Tajikistan","childNum":2}},{"geometry":{"type":"Polygon","coordinates":["@@¥GקȌǭˉɃIǵQřŕ½ÕƯщŃ¯ñ˝Ǉˑŗ؛ƓƗĥțÝĹ¹ƛā͉íǿǬϿ¬ɵǔçSSȼċĠºǶýϞٹKɹ˚ԝʒŹɐСƢүcށʶƇȲمƙƙݟ{ЙɯǙʵڇŃചƏɔ̱ŎƯÛầ̰½ŋĸ^Ȯнà¢ǵșɾÏ̂Ǯ՞ǜǵЄKɈƛѐµŢ̬fǖԝϖʓԊ՝oǍıŽɝѕ̙ӤӀ̰ࣄǀ؞̵Ӥ֙Ҙt࢔·ĎòŻю˜ɢѰÈǸʢϐǁ˓ˢ˚ĦĮٞϣՌĥȧɒƛĿ\\̟̬ƭڎɈò΀ƵӤय઀ۅҞƷબا̞LՔɏãǛ]ƽAşXġ"],"encodeOffsets":[[68119,38245]]},"properties":{"name":"Turkmenistan","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@΄ŶƋȧȷò"],["@@ěʤŞp¤ŮǱU]ĢɌʜǠÔਨĮ̆Ŧˤĝ͋ʓ౏һ˷ȣ"],["@@ÇƟÓŜŔ"]],"encodeOffsets":[[[127014,-9565]],[[128070,-9740]],[[128662,-8335]]]},"properties":{"name":"Timor-Leste","childNum":3}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@êßőǥƀǜ¿"],["@@īAĎÚ^×"]],"encodeOffsets":[[[-179365,-21677]],[[-178128,-19086]]]},"properties":{"name":"Tonga","childNum":2}},{"geometry":{"type":"Polygon","coordinates":["@@ݥÅ΀ǘlˮơĬ؞ĺĭƝl҇"],"encodeOffsets":[[-62476,10378]]},"properties":{"name":"Trinidad and Tobago","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ȡbpŴɮÅ»ď"],["@@ŻÑŎŜnÉ"],["@@ƇȅĐ֯ſǟŅŽ́ƃʛ२ÁȼĭϜׯЄȃјȡİ˽Ҟiؚ̦պȶĘèĴÛϜ״šŶͦ˄qĔतͺŴOƣĴ˒£ĆΣĴ¥բ˺ÔǕձחĪʍΎɅĮͥίҿӓͯÍȉɌ˟΄TƥȢĦʤˉ¯¹ʪÉCuǅOËKɇäıD©}¹ŭËǃõƍëƭû¯¹ƍûŏǋǛ§"]],"encodeOffsets":[[[11221,34532]],[[11549,35588]],[[10522,32446]]]},"properties":{"name":"Tunisia","childNum":3}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ʩ@ɀĐªď"],["@@N£ʂ͙ŷȥĆ̗ԼïД˱¤«ȕĲṶ̈̄̅ȾғvϙŶ©ȧΩ͈ǓaɳȄŧ{ůp½PÁFǡıÛ¡JÎNŴa hďXƩùƇHӫŠŽƋ SėĥƯķągĸ iL^Qěéơ£঳·ɉõιƷˣñƹŧkȽAǛ^şCӗǬʇěƽ§ǑÅóğHŝRķEã¦²ƑıT}qÛ§ǏQ£Ċȉɕ¿yƗũÅ¡ĕuCƇà×Nçͦ͆̊ŝɄտ˯׃ȰࡑՓݳūΕŰ̗֛̐ɠשÄǕԃٍħϹƘɇ֑͠ĆʇƫÆİՋҪĴȦǔࠕ©ŲȠϻдƀÊRɈ߉ʢŴ̪ȞʱЂĮȥ~ťȒɂƬǗØżƟƪǼɎډçWĐäΞҴΈҠ²ǆŕɆRĶ¬ĭĎŤĬowĵ࢜jȁŦƞĆݠŊءƬʄᄚħǠǲ܊Έࣚʎ଴½ɌĚŰŗƦƋɖāв¢̖΁̺öʎƯ૆ɓ࢜ƶ۶ũیʦ˦ƌĄĀǈà}QĒÂҒ±ĎĴpXbPÚNÔM]ÑîsŴĩüng}mXy¬Ȓ"],["@@ҐnƸΑܠ˗čȅڅÔ֡ěȭʉУǃӍΟ¨ȪҐʾֽ_ÉĮʘȒAʆʚƎǘɕĬîŶtÐ~¨dɨvêƶd¨b¨[ưŏ"]],"encodeOffsets":[[[26594,41100]],[[44476,42144]],[[28135,42954]]]},"properties":{"name":"Turkey","childNum":3}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ĝʮʌǏ˟"],["@@ÜǳûÇʣʀńϚǀϝ"],["@@īҍđÚvψǈl"],["@@Ḯ႗ϝപ৏ΕୃÎɗ֠׉ɏгCɧŢsZǕŗεŮнǄȿìڵبϵЋ̙ƟûۡɉзȷӑĴǏʉƷāƙͥŦ֡ŝī_¾ýÒƝêŁDŻŉÉgũfȡRȉGmĬÎĭ¬ƓɖºʎćҪȽђ˙Ȥęƥ߿ɚƏîہɦqĦhËjŧMįĔěŶć ɱIĝ¼ÛÞ½öğʀ˃զÿɎčƔęĺŧĦƍĤõÚĳȈ¥äŢuŮVºĘǄV¬YÔ©ŶǙѢeìDÂà˶A ˆKȔŰϠպ͈͠jƄ}ŎÑPėõd¹rmfQ¨¤ň£¬dzüǀhvFÂp¸T OÈvÖlĺWĊÞKͼ§Ė©¾ß|ĽƆÞN¢bTHŸXÎÊZ^GlG౶@ಀ@"]],"encodeOffsets":[[[40665,-8168]],[[40445,-6322]],[[40822,-5023]],[[34717,-1026]]]},"properties":{"name":"Tanzania","childNum":4}},{"geometry":{"type":"Polygon","coordinates":["@@BµF«WίͩĕŔȽ«ŠతǼʹRː઺਎ÛȎϓƼİ͢ûǎŸ̰Ëè˂ʜͪĝɂŀ˴ʁǘƾ֌ƤаĿâ´ƢƢȠȢƀ˥®gk³ĒãłHǋP¯ǍϪַm¿æ̗ZǙAţƫЋˇǝÑƥȿəǽγধ౿@౷@iF]JÉYÍÕeáNSG"],"encodeOffsets":[[31243,-1092]]},"properties":{"name":"Uganda","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@Ś£ӯĒ¯Įцƛ"],["@@֝aؽ̡ȡ¤؛śڿѥɠƤ¥Ɗ͉ɁƢЩκ͓״Řͼ¹Ƴ˟ѭ¹ͯþ͓ʝԯåһ˫ϫƔĖҜ࢟ʲঀҘÏŲƏȑİ؍ûթǖƪŰϟľڲáǼŠ҃vʇӐĨҿ˹^ĦƇůԙÛӝֻӹŧÞϱřĢƏ¤Ʊaˡħĥj]ol˱ªő°ėĔ¬^Ǆh^°mþÐÐĘ¶¾¸TîĢĊÒrÞ¤¿Ħ[ÖEÀǀÌE^eHŁhEȠÞ͈ēTǔd²ǧĬVðPČq¸ʱŖĭˠˏǔSЌםƶéEړˬˏę˟@­`ǧýkƩ}ɻȥɇsȁȋkСƽͳǨߙÄȁKǥĂÁFəŝěƌƽrˡɰķCVƎŸİĀǀŔƄ^ʸRšø³̰¶Êˤʘ֦ДɖĲĸHðćǊČÂγπ§ǸþĮîGÖ[ðxLŰÖźĢèþத²ࠨŷƮaĸNÌQżŽͦqTāƆĪ֢}Êg¤ïcKĲĜàl¨Tx¢vDȴȵۄĄʐǡƶæiªSÄ¼yÄ¨þìŀôæú¶ŔĀȦHņ°ȒFǄM̠§ˀɎદ֎ӃɵďþΣ܌©ȼƇɼ֥ѴцǗڬǞԾЭǲŎֈȑɎԘʅʦJz˙Ρȱˊƽǯ[ŭǻǆ£ĺɯƭ͍݇ѿȵŻѥ"]],"encodeOffsets":[[[32781,47313]],[[39132,48222]]]},"properties":{"name":"Ukraine","childNum":2}},{"geometry":{"type":"Polygon","coordinates":["@@½ɤĄĴĤ¾öJÌ´Õ˴mxfÈĸʎΦ˹Ά͝gŻ\\óŌTȸǲrNs͒͹è²ϖǥ̵̰Ϫǝƈ̃А˫õëăęíéó·{g¿@ПƊñ£«Ï¿Ɨǩțŋ͏ʑ؝ə٫ƂӛĿޝϖօqӏӄؘʀʎŘǆĂàŴHÖÂYÈjÊGÄyÀlªŀĔìǆjàŠȄrücƖÚà"],"encodeOffsets":[[-59198,-31599]]},"properties":{"name":"Uruguay","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ʥ¸ƕ֮ȔɞWɘՐɿͨЁٷљ"],["@@Ľ_ÝžƜOō"],["@@ǬhʎǍΙŷÑǒǽŮĦðĊđ"],["@@т»ŭĉα´ÞĒ"],["@@ƐűЋXƍɢʼĮƎȵ"],["@@ȣmƱŎǮƴȒGiʋ"],["@@ýMǨ¤ĩ"],["@@Ǖƿ˖ͤĿǣ"],["@@ș֊`͂Ǻࢋ"],["@@b×ɡժɀӑ"],["@@ǳĠŌèş"],["@@ǫU͊ČƝõ"],["@@ıkƂĘë"],["@@¨ÕůJĽŮȆá"],["@@ǩĄưZzĝ"],["@@˪©ɓ»ÕĦ"],["@@ũeź¶Ьˏãǫ"],["@@įŵŦǼuÅ"],["@@ÏÃ֬Şઓ΁ۑÓŖǮʲņߖÔ̐Ɗȣƕ"],["@@ɉlǈĢÂō"],["@@ˍcǶĊĘå"],["@@ĕmĪƺSƋ"],["@@ȋĦƌĦĽ"],["@@ĭpŌþÿ"],["@@Ǆȝ̝ʪĨƆð÷ŻÅľÓ"],["@@úśüķ٦ŕŨåˤúĴXΠ»˰ŏðƃÈ@˾tԲȳ̀ŖǠNÍૼœࣞʴՔȯҊǳ҈ǱࡪΓӨȫǎãǮ̙ȌˊHŪΗ̦ŬĹĻƫ֢ʣ̲ƝÈɛ̆ౙ©ą̗܋Ӄ̽ɱϞʧɂ@ϸǶϼǶЖŨپĲࣘ͐ɐƈÅÔöę̢Ɯ¼Ƃ´ŀɘNܦEٶ@ՒԪ̰ȎΘʮͼǖȠ|ĊJᥬFǄʄϴgӌՔʐڬٞيǊĨǷלŔήɛJଗ̴ĽÈίȰGŚ˻ǡŷկĉǽǉāĲʣJçȽɻæ¦Țϵӫʛ÷ðÇđ{ĨŽȅƁêǵõҭבăȳǺǝηʕʶđˀЅΦÎȩɞŎYŌį|ʅשţưϽȋhɌĹÃĳĪŏΓ୵ÿࢿ҅Ĭʮĉǎ˭˱͕ʖŇěԣ^Ǣؕ࠭Ƈ©¶ǐՃ̂Đʦ̊ǎ˝ŷƽǅǐҡʮ˅ĉƽŰƑݩੱȸˆҲǇdO͠ǉçǵƀBƐȺtǑŐŅ£ƢŦƉČǎŊčĠêļɸ¼ŅŌĎïØˋə­æ÷ƭųŦţŋ½ƐבʃȲR×˞ϙѵʬmćģŸșǞКȕʏYȻȐJڂϵãǽůNԱОҾзȴćǷř|«ĥʭȚЊϟį»ȗȘԳĜӠſƺɵШ`Ϻࣿ΍۸ƹƢ̣˟ƔCŏķ×˧čƥǶPɫրZ[ɻȖɨĂį]ǑͱʗˇóŖƏďʡĆѶɏɟɻʓƐĂŉȘëʎ¤Ă­ßýܙȫĭņǡ̷ȯƫ́sȖ¹ɏͣLλǋ̑Еƈ¥ɧѻ̇ŅnOţΟƕɫXƈ÷ėœȇƈĞțΙ˱ ùǋȽìMǥƭþõŹÃ×ͭɢࡑ؎વ½ǵň̷ŹɶF̄ñƫJȞűƮǿזೕÛࠋ̝ԓՁûsƠǐơĔɁ϶̍ŶȱюŊǔǯœvΆȕĥǕǰɯτʸ̨ɧĎÄƽōÉčŘǈ࢔ࢗ࢐̋ƬɟÕƁ޻ȑāò@ÚĊëİ˓ȸÔƒŵĵחȜ˦NőòߍŹǺüWŜơġǗǏxČƣŇ͑Ǻ¼ȃ͸Ľ˝٧¤ΓÏɣƯҋǠǥùåřȦģπŦÖ±ųğƸēȘŤ ȏ̯ǙפΕŝƻãÄşčÇǴ׹̀ĘǛƅșƕǆ̹ƏҏƂŞ|ćȸɟQ͇ʀȯŗ¨ęƩޏȆյ­]Ɋ£ʓܳʯȞƐɃAǞɿŔ˧͕Ϳ߯ЍȊŦοǇĪȀɁɉĭćäÅȏˉ§`éĚĝ̝ůŤƕų˻ˡňÚƛǪ¥ÍɩĴЙʐѱȕ÷ปӚ̉Ԣ£ЮړټϩܖֻԂۡẴĳɷ҉ɵǽ߉͚ͥʔЋࠀঙڰʡˊ๟XEϙᛟIẗসĢǨ፹Ƴ˵٘ԧΪˣlăɰՙĂϱʴܿ¬ƏĞ[ҘǕòǺ͑ɂӿռÐѪӳ̚ĕˮ®Ɍ̀ˏ˃МÒ ڐ¤ܱöŅˋΉȔÇÇúǪڗװōܲѱԂʈࣶϽଔ݈͠Ȃཔ»դ٨ÅȵİԛRz˚Ċơ´ȒȉǠɨĎɥÔwåȥעʉˆ¡΀ؠǱࡄāǐÂĺɋĹţ°ˉ΁ǼlŧOfâѠφţʳĈˍǅÙ»ĲǗǓ˜ÿɖ¡ΎƈȐɳŰĞŒɕƠƢaƐ£ĞĽb¸ÇŞ୎@෤@Ϊ@Ψ@ܒ@੼@。@。@　@੼@ܒ@Ϊ@ܒ@ȤAN͆ʨÅM¶ɋ"],["@@Á¹ÁĜń¡"],["@@ƯnÔüƳŜѲÛȡƩ"],["@@ѭ_ҼȔǳ"],["@@Šŋёōɚ͎Ęĳ"],["@@ȉØļ¨ĎĿ"],["@@ƑjΚǚɇȃ"],["@@Ѿ³ڿƁÚτZ"],["@@ŽBŲĬƀ·ųµ"],["@@ԱYࠪǎŇĎɢĔļ«Ùțҳč"],["@@ˏ̼ú«ŕ"],["@@ԾƉсċυƼˊÚ"],["@@िћͲͪʺ´þǼϮPŗƍ"],["@@ȲĈŢÅ˃ǧǆ«зȃޯŷըȐĶƠǨ«ÊÊ̩ČÌň̪Ä¶ė"],["@@ǹa}Ĭǎǚęů{"],["@@VŰŤóŹ»"],["@@ŕìƜ¬ŗ"],["@@ĆƑʞūɱÈ௹ˇÁǂ΄ʤ֠Ť˦Ë"],["@@đāÔƚ~×"],["@@TǇəŚvȨȐƹ"],["@@̕ǥ°ǚʈŴ^ŧ"],["@@ȴCóǩ؉Զ˨ùȢˍ"],["@@[ßƣØǀH"],["@@ŎÆÆǏʵÒɔ÷"],["@@́ɱñȒδ "],["@@ƟRŦĂzē"],["@@ǫʁɓǞ˃ȱëɂȆΒ̘ľɬȑfȩ"],["@@̨gĚȕоǏΦΏ˵ĨĵķǶTǰɑȘ_õеࢭԪƈÌÙǌիǐςŖŉĬ̷Ñǲƴǌ"],["@@ɉĀŐĦɮųƏ"],["@@iƓŻ[̹ŲɾΜˀɇŕ"],["@@ħ²ǰćó"],["@@ʀƃ£Ļø"],["@@ǛĘȒɲǿƭ¥"],["@@ɂǍǯѯȫ§Ę͊ʏ͎ȘĀźñ"],["@@̶ÕɅÙ³ʙǬƞ˫˛еҶԦK"],["@@̯ÓöŐ̈́ĉy"],["@@̌Տ½уʣʬĈƈϓŤŌǤɉȈ̛ȫUɬ̾ɈДƷ"],["@@ЀD­ĹɢŃǑĹѹɘŹԁĳϫƣøû͓ƵȠǲϙźăȇ˫ѴҶǤά̃ŹҶ̔ōɪÄ´ƪʼÙ¡Õ"],["@@İɅɢŐ͢ğ_Ǖ̱¡̠¢ȱՏƼŅȷǿvփѴȺȺƬķ¦Č̪ "],["@@φcΖџЁΔ͌׉ԕʹ|τνܼȲǋ"],["@@ϊ¥āōǗÖįʏzˣƙϟĀ;ǮǢeÍŚΦ³"],["@@ų´Ȭà÷œ"],["@@ǱüΔńǡǿ"],["@@ȿWǊĊ¶ñ"],["@@ŌӲΐƊs׹ѧ"],["@@;ÍŚͳҳűࠫȴʟǖԌV̢ƒ˺B"],["@@ʖѡřëŔʸ"],["@@ŇɭēĸȜŶ"],["@@іƃͻ|ΉȪʰġ"],["@@ή¯آĂضʳ۴¿ćԓʽǷţƔࡻ˔ڃÿī̐ʄ¿"],["@@@݁@௯@݃@௯@݃@௯@݃@ӫȪȦÖæЖĭʠŪψVžH@Áµě¡õˈřҨ˭،͓ÔʻèYƊîŶ¬˂ĠȠDĦʶǂjŀԴľˮ^ˈƑĜʑɢõʆǅÂÍĤ÷֠̅ɬɽɰɱϜϗĜýŢŋÎÝ¡­·ÅƴŀkÕąȨÃǝ̒ۖʗƠÂĝˆ[ìƃǙޥ̣ɋɱǓ̌ʤƈ͇դĂņفǱǗ˻ơÄ©ż̨΀ʘ ˱ʙƮգՖաĮƵЂ҂ÛΙɢ˺̩ ƍãʷȒżϒ˧ʇӉȄӳߺśǻΌߧЙƔ˛WũΠǼÜǅqŏƦÛʃٙɨÏƉϐÃŊǇɒ_Ɛűҟǫ޽˴ࠃӬ੏̤ɔƞBɌȎ˽ÆǬȞ`ϋƪࠧʩࠩǊ@Ǹɗŭੳń৛åŔ׹ƺǮˬҵʏճĈȊƢݩàƮèȷðƀ˄ºങȇĩàɈɂӹƅCǥǱwʦŵ͑ƻѪîž÷ϻзۯTŅĜǟ˯ĭŲñƳǡ ǉǽ΅G˝ˁȽ¨үģǱÜÆĸРŊ̔Ɍ͹Ĺ͹ŎϨвϖޒʨମŭࠓʜԶˢҗȃՕl࠙ˑߓ׷П~̬ʡҩŽĲŏҹ¿ѳЭٔǑgǣ܍ϙŧٕť٩ϕփǩǋ̋ງӗřĻɶįȓőñĺǕǓ؝ƩŗƹģǺؓʭݏƓ½ŢȰ~˅ÚҳЕʿñȳǎȿǅɉĢWůǡÝ´ʂ̺Ø৬װМĪˈwęęǞŏĆĮΒÅȻǜȦʶ୒ӌȲó^ȶ˸ɒЌȖ͒kȟƢð̺ΖŤʛºƘҤ˄ƔͰ˩ɍࢣɛʿȤàŲ̂m˻ĦɋƇŋj҃Ƌ؁ьȿùωȚݑɃĩŉڅsΞŴäˆ˱ŸϊӽאϬΌ؇ΩĪɻटǁϳĄݑԔ˝Ҥ̎Фķ­ƭ̼ƤɨƧʸƖСĆː¤ƍÈȵĕवÌȸƮΉ¶LĘʕơʡȞfǜʵT_Ė̠ɧĜϺÈāȘњ̼ξpƚǴǟïMʜΒĦqƺϺ˜ĹķğȞøǼ¹֔Ȃ˶ɚʸÛܾĘ˒ɖǫζхƶҸŨÜƪɱƜՇğࢡχͫɸºȓӍƒ੟œএň˳ŪÆǖљȘ٬ňਹŘֹȢහФ٦Yũǀ૆ɨ۔[ɋBŝ̡˴ō๘ДȪΞýïŪڝÞՃͦƴŤ٪ЧଠŇǊх¶҅ıϫŰŘːຽèӿшᖉن΀ÆƞϮঔEনżဤજǉƛࣞŤ݊ʌɂʿ¨Ȁάx׍ĢʹŶ̮üÂŉૼĤ௄Ң@ĭݠƝӉǉƤïڴɎࠆʡ߲Ōذ§ɐÑƓǃՊ»Ƨĝᖨ¤ೂʃཐµՀŵဖĞіéદ͉;E@݃"]],"encodeOffsets":[[[-159315,19469]],[[-160613,21272]],[[-160242,21435]],[[-160986,21725]],[[-161586,21972]],[[-163197,22459]],[[-82989,25311]],[[-82311,25746]],[[-99502,26788]],[[-82111,27934]],[[-93996,30209]],[[-86945,30355]],[[-91365,30807]],[[-121188,34187]],[[-122924,34733]],[[-122759,34898]],[[-77357,36086]],[[-75912,40634]],[[-74250,41970]],[[-71657,42256]],[[-72202,42370]],[[-72951,42488]],[[-69823,45397]],[[-125331,48533]],[[-125514,49313]],[[-97078,50179]],[[-180517,53036]],[[-182148,52890]],[[-181399,52958]],[[-180831,53112]],[[184042,53152]],[[181674,53128]],[[-177718,53388]],[[-176603,53527]],[[-178869,53284]],[[177893,53617]],[[176960,54286]],[[-171995,54626]],[[-170614,55195]],[[-169821,55369]],[[-169534,55436]],[[-166193,56164]],[[-167399,56301]],[[-163343,56476]],[[-134491,56402]],[[-163709,56452]],[[-136050,56212]],[[-164177,56666]],[[-164541,56643]],[[-136504,56877]],[[-159299,57161]],[[-134122,56821]],[[-136771,57692]],[[-135966,57598]],[[-135283,57456]],[[-157909,57872]],[[-173829,57995]],[[-135932,57883]],[[-137205,58210]],[[-136567,58372]],[[-156679,58496]],[[-138209,58728]],[[-156567,59212]],[[-138987,59643]],[[-137912,59558]],[[-156074,59761]],[[-156145,59889]],[[-164781,59983]],[[-151574,61507]],[[-151281,61249]],[[-170122,61833]],[[-149907,61901]],[[-151202,61902]],[[-176888,61909]],[[-175578,65168]],[[-144386,70425]]]},"properties":{"name":"United States","childNum":76}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@§QûD«TæBbëĄÂfE´ÍŰmſ"],["@@d´fĂèhiGo࢟ӹҬƝńƧɀÓǂ@Ā{`ZZǦƺt¸Ƥó´s]ÆđjĭäOĀmÂn¤[b¯LývWŴ|ʜƣЊѹʫǝªYųɏXρʋ͛Ŕˁ_ʱɿƸ͊˴̋ˀׁ̙ͅĢųǙÌ̳է}˼ÝƕÂţȕÍƧʩسîʗſËȽʒí ŗπÉϡɚɗ҉؋_ȯÓMx«xýI÷ÙQĹ£ĽtŃČȭʱoWĢBŠ^ƾäǜՓɐ̝KફبҝƸ੿ۆӣरͿƶɇñڍ̫Ʈ[̠ŀɑƜȨՋĦٝϤĥĭ˙˔ˡϏǂǷʡѯÇ˛ɡżэčñ࢓¸AᶢᓢҼᏠউࠜݷৼŘਢÝϴƎӺͻʴεʠĨùࠡЪUǰۅਐdƪƗÅȁȴŹʀXɈӎҤຘۖ"]],"encodeOffsets":[[[72916,40850]],[[72650,43263]]]},"properties":{"name":"Uzbekistan","childNum":2}},{"geometry":{"type":"Polygon","coordinates":["@@ÿŊňÒǛ"],"encodeOffsets":[[-62642,13474]]},"properties":{"name":"St. Vin. and Gren.","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@Ñäźļ®ķŕç"],["@@ķǦĈíÅ"],["@@ËȳСĂǄĖƺíưŊ"],["@@|SwÌǷͯΏɷeғˏ˧ĉɏ¸ʩśŧΥЙַ̎]Ɲǔ͑\\ǱŖĴɛϖΥƦ࣭ւùɋӛȋǑ͵ݱΟ̳ΕéEžƃŠ͇ǕʋeѻϦ˫খ΃ͺȫ@ҢԺ̍ϖǋզ~ٚ̎ֈOɀ̓ĠԧŏߣםۤԛŘ͡ē݅Ǜ˴ɝźèٚɳ˺ŋКɗŴʵÅ̠ԄŘמừψ̤ʒЂ՜ǐ՗ʕĸ͉ǌƩõeŘ˱ѥۻв٣͆Ħƀǀ̜ω۞tδਤ΄ŖŮʖWǖ́¡ÿɘìǲǖä̼וڮ¡ήɫĒЅƀǑཤŞͪ̿؈ƿɼz֌̠Ϧџ§þ᎚ĘсǍ҃]Ȓȯ¯ȝǆŨȖΗČČÊ»CƂâG˺˿}ƶƬäڞ҃ȗǥȋү̷E˄ƽцƴՒrŴÉƼȱcµĭ³s}ĝЗȫǣɷĤǷuğ¦±¼W¶nÖC ]h_H}÷³˕ƫΫÕµęnÙ~ÇA½pƝÅßãóġ^ǬțǶȥǲȡ"]],"encodeOffsets":[[[-62461,9081]],[[-62280,9358]],[[-65381,11399]],[[-62199,5327]]]},"properties":{"name":"Venezuela","childNum":4}},{"geometry":{"type":"Polygon","coordinates":["@@Ƽʷ£ļþ"],"encodeOffsets":[[-66320,18222]]},"properties":{"name":"U.S. Virgin Is.","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@̥ƙ˼Ǹj"],["@@ĹmŸŊ}ě"],["@@ǗĽÔƨń©"],["@@ҿȉ±ȕǃęЙªÐɟǟǩnœӵϟ̥ࠇɢϵԢұgȑĝx௴ணǒRڔߟМ౫ӝŸ˗j̗ğŶĭėðɉĉrOׅƅŏ«ɋݡσÿƩث˓ɉɚ·ɕȃŖķÏŖi`űʗF˒Ƴǅɍϫ̦άэãſչψˠͿTǓ֗ʭ͕ϵ́łŲÝ²vڮȪ͞ʁǨśȥȮΤļǒƖAǮɨÇúĚ̌ĈŁʼÍEȲʯɎI˺žļͤÑ^ɘʊ~њ˚ƲĨĖ֔Ŋ΂ɯׂǀӠIĨV®iqÄÚÞ¾ÒhÆ°µæĕØķĂ·¸ĩƸpƠê~HüϹʺĲeV©éġƄYƜO²oÞțɜɛǖ˃˔ēȸÏĀ¹Ü˿Ʉwú¶ĶD੉ռHtĄ°ÀWæêfzFǔNʊ×PžŖ¶è\\ÊĨĪBÄòűFkh_ÂIĹĕVqbÒ®t¨üȟƤŻþÿϹȽƟêɅ¼ęªȁʼÂĊΒĕ®{ÔcQwõÝCáȔϣЌ¤¼ªƜȮÄіʫ̂˔ƔƳņǌ˒ȭǜɔȔąˆĲƬ˄ΊǬӊΡʰ¢Ōĥɚ¨ȨŉȕɷĨѭʶ¯͊˅΄Ǵū"]],"encodeOffsets":[[[106562,10641]],[[110102,21429]],[[110186,21727]],[[110564,22025]]]},"properties":{"name":"Vietnam","childNum":4}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ŧéŉŮ~ŌȴǏ"],["@@̇Ì|ȒņZǆʷ"],["@@ŞŹ»đ̭ôĪƄƢT"],["@@ɟsƖʾš"],["@@̯ĜȠŮŐɉ"],["@@Τ̓͗ėčʹǓØ¢ǐŖÞƯ"],["@@ȥ_ˠƚùŹ"],["@@Ä˥ɠȆŀэęř˯§ŃȎÁٜƬƹ"],["@@ŻgĈĄ²àĳ"],["@@ąĲöâPǓ"]],"encodeOffsets":[[[173560,-20009]],[[173399,-19394]],[[172489,-17963]],[[172490,-17181]],[[172336,-16728]],[[171431,-16482]],[[171942,-15806]],[[170748,-15182]],[[171607,-14603]],[[171509,-14240]]]},"properties":{"name":"Vanuatu","childNum":10}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ϧœŨϊmŲƕ"],["@@ƀȁ¡ĳʻZȱɲϐª"]],"encodeOffsets":[[[-175569,-14383]],[[-176469,-13788]]]},"properties":{"name":"Samoa","childNum":2}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@Τv˘ġ͍ǉ·͹ǶȀƶȔß"],["@@ÇĖþ½"],["@@ÿÌºtą"],["@@ُ̕Ź˱ʧݧίဇӳֳӳ֭Cӧ͗ԳǑोŃԳѭΝBԥǫ̷Řūɋӌ¨ΨǋˌŹެãǎǕČŢĀçˠĺ̈́˸̬ˊv֜ȒƂмǇਚĒ͚Ŀ৊ȼʽƖMʤƒŚ˲ӾתܶΦូΒजጉ"]],"encodeOffsets":[[[55054,12941]],[[43783,14034]],[[43815,14307]],[[54360,17048]]]},"properties":{"name":"Yemen","childNum":4}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@ɡǘÐÊę"],["@@ƾӋÃ౑ӯǶŅóĳřʇεH̹ÜM°ĻŀǓǰŁǐ³ɰ]ǺHYŐlǴxŤ~NôyòLźyΤVĠD̏଍ȽΟߙٍ଩࿋౫ફହݫկȧԻnșʣӑ¾ƩǏșIݭƴίõ׵Òʷ·ϧʭੑùё˓؇ƞĺțŤɡ[áʀɃuȧğƄæІӧݞĪĴƆǜȴīٚܧઢͭ࢔ǭͨʕ͢ʶƒ¶nŌü¶¶ļŎďƞŗöέܨǁ܎«ìÊƚȐƄφļ@Ţ@ᲄϜ̵̦٥ŷ۝ĊęߤP¨ĆȈØä²łǈɈƜÞ¦ɾ֦ɪƚȬEъʹÚsȊMŚğݐ[ʰĀͦޒĶČɠ¶°͒˼ɀ֎ֶήͮΐՊǴĘðÊĮİÆȠhZBʈ Ȭʎěɨɂ̦Uļú³ɐࣣɈ·","@@Ɯ˳͞ˑʮÒĚɜ̈́ɊlϜǨȀŶŒ²ŀĺíȖ֧ђYģkȩïůßĿÿȉÇχӡȥęŘ˅"]],"encodeOffsets":[[[38766,-48070]],[[32563,-24465],[27847,-30659]]]},"properties":{"name":"South Africa","childNum":2}},{"geometry":{"type":"Polygon","coordinates":["@@ĐŽĐđ[ûuëǾǓRį`ĞƋ̳ʣòʱģȷ[Mįàſ©ΟôǏƠҋ̓Oѯʟ̫ńĉƸˏƖÌ®Ó᠁ࠑƐշޅ­ӗʓŷҙ̩۟މࢿȭéϑĦ˳إƪɍȌɥ¨ϩݧƇঽ࡚ǵ֢A౞@˒@˒@ڠȘ@൒@ĶZåǄH²pĜØފcܔͲɑXʋފǤ¼Ή֪ɡڢÙˮ͖Βխ۶ʓŜˋƮõʆϋǀ{̒ŲȄǃjંʋÓmǅΟÎӥҌċȶɘࡢܸǟϢЀτzʦ༔ʖâǇ¾õÜÝĞ»ɰJĊð¹lûİēȶFÝlۂɥƎíÜǷŸf"],"encodeOffsets":[[33944,-9834]]},"properties":{"name":"Zambia","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@ǵḀ̄TɁ}ɩʍĜȩʇYA¥ßàǍ¾aȄࡗȲ́ЬTҼͯrđ͆ࡓӀȕԌבࠂ`ƨئƩ˴ϒĥȮêފࣀ̪۠ŸҚӘʔކ®̑ڢyעͽࡐʃÑƝĮఏʗӥŴƍÓʇǸԃп֥śЏÜƉࡁࡃšđ"],"encodeOffsets":[[32039,-22939]]},"properties":{"name":"Zimbabwe","childNum":1}},{"geometry":{"type":"Polygon","coordinates":["@@ڽը¿ުەߖ٬֖NΦxHޠцǒŴ\\ǔéº`Ęfú[ǜ¹~}¢ũYĨĀcĖÌ£¢áfÁnÛ½˩ƄÇÞŻ~eÖBŒvİ¤RXĩÌĄ§a÷Îŕ÷ĸȣ¡ıǽûsmQ}«»ď§řjʅõōDđ³Û«ŷ³அɘŅĭāuƕɳ`«Ī¢§NÙéõʠ{ɩɥƤǵšɇ"],"encodeOffsets":[[75785,34052]]},"properties":{"name":"","childNum":1}},{"geometry":{"type":"MultiPolygon","coordinates":[["@@͙̄͟ƹիԠύfʛϋӻýģлʫ@˭ƪǕIđmɜ\\cȝ`¥CƴˁȖƈʊƣǶɪɦʟ|öÜêO¡¨ĩ_¬ƖɴĂxĮņஆɗŸ´Ü¬Ĕ´ŌCʆöŚiĐ¨®¼P~tnǾü~Ðd¢̀ǊŰÜŀÄĒ¬Ȳ_́êǁƘ˹ҎǹÒĽȱǷ´ǡæͅìíƐĵn­ĵ§rчɃŝƑŢa¸ȵw"]],"encodeOffsets":[[[80376,33312]]]},"properties":{"name":"","childNum":1}}],"UTF8Encoding":true});}));