const analytics = {
  tab1: 'Affected vehicles',
  tab2: 'Alerts',
  totalAssets: 'Affected Vehicles',
  totalAlerts: 'Alerts',
  alertLevel: 'Alert Severity',
  alertStatus: 'Alert Status',
  affectedModel: 'Affected Automakers',
  safeEventTop5: 'Top 5 Detectors',
  assetLocation: 'Alerts By Location',
  totalModels: 'Automakers',
  affectedAssets: 'Total Unique Affected Assets',
  alertType: 'Total Unique Alert Types',
  alertLevelRadio: 'Total Alert Count By Severity',
  alertTrend: 'Alerts By Severity Over Time',
  alertRecord: 'Alert Record',
  importAssets: 'Impact Assets',
  drawer: {
    detectionRange: 'Detection Range',
    engineType: 'Engine type',
    vehicleYear: 'Vehicle Year',
    assetId: 'Asset ID',
    alertId: 'Alert ID',
    alertType: 'Alert Type',
    alertModel: 'Alert Automaker',

    otherYear: 'Other Years',
  },
}

export default analytics
