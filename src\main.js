// eslint-disable-next-line
import { i18n } from '@/plugins/i18n'
import '@/plugins/vue-composition-api'
import './assets/iconfont/iconfont'
import './assets/iconfont/iconfont.css'
// eslint-disable-next-line
import '@/styles/styles.scss'
// eslint-disable-next-line
import '@/plugins/systemColor'

// import '@core/@fake-db/db'
import Vue from 'vue'

import '@/assets/echarts-theme/dark'
import '@/assets/echarts-theme/light'
import * as echarts from 'echarts'

// import { MessageBox } from 'element-ui'
// import baiduMap from 'vue-baidu-map'
import VueClipBoard from 'vue-clipboard2'
import VueSweetalert2 from 'vue-sweetalert2'
import App from './App.vue'
// eslint-disable-next-line
import '@/plugins/element'
// eslint-disable-next-line
import vuetify from '@/plugins/vuetify'
import '@mdi/font/css/materialdesignicons.css'
import loading from './components/page-loading'
import router from './router'
import store from './store'

// 通知
import VsocIcon from '@/components/VsocIcon'
import Notify from './components/notify'
import * as directives from './util/directive'
import * as filters from './util/filters'
// 将自动注册所有组件为全局组件
import dataV from '@jiaminghi/data-view'
// import BaiduMapComponent from './components/BaiduMap.vue'
// 引入样式
import VueEasytable from 'vue-easytable'
import 'vue-easytable/libs/theme-default/index.css'
import { setDate } from './util/timezone'
Vue.use(VueEasytable)
Vue.use(loading)
Vue.use(dataV)
setDate()
// 全局引入echarts
// 使用VueClipBoard插件，主要功能：类似于剪切板
Vue.use(VueClipBoard)
Vue.config.productionTip = false

// 注册bus
Vue.prototype.$bus = new Vue()

// 注册全局过滤器
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key]) // 插入过滤器名和对应方法
})

// 注册全局指令
Object.keys(directives).forEach(key => {
  Vue.directive(key, directives[key]) // 插入指令名和对应方法
})

// 需要挂载到Vue原型上
Vue.prototype.$echarts = echarts

Vue.prototype.$notify = Notify

// 文本域行数
Vue.prototype.$AREA_ROWS = 6

// Vue.prototype.$confirm = MessageBox

Vue.component('vsoc-icon', VsocIcon)
// 引入提示框
Vue.use(VueSweetalert2)
Vue.config.productionTip = false // 关闭开发模式提示

new Vue({
  router,
  store,
  i18n,
  vuetify,
  render: h => h(App),
}).$mount('#app')
