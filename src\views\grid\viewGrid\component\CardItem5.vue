<template>
  <!-- 条形图1 -->
  <div class="box">
    <div class="box-header">
      {{ title }}
    </div>

    <div class="tab-content" style="margin-top: 1%">
      <div class="d-flex flex-column h-full">
        <template v-if="list1.length !== 0">
          <div
            v-for="(item, index) in list1"
            :key="index"
            class="d-flex align-center justify-space-between"
            style="height: 25%"
          >
            <div class="d-flex align-center">
              <div
                class="d-flex align-center"
                v-if="vehicleStatus[item.healthStatus]"
                :style="{ color: `${vehicleStatus[item.healthStatus].color}` }"
              >
                <vsoc-icon
                  size="1.2rem"
                  type="fill"
                  icon="icon-dunpai"
                ></vsoc-icon>
                <!-- <span class="ml-1 fs-14 card-item5-text">{{
                  vehicleStatus[item.healthStatus].text1
                }}</span> -->
              </div>
              <span class="right-chart1-text1 card-item5-text1">
                {{ item.vehicleId }}</span
              >
            </div>
            <template v-if="item.totalNum > 0">
              <v-tooltip bottom content-class="tooltip-box">
                <template v-slot:activator="{ on, attrs }">
                  <div
                    class="w-100 d-flex align-center overflow-hidden"
                    style="border-radius: 10px"
                    v-bind="attrs"
                    v-on="on"
                  >
                    <v-progress-linear
                      v-for="(v, x) in item.alertList"
                      :key="x"
                      :value="100"
                      :color="v.color"
                      :style="{
                        width: v.percent + '%',
                      }"
                      class="progress-linear1"
                      background-color="transparent"
                    >
                      <span
                        class="right-chart1-value right-chart2-value text-center"
                        >{{ v.value }}</span
                      >
                    </v-progress-linear>
                  </div>
                </template>
                <div class="fs-14-1">
                  <div>{{ item.vehicleId }}</div>
                  <template v-for="(v, x) in item.alertList">
                    <div
                      v-if="v.value > 0"
                      class="d-flex align-center mt-1"
                      :key="x"
                    >
                      <span
                        class="tooltip-dot"
                        :style="{ background: v.color }"
                      ></span>
                      <span>{{ v.value }}</span>
                    </div>
                  </template>
                </div>
              </v-tooltip>
            </template>
            <template v-else>
              <v-progress-linear
                class="progress-linear1"
                :value="100"
                color="#8F9AB2"
                background-color="transparent"
                style="border-radius: 10px"
              >
                <span class="right-chart1-value text-center">0</span>
              </v-progress-linear>
            </template>
          </div>
        </template>
        <template v-else>
          <div
            class="fs-16 color--primary h-100 d-flex justify-center align-center"
          >
            None
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import { getRoundSize } from './chart'
export default {
  name: 'CardItem5',
  props: {
    title: {
      type: String,
      default: '',
    },
    list: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  data() {
    return {
      list1: [],
    }
  },
  computed: {
    alarmLevel() {
      return this.$store.state.enums.enums.AlarmLevel
    },
    vehicleStatus() {
      return this.$store.state.enums.enums.HealthStatus
    },
  },
  watch: {
    list: {
      handler() {
        this.getMetrics()
      },
      deep: true,
    },
  },
  created() {
    this.getMetrics()
  },
  methods: {
    getRoundSize,
    getMetrics() {
      this.list1 = []
      this.list.forEach(v => {
        if (v.metrics) {
          v.totalNum = v.pendingCount
          if (v.totalNum > 0) {
            v.alertList = [
              {
                percent: v.metrics[0]
                  ? ((v.metrics[0] / v.totalNum) * 100).toFixed(4)
                  : 0,
                value: v.metrics[0] || 0,
                color: this.alarmLevel?.[0].color,
              },
              {
                percent: v.metrics[1]
                  ? ((v.metrics[1] / v.totalNum) * 100).toFixed(4)
                  : 0,
                value: v.metrics[1] || 0,
                color: this.alarmLevel?.[1].color,
              },
              {
                percent: v.metrics[2]
                  ? ((v.metrics[2] / v.totalNum) * 100).toFixed(4)
                  : 0,
                value: v.metrics[2] || 0,
                color: this.alarmLevel?.[2].color,
              },
              {
                percent: v.metrics[3]
                  ? ((v.metrics[3] / v.totalNum) * 100).toFixed(4)
                  : 0,
                value: v.metrics[3] || 0,
                color: this.alarmLevel?.[3].color,
              },
              {
                percent: v.metrics[4]
                  ? ((v.metrics[4] / v.totalNum) * 100).toFixed(4)
                  : 0,
                value: v.metrics[4] || 0,
                color: this.alarmLevel?.[4].color,
              },
              {
                percent: v.metrics[5]
                  ? ((v.metrics[5] / v.totalNum) * 100).toFixed(4)
                  : 0,
                value: v.metrics[5] || 0,
                color: this.alarmLevel?.[5].color,
              },
            ]
          }
        }
        this.list1.push(v)
      })
      // this.list1[0].healthStatus = '0'
      // this.list1[1].healthStatus = '1'
      // this.list1[2].healthStatus = '2'
      // this.list1[3].healthStatus = '2'
      // this.list1[4].healthStatus = '3'
      // console.log(
      //   this.list1,
      //   this.$store.state.enums.enums.AlarmLevel,
      //   this.$store.state.enums.enums.HealthStatus,
      //   11,
      // )
    },
  },
}
</script>
<style lang="scss">
.tooltip-box {
  background: rgba(255, 255, 255, 0.1) !important;
  box-shadow: rgba(0, 0, 0, 0.2) 1px 2px 10px;

  // transition: opacity 0.2s cubic-bezier(0.23, 1, 0.32, 1),
  //   visibility 0.2s cubic-bezier(0.23, 1, 0.32, 1),
  //   transform 0.4s cubic-bezier(0.23, 1, 0.32, 1);

  border-radius: 4px;
  color: rgb(255, 255, 255);
  font: 6px / 10px 'Microsoft YaHei';
  padding: 10px;
  // transform: translate3d(67px, -37px, 0px);
  backdrop-filter: blur(6px);
  pointer-events: none;
}
.tooltip-dot {
  display: inline-block;
  margin-right: 8px;
  border-radius: 10px;
  width: 10px;
  height: 10px;
}
</style>
