const cloud = {
  currentTitle: '云端',
  headers: {
    itemKey: '配置项',
    itemName: '统计指标',
    itemEnName: '统计指标英文名称',
    staticValue: '静态指标数据',
    method: '调用方式',
    readMethod: '数据源',
    chart: '图表',
    initialWidth: '初始宽度',
    initialHeight: '初始高度',
    minWidth: '最小宽度',
    minHeight: '最小高度',
    status: '是否有效',
    border: '是否有边框',
  },
  static: {
    added: '已添加项目',
    handling: '待添加项目',
    hint: '回车创建标签：',
  },
  swal: {
    title: '同步更新',
    title1: '同步',
    title2: '不同步',
    text: '该统计指标当前被用于{0}个画布实例中。您是否希望同步更新这些画布实例的数据？',
    hint: '同步成功',
  },
  tip: '请输入1-24的整数',
  exampleHint: '点击下载样例数据',
  empty: '内容为空，已取消下载！',
}

export default cloud
