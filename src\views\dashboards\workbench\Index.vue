<template>
  <div
    class="pa-4 h-full overflow-auto scroll-bar-bg"
    style="max-height: calc(100vh - 50px)"
  >
    <v-row>
      <v-col cols="12" md="9">
        <v-skeleton-loader height="15rem" :loading="isLoading" type="image">
          <v-card class="pa-6 pb-8 pr-0">
            <v-card-title class="text-2xl font-weight-medium pa-0">
              {{ $t('workbench.hint', { userName: userInfo.userName }) }}
            </v-card-title>
            <v-card-text
              class="d-flex align-center justify-space-between pa-0 pt-5"
            >
              <div
                v-for="(item, index) in countList"
                :key="index"
                class="w-25 d-flex flex-row cursor-pointer"
                @click="goIndex(item)"
              >
                <div class="w-100">
                  <div class="text-ml">{{ item.text }}</div>
                  <div class="pt-2 pb-4">
                    <!-- <span class="text-4xl color-base font-weight-medium pr-2">{{
                      item.value
                    }}</span> -->
                    <count-to
                      class="text-4xl color-base font-weight-medium pr-2"
                      :startVal="0"
                      :endVal="item.value"
                      :duration="3600"
                    ></count-to>
                    <!-- <span class="text-ml vertical-align-top">{{
                      $t('workbench.unit')
                    }}</span> -->
                  </div>
                  <v-progress-linear
                    :color="item.color"
                    value="50"
                    rounded
                    background-color="bg-body"
                  ></v-progress-linear>
                </div>
                <!-- :class="{ 'opacity-0': index === 3 }" -->
                <v-divider
                  class="my-3 mx-10"
                  :class="{ 'opacity-0': index === 3 }"
                  vertical
                ></v-divider>
              </div>
            </v-card-text>
          </v-card>
        </v-skeleton-loader>
        <v-skeleton-loader
          height="22.5rem"
          class="mt-3"
          :loading="isLoading"
          type="image"
        >
          <data-collection
            style="height: 22.5rem"
            :collectionData="collectionData"
            @refresh="loadDataCollection"
          ></data-collection>
        </v-skeleton-loader>
      </v-col>

      <v-col cols="6" md="3">
        <v-skeleton-loader :loading="isLoading" type="image" class="h-full">
          <quick-entry :quickData="quickData"></quick-entry>
        </v-skeleton-loader>
      </v-col>
    </v-row>
    <v-row>
      <v-col cols="5" md="5" style="height: 26.1667rem">
        <v-skeleton-loader :loading="isLoading" type="image" class="h-full">
          <ring-chart
            :title="$t('workbench.alert.status')"
            echartId="alertStatus"
            :list="alertStatusData"
            @refresh="loadAlarmStatus"
          >
          </ring-chart>
        </v-skeleton-loader>
      </v-col>
      <v-col cols="7" md="7">
        <v-skeleton-loader :loading="isLoading" type="image" class="h-full">
          <stack-chart
            :title="$t('workbench.alert.trend')"
            echartId="alertTrend"
            :list="alertTrendData"
            @refresh="loadAlarmTrend"
          ></stack-chart>
        </v-skeleton-loader>
      </v-col>
    </v-row>
    <v-row>
      <v-col cols="5" md="5" style="height: 26.1667rem">
        <v-skeleton-loader :loading="isLoading" type="image" class="h-full">
          <ring-chart
            :title="$t('workbench.ticket.status')"
            :list="orderStatusData"
            echartId="orderStatus"
            @refresh="loadTicketStatus"
          ></ring-chart>
        </v-skeleton-loader>
      </v-col>
      <v-col cols="7" md="7">
        <v-skeleton-loader :loading="isLoading" type="image" class="h-full">
          <stack-chart
            :title="$t('workbench.ticket.trend')"
            echartId="orderTrend"
            :list="ticketTrendData"
            type="trend"
            @refresh="loadTicketTrend"
          ></stack-chart>
        </v-skeleton-loader>
      </v-col>
    </v-row>
  </div>
</template>

<script>
import { getMyMessagesList } from '@/api/message'
import {
  getAlarmAndTick,
  getAlarmStatus,
  getAlarmTrend,
  getDataProcessing,
  getTicketStatus,
  getTicketTrend,
} from '@/api/workbench/index'
import store from '@/store'
import { getLocalStorage } from '@/util/localStorage'
import { format } from 'date-fns'
import countTo from 'vue-count-to'
import DataCollection from './components/DataCollection.vue'
import QuickEntry from './components/QuickEntry.vue'
import RingChart from './components/RingChart.vue'
import StackChart from './components/StackChart.vue'
import { defaultHoursQueryFn, defaultQueryFn } from './components/constant'
export default {
  name: 'Workbench',
  components: {
    DataCollection,
    QuickEntry,
    RingChart,
    StackChart,
    countTo,
  },
  data() {
    return {
      timer2: null,
      isLoading: false,
      userInfo: {},
      countObj: {},
      query: defaultQueryFn(),
      collectionData: {},
      quickData: {
        myMessages: [],
      },
      alertStatusData: [],
      orderStatusData: [],
      alertTrendData: [],
      ticketTrendData: [],
    }
  },
  computed: {
    ticketStatusList() {
      return Object.assign([], this.$store.state.enums.enums.TicketStatus)
    },
    alertStatusList() {
      return this.$store.getters['enums/getAlertStatus']
    },
    countList() {
      this.userInfo = JSON.parse(getLocalStorage('userInfo'))

      return [
        {
          text: this.$t('workbench.myTicket'),
          value: this.countObj.selectTicket,
          color: '#0EAA9F',
          path: '/ticket/lists',
          query: {
            isQuery: 1,
            assignedGroups: JSON.stringify(
              this.userInfo.departments.map(v => v.id),
            ),
            assignedTos: JSON.stringify([this.userInfo.userId]),
            statuses: JSON.stringify(
              this.ticketStatusList
                .filter(x => !['2', '4', '5'].includes(x.value))
                .map(y => y.value),
            ),
          },
        },
        {
          text: this.$t('workbench.myAlert'),
          value: this.countObj.selectAlarm,
          color: '#DA1F1F',
          path: '/alerts',
          query: {
            isQuery: 1,
            statusList: JSON.stringify(
              this.alertStatusList
                .filter(x => ['0', '1'].includes(x.value))
                .map(y => y.value),
            ),
            updateUser: this.userInfo.userId,
          },
        },
        {
          text: this.$t('workbench.assignTicket'),
          value: this.countObj.selectTicketIsNull,
          color: '#0082D6',
          path: '/ticket/lists',
          query: {
            isQuery: 1,
            statuses: JSON.stringify(['0']),
          },
        },
        {
          text: this.$t('workbench.assignAlert'),
          value: this.countObj.selectAlarmIsNull,
          color:
            store.state.appConfig.appPrimary === 'lotus'
              ? '#FFF200'
              : '#FFBF1C',
          path: '/alerts',
          query: {
            isQuery: 1,
            statusList: JSON.stringify(['0']),
          },
        },
      ]
    },
  },
  watch: {
    '$store.state.global.messageNum': {
      handler(val) {
        this.loadAlarmAndTick()
      },
      deep: true,
    },
  },
  mounted() {
    this.loadData(true)
    let time =
      this.$store.getters['enums/getPollingInterval'](
        this.$generateMenuTitle(this.$route.meta),
      ) || 15
    this.timer2 = setInterval(this.loadData, time * 1000)
  },
  beforeDestroy() {
    clearInterval(this.timer2)
  },
  methods: {
    loadData(mode = false) {
      this.isLoading = mode
      let fn = mode => {
        return mode
          ? [
              this.loadDataCollection(),
              this.loadAlarmStatus(),
              this.loadTicketStatus(),
              this.loadAlarmTrend(),
              this.loadTicketTrend(),
            ]
          : []
      }
      Promise.all([this.loadAlarmAndTick(), this.loadEntrance(), ...fn(mode)])
        .catch(err => {
          console.log(err)
        })
        .finally(() => {
          // console.log('轮询开始了', format(new Date(), 'yyyy-MM-dd HH:mm:ss'))
          this.isLoading = false
        })
    },
    goIndex({ path, query }) {
      this.$router.push({ path, query })
    },
    async loadAlarmAndTick() {
      const { data } = await getAlarmAndTick()
      this.countObj = data
    },
    async loadDataCollection(obj = {}) {
      const params = { ...this.query, ...obj }
      const { data } = await getDataProcessing(params)
      this.collectionData = {
        xData: [],
        yData: [],
        total: 0,
        updateDate: '',
      }
      data.data.forEach(v => {
        this.collectionData.total += Number(v.number)
        const formatDate = format(new Date(v.time), 'MM/dd')
        this.collectionData.xData.push(formatDate)
        this.collectionData.yData.push(v.number)
      })
      this.collectionData.updateDate = data.updateDate
    },
    async loadEntrance() {
      const params = {
        page: 1,
        pageSize: 5,
      }
      const { data } = await getMyMessagesList(params)
      this.quickData.myMessages = data.records
    },
    async loadAlarmStatus(obj = {}) {
      const params = { ...this.query, ...obj }
      const { data } = await getAlarmStatus(params)
      this.alertStatusData = data.map((item, index) => {
        return {
          name: item.statusName,
          format: item.format,
          value: item.count,
          color: '',
        }
      })
    },
    async loadTicketStatus(obj = {}) {
      const params = { ...this.query, ...obj }
      const { data } = await getTicketStatus(params)
      this.orderStatusData = data.map((item, index) => {
        return {
          name: item.statusName,
          format: item.format,
          value: item.count,
          color: '',
        }
      })
    },
    async loadAlarmTrend(obj = {}) {
      const params = { ...this.query, ...obj }
      const { data } = await getAlarmTrend(params)
      this.alertTrendData = data
    },
    async loadTicketTrend(obj = {}) {
      const params = { ...this.query, ...obj }
      const { data } = await getTicketTrend(params)
      this.ticketTrendData = data
    },
  },
}
</script>
<style scoped lang="scss">
.vertical-align-top {
  vertical-align: top;
}
.v-card {
  height: 100%;
}
.text-4xl {
  font-size: 2.6667rem;
  line-height: 2.6667rem;
}

.col-xl,
.col-xl-auto,
.col-xl-12,
.col-xl-11,
.col-xl-10,
.col-xl-9,
.col-xl-8,
.col-xl-7,
.col-xl-6,
.col-xl-5,
.col-xl-4,
.col-xl-3,
.col-xl-2,
.col-xl-1,
.col-lg,
.col-lg-auto,
.col-lg-12,
.col-lg-11,
.col-lg-10,
.col-lg-9,
.col-lg-8,
.col-lg-7,
.col-lg-6,
.col-lg-5,
.col-lg-4,
.col-lg-3,
.col-lg-2,
.col-lg-1,
.col-md,
.col-md-auto,
.col-md-12,
.col-md-11,
.col-md-10,
.col-md-9,
.col-md-8,
.col-md-7,
.col-md-6,
.col-md-5,
.col-md-4,
.col-md-3,
.col-md-2,
.col-md-1,
.col-sm,
.col-sm-auto,
.col-sm-12,
.col-sm-11,
.col-sm-10,
.col-sm-9,
.col-sm-8,
.col-sm-7,
.col-sm-6,
.col-sm-5,
.col-sm-4,
.col-sm-3,
.col-sm-2,
.col-sm-1,
.col,
.col-auto,
.col-12,
.col-11,
.col-10,
.col-9,
.col-8,
.col-7,
.col-6,
.col-5,
.col-4,
.col-3,
.col-2,
.col-1 {
  padding: 6px;
}
</style>
