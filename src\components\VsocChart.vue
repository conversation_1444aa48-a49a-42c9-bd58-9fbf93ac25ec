<template>
  <div :id="echartId" class="w-100 h-100" v-resize="onResize"></div>
  <!-- <div class="h-100 w-100" v-resize="onResize">
    <div v-show="!isEmpty" :id="echartId" class="w-100 h-100"></div>
    <chart-empty
      class="h-100 w-100 chart-empty"
      v-if="isEmpty && !isFirstLoading"
    ></chart-empty>
  </div> -->
</template>

<script>
import { emptyChartOptionFn } from '@/util/utils'
const splitLine = {
  show: true,
  lineStyle: {
    // 实线
    // type: 'solid',
    // width: 0.5,
    // opacity: 0.8,

    // 虚线
    type: [5, 5],
    dashOffset: 0,
    shadowBlur: 0,
  },
}

// 默认配置项
const defaultOption = {
  // 图例显示在底部
  legend: {
    bottom: 0,
  },

  // 画布布局
  grid: {
    top: '5%',
    left: '2%',
    right: '3%',
    bottom: '10%',
    containLabel: true,
  },
  backgroundColor: 'transparent',
  textStyle: {
    fontSize: '1rem',
  },
}
export default {
  props: {
    // 高度
    height: {
      type: [String, Number],
      require: false,
      default: () => 'auto',
    },

    // 宽度
    width: {
      type: String,
      default: () => 'auto',
    },
    option: {
      type: Object,
      default: () => defaultOption,
    },

    // x坐标轴虚线
    isXDashed: {
      type: Boolean,
      default: () => false,
    },

    // y坐标轴虚线
    isYDashed: {
      type: Boolean,
      default: () => false,
    },
    echartId: {
      type: String,
      require: true,
      default: () => 'echart',
    },
    isShowEmpty: {
      type: Boolean,
      default: () => true,
    },
  },
  data() {
    return {
      isFirstLoading: true,
      myChart: undefined,
      resizeObserver: '',
    }
  },
  // computed: {
  //   isEmpty() {
  //     if (!this.option.series || this.option.series.length === 0) {
  //       return false
  //     }
  //     let len = this.option.series?.length
  //     return (
  //       this.option.series.map(v => v.data)?.filter(y => y.length === 0)
  //         ?.length === len
  //     )
  //   },
  // },
  watch: {
    '$vuetify.theme.dark': {
      handler() {
        this.$nextTick(() => {
          this.myChart && this.myChart.dispose()
          this.onDraw()
        })
      },
      deep: true,
    },
    'option.series': {
      handler(newVal) {
        this.myChart && this.myChart.clear()
        this.myChart && this.setOption()
      },
      deep: true,
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.onDraw()
      this.isFirstLoading = false
      // this.onResizeObserver()
    })
  },
  // beforeDestroy() {
  //   const ele = document.getElementById(this.echartId)
  //   if (ele) {
  //     this.resizeObserver.unobserve(ele)
  //   }
  // },
  methods: {
    onDraw() {
      const ele = document.getElementById(this.echartId)
      if (!ele) {
        return
      }
      this.myChart = this.$echarts.init(
        ele,
        this.$vuetify.theme.dark ? 'dark' : 'light',
        {
          height: this.height,
          width: this.width,
        },
      )
      this.myChart.on('finished', event => {
        this.$emit('finished', this.myChart, event)
        this.isFirstLoading = false
      })
      this.setOption()
      this.myChart.on('mouseover', event => {
        this.$emit('highlight', event, this.myChart)
      })
      this.myChart.on('mouseout', event => {
        this.$emit('mouseout', event, this.myChart)
      })
      this.myChart.on('legendselectchanged ', event => {
        this.$emit('select', event, this.myChart)
      })
      this.myChart.on('click', event => {
        this.$emit('click', event, this.myChart)
      })

      // 默认第一条高亮
      // this.myChart.dispatchAction({ type: 'highlight', seriesIndex: 0, dataIndex: 0 })

      // this.myChart.dispatchAction({ type: 'select', seriesIndex: 0, dataIndex: 0 })
    },
    setOption() {
      if (this.isXDashed) {
        // 坐标轴存在两种：多坐标，单坐标
        this.option.xAxis = Array.isArray(this.option.xAxis)
          ? this.option.xAxis.map(v => Object.assign(v, { splitLine }))
          : Object.assign(this.option.xAxis, { splitLine })
      }
      if (this.isYDashed) {
        this.option.yAxis = Array.isArray(this.option.yAxis)
          ? this.option.yAxis.map(v => Object.assign(v, { splitLine }))
          : Object.assign(this.option.yAxis, { splitLine })
      }
      let option = { ...defaultOption, ...this.option }
      // 第二种数据为空处理方法
      let len = option.series.length
      const isEmpty =
        option.series.map(v => v.data).filter(y => y.length === 0).length ===
        len
      if (isEmpty && this.isShowEmpty) {
        option = emptyChartOptionFn()
      }
      // this.isFirstLoading = false
      this.myChart.clear()
      this.myChart.setOption(option, { notMerge: true })
    },
    onResize() {
      if (!this.myChart) {
        return
      }

      this.myChart.resize({
        animation: {
          duration: 1000,
          // easing: 'cubicInOut',
          // delay: 500,
        },
      })
    },
    onResizeObserver() {
      const ele = document.getElementById(this.echartId)
      if (!ele) {
        return
      }
      const _this = this
      this.resizeObserver = new ResizeObserver(entries => {
        _this.onResize()
      })

      this.resizeObserver.observe(ele)
    },
  },
}
</script>
