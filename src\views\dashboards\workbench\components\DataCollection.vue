<template>
  <common
    type="DataCollection"
    :title="$t('workbench.dataCollection.title')"
    v-on="$listeners"
  >
    <template #subTitle>
      {{ collectionData.total | numberToFormat }}
      <span class="ml-4 text-base">
        {{ $t('global.updateDiff', [collectionData.updateDate]) }}
      </span>
    </template>
    <template #chart>
      <vsoc-chart
        ref="areaLine"
        echart-id="area-line"
        :option="gradientChart"
        :is-y-dashed="true"
      ></vsoc-chart>
    </template>
  </common>
</template>

<script>
import VsocChart from '@/components/VsocChart'
import Common from './Common.vue'
import { grid, textStyle, tooltip } from './constant'
export default {
  props: {
    collectionData: {
      type: Object,
      default() {
        return {
          xData: [],
          yData: [],
          total: 0,
          updateDate: '',
        }
      },
    },
  },
  components: {
    VsocChart,
    Common,
  },

  computed: {
    gradientChart() {
      return {
        textStyle,
        series: [
          {
            name: this.$t('workbench.dataCollection.hint'),
            data: this.collectionData.yData,
            type: 'line',
            showSymbol: false,
            smooth: true,
            symbol: 'none',
            emphasis: { focus: 'series' },
            areaStyle: {
              opacity: 0.25,
              // linear-gradient(89.57deg, #0082D6 0.29%, #0EAA9F 66.66%)
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0.29 / 100,
                    color: '#0082D6', // 蓝色
                  },
                  {
                    offset: 100 / 100,
                    color: 'rgba(0, 130, 214, 0)',
                  },
                ],
                global: false, // 缺省为 false
              },
            },
            animationDuration: 2500,
            animationEasing: 'cubicInOut',
          },
        ],
        tooltip,
        legend: { show: false },
        grid,
        // toXDataYMD(
        //       this.collectionData.xData,
        //       this.currentDate === 24 ? 'HH:mm' : 'MM/dd',
        //     ),
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            data: this.collectionData.xData,
            axisLine: { show: false },
            axisTick: { show: false },
          },
        ],
        yAxis: [
          {
            type: 'value',
          },
        ],
      }
    },
  },
}
</script>
<style scoped lang="scss"></style>
