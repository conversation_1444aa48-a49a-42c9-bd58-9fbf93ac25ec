<!-- 处置管理 -->
<template>
  <div>
    <bread-crumb
      ref="topHeader"
      :showDoQuery="showDoQuery"
      :filterList="filterList"
      :showAdvance="true"
    >
    </bread-crumb>

    <v-card tile class="pa-0" elevation="0">
      <v-card-text class="main-content">
        <div class="mb-3 d-flex justify-space-between">
          <div class="d-flex justify-end align-center">
            <v-btn elevation="0" color="primary" v-has:action-new @click="add">
              <span> {{ $t('action.add') }} </span>
            </v-btn>
          </div>
          <div class="d-flex align-center">
            <table-search
              :searchList="searchList"
              :searchQuery="query"
              @search="$_search"
            ></table-search>
            <!-- <v-text-field
              v-model="query.name"
              outlined
              dense
              hide-details
              color="primary"
              :label="$t('alertAction.headers.name')"
              large
              clearable
              @click:clear="onClear"
              @keyup.enter.native="$_search"
              class="me-3 text-width"
            ></v-text-field>
            <v-btn class="primary--text bg-btn" elevation="0" @click="$_search">
              <span>{{ $t('action.search') }}</span>
            </v-btn> -->
          </div>
        </div>

        <v-data-table
          fixed-header
          show-expand
          :items-per-page="query.pageSize"
          :height="tableHeight"
          hide-default-footer
          :headers="headers"
          :items="tableData"
          :loading="tableLoading"
          class="flex-1 thead-light"
          @click:row="$_clickRow"
        >
          <template v-slot:item.severityList="{ item }">
            <div
              v-if="item.severityList && item.severityList.length"
              class="d-flex align-center"
            >
              <vsoc-icon
                v-for="v in item.severityList"
                :key="v"
                type="fill"
                icon="icon-dunpai"
                class="mr-1"
                :style="{
                  color: alertLevel[Number(v)]
                    ? alertLevel[Number(v)].color
                    : '',
                }"
                v-show-tips="alertLevel[Number(v)].text"
              ></vsoc-icon>
            </div>
            <div v-else>N/A</div>
          </template>

          <template v-slot:item.name="{ item }">
            <span v-show-tips>{{ item.name }}</span>
          </template>

          <template v-slot:item.description="{ item }">
            <div v-show-tips style="width: 300px">
              {{ item.description | dataFilter }}
            </div>
          </template>
          <template v-slot:item.version="{ item }">
            <span>{{ item.version | dataFilter }}</span>
          </template>
          <!-- disposeType 处置类型: 0：工单集成；1：邮件通知；2：系统消息；3：kafka集成 -->
          <template v-slot:item.disposeType="{ item }">
            <div v-if="disposeTypeListEnums[item.disposeType]">
              {{ disposeTypeListEnums[item.disposeType].text }}
            </div>
            <div v-else>N/A</div>
          </template>

          <template v-slot:item.active="{ item }">
            <div v-if="responseStatus[item.active]">
              <v-badge
                dot
                inline
                offset-x="10"
                :offset-y="-18"
                :color="
                  responseStatusList[item.active]
                    ? responseStatusList[item.active].color
                    : ''
                "
                class="mr-1"
              ></v-badge>
              <span>{{ responseStatus[item.active].text }}</span>
            </div>

            <span v-else>N/A</span>
          </template>
          <template v-slot:item.updateUser="{ item }">
            <span>{{ item.updateUser | dataFilter }}</span>
          </template>
          <template v-slot:item.updateDate="{ item }">
            <span>{{ item.updateDate | toDate | dataFilter }}</span>
          </template>
          <template v-slot:item.actions="{ item }">
            <v-btn icon @click="onInfo(item)" v-has:action-detail>
              <vsoc-icon
                v-show-tips="$t('action.detail')"
                type="fill"
                icon="icon-xiangqing"
                class="action-btn"
                size="x-large"
              ></vsoc-icon>
            </v-btn>
            <v-btn
              v-if="item.active === '0'"
              icon
              @click.stop="handleStatus(item)"
              v-has:action-disable
            >
              <vsoc-icon
                v-show-tips="responseStatus['1'].text"
                type="fill"
                :icon="'icon-tingyong'"
                class="action-btn"
                size="x-large"
              ></vsoc-icon>
            </v-btn>

            <v-btn
              v-if="item.active === '1'"
              icon
              @click.stop="handleStatus(item)"
              v-has:action-enable
            >
              <vsoc-icon
                v-show-tips="responseStatus['0'].text"
                type="fill"
                :icon="'icon-qiyong'"
                class="action-btn"
                size="x-large"
              ></vsoc-icon>
            </v-btn>

            <v-btn icon v-has:action-edit @click.stop="$_edit(item)">
              <vsoc-icon
                v-show-tips="$t('action.edit')"
                type="fill"
                icon="icon-bianji"
                class="action-btn"
                size="x-large"
              ></vsoc-icon>
            </v-btn>
            <v-btn v-has:action-del icon @click.stop="del(item)">
              <vsoc-icon
                v-show-tips="$t('action.del')"
                type="fill"
                icon="icon-shanchu"
                class="action-btn"
                size="x-large"
              ></vsoc-icon>
            </v-btn>
          </template>

          <template v-slot:expanded-item="{ headers, item }">
            <td :colspan="headers.length + 1" class="pa-8">
              <div class="d-flex">
                <div class="w-15 mr-4">
                  <div class="pb-2 text--secondary">
                    {{ $t('alertAction.headers.name') }}
                  </div>
                  <div v-show-tips>
                    {{ item.name }}
                  </div>
                </div>
                <div class="w-30 mr-4">
                  <div class="pb-2 text--secondary">
                    {{ $t('alertAction.headers.warnClassifyTypeName') }}
                  </div>
                  <div v-show-tips>
                    <span>{{ item.warnClassifyTypeName }}</span>
                    <span
                      v-if="
                        item.warnClassifyNameList &&
                        item.warnClassifyNameList.length
                      "
                      >：{{ item.warnClassifyNameList.join(',') }}</span
                    >
                  </div>
                </div>
                <div class="w-15 mr-4">
                  <div class="pb-2 text--secondary">
                    {{ $t('alertAction.headers.disposeTypeName') }}
                  </div>
                  <div v-if="disposeTypeListEnums[item.disposeType]">
                    {{ disposeTypeListEnums[item.disposeType].text }}
                  </div>
                  <div v-else>N/A</div>
                </div>
                <!-- 0：工单集成；1：邮件通知；2：系统消息；3：kafka集成 4:钉钉通知-->
                <div
                  v-if="
                    item.disposeType === '1' ||
                    item.disposeType === '2' ||
                    item.disposeType === '4'
                  "
                  class="w-60"
                >
                  <div class="pb-2 text--secondary">
                    {{ $t('alertAction.headers.receiveUser') }}
                  </div>
                  <div
                    v-if="item.receiveUser && item.receiveUser.length"
                    class="d-flex align-center flex-wrap"
                  >
                    <v-chip
                      v-for="v in item.receiveUser"
                      :key="v.value"
                      small
                      label
                      color="primary"
                      class="badge-font-size text-xxs ml-0 mr-2 mb-2"
                    >
                      {{ v.text }}
                    </v-chip>
                  </div>
                  <div v-else>N/A</div>
                </div>

                <div v-else class="w-60"></div>
              </div>
            </td>
          </template>
        </v-data-table>

        <!-- 分页器 -->
        <vsoc-pagination
          :page.sync="query.pageNum"
          :size.sync="query.pageSize"
          :total="tableDataTotal"
          @change-size="$_search"
          @change-page="getTableData"
        >
        </vsoc-pagination>
      </v-card-text>
    </v-card>
    <!-- 高级查询 -->
    <vsoc-drawer
      v-model="showDrawer"
      :title="$t('action.advanced')"
      temporary
      @click:confirm="doQuery"
    >
      <template #right-title>
        <v-btn icon class="no-hb ml-4" text @click="clearAdvanceQuery">
          <v-icon size="16">mdi-filter-variant-remove</v-icon>
        </v-btn>
      </template>
      <v-text-field
        v-model="advanceQuery.name"
        color="primary"
        :label="$t('alertAction.headers.name')"
        class="mt-2"
      ></v-text-field>
      <div
        class="text-base color-base font-weight-semibold-light drawer-2 mb-2"
      >
        {{ $t('alertAction.headers.disposeTypeName') }}
      </div>
      <div class="d-flex flex-wrap justify-space-between">
        <v-checkbox
          v-for="(item, i) in actionTypeEnum"
          :key="item.value"
          v-model="advanceQuery.disposeTypeList"
          color="primary"
          :value="item.value"
          hide-details
          class="mt-0 w-50 pa-0 mb-4"
        >
          <template v-slot:label>
            <span class="text-base color-base">
              {{ item.text }}
            </span>
          </template>
        </v-checkbox>
      </div>
      <div class="text-base color-base font-weight-semibold-light mt-2 mb-2">
        {{ $t('alertAction.headers.severity') }}
      </div>
      <div class="d-flex flex-wrap justify-space-between">
        <v-checkbox
          v-for="(item, i) in alertLevel"
          :key="i"
          v-model="advanceQuery.severityList"
          color="primary"
          :value="item.value"
          hide-details
          class="mt-0 w-50 pa-0 mb-4"
        >
          <template v-slot:label>
            <div class="d-flex align-center text-center">
              <vsoc-icon
                :style="{ color: item.color }"
                type="fill"
                icon="icon-dunpai"
                class="mr-2"
              ></vsoc-icon>
              <!-- <v-icon :style="{ color: item.color }" size="16" class="mr-2">
                mdi-shield
              </v-icon> -->
              <span class="text-base color-base">
                {{ item.text }}
              </span>
            </div>
          </template>
        </v-checkbox>
      </div>
      <div class="text-base color-base font-weight-semibold-light mt-2 mb-2">
        {{ $t('alertAction.headers.active') }}
      </div>
      <v-radio-group
        v-model="advanceQuery.active"
        row
        hide-details
        color="primary"
        class="mt-0 pa-0"
      >
        <v-row class="ma-0">
          <v-col
            v-for="item in responseStatusList"
            :key="item.key"
            class="pa-0"
          >
            <v-radio :value="item.key" class="mr-0">
              <template #label>
                <!-- <v-icon size="16" class="mr-2" :color="item.color">
                  {{ item.icon }}
                </v-icon> -->
                <v-badge
                  dot
                  inline
                  offset-x="10"
                  :offset-y="-18"
                  :color="
                    responseStatusList[item.key]
                      ? responseStatusList[item.key].color
                      : ''
                  "
                  class="mr-2"
                ></v-badge>
                <span class="text-base color-base">{{
                  responseStatus[item.key].text
                }}</span>
              </template>
            </v-radio>
          </v-col>
        </v-row>
      </v-radio-group>
    </vsoc-drawer>
  </div>
</template>

<script>
import { deleteAction, getAction, updateActive } from '@/api/action'
import { querySysGroupList } from '@/api/system/organization'
import { findAllUsers } from '@/api/system/user'
import BreadCrumb from '@/components/bread-crumb/index.vue'
import VsocDrawer from '@/components/VsocDrawer.vue'
import VsocFilter from '@/components/VsocFilter.vue'
import VsocPagination from '@/components/VsocPagination.vue'
import { flatTree } from '@/views/handle/response/tools.js'

import { responseStatusList } from '@/util/enum'
import { dataFilter } from '@/util/filters'

import TableSearch from '@/components/TableSearch/index.vue'
import {
  clearFilterItem,
  deepClone,
  handleFilterItem,
  setRemainingHeight,
} from '@/util/utils'

export default {
  name: 'ActionIndex',
  components: {
    VsocPagination,
    VsocDrawer,
    BreadCrumb,
    VsocFilter,
    TableSearch,
  },
  filters: {
    dataFilter,
  },
  data() {
    return {
      responseStatusList,
      showDrawer: false,
      isEdit: false,

      // 查询内容
      searchKey: '',

      // 分页参数
      query: {
        pageNum: 1,
        pageSize: 10,
        name: '',
        disposeTypeList: [],
        severityList: [],
        active: '',
      },

      tableData: [],
      userInfos: [],
      groupInfos: [],
      tableHeight: '34.5rem',
      tableDataTotal: 0,
      tableLoading: false,
      filterList: [],
      advanceQuery: {
        pageNum: 1,
        pageSize: 10,
        name: '',
        disposeTypeList: [],
        severityList: [],
        active: '',
      },
    }
  },
  computed: {
    searchList() {
      return [
        {
          type: 'input',
          value: 'name',
          text: this.$t('alertAction.headers.name'),
        },
      ]
    },
    headers() {
      return [
        {
          text: this.$t('alertAction.headers.name'),
          value: 'name',
          width: '170px',
        },
        {
          text: this.$t('global.desc'),
          value: 'description',
          width: '300px',
        },
        {
          text: this.$t('alertAction.headers.disposeTypeName'),
          value: 'disposeType',
          width: '140px',
        },
        {
          text: this.$t('alertAction.headers.version'),
          value: 'version',
          width: '120px',
        },
        {
          text: this.$t('alertAction.headers.severityList'),
          value: 'severityList',
          width: '200px',
        },
        {
          text: this.$t('alertAction.headers.active'),
          value: 'active',
          width: '100px',
        },
        {
          text: this.$t('global.updateDate'),
          value: 'updateDate',
          width: '180px',
        },
        {
          text: this.$t('global.updateUser'),
          value: 'updateUser',
          width: '180px',
        },
        {
          text: '',
          value: 'actions',
          sortable: false,
          width: '200px',
        },
      ]
    },
    actionTypeEnum() {
      const allTypes = Object.assign(
        [],
        this.$store.state.enums.enums.DisposeType,
      ).filter(v => v.dictId !== '3')
      return allTypes
      // return this.$store.getters['enums/getDingType'] === 'true'
      //   ? allTypes
      //   : allTypes.filter(v => v.value !== '4')
    },
    disposeTypeListEnums() {
      return this.$store.state.enums.enums.DisposeType
    },
    // 获取告警等级颜色
    alertLevel() {
      const levels = this.$store.state.enums.enums.AlarmLevel
      let levellist = {}
      for (let i in levels) {
        if (i !== '5') {
          levellist[i] = levels[i]
        }
      }
      return levellist
    },
    responseStatus() {
      return this.$store.state.enums.enums['Response Status']
    },
    activeEnum() {
      return this.$store.state.enums.enums.ActiveStatus
    },
    activeEnums() {
      return this.$store.getters['enums/getActiveStatus']
    },
    extentEnum() {
      return this.$store.state.enums.enums.DetectorExtent
    },
    ActiveStatusEnum() {
      return this.$store.state.enums.enums.ActiveStatus
    },
  },
  watch: {
    // 监听筛选框内容，重新设置表格高度
    filterList() {
      this.$_setTableHeight()
    },
  },
  created() {
    this.$_setTableHeight()
    this.$bus.$on('resize', this.$_setTableHeight)
    this.getUser()
    this.getGroups()
    this.query.severityList = ['0', '1', '2', '3', '4']
    this.$_search()
  },
  beforeDestroy() {
    this.$bus.$off('resize', this.$_setTableHeight)
  },
  methods: {
    onClear() {
      this.query.name = ''
      this.$_search()
    },
    onReset() {
      Object.assign(this.$data.query, this.$options.data.call(this).query)
      this.$_search()
    },
    // 查询
    $_search() {
      this.query.pageNum = 1
      this.getTableData()
      this.$_appendFilterListItem()
    },
    //获取全部用户
    async getUser() {
      const res = await findAllUsers({ status: '1' })
      this.userInfos = res.data || []
    },
    //获取全部组织
    async getGroups() {
      const res = await querySysGroupList({ state: '0' })
      this.groupInfos = flatTree(res.data)
    },
    // 获取表格内容
    async getTableData() {
      try {
        // 保存查询参数
        const data = deepClone(this.query)
        this.tableLoading = true
        const res = await getAction(data)
        this.tableDataTotal = res.data.total
        this.tableData = res.data.records
        setTimeout(() => {
          if (this.tableData.length) {
            this.tableData.forEach(item => {
              if (item.disposeType === '1' || item.disposeType === '2') {
                item.receiveUser = this.userInfos
                  .filter(
                    v => item.recipients.split(',').indexOf(v.userId) !== -1,
                  )
                  .map(v => {
                    return {
                      ...v,
                      value: v.userId,
                      text: v.userName,
                    }
                  })
              } else if (item.disposeType === '4') {
                item.receiveUser = this.groupInfos
                  .filter(v => item.recipients.split(',').indexOf(v.id) !== -1)
                  .map(v => {
                    return {
                      ...v,
                      value: v.id,
                      text: v.name,
                    }
                  })
              }
            })
          }
        }, 500)
      } catch (e) {
        console.error(`获取处置管理错误：${e}`)
      }
      this.tableLoading = false
    },

    $_setTableHeight() {
      this.$nextTick(() => {
        const filterFn = () => {
          return -this.$refs.topHeader.filterHeight
        }
        this.tableHeight = setRemainingHeight(filterFn)
      })
    },

    add() {
      this.$router.push('/action/add')
    },
    $_edit(item) {
      this.$router.push(`/action/edit?id=${item.id}`)
    },
    // 详情
    onInfo(item) {
      this.$router.push(`/action/detail?id=${item.id}&isDetail=1`)
    },
    del(data) {
      if (data.active === '0') {
        return this.$notify.info('info', this.$t('alertAction.swal.del.tip'))
      }
      this.$swal({
        title: this.$t('alertAction.swal.del.title'),
        text: this.$t('alertAction.swal.del.text'),
        icon: 'warning',
        showCancelButton: true,
        reverseButtons: true,
        confirmButtonText: this.$t('action.confirm'),
        cancelButtonText: this.$t('action.cancel'),
        customClass: {
          confirmButton: 'sweet-btn-primary',
        },
      }).then(async result => {
        if (result.isConfirmed) {
          try {
            const res = await deleteAction({ id: data.id })
            if (res.code === 200) {
              this.$notify.info(
                'success',
                this.$t('global.hint.del', [
                  this.$t('alertAction.currentTitle'),
                ]),
              )
            }
            this.$_search()
          } catch (e) {
            console.error(`删除处置错误：${e}`)
          }
        }
      })
    },
    handleStatus(item) {
      const text =
        item.active === '0'
          ? this.responseStatus['1'].text
          : this.responseStatus['0'].text
      this.$swal({
        title:
          item.active === '0'
            ? this.$t('response.swal.stop')
            : this.$t('response.swal.start'),
        text: this.$t('response.swal.sure', [text, item.name]),
        icon: item.active === '0' ? 'error' : 'success',
        reverseButtons: true,
        showCancelButton: true,
        confirmButtonText: this.$t('action.confirm'),
        cancelButtonText: this.$t('action.cancel'),
        customClass: {
          confirmButton: 'sweet-btn-primary',
        },
      }).then(async result => {
        if (result.isConfirmed) {
          try {
            const res = await updateActive({
              id: item.id,
              active: item.active === '0' ? '1' : '0',
            })
            if (res.code === 200) {
              this.$notify.info('success', this.$t('response.swal.tip', [text]))
            }
            this.$_search()
          } catch (e) {
            console.error(text + `错误：${e}`)
          }
        }
      })
    },
    // 单击表格行
    $_clickRow(item, slot) {
      slot.expand(!slot.isExpanded)
    },

    showDoQuery() {
      this.showDrawer = true
      this.advanceQuery = deepClone(this.query)
    },

    // 重置
    clearAdvanceQuery() {
      this.advanceQuery = {
        pageNum: 1,
        pageSize: 10,
        name: '',
        disposeTypeList: [],
        severityList: [],
        active: '',
      }
    },

    // 确认查询
    doQuery(cb) {
      this.query = deepClone(this.advanceQuery)
      this.$_search()
      cb()
    },

    // 添加查询条件
    $_appendFilterListItem() {
      const searchKeyList = [
        {
          key: 'name',
          type: 'String',
          label: 'alertAction.headers.name',
        },
        {
          key: 'disposeTypeList',
          type: 'Array',
          mapKey: 'disposeTypeListEnums',
          label: 'alertAction.headers.disposeTypeName',
        },
        {
          key: 'severityList',
          type: 'Array',
          mapKey: 'alertLevel',
          label: 'alertAction.headers.severity',
        },
        {
          key: 'active',
          type: 'String',
          mapKey: 'responseStatus',
          label: 'alertAction.headers.active',
        },
      ]
      handleFilterItem.call(this, searchKeyList)

      // 类型
      // const disposeType = this.filterList.find(v => v.key === 'disposeType')
      // if (disposeType) {
      //   disposeType.text = this.actionTypeEnum[disposeType.value].text
      // }

      // 状态
      const active = this.filterList.find(v => v.key === 'active')
      if (active) {
        active.text = this.responseStatus[active.value].text
      }
    },

    $_clearFilter(item) {
      const bool = clearFilterItem.call(this, item)
      if (!bool) {
        this.$_search()
      }
    },
  },
}
</script>
