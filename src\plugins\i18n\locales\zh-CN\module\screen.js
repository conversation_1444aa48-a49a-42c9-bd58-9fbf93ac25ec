const screen = {
  week: {
    sun: '周日',
    mon: '周一',
    tue: '周二',
    wed: '周三',
    thu: '周四',
    fri: '周五',
    sat: '周六',
  },
  alertStatus: '告警状态分布统计',
  alertEvent: '告警事件类型 Top5',
  alerting: '待处理告警',
  alertTrend: '告警趋势',

  dataHandle: '数据处理',
  ticketStatus: '工单状态分布统计',
  ticketType: '工单类型 Top5',
  ticketing: '待处理工单',
  ticketTrend: '工单趋势',

  alertTotal: '告警总数',
  ticketTotal: '工单总数',
  vulnerabilityTotal: '漏洞总数',
  assetTotal: '资产总数',
  model: '车型',

  fullHint: '请您点击右上角的"全屏"按钮，以便您获得最好的查看体验！',

  bar: {
    cloud: '云端态势',
    vehicle: '车端态势',
  },
}

let cloud = {
  title: '车联网云安全态势',
  left1: {
    title: '安全风险概述',
  },
  left2: '',

  center1: '',
  center2: '',
  center3: '',

  right1: '',
  right2: '',

  times: '次',
  days: '天',
}

export let loadCloudMenu = list => {
  // let list = store.state.global.cloudConfigList
  // if (store.state.global.cloudConfigList.length === 0) {
  //   list = await store.dispatch('global/loadCloudConfigList')
  // }
  cloud.left1.attacksToday = list.find(v => v.itemKey === 'C002')?.itemName
  cloud.left1.attacksDays7 = list.find(v => v.itemKey === 'C003')?.itemName
  cloud.left1.internetDomain = list.find(v => v.itemKey === 'C004')?.itemName
  cloud.left1.baselineItem = list.find(v => v.itemKey === 'C005')?.itemName
  cloud.left1.coutinousDays = list.find(v => v.itemKey === 'C006')?.itemName

  cloud.left2 = list.find(v => v.itemKey === 'C009')?.itemName

  cloud.center1 = list.find(v => v.itemKey === 'C007')?.itemName
  cloud.center2 = list.find(v => v.itemKey === 'C010')?.itemName
  cloud.center3 = list.find(v => v.itemKey === 'C011')?.itemName

  cloud.right1 = list.find(v => v.itemKey === 'C008')?.itemName
  cloud.right2 = list.find(v => v.itemKey === 'C012')?.itemName
}

// loadCloudMenu()

export default Object.assign(screen, { cloud })
