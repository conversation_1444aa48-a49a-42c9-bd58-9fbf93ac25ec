const asset = {
  headers: {
    assetId: '资产编号',
    model: '车型',
    currentSituation: '当前态势',
    newAlert: '未处理告警',
    assetGroup: '资产组',
    firstRegistrationTime: '首次登记时间',
    lastActiveTime: '最后接收时间',
    lastPosition: '最后位置',
    count: '告警数量',
    year: '年份',
  },
  swal: {
    title: '资产删除',
    text: '请确认是需要删除该{id}资产；删除后，数据不可恢复！',
    cancel: '已取消删除该资产：{id}',
  },
  template: {
    hint: '下载车辆信息模板',
    text: '车辆信息导入模板',
  },
  alertStatus: '告警状况',
  assetEventAxis: '资产事件轴',
  selectAssetGroup: '选择资产组',
  closeAutoRefresh: '自动刷新已关闭！',
  noGrant: '无页面权限，请联系管理员授权！',
  setRefresh: '设置刷新频率',
  investigationAdvance: '高级调查',
  axis: {
    item: '{num}项',
    frequency: '{num}次',
    view: '查看数字孪生',
    queryField: '查询字段',
    headers: {
      signalName: '信号名称',
      value: '值',
      signalSource: '信号来源时间',
      desc: '描述',
    },
  },
  idps: {
    posture: '安全态势',
    status: '未处理告警',
    version: 'IDPS版本号',
    os: '系统名称',
    typeTotal: '告警类型总数',
    typeDis: '告警分类分布',
    alertDis: '告警严重性分布',
    point: '{num}分',
  },
  recentActivity: '最近活动',
  tabName0: '零部件态势',
  tabName1: '零部件状态',
  idpsTab2: {
    eventDistribution: '事件类型分布 TOP5',
    ipDistribution: '访问IP分布 TOP5',
    ipDistribution: '访问IP分布 TOP5',
    ruleDistribution: 'IDPS防护规则分布',
    ruleTotal: '防护项总数',
    ruleUpdates: '规则更新动态',
    ruleUpdatesMore: '查看更多规则动态',
    updates: '更新动态',
    equipmentApplication: '设备应用情况',
    processDetails: {
      title: '进程详情',
      userTotal: '当前用户总数',
      processTotal: '当前进程总数',
      headers: {
        user: '用户',
        process: '进程',
        startCommand: '启动命令',
        cpu: 'CPU占用率',
        ram: 'Ram占用率',
        rom: 'Rom占用率',
      },
    },
    listeningPort: '监听端口',
    viewMore: '查看更多',
    currentFirewallPolicy: {
      title: '当前防火墙策略',
      headers: {
        id: '防火墙规则ID',
        type: '类型',
        detail: '策略详情',
        isPolicy: '是否有策略',
      },
    },
    eventDetailList: {
      title: 'IDPS事件详情列表',
      headers: {
        time: '发生时间',
        type: '事件类型',
        contnet: '事件内容',
        count: '发生次数',
      },
    },
  },
  noneSubHint: '暂无子信号！',
  fileHint: '单个附件大小不超过10M；附件格式要求：xls,xlsx',
}

export default asset
