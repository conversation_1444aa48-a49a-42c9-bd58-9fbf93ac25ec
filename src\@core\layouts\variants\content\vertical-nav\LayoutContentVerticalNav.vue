<template>
  <v-app
    :class="[
      { 'nav-drawer-mini': menuIsVerticalNavMini },
      { 'content-full': appContentWidth === 'full' },
    ]"
    class="content-layout vertical-nav"
  >
    <!-- TODO: All elevations are to bottom and footer requires shadow on top -->
    <!-- :class="[
        { 'app-system-bar-boxed': appContentWidth === 'boxed' },
        { 'bg-blur': appBarIsBlurred },
      ]" -->
    <v-system-bar
      app
      :height="$store.state.global.systemBarHeight"
      :absolute="appBarType === 'static'"
      class="app-system-bar pa-0 pr-2"
      :class="{ 'box-shadow-bottom': $vuetify.theme.isDark }"
    >
      <slot
        name="navbar"
        :isVerticalNavMenuActive="isVerticalNavMenuActive"
        :toggleVerticalNavMenuActive="toggleVerticalNavMenuActive"
      ></slot>
    </v-system-bar>

    <v-navigation-drawer
      v-if="!menuIsMenuHidden"
      ref="layoutDrawer"
      class="app-navigation-menu"
      app
      permanent
      :right="$vuetify.rtl"
      :disable-resize-watcher="true"
      v-model="isVerticalNavMenuActive"
      :expand-on-hover="menuIsVerticalNavMini"
      :width="width"
    >
      <!-- TODO: 20221013 Henry01  Width 可能要动态根据屏幕尺寸 !-->
      <vertical-nav-menu
        :nav-menu-items="navMenuItems"
        @close-nav-menu="isVerticalNavMenuActive = false"
      />
    </v-navigation-drawer>

    <slot name="v-app-content"></slot>

    <!-- :style="`max-height:${mainMaxHeight}px`" class="overflow-auto" -->
    <v-main style="max-height: 100vh" class="overflow-auto">
      <app-content-container>
        <slot></slot>
      </app-content-container>
    </v-main>
    <v-overlay
      :value="$store.state.app.shallContentShowOverlay"
      z-index="4"
      absolute
      class="content-overlay"
    ></v-overlay>

    <v-footer
      v-if="footerType !== 'hidden'"
      app
      inset
      :absolute="footerType === 'static'"
      padless
      :class="{ 'mx-auto': appContentWidth !== 'full' }"
      :color="footerType === 'static' ? 'transparent' : null"
    >
      <div :class="{ 'px-5': footerType === 'fixed' }" class="py-4 w-full">
        <slot name="footer"></slot>
      </div>
    </v-footer>
  </v-app>
</template>

<script>
import { getVuetify } from '@/@core/utils'
import { SCREEN_THRESHOLD } from '@/util/constant'
import useAppConfig from '@core/@app-config/useAppConfig'
import AppContentContainer from '@core/layouts/components/app-content-container/AppContentContainer.vue'
import VerticalNavMenu from '@core/layouts/components/vertical-nav-menu/VerticalNavMenu.vue'
import { computed, getCurrentInstance, ref, watch } from '@vue/composition-api'
import { useWindowScroll } from '@vueuse/core'

export default {
  components: {
    AppContentContainer,
    VerticalNavMenu,
  },
  props: {
    navMenuItems: {
      type: Array,
      required: true,
    },
  },
  setup() {
    /* eslint-disable object-curly-newline, operator-linebreak */
    const {
      menuIsVerticalNavMini,
      menuIsMenuHidden,
      appBarType,
      appBarIsBlurred,
      footerType,
      appContentWidth,
    } = useAppConfig()
    const $vuetify = getVuetify()

    const isVerticalNavMenuActive = ref(true)

    const vm = getCurrentInstance().proxy

    // TODO: Check do we need below watch
    watch(
      () => $vuetify?.breakpoint.mdAndDown,
      value => {
        isVerticalNavMenuActive.value = !value

        vm.$store.commit('appConfig/TOGGLE_MENU_VERTICAL_NAV_MINI', value)

        // // 左边菜单栏隐藏时，菜单最小化为false（isVerticalNavMini为false）
        // vm.$store.commit('appConfig/TOGGLE_MENU_VERTICAL_NAV_MINI', true)
      },
      {
        immediate: true,
      },
    )

    const toggleVerticalNavMenuActive = () => {
      isVerticalNavMenuActive.value = !isVerticalNavMenuActive.value
    }

    const { y: scrollY } = useWindowScroll()

    const width = computed(() => {
      if (vm.$store.state.global.clientWidth === 0) {
        // 第一次打开网页才会进入
        vm.$store.commit(
          'global/setClientWidth',
          window.document.documentElement.clientWidth,
        )
      }

      if (vm.$store.state.global.clientWidth > SCREEN_THRESHOLD) {
        vm.$store.commit('global/setLeftMenuWidth', 200)
      } else {
        vm.$store.commit('global/setLeftMenuWidth', 200)
      }

      return vm.$store.state.global.leftMenuWidth
    })

    const mainMaxHeight = computed(() => {
      const screenHeight =
        window.innerHeight ||
        document.documentElement.clientHeight ||
        document.body.clientHeight
      return screenHeight - vm.$store.state.global.systemBarHeight
    })

    return {
      mainMaxHeight,
      width,
      isVerticalNavMenuActive,
      toggleVerticalNavMenuActive,
      menuIsVerticalNavMini,
      menuIsMenuHidden,
      appBarType,
      appBarIsBlurred,
      footerType,
      appContentWidth,
      scrollY,
    }
  },
}
</script>

<style lang="scss" scoped>
@import '~@core/layouts/styles/_variables';

.box-shadow-bottom {
  box-shadow: 0px -1px 0px 0px #1d233f inset;
}
// .app-content-container {
//   padding: $content-padding-vertical-navigation-menu;
// }

// Vuetify Fix
// https://github.com/vuetifyjs/vuetify/issues/13327
$nav-drawer-mini-width: 56px;

.v-application {
  .v-main,
  .v-footer {
    transition-duration: 0.3s;
  }

  .v-footer {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
  }
  @include theme(v-footer) using ($material) {
    // Elevation 3 with -y
    &.v-footer--fixed {
      box-shadow: 0 -4px 8px -4px
        rgba(map-deep-get($material, 'shadow-color'), 0.42) !important;
    }
  }

  &.nav-drawer-mini {
    .v-main {
      // We haven't used `ltr` & `rtl` mixin because those doesn't work with top-level selectors: https://github.com/vuetifyjs/vuetify/issues/13987
      @at-root {
        .nav-drawer-mini {
          &.v-application--is-ltr {
            .v-main {
              padding-left: $nav-drawer-mini-width !important;
            }
          }
          &.v-application--is-rtl {
            .v-main {
              padding-right: $nav-drawer-mini-width !important;
            }
          }
        }
      }
    }
    @media #{map-get($display-breakpoints, 'lg-and-up')} {
      .app-navigation-menu ~ .v-footer,
      .app-navigation-menu + .v-app-bar {
        left: $nav-drawer-mini-width !important;
      }
    }
  }

  .v-app-bar,
  .v-footer {
    max-width: calc(1440px - (1.5rem * 2));
    @media screen and (max-width: 1456px) {
      margin-left: 1.5rem !important;
      margin-right: 1.5rem !important;
    }

    @at-root .v-application {
      &.content-full {
        .v-footer,
        .v-app-bar {
          max-width: unset;
          margin-left: $content-padding-vertical-navigation-menu !important;
          margin-right: $content-padding-vertical-navigation-menu !important;
        }
      }

      &:not(.nav-drawer-mini) {
        @media screen and (max-width: 1711px) {
          .app-navigation-menu ~ .v-footer,
          .app-navigation-menu + .v-app-bar {
            margin-left: 1.5rem !important;
            margin-right: 1.5rem !important;
          }
        }
      }
      &.nav-drawer-mini {
        @media screen and (max-width: 1523px) {
          .v-footer,
          .v-app-bar {
            margin-left: 1.5rem !important;
            margin-right: 1.5rem !important;
          }
        }
      }
    }
  }

  .v-app-bar {
    border-radius: 0 0 10px 10px !important;
    z-index: 5;

    &.v-toolbar:not(.app-bar-shinked) {
      background-color: transparent;
    }

    &.app-bar-static {
      will-change: padding, background-color;
      transition: padding 0.2s ease, background-color 0.18s ease, left 0.3s ease;

      &.v-toolbar.v-sheet:not(.v-app-bar--is-scrolled) {
        box-shadow: none !important;
      }

      ::v-deep > .v-toolbar__content {
        padding: 0;
      }
    }
  }
}

@include theme(v-app-bar) using ($material) {
  &.v-toolbar.app-bar-shinked {
    background-color: map-deep-get($material, 'cards');
  }
}

.v-application.content-layout {
  @media #{map-get($display-breakpoints, 'md-and-down')} {
    .v-main,
    .v-footer,
    .v-app-bar {
      max-width: unset;
      left: 0 !important;
      @include ltr() {
        padding-left: 0 !important;
      }
      @include rtl() {
        padding-right: 0 !important;
      }
    }
  }
}
::v-deep .v-navigation-drawer__content {
  overflow: hidden !important;
}

.theme--light.app-system-bar {
  background: linear-gradient(92.46deg, #224fa9 0%, #0f3480 68.57%);
  color: #fff !important;
}
.theme--dark.app-system-bar {
  background: #171b34;
}
// .app-system-bar {
//   background: #13172e !important;
// }
</style>
