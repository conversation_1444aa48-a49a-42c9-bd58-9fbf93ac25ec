<template>
  <div class="c-box">
    <div class="c-box-header">{{ $t('screen.cloud.center2') }}</div>

    <div class="box-chart d-flex flex-column">
      <div class="c-center2-text">{{ $t('enums.datePresets.last30') }}</div>
      <div class="c-center2-num">
        <count-to
          ref="count1"
          class="ml-0"
          :startVal="0"
          :endVal="total"
          :duration="2500"
        ></count-to
        ><span class="c-center2-unit">{{ $t('screen.cloud.times') }}</span>
      </div>
      <v-progress-linear
        rounded
        :height="$getCeilSize(8)"
        color="#32FDB8"
        :value="50"
      >
      </v-progress-linear>
    </div>
  </div>
</template>

<script>
import countTo from 'vue-count-to'
export default {
  name: 'LeftCloud2',
  props: {
    total: {
      type: [Number, String],
      default: 256,
    },
  },
  components: {
    countTo,
  },
  watch: {
    total() {
      this.$refs.count1 && this.$refs.count1.start()
    },
  },
}
</script>
