<template>
  <!-- { 'vertical-nav-menu-group-active': isActive }, -->
  <v-list-group
    v-if="canViewVerticalNavMenuGroup(item)"
    ref="refVListGroup"
    class="vertical-nav-menu-group"
    :class="[
      { 'vertical-nav-menu-group-active': isActive || isHover },
      ...rootThemeClasses,
    ]"
    :value="isGroupExpanded"
    @click="updateGroupOpen(!isOpen)"
    @mousemove="isHover = true"
    @mouseout="isHover = false"
  >
    <!-- @mousemove="isMouseHovered = true" -->
    <template #prependIcon>
      <vsoc-icon
        v-if="
          item.icon.includes('icon-chexinganquan') &&
          (isActive || isGroupExpanded)
        "
        class="d-flex align-center primary--text"
        type="fill"
        :icon="item.icon"
      ></vsoc-icon>
      <vsoc-icon
        v-else
        class="d-flex align-center"
        :icon="item.icon | getCheckedIcon(isActive || isGroupExpanded)"
      ></vsoc-icon>
      <!-- <v-icon size="16" :class="{ 'alternate-icon-small': !item.icon }">
        {{ item.icon || alternateIcon }}
      </v-icon> -->
    </template>
    <template #activator>
      <v-list-item-title width="48">
        {{ $generateMenuTitle(item) }}
        <!-- {{ item.title }} -->
      </v-list-item-title>

      <!-- <v-list-item-action v-if="item.badge" class="flex-shrink-0">
        <v-badge
          :color="item.badgeColor"
          inline
          :content="item.badge"
        >
        </v-badge>
      </v-list-item-action> -->
    </template>

    <component
      :is="resolveNavItemComponent(child)"
      v-for="child in item.children"
      :key="child.subheader || child.title"
      :item="child"
    ></component>
  </v-list-group>
</template>

<script>
// eslint-disable-next-line object-curly-newline
import {
  computed,
  getCurrentInstance,
  inject,
  ref,
  watch,
} from '@vue/composition-api'

// SFCs
import useVerticalNavMenu from '@/@core/layouts/composable/vertical-nav/useVerticalNavMenu'
import VerticalNavMenuLink from '@core/layouts/components/vertical-nav-menu/components/vertical-nav-menu-link/VerticalNavMenuLink.vue'
import VerticalNavMenuSectionTitle from '@core/layouts/components/vertical-nav-menu/components/vertical-nav-menu-section-title/VerticalNavMenuSectionTitle.vue'

// Composable
import useNav from '@/@core/layouts/composable/useNav'
import useAppConfig from '@core/@app-config/useAppConfig'
import useVerticalNavGroup from '@core/layouts/composable/vertical-nav/useVerticalNavGroup'
import { useUtils as useAclUtils } from '@core/libs/acl'
import { useUtils } from '@core/libs/i18n'

// Other
import themeConfig from '@themeConfig'

export default {
  name: 'VerticalNavMenuGroup',
  components: {
    VerticalNavMenuLink,
    VerticalNavMenuSectionTitle,
  },
  props: {
    item: {
      type: Object,
      required: true,
    },
  },
  computed: {
    // From Vuetify's `vuetify/src/mixins/themeable/index.ts` mixin
    rootThemeClasses() {
      return {
        'theme--dark': this.$vuetify.theme.isDark,
        'theme--light': !this.$vuetify.theme.isDark,
      }
    },
  },
  setup(props) {
    const vm = getCurrentInstance().proxy
    const { resolveNavItemComponent } = useVerticalNavMenu()
    const { isOpen, updateGroupOpen, isActive } = useVerticalNavGroup(
      props.item,
    )
    const { canViewVerticalNavMenuGroup } = useAclUtils()
    const { isNavGroupActive } = useNav()
    const { menuIsVerticalNavMini } = useAppConfig()
    const isMouseHovered = inject('isMouseHovered')

    const isAccordion = themeConfig.menu.verticalMenuAccordion

    // Template ref
    const refVListGroup = ref(null)

    // VNavigationDrawer Internal API
    const isGroupExpanded = computed({
      get: () => (refVListGroup.value ? refVListGroup.value.isActive : false),
      set: value => {
        refVListGroup.value.isActive = value
      },
    })
    watch(isOpen, value => {
      if (isAccordion) isGroupExpanded.value = value
    })

    watch(menuIsVerticalNavMini, val => {
      if (val && !isMouseHovered) isGroupExpanded.value = false
      else {
        isGroupExpanded.value = isOpen.value
      }
    })

    watch(isMouseHovered, value => {
      if (menuIsVerticalNavMini.value) {
        if (value) isGroupExpanded.value = isOpen.value
        else isGroupExpanded.value = false
      }
    })
    const isHover = ref(false)

    // I18n
    const { t } = useUtils()

    return {
      isGroupExpanded,
      resolveNavItemComponent,
      isNavGroupActive,
      menuIsVerticalNavMini,
      refVListGroup,
      isMouseHovered,
      isOpen,
      isActive,
      isHover,
      updateGroupOpen,

      // Alternate Icon
      alternateIcon: themeConfig.menu.groupChildIcon,

      // i18n
      t,

      // ACL
      canViewVerticalNavMenuGroup,
    }
  },
}
</script>
<style lang="scss" scoped>
::v-deep .v-icon__svg {
  width: 16px;
  height: 16px;
}
// ::v-deep .v-icon.v-icon {
//   font-size: 16px;
// }
</style>
<style lang="scss">
@import '~vuetify/src/styles/styles.sass';
.app-navigation-menu .v-list-item,
.app-navigation-menu .v-subheader {
  padding-left: 18px !important;
  padding-right: 12px !important;
}
.v-list-group__header__prepend-icon {
  padding-right: 14px !important;
}
// .v-list-item__icon {
//   padding-right: 12px;
// }

.v-application--is-ltr .vertical-nav-menu-items .v-list-item__icon:first-child {
  margin-right: 0 !important ;
}
.vertical-nav-menu-group {
  // color: #1f2533;

  &.v-list-group--active
    > .v-list-group__header
    > .v-list-group__header__append-icon
    .v-icon {
    transform: none;
  }

  & > .v-list-group__header > .v-list-group__header__append-icon .v-icon {
    transform: rotate(-90deg);
  }

  & & .v-list-group__items .v-list-item__icon {
    visibility: hidden;
  }
}

// @include theme(vertical-nav-menu-group) using ($material) {
//   &.vertical-nav-menu-group-active > .v-list-group__header {
//     // background-color: rgba(
//     //   map-deep-get($material, 'text', 'primary'),
//     //   map-deep-get($material, 'states', 'selected')
//     // );
//     // background: $primary-hover-color;
//     color: var(--v-primary-base) !important;
//     caret-color: var(--v-primary-base) !important;
//   }
// }

// .alternate-icon-small {
//   font-size: 14px;
//   height: 14px;
//   width: 14px;
// }
.vertical-nav-menu-items .v-list-item {
  justify-content: center !important;
}
</style>
