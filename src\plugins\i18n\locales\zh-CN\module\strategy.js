const strategy = {
  project: {
    currentTitle: '项目',
    headers: {
      projectName: '项目名称',
      groupType: '组别类型',
      vehicleModel: '车型型号',
      vehicleYear: '车型年份',
      validRulesetCount: '有效策略',
      ruleSetId: '待审核策略',
    },
    swal: {
      del: {
        title: '删除项目',
        text: '是否确认删除项目：{0}？',
      },
      del1: {
        title: '删除拦截',
        text: '此项目下存在有效的Ruleset，无法直接删除。请先解除所有关联的Ruleset后再试。',
        btn: '知道了',
      },
    },
  },
  ruleSet: {
    currentTitle: '策略管理',
    headers: {
      rulesetName: '策略名称',
      modelCode: '车型代码（16进制）',
      mainVersion: '主版本号（16进制）',
      revisionNumber: '修订号',
      enableStatus: '规则状态',
      filterStatus: '算法过滤',
      rulesetState: '状态',
      reason: '原因',
      verificationContent: '验证内容',
    },
    securityPolicy: '安全策略',
    pending: '待审核策略',
    review: '审核策略',
    edit: '修改策略',
    upload: {
      rulesetUpload: '策略上传',
      tip: '值需要为16进制并且是大写字母',
      tip1: '文件内容不是有效的JSON格式！',
      tip2: '请上传策略！',
    },
    del: {
      title: '删除RuleSet',
      text: '确认删除RuleSet:{0}?',
    },
    info: {
      content: '变更内容',
      current: '禁用规则明细（当前版本）',
      new: '禁用规则明细（新版本）',
      old: '禁用规则明细（旧版本）',
      newStatus: '规则状态（新版本）',
      oldStatus: '规则状态（旧版本）',
      tip: '提交成功',
      tip1: '禁用成功',
      tip2: '启用成功',
      audit: '策略审核',
      detail: '策略详情',
      history: '策略历史详情',
      reviewfailed: '审核不通过',
      reviewSuccess: '审核通过',
      verificationContent: '验证内容',
    },
    historicalVersion: '历史版本',
  },
  decryptionTool: {
    form: {
      modelCode: '车型（16进制）',
      mainVersion: '主版本号(16进制)',
      indexId: 'Index ID',
      messageTime: 'Message Time',
      rules: '数据可视化规则',
      encryptedData: '原始加密数据',
    },
    results: '解析结果',
    tip: '原始加密数据必须是544个字符',
    tip1: '数据可视化规则格式不正确，请检查JSON格式',
  },
}
export default strategy
