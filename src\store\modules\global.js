// import { getAllAlertTags } from '@/api/detector'
// import { getAllVehicleList } from '@/api/list/index'
import { getAllClassify } from '@/api/classify/index'
import { getOperation } from '@/api/log/audit'
import { getPropertiesList } from '@/api/system/parameter'

import { getAutomakerList } from '@/api/asset/automaker'
import { getAllPlatFormList } from '@/api/asset/platform'
import { getMyMessagesList, getUnreadStatistics } from '@/api/message/index'
// import { getParseAllList } from '@/api/parse'
import { digitalTwinsByCondition } from '@/api/signal/index'

import { getConfigDt } from '@/api/system/index-dt'
import { findUsers } from '@/api/system/user'
import {
  getTicketBtn,
  getTicketPriority,
  getTicketStatus,
} from '@/api/ticket/index'
import { i18n } from '@/plugins/i18n/index'
import { PAGESIZE_MAX } from '@/util/constant'
import { getLocalStorage, setLocalStorage } from '@/util/localStorage'
import { getToken } from '@/util/token'
export default {
  namespaced: true,
  state: {
    isCustomizerOpen: false,
    // admin
    userIdByAllList: [],
    isScroll: false,
    // 间隔高度
    subMarginFn: () => {
      return -12
    },
    // 左侧菜单宽度
    leftMenuWidth: 200,
    // 导航栏高度
    systemBarHeight: 48,
    // bread-crumb高度
    breadCrumbHeight: 54,

    // 屏幕宽度
    clientWidth: 0,

    // 公式列表
    formulaList: [],

    // 数字孪生信号列表
    signals: [],

    // 数字孪生数据JSON结构
    signalsMap: {},

    // 数字孪生事件列表
    events: [],

    // 数字孪生异常列表
    features: [],

    // 数字孪生【Structured】子信号列表
    subSignals: [],

    // 数字孪生【Structured list】子信号列表
    structureList: [],

    // 规则组件逻辑源
    logicSource: [],

    // 逻辑源Map
    logicSourceMap: {},

    // 规则组件操作符
    logicOperator: [],

    // 资产组列表
    groups: [],

    // 告警标签列表
    alertTags: [],

    // 函数列表
    functions: [],

    // 列表项
    list: [],

    //获取工单状态按钮
    tickets: [],
    //工单状态
    ticketStatus: [],
    //工单优先级
    ticketPriority: [],

    messageNum: 0,
    unreadObj: {},
    messageList: [],
    //轮询
    pollingST: null,
    parses: [],
    // 零部件
    platList: [],
    // 供应商
    vendorList: [],
    // 平台
    platformList: [],
    models: [],
  },
  getters: {
    getTopHeaderHeight(state) {
      let marginHeight = state.subMarginFn()
      return state.systemBarHeight + state.breadCrumbHeight - marginHeight
    },
    getFileHeaders() {
      // 附件请求头
      const fileHeaders = {
        Authorization: getToken(),
        'Accept-Language': i18n.locale,
      }
      return fileHeaders
    },
    // // 0字符 1数值
    // getStringList(state) {
    //   return state.list.filter(item => Number(item.valueType) === 0)
    // },
    // getNumberList(state) {
    //   return state.list.filter(item => Number(item.valueType) === 1)
    // },
    getMessageNum(state) {
      return state.messageNum
    },
    getUnreadMessage(state) {
      return state.unreadObj
    },
    getMessageList(state) {
      // return type =>
      //   state.messageList.filter(
      //     item => Number(item.messageType) === Number(type),
      //   )
      return state.messageList
    },
    getSubSignals(state) {
      return state.subSignals
    },
    getSignals(state) {
      return assetsType =>
        state.signals.filter(
          item => Number(item.assetType) === Number(assetsType),
        )
    },

    getSignalsByMult(state) {
      return params => {
        let result = state.signals.filter(
          item => Number(item.assetType) === Number(params.assetType),
        )
        if (params.signalValueType) {
          result = result.filter(
            item => item.signalValueType == params.signalValueType,
          )
        }
        return result
      }
    },

    getEvents(state) {
      return assetsType =>
        state.events.filter(
          item => Number(item.assetType) === Number(assetsType),
        )
    },

    getFeatures(state) {
      return assetsType =>
        state.features.filter(
          item => Number(item.assetType) === Number(assetsType),
        )
    },
  },
  actions: {
    async loadClassifyList({ commit }, params = { type: '', name: '' }) {
      try {
        const { data } = await getAllClassify(params)
        return data
      } catch (err) {
        console.log('分类管理数据报错', err)
      }
    },
    async getParseList({ commit, state }) {
      try {
        if (state.parses.length > 1) {
          return Promise.resolve(state.parses)
        }

        const { data } = await getParseAllList()
        let newData = data.map(item => {
          return {
            text: item.name,
            value: item.id,
          }
        })
        newData.unshift({
          text: 'CommonParser',
          value: '-1',
        })

        commit('setParse', newData)
        return newData
      } catch (err) {
        console.log('获取解析规则报错', err)
      }
    },
    async loadAuditConfigList({ commit }) {
      try {
        const { data } = await getOperation()
        const newData = data.map(item => {
          item.feature.forEach(v => {
            v.checked = v.status === '1'
          })
          return {
            text: item.menu,
            value: item.menu,
          }
        })
        commit('setAuditConfigList', newData)
        return newData
      } catch (err) {
        console.log('获取审计配置数据报错', err)
      }
    },
    async loadUserList({ commit }, roleId = '') {
      try {
        const params = {
          pageNum: 1,
          pageSize: PAGESIZE_MAX,
          roleId,
        }
        let { data } = await findUsers(params)
        data.records.forEach(item => {
          item.text = item.userName
          item.value = item.userId
        })
        let userIdList = data.records.map(v => v.userId)
        commit('setUserIdByAdmin', userIdList)
        return data.records
      } catch (err) {
        console.log('获取用户数据报错', err)
      }
    },

    async searchSignal(
      { commit, state },
      params = { isReset: false, active: '1' },
    ) {
      if (state.signals.length && state.events.length && !params.isReset) {
        return {
          eventList: state.events,
          signalList: state.signals,
        }
      }
      try {
        const res = await digitalTwinsByCondition({
          assetType: '',
          name: '',
          signalType: '',
          signalValueTypes: [],
          isAbnormalValue: '', // 逻辑源是否是异常值 0不是 1 是
          active: params.active || '1', // 0-无效，1-有效
        })
        const eventList = []
        const signalList = []
        const featureList = [] // 特征信号
        const subSignals = []
        res.data.forEach(item => {
          if (item.active !== '1') return
          item.displayName = item.name
          if (item.featureId) {
            featureList.push(item)
          }
          if (item.dataType === '2') {
            subSignals.push(item)
          }
          // 0信号状态 1行为事件
          if (item.signalType === '1') {
            eventList.push(item)
          } else if (item.signalType === '0') {
            signalList.push(item)
          }
        })
        commit('setSignals', signalList)
        commit('setSubSignals', subSignals)
        commit('setEvents', eventList)
        commit('setFeatures', featureList)
        commit('setSignalsMap', res.data)

        return {
          eventList,
          signalList,
        }
      } catch (e) {
        console.error(`获取数字孪生数据：${e}`)
      }
    },

    async searchLogicSource({ commit, state }) {
      return new Promise(resolve => {
        if (state.logicOperator.length && state.logicSource.length) {
          resolve({
            logicSource: state.logicSource,
            logicOperator: state.logicOperator,
          })

          return
        }
        Promise.all([getConfigDt('SOURCE'), getConfigDt('OPERATOR')]).then(
          e => {
            e[0].sort((item1, item2) => item1.id - item2.id)
            e[1].sort((item1, item2) => item1.id - item2.id)
            const logicSource = e[0].map(item => ({
              text: item.code,
              value: item.value,
              enTitle: item.enTitle,
            }))
            const logicOperator = e[1].map(item => ({
              text: item.code,
              value: item.value,
            }))
            commit('setLogicSource', logicSource)
            commit('setLogicOperator', logicOperator)
            commit('setLogicSourceMap', logicSource)
            resolve({
              logicSource,
              logicOperator,
            })
          },
        )
      })
    },

    //  获取所有列表
    // async searchAllList({ commit, state }) {
    //   try {
    //     const res = await getAllVehicleList({ name: '' })

    //     const data = res.data.map(item => ({
    //       name: item.name,
    //       displayName: item.name,
    //       id: item.id,
    //       valueType: item.valueType,
    //       description: item.description,
    //     }))
    //     commit('setList', data)

    //     return data
    //   } catch (e) {
    //     console.error(`获取列表项错误：${e}`)
    //   }
    // },
    //获取所有车企
    async loadAllAutomaker({ commit, state }, params) {
      try {
        const query = {
          automaker_name: '',
          is_supervise: params?.is_supervise || '',
        }
        const res = await getAutomakerList(query)
        const data = res.data.map(item => ({
          ...item,
          text: item.automakerName,
          value: item.automakerCode,
        }))
        commit('setModels', data)
        return data
      } catch (e) {
        console.error(`获取车企错误：${e}`)
      }
    },

    //获取所有车企平台
    async loadPlatFormData({ commit, state }, params) {
      try {
        const query = {
          platformName: '',
          isSupervise: params?.is_supervise || '',
        }
        const res = await getAllPlatFormList(query)
        const data = res.data.map(item => ({
          ...item,
          text: item.platformName,
          value: item.id,
        }))
        commit('setModels', data)
        return data
      } catch (e) {
        console.error(`获取车企平台错误：${e}`)
      }
    },

    // async searchAlertTags({ commit, state }, isReset = false) {
    //   if (state.alertTags.length && !isReset) {
    //     return state.alertTags
    //   }
    //   const data = await getAllAlertTags()

    //   commit(
    //     'setAlertTags',
    //     data.data.length &&
    //       data.data.map(v => ({
    //         text: v.id,
    //         value: v.id,
    //       })),
    //   )

    //   return state.alertTags
    // },

    async getFunctions({ commit, state }, isReset) {
      if (state.functions.length && !isReset) {
        return state.functions
      }
      const res = await getPropertiesList({ pageNum: 1, pageSize: 9999 })
      // commit('setFunctions', res.content)
      let values = res.data.records
        .filter(v => v.groupName === 'Formula_List')
        .map(v => {
          return {
            ...v,
            function_name: v.propertyName,
            grammar: v.propertyKey,
            parameters: v.propertyValue,
          }
        })
      commit('setFunctions', values)
      return state.functions
    },

    //获取工单权限
    async getTicketBtn({ commit, state }, isReset) {
      if (state.tickets.length && !isReset) {
        return state.tickets
      }
      const res = await getTicketBtn()
      commit('setTickets', res.data)
      return state.tickets
    },

    //获取工单状态
    async getTicketStatus({ commit, state }, isReset) {
      if (state.ticketStatus.length && !isReset) {
        return state.ticketStatus
      }
      const res = await getTicketStatus()
      res.data = res.data.map(v => {
        return {
          ...v,
          text: v.dictName,
          value: v.dictId,
        }
      })
      commit('setTicketStatus', res.data)
      return state.ticketStatus
    },

    //获取工单优先级
    async getTicketPriority({ commit, state }, isReset) {
      if (state.ticketPriority.length && !isReset) {
        return state.ticketPriority
      }
      const res = await getTicketPriority()
      res.data = res.data.map(v => {
        return {
          ...v,
          text: v.dictName,
          value: v.dictId,
        }
      })
      commit('setTicketPriority', res.data)
      return state.ticketPriority
    },

    //获取消息列表
    async getMessge({ commit, state }) {
      Promise.all([
        getMyMessagesList({
          pageNum: 1,
          status: '0',
          pageSize: 50,
        }),
        getUnreadStatistics(),
      ]).then(e => {
        commit('setMessageList', e[0].data.records || [])
        commit('setMessageNum', e[1].data.total)
        commit('setUnreadMessage', e[1].data)
      })
    },

    poll({ commit, state, dispatch }) {
      clearInterval(state.pollingST)
      // 300000
      state.pollingST = setInterval(
        () => {
          dispatch('getMessge')
        },
        getLocalStorage('noteSetting')
          ? Number(JSON.parse(getLocalStorage('noteSetting')).time) * 1000
          : 300 * 1000,
      )
    },
  },
  mutations: {
    setPlatformList(state, val) {
      state.platformList = val
    },
    setVendorList(state, val) {
      state.vendorList = val
    },
    setPlatList(state, val) {
      state.platList = val
    },
    setVendorList(state, val) {
      state.vendorList = val
    },
    setParse(state, val) {
      state.parses = val
    },
    setIsCustomizerOpen(state, val) {
      state.isCustomizerOpen = val
    },
    setUserIdByAdmin(state, val) {
      state.userIdByAllList = val
      setLocalStorage('userIdByAllList', JSON.stringify(val))
      // localStorage.setItem('userIdByAllList', JSON.stringify(val))
    },
    setScroll(state, val) {
      state.isScroll = val
    },
    setAuditConfigList(state, val) {
      state.auditConfigList = val
    },
    setLeftMenuWidth(state, val) {
      state.leftMenuWidth = val
    },
    clearPollingST(state, val) {
      clearInterval(state.pollingST)
    },
    setTicketPriority(state, val) {
      state.ticketPriority = val
    },
    setTicketStatus(state, val) {
      state.ticketStatus = val
    },
    setMessageList(state, val) {
      state.messageList = val
    },
    setMessageNum(state, val) {
      state.messageNum = val
    },
    setUnreadMessage(state, val) {
      state.unreadObj = val
    },
    setTickets(state, val) {
      state.tickets = val
    },
    setModels(state, val) {
      state.models = val
    },
    setStructureList(state, val) {
      state.structureList = val
    },
    setSubSignals(state, val) {
      state.subSignals = val
    },

    // 修改屏幕宽度
    setClientWidth(state, val) {
      state.clientWidth = val
    },
    setFunctions(state, val) {
      state.functions = val
    },
    setAlertTags(state, val) {
      state.alertTags = val
    },
    setFormulaList(state, val) {
      state.formulaList = val
    },

    setSignalsMap(state, val) {
      const obj = {}
      val.forEach(item => {
        obj[item.id] = item
      })
      state.signalsMap = obj
    },

    setSignals(state, val) {
      // 信号取值类型：0 Text, 1 Boolean, 2 Numeric, 3 Whole Numeric, 4 Timestamp, 5 Structured, 6 Structured list, 7 Array, 8 Histogram
      // const arr = val.filter(item => item.signalValueType === 'OBJECT_ARRAY' || item.signalValueType === 'DICTIONARY')
      // state.signals = val.filter(v => v.signalValueType !== 'ARRAY')
      // state.signals = val.filter(v => v.signalValueType !== '7')
      state.signals = val
    },

    setEvents(state, val) {
      state.events = val
    },
    setFeatures(state, val) {
      state.features = val
    },
    setLogicSource(state, val) {
      state.logicSource = val
    },

    setLogicSourceMap(state, val) {
      const obj = {}
      val.forEach(item => {
        obj[item.value] = item
      })
      state.logicSourceMap = obj
    },

    setLogicOperator(state, val) {
      state.logicOperator = val
    },

    setGroups(state, val) {
      state.groups = val
    },
    setList(state, val) {
      state.list = val
    },
  },
}
