<template>
  <v-dialog width="500px" v-model="isDialogVisible" persistent scrollable>
    <v-card>
      <v-card-title>{{ title }}</v-card-title>
      <v-divider></v-divider>
      <v-card-text class="px-5 py-0">
        <v-form ref="form" v-model="valid" class="px-4 py-4">
          <v-col class="px-0 pt-0 pb-5 d-flex">
            <div class="mt-3 text-sm w-20">
              {{ $t('organization.headers.code') }}
            </div>
            <v-text-field
              v-model="query.departmentId"
              :label="$t('organization.headers.code')"
              color="primary"
              outlined
              dense
              :rules="IdRules"
              hide-details="auto"
            ></v-text-field>
          </v-col>
          <v-col class="px-0 pt-0 pb-5 d-flex">
            <div class="mt-3 text-sm w-20">
              {{ $t('organization.headers.name') }}
            </div>
            <v-text-field
              v-model="query.departmentName"
              :label="$t('organization.headers.name')"
              color="primary"
              outlined
              dense
              :rules="nameRules"
              hide-details="auto"
            ></v-text-field>
          </v-col>
          <v-col v-if="query.type == '1'" class="px-0 pt-0 pb-5 d-flex">
            <div class="mt-3 text-sm w-20">
              {{ $t('organization.headers.manager') }}
            </div>
            <!-- <v-text-field
              v-model="query.directorId"
              label="部门负责人"
              color="primary"
              outlined
              dense
              hide-details="auto"
            ></v-text-field> -->
            <v-autocomplete
              v-model="query.directorId"
              dense
              :menu-props="{ offsetY: true, maxHeight: 300 }"
              append-icon="mdi-chevron-down"
              hide-details="auto"
              outlined
              :items="userData"
              :label="$t('organization.headers.manager')"
              item-text="userName"
              item-value="userId"
              :loading="isLoading"
              :search-input.sync="search"
              no-filter
              clearable
            />
            <!-- <el-select
              v-model="query.directorId"
              filterable
              remote
              reserve-keyword
              placeholder="部门负责人"
              :remote-method="getUsers"
              :loading="searchLoading"
              class="w-80"
              clearable
              @clear="clear"
            >
              <el-option
                v-for="item in userData"
                :key="item.userId"
                :label="item.userName"
                :value="item.userId"
                style="width: 342px"
              >
              </el-option>
            </el-select> -->
          </v-col>
          <v-col class="px-0 pt-0 pb-5 d-flex">
            <div class="mt-3 text-sm w-20">
              {{ $t('organization.headers.parent') }}
            </div>
            <v-text-field
              :label="totalName"
              color="primary"
              outlined
              dense
              disabled
              hide-details="auto"
            ></v-text-field>
          </v-col>
          <v-col class="px-0 pt-0 pb-5 d-flex">
            <div class="mt-3 text-sm w-20">
              {{ $t('organization.headers.DingTalkId') }}
            </div>
            <v-text-field
              v-model="query.dingTalk"
              :label="$t('organization.headers.DingTalkId')"
              color="primary"
              outlined
              dense
              hide-details="auto"
            ></v-text-field>
          </v-col>
        </v-form>
      </v-card-text>

      <v-divider></v-divider>

      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn
          class="px-6 py-2 font-weight-normal btn-secondary btn-outline-secondary"
          depressed
          outlined
          @click="close"
        >
          <span class="action-btn"> {{ $t('action.cancel') }}</span>
        </v-btn>
        <v-btn
          class="ml-4 px-6 py-2 btn-primary"
          color="primary"
          :loading="confirmLoading"
          @click="comfirm"
        >
          {{ $t('action.confirm') }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
import { findUsers } from '@/api/system/user'

export default {
  data() {
    return {
      valid: true,
      isDialogVisible: false,
      confirmLoading: false,
      nameRules: [
        v =>
          !!v ||
          this.$t('validation.required', [
            this.$t('organization.headers.name'),
          ]),
      ],
      IdRules: [
        v =>
          !!v ||
          this.$t('validation.required', [
            this.$t('organization.headers.code'),
          ]),
      ],
      userData: [],
      // searchLoading: false,
      isLoading: false,
      search: null,
      title: '',
      totalName: '',
      query: {
        isParent: '',
        type: '',
        departmentId: '',
        departmentName: '',
        directorId: '',
        state: 0,
        dingTalk: '',
      },
    }
  },
  watch: {
    search(val) {
      if (this.isLoading) return
      this.getUsers(val)
    },
  },
  methods: {
    open(item, fn) {
      this.title = item.title
      this.totalName = item.totalName
      this.query.isParent = item.isParent
      this.query.type = item.type
      this.query.departmentId = ''
      this.query.departmentName = ''
      this.query.directorId = ''
      this.query.dingTalk = ''
      this.confirmLoading = false
      this.getUsers()
      this.$nextTick(() => {
        this.isDialogVisible = true
        this.$refs.form && this.$refs.form.resetValidation()
        this.callback = fn
      })
    },
    // clear() {
    //   this.query.directorId = ''
    //   this.getUsers()
    // },
    // 获取用户信息
    async getUsers(val) {
      const params = {
        pageNum: 1,
        pageSize: 20,
        userName: val || '',
      }
      try {
        this.isLoading = true
        const res = await findUsers(params)
        this.userData = res.data.records || []
      } catch (e) {
        console.error(`获取所有用户信息：${e}`)
      } finally {
        this.isLoading = false
      }
    },
    dealName(type) {
      return type == '0'
        ? this.$t('organization.headers.company')
        : this.$t('organization.headers.department')
    },
    comfirm() {
      const bool = this.$refs.form.validate()
      if (!bool) return
      this.confirmLoading = true
      this.callback(this.query)
    },
    close() {
      this.isDialogVisible = false
      this.confirmLoading = false
    },
  },
}
</script>
