const vulnerability = {
  currentTitle: 'Vulnerabilities',
  headers: {
    cnnvdId: 'CNNVD ID',
    cnvdId: 'CNVD ID',
    cveId: 'CVE ID',
    cvss: 'CVSS Score',
    vulnerabilityName: 'Title',
    alarmLevel: 'Severity',
    loopholeType: 'Type',
    recordTime: 'Submission Time',
    publicTime: 'Public Time',
    createTime: 'Synchronised Time',
    updateTime: 'Update Time',
    dataSource: 'Channel',
    entryInfo: 'Synchronization Information',
    idInfo: 'ID',
    vulnerInofo: 'Vulnerability Information',
    collectionTime: 'Submission Public Time',
    updateInfo: 'Update Information',
    findUser: 'Finder',
    findDate: 'Find Date',
    vulnerId: 'Vulnerability lD',
    affectManufacturers: 'Affected Vendor',
  },
  vulnerabilityDate: 'Vulnerability Time',
  tab2: 'Lab Vulnerabilities',
  template: {
    hint: 'Download the vulnerability asset import template',
    text: 'Vulnerability Asset Import Template',
  },
  edit: {
    title2: 'Affested Product',
    title3: 'Vulnerability Details',
    title4: 'Add Lab Vulnerabilities',
    title5: 'Edit Lab Vulnerabilities',
    title6: 'Lab Vulnerability Details',
    hint: 'eg: xxxx website/xxxx system/xxxx platform/xxxx',
    url: 'URL',
    desc: 'Description',
    solution: 'Solution',
    file: 'Attachments',
  },
  tip: 'Components and version numbers affected by the vulnerability',
  repair: 'Repair Plan',
  cvssInfo: 'CVSS information',
  cvssHint: 'Please refer to the score of CVE',
  link: 'Reference Link',
  patch: 'Patch Information',
  cveLink: 'CVE Link',
  swal: {
    del: {
      title: 'Delete Lab Vulnerabilities',
      text: 'Are you sure you want to delete Lab Vulnerability: {0}?',
    },
  },

  cve: {
    headers: {
      desc: 'Descriptions',
      v3: 'CVSS Version 3.x',
      v2: 'CVSS Version 2.x',
      publishedDate: 'NVD Published Date',
    },
    drawer: {
      cweId: 'CWE ID',
      isLink: 'Contains Hyperlinks',
      version: 'CVSS Metrics',
      range: 'Severity Score Range',
      lastUpdateDate: 'NVD Last Modified',
    },
    detail: {
      state: 'State',
      metric: 'Severity',
      affectedRange: 'Affected',
      product: 'Product',
      vendor: 'Manufacturer',
      version: 'Version',
      type: 'Weakness Enumeration',
      hint: 'Affected Manufacturers/products and versions',
    },
  },
  cnnvd: {
    updateDate: 'Update Time',
    publicDate: 'Public Time',
    updatePublicTime: 'Update Public Time',
  },

  cavd: {
    publicTime: 'Public Time',
    info: 'CAVD Information',
    id: 'CAVD ID',
    publicDate: 'Public Date',
    repair: 'Repair Suggestions',
    verify: 'Verify Message',
    synchronizer: 'Synchronizer',
  },
}

export default vulnerability
