<template>
  <div class="box">
    <div class="box-header">
      {{ $t('screen.ticketing')
      }}<span class="box-header-num">{{
        ticketPending.count | numberToFormat
      }}</span>
    </div>
    <v-row
      no-gutters
      class="box-chart d-flex justify-space-around w-100"
      style="padding-top: 4%"
    >
      <v-col
        v-for="(item, index) in option"
        :key="index"
        cols="auto"
        class="text-center d-flex flex-column align-center w-25"
      >
        <!-- <div class="fs-12">新增{{ item.addNum | num }}</div> -->
        <div
          class="flex-1 align-center w-100"
          style="padding: 9.5% 10% 13.5% 10%"
        >
          <div class="h-100 d-flex align-center w-100 dv9">
            <dv-decoration-9 :style="dvStyle">{{ item.count }}</dv-decoration-9>
          </div>
        </div>

        <div
          class="font-weight-medium fs-16"
          :style="'color:' + item.color"
          style="padding-top: 5%; padding-bottom: 10%"
        >
          {{ item.alarmLevelName }}
        </div>
      </v-col>
    </v-row>
    <!-- <div class="box-chart d-flex justify-space-around mt-per-5">
      <template v-for="(item, index) in option">
        <div
          :key="index"
          class="text-center d-flex flex-column align-center"
          @click="goAlert(item)"
        >
          <div class="pb-2 fs-12">新增{{ item.addNum | num }}</div>
          <div class="flex-1 align-center">
            <div class="h-100 d-flex align-center">
              <dv-decoration-9 style="height: 85px; width: 85px">{{
                item.count
              }}</dv-decoration-9>
            </div>
          </div>
          <div
            class="pt-3 font-weight-medium fs-16 mb-5"
            :style="'color:' + item.color"
          >
            {{ item.alarmLevelName }}
          </div>
        </div>
      </template>
    </div> -->
  </div>
</template>

<script>
import { getRoundSize } from './chart'
const option = [
  {
    addNum: 10,
    count: 55,
    alarmLevel: '0',
    alarmLevelName: 'P0',
    color: '#FF385D',
  },
  {
    addNum: 8,
    count: 21,
    alarmLevel: '1',
    alarmLevelName: 'P1',
    color: '#FF6B00',
  },
  {
    addNum: 15,
    count: 103,
    alarmLevel: '2',
    alarmLevelName: 'P2',
    color: '#F0DA4C',
  },
  {
    addNum: 22,
    count: 47,
    alarmLevel: '3',
    alarmLevelName: 'P3',
    color: '#32FDB8',
  },
]
export default {
  name: 'RightChart2',
  props: {
    ticketPending: {
      type: Object,
      default: () => {
        return {
          count: 0,
          levelData: [],
        }
      },
    },
  },
  computed: {
    dvStyle() {
      const len = getRoundSize(100)
      return {
        height: len + 'px',
        width: len + 'px',
      }
    },
    option() {
      let ticketLevel = this.$store.state.enums.enums.TicketLevel
      if (!ticketLevel || ticketLevel.length === 0) {
        return []
      }
      return this.ticketPending.levelData.map(item => {
        return {
          count: item.count,
          alarmLevel: item.level,
          alarmLevelName: ticketLevel[item.level]
            ? ticketLevel[item.level].text.slice(0, 2)
            : item.level,
          color: ticketLevel[item.level]
            ? ticketLevel[item.level].color
            : '#0F7EFF',
        }
      })
    },
  },
  methods: {
    goAlert(item) {
      this.$router.push({
        path: '/alerts',
        query: { isQuery: 1, alarmLevelList: [item.alarmLevel] },
      })
    },
  },
}
</script>
