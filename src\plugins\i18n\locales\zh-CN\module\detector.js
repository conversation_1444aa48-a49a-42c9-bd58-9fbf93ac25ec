const detector = {
  currentTitle: '检测',
  headers: {
    name: '名称',
    description: '描述',
    detectorRange: '检测范围',
    detectorType: '检测类型',
    severity: '严重等级',
    active: '状态',
    detectorClassifyNameList: '标签',
    createUser: '所有者',
    groups: '适用资产',
    disposal: '触发处置',
    time: '聚合时间',
    aggregationtime: '告警降噪',
    period: '检测周期',
    threshold: '检测阈值',
    logic: '检测逻辑',
    actions: '处置流程',
    signals: '调查信号',
    config: '告警信息',
    parameter: '参数',
  },
  allText: '检测数据',
  btn: {},
  swal: {
    text: '确认删除检测:{0}?',
    add: '新增成功！',
    edit: '修改成功！',
    del: {
      title: '删除检测',
      text: '是否确认删除检测：{0}？',
    },
    batchDel: {
      title: '批量删除检测',
      text: '是否确认删除检测共{0}条？',
    },
    batchExport: {
      title: '批量导出检测',
      text: '是否确认导出检测共{0}条？',
    },
    hint: {
      del: '请先选择需要删除的检测！',
      batchDel: '批量删除检测成功！',
      allDel: '全部删除检测成功！',
      export: '请先选择需要导出的检测！',
      batchExport: '批量导出检测成功！',
    },
  },
  hint: {
    del: '删除成功',
    tip: '尚未配置处置流程',
    tip1: '系统将自动根据严重等级匹配预先设置的处置流程',
    tip2: '测试模式下由检测器触发的警报不会影响资产态势且不会展示在仪表板中',
    tip3: '检测器类型选择推荐：对于经常会达到(>100次/每小时)的场景，强烈建议选择“高频”',
    tip4: '警报：低频检测(<100次/小时)，支持单个告警在平台上调查',
    tip5: '高频：高频检测(>100次/小时)，不支持在平台上调查，通过Kafka集成后完成后续调查',
    tip6: '多资产检测器在配置的“检测周期”内，当受影响资产数量超过“检测阈值”时触发告警',
    tip7: '该检测器将在配置的聚合时间内对同一资产检测到的这类违规聚合为一个警报',
    tip8: '聚合时间应该在0~1440分钟内',
    tip9: '指定触发告警的受影响资产的最小数',
    tip10: '请选择处置方式，高频只能选择Kafka处置流程',
    tip11: '资产组',
    tip12: '已选择的信号',
    tip13: '由此检测器触发的警报将具有以下描述，可在描述中添加动态信号',
    tip14: '最多选择30条',
    tip15: '请选择资产组',
    tip16: '请选择',
    tip17: '检测阈值应该大于0',
    tip18: '你未选择任何处置流程，是否确认？',
  },
  detail: {
    min: '分钟',
    match: '预置匹配',
    disposition: '自定义处置',
    selected: '已配置流程',
    type: '适用类型',
    recommed: '推荐信号',
    all: '全部信号',
    name: '信号名称',
    modelType: '模型类型',
    signal: '详细信号',
    tip: '已触发告警总量',
    warn: '警告',
    tip1: '该检测规则已触发[{0}]条告警，是否更新这些告警信息?',
  },
  dialog: {
    disposition: '选择处置流程',
    name: '处置名称',
    description: '描述',
    disposeTypeName: '处置类型',
    active: '状态',
  },
  hover1: '若存在资产的CPE重复，则保留一条',
  hover2: '若资产的CPE为空，则不导出',
  fileHint: '单个附件大小不超过5M；附件格式要求：json',
}

export default detector
