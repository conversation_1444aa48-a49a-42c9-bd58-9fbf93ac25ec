export const tooltip = {
  trigger: 'axis',
  axisPointer: {
    type: 'line',
  },

  // formatter: '{a}: {c}',
  textStyle: {
    color: '#fafafa',
  },
  borderColor: 'transparent',
  backgroundColor: 'rgba(0, 0, 0, 0.5)',
  extraCssText: 'backdrop-filter: blur(6px);',
}

export const grid = {
  top: '20%',
  left: '2%',
  right: '2.5%',
  bottom: '0%',
  containLabel: true,
}

export const legend = {
  show: true,
  right: 20,
  icon: 'roundRect',
  itemHeight: 5,
  itemWidth: 10,
  itemGap: 40,
  textStyle: {
    fontSize: '1rem',
  },
  itemStyle: {
    borderColor: 'transparent',
    borderWidth: 6,
  },
}

// 线性渐变
const seriesCommonFn = (name, data, color, color0, width, time) => {
  return {
    name: name,
    data: data,
    type: 'line',
    smooth: true,
    showSymbol: false,
    // symbol: 'none',
    symbolSize: 10,
    emphasis: { focus: 'series' },
    animationDuration: time || 2500,
    animationEasing: 'cubicInOut',
    lineStyle: {
      width: width || 6,
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 1,
        y2: 0,
        colorStops: [
          // linear-gradient(89.21deg, rgba(83, 61, 241, 0) -0.8%, #533DF1 51.74%, rgba(83, 61, 241, 0) 99.08%);
          {
            offset: 0 / 100,
            color: color0 || 'rgba(83, 61, 241, 0)',
          },
          {
            offset: 50 / 100,
            color: color,
          },
          {
            offset: 100 / 100,
            color: color0 || 'rgba(83, 61, 241, 0)',
          },
        ],
        global: false, // 缺省为 false
      },
    },
  }
}

import { format, subHours } from 'date-fns'
const xAxisFn = (xData, forma) => {
  return [
    {
      type: 'category',
      boundaryGap: false,
      data: xData,
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: {
        show: true,
        // rotate: 35, //35度角倾斜显示
        formatter: name => {
          return format(new Date(name), forma || 'HH:mm')
        },
      },
      axisPointer: {
        show: true,
        label: {
          formatter: obj => {
            let name = obj.value
            let start = format(subHours(new Date(name), 1), forma || 'HH:mm')
            let end = format(new Date(name), forma || 'HH:mm')
            return start === end ? end : `${start}~${end}`
          },
        },
      },
    },
  ]
}
// x--时间坐标轴 线性渐变
export const linearGradient = {
  seriesCommonFn,
  xAxisFn,
}
