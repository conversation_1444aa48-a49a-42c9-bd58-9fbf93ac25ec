<template>
  <div class="border-box cursor-pointer" @click="$emit('click')">
    <template v-if="!disabled">
      <div class="left-top-radius"></div>
      <div class="right-top-radius"></div>
      <div class="left-bottom-radius"></div>
      <div class="right-bottom-radius"></div>
    </template>
    <div
      :class="{ 'opacity-4': disabled }"
      class="w-100 h-full d-flex align-center"
    >
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DvBorderBox14',
  props: {
    disabled: {
      type: Boolean,
      default: () => false,
    },
  },
}
</script>
