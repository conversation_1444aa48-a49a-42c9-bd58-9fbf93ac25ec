import Notify from '@/components/notify'
import { i18n } from '@/plugins/i18n'
import store from '@/store'
import themeConfig from '@themeConfig'
import axios from 'axios'
import JSONbig from 'json-bigint'
import Vue from 'vue'
import router from '../router'
import { getToken, removeToken } from './token'
const vm = new Vue()
const JSONbigToString = JSONbig({ storeAsString: true })

const service = axios.create({
  timeout: 50000,

  // 处理大数字精度丢失问题
  transformResponse: [
    function (data) {
      try {
        // 大数字转换为String
        return JSONbigToString.parse(data)
      } catch (e) {
        // 转换失败的情况下
        return data
      }
    },
  ],
})

let cancelToken = axios.CancelToken
service.interceptors.request.use(
  config => {
    // config.cancelToken = new cancelToken(c => {
    //   console.log('config.url', config.url)
    //   if (config.url.indexOf('ak') > -1) {
    //     config.baseURL = 'https://api.map.baidu.com'
    //     console.log('cancelToken', c)
    //   }
    // })

    if (i18n.locale) {
      config.headers['Accept-Language'] = i18n.locale
    }
    if (config.url.includes('/user/login')) {
      return config
    }
    const token = getToken()
    if (token) {
      config.headers.Authorization = token
    }
    const flag = themeConfig.pageLoading.find(v => config.url.indexOf(v) !== -1)
    if (flag || config.loading) {
      vm.$showLoading()
    }
    return config
  },
  err => {
    vm.$hideLoading()
    Promise.resolve(err)
  },
)

service.interceptors.response.use(
  resp => {
    const flag = themeConfig.pageLoading.find(
      v => resp.config.url.indexOf(v) !== -1,
    )
    if (flag || resp.config.loading) {
      vm.$hideLoading()
    }

    // 未设置状态码则默认成功状态
    const code = resp.data?.code || 200

    // 二进制数据
    if (
      resp.request.responseType === 'blob' ||
      resp.request.responseType === 'arraybuffer'
    ) {
      if (
        resp.request.responseURL.includes('/vehicleAsset/downloadBOM') ||
        resp.request.responseURL.includes('/featureModel/exportAnalyseDetails')
      ) {
        return Promise.resolve(resp)
      } else {
        return Promise.resolve(resp.data)
      }
    }
    if (resp.request.responseURL.includes('type=GET&url=/analysis')) {
      return Promise.resolve(resp.data)
    }
    if (resp.request.responseURL.includes('type=GET&url=/verify/cpe')) {
      return Promise.resolve(resp.data)
    }
    if (code === 601 || code === 602) {
      return Promise.resolve(resp.data)
    }
    let msg = resp.data?.msg
    let bool = false
    switch (code) {
      case 302:
        msg = msg || i18n.t('global.request.code302')
        // token已过期，清空相关信息
        // localStorage.removeItem('token')
        removeToken()
        store.commit('setToken', '')
        store.commit('setUserInfo', { userName: '' })
        Notify.info('error', msg)

        // 刷新页面
        // location.reload()
        router.push({ name: 'auth-login' })
        break
      case 400:
        msg = msg || i18n.t('global.request.code400')
        break
      case 401:
        msg = msg || i18n.t('global.request.code401')
        break
      case 403:
        msg = msg || i18n.t('global.request.code403')
        break
      case 404:
        msg = msg || i18n.t('global.request.code404')
        break
      case 405:
        msg = msg || i18n.t('global.request.code405')
        break
      case 500:
        msg = msg || i18n.t('global.request.code500')
        break
      case 200:
        bool = true
        break
      default:
        msg = msg || i18n.t('global.request.codeDefault')
        break
    }
    if (bool) {
      return Promise.resolve(resp.data)
    }
    Notify.info('error', msg)

    return Promise.reject(msg)
  },
  err => {
    vm.$hideLoading()
    console.error('=======error=======', err)
    const status = err?.response?.status
    let { message } = err
    if (!status) {
      Notify.info('error', message)

      return Promise.reject(err)
    }
    if (status === 401 && err.request.responseURL.indexOf('/login') > 0) {
      Notify.info('error', i18n.t('global.request.err401'))
    } else if (status === 403) {
      Notify.info('error', i18n.t('global.request.err403'))
    } else {
      if (message == 'Network Error') {
        message = i18n.t('global.request.networkErr')
      } else if (message.includes('timeout')) {
        message = i18n.t('global.request.timeout')
      } else if (message.includes('Request failed with status code')) {
        // message = `系统接口${message.substr(message.length - 3)}异常`
        message = i18n.t('global.request.fail', {
          name: message.substr(message.length - 3),
        })
      }
      Notify.info('error', message)
    }

    return Promise.reject(err)
  },
)

export const request = config => service(config)

export const postRequest = (url, params) =>
  service({
    method: 'post',
    url: `${url}`,
    data: params,
  })

export const postForm = (url, params) =>
  service({
    method: 'post',
    url: `${url}`,
    data: params,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    transformRequest: [
      data => {
        let ret = ''
        for (const it in data) {
          ret += `${encodeURIComponent(it)}=${encodeURIComponent(data[it])}&`
        }

        return ret
      },
    ],
  })

export const postMultipart = (url, params) =>
  service({
    method: 'post',
    url: `${url}`,
    data: params,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })

export const putRequest = (url, params) =>
  service({
    method: 'put',
    url: `${url}`,
    data: params,
  })

export const deleteRequest = (url, params) =>
  service({
    method: 'delete',
    url: `${url}`,
    params,
  })

export const getRequest = (url, params) =>
  service({
    method: 'get',
    url: `${url}`,
    params,
  })

// 代理路径
export const vsocPath =
  process.env.NODE_ENV === 'development' ? '/vsoccatarcdev' : '/vsoccatarctest'
export const cmdbPath = '/vsoc/ac'
