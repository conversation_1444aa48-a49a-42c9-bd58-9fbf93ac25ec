<template>
  <div class="auth-wrapper auth-v1">
    <div class="auth-inner">
      <v-card class="auth-card">
        <v-card-title class="d-flex align-center justify-center py-7">
          <router-link to="/" class="d-flex align-center">
            <v-img
              :src="appLogo"
              max-height="30px"
              max-width="30px"
              alt="logo"
              contain
              class="me-3"
            ></v-img>

            <h2 class="text-2xl font-weight-semibold">
              {{ appTitle }}
            </h2>
          </router-link>
        </v-card-title>

        <v-card-text>
          <p class="text-2xl font-weight-semibold text--primary mb-2">
            重置密码? 🔒
          </p>
          <p class="mb-2">请输入您的电子邮件，我们将向您发送重置密码的说明</p>
        </v-card-text>

        <!-- login form -->
        <v-card-text>
          <v-form ref="refForm">
            <v-row dense>
              <v-col cols="12">
                <v-text-field
                  v-model="form.email"
                  outlined
                  label="邮箱地址"
                  placeholder="<EMAIL>"
                  dense
                  :rules="[
                    rules.required(form.email, '邮箱地址'),
                    rules.emailValidator,
                  ]"
                ></v-text-field>
              </v-col>
              <v-col cols="8">
                <v-text-field
                  v-model.number="form.verificationCode"
                  type="number"
                  outlined
                  dense
                  label="验证码"
                  :rules="[
                    rules.required(form.verificationCode, '验证码'),
                    rules.integerValidator,
                  ]"
                ></v-text-field>
              </v-col>
              <v-col cols="4">
                <v-btn outlined :loading="isVerificationLoading">{{
                  verificationLabel
                }}</v-btn>
              </v-col>
              <v-col cols="12">
                <v-text-field
                  outlined
                  dense
                  v-model="form.password"
                  :type="isPasswordVisible ? 'text' : 'password'"
                  :append-icon="
                    isPasswordVisible
                      ? icons.mdiEyeOffOutline
                      : icons.mdiEyeOutline
                  "
                  label="新密码"
                  hint="密码长度不能少于8位数"
                  persistent-hint
                  :rules="[
                    rules.required(form.password, '密码'),
                    rules.min(form.password, '密码'),
                  ]"
                  @click:append="isPasswordVisible = !isPasswordVisible"
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-text-field
                  v-model="form.cPassword"
                  outlined
                  dense
                  class="mt-3"
                  :type="isPasswordVisible ? 'text' : 'password'"
                  :append-icon="
                    isPasswordVisible
                      ? icons.mdiEyeOffOutline
                      : icons.mdiEyeOutline
                  "
                  label="确认新密码"
                  :rules="[
                    rules.required(form.cPassword, '密码'),
                    rules.min(form.cPassword, '密码'),
                    rules.confirmedValidator(form.cPassword, form.password),
                  ]"
                  @click:append="isPasswordVisible = !isPasswordVisible"
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-btn
                  block
                  color="primary"
                  @click="onSubmit"
                  :loading="isBtnLoading"
                >
                  确定
                </v-btn>
              </v-col>
            </v-row>

            <!-- <v-btn block outlined> 返回 </v-btn> -->
          </v-form>
        </v-card-text>

        <v-card-actions class="d-flex justify-center align-center">
          <router-link
            :to="{ name: 'login' }"
            class="d-flex align-center text-sm"
          >
            <v-icon size="24" color="primary">
              {{ icons.mdiChevronLeft }}
            </v-icon>
            <span>返回登录页面</span>
          </router-link>
        </v-card-actions>
      </v-card>
    </div>

    <!-- background triangle shape  -->
    <img
      class="auth-mask-bg"
      height="190"
      :src="
        require(`@/assets/images/misc/mask-${
          $vuetify.theme.dark ? 'dark' : 'light'
        }.png`)
      "
    />

    <!-- tree -->
    <v-img
      class="auth-tree"
      width="247"
      height="185"
      src="@/assets/images/misc/tree.png"
    ></v-img>

    <!-- tree  -->
    <v-img
      class="auth-tree-3"
      width="377"
      height="289"
      src="@/assets/images/misc/tree-3.png"
    ></v-img>
  </div>
</template>

<script>
import {
  confirmedValidator,
  emailValidator,
  integerValidator,
  min,
  required,
} from '@core/utils/validation'
import { mdiChevronLeft, mdiEyeOffOutline, mdiEyeOutline } from '@mdi/js'
import themeConfig from '@themeConfig'
import { ref } from '@vue/composition-api'
export default {
  setup() {
    const isPasswordVisible = ref(false)
    const form = ref({
      email: '',
      verificationCode: undefined,
      password: '',
      cPassword: '',
    })
    let verificationLabel = ref('获取验证码')
    let isBtnLoading = ref(false)
    let isVerificationLoading = ref(false)
    const refForm = ref(null)
    const onSubmit = () => {
      refForm.value.validate()
    }

    return {
      isBtnLoading,
      isVerificationLoading,
      isPasswordVisible,
      form,
      refForm,
      verificationLabel,
      // themeConfig
      appTitle: themeConfig.app.title,
      appLogo:
        require('@/assets/images/svg/logo-primary.svg') || themeConfig.app.logo,

      icons: {
        mdiChevronLeft,
        mdiEyeOffOutline,
        mdiEyeOutline,
      },
      rules: {
        required,
        emailValidator,
        min,
        confirmedValidator,
        integerValidator,
      },
      onSubmit,
    }
  },
}
</script>

<style lang="scss">
@import '@core/preset/preset/pages/auth.scss';
</style>
