<template>
  <!-- 定制化图形3 资产统计-->
  <div class="box h-100">
    <div class="box-header">{{ title }}</div>
    <v-row
      no-gutters
      style="padding-top: 2%"
      class="box-chart d-flex justify-space-around w-100 px-0 pb-0 ma-0"
    >
      <v-col
        v-for="(item, index) in option"
        :key="index"
        cols="auto"
        class="text-center d-flex flex-column justify-space-around pa-0 ma-0"
      >
        <div class="flex-1 align-center w-100 justify-center">
          <div class="h-100 d-flex align-center w-100 dv9">
            <dv-decoration-9
              v-show-tips="item.value"
              :ref="'dv9' + index"
              :style="dvStyle"
            >
              <span
                class="card-item-title-text"
                :style="{
                  color: item.riskType
                    ? riskColor[item.riskType] || '#44E2FE'
                    : Number(item.type) === 1
                    ? '#32FDB8'
                    : Number(item.type) === 2
                    ? '#FF385D'
                    : '#44E2FE',
                }"
                >{{ numberToFormat(item.value) }}</span
              >
            </dv-decoration-9>
          </div>
        </div>

        <div class="font-weight-medium fs-14 ml-n2">
          {{ item.type ? $t('global.' + item.name) : item.name }}
        </div>
      </v-col>
    </v-row>
  </div>
</template>

<script>
import { numberToFormat } from '@/util/filters'
import { riskColor } from '../util/defaultPreview'
import { getRoundSize } from './chart'

export default {
  name: 'CardItem3',
  props: {
    title: {
      type: String,
      default: '',
    },
    list: {
      type: Array,
      default: () => {
        return []
      },
    },
    isFull: {
      type: Boolean,
      default: false,
    },
  },
  components: {},
  computed: {
    dvStyle() {
      const len = this.isFull ? getRoundSize(65) : getRoundSize(80)
      return {
        height: len + 'px',
        width: len + 'px',
      }
    },
    option() {
      return this.list
    },
  },

  data() {
    return {
      numberToFormat,
      riskColor,
    }
  },
  mounted() {
    setTimeout(() => {
      this.option.forEach((v, index) => {
        if (this.$refs['dv9' + index]) {
          this.$refs['dv9' + index][0].initWH()
        }
      })
    })
  },
  methods: {
    getRoundSize,
  },
}
</script>
<style lang="scss" scoped></style>
