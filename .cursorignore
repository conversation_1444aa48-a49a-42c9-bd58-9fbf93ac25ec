# Add directories or file patterns to ignore during indexing (e.g. foo/ or *.csv)

scripts
node_modules
.DS_Store
dist
dist-ssr
*.local
public/

/src/@core/
/src/api/
/src/assets/
/src/components/
/src/core/
/src/helpers/
/src/hooks/
/src/layouts/
/src/plugins/
/src/services/
/src/store/
/src/styles/
/src/util/
/src/widgets/
/src/messages/
/src/mixins/
/src/models/
/src/router/
/src/services/
/src/services/
/src/router/
/src/navigation/
/src/views/ai/
/src/views/auth/
/src/views/dashboards/
/src/views/data-big-screen/

/src/views/handle/

/src/views/swbom/
/src/views/system/
/src/views/intelligence/
/src/views/common
/src/views/collection/
/src/views/analyse/
/src/views/message/
/src/views/parse/
/src/views/record/
/src/views/report/
/src/views/simulator/
/src/views/ticket1/
/src/views/log/

*.env.*
*.MD
*.md
*.txt
*.json
*.yaml
*.yml
*.csv
*.xls
*.docx
*.doc
*.pptx
*.ppt
*.pdf
*.zip
*.rar

APP.vue
# main.js
router.js

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*


vue.config.js
themeConfig.js
license.md
postcss.config.js
tailwind.config.js
tsconfig.json

/cypress/videos/
/cypress/screenshots/


# 👉 Custom Git ignores

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/*.code-snippets
!.vscode/tours
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.yarn

# iconify dist files
src/plugins/iconify/icons.css

# Ignore MSW script
public/mockServiceWorker.js

# Env files
.env*
!.env.example
