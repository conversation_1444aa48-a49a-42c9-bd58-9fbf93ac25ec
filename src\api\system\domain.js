import { request, vsocPath } from '../../util/request'

export const getDomainList = function (data) {
  return request({
    url: `${vsocPath}/domain/domains`,
    method: 'post',
    data,
  })
}

export const addDomain = function (data) {
  return request({
    url: `${vsocPath}/domain/addDomain`,
    method: 'post',
    data,
  })
}

export const editDomain = function (data) {
  return request({
    url: `${vsocPath}/domain/updateDomain`,
    method: 'post',
    data,
  })
}
