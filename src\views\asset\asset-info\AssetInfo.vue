<template>
  <v-row class="ma-0">
    <v-col cols="12">
      <v-card class="bg-gradient-secondary px-4 py-2 assets-info">
        <v-card-text>
          <!-- 背景纹理 -->
          <v-img
            src="@/assets/images/svg/waves-white2.svg"
            class="position-absolute opacity-4 start-0 top-0 w-100 card-shadow"
            style="
              background-position: center center !important;
              background-repeat: no-repeat !important;
            "
          ></v-img>
          <v-row>
            <!-- 资产左侧信息 -->
            <v-col cols="3">
              <div class="d-flex flex-column align-center justify-center">
                <div class="w-100 text-start mb-10">
                  <span class="text-xl white--text opacity-9"> 资产编号 </span>
                  <hr class="horizontal light mt-1 mb-4" />
                  <div class="d-flex justify-start">
                    <div class="w-100 mb-6">
                      <div
                        v-show-tips="assetsDetails.id"
                        class="text-base white--text font-weight-semibold cursor-pointer position-absolute d-flex align-center"
                      >
                        <div
                          class="text-overflow-hide h-full"
                          style="max-width: 12rem"
                        >
                          {{ assetsDetails.id }}
                        </div>
                        <v-btn
                          v-copy="assetsDetails.id"
                          v-show-tips="'复制'"
                          icon
                          class="white--text"
                        >
                          <!-- <v-icon size="1.2rem"> mdi-content-copy </v-icon> -->
                          <vsoc-icon
                            size="1.2rem"
                            type="fill"
                            icon="icon-fuzhi"
                          ></vsoc-icon>
                        </v-btn>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="w-100 text-start mb-10">
                  <span class="text-xl white--text opacity-9">
                    首次登记时间
                  </span>
                  <hr class="horizontal light mt-1 mb-4" />
                  <div class="d-flex justify-start">
                    <div class="w-100">
                      <span
                        class="text-overflow-hide text-base white--text font-weight-semibold"
                      >
                        {{ assetsDetails.regisDate | toDate }}
                      </span>
                    </div>
                  </div>
                </div>
                <div class="w-100 text-start">
                  <span class="text-xl white--text opacity-9">
                    最后接收时间
                  </span>
                  <hr class="horizontal light mt-1 mb-4" />
                  <div class="d-flex justify-start">
                    <div class="w-100">
                      <span
                        class="text-overflow-hide text-base white--text font-weight-semibold"
                      >
                        {{ assetsDetails.lastActiveDate | toDate }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </v-col>
            <!-- //车模型 -->
            <v-col cols="6">
              <!--  -->
              <div class="d-flex flex-column">
                <!-- src="@/assets/images/pages/model-m.png" -->
                <div class="w-100 text-center">
                  <v-img
                    contain
                    class="car-model"
                    :src="
                      assetsDetails.modelUrl ||
                      require('@/assets/images/pages/model-m.png')
                    "
                  />
                </div>
                <div class="w-100 text-center">
                  <span class="text-xl white--text opacity-9"> 资产组 </span>
                  <hr class="horizontal light mt-1 mb-4" />
                  <div class="d-flex justify-center">
                    <div>
                      <v-chip-group active-class="text-white" dark>
                        <v-chip
                          v-for="tag in assetsDetails.groupNameList"
                          :key="tag"
                          small
                        >
                          <span class="font-weight-semibold">{{ tag }}</span>
                        </v-chip>
                      </v-chip-group>
                    </div>
                    <!-- 第二版资产组 -->
                    <!-- <template>
                      <div class="text-center">
                        <v-menu offset-x :close-on-content-click="false" @input="onBlurGroupSelect">
                          <template v-slot:activator="{ on, attrs }">
                            <v-btn icon rounded color="#fff" width="1.5rem" height="1.5rem"
                              @click.stop="editGroupMember(assetsDetails)" v-bind="attrs" v-on="on">
                              <v-icon size="1.25rem" class="material-icons-round " dark>
                                mdi-plus-circle
                              </v-icon>
                            </v-btn>
                          </template>
                          <v-list dense>
                            <v-list-item v-for="(item, index) in groupMember.allGroups" :key="index">
                              <v-checkbox v-model="groupMember.selectGroups" :value="item.text" :label="item.text"></v-checkbox>
                            </v-list-item>
                          </v-list>
                        </v-menu>
                      </div>
                    </template> -->
                    <!-- 第一版资产组 -->
                    <!-- <v-btn
                      icon
                      rounded
                      color="#fff"
                      width="1.5rem"
                      height="1.5rem"
                      @click.stop="editGroupMember(assetsDetails)"
                    >
                      <v-icon size="1.25rem" class="material-icons-round" dark>
                        mdi-plus-circle
                      </v-icon>
                    </v-btn>
                    <template v-if="groupMember.showEditGroup">
                      <v-select
                        full-width
                        v-model="groupMember.selectGroups"
                        multiple
                        item-text="text"
                        item-value="text"
                        :items="groupMember.allGroups"
                        outlined
                        dense
                        hide-details="auto"
                        class="mb-4"
                        @blur="onBlurGroupSelect"
                      >
                      </v-select>
                    </template> -->
                    <!-- 资产组 -->
                    <template>
                      <v-dialog
                        max-width="45rem"
                        v-model="isDialogVisible"
                        scrollable
                      >
                        <template #activator="{ on, attrs }">
                          <v-btn
                            icon
                            rounded
                            color="#fff"
                            width="1.5rem"
                            height="1.5rem"
                            v-on="on"
                            v-bind="attrs"
                            @click.stop="editGroupMember(assetsDetails)"
                          >
                            <v-icon
                              size="1.25rem"
                              class="material-icons-round"
                              dark
                            >
                              mdi-plus-circle
                            </v-icon>
                          </v-btn>
                        </template>
                        <v-card>
                          <v-card-title>选择资产组</v-card-title>
                          <v-divider></v-divider>
                          <v-card-text>
                            <!-- <v-select
                              full-width
                              v-model="groupMember.selectGroups"
                              multiple
                              item-text="text"
                              item-value="text"
                              :items="groupMember.allGroups"
                              outlined
                              dense
                              hide-details="auto"
                              class="mt-4 mb-1"
                            >
                            </v-select> -->
                            <v-autocomplete
                              v-model="groupMember.selectGroups"
                              color="primary"
                              :items="groupMember.allGroups"
                              label="资产组"
                              multiple
                              outlined
                              hide-details
                              chips
                              :menu-props="{ offsetY: true }"
                              class="mt-5 mb-1"
                              item-text="text"
                              item-value="text"
                            >
                              <template v-slot:selection="{ item, index }">
                                <v-chip
                                  label
                                  color="primary"
                                  close
                                  @click:close="
                                    groupMember.selectGroups.splice(index, 1)
                                  "
                                >
                                  <span>{{ item.text }}</span>
                                </v-chip>
                              </template>
                            </v-autocomplete>
                          </v-card-text>

                          <v-divider></v-divider>

                          <v-card-actions>
                            <v-spacer></v-spacer>
                            <v-btn
                              outlined
                              depressed
                              @click="isDialogVisible = false"
                            >
                              取消
                            </v-btn>
                            <v-btn
                              color="primary"
                              :loading="confirmLoading"
                              @click="onBlurGroupSelect"
                            >
                              确认
                            </v-btn>
                          </v-card-actions>
                        </v-card>
                      </v-dialog>
                    </template>
                  </div>
                </div>
              </div>
            </v-col>

            <!-- 资产右侧信息 -->
            <v-col cols="3">
              <div class="d-flex flex-column">
                <div class="w-100 text-start mb-10">
                  <span class="text-xl white--text opacity-9"> 当前态势 </span>
                  <hr class="horizontal light mt-1 mb-4" />
                  <div class="d-flex justify-start">
                    <div
                      class="w-100 d-flex align-center white--text text-base font-weight-semibold text-overflow-hide text-start"
                    >
                      <v-icon
                        :style="{
                          color: assetsDetails.healthStatus
                            ? carStatusMap[assetsDetails.healthStatus].color
                            : '',
                        }"
                        size="1.5rem"
                      >
                        mdi-shield
                      </v-icon>
                      <span class="pl-2">{{
                        assetsDetails.healthStatus
                          ? carStatusMap[assetsDetails.healthStatus].text
                          : ''
                      }}</span>
                    </div>
                  </div>
                </div>
                <div class="w-100 text-start mb-10">
                  <span class="text-xl white--text opacity-9"> 告警状况 </span>
                  <hr class="horizontal light mt-1 mb-4" />
                  <div
                    class="d-flex justify-center white-space-nowrap overflow-auto"
                  >
                    <div
                      v-if="assetsDetails.healthStatus"
                      class="w-100 d-flex align-center text-white text-base font-weight-semibold alert-list"
                      @click="goAlert"
                    >
                      <div v-if="assetsDetails.assetAlarmInfoVos.length > 0">
                        <v-avatar
                          class="mr-2 icon-fb"
                          size="2.5rem"
                          v-for="(
                            alarmItem, index
                          ) in assetsDetails.assetAlarmInfoVos"
                          :key="index"
                          v-show-tips="
                            alarmLevel[alarmItem.alarmLevel] &&
                            alarmLevel[alarmItem.alarmLevel].text
                          "
                          :color="
                            alarmLevel[alarmItem.alarmLevel] &&
                            alarmLevel[alarmItem.alarmLevel].color
                          "
                        >
                          <span class="text-sm">{{
                            alarmItem.alarmCount
                          }}</span>
                        </v-avatar>
                      </div>
                      <div v-else>
                        {{ '' | dataFilter }}
                      </div>
                      <!-- <v-avatar class="mr-2 icon-fb" :color="$alertColor0" size="2.5rem">
                        <span v-show-tips="assetsDetails.alarmInfo.disasterNumber" class="text-sm">{{
                          assetsDetails.alarmInfo.disasterNumber | tranNumber(1)
                        }}</span>
                      </v-avatar>
                      <v-avatar class="mr-2 icon-fb" :color="$alertColor1" size="2.5rem">
                        <span v-show-tips="assetsDetails.alarmInfo.majorNumber" class="text-sm">{{
                          assetsDetails.alarmInfo.majorNumber | tranNumber(1)
                        }}</span>
                      </v-avatar>
                      <v-avatar class="mr-2 icon-fb" :color="$alertColor2" size="2.5rem">
                        <span v-show-tips="assetsDetails.alarmInfo.minorNumber" class="text-sm">{{
                          assetsDetails.alarmInfo.minorNumber | tranNumber(1)
                        }}</span>
                      </v-avatar>
                      <v-avatar class="mr-2 icon-fb" :color="$alertColor3" size="2.5rem">
                        <span v-show-tips="assetsDetails.alarmInfo.warningNumber" class="text-sm">{{
                          assetsDetails.alarmInfo.warningNumber | tranNumber(1)
                        }}</span>
                      </v-avatar> -->
                    </div>
                  </div>
                </div>
                <div class="w-100 text-start">
                  <span class="text-xl white--text opacity-9"> 最后位置 </span>
                  <hr class="horizontal light mt-1 mb-4" />
                  <div class="d-flex justify-start">
                    <!-- <v-tooltip bottom color="primary">
                      <template v-slot:activator="{ on, attrs }">
                        <span
                          class="text-base font-weight-semibold mb-0 text-white text-hide-2 cursor-pointer text-overflow-hide position-absolute"
                          v-bind="attrs"
                          v-on="on"
                          style="width: 18.75rem"
                        >
                          {{ address | dataFilter }}11111122222
                        </span>
                      </template>
                      <span>{{ address | dataFilter }}</span>
                    </v-tooltip> -->
                    <div
                      v-show-tips="address"
                      style="width: 18.75rem"
                      class="text-base font-weight-semibold mb-0 text-white text-hide-2 cursor-pointer text-overflow-hide position-absolute"
                    >
                      {{ address | dataFilter }}
                    </div>
                  </div>
                </div>
              </div>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-col>

    <!-- 控制器详情 -->
    <v-col cols="12">
      <v-row>
        <v-col cols="3">
          <v-card class="bg-gradient-secondary px-4 py-4">
            <v-row no-gutters>
              <v-col sm="8" class="text-start">
                <p
                  class="text-white text-base mb-0 font-weight-normal text-capitalize font-weight-semibold opacity-7"
                >
                  T-BOX
                </p>
                <p
                  class="text-xl white--text font-weight-bolder mb-0 align-center"
                >
                  normal
                  <v-icon color="success" size="1.25rem">
                    mdi-shield-check-outline
                  </v-icon>
                </p>
              </v-col>
              <v-col sm="4" class="text-end">
                <v-avatar
                  color="white"
                  class="shadow-dark border-radius-xl v-avatar shadow rounded bg-white"
                  height="48px"
                  width="48px"
                >
                  <v-icon size="24px" color="primary" class="text-primary">
                    mdi-router-wireless-settings
                  </v-icon>
                </v-avatar>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <v-col cols="3">
          <v-card class="bg-gradient-secondary px-4 py-4">
            <v-row no-gutters>
              <v-col sm="8" class="text-start">
                <p
                  class="text-white text-base mb-0 font-weight-normal text-capitalize font-weight-semibold opacity-7"
                >
                  IVI
                </p>
                <p
                  class="text-xl white--text font-weight-bolder mb-0 align-center"
                >
                  normal
                  <v-icon color="success" size="1.25rem">
                    mdi-shield-check-outline
                  </v-icon>
                </p>
              </v-col>
              <v-col sm="4" class="text-end">
                <v-avatar
                  color="white"
                  class="shadow-dark border-radius-xl v-avatar shadow rounded bg-white"
                  height="48px"
                  width="48px"
                >
                  <v-icon size="24px" class="text-primary">
                    mdi-monitor
                  </v-icon>
                </v-avatar>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <v-col cols="3">
          <v-card class="bg-gradient-secondary px-4 py-4">
            <v-row no-gutters>
              <v-col cols="8" class="text-start">
                <p
                  class="text-white text-base mb-0 font-weight-normal text-capitalize font-weight-semibold opacity-7"
                >
                  网关
                </p>
                <p
                  class="text-xl white--text font-weight-bolder mb-0 align-center"
                >
                  normal
                  <v-icon color="success" size="1.25rem">
                    mdi-shield-check-outline
                  </v-icon>
                </p>
              </v-col>
              <v-col cols="4" class="text-end">
                <v-avatar
                  color="white"
                  class="shadow-dark border-radius-xl v-avatar shadow rounded bg-white"
                  height="48px"
                  width="48px"
                >
                  <v-icon size="24px" class="text-primary">
                    mdi-router-network
                  </v-icon>
                </v-avatar>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <v-col cols="3">
          <v-card class="bg-gradient-secondary px-4 py-4">
            <v-row no-gutters>
              <v-col cols="8" class="text-start">
                <p
                  class="text-white text-base mb-0 font-weight-normal text-capitalize font-weight-semibold opacity-7"
                >
                  域控-101
                </p>
                <p
                  class="text-xl danger--text font-weight-bolder mb-0 align-center"
                >
                  abnormal
                  <v-icon color="danger" size="1.25rem">
                    mdi-shield-alert-outline
                  </v-icon>
                </p>
              </v-col>
              <v-col cols="4" class="text-end">
                <v-avatar
                  color="white"
                  class="shadow-dark border-radius-xl v-avatar shadow rounded bg-white"
                  height="48px"
                  width="48px"
                >
                  <v-icon size="24px" class="text-primary"> mdi-lan </v-icon>
                </v-avatar>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
      </v-row>
    </v-col>
    <!-- 资产时间线 -->
    <v-col cols="12">
      <v-card class="pb-6">
        <div class="d-flex justify-space-between">
          <div class="d-flex">
            <!-- <v-avatar
              color="bg-gradient-primary"
              class="border-radius-xl mb-0 ml-4"
              height="4rem"
              width="4rem"
            > -->
            <v-icon size="1.5rem" class="ml-4"> mdi-chart-timeline </v-icon>

            <p class="ml-1 font-weight-semibold text--primary mt-4">
              资产事件轴
              <v-tooltip
                bottom
                transition="slide-x-transition"
                max-width="40rem"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-icon
                    :color="$bodyColor"
                    v-bind="attrs"
                    class="icon-hb"
                    size="1rem"
                    v-on="on"
                  >
                    mdi-information
                  </v-icon>
                </template>
                <span>Based on asset sent timestamps</span>
              </v-tooltip>
            </p>
          </div>
          <div class="ma-1">
            <v-btn icon v-show-tips="'刷新'" @click="onReload">
              <v-icon size="1.5rem" color="primary">mdi-reload</v-icon>
            </v-btn>
            <v-btn v-show-tips="'高级调查'" icon @click.stop="goAdvance">
              <v-icon size="1.5rem" color="primary">
                mdi-cloud-search-outline
              </v-icon>
            </v-btn>
          </div>
        </div>
        <div class="mt-4">
          <asset-time-line
            :time-line-data="timeLineData"
            time-line-type="last-dash-arrow"
            :is-no-data="timeLineNoData"
            @change="timeLineChange"
          ></asset-time-line>
        </div>
      </v-card>
    </v-col>
    <v-col cols="12">
      <template v-if="!timeLineNoData">
        <!-- 事件简要名称 -->
        <asset-event-detail
          :data="currentLineData"
          :assetType="assetsDetails.assetType"
        ></asset-event-detail>
        <!-- 最近位置 -->
        <asset-map :local-list="localList"></asset-map>
      </template>
    </v-col>
  </v-row>
</template>

<script>
import { addRelationByAssetId } from '@/api/asset/index'
// import { updateVehicleGroupMember } from '@/api/asset/vehicle-group-member'
import { toDate } from '@/util/filters'
import { cloneDeep } from 'lodash'
import AssetEventDetail from './components/AssetEventDetail.vue'
import AssetTimeLine from './components/AssetTimeLine.vue'
export default {
  name: 'AssetsInfo',
  components: {
    AssetTimeLine,
    AssetEventDetail,
  },
  props: {
    assetsDetails: Object,
    sn: String,
    date: Array,
  },
  data() {
    return {
      isRefreshError: false,
      confirmLoading: false,
      isDialogVisible: false,
      // TODO 临时使用
      assetsGroups: [],
      carStatusMap: this.$store.state.enums.enums.HealthStatus,
      // {
      //   fine: {
      //     color: this.$alertColor3,
      //     text: 'A',
      //   },
      //   good: {
      //     color: this.$alertColor2,
      //     text: 'B',
      //   },
      //   normal: {
      //     color: this.$alertColor1,
      //     text: 'C',
      //   },
      //   poor: {
      //     color: this.$alertColor0,
      //     text: 'D',
      //   },
      // },
      groupMember: {
        showEditGroup: false,
        allGroups: [],
        groupsMap: {},
        selectGroups: [],
      },
      timeLineData: null,
      currentLineData: {
        fullDigitalTwin: [],
      },
      localList: [],
      timeLineNoData: false,
      address: '',
    }
  },
  computed: {
    // addressInfo() {
    //   return this.address
    // },
    alarmLevel() {
      return this.$store.state.enums.enums.AlarmLevel
    },
  },
  watch: {
    assetsDetails: {
      handler(newVal) {
        if (!newVal) return
        this.init()
        // if (newVal.latitude && newVal.longitude) {
        //   this.getLocaltion(newVal).then(e => {
        //     this.address = e || ''
        //   })
        // }
      },
      deep: true,
      immediate: true,
    },
  },
  // mounted() {
  //   this.init()
  // },
  methods: {
    async onBlurGroupSelect(bool) {
      // if (bool) {
      //   return
      // }
      this.confirmLoading = true
      try {
        let groupIds = this.groupMember.allGroups
          .filter(v => this.groupMember.selectGroups.includes(v.text))
          .map(t => t.value)
        let params = {
          assetId: this.assetsDetails.id,
          groupIds,
        }
        const res = await addRelationByAssetId(params)
        if (res.code === 200) {
          this.assetsDetails.groupNameList = this.groupMember.selectGroups
          this.groupMember.showEditGroup = false
          this.confirmLoading = false
        } else {
          this.$notify.info('error', res.msg)
        }
      } catch (e) {
        console.error(`修改资产组失败：${e}`)
        this.confirmLoading = false
      }
      this.isDialogVisible = false
    },
    // 初始化页面数据
    init() {
      this.getTimeLine()
      this.getVehicleGroups()
    },
    onReload() {
      this.init()
      this.$emit('refresh', 'refresh')
      // if (!this.isRefreshError) {
      //   this.$notify.info('success', '已刷新')
      // }
    },

    clearChip(index) {
      this.groupMember.selectGroups.splice(index, 1)
    },

    async $_confirmGroupMember(callBack) {
      try {
        const res = await updateVehicleGroupMember({
          groupIds: this.groupMember.selectGroups,
          vehicleId: this.sn,
        })
        if (res.status === 200) {
          callBack()
          this.$notify.info('success', '修改成功!')
          this.$emit(
            'change-groups',
            Object.assign([], this.groupMember.selectGroups),
          )
        }
      } catch (e) {
        callBack(false, true)
        console.error(`修改资产组失败：${e}`)
      }
    },

    /*   async getVehicleGroups() {
                        try {
                            this.groupMember.allGroups = []
                            // TODO 需要查询所有 前端需要考虑 做滚动加载调用的逻辑
                            const res = await searchAllVehicleGroup()
                            this.groupMember.allGroups = res.data
                            this.groupMember.allGroups.forEach(v => {
                                this.groupMember.groupsMap[v.id] = v.name
                            })
                        } catch (e) {
                            console.error(`获取资产组列表错误：${e}`)
                        }
                    }, */

    async getVehicleGroups() {
      try {
        this.groupMember.allGroups = []

        // TODO 需要查询所有 前端需要考虑 做滚动加载调用的逻辑
        // const data = await searchAllVehicleGroupDt()
        const data = await this.$store.dispatch(
          'global/searchGroups',
          this.assetsDetails.assetType,
        )
        this.groupMember.allGroups = data
        this.groupMember.allGroups.forEach(v => {
          this.groupMember.groupsMap[v.id] = v.name
        })
      } catch (e) {
        this.isRefreshError = true
        console.error(`获取资产组列表错误：${e}`)
      }
    },

    editGroupMember() {
      this.groupMember.selectGroups = Object.assign(
        [],
        this.assetsDetails.groupNameList,
      )
      this.groupMember.showEditGroup = true
    },

    /*
                    async getTimeLine() {
                        try {
                            const res = await getTimeLine(this.sn)
                            res.data.forEach(item => {
                                item.latitude = ''
                                item.longitude = ''
                                item.fullDigitalTwin ? '' : item.fullDigitalTwin = []
                                if (item.fullDigitalTwin && item.fullDigitalTwin.length) {
                                    item.fullDigitalTwin.forEach(v => {
                                        if (v.name === 'vehicle_location_currentlocation_latitude') {
                                            item.latitude = v.value
                                        }
                                        if (v.name === 'vehicle_location_currentlocation_longitude') {
                                            item.longitude = v.value
                                        }
                                    })
                                }
                            })
                            Promise.all(res.data.map(item => this.getLocaltion(item))).then(() => {
                                this.timeLineData = res.data
                                this.timeLineNoData = !res.data.length
                                this.currentLineData = this.timeLineData[this.timeLineData.length - 1]
                                this.handleMapData();
                            })

                        } catch (e) {
                            console.error('资产时间线：', e)
                        }
                    },
            */

    async getTimeLine() {
      try {
        if (!this.assetsDetails.eventAxisVos) {
          return
        }
        const data = cloneDeep(this.assetsDetails.eventAxisVos)
        data.forEach(item => {
          // item.latitude = ''
          // item.longitude = ''
          item.fullDigitalTwin ? '' : (item.fullDigitalTwin = [])
          if (item.fullDigitalTwin && item.fullDigitalTwin.length) {
            item.fullDigitalTwin.forEach(v => {
              if (v.id === 'vehicle_location_currentlocation_latitude') {
                item.latitude = v.value
              }
              if (v.id === 'vehicle_location_currentlocation_longitude') {
                item.longitude = v.value
              }
            })
          }
        })
        // Promise.all(data.map(item => this.getLocaltion(item))).then(res => {
        //   this.timeLineData = data
        //   this.timeLineNoData = !data.length
        //   this.currentLineData = this.timeLineData[this.timeLineData.length - 1]
        //   // this.handleMapData()
        // })
      } catch (e) {
        this.isRefreshError = true
        console.error('资产时间线：', e)
      }
    },

    goAdvance() {
      // this.$router.push('/investigation/id=1117')

      this.$router.push({
        path: '/investigate',
        query: {
          alertId: this.assetsDetails.alertId,
          vehicleId: this.assetsDetails.id,
          assetType: this.assetsDetails.assetType,
        },
      })
    },

    timeLineChange(i) {
      this.currentLineData = this.timeLineData[i]
    },

    // 点击告警状态
    goAlert() {
      const alarmLevelList = this.assetsDetails.assetAlarmInfoVos
        .filter(v => v.alarmCount > 0)
        .map(t => t.alarmLevel)
      this.$router.push({
        path: '/alerts',
        query: {
          isQuery: 1,
          id: this.sn,
          alarmLevelList: JSON.stringify(alarmLevelList),
        },
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.assets-info {
  // hr.horizontal.light {
  //   background-image: linear-gradient(90deg, hsla(0, 0%, 100%, 0), #fff, hsla(0, 0%, 100%, 0)) !important;
  // }
  .car-model {
    z-index: 1;
    width: 60vh;
    padding: 0 4rem 0 0;
    max-width: 550px;
    min-width: 300px;
    margin: auto;
  }

  // .card-block.card-block.card-block {
  //   padding-top: 258px;
  // }

  .alert-list ::v-deep.v-avatar > span {
    text-overflow: initial !important;
    overflow: initial !important;
  }
}
.bg {
  background: url(../../../assets/images/svg/waves-white.svg) center no-repeat;
}
</style>
