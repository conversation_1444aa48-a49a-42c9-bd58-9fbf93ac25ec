<template>
  <v-card class="h-full d-flex flex-column">
    <v-card-title>{{ $t('asset.idpsTab2.ruleDistribution') }}</v-card-title>
    <v-card-text class="flex-1 pb-0">
      <v-row class="h-100">
        <v-col cols="4" sm="5" class="h-100">
          <vsoc-chart
            class="ml-2"
            echart-id="donut"
            :option="influenceModelOption.chartOption"
            @highlight="onHighlight"
          ></vsoc-chart>
        </v-col>
        <v-col cols="8" sm="7" class="d-flex flex-column justify-center h-100">
          <v-simple-table
            class="scroll-bar-card"
            style="max-height: 20rem !important"
            height="100%"
          >
            <template v-slot:default>
              <tbody>
                <tr
                  v-for="item in influenceModelOption.list"
                  :key="item.dessert"
                >
                  <td class="d-flex align-center">
                    <v-icon class="mr-2" size="6px" :color="item.color">
                      mdi-circle-medium
                    </v-icon>

                    <span
                      class="w-60 text-overflow-hide inline-block align-middle text--primary"
                      v-show-tips="item.name"
                    >
                      {{ item.name }}
                    </span>
                  </td>
                  <td class="text-center text--primary px-0">
                    {{ item.value | numberToFormat }}
                  </td>
                  <td class="text-center text--secondary">
                    {{ item.percent }}
                  </td>
                </tr>
              </tbody>
            </template>
          </v-simple-table>
        </v-col>
      </v-row>
    </v-card-text>
  </v-card>
</template>

<script>
import VsocChart from '@/components/VsocChart.vue'
import themeConfig from '@themeConfig'
export default {
  name: 'RegularDistribution',
  components: {
    VsocChart,
  },
  computed: {
    influenceModelOption() {
      const list = [
        {
          name: '网络规则',
          percent: '57.7%',
          value: 55,
          color: '#533DF1',
        },
        {
          name: '主机规则',
          percent: '25.0%',
          value: 24,
          color: '#44E2FE',
        },
        {
          name: '流量规则',
          percent: '14.2%',
          value: 13,
          color: '#40CD6E',
        },
        {
          name: '蓝牙规则',
          percent: '2.1%',
          value: 2,
          color: '#2F95E9',
        },
      ]

      const sum = list.map(v => v.value).reduce((x, y) => x + y)
      list.forEach(item => {
        item.percent = ((item.value / sum) * 100).toFixed(1) + '%'
      })
      return {
        list,
        chartOption: {
          color: list.map(v => v.color),
          tooltip: {
            trigger: 'item',
            formatter: '{b}: {c}\t({d}%)',
            backgroundColor: '',
            textStyle: { color: '#fff' },
          },
          legend: { top: '5%', left: 'center', show: false },
          series: [
            {
              type: 'pie',
              radius: ['80%', '100%'],
              avoidLabelOverlap: true,
              percentPrecision: 1,
              minAngle: 20,
              stillShowZeroSum: true,
              label: {
                show: false,
              },
              itemStyle: {
                borderWidth: 10,
                borderRadius: 16,
                borderColor: this.$vuetify.theme.isDark
                  ? themeConfig.themes.dark.backgroundColor
                  : themeConfig.themes.light.backgroundColor,
              },
              emphasis: { label: { show: false } },
              labelLine: { show: false },
              data: list,
            },
          ],
          title: {
            subtext: `{text|${this.$t('asset.idpsTab2.ruleTotal')}}`,
            text: list.length,
            top: '35%',
            left: '48.2%',
            textAlign: 'center',
            padding: [5, 5],
            textStyle: { fontSize: '2.666666rem', fontWeight: 600 },
            subtextStyle: {
              rich: {
                text: {
                  fontSize: '1rem',
                  color: '#7A8599',
                  verticalAlign: 'bottom',
                },
              },
            },
          },
        },
        isLoading: false,
      }
    },
  },
  methods: {
    onHighlight(event, myChart) {
      this.influenceModelOption.chartOption.tooltip.backgroundColor =
        event.color
      myChart.setOption(this.influenceModelOption.chartOption)
    },
  },
}
</script>
<style scoped lang="scss">
::v-deep
  .v-data-table
  > .v-data-table__wrapper
  > table
  > tbody
  > tr:not(:last-child)
  > td:not(.v-data-table__mobile-row) {
  border: none !important;
}
</style>
