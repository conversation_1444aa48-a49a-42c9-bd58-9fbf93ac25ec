const dict = {
  currentTitle: 'Dict type',
  currentTitle1: 'Dict data',
  headers: {
    dictId: 'Dict Key',
    dictName: 'Dict Name',
    dictEnName: 'Dict Type',
    dictDataCount: 'Count',
    status: 'Status',
    remark: 'Remark',
    isDefault: 'Built in system',
  },
  headers1: {
    dictType: 'Dict Type', //字典类型id
    dictName: 'Dict tags (Chinese)', //字典标签(中文)
    dictName1: 'Dict tags', //字典标签(中文)
    dictEnName: 'Dict tags (English)', //字典标签(英文)
    dictId: 'Dict Key', //字典键值
    sort: 'Dict Sort', //排序
    status: 'Status', //状态（0正常 1停用）
    isDefault: 'Built in system', //系统默认（0是 1否）
    remark: 'Remark', //
  },
  tip: 'Key values in data storage',
  tip1: 'The dictionary type has associated dictionary data',
  tip2: 'Please select dict type',
  del: {
    title: 'Delete dict type',
    text: 'Are you sure to delete the dict type:{0}?',
  },
  del1: {
    title: 'Delete dict data',
    text: 'Are you sure to delete the dict data:{0}?',
  },
}
export default dict
