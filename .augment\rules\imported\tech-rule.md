---
type: 'agent_requested'
---

#项目使用的技术栈-技术架构

- 项目基于 vue2.6.14 版本开发
- 使用 Vuetify2.6.7 组件库, 如果组件库时，
- CCS 默认使用 vuetify 的样式规范；
- 使用 Javascript 语言
- 项目包使用 yarn 管理
- 项目使用 vue-cli-service 打包编译

#代码规范

- 目录为全小写字母，多个字母用-分割；eg: asset-info
- vue 文件采用驼峰命名规范，并且首字母大写；eg:AssertProfile
- js 文件采用驼峰命名规范，并且首字母小写; eg:appConfigStoreModule.js
- css 文件 采用全小写字母，多个字母用-分割；eg: knowledge-base.scss
- 公共组件目录统一命名为 components，无论是全局公共组件目录 还是业务内模块组件
- 所有常量定义 放在 util/constant.js 中
- 枚举值定义放在 util/enum.js 中
- 全局 filter 放在 util/filter.js 中
- 公共函数放在 util/utils.js 中
- 方法与函数都需要增加备注与描述
- 所有组件 方法 函数 都需要增加备注与描述
