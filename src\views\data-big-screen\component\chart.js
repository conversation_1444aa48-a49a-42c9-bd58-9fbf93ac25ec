import { hexToRgb } from '@core/utils/index.js'

import { i18n } from '@/plugins/i18n'
import store from '@/store'
import * as echarts from 'echarts'
import Vue from 'vue'
export let backgroundColor = 'transparent'

// 主色调
export let primary = '#44E2FE'

export let getRoundSize = num => {
  let screenWidth = store.state.global.clientWidth
  return Math.round((screenWidth / 1920) * num)
}

// 一般用于image宽高
export let getCeilSize = num => {
  let screenWidth = store.state.global.clientWidth
  return Math.ceil((screenWidth / 1920) * num)
}
Vue.prototype.$getCeilSize = getCeilSize

export let textStyle = () => {
  return {
    fontFamily: 'Roboto',
    fontStyle: 'normal',
    fontWeight: 400,
    fontSize: getRoundSize(14),
    // lineHeight: getRoundSize(14),
    // color: '#ffffffcc',
  }
}

export let tooltip = () => {
  return {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
    },
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderColor: 'rgba(255, 255, 255, 0.1)',
    textStyle: {
      color: '#ffffff',
      fontSize: getRoundSize(14),
      lineHeight: getRoundSize(22),
    },
    extraCssText: 'backdrop-filter: blur(6px);',
  }
}

export let legend = () => {
  return {
    top: getRoundSize(10),
    show: true,
    right: 0,
    icon: 'circle',
    itemHeight: getRoundSize(8),
    itemGap: getRoundSize(12),
    textStyle: {
      fontSize: getRoundSize(12),
      padding: [0, 0, 0, -8],
      color: '#ffffff99',
    },
  }
}

export let grid = {
  top: '30%',
  left: '0%',
  right: '2.5%',
  bottom: '0%',
  containLabel: true,
}
// 坐标字体
export let axisLabel = () => {
  return {
    fontSize: getRoundSize(12),
  }
}
export let xAxisFn = data => {
  return {
    type: 'category',
    boundaryGap: false,
    data: data,
    axisLine: { show: false },
    axisTick: { show: false },
    axisLabel: axisLabel(),
  }
}

// 虚线样式
export let splitLine = {
  show: true,
  lineStyle: {
    type: [5, 5],
    dashOffset: 0,
    shadowBlur: 0,
    opacity: 0.15,
    // color: '#28354b',
  },
}

export let yAxisFn = () => {
  return [
    {
      type: 'value',
      splitLine: splitLine,
      axisLabel: axisLabel(),
    },
  ]
}

export let linearSeriesFn = (color, data, name) => {
  let rgbObj = ''
  if (color.includes('#')) {
    rgbObj = hexToRgb(color)
  }

  const startColor =
    `rgba(${rgbObj.r},${rgbObj.g},${rgbObj.b},0.8)` ||
    'rgba(251, 114, 147, 0.6)'
  const endColor =
    `rgba(${rgbObj.r},${rgbObj.g},${rgbObj.b},0)` || 'rgba(251, 114, 147, 0)'
  return {
    name: name,
    type: 'line',
    // stack---面积图堆叠
    // stack: 'Total',
    // 平滑曲线
    // smooth: true,
    color: startColor,
    lineStyle: {
      width: 1,
      type: [5, 5],
      dashOffset: 0,
      shadowBlur: 0,
    },
    showSymbol: true,
    symbol: 'circle',
    symbolSize: 6,

    areaStyle: {
      opacity: 0.8,
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        {
          offset: 0,
          color: startColor,
        },
        {
          offset: 1,
          color: endColor,
        },
      ]),
      itemStyle: {
        borderWidth: 50,
      },
    },
    emphasis: {
      focus: 'series',
    },
    data: data,
  }
}

export let linearOptionFn = (xList, yList) => {
  return {
    backgroundColor,
    tooltip: tooltip(),
    legend: { show: false },
    grid,
    xAxis: xAxisFn(xList),
    yAxis: yAxisFn(),
    series: yList.map(item => {
      return linearSeriesFn(item.color, item.data, item.name)
    }),
  }
}

export let stackSeriesFn = (color, data, name) => {
  return {
    name: name,
    type: 'bar',
    stack: 'total-profit',
    barWidth: 10,
    barMinHeight: 5,
    chip: false,
    emphasis: { focus: 'series' },
    itemStyle: {
      borderRadius: 10,
      borderWidth: 5,
      borderColor: 'transparent',
      borderJoin: 'round',
      borderType: 'solid',
    },
    data: data,
    color: color,
  }
}

export let stackOptionFn = (xList, yList) => {
  return {
    backgroundColor,
    tooltip: {
      ...tooltip(),
      trigger: 'axis',
      axisPointer: { type: 'none' },
      borderWidth: 0,
    },
    legend: legend(),
    grid: {
      ...grid,
      right: '4%',
    },
    xAxis: xAxisFn(xList),
    yAxis: yAxisFn(),
    series: yList.map(item => {
      return stackSeriesFn(item.color, item.data, item.name)
    }),
  }
}

export let lineSeriesFn = (color, data, name) => {
  return {
    type: 'line',
    name: name,
    data: data,
    color: color,
    symbol: 'circle',
    symbolSize: 6,
    lineStyle: { type: [5, 5], dashOffset: 0, shadowBlur: 0 },
  }
}

export let lineOptionFn = (xList, yList, updateDate, size) => {
  return {
    backgroundColor,
    tooltip: {
      ...tooltip(),
      trigger: 'axis',
      axisPointer: { type: 'line' },
      borderWidth: 0,
    },
    legend: {
      ...legend(),
      ...{
        formatter: name => {
          if (updateDate) {
            return `${name}    ${i18n.t('global.updateDiff', [updateDate])}`
          } else {
            return name.slice(0, 1)
          }
        },
      },
    },
    grid: {
      ...grid,
      top: size ? size.top : '30%',
      bottom: size ? size.bottom : '0%',
      right: '4%',
    },
    xAxis: xAxisFn(xList),
    yAxis: yAxisFn(),
    series: yList.map(item => {
      return lineSeriesFn(item.color, item.data, item.name)
    }),
  }
}

export const cloudTooltip = {
  trigger: 'item',
  formatter: '{b}:\t{c}({d}%)',
  backgroundColor: '',
  padding: [6, 10],
  textStyle: { color: '#fff' },
}

export const cloudPieSeriesFn = (list, formatter) => {
  return [
    {
      name: '',
      type: 'pie',
      radius: ['75%', '95%'],
      avoidLabelOverlap: true,
      percentPrecision: 0,
      minAngle: 20,
      stillShowZeroSum: true,
      top: getRoundSize(25),
      bottom: getRoundSize(30),
      left: getRoundSize(-180),
      label: {
        show: true,
        position: 'center',
        lineHeight: getRoundSize(16),
        formatter: formatter,
        rich: {
          name: {
            fontSize: getRoundSize(14),
            padding: [0, 0],
            color: '#fff',
            opacity: 0.6,
          },
          value: {
            lineHeight: getRoundSize(32),
            fontSize: getRoundSize(32),
            fontWeight: 600,
            padding: [getRoundSize(8), 0],
            verticalAlign: 'bottom',
            color: primary,
          },
          unit: {
            fontSize: getRoundSize(16),
            fontWeight: 500,
            padding: [getRoundSize(10), getRoundSize(5)],
            verticalAlign: 'bottom',
            color: primary,
            opacity: 0.6,
          },
        },
      },
      data: list,
      itemStyle: { borderWidth: 0, borderColor: '#fff' },
      emphasis: {
        show: true,
        scale: true,
      },
    },
  ]
}

export const cloudPieLegendFn = () => {
  return {
    type: 'scroll',
    orient: 'vertical',
    right: 16,
    top: 'center',
    icon: 'circle',
    itemGap: getRoundSize(15), // 图标间距
    itemHeight: getRoundSize(10),
    textStyle: {
      fontSize: getRoundSize(14),
      padding: [0, 0, 0, 0],
      color: '#fff',
      opacity: 0.6,
      width: getRoundSize(120),
      overflow: 'truncate',
    },
  }
}

export const cloudPieColor = ['#4A687B', '#177754', '#4CA3B6', '#1B3F82']
