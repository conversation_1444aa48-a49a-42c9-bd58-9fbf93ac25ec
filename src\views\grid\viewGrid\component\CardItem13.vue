<template>
  <!-- 环形图1 -->
  <div class="box">
    <div class="box-header">{{ title }}</div>
    <template v-if="list.length !== 0">
      <div class="h-100 d-flex w-100">
        <card-ring
          v-for="(item, index) in list"
          :key="index"
          :echartId="'echartId' + index"
          :list="item.data"
          :total="item.total"
          :title="item.title"
        ></card-ring>
      </div>
    </template>
    <template v-else>
      <div
        class="box-chart d-flex align-center color--primary justify-center fs-16"
      >
        None
      </div>
    </template>
  </div>
</template>

<script>
import VsocChart from '@/components/VsocChart.vue'
import CardRing from './CardRing.vue'
export default {
  name: 'CardItem13',
  components: {
    VsocChart,
    CardRing,
  },
  props: {
    title: {
      type: String,
      default: '',
    },
    echartId: {
      type: String,
      default: '',
    },
    list: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  created() {},
  computed: {},
  methods: {},
}
</script>
