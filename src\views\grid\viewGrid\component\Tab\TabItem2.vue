<template>
  <div class="d-flex flex-column h-full">
    <template v-if="list.length !== 0">
      <div
        v-for="(earning, index) in list"
        :key="index"
        class="d-flex align-center"
        style="height: 25%"
      >
        <span class="right-chart1-text">{{ earning.classifyName }}</span>
        <v-progress-linear
          background-color="transparent"
          rounded
          :color="earning.color"
          :value="earning.percentage"
          class="progress-linear"
          :class="`progress-${index}`"
        >
        </v-progress-linear>
        <span class="right-chart1-value">
          {{ earning.count | numberToFormat }} ({{ earning.percentage }})
        </span>
      </div>
    </template>
    <template v-else>
      <div
        class="fs-16 color--primary h-100 d-flex justify-center align-center"
      >
        None
      </div>
    </template>
  </div>
</template>

<script>
const colorList = ['#32FDB8', '#21CBFF', '#FE9837', '#44E2FE', '#FFE436']
const list = [
  {
    classifyName: '恶意外联IP',
    alarmLevel: '3',
    alarmLevelName: '低',
    detectorId: '1663007853415235584',
    alarmPercentage: '35.12%',
    count: 335,
    title: '恶意外联IP',
    color: '#32FDB8',
    value: 335,
    percentage: '35.12',
    level: '3',
  },
  {
    classifyName: '疑似危险指令',
    alarmLevel: '5',
    alarmLevelName: '测试',
    detectorId: '1663080128713850880',
    alarmPercentage: '21.59%',
    count: 206,
    title: '疑似危险指令',
    color: '#21CBFF',
    value: 206,
    percentage: '21.59',
    level: '5',
  },
  {
    classifyName: '超速',
    alarmLevel: '1',
    alarmLevelName: '高',
    detectorId: '1597109462940778496',
    alarmPercentage: '16.35%',
    count: 156,
    title: '超速',
    color: '#FE9837',
    value: 156,
    percentage: '16.35',
    level: '1',
  },
  {
    classifyName: '总线负载过大',
    alarmLevel: '0',
    alarmLevelName: '严重',
    detectorId: '1663093495977476096',
    alarmPercentage: '14.36%',
    count: 137,
    title: '总线负载过大',
    color: '#44E2FE',
    value: 137,
    percentage: '14.36',
    level: '0',
  },
  {
    classifyName: '里程欺诈',
    alarmLevel: '1',
    alarmLevelName: '高',
    detectorId: '1622360118140076032',
    alarmPercentage: '12.58%',
    count: 120,
    title: '里程欺诈',
    color: '#FFE436',
    value: 120,
    percentage: '12.58',
    level: '1',
  },
]
export default {
  name: 'TabItem2',
  props: {
    events: {
      type: Array,
      default: () => {
        return list
      },
    },
  },
  computed: {
    list() {
      return this.events
    },
  },
  data() {
    return {
      // list: this.events,
      option: {
        data: list.map(t => {
          return {
            name: t.name,
            value: t.value,
          }
        }),
        colors: colorList,
      },
    }
  },
}
</script>
