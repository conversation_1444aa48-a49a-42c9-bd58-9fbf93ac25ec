const ai = {
  totalNumberOfSamples: 'Total Number Of Samples',
  lastLearningStage: 'Last Learning Stage',
  basicInformation: 'Basic Information',
  featureType: 'Feature Type',
  dataType: 'Data Type',
  startingTimeOfData: 'Starting Time Of Data',
  dataDeadline: 'Data Deadline',
  alarmID: 'Alarm ID',
  vehicleModel: 'Vehicle Model',
  classification: 'Classification',
  typesOf: 'Types Of',
  selectModel: {
    title: 'Select Model',
    tips: "We offer a diverse selection of AI models. Whether it's an immediate experience or in-depth comparison, we are committed to helping you find the most suitable solution",
    btn: 'AI Intelligent Analysis',
    tips2: 'Analysis Successful',
  },
  warning: 'Please select at least one model for analysis',
  empty: {
    title: 'Model not configured',
    tips: 'You have not configured the model yet, please contact the administrator',
    btn: 'Contact the administrator',
  },
  loading: {
    title: 'Loading',
    tips: 'The model is loading, please be patient and wait',
  },
  error: {
    title: 'Network Anomaly',
    tips:
      'Connection encountered obstruction: We are temporarily unable to establish communication with the server, which may be due to unstable network connection or our server undergoing brief maintenance.\n' +
      'Please check your network settings and try again later.',
    tips2:
      'If multiple attempts fail, please check if other network applications are working properly to help locate the problem.',
    tips3: 'Network connection failed.',
    tip4: 'This operation will permanently overwrite the original record. Please confirm if you want to continue.',
    tip5: 'Token Excess',
    btn1: 'Attempt to reconnect',
    btn2: 'Contact Us',
  },
  timeOut: {
    title: 'Token usage exceeds quota',
    tips: 'Dear user, your current token usage has reached the limit. To ensure that the service is not affected, we suggest that you consider upgrading your quota plan.',
    tips2:
      'If you have any questions or need to immediately increase your token quota, please feel free to contact us at any time.',
    btn: 'Get in touch with us',
  },
  result: {
    drawer: {
      title: 'Please select a model:',
      tips: 'Please select one engine for comparative analysis',
      tips2: 'Reanalysis successful',
      tips3: 'Reanalysis failed',
    },
    del: {
      title: 'delete record',
      tips: 'The record cannot be restored after deletion. Are you sure to delete it?',
    },
    save: 'Saved successfully',
    title: 'No model selected',
    tips: 'Click the plus sign in the bottom right corner to add the model',
  },
}

export default ai
