<template>
  <div class="c-box pb-0">
    <div class="c-box-header">{{ title || $t('screen.cloud.left2') }}</div>

    <vsoc-chart
      echartId="left-cloud-2"
      class="box-chart"
      :option="chartOption"
      @highlight="onHighlight"
    ></vsoc-chart>
  </div>
</template>

<script>
import VsocChart from '@/components/VsocChart.vue'
import { numberToFormat } from '@/util/filters'
import {
  cloudPieColor,
  cloudPieLegendFn,
  cloudPieSeriesFn,
  cloudTooltip,
} from './chart'
export default {
  name: 'LeftCloud2',
  components: {
    VsocChart,
  },
  props: {
    title: {
      type: String,
      default: '',
    },
    list: {
      type: Array,
      default: () => {
        return [
          { name: 'Normal', value: 12 },
          { name: 'Moderate Risk', value: 8 },
          { name: 'Low Risk', value: 6 },
          { name: 'High Risk', value: 2 },
        ]
      },
    },
    total: {
      type: [Number, String],
      default: () => {
        return 1.56 * Math.pow(10, 6)
      },
    },
  },
  computed: {
    chartOption() {
      const formatter = () => {
        const totalObj = numberToFormat(this.total, 'Object')
        return `\n{value|${totalObj.num}}{unit|${
          totalObj.unit
        }}\n{name|${this.$t('enums.datePresets.last30')}}`
      }
      return {
        color: cloudPieColor,
        tooltip: cloudTooltip,
        legend: cloudPieLegendFn(),
        series: cloudPieSeriesFn(this.list, formatter),
        // series: [
        //   {
        //     name: '',
        //     type: 'pie',
        //     radius: ['75%', '95%'],
        //     avoidLabelOverlap: true,
        //     percentPrecision: 0,
        //     minAngle: 20,
        //     stillShowZeroSum: true,
        //     top: 20,
        //     left: getRoundSize(-180),
        //     label: {
        //       show: true,
        //       position: 'center',
        //       lineHeight: getRoundSize(16),
        //       formatter: () => {
        //         const totalObj = numberToFormat(this.total, 'Object')
        //         return `\n{value|${totalObj.num}}{unit|${totalObj.unit}}\n{name|Last 30 days}`
        //       },
        //       // width: 100,
        //       // overflow: 'truncate',
        //       rich: {
        //         name: {
        //           fontSize: getRoundSize(14),
        //           padding: [0, 0],
        //           color: '#fff',
        //           opacity: 0.6,
        //         },
        //         value: {
        //           lineHeight: getRoundSize(32),
        //           fontSize: getRoundSize(32),
        //           fontWeight: 600,
        //           padding: [getRoundSize(8), 0],
        //           verticalAlign: 'bottom',
        //           color: primary,
        //         },
        //         unit: {
        //           fontSize: getRoundSize(16),
        //           fontWeight: 500,
        //           padding: [getRoundSize(10), getRoundSize(5)],
        //           verticalAlign: 'bottom',
        //           color: primary,
        //           opacity: 0.6,
        //         },
        //       },
        //     },
        //     data: this.list,
        //     itemStyle: { borderWidth: 0, borderColor: '#fff' },
        //     emphasis: {
        //       show: true,
        //       scale: true,
        //     },
        //   },
        // ],
      }
    },
  },
  methods: {
    onHighlight(obj, myChart) {
      const option = this.chartOption
      option.tooltip.backgroundColor = obj.color
      myChart.setOption(option)
    },
  },
}
</script>
