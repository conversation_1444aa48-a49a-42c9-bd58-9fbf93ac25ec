const dataCenter = {
  headers: {
    id: 'ID',
    name: 'Name',
    type: 'Type',
    source: 'Channel',
    collectionDate: 'Collection Time',
    generationDate: 'Generation Time',
    reportId: 'ID',
    attachmentCount: 'Attachment',
  },
  collectionRange: 'Collection Time Range',
  jsonView: 'JSON View',
  tab: {
    periodic: 'Timing Period',
    case: 'Case Period',
    dataId: 'Data ID',
  },
  service: 'Service list change monitoring statistics',
  vehicles:
    'Total number of vehicles expected to report as they are registered with Device Platform',
  reportVehicle:
    'Anzahl der gemeldeten versendeten Fahrzeuge (mindestens einmal)',
  cdi: {
    title: 'Title',
    id: 'Event ID',
    contact: 'Contact Person',
    type: 'Type',
    createdDate: 'Create Time',
    lastChangeDate: 'Last Updated',
    lifecycleStatus: 'Life Cycle State',
    priority: 'Priority',
    threatLevel: 'Threat Level',
    referenceUrl: 'Reference URL',
    cveRef: 'CVE Reference URL',
    description: 'Description',
  },
  vehicle: {
    title: 'Affected Models',
    count: 'Number Of Affected Vehicles',
  },
  attach: {
    title: 'Attachment List',
    id: 'ID',
    name: 'Name',
    path: 'Path',
    type: 'Type',
    size: 'Size',
    creater: 'Create User',
    createDate: 'Create Time',
  },
  changeList: {
    serviceListChangesMonitor: 'Service List Change Monitoring',
    abnormalMemoryProfileMonitor: 'Abnormal Memory Monitoring',
    networkConnectionStatusMonitor: 'Network Link Monitoring',
    privilegeUserLoginMonitor: 'User Monitoring',
    oTAAbnormalMonitor: 'Ota Abnormal Monitoring',
  },
  serviceListChangesMonitor: {
    totalSuccessNum: 'Total number of successful events',
    totalFailNum: 'Total number of failed events',
    newNum: 'The total number of new application service list types',
    reduceNum: 'Reduce the total number of app service list types',
  },
  abnormal: 'Abnormal memory monitoring statistics',
  abnormalMemoryProfileMonitor: {
    cpuTypeNum: 'Total number of events of CPU type',
    ramMemoryTypeNum: 'Total number of events for RAM memory type',
  },
  network: 'Network Connection Monitoring Statistics',
  networkConnectionStatusMonitor: {
    typeNum: '345G Type Quantity',
    wifiTypeNum: 'Number of WIFI types',
    connSuccess: 'Number of successful connections',
    connFail: 'Connection failures',
  },
  privilege: 'User monitoring statistics',
  ota: 'OTA monitoring statistics',
  occurDate: 'Starting Time',
  summary: 'Summary',
  scale: 'Scale',
  lep: {
    vehiclesCount:
      'The number of vehicles that have sent reports in the last {count} days',
    firstCount:
      'The number of vehicles sending the report for the first time in the last {count} days',
    affectedModels: 'Event affected models',
    suspiciousCount: 'Total Suspicious Events',
    model: 'Car Model',
    caseStatistics: 'CASE report statistics',
    caseCount: 'Cases Under Investigation',
    addCaseCount: 'Cases Opened In Period',
    closeCaseCount: 'Cases Closed In Period',
    suspiciousDistribution: 'Distribution of Suspicious Event Types',
    title: 'Periodic Report Statistics',
    resport1: 'Vehicle Expected To Report',
    resport2: 'Vehicle Ever Reported',
    resport3: 'Vehicle Not Reported In Period',
    resport4: 'Vehicle New Reported In Period',
  },
}

export default dataCenter
