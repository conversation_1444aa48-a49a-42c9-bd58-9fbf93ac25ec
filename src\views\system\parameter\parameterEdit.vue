<template>
  <vsoc-drawer
    v-model="isDrawerShow"
    :title="
      mode === 'new'
        ? $t('global.drawer.addTitle', { cur: $t('parameter.currentTitle') })
        : $t('global.drawer.editTitle', {
            cur: $t('parameter.currentTitle'),
          })
    "
    @click:confirm="onSave"
  >
    <v-form ref="form" v-model="valid" lazy-validation>
      <div class="mt-2">
        <v-row class="pl-6 pr-6">
          <v-text-field
            v-model="advanceQuery.propertyName"
            :label="$t('parameter.headers.propertyName')"
            required
            color="primary"
            class="is-required"
            :rules="[
              v =>
                !!v ||
                $t('validation.required', [
                  $t('parameter.headers.propertyName'),
                ]),
              v => max(v, 200),
            ]"
          >
          </v-text-field>
        </v-row>
        <!-- 参数组名 -->
        <v-row class="pl-6 pr-6">
          <v-combobox
            v-model="advanceQuery.groupName"
            color="primary"
            dense
            class="is-required mt-3"
            :items="allGroups"
            append-icon="mdi-chevron-down"
            :label="$t('parameter.headers.groupName')"
            persistent-hint
            :search-input.sync="searchTag"
            :rules="[
              v =>
                !!v ||
                $t('validation.required', [$t('parameter.headers.groupName')]),
              v => /^[A-Za-z0-9-_.]+$/.test(v) || this.$t('parameter.tip1'),
            ]"
            @change="alertTagsChange"
          >
            <template v-slot:selection="{ item, index }">
              <v-chip
                small
                pill
                color="primary"
                close
                @click:close="removeTags(index)"
              >
                {{ item }}
              </v-chip>
            </template>
            <template v-slot:no-data>
              <p
                class="pa-2 mb-0 text-body list-hover"
                @click="createTag(searchTag)"
              >
                {{ $t('parameter.tip') }}：<strong>{{ searchTag }}</strong>
              </p>
            </template>
          </v-combobox>
        </v-row>
        <!-- 参数键名 -->
        <v-row class="pl-6 pr-6">
          <v-text-field
            v-model="advanceQuery.propertyKey"
            :label="$t('parameter.headers.propertyKey')"
            required
            color="primary"
            class="is-required"
            :rules="[
              v =>
                !!v ||
                $t('validation.required', [
                  $t('parameter.headers.propertyKey'),
                ]),
              v => /^[A-Za-z0-9-_.,()]+$/.test(v) || this.$t('parameter.tip1'),
            ]"
          >
          </v-text-field>
        </v-row>
        <!-- 参数类型 -->
        <v-row class="pl-6 pr-6">
          <v-select
            v-model="advanceQuery.propertyType"
            color="primary"
            class="is-required"
            :items="typeList"
            :menu-props="{ offsetY: true }"
            :label="$t('parameter.headers.propertyType')"
            :rules="[
              v =>
                !!v ||
                $t('validation.required', [
                  $t('parameter.headers.propertyType'),
                ]),
            ]"
            @change="advanceQuery.propertyValue = ''"
          >
          </v-select>
        </v-row>
        <!-- 参数键值 -->
        <v-row class="pl-6 pr-6">
          <v-select
            v-if="advanceQuery.propertyType === 'BOOLEAN'"
            v-model="advanceQuery.propertyValue"
            color="primary"
            class="is-required"
            :items="valueList"
            :menu-props="{ offsetY: true }"
            :label="$t('parameter.headers.propertyValue')"
            :rules="[
              v =>
                !!v ||
                $t('validation.required', [
                  $t('parameter.headers.propertyValue'),
                ]),
            ]"
          >
          </v-select>
          <v-textarea
            v-else
            v-model="advanceQuery.propertyValue"
            :label="$t('parameter.headers.propertyValue')"
            required
            :rows="3"
            color="primary"
            class="is-required"
            :rules="[
              v =>
                !!v ||
                $t('validation.required', [
                  $t('parameter.headers.propertyValue'),
                ]),
            ]"
          ></v-textarea>
          <!-- <v-text-field
            v-else
            v-model="advanceQuery.propertyValue"
            :label="$t('parameter.headers.propertyValue')"
            required
            color="primary"
            class="is-required"
            :rules="[
              v =>
                !!v ||
                $t('validation.required', [
                  $t('parameter.headers.propertyValue'),
                ]),
            ]"
          >
          </v-text-field> -->
        </v-row>

        <div class="mt-1">
          <v-row class="pl-6 pr-6">
            <v-row class="pl-6 pr-6">
              <span class="text-base font-weight-medium color-base">
                {{ $t('parameter.headers.isDefault') }}
              </span>
            </v-row>
            <v-btn-toggle
              class="w-100 mt-2"
              v-model="advanceQuery.isDefault"
              color="primary"
            >
              <v-btn
                v-for="item in isDefaultList"
                :key="item.value"
                :value="item.value"
                elevation="0"
                class="w-50"
              >
                <v-badge
                  dot
                  inline
                  offset-x="10"
                  :offset-y="-18"
                  :color="item.color"
                  class="mr-2"
                ></v-badge>
                <span>{{ item.text }}</span>
              </v-btn>
            </v-btn-toggle>
          </v-row>
        </div>

        <v-row class="pl-6 pr-6">
          <v-textarea
            class="mt-5"
            v-model="advanceQuery.description"
            :label="$t('parameter.headers.description')"
            :rows="5"
            color="primary"
          ></v-textarea>
        </v-row>
      </div>
    </v-form>
  </vsoc-drawer>
</template>

<script>
import { max } from '@/@core/utils/validation'
import {
  addProperty,
  queryGroupNames,
  updateProperty,
} from '@/api/system/parameter'
import VsocDrawer from '@/components/VsocDrawer.vue'
import { activeColor, inactiveColor } from '@/plugins/systemColor'

export default {
  name: 'dictEdit',
  props: {
    mode: {
      type: String,
      default: () => 'new',
    },
  },
  data() {
    return {
      max,
      allGroups: [],
      typeList: [
        'BOOLEAN',
        'STRING',
        'URL',
        'INTEGER',
        'ENCRYPTEDSTRING',
        'NUMBER',
      ],
      valueList: ['true', 'false'],
      valid: true,
      searchTag: '',
      createing: false,
      isDrawerShow: false,
      isDefaultList: [
        {
          text: '是',
          value: '0',
          color: activeColor,
        },
        {
          text: '否',
          value: '1',
          color: inactiveColor,
        },
      ],
      advanceQuery: {
        propertyName: '', //参数名称
        groupName: '', //参数组名
        propertyKey: '', //参数键名
        propertyType: '', //参数值类型
        propertyValue: '', //参数键值
        isDefault: '0', //系统内置（0是 1否）
        description: '', //描述
      },
    }
  },
  components: {
    VsocDrawer,
  },
  mounted() {
    this.getAllGroup()
  },
  methods: {
    //获取所有组名
    async getAllGroup() {
      const res = await queryGroupNames({ groupName: '' })
      this.allGroups = res.data || []
    },
    alertTagsChange(tag) {
      // const tagName = tag[tag.length - 1]
      // console.log(tagName, tag, 111)
      // if (typeof tagName === 'string') {
      //   this.allGroups.splice(tag.length - 1, 1)
      //   this.createTag(tagName)
      // }
    },
    async createTag(tagName) {
      let bool = /^[A-Za-z0-9-_.]+$/.test(tagName)
      if (!bool) {
        return this.$notify.info('warning', this.$t('parameter.tip1'))
      }
      if (this.createing) return
      this.createing = true
      this.allGroups.push(tagName)
      this.advanceQuery.groupName = tagName
      this.searchTag = ''
      this.createing = false
    },
    removeTags(index) {
      this.advanceQuery.groupName = ''
      // this.allGroups.splice(index, 1)
    },
    open(item) {
      this.$refs.form.resetValidation()
      this.$nextTick(() => {
        if (this.mode === 'new') {
          this.advanceQuery = {
            propertyName: '', //参数名称
            groupName: '', //参数组名
            propertyKey: '', //参数键名
            propertyType: '', //参数值类型
            propertyValue: '', //参数键值
            isDefault: '0', //系统内置（0是 1否）
            description: '', //描述
          }
        } else {
          this.advanceQuery = { ...item }
        }
        this.isDrawerShow = true
      })
    },
    updateTheme() {
      let themeColor = JSON.parse(this.advanceQuery.propertyValue)
      if (themeColor) {
        this.$store.commit(
          'appConfig/UPDATE_GLOBAL_THEME_MODE',
          themeColor.Theming,
        )
        this.$store.commit(
          'appConfig/UPDATE_APP_PRIMARY',
          themeColor.PrimaryColor,
        )
      }
    },
    onSave(callback) {
      const bool = this.$refs.form.validate()
      if (!bool) return callback(false, true)

      if (this.mode === 'edit') {
        updateProperty(this.advanceQuery)
          .then(resp => {
            if (resp.code === 200) {
              this.$notify.info(
                'success',
                this.$t('global.hint.edit', [this.advanceQuery.propertyName]),
              )
              this.$emit('save')
              if (this.advanceQuery.propertyKey === 'Theme_Color') {
                this.updateTheme()
              }
              callback()
            } else {
              throw new Error(resp.msg)
            }
          })
          .catch(e => {
            this.$notify.info('error', e)
            callback(false, true)
          })
      } else {
        addProperty(this.advanceQuery)
          .then(resp => {
            if (resp.code === 200) {
              this.$notify.info(
                'success',
                this.$t('global.hint.add', [this.advanceQuery.propertyName]),
              )
              this.$emit('save')
              if (this.advanceQuery.propertyKey === 'Theme_Color') {
                this.updateTheme()
              }
              callback()
            } else {
              throw new Error(resp.msg)
            }
          })
          .catch(e => {
            callback(false, true)
          })
      }
    },
  },
}
</script>

<style scoped></style>
