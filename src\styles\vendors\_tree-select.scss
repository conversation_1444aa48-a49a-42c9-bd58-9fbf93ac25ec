.vue-treeselect__control {
  height: 40px !important;
  padding-left: 12px !important;
  padding-right: 12px !important;
}
.vue-treeselect__placeholder,
.vue-treeselect__single-value {
  line-height: 40px !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
}
.vue-treeselect__placeholder {
  color: rgba(94, 86, 105, 0.68) !important;
  font-size: 0.2875rem !important;
}
.vue-treeselect__single-value {
  color: rgba(94, 86, 105, 0.87) !important;
  font-size: 14px !important;
}
.vue-treeselect--searchable .vue-treeselect__input-container {
  padding-left: 0 !important;
  padding-right: 0 !important;
}
.vue-treeselect__control-arrow {
  color: currentColor !important;
  margin-right: 10px !important;
  font-size: 20px !important;
}
.vue-treeselect__option {
  line-height: 40px !important;
  font-size: 0.8125rem !important;
  font-weight: 500 !important;
}
.vue-treeselect--single .vue-treeselect__option--selected {
  background: rgba(79, 94, 144, 0.16) !important;
  color: var(--v-primary-base) !important;
  line-height: 40px !important;
}
