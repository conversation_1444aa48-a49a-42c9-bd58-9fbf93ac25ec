<template>
  <!-- :class="{ 'bg-gradient-info white--text ': isActive }"
    class="vertical-nav-menu-link"
    active-class="bg-gradient-info" -->
  <!-- white--text v-list-item--active -->
  <v-list-item
    color="primary"
    v-if="canViewVerticalNavMenuLink(item)"
    v-bind="linkProps"
    @mousemove="isHover = true"
    @mouseout="isHover = false"
    :class="{ 'primary--text bg-btn': isActive }"
  >
    <v-list-item-icon>
      <!-- :color="isActive ? 'white' : null" -->

      <vsoc-icon
        class="d-flex align-center"
        :icon="item.icon | getCheckedIcon(isActive)"
      ></vsoc-icon>
      <!-- <v-icon
        v-if="item.icon && item.icon.includes('mdi')"
        size="16"
        :class="{ 'alternate-icon-small': !item.icon }"
      >
        {{ item.icon || alternateIcon }}
      </v-icon> -->
    </v-list-item-icon>
    <!-- :class="{ 'white--text': isActive }" -->
    <v-list-item-title>
      {{ $generateMenuTitle(item) }}
      <!-- {{ item.title }} -->
    </v-list-item-title>
    <v-list-item-action v-if="item.badge" class="flex-shrink-0">
      <v-badge :color="item.badgeColor" inline :content="item.badge"> </v-badge>
    </v-list-item-action>
  </v-list-item>
</template>

<script>
import useVerticalNavLink from '@core/layouts/composable/vertical-nav/useVerticalNavLink'
import { useUtils as useAclUtils } from '@core/libs/acl'
import { useUtils } from '@core/libs/i18n'
import themeConfig from '@themeConfig'
import { getCurrentInstance, ref } from '@vue/composition-api'

export default {
  props: {
    item: {
      type: Object,
      required: true,
    },
  },
  setup(props) {
    const vm = getCurrentInstance().proxy
    const { isActive, linkProps } = useVerticalNavLink(props.item)
    const { t } = useUtils()
    const { canViewVerticalNavMenuLink } = useAclUtils()
    const isHover = ref(false)
    return {
      isActive,
      linkProps,
      alternateIcon: themeConfig.menu.groupChildIcon,
      isHover,
      // i18n
      t,

      // ACL
      canViewVerticalNavMenuLink,
    }
  },
}
</script>

<style lang="scss" scoped>
@import '~vuetify/src/styles/styles.sass';
//  .alternate-icon-small {
//   font-size: 14px !important;
//   height: 14px !important;
//   width: 14px !important;
//   min-width: none !important;
// }
.vertical-nav-menu-link {
  &.v-list-item--active {
    box-shadow: 0 5px 10px -4px rgba(94, 86, 105, 0.42);
    @include elevationTransition();
  }

  .v-list-item__title {
    z-index: 1;
  }
  &.v-list-item--active > a {
    background-color: rgba(red, 0.8);
  }
}

// @include theme(vertical-nav-menu-link) using ($material) {
//   &.v-list-item--active > a {
//     background-color: rgba(
//       map-deep-get($material, 'text', 'primary'),
//       map-deep-get($material, 'states', 'selected')
//     );
//   }
// }
</style>
