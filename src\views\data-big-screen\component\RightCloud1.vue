<template>
  <div class="c-box" style="padding-bottom: 5% !important">
    <div class="c-box-header">{{ $t('screen.cloud.right1') }}</div>
    <div class="box-chart d-flex flex-column">
      <!-- class="pt-4" -->
      <!-- <div class="fs-14 opacity-6 mt-4">Last 30 days</div> -->
      <vsoc-chart
        class="flex-1"
        echartId="assets-overview"
        :option="chartOption"
        @highlight="onHighlight"
      ></vsoc-chart>
    </div>
  </div>
</template>
<script>
import VsocChart from '@/components/VsocChart.vue'
import { numberToFormat } from '@/util/filters'
import chroma from 'chroma-js'
import { cloudTooltip, getRoundSize, primary } from './chart'
export default {
  name: 'RightCloud1',
  components: {
    VsocChart,
  },
  props: {
    list: {
      type: Array,
      default: () => {
        return [
          { name: 'CBS', value: 1373 },
          { name: 'Redis', value: 72 },
          { name: 'Repository', value: 578 },
          { name: 'MongoDB', value: 35 },
          { name: 'CVM', value: 831 },
          { name: 'SSL', value: 22 },
          { name: 'CDB', value: 1732 },
          { name: 'NAT-gateway', value: 13 },
          { name: 'CLB', value: 172 },
          { name: 'VPN-gateway', value: 6 },
          { name: 'COS', value: 99 },
          { name: 'Postgres', value: 1 },
        ]
      },
    },
    total: {
      type: [Number, String],
      default: () => 3748,
    },
  },
  computed: {
    chartOption() {
      const common = {
        type: 'pie',
        center: ['50%', '32%'],
        label: { show: false },
        labelLine: { show: false },
        width: '60%',
        left: 'center',
      }
      const rich = {
        name: {
          width: getRoundSize(110),
          fontSize: getRoundSize(14),
          padding: [0, getRoundSize(10), 0, 0],
        },
        value: {
          padding: [0, 0, 0, 0],
          width: getRoundSize(0),
        },
      }
      const option = {
        tooltip: cloudTooltip,
        backgroundColor: 'transparent',
        color: chroma
          .scale([
            '#0A29AE',
            '#4970F2',
            '#9BF0F1',
            '#37B7BE',
            '#45A9F1',
            '#1559FB',
            '#0A29AE',
          ])
          .mode('lch')
          .colors(this.list.length),
        legend: {
          show: true,
          // type: 'scroll',
          icon: 'circle',
          left: '15%',
          top: '60%',
          // bottom: '2%',
          // right: '10%',
          // orient: 'vertical',
          itemGap: getRoundSize(15),
          // itemWidth: 80,
          itemHeight: getRoundSize(10),
          textStyle: {
            lineHeight: getRoundSize(22),
            color: '#ffffff',
            opacity: 0.6,
            rich,
          },
          itemStyle: {
            borderWidth: 10,
          },
          formatter: name => {
            const cur = this.list.find(v => v.name === name)
            return `{name|${name}\t${cur.value}}{value|}`
          },
        },
        textStyle: {
          fontStyle: 'normal',
          fontWeight: 400,
          color: '#ffffffcc',
        },
        title: {
          text: numberToFormat(this.total),
          position: 'center',
          top: '27%',
          left: '49%',
          textAlign: 'center',
          textStyle: {
            fontSize: getRoundSize(32),
            fontWeight: 500,
            lineHeight: getRoundSize(48),
            letterSpacing: 0.08,
            color: primary,
          },
        },
        series: [
          {
            name: '里面的环',
            ...common,
            radius: ['48%', '55%'],
            emphasis: { scale: false },
            color: '#103d60',
            data: [{ value: 100, name: '' }],
          },
          {
            name: '中间的环',
            ...common,
            percentPrecision: 0,
            radius: ['65%', '82%'],
            emphasis: { scale: true },
            data: this.list,
          },
          {
            name: '外面的环',
            ...common,
            percentPrecision: 0,
            radius: ['98%', '100%'],
            emphasis: { scale: false },
            data: [
              {
                name: '',
                value: 50,
                itemStyle: {
                  color: '#44E2FE',
                },
              },
              {
                name: '',
                value: 5,
                itemStyle: {
                  color: 'transparent',
                },
              },
              {
                name: '',
                value: 50,
                itemStyle: {
                  color: '#44E2FE',
                },
              },
              {
                name: '',
                value: 5,
                itemStyle: {
                  color: 'transparent',
                },
              },
              {
                name: '',
                value: 50,
                itemStyle: {
                  color: '#44E2FE',
                },
              },
              {
                name: '',
                value: 5,
                itemStyle: {
                  color: 'transparent',
                },
              },
              {
                name: '',
                value: 50,
                itemStyle: {
                  color: '#44E2FE',
                },
              },
              {
                name: '',
                value: 5,
                itemStyle: {
                  color: 'transparent',
                },
              },
            ],
          },
        ],
      }

      return option
    },
  },
  methods: {
    onHighlight(obj, myChart) {
      const option = this.chartOption
      option.tooltip.backgroundColor = obj.color
      myChart.setOption(option)
    },
  },
}
</script>
