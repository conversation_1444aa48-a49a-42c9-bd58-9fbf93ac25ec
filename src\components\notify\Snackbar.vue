<template>
  <v-snackbar
    v-model="visible"
    bottom
    right
    :color="color"
    class="snackbar-shadow v-application"
    :timeout="timeout"
    :multi-line="true"
    height="52"
    max-height="52"
  >
    <div class="d-flex align-center alert-notify text-center">
      <!-- <v-icon size="1.5rem" class="me-3" color="white">
        {{ icon }}
      </v-icon> -->
      <div
        class="d-flex align-center flex-wrap w-100"
        style="width: 200px; max-width: 480px"
      >
        <span
          class="d-flex text-ml font-weight-medium text-white text-decoration-none ml-2 text-overflow-hide"
        >
          <div v-if="showIcon" class="d-flex align-center">
            <img
              class="mr-1"
              v-if="level !== 'error'"
              src="@/assets/images/icon-yellow.png"
              height="16"
              width="16"
            />
            <img
              v-else
              class="mr-1 d-flex align-center"
              src="@/assets/images/icon-white.png"
              height="16"
              width="16"
            />
          </div>
          {{ message }}
        </span>
      </div>
    </div>
    <template v-slot:action="{ attrs }">
      <!-- max-width="136"
        height="38"
        min-width="38"
        width="38"
              elevation="0"
        class="font-weight-600 text-capitalize py-3 px-6 rounded-sm btn-primary"

        :ripple="false"
      -->
      <v-btn
        icon
        rounded
        class="mr-3 text-capitalize"
        color="rgba(255,255,255, .85)"
        v-bind="attrs"
        @click.stop="closeSnackbar"
      >
        <vsoc-icon size="large" icon="mdi-close"> </vsoc-icon>
      </v-btn>
    </template>
  </v-snackbar>
</template>
<script>
import store from '@/store'
const messageColors = {
  success: ['#36B252', ' mdi-checkbox-marked-circle-outline'], // 默认图标为 mdi-checkbox-marked-circle-outline
  warning: ['#FBD82C', 'mdi-alert-circle-outline'], // 默认图标为mdi-alert-circle-outline
  error: ['#DA1F1F', 'mdi-close-circle-outline'], // 如果是Error 类型 则 默认图标为mdi-close-circle-outline
  info: ['primary', 'mdi-bell'], // 如果是默认 类型 则 默认图标为mdi-close-circle-outline
  // info: {color: '#FB8C00', text: '差'},
}
export default {
  data() {
    return {
      // 消息框
      icon: 'notifications',
      color: '#fff',
      visible: false,
      message: '',
      timeout: 300,
      level: '',
      showIcon: store.state.appConfig.appPrimary === 'lotus' ? true : false,
    }
  },
  methods: {
    info(level, message, timeout) {
      this.level = level
      if (store.state.appConfig.appPrimary === 'lotus') {
        if (level !== 'error') {
          this.color = '#000'
        } else {
          this.color = messageColors[level][0]
            ? messageColors[level][0]
            : '#000'
        }
      } else {
        this.color = messageColors[level][0]
          ? messageColors[level][0]
          : '#172b4d'
      }
      this.message = message
      this.visible = true
      this.icon = messageColors[level][1] ? messageColors[level][1] : 'mdi-bell'
      this.timeout = timeout || 3000
      if (
        store.state.appConfig.globalThemeMode === 'semi-dark' &&
        store.state.appConfig.appPrimary === 'lotus' &&
        level === 'info'
      ) {
        this.color = '#323232'
      }
    },
    closeSnackbar() {
      this.visible = false
      // this.snackbar.color = null  设置为空 关闭时会显示黑色 不友好 @Henry @20220507
      this.message = ''
    },
  },
}
</script>
