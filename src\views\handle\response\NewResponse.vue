<template>
  <edit-page
    class="list-edit"
    :popsName="
      editType === 'add'
        ? $t('response.btn.add')
        : editType === 'edit'
        ? $t('response.btn.edit')
        : $t('response.btn.detail')
    "
  >
    <div v-if="editType !== 'detail'" slot="BreadBtn">
      <v-btn color="primary" elevation="0" @click="confirmEdit">
        {{ $t('action.save') }}
      </v-btn>
    </div>
    <div class="response-center-box">
      <v-form ref="form" v-model="valid">
        <div
          class="response-title font-weight-semibold-light mb-4 mt-6 d-flex align-center"
        >
          <div>{{ $t('global.drawer.baseInfo') }}</div>
          <div
            v-if="responseInfo.id"
            class="ml-6 primary--text editor2 d-flex align-center"
          >
            <span>{{ responseInfo.id }}</span>
            <v-btn v-show-tips="$t('action.copy')" icon small>
              <i
                v-copy="responseInfo.id"
                class="iconfont icon-fuzhi text-root primary--text"
              ></i>
            </v-btn>
          </div>
        </div>
        <div class="px-9">
          <v-text-field
            v-model="responseInfo.responseName"
            color="primary"
            :label="$t('response.headers.responseName')"
            :rules="nameRules"
            :disabled="editType === 'detail'"
            class="is-required"
          ></v-text-field>
        </div>
        <div
          class="response-title response-event font-weight-semibold-light mb-5"
        >
          {{ $t('response.headers.responseEventName1') }}
        </div>
        <div class="px-9">
          <v-radio-group v-model="responseInfo.responseEvent" row hide-details>
            <v-row>
              <v-col
                v-for="(item, index) in responseEventList"
                :key="index"
                cols="3"
              >
                <v-radio
                  :label="item.dictName"
                  :value="item.dictId"
                  :disabled="editType === 'detail'"
                  @change="changeEvent(item)"
                ></v-radio>
              </v-col>
            </v-row>
          </v-radio-group>
        </div>
        <div class="response-title font-weight-semibold-light mb-4 mt-8">
          {{ $t('response.headers.responseCondition') }}
        </div>
        <div class="px-9">
          <v-radio-group
            v-model="responseInfo.responseConditionType"
            row
            hide-details
          >
            <v-row>
              <v-col
                v-for="(item, index) in responseConditionTypeList"
                :key="index"
              >
                <v-radio
                  :label="item.dictName"
                  :value="item.dictId"
                  :disabled="editType === 'detail'"
                ></v-radio>
              </v-col>
              <v-col />
              <v-col />
            </v-row>
          </v-radio-group>

          <div
            v-for="(item, index) in responseConditionVos"
            :key="index"
            class="mb-4"
          >
            <v-row>
              <v-col class="mr-4">
                <v-autocomplete
                  v-model="item.event"
                  :menu-props="{ offsetY: true, maxHeight: 300 }"
                  append-icon="mdi-chevron-down"
                  :items="responseConditionList[responseInfo.responseEvent]"
                  item-text="text"
                  item-value="value"
                  :label="$t('response.swal.tip1')"
                  hide-details
                  :disabled="editType === 'detail'"
                  @change="eventChange('eventChange', { item, val: $event })"
                >
                </v-autocomplete>
              </v-col>
              <v-col v-if="item.progree > 1" class="mr-4">
                <v-select
                  v-model="item.operator"
                  color="primary"
                  :label="$t('response.swal.tip2')"
                  :items="dealOperator(item)"
                  item-text="text"
                  item-value="value"
                  hide-details
                  :disabled="editType === 'detail'"
                  :menu-props="{ offsetY: true }"
                  @change="eventChange('operatorChange', { item, val: $event })"
                >
                </v-select>
              </v-col>
              <v-col v-if="item.progree > 2" class="mr-4">
                <template v-if="item.multiple">
                  <v-autocomplete
                    v-model="item.value"
                    multiple
                    :menu-props="{ offsetY: true, maxHeight: 300 }"
                    append-icon="mdi-chevron-down"
                    :items="dealValue(item)"
                    item-text="text"
                    item-value="value"
                    :label="$t('response.swal.tip3')"
                    hide-details
                    :disabled="editType === 'detail'"
                    @change="eventChange('valueChange', { item, val: $event })"
                  >
                    <template v-slot:selection="{ index }">
                      <span v-if="index === 0">
                        {{ $t('global.pagination.selected') }}：{{
                          item.value.length
                        }}
                      </span>
                    </template>
                  </v-autocomplete>
                </template>
                <template v-else>
                  <v-autocomplete
                    v-model="item.value"
                    :menu-props="{ offsetY: true, maxHeight: 300 }"
                    append-icon="mdi-chevron-down"
                    :items="dealValue(item)"
                    item-text="text"
                    item-value="value"
                    :label="$t('response.swal.tip3')"
                    hide-details
                    :disabled="editType === 'detail'"
                    @change="eventChange('valueChange', { item, val: $event })"
                  >
                  </v-autocomplete>
                </template>
              </v-col>
              <div
                v-if="index > 0 && editType !== 'detail'"
                class="del-btn"
                @click="responseConditionVos.splice(index, 1)"
              >
                <vsoc-icon
                  type="fill"
                  class="action-btn1"
                  size="x-large"
                  icon="icon-shanchu"
                ></vsoc-icon>
              </div>
            </v-row>
            <div v-if="item.error" class="logic-engine__error">
              {{ item.error }}
            </div>
          </div>

          <div
            v-if="editType !== 'detail'"
            class="d-inline-flex primary--text mt-4 mb-6 cursor-pointer"
            @click="addOperation"
          >
            <vsoc-icon type="fill" size="16px" icon="icon-xinzeng"></vsoc-icon>
            <span class="ml-2">
              {{ $t('response.btn.add1') }}
            </span>
          </div>
        </div>

        <div class="response-title font-weight-semibold-light mb-4">
          {{ $t('response.headers.Operator') }}
        </div>
        <div class="px-9">
          <div class="w-100 d-flex align-center">
            <div class="w-30">
              <v-select
                v-model="responseInfo.action"
                color="primary"
                :label="$t('response.headers.Operator')"
                :items="responseExecuteType"
                item-text="text"
                item-value="value"
                :menu-props="{ offsetY: true }"
                hide-details
                :disabled="editType === 'detail'"
              >
              </v-select>
            </div>
            <v-btn
              elevation="0"
              color="primary"
              class="ml-4"
              v-if="editType !== 'detail'"
              @click="changAction"
            >
              <span> {{ $t('response.btn.add2') }} </span>
            </v-btn>
          </div>
          <div class="mt-2" v-if="responseOperationVos.length">
            <v-tabs height="54" v-model="currentTab" show-arrows>
              <v-tab
                v-for="(n, index) in responseOperationVos"
                :key="index"
                class="mr-6"
              >
                {{ executeTypeMap[n.executeType].text }}
                <div
                  class="ml-3"
                  v-if="editType !== 'detail'"
                  @click.stop="responseOperationVos.splice(index, 1)"
                >
                  <vsoc-icon
                    v-show-tips="$t('action.del')"
                    type="fill"
                    icon="icon-shujuleixing-shibai"
                    size="large"
                  ></vsoc-icon>
                </div>
              </v-tab>
            </v-tabs>
            <v-tabs-items v-model="currentTab">
              <v-tab-item v-for="(v, i) in responseOperationVos" :key="i">
                <v-row>
                  <v-col class="mt-4 d-flex" cols="12">
                    <v-autocomplete
                      v-model="v.notificationObjectList"
                      multiple
                      :menu-props="{ offsetY: true, maxHeight: 300 }"
                      append-icon="mdi-chevron-down"
                      :items="v.actionsList"
                      chips
                      item-text="text"
                      item-value="value"
                      :label="$t('response.swal.notice1')"
                      return-object
                      :disabled="editType === 'detail'"
                      class="flex-1"
                    >
                      <template v-slot:selection="{ item, index }">
                        <v-chip
                          color="primary"
                          close
                          small
                          :disabled="editType === 'detail'"
                          @click:close="
                            v.notificationObjectList.splice(index, 1)
                          "
                        >
                          <span>{{ item.text }}</span>
                        </v-chip>
                      </template>
                    </v-autocomplete>
                    <div
                      v-if="
                        v.executeType === '0' &&
                        responseInfo.responseEvent === '0'
                      "
                      class="w-50 ml-6"
                    >
                      <v-switch
                        v-model="v.sendAttachments"
                        style="width: 180px"
                        inset
                        dense
                        false-value="1"
                        true-value="0"
                        hide-details
                        :label="$t('response.infoHeaders.send')"
                      ></v-switch>
                    </div>
                  </v-col>
                  <v-col cols="12">
                    <v-text-field
                      v-model="v.noticeTitle"
                      :label="$t('response.swal.notice2')"
                      :rules="responseRules"
                      :disabled="editType === 'detail'"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" class="mt-1">
                    <quill-editor
                      :key="editorKey"
                      ref="myQuillEditor"
                      v-model="v.noticeContent"
                      class="editor-box editor"
                      :disabled="editType === 'detail'"
                      :options="editorOption"
                    >
                    </quill-editor>
                  </v-col>
                </v-row>
              </v-tab-item>
            </v-tabs-items>
            <div
              v-if="editType !== 'detail'"
              class="d-inline-flex primary--text mt-4 cursor-pointer"
              @click="checkTag"
            >
              <vsoc-icon
                type="fill"
                size="16px"
                icon="icon-quanjusousuo"
              ></vsoc-icon>
              <span class="ml-2">
                {{ $t('response.swal.tip4') }}
              </span>
            </div>
          </div>
        </div>
      </v-form>
    </div>
    <vsoc-drawer
      width="500px"
      v-model="isShow"
      :title="$t('response.swal.tip4')"
      hideFooter
    >
      <div class="pt-3">
        {{ $t('response.swal.tip5') }}
      </div>
      <v-text-field
        v-model="search"
        color="primary"
        hide-details
        :label="$t('response.swal.parameter')"
      ></v-text-field>
      <v-data-table
        :headers="headers"
        hide-default-footer
        fixed-header
        :items-per-page="999"
        :items="tagTable"
        :search="search"
        :height="tableHeight"
        item-key="value"
        class="table border-radius-xl mt-3 thead-light"
      >
        <template v-slot:item.value="{ item }">
          <div class="d-flex">
            <span>{{ item.value }}</span>
            <vsoc-icon
              type="fill"
              class="action-btn ml-2 cursor-pointer"
              icon="icon-fuzhi"
              v-copy="item.value"
            />
          </div>
        </template>
      </v-data-table>
    </vsoc-drawer>
  </edit-page>
</template>

<script>
import { max, required } from '@/@core/utils/validation'
import { getAllClassify } from '@/api/classify/index'
import { addResponse, editResponse, linkageDetail } from '@/api/response/index'
import { querySysGroupList } from '@/api/system/organization'
import { findAllUsers } from '@/api/system/user'
import editPage from '@/components/EditPage.vue'
import VsocDialog from '@/components/VsocDialog.vue'
import VsocDrawer from '@/components/VsocDrawer.vue'
import VsocPagination from '@/components/VsocPagination.vue'
import { dataFilter } from '@/util/filters'
import { deepClone, setRemainingHeight } from '@/util/utils'
import 'quill/dist/quill.bubble.css'
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import { quillEditor } from 'vue-quill-editor'
import { flatTree, getLogicItem, handleChange, tagList } from './tools.js'

export default {
  name: 'NewResponse',
  components: {
    VsocPagination,
    VsocDialog,
    editPage,
    quillEditor,
    VsocDrawer,
  },
  filters: {
    dataFilter,
  },
  data() {
    return {
      editorKey: 0,
      search: '',
      tableHeight: '34.5rem',

      isShow: false,

      tagTable: [],
      editType: 'add',
      valid: true,
      responseInfo: {
        id: '',
        responseName: '',
        responseEvent: '0', //响应事件 0工单创建 1工单更新 2告警创建 3告警更新
        responseConditionType: '0', //响应条件类型 0满足以下所有条件 1满足以下任一条件
        action: '',
        logic: '0',
      },
      nameRules: [
        v => required(v, this.$t('response.headers.responseName')),
        v => max(v, 256),
      ],
      responseRules: [
        v => required(v, this.$t('response.swal.notice2')),
        v => max(v, 256),
      ],
      responseOperationVos: [],
      currentTab: 0,
      responseName: '',
      triggerCondition: '',

      actionsList: [],
      responseConditionVos: [],
      conditions: [],
      userList: [],
      userList1: [],
      groupList: [],
      ticketClassify: [],
      alertClassify: [],
      tableDataTotal: 0,
      confirmLoading: false,
      isFlag: false,
    }
  },
  watch: {
    '$i18n.locale': {
      handler(newVal) {
        this.editorKey++
      },
      deep: true,
    },
  },
  created() {
    this.$_setTableHeight()
    this.$bus.$on('resize', this.$_setTableHeight)
    if (this.$route.query.id) {
      if (this.$route.query.isDetail === '1') {
        this.editType = 'detail'
      } else {
        this.editType = 'edit'
      }
      Promise.all([this.getClassify(), this.getUsers(), this.getGroup()]).then(
        e => {
          this.getInfo()
        },
      )
    } else {
      this.editType = 'add'
      this.getClassify()
      this.getUsers()
      this.getGroup()
      this.changeEvent({ value: this.responseInfo.responseEvent })
    }
  },
  beforeDestroy() {
    this.$bus.$off('resize', this.$_setTableHeight)
  },
  computed: {
    headers() {
      return [
        {
          text: this.$t('response.infoHeaders.type'),
          value: 'type',
          width: '80px',
          filterable: false,
        },
        {
          text: this.$t('response.infoHeaders.value'),
          value: 'value',
          width: '80px',
        },
        {
          text: this.$t('response.infoHeaders.name'),
          value: 'name',
          width: '130px',
        },
      ]
    },
    editorOption() {
      return {
        placeholder: this.$t('alertAction.headers.noticeContent'),
        modules: {
          toolbar: false,
        },
      }
    },
    executeTypeMap() {
      return this.$store.state.enums.enums['Response Execute Type']
    },
    responseEventList() {
      return this.$store.getters['enums/getResponseEvent']
    },
    responseEventMap() {
      return this.$store.state.enums.enums['Response Event']
    },
    responseConditionTypeList() {
      return this.$store.getters['enums/getResponseConditionType']
    },
    responseConditionTypeMap() {
      return this.$store.state.enums.enums['Response Condition Type']
    },
    responseConditionList() {
      return this.$store.getters['enums/getResponseConditionField']
    },
    responseConditionOperator() {
      return this.$store.getters['enums/getResponseConditionOperator']
    },
    ticketStatus() {
      return this.$store.getters['enums/getTicketStatus']
    },
    ticketLevel() {
      return this.$store.getters['enums/getTicketLevel']
    },
    ticketSource() {
      let sources = deepClone(this.$store.getters['enums/getTicketDataSource'])
      sources.unshift({
        text: this.$t('global.null'),
        value: '-1',
      })
      return sources
    },
    alertStatus() {
      return this.$store.getters['enums/getAlertStatus']
    },
    alertLevel() {
      return this.$store.getters['enums/getAlarmLevel']
    },
    dataSources() {
      let sources = deepClone(this.$store.getters['enums/getDataSources'])
      sources.unshift({
        text: this.$t('global.null'),
        value: '-1',
      })
      return sources
    },
    responseExecuteType() {
      const allTypes = this.$store.getters['enums/getResponseExecuteType']
      return this.$store.getters['enums/getDingType'] === 'true'
        ? allTypes
        : allTypes.filter(v => v.dictId !== '2')
    },
    responseNotificationObject() {
      return this.$store.getters['enums/getResponseNotificationObject']
    },
  },
  methods: {
    async getClassify() {
      const res = await getAllClassify({ type: '' })
      res.data = res.data.map(v => {
        return {
          ...v,
          text: v.name,
          value: String(v.id),
        }
      })
      this.alertClassify = res.data.filter(v => v.type === '0')
      this.ticketClassify = res.data.filter(v => v.type === '1')
    },
    async getUsers() {
      const res = await findAllUsers({ status: '1' })
      this.userList = res.data.map(v => {
        return {
          ...v,
          value: v.userId,
          text: v.userName,
        }
      })
      this.userList1 = [
        {
          text: '空',
          value: '-1',
        },
      ].concat(this.userList)
    },
    async getGroup() {
      this.isFlag = false
      const res = await querySysGroupList({ state: '0' })
      this.groupList = flatTree(res.data)
      this.groupList = this.groupList.map(v => {
        return {
          ...v,
          value: v.id,
          text: v.name,
        }
      })
      this.groupList.unshift({
        text: this.$t('global.null'),
        value: '-1',
      })

      this.isFlag = true
    },
    async getInfo() {
      const res = await linkageDetail({ id: this.$route.query.id })

      this.responseInfo = res.data
      this.$set(this.responseInfo, 'action', '')
      this.responseConditionVos = []
      res.data.responseConditionVos.forEach(v => {
        const item = getLogicItem()
        item.event = v.conditionField
        item.operator = v.operator
        item.value = v.valueList
        item.isEdit = true
        item.responseEvent = this.responseInfo.responseEvent
        this.responseConditionVos.push(item)
      })
      this.$nextTick(() => {
        this.responseConditionVos.forEach(v => {
          handleChange['eventChange'].call(this, {
            item: v,
            isInit: true,
          })
          handleChange['operatorChange'].call(this, {
            item: v,
            isInit: true,
          })
          handleChange['valueChange'].call(this, {
            item: v,
            val: v.multiple ? v.value : v.value[0],
          })
        })
      })

      this.responseOperationVos = []

      res.data.responseOperationVos.forEach(v => {
        const params = {
          operaName: v.executeTypeName,
          executeType: v.executeType,
          notificationObjectList: [],
          noticeTitle: v.noticeTitle, //通知主题
          noticeContent: v.noticeContent, //通知内容
          actionsList: [],
          sendAttachments: v.sendAttachments,
        }
        params.actionsList = this.dealAction(params)

        params.notificationObjectList = params.actionsList.filter(
          x =>
            (x.notificationType === 1 &&
              v.notificationObjectList &&
              v.notificationObjectList.indexOf(x.value) !== -1) ||
            (x.notificationType !== 1 &&
              v.recipientList &&
              v.recipientList.indexOf(x.value) !== -1),
        )
        this.responseOperationVos.unshift(params)
        // params.actionsList.forEach(x => {
        //   let item = ''
        //   if (x.notificationType === 1) {
        //     item = v.notificationObjectList.find(
        //       y => Number(y) === Number(x.value),
        //     )
        //   } else {
        //     item =
        //       v.recipientList &&
        //       v.recipientList.find(y => Number(y) === Number(x.value))
        //   }
        //   if (item) {
        //     params.notificationObjectList.push(x)
        //   }
        // })
      })
    },
    dealOperator(item) {
      return this.responseConditionOperator.filter(v =>
        item.operatorList.find(item => item.value === v.value),
      )
    },
    dealValue(item) {
      if (item.event === '0') {
        item.valueList = this.ticketStatus
      } else if (item.event === '1') {
        item.valueList = this.ticketLevel
      } else if (item.event === '2') {
        item.valueList = this.ticketClassify
      } else if (item.event === '3') {
        if (this.responseInfo.responseEvent === '1') {
          item.valueList = this.userList
        } else {
          item.valueList = this.userList1
        }
      } else if (['4', '21', '22'].indexOf(item.event) !== -1) {
        item.valueList = this.userList1
      } else if (item.event === '5') {
        item.valueList = this.groupList
      } else if (item.event === '6') {
        item.valueList = this.ticketSource
      } else if (item.event === '18') {
        item.valueList = this.alertStatus
      } else if (item.event === '19') {
        item.valueList = this.alertLevel
      } else if (item.event === '20') {
        item.valueList = this.alertClassify
      } else if (item.event === '23') {
        item.valueList = this.dataSources
      }
      return item.valueList
    },
    $_setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = setRemainingHeight() + 30
      })
    },
    changAction() {
      if (!this.responseInfo.action) return
      let item = this.responseExecuteType.find(
        v => v.value === this.responseInfo.action,
      )
      const params = {
        operaName: item.text,
        executeType: item.value,
        notificationObjectList: [], //通知对象
        noticeTitle: '', //通知主题
        noticeContent: '', //通知内容
        actionsList: [],
        sendAttachments: '1',
      }
      params.actionsList = this.dealAction(params)
      this.$nextTick(() => {
        this.responseOperationVos.unshift(params)
        this.responseInfo.action = ''
      })
    },
    dealAction(params) {
      let actionsList = []
      if (
        this.responseInfo.responseEvent === '0' ||
        this.responseInfo.responseEvent === '1'
      ) {
        if (params.executeType === '2') {
          actionsList.push(
            {
              value: '3',
              text: '工单处理组',
              notificationType: 1,
            },
            { header: '系统内处理组' },
          )
          actionsList = actionsList.concat(
            this.groupList.filter(v => v.value !== '-1'),
          )
        } else {
          actionsList.push(
            { value: '0', text: '工单创建人', notificationType: 1 },
            { value: '1', text: '工单处理人', notificationType: 1 },
            { header: '系统内用户' },
          )
          actionsList = actionsList.concat(this.userList)
        }
      } else {
        if (params.executeType === '2') {
          actionsList.push({ header: '系统内处理组' })
          actionsList = actionsList.concat(
            this.groupList.filter(v => v.value !== '-1'),
          )
        } else {
          actionsList.push(
            { value: '4', text: '告警创建人', notificationType: 1 },
            { value: '5', text: '告警更新人', notificationType: 1 },
            { header: '系统内用户' },
          )
          actionsList = actionsList.concat(this.userList)
        }
      }
      return actionsList
    },
    //变更响应事件
    changeEvent(item) {
      const logic = getLogicItem()
      logic.responseEvent = item.value
      this.responseConditionVos = [logic]
      // this.conditions = this.responseConditionList[item.value]
      this.responseInfo.action = ''
      this.responseOperationVos = []
    },
    // 当某阶段的值发生变化，选择逻辑源
    eventChange(eventName, params) {
      handleChange[eventName].call(this, params)
    },
    //添加条件
    addOperation() {
      const logic = getLogicItem()
      logic.responseEvent = this.responseInfo.responseEvent
      this.responseConditionVos.push(logic)
    },
    checkTag() {
      this.isShow = true
      this.search = ''
      if (
        this.responseInfo.responseEvent === '0' ||
        this.responseInfo.responseEvent === '1'
      ) {
        this.tagTable = tagList[0]
      } else {
        this.tagTable = tagList[1]
      }
    },
    validate() {
      const bool = []
      this.responseConditionVos.forEach(item => {
        item.error = ''
        if (!item.settingOver) {
          item.error = this.$t('response.swal.delTip1')
          bool.push(item.error)
        }
      })
      return bool
    },
    async confirmEdit() {
      const bool = this.$refs.form.validate()
      if (!bool) return
      if (this.responseConditionVos.filter(v => v.settingOver).length === 0) {
        return this.$notify.info('error', this.$t('response.swal.delTip3'))
      }
      const validateList = this.validate()
      if (validateList.length) {
        return this.$notify.info('error', validateList[0])
      }
      if (this.responseOperationVos.length === 0) {
        return this.$notify.info('error', this.$t('response.swal.delTip4'))
      } else {
        const boolOpera = this.responseOperationVos.filter(
          v =>
            !v.noticeTitle ||
            !v.noticeContent ||
            v.notificationObjectList.length === 0,
        )
        if (boolOpera.length) {
          return this.$notify.info('error', this.$t('response.swal.delTip2'))
        }
      }

      let responseConditionVos = []
      this.responseConditionVos.forEach(v => {
        responseConditionVos.push({
          conditionField: v.event,
          operator: v.operator,
          valueList: v.value ? (v.multiple ? v.value : [v.value]) : [],
        })
      })

      let responseOperationVos = []
      this.responseOperationVos.forEach(v => {
        let notificationObjectList = v.notificationObjectList.filter(
          v => v.notificationType === 1,
        )
        let recipientList = v.notificationObjectList.filter(
          v => v.notificationType !== 1,
        )
        responseOperationVos.push({
          sendAttachments: v.sendAttachments,
          executeType: v.executeType,
          notificationObjectList: notificationObjectList.length
            ? notificationObjectList.map(v => v.value)
            : [],
          noticeTitle: v.noticeTitle,
          noticeContent: v.noticeContent,
          recipientList: recipientList.length
            ? recipientList.map(v => v.value)
            : [],
        })
      })
      const params = {
        responseName: this.responseInfo.responseName,
        responseEvent: this.responseInfo.responseEvent,
        responseConditionType: this.responseInfo.responseConditionType,
        responseConditionVos: responseConditionVos,
        responseOperationVos: responseOperationVos,
      }
      try {
        this.confirmLoading = true
        if (this.editType === 'add') {
          const res = await addResponse(params)
          if (res.code === 200) {
            this.$notify.info(
              'success',
              this.$t('global.hint.add', [this.$t('response.currentTitle')]),
            )
            this.$router.push('/ResponseIndex')
          }
        } else if (this.editType === 'edit') {
          params.id = this.responseInfo.id
          const res = await editResponse(params)
          if (res.code === 200) {
            this.$notify.info(
              'success',
              this.$t('global.hint.edit', [this.$t('response.currentTitle')]),
            )
            this.$router.go(-1)
          }
        }
      } catch (error) {
        console.error(`创建错误：${error}`)
      } finally {
        this.confirmLoading = false
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.response-center-box {
  font-size: 14px;
  color: var(--v-color-base) !important;
  padding-bottom: 64px;

  .logic-engine__error {
    font-size: 12px;
    color: var(--v-error-base) !important;
  }
  .response-title {
    font-size: 16px !important;
  }
  .response-event {
    margin-top: 10px;
  }
  .row {
    margin: 0;
  }
  .col {
    padding: 0;
  }
  .del-btn {
    width: 36px;
    height: 48px;
    background: #f5f6fa !important;
    border-radius: 4px;
    line-height: 48px;
    cursor: pointer;
    color: #686e7c;
    .action-btn1 {
      justify-content: center;
    }
  }
  .del-btn:hover {
    background: rgba(1, 76, 241, 0.1);
    color: $primary !important;
  }

  ::v-deep .v-slide-group__prev,
  .v-slide-group__next {
    flex: 1 !important;
    min-width: 16px !important;
  }
  ::v-deep .v-slide-group__prev {
    margin-right: 24px !important;
  }
  ::v-deep .v-slide-group__next {
    margin-left: 24px !important;
  }
  ::v-deep .v-input--radio-group {
    margin-top: 0 !important;
    padding-top: 0 !important;
  }
  ::v-deep .v-input--radio-group .v-radio {
    margin-right: 0;
    // width: 220px;
  }
  ::v-deep .v-input--radio-group .v-input__slot {
    margin-bottom: 0;
    height: 36px !important;
  }
  // ::v-deep .v-input--radio-group .v-label {
  //   font-size: 12px !important;
  //   color: #1f2533;
  // }
  ::v-deep .v-input--radio-group .v-input--selection-controls__input {
    margin-left: -4px !important;
    margin-right: 4px !important;
  }
  ::v-deep .v-slide-group__wrapper {
    box-shadow: none !important;
  }
  ::v-deep .v-tab {
    font-size: 14px !important;
    max-width: none !important;
  }
  .editor {
    height: 200px !important;
    // ::v-deep .ql-editor {
    //   padding: 8px 12px !important;
    //   color: var(--v-color-base) !important;
    //   font-size: 14px !important;
    // }
    // ::v-deep .ql-container.ql-snow {
    //   border-color: #e0dede !important;
    //   border-radius: 5px !important;
    // }

    // ::v-deep .ql-editor.ql-blank::before {
    //   font-style: normal !important;
    //   color: rgba(94, 86, 105, 0.68) !important;
    //   font-size: 1rem !important;
    // }
    // ::v-deep .ql-container.ql-snow.ql-disabled {
    //   .ql-editor {
    //     color: rgba(94, 86, 105, 0.38) !important;
    //   }
    // }
  }
}
</style>
