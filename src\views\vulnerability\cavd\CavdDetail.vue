<!-- 漏洞详情 -->
<template>
  <div>
    <bread-crumb>
      <template slot="title">
        <div class="d-flex align-center color-base text-title">
          <v-chip
            v-if="$toItem(cavdVulnerabilityLevelEnums, form.vulLevel)"
            :color="$toItem(cavdVulnerabilityLevelEnums, form.vulLevel).color"
            class="rounded-lg"
          >
            <vsoc-icon
              size="x-large"
              icon="icon-loudongdengjibiaozhi"
              type="fill"
            ></vsoc-icon>
            <span class="ml-1">{{
              $toItem(cavdVulnerabilityLevelEnums, form.vulLevel).text
            }}</span>
          </v-chip>
          <span class="ml-2 text-no-wrap">{{ form.cavdNo }}</span>
          <span class="ml-1 text-no-wrap">{{
            $t('vulnerability.edit.title3')
          }}</span>
        </div>
      </template>
      <template slot="left">
        <div
          class="d-flex justify-space-between align-center pl-8 text-overflow-hide"
        >
          <div
            class="ml-2 text-title font-weight-medium color-base text-overflow-hide"
          >
            {{ form.vulName }}
          </div>
        </div>
      </template>
      <v-btn color="primary" elevation="0" @click="addTicket">
        {{ $t('alert.btn.ticket') }}
      </v-btn>
    </bread-crumb>

    <v-card tile class="h-100 overflow-y-auto" elevation="0">
      <v-card-text class="pa-0">
        <div
          class="overflow-y px-10"
          :style="{ height: `calc(100vh - ${topHeaderHeight}px)` }"
        >
          <h6
            class="d-flex align-center text-sm-h6 text-typo font-weight-bolder mb-2 my-6"
          >
            <span class="text-title font-weight-medium">{{
              $t('global.drawer.baseInfo')
            }}</span>
          </h6>

          <div class="mx-6 mt-6">
            <v-form>
              <v-row>
                <v-col cols="4">
                  <!-- <v-text-field
                    v-model="form.cnnvdId"
                    :label="$t('vulnerability.headers.cnnvdId')"
                    hide-details
                    disabled
                  ></v-text-field> -->
                  <div class="text-root-base accent--text">
                    {{ $t('vulnerability.cavd.id') }}
                  </div>
                  <div class="text-content color-base mt-1 font-weight-medium">
                    {{ form.cavdNo }}
                  </div>
                </v-col>
                <v-col cols="4">
                  <!-- <v-text-field
                    v-model="form.cveId"
                    :label="$t('vulnerability.headers.cveId')"
                    hide-details
                    disabled
                  ></v-text-field> -->
                  <div class="text-root-base accent--text">
                    {{ $t('vulnerability.headers.cveId') }}
                  </div>
                  <div
                    class="text-content color-base mt-1 font-weight-medium"
                    v-html="form.cveContent || 'N/A'"
                  ></div>
                </v-col>

                <v-col cols="4">
                  <div class="text-root-base accent--text">
                    {{ $t('vulnerability.cavd.publicDate') }}
                  </div>
                  <div class="text-content color-base mt-1 font-weight-medium">
                    {{
                      form.publishDatestr
                        | toDate(form.publishDatestr, 'YYYY-MM-DD')
                        | dataFilter
                    }}
                  </div>
                </v-col>

                <v-col cols="4">
                  <div class="text-root-base accent--text">
                    {{ $t('vulnerability.cavd.publicTime') }}
                  </div>
                  <div class="text-content color-base mt-1 font-weight-medium">
                    {{ form.publishDate | toDate | dataFilter }}
                  </div>
                </v-col>
                <v-col cols="4">
                  <div class="text-root-base accent--text">
                    {{ $t('global.updateDate') }}
                  </div>
                  <div class="text-content color-base mt-1 font-weight-medium">
                    {{ form.updateDate | toDate | dataFilter }}
                  </div>
                </v-col>

                <v-col cols="4">
                  <div class="text-root-base accent--text">
                    {{ $t('vulnerability.headers.dataSource') }}
                  </div>
                  <div
                    class="text-content color-base mt-1 font-weight-medium"
                    v-if="sourceEnums[form.source]"
                  >
                    {{ sourceEnums[form.source].text }}
                  </div>
                </v-col>

                <v-col cols="4">
                  <div class="text-root-base accent--text">
                    CAVD {{ $t('global.createDate') }}
                  </div>
                  <div class="text-content color-base mt-1 font-weight-medium">
                    {{ form.createTime | toDate | dataFilter }}
                  </div>
                </v-col>

                <v-col cols="4">
                  <div class="text-root-base accent--text">
                    CAVD {{ $t('global.updateDate') }}
                  </div>
                  <div class="text-content color-base mt-1 font-weight-medium">
                    {{ form.updateTime | toDate | dataFilter }}
                  </div>
                </v-col>

                <v-col cols="4">
                  <div class="text-root-base accent--text">
                    CAVD {{ $t('alertAction.headers.version') }}
                  </div>
                  <div class="text-content color-base mt-1 font-weight-medium">
                    {{ form.version | dataFilter }}
                  </div>
                </v-col>

                <v-col cols="4">
                  <div class="text-root-base accent--text">
                    {{ $t('vulnerability.headers.createTime') }}
                  </div>
                  <div class="text-content color-base mt-1 font-weight-medium">
                    {{ form.syncTime | toDate | dataFilter }}
                  </div>
                </v-col>

                <v-col cols="4">
                  <div class="text-root-base accent--text">
                    {{ $t('vulnerability.cavd.synchronizer') }}
                  </div>
                  <div class="text-content color-base mt-1 font-weight-medium">
                    {{ form.syncUser | dataFilter }}
                  </div>
                </v-col>
              </v-row>
            </v-form>
            <v-divider class="mt-4 divider--dashed"></v-divider>
            <h6
              class="d-flex align-center text-sm-h6 text-typo font-weight-bolder mb-2 mt-6"
            >
              <span class="text-root-base accent--text">{{
                $t('vulnerability.edit.desc')
              }}</span>
            </h6>
            <p
              class="text-content color-base font-weight-medium"
              v-html="form.vulDescContent || 'N/A'"
            ></p>

            <v-divider class="mt-6 divider--dashed"></v-divider>
            <h6
              class="d-flex align-center text-sm-h6 text-typo font-weight-bolder mb-2 mt-6"
            >
              <span class="text-root-base accent--text">{{
                $t('vulnerability.edit.title2')
              }}</span>

              <v-icon
                class="ml-2"
                v-show-tips="$t('vulnerability.tip')"
                size="16"
                >mdi-help-circle-outline</v-icon
              >
            </h6>

            <v-chip
              v-for="(item, index) in splitAffectedComponent(
                form.affectedComponent,
              )"
              :key="index + 'affectedComponent'"
              class="mr-3 mt-3"
              :class="{
                'primary--text opacity-b1 bg-transparent': index === 0,
              }"
            >
              {{ item }}
            </v-chip>

            <v-divider class="mt-4 divider--dashed"></v-divider>
            <h6
              class="d-flex align-center text-sm-h6 text-typo font-weight-bolder mb-2 mt-6"
            >
              <span class="text-root-base accent--text">{{
                $t('vulnerability.cavd.repair')
              }}</span>
            </h6>
            <p class="text-content color-base font-weight-medium">
              {{ form.fixSuggestion | dataFilter }}
            </p>

            <v-divider class="mt-4 divider--dashed"></v-divider>
            <h6
              class="d-flex align-center text-sm-h6 text-typo font-weight-bolder mb-2 mt-6"
            >
              <span class="text-root-base accent--text">{{
                $t('vulnerability.cavd.verify')
              }}</span>
            </h6>
            <p
              class="text-content color-base font-weight-medium"
              v-html="form.validationinfo || 'N/A'"
            ></p>

            <v-divider class="mt-6 divider--dashed"></v-divider>
            <h6
              class="d-flex align-center text-sm-h6 text-typo font-weight-bolder mb-2 mt-6"
            >
              <span class="text-root-base accent--text">{{
                $t('vulnerability.link')
              }}</span>
            </h6>
            <p>
              <span
                class="text-content font-weight-medium"
                v-html="form.refLinkContent || 'N/A'"
              >
              </span>
            </p>

            <v-divider class="mt-6 divider--dashed"></v-divider>

            <h6
              class="d-flex align-center text-sm-h6 text-typo font-weight-bolder mb-2 mt-6"
            >
              <span class="text-root-base accent--text">{{
                $t('vulnerability.patch')
              }}</span>
            </h6>
            <p>
              <span
                class="text-content font-weight-medium"
                v-html="form.vulPatchContent || 'N/A'"
              >
              </span>
            </p>
          </div>
        </div>
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
import { getCavdDetail } from '@/api/vulnerability/index'
import breadCrumb from '@/components/bread-crumb/index'
import { setLocalStorage } from '@/util/localStorage'
export default {
  name: 'CavdDetail',
  components: {
    breadCrumb,
  },
  data() {
    return {
      form: {
        cnnvdName: 'MediaWiki限制绕过漏洞',
        description:
          "MediaWiki是用PHP编写的免费软件开源wiki包，最初用于维基百科，现在也被非营利Wikimedia基金会的若干其他项目和许多其他wikis使用。 MediaWiki存在限制绕过漏洞，该漏洞源于'user'的'$wgRateLimits' rate limiter条目会覆盖'newbie'用户的设置，远程认证用户可利用该漏洞绕过预期的限制。",
        loopholeType: '0',
        publicDate: '2019-06-24T00:00:00.000+0000',
        patch: 'MediaWiki限制绕过漏洞的补丁',
        patchUrl:
          '厂商已发布了漏洞修复程序，请及时关注更新： https://lists.wikimedia.org/pipermail/mediawiki-announce/2018-September/000223.html',
        solution:
          "MediaWiki是用PHP编写的免费软件开源wiki包，最初用于维基百科，现在也被非营利Wikimedia基金会的若干其他项目和许多其他wikis使用。 MediaWiki存在限制绕过漏洞，该漏洞源于'user'的'$wgRateLimits' rate limiter条目会覆盖'newbie'用户的设置，远程认证用户可利用该漏洞绕过预期的限制。目前，供应商发布了安全公告及相关补丁信息，修复了此漏洞。",
        attachment: null,
        cvssScore: null,
        cvssVersion: null,
        cnnvdLevel: '1',
        id: 1,
        verification: null,
        cnnvdId: 'CNVD-2019-18907',
        cveIdUrl: 'null',
        cveId: 'CVE-2018-0503',
        loopholeTypeName: '通用软硬件漏洞',
        updateTime: null,
        vulnerabilityLevelName: '中危',
        dataSourceName: 'cnvd',
        referenceUrl: 'https://securitytracker.com/id/1041695',
        submitTime: '2018-09-21T00:00:00.000+0000',
        recordTime: null,
        influenceProduct: [
          'MediaWiki Mediawiki 1.31',
          'MediaWiki Mediawiki 1.30.1',
          'MediaWiki Mediawiki 1.29.3',
          'MediaWiki Mediawiki 1.27.5',
        ],
        createTime: '2023-05-19T02:41:16.000+0000',
        createUser: null,
        dataSource: '1',
        cvss: null,
      },
    }
  },
  computed: {
    topHeaderHeight() {
      return this.$store.getters['global/getTopHeaderHeight']
    },
    cavdVulnerabilityLevelEnums() {
      return this.$store.getters['enums/getCavdVulnerabilityLevel']
    },
    sourceEnums() {
      return this.$store.getters['enums/getVulnerabilitySource']
    },
  },
  created() {
    this.loadDetail()
  },
  methods: {
    splitAffectedComponent(str) {
      if (!str) {
        return 'N/A'
      }
      // (?=[0-9](.[0,9])*)
      const newStr = str.replace(/(,|，|;|；)/g, '||')
      return newStr.split('||')
    },
    //转工单
    addTicket() {
      // 0其他1超危2高3中4低 0严重 1高 2中 3低
      const params = {
        priority:
          Number(this.form.vulLevel) > 0
            ? (Number(this.form.vulLevel) - 1).toString()
            : '3',
        title:
          this.form.cavdNo +
          '_' +
          this.$toItem(this.cavdVulnerabilityLevelEnums, this.form.vulLevel)
            .text +
          '_' +
          this.form.vulName, //漏洞编号_漏洞等级_漏洞名称
        ticketContent: this.form.vulDescContent,
        dataSource: '1',
        relationId: this.form.cavdNo,
      }
      setLocalStorage('alertTicket', JSON.stringify(params))
      // localStorage.setItem('alertTicket', JSON.stringify(params))
      this.$router.push('/ticket/addTicket?type=1')
    },
    showUrl(url) {
      return url && url.trim().startsWith('http')
    },
    async loadDetail() {
      try {
        const params = {
          cavdNo: this.$route.query.cavdNo,
        }
        const { data } = await getCavdDetail(params)
        this.form = data
      } catch (err) {
        console.log('漏洞详情报错', err)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .v-text-field > .v-input__control > .v-input__slot:before {
  width: 0;
}
::v-deep .v-input--is-disabled input {
  color: var(--v-color-base);
}
.col-6 {
  padding: 0 1rem 1rem;
}
</style>
