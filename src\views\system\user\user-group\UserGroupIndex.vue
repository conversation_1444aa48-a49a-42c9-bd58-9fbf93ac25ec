<template>
  <v-card>
    <v-card-text class="pa-0">
      <div class="px-4 d-flex justify-space-between align-center pt-4">
        <div class="w-30 d-flex align-end">
          <v-text-field
            v-model="query.groupName"
            class="flex-6"
            color="primary"
            hide-details="auto"
            label="用户组名称"
            dense
            outlined
            @keyup.enter.native="$_search"
          ></v-text-field>
          <v-btn outlined color="primary" class="ml-4" @click="$_search">
            <v-icon class="me-1"> mdi-magnify </v-icon>
            <span>查询</span>
          </v-btn>
        </div>
        <div class="d-flex justify-end align-center">
          <!-- <v-btn
            levation="0"
            height="2.3rem"
            color="primary"
            class="flex-1 bg-gradient-primary px-6  shadow-0 font-weight-semibold"
            @click="add"
          >
            新增用户组
          </v-btn> -->

          <v-btn color="primary" class="me-1" @click="add">
            <v-icon>mdi-plus</v-icon>
            <span> 新增用户组 </span>
          </v-btn>
        </div>
      </div>
      <!--:search="search"-->
      <v-data-table
        fixed-header
        :items-per-page="query.pageSize"
        item-key="groupId"
        :height="tableHeight"
        hide-default-footer
        :headers="headers"
        :items="tableData"
        class="table border-radius-xl mt-4 thead-dark"
        :loading="tableLoading"
      >
        <template v-slot:item.groupName="{ item }">
          <span v-show-tips>{{ item.groupName }}</span>
        </template>

        <template v-slot:item.description="{ item }">
          <span v-show-tips>{{ item.description }}</span>
        </template>

        <template v-slot:item.state="{ item }">
          <v-icon v-if="item.state == 1" size="1.5rem" :color="$activeColor">
            mdi-check-circle
          </v-icon>
          <v-icon v-else size="1.5rem" :color="$inactiveColor">
            mdi-close-circle
          </v-icon>
        </template>

        <template v-slot:item.updateDate="{ item }">
          <span v-show-tips>{{ item.updateDate | toDate }}</span>
        </template>
        <template v-slot:item.actions="{ item }">
          <v-btn icon @click.stop="edit(item)">
            <v-icon size="1.25rem"> mdi-pencil </v-icon>
          </v-btn>
          <v-btn :loading="item.btnLoading" icon @click.stop="del(item)">
            <v-icon size="1.25rem"> mdi-delete </v-icon>
          </v-btn>
        </template>
      </v-data-table>

      <!-- 分页器 -->
      <vsoc-pagination
        :page.sync="query.pageNum"
        :size.sync="query.pageSize"
        :total="tableDataTotal"
        @change-page="getTableData"
        @change-size="$_search"
      >
        <template #prev>
          <span class="pr-0">
            总数 :
            <span class="font-weight-semibold">{{ tableDataTotal }}</span>
          </span>
        </template>
      </vsoc-pagination>
    </v-card-text>

    <vsoc-drawer
      v-model="showEditGroup"
      :title="editType === 'add' ? '新增用户组' : '修改用户组'"
      @click:confirm="confirmEditGroup"
    >
      <v-form ref="form" v-model="valid">
        <div class="text-lg font-weight-semibold mb-4">名称</div>
        <v-text-field
          v-model="editGroup.groupName"
          outlined
          dense
          color="primary"
          label="名称"
          :rules="groupRules.groupName"
        ></v-text-field>
        <div class="text-lg font-weight-semibold mb-4">描述</div>
        <!--<v-text-field  outlined dense color="primary" v-model="editGroup.description"-->
        <!--label="描述"></v-text-field>-->

        <v-textarea
          v-model="editGroup.description"
          label="描述"
          placeholder="eg. 一线运维"
          dense
          required
          outlined
          :rows="$AREA_ROWS"
          color="primary"
        >
        </v-textarea>

        <!--                <div class="text-lg font-weight-semibold mb-4">状态</div>
                                <v-btn-toggle mandatory active-class="primary-light text-white " v-model="editGroup.state">
                                    <v-btn height="2.5rem" width="8rem" class="px-6" value="1">
                                        <v-icon size="1.25rem" :color="$activeColor" left>
                                            check_circle
                                        </v-icon>
                                        有效
                                    </v-btn>
                                    <v-btn height="2.5rem" width="8rem" value="0" :disabled="editType === 'add'">
                                        <v-icon size="1.25rem" :color="$inactiveColor" left>
                                            mdi-close-circle
                                        </v-icon>
                                        不活跃
                                    </v-btn>
                                </v-btn-toggle>-->

        <!--                <div class="text-lg font-weight-semibold mb-4">资产类型</div>
                                <v-btn-toggle mandatory active-class="primary-light text-white " v-if="editType !== 'edit'">
                                    <v-btn height="2.5rem" width="8rem" class="px-6" value="Vehicle">
                                        <v-icon groupName="vehicle" width="28" height="21"></v-icon>
                                        车辆
                                    </v-btn>
                                    <v-btn height="2.5rem" width="8rem" value="Consumer">
                                        <v-icon size="1.125rem" class="pr-2">
                                            mdi-cellphone-link
                                        </v-icon>
                                        App
                                    </v-btn>
                                </v-btn-toggle>
                                <p v-else class="text-body">{{editGroup.assetType}}</p>-->
      </v-form>
    </vsoc-drawer>
  </v-card>
</template>

<script>
import {
  addGroup,
  delGroup,
  selectUserGroupCount,
  updateGroup,
  userGroup,
} from '@/api/system/user-group'
import VsocDrawer from '@/components/VsocDrawer.vue'
import VsocPagination from '@/components/VsocPagination.vue'
import { PAGESIZE } from '@/util/constant'
import { deepClone } from '@/util/throttle'

// import { addGroup, delGroup, updateGroup } from '../../../api/system/user-group'

// import { getUserGroupMember } from '@/api/system/user-group-member'

export default {
  name: 'UserGroupIndex',
  components: {
    VsocDrawer,
    VsocPagination,
  },
  data() {
    return {
      showEditGroup: false,
      editType: 'add',

      // 查询条件列表
      // 查询条件下拉选择
      search: '',
      headers: [
        {
          text: 'ID',
          value: 'groupId',
          width: '10%',
        },
        {
          text: '用户组名称',
          value: 'groupName',
          width: '30%',
        },
        {
          text: '描述',
          value: 'description',
          width: '30%',
        },
        {
          text: '最后更新时间',
          value: 'updateDate',
          width: '20%',
        },
        {
          text: '更新者',
          value: 'updateUser',
          width: '20%',
        },
        {
          text: '',
          value: 'actions',
          width: '120px',
          sortable: false,
        },
      ],
      tableData: [],
      tableHeight: '34.5rem',
      tableLoading: false,
      btnLoading: false,
      query: {
        pageNum: 1,
        pageSize: PAGESIZE,
        groupName: '',

        // assetType: 'Vehicle'
      },
      filterList: [],
      tableDataTotal: 0,
      valid: false,

      // 编辑组信息
      editGroup: {
        groupId: '',
        groupName: '',
        description: '',
      },
      groupRules: {
        groupName: [v => !!v || '组名称是必填的'],
      },
    }
  },
  created() {
    this.$_setTableHeight()
    this.$bus.$on('resize', this.$_setTableHeight)
    this.getTableData()
  },
  beforeDestroy() {
    this.$bus.$off('resize', this.$_setTableHeight)
  },
  methods: {
    /*            async getTableData() {
                      try {
                          this.tableLoading = true
                          this.tableData = []
                          const res = await getUserGroupLike({
                              ...this.query,
                              pageNum: this.query.pageNum - 1
                          })
                          this.tableData = res.data.content
                          this.tableDataTotal = res.data.totalElements
                      } catch (e) {
                          console.error(`获取用户组管理：${e}`)
                      }
                      this.tableLoading = false
                  }, */

    async getTableData() {
      try {
        this.tableLoading = true
        this.tableData = []
        const res = await userGroup(this.query)

        // await getUserGroupLikeDt({
        //   ...this.query,
        //   pageNum: this.query.pageNum - 1,
        // })
        this.tableData = res.data.records
        this.tableDataTotal = res.data.total
      } catch (e) {
        console.error(`获取用户组管理：${e}`)
      }
      this.tableLoading = false
    },

    $_search() {
      this.query.pageNum = 1
      this.getTableData()
    },

    /*            $_setTableHeight() {
                      this.$nextTick(() => {
                          let num = this.filterList.length === 0 ? 15.5 : 18.24;
                          const fontSize = +getComputedStyle(window.document.documentElement)['font-size'].replace('px', '')
                          this.tableHeight = document.documentElement.clientHeight - num * fontSize
                      })
                  }, */

    $_setTableHeight() {
      this.$nextTick(() => {
        let num = 17.2
        const fontSize = +getComputedStyle(window.document.documentElement)[
          'font-size'
        ].replace('px', '')
        if (fontSize >= 16) {
          num -= fontSize - 14
        }
        this.tableHeight =
          document.documentElement.clientHeight - num * fontSize
      })
    },

    /*            change(e) {
                      let val = ['Vehicle', 'Consumer']
                      this.query.assetType = val[e]
                      this.$_search()
                  }, */

    add() {
      this.editType = 'add'
      this.showEditGroup = true
      this.editGroup = {
        // 资产类型
        // assetType: this.query.assetType,
        // state: 1,
        groupName: '',
        description: '',
      }
      this.$refs.form.resetValidation()
    },

    edit(item) {
      this.editType = 'edit'
      this.editGroup = deepClone(item)
      this.showEditGroup = true
    },

    async del(item) {
      this.$set(item, 'btnLoading', true)
      let userMembers = []
      const res = await selectUserGroupCount({ id: item.groupId })
      userMembers = res.data
      this.$set(item, 'btnLoading', false)

      // 如果组已经有使用，不能删除
      this.doDeleteGroup(item, userMembers)

      // if (userMembers > 0) {
      //   this.$notify.info('error', `无法删除已使用的用户组，已被${userMembers}个用户引用！`)
      // } else {
      //   this.doDeleteGroup(item)
      // }
    },

    // 确认编辑组
    async confirmEditGroup(callBack) {
      const bool = this.$refs.form.validate()
      if (!bool) return callBack(false, true)
      try {
        if (!this.editGroup.description) {
          this.editGroup.description = this.editGroup.groupName
        }

        // this.editgroup.isEncryption ? this.editgroup.isEncryption = this.editgroup.isEncryption.toString() : ''
        if (this.editType === 'add') {
          const res = await addGroup(this.editGroup)
          if (res.code === 200) {
            this.$notify.info('success', '新增成功！')
          }
          callBack()
        } else if (this.editType === 'edit') {
          // const res = await updateGroup(this.editGroup, this.editGroup.groupId)
          const res = await updateGroup(this.editGroup)
          if (res.code === 200) {
            this.$notify.info('success', '修改成功！')
          }
          callBack()
        }
        this.$_search()
      } catch (e) {
        console.error(`编辑用户组错误：${e}`)
        callBack(false, true)
      }
    },

    // 删除组
    doDeleteGroup(item, userMembers) {
      this.$swal({
        title: '删除用户组',
        text: `${item.groupName}组已关联${userMembers}个用户，是否确认删除用户组:${item.groupName}？`,
        icon: 'warning',
        reverseButtons: true,
        showCancelButton: true,
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        customClass: {
          confirmButton: 'sweet-btn-primary',
        },
      }).then(async result => {
        if (result.isConfirmed) {
          // this.$swal.fire('更新成功', '', 'success');
          try {
            const res = await delGroup({ id: item.groupId })
            if (res.code === 200) {
              this.$notify.info('success', '删除成功！')
            }
            this.$_search()
          } catch (e) {
            console.error(`删除用户组错误：${e}`)
          }
        }
      })
    },
  },
}
</script>
