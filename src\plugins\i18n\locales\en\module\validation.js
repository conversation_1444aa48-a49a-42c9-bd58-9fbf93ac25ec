const validation = {
  required: '{0} is required',
  defaultName: 'This field',
  wrongFormat: '{0} format error',
  email: 'The Email field must be a valid email',
  minLen: 'The length of this field cannot be less than {minLen}',
  matchPassword: 'The new password and the confirmation password do not match',
  maxLen: 'Maximum length is {0}',
  url: 'URL is invalid',
  intRange: 'Valid value range: positive integer within 1-100',
  underValidate: 'Can only be lowercase letters, numbers and underscores',
  intRange2: 'Valid value range: integer of {range}',
  select: '{0} is required',
  int: '{name} must be an integer',
  valueRange: 'Valid value range:{0}~{1}',
  maxValue: 'The maximum value is {0}',
  minValue: 'The minimum value is {0}',
  chineseValidate: '{0}Chinese input not supported',
  minSelect: 'Select at least one option',
}

export default validation
