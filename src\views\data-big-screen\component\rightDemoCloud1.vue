<template>
  <div class="c-box" style="padding-bottom: 8% !important">
    <div class="c-box-header" style="line-height: 0">
      {{ isMapChina ? '境内攻击排名TOP' : '境外攻击排名TOP' }}
    </div>
    <div class="box-chart d-flex flex-column h-100">
      <v-data-table
        fixed-header
        class="table-card table-card-1"
        dense
        style="background: transparent !important"
        :items-per-page="isMapChina ? list1.length : list.length"
        hide-default-footer
        :headers="isMapChina ? headers1 : headers"
        item-key="index"
        :items="isMapChina ? list1 : list"
      >
        <template v-slot:item.riskNum="{ item }">
          <div
            v-if="isMapChina"
            class="risk-num-sort"
            :style="{ backgroundColor: riskNumColor[item.riskNum] }"
          >
            {{ item.riskNum }}
          </div>
          <img
            v-else
            class="risk-num"
            :src="require(`../images/country/${item.riskNum}.png`)"
          />
        </template>
      </v-data-table>

      <vue-seamless-scroll
        :data="isMapChina ? list1 : list"
        :class-option="classOption"
        style="overflow: hidden"
      >
        <v-data-table
          fixed-header
          class="table-card table-card-2 flex-1"
          dense
          style="background: transparent !important"
          :items-per-page="isMapChina ? list1.length : list.length"
          hide-default-footer
          :headers="isMapChina ? headers1 : headers"
          item-key="index"
          :items="isMapChina ? list1 : list"
        >
          <template v-slot:item.riskNum="{ item }">
            <div
              v-if="isMapChina"
              class="risk-num-sort"
              :style="{ backgroundColor: riskNumColor[item.riskNum] }"
            >
              {{ item.riskNum }}
            </div>
            <img
              v-else
              class="risk-num"
              :src="require(`../images/country/${item.riskNum}.png`)"
            />
          </template>
        </v-data-table>
      </vue-seamless-scroll>
    </div>
  </div>
</template>

<script>
import vueSeamlessScroll from 'vue-seamless-scroll'
export default {
  name: 'rightDemoCloud1',
  components: {
    vueSeamlessScroll,
  },
  props: {
    list: {
      type: Array,
      default: () => {
        return [
          { riskNum: '1', name: '美国', value: '275(40%)' },
          { riskNum: '2', name: '澳大利亚', value: '264(30%)' },
          { riskNum: '3', name: '韩国', value: '251(20%)' },
          { riskNum: '4', name: '英国', value: '239(10%)' },
          { riskNum: '5', name: '日本', value: '227(5%)' },
          { riskNum: '1', name: '美国', value: '275(40%)' },
          { riskNum: '2', name: '澳大利亚', value: '264(30%)' },
          { riskNum: '3', name: '韩国', value: '251(20%)' },
          { riskNum: '4', name: '英国', value: '239(10%)' },
          { riskNum: '5', name: '日本', value: '227(5%)' },
        ]
      },
    },
    list1: {
      type: Array,
      default: () => {
        return [
          { riskNum: '1', name: '北京', value: '275(40%)' },
          { riskNum: '2', name: '上海', value: '264(30%)' },
          { riskNum: '3', name: '广州', value: '251(20%)' },
          { riskNum: '4', name: '大连', value: '239(10%)' },
          { riskNum: '5', name: '南宁', value: '227(5%)' },
          { riskNum: '1', name: '北京', value: '275(40%)' },
          { riskNum: '2', name: '上海', value: '264(30%)' },
          { riskNum: '3', name: '广州', value: '251(20%)' },
          { riskNum: '4', name: '大连', value: '239(10%)' },
          { riskNum: '5', name: '南宁', value: '227(5%)' },
        ]
      },
    },
    isMapChina: {
      type: Boolean,
      default: () => false,
    },
  },
  computed: {
    headers() {
      return [
        {
          text: '排名',
          value: 'riskNum',
        },
        {
          text: '国家名称',
          value: 'name',
        },
        {
          text: '攻击次数',
          value: 'value',
          align: 'center',
        },
      ]
    },
    headers1() {
      return [
        {
          text: '排名',
          value: 'riskNum',
        },
        {
          text: '省份',
          value: 'name',
        },
        {
          text: '攻击次数',
          value: 'value',
          align: 'center',
        },
      ]
    },
    classOption() {
      return {
        step: 0.3, // 数值越大速度滚动越快
        limitMoveNum: 10, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
      }
    },
  },
  data() {
    return {
      riskNumColor: [
        '#ca3d3e',
        '#df7746',
        '#e1a822',
        '#79b64c',
        '#00aeee',
        '#ffc000',
        '#ed9595',
      ],
    }
  },
  methods: {},
}
</script>
