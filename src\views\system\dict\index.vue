<template>
  <div>
    <bread-crumb></bread-crumb>
    <v-card tile class="main-content">
      <v-card-text class="pa-0">
        <div class="d-flex justify-space-between align-center flex-row-reverse">
          <div class="d-flex align-center">
            <table-search
              :searchList="searchList"
              :searchQuery="query"
              @search="$_search"
            ></table-search>
          </div>
          <div class="d-flex justify-end align-center">
            <v-btn
              v-has:dict-type-new
              elevation="0"
              color="primary"
              @click="$_add"
            >
              <span>
                {{ $t('action.add') }}
              </span>
            </v-btn>
          </div>
        </div>
        <v-data-table
          fixed-header
          :items-per-page="query.pageSize"
          item-key="code"
          :height="tableHeight"
          hide-default-footer
          :headers="headers"
          :items="tableData"
          class="table border-radius-xl mt-3 thead-light"
          :loading="tableLoading"
          @click:row="checkData"
        >
          <template v-slot:item.dictName="{ item }">
            <div v-show-tips style="width: 140px" class="text-overflow-hide">
              {{ item.dictName | dataFilter }}
            </div>
          </template>
          <template v-slot:item.status="{ item }">
            <v-badge
              dot
              inline
              offset-x="10"
              :offset-y="-18"
              :color="item.status === '0' ? activeColor : inactiveColor"
              class="mr-1"
            ></v-badge>
            <span>{{ item.statusName }}</span>
          </template>
          <template v-slot:item.remark="{ item }">
            <div v-show-tips style="width: 140px" class="text-overflow-hide">
              {{ item.remark | dataFilter }}
            </div>
          </template>
          <template v-slot:item.actions="{ item }">
            <v-btn v-has:dict-type-edit icon @click.stop="$_edit(item)">
              <vsoc-icon
                v-show-tips="$t('action.edit')"
                type="fill"
                icon="icon-bianji"
                class="action-btn"
                size="x-large"
              ></vsoc-icon>
            </v-btn>
            <v-btn
              v-has:dict-type-delete
              v-if="item.isDefault !== '0'"
              icon
              @click.stop="$_del(item)"
            >
              <vsoc-icon
                v-show-tips="$t('action.del')"
                type="fill"
                icon="icon-shanchu"
                class="action-btn"
                size="x-large"
              ></vsoc-icon>
            </v-btn>
          </template>
        </v-data-table>

        <!-- 分页器 -->
        <vsoc-pagination
          :page.sync="query.pageNum"
          :size.sync="query.pageSize"
          :total="tableDataTotal"
          @change-page="getTableData"
          @change-size="$_search"
        >
        </vsoc-pagination>
      </v-card-text>
    </v-card>
    <dict-edit ref="refDictEdit" :mode="drawMode" @save="$_search" />
  </div>
</template>
<script>
import { delDictType, getDictTypeList } from '@/api/system/dict'

import VsocPagination from '@/components/VsocPagination.vue'

import breadCrumb from '@/components/bread-crumb/index'
import TableSearch from '@/components/TableSearch/index.vue'
import { activeColor, inactiveColor } from '@/plugins/systemColor'
import { setRemainingHeight } from '@/util/utils'
import DictEdit from './dictEdit.vue'
export default {
  name: 'DictIndex',
  components: {
    VsocPagination,
    breadCrumb,
    TableSearch,
    DictEdit,
  },
  data() {
    return {
      activeColor,
      inactiveColor,
      // 分页参数
      query: {
        dictName: '', //字典类型名称
        dictEnName: '', //字典类型
        status: '', //状态（0正常 1停用）
        startDate: '', //创建开始时间
        endDate: '', //创建结束时间
        pageNum: 1, //当前页
        pageSize: 10, //每页多少条
      },
      tableLoading: true,
      tableDataTotal: 0,
      tableData: [],
      tableHeight: '34.5rem',
      drawMode: '',
    }
  },
  created() {
    this.$_setTableHeight()
    this.$bus.$on('resize', this.$_setTableHeight)
    this.$_search()
  },
  computed: {
    searchList() {
      return [
        {
          type: 'input',
          value: 'dictName',
          text: this.$t('dict.headers.dictName'),
        },
        {
          type: 'input',
          value: 'dictEnName',
          text: this.$t('dict.headers.dictEnName'),
        },
        {
          type: 'input',
          value: 'remark',
          text: this.$t('dict.headers.remark'),
        },
      ]
    },
    headers() {
      return [
        {
          text: this.$t('dict.headers.dictId'),
          value: 'dictId',
          width: '100px',
        },
        {
          text: this.$t('dict.headers.dictName'),
          value: 'dictName',
          width: '140px',
        },

        {
          text: this.$t('dict.headers.dictEnName'),
          value: 'dictEnName',
          width: '140px',
        },
        {
          text: this.$t('dict.headers.dictDataCount'),
          value: 'dictDataCount',
          width: '100px',
        },
        {
          text: this.$t('dict.headers.status'),
          value: 'status',
          width: '100px',
        },
        {
          text: this.$t('dict.headers.isDefault'),
          value: 'isDefaultName',
          width: '100px',
        },
        {
          text: this.$t('dict.headers.remark'),
          value: 'remark',
          width: '140px',
        },
        {
          text: this.$t('global.createDate'),
          value: 'createDate',
          width: '160px',
        },
        {
          text: '',
          value: 'actions',
          sortable: false,
          width: '120',
        },
      ]
    },
  },
  beforeDestroy() {
    this.$bus.$off('resize', this.$_setTableHeight)
  },
  methods: {
    checkData(item) {
      if (this.$store.state.permission.buttons.includes('dict-type-check')) {
        this.$router.push(`/dict/data?dictId=${item.dictId}`)
      }
    },
    //新增
    $_add() {
      this.drawMode = 'new'
      this.$refs['refDictEdit'].open()
    },
    //编辑
    $_edit(item) {
      this.drawMode = 'edit'
      this.$refs['refDictEdit'].open(item)
    },
    //删除
    async $_del(item) {
      if (item.dictDataCount > 0) {
        return this.$notify.info('warning', this.$t('dict.tip1'))
      }
      // const res = await delDictType({ dictId: item.dictId })
      // if (res.code === 200) {
      //   this.$notify.info(
      //     'success',
      //     this.$t('global.hint.del', [this.$t('dict.currentTitle')]),
      //   )
      //   this.$_search()
      // }
      this.$swal({
        title: this.$t('dict.del.title'),
        text: this.$t('dict.del.text', [item.dictName]),
        icon: 'warning',
        showCancelButton: true,
        reverseButtons: true,
        confirmButtonText: this.$t('action.confirm'),
        cancelButtonText: this.$t('action.cancel'),
        customClass: {
          confirmButton: 'sweet-btn-primary',
        },
      }).then(async result => {
        if (result.isConfirmed) {
          try {
            const res = await delDictType({
              dictId: item.dictId,
            })
            if (res.code === 200) {
              this.$notify.info(
                'success',
                this.$t('global.hint.del', [this.$t('dict.currentTitle')]),
              )
            }
            this.$_search()
          } catch (e) {
            console.error(`删除字典类型错误：${e}`)
          }
        }
      })
      // this.$swal({
      //   title: this.$t('alertAction.swal.del.title'),
      //   text: this.$t('alertAction.swal.del.text'),
      //   icon: 'warning',
      //   showCancelButton: true,
      //   reverseButtons: true,
      //   confirmButtonText: this.$t('action.confirm'),
      //   cancelButtonText: this.$t('action.cancel'),
      //   customClass: {
      //     confirmButton: 'sweet-btn-primary',
      //   },
      // }).then(async result => {
      //   if (result.isConfirmed) {
      //     try {
      //       const res = await deleteAction({ id: data.id })
      //       if (res.code === 200) {
      //         this.$notify.info(
      //           'success',
      //           this.$t('global.hint.del', [
      //             this.$t('alertAction.currentTitle'),
      //           ]),
      //         )
      //       }
      //       this.$_search()
      //     } catch (e) {
      //       console.error(`删除处置错误：${e}`)
      //     }
      //   }
      // })
    },
    $_setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = setRemainingHeight(
          this.$store.state.global.subMarginFn,
        )
      })
    },
    $_search() {
      this.query.pageNum = 1
      this.getTableData()
    },
    // 加载表格数据
    async getTableData() {
      this.tableLoading = true
      try {
        const res = await getDictTypeList(this.query)
        this.tableDataTotal = res.data.total
        this.tableData = res.data.records
      } catch (e) {
        console.error(`获取字典类型错误：${e}`)
      } finally {
        this.tableLoading = false
      }
    },
  },
}
</script>
