<!-- token管理 -->
<template>
  <div>
    <bread-crumb></bread-crumb>
    <v-card tile class="main-content">
      <v-card-text class="pa-0">
        <div class="d-flex justify-space-between align-center">
          <div class="d-flex justify-end align-center">
            <v-btn elevation="0" color="primary" @click="add">
              <span>
                {{ $t('token.btn1') }}
              </span>
            </v-btn>
          </div>
          <div class="d-flex align-center">
            <table-search
              :searchList="searchList"
              :searchQuery="query"
              @search="changeSize"
            ></table-search>
          </div>
        </div>
        <!-- loading-text="查询中... 请等待" -->
        <v-data-table
          fixed-header
          :items-per-page="query.pageSize"
          item-key="id"
          show-expand
          :height="tableHeight"
          hide-default-footer
          :headers="headers"
          :items="tableData"
          :loader-height="6"
          class="table border-radius-xl mt-3 thead-light"
          :loading="tableLoading"
        >
          <template v-slot:item.accessKeyType="{ item }">
            <div>{{ item.accessKeyType === 'External' ? '外部' : '内部' }}</div>
          </template>

          <template v-slot:item.accessName="{ item }">
            <div v-show-tips class="text-truncate" style="width: 10rem">
              {{ item.accessName }}
            </div>
          </template>

          <template v-slot:item.accessKey="{ item }">
            <div class="d-flex align-center">
              <div
                v-show-tips
                class="text-truncate"
                style="width: 10rem; display: inline-block"
              >
                {{ item.accessKey }}
              </div>
              <v-btn v-copy="item.accessKey" v-show-tips="'复制'" icon>
                <!-- <v-icon size="1.25rem"> mdi-content-copy </v-icon> -->
                <vsoc-icon
                  type="fill"
                  class="secondary--text"
                  icon="icon-fuzhi"
                ></vsoc-icon>
              </v-btn>
            </div>
          </template>

          <template v-slot:item.accessSecret="{ item }">
            <div class="d-flex align-center">
              <div
                v-show-tips
                class="text-truncate"
                style="width: 10rem; display: inline-block"
              >
                {{ item.accessSecret }}
              </div>
              <v-btn v-copy="item.accessSecret" v-show-tips="'复制'" icon>
                <!-- <v-icon size="1.25rem"> mdi-content-copy </v-icon> -->
                <vsoc-icon
                  type="fill"
                  class="secondary--text"
                  icon="icon-fuzhi"
                ></vsoc-icon>
              </v-btn>
            </div>
          </template>

          <template v-slot:item.isActive="{ item }">
            <v-icon class="ml-n1 mr-1" :color="userStatus[item.isActive].color"
              >mdi-circle-medium</v-icon
            >
            <span v-show-tips>{{
              formatDate(item.expirationDate)
                ? '无效'
                : item.isActive == 1
                ? '有效'
                : '无效'
            }}</span>
          </template>

          <template v-slot:item.roleName="{ item }">
            {{ item.accessKeyType === 'Internal' ? item.roleName : 'N/A' }}
          </template>

          <template v-slot:item.expirationDate="{ item }">
            <span v-show-tips v-if="item.expirationDate !== null">{{
              item.expirationDate
            }}</span>
            <v-tooltip bottom v-else>
              <template v-slot:activator="{ on, attrs }">
                <span v-bind="attrs" v-on="on">Never</span>
              </template>
              <span>Token valid until revoked</span>
            </v-tooltip>
          </template>

          <template v-slot:item.lastUsedDate="{ item }">
            <span style="width: 10rem; display: inline-block" v-show-tips>{{
              item.lastUsedDate ? item.lastUsedDate : 'N/A'
            }}</span>
          </template>
          <template v-slot:item.usedCount="{ item }">
            <span style="width: 10rem; display: inline-block" v-show-tips>{{
              item.accessKeyType !== 'External' ? item.usedCount : 'N/A'
            }}</span>
          </template>
          <template v-slot:item.actions="{ item }">
            <v-btn
              v-has:token-revoke
              icon
              @click="revoke(item)"
              v-if="
                item.accessKeyType === 'Internal' &&
                item.isActive != 0 &&
                !formatDate(item.expirationDate)
              "
            >
              <vsoc-icon
                v-show-tips="
                  $generateMenuTitle($route.meta.buttonInfo['token-revoke'])
                "
                icon="icon-chexiao"
                class="action-btn"
                size="x-large"
              >
              </vsoc-icon>
            </v-btn>
            <v-btn
              v-has:token-change
              icon
              @click="edit(item)"
              v-if="
                item.accessKeyType === 'External' &&
                item.isActive != 0 &&
                !formatDate(item.expirationDate)
              "
            >
              <vsoc-icon
                v-show-tips="
                  $generateMenuTitle($route.meta.buttonInfo['token-change'])
                "
                type="fill"
                class="action-btn"
                icon="icon-bianjimingcheng"
                size="x-large"
              ></vsoc-icon>
            </v-btn>
            <v-btn
              v-has:bind-api
              icon
              v-if="
                item.accessKeyType === 'Internal' &&
                item.role === '0' &&
                item.isActive != '0' &&
                !formatDate(item.expirationDate)
              "
              @click.stop="bindApi(item)"
            >
              <vsoc-icon
                v-show-tips="
                  $generateMenuTitle($route.meta.buttonInfo['bind-api'])
                "
                icon="icon-quanxianshezhi"
                class="action-btn"
                size="x-large"
                type="fill"
              >
              </vsoc-icon>
            </v-btn>
          </template>
          <template v-slot:expanded-item="{ headers, item }">
            <td :colspan="headers.length + 1" class="pa-8">
              <div class="d-flex">
                <div class="w-10">
                  <div class="pb-2 text--secondary">
                    {{ $t('token.headers.describe') }}
                  </div>
                  <div v-show-tips>
                    {{ item.description | dataFilter }}
                  </div>
                </div>
              </div>
            </td>
          </template>
        </v-data-table>

        <!-- 分页器 -->
        <vsoc-pagination
          :page.sync="query.pageNum"
          :size.sync="query.pageSize"
          :total="tableDataTotal"
          @change-page="changePage"
          @change-size="changeSize"
        >
        </vsoc-pagination>
      </v-card-text>

      <vsoc-drawer
        ref="drawer"
        v-model="drawer"
        temporary
        :title="ops === 'edit' ? $t('token.btn2') : $t('token.btn1')"
        @click:confirm="onConfirm"
        @click:cancel="onCancel"
        @click:close="onCancel"
      >
        <v-form ref="form" v-model="valid" dark lazy-validation>
          <div class="mt-4">
            <v-row class="px-4">
              <v-select
                :disabled="ops === 'edit'"
                :items="items"
                v-model="tokenEt.accessKeyType"
                item-text="lable"
                item-value="value"
                :label="$t('token.headers.accessKeyType')"
                :menu-props="{ offsetY: true }"
              ></v-select>
            </v-row>
            <v-row class="px-4" v-if="tokenEt.accessKeyType === 'Internal'">
              <v-select
                :items="tokenLevelEum"
                v-model="tokenEt.role"
                item-text="text"
                item-value="value"
                :label="$t('token.headers.tokenLevel')"
                :menu-props="{ offsetY: true }"
              ></v-select>
            </v-row>
            <v-row class="px-4">
              <v-text-field
                v-model="tokenEt.accessName"
                :rules="nameRules"
                :disabled="ops === 'edit'"
                :label="$t('token.headers.accessName')"
                :placeholder="$t('token.headers.accessName')"
                counter="100"
                required
                class="is-required"
              >
              </v-text-field>
            </v-row>
            <v-row
              class="px-4"
              v-if="ops !== 'edit' && tokenEt.accessKeyType === 'Internal'"
            >
              <v-menu
                v-model="menu1"
                :close-on-content-click="false"
                max-width="290"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    :value="tokenEt.expirationDate"
                    clearable
                    :label="$t('token.headers.expirationDate')"
                    readonly
                    hint="MM/DD/YYYY format"
                    v-bind="attrs"
                    v-on="on"
                    @click:clear="tokenEt.expirationDate = null"
                  ></v-text-field>
                </template>
                <v-date-picker
                  v-model="tokenEt.expirationDate"
                  :locale="locale"
                  @change="menu1 = false"
                  :min="minDate"
                ></v-date-picker>
              </v-menu>
            </v-row>
            <v-row class="px-4" v-if="tokenEt.accessKeyType === 'External'">
              <v-textarea
                v-model="tokenEt.accessKey"
                :rules="accessKeyRules"
                label="Access Key"
                placeholder="Access Key"
                counter="100"
                required
                class="is-required"
              >
              </v-textarea>
            </v-row>
            <v-row class="px-4" v-if="tokenEt.accessKeyType === 'External'">
              <v-textarea
                v-model="tokenEt.accessSecret"
                :rules="encryptedStringRules"
                counter="100"
                label="Access Secret"
                placeholder="Access Secret"
                required
                class="is-required"
              >
              </v-textarea>
            </v-row>
            <v-row class="px-4">
              <v-textarea
                :disabled="ops === 'edit'"
                v-model="tokenEt.description"
                counter="100"
                :label="$t('token.headers.describe')"
                :placeholder="$t('token.headers.describe')"
              >
              </v-textarea>
            </v-row>
          </div>
        </v-form>
      </vsoc-drawer>

      <grant-api ref="grantApi" />
    </v-card>
  </div>
</template>

<script>
import { checkChinese, max, required } from '@/@core/utils/validation'
import {
  addToken,
  findTokenList,
  revokeAccessToken,
  updateToken,
} from '@/api/system/token'
import TableSearch from '@/components/TableSearch/index.vue'
import VsocDrawer from '@/components/VsocDrawer.vue'
import VsocPagination from '@/components/VsocPagination.vue'
import breadCrumb from '@/components/bread-crumb/index'
import elTree from '@/components/search-tree/index'
import { PAGESIZE } from '@/util/constant'
import { dataFilter } from '@/util/filters'
import { setRemainingHeight } from '@/util/utils'
import lodash from 'lodash'
import moment from 'moment'

import GrantApi from './GrantApi.vue'

export default {
  name: 'TokenIndex',
  components: {
    VsocPagination,
    VsocDrawer,
    breadCrumb,
    elTree,
    TableSearch,
    GrantApi,
  },
  filters: {
    dataFilter,
  },
  data() {
    return {
      minDate: '',
      menu1: false,
      locale: '',

      // 授权范围列表
      items: [],

      // 分页参数
      query: {
        pageNum: 1,
        pageSize: PAGESIZE,
        accessName: '',
      },
      tableDataTotal: 10,
      tableData: [],
      tableHeight: '34.5rem',
      tableLoading: false,
      ops: 'new',
      drawer: false,
      tokenEt: this.initTokenData(),

      valid: true,
      nameRules: [
        v => required(v, this.$t('token.headers.accessName')),
        v => max(v, 100),
        v => checkChinese(v, this.$t('token.headers.accessName')),
      ],
      accessKeyRules: [v => max(v, 100), v => checkChinese(v, 'Access Key')],
      encryptedStringRules: [
        v => required(v, 'Access Secret'),
        v => max(v, 100),
        v => checkChinese(v, 'Access Secret'),
      ],
    }
  },
  computed: {
    tokenLevelEum() {
      return Object.assign([], this.$store.state.enums.enums['Token Level'])
    },
    // 用户状态枚举
    userStatus() {
      return this.$store.getters['enums/getUserStatus']
    },
    searchList() {
      return [
        {
          type: 'input',
          value: 'accessName',
          text: this.$t('token.headers.accessName'),
        },
      ]
    },

    headers() {
      let defaultHeaders = [
        {
          text: this.$t('token.headers.accessKeyType'),
          value: 'accessKeyType',
          widths: '80px',
        },
        {
          text: this.$t('token.headers.accessName'),
          value: 'accessName',
          width: '100px',
        },
        {
          text: 'Access Key',
          value: 'accessKey',
          widths: '100px',
        },
        {
          text: 'Access Secret',
          value: 'accessSecret',
          widths: '100px',
        },
        {
          text: this.$t('token.headers.tokenLevel'),
          value: 'roleName',
          width: '80px',
        },
        {
          text: this.$t('token.headers.isActive'),
          value: 'isActive',
          width: '80px',
        },
        {
          text: this.$t('token.headers.expirationDate'),
          value: 'expirationDate',
          width: '120px',
        },
        {
          text: this.$t('token.headers.lastUsedDate'),
          value: 'lastUsedDate',
          width: '120px',
        },
        {
          text: this.$t('token.headers.usedCount'),
          value: 'usedCount',
          width: '80px',
        },
        {
          text: this.$t('token.headers.createUser'),
          value: 'createUser',
          width: '80px',
        },
        {
          text: this.$t('token.headers.createDate'),
          value: 'createDate',
          width: '120px',
        },
        {
          text: this.$t('token.headers.updateUser'),
          value: 'updateUser',
          width: '80px',
        },
        {
          text: this.$t('token.headers.updateDate'),
          value: 'updateDate',
          width: '120px',
        },
        {
          text: '',
          value: 'actions',
          sortable: false,
          width: 50,
        },
      ]
      return defaultHeaders
    },
  },
  watch: {
    drawer(val) {
      if (!val) {
        this.navigationDrawerClose()
      }
    },
    '$i18n.locale': {
      handler(val) {
        // 判断中英文
        if (val === 'en') {
          this.locale = ''
          this.items = [
            { lable: 'Internal', value: 'Internal' },
            { lable: 'External', value: 'External' },
          ]
        } else {
          this.locale = 'zh-cn'
          this.items = [
            { lable: '内部', value: 'Internal' },
            { lable: '外部', value: 'External' },
          ]
        }
      },
      deep: true,
    },
  },
  created() {
    this.$_setTableHeight()
    this.$bus.$on('resize', this.$_setTableHeight)
    // 获取当前时间
    this.minDate = moment().add(1, 'days').format()
    // 判断中英文
    if (this.$i18n.locale === 'en') {
      this.locale = ''
      this.items = [
        { lable: 'Internal', value: 'Internal' },
        { lable: 'External', value: 'External' },
      ]
    } else {
      this.locale = 'zh-cn'
      this.items = [
        { lable: '内部', value: 'Internal' },
        { lable: '外部', value: 'External' },
      ]
    }
  },
  async mounted() {
    this.$_getTableData()
  },
  beforeDestroy() {
    this.$bus.$off('resize', this.$_setTableHeight)
  },
  methods: {
    bindApi(item) {
      this.$nextTick(() => {
        this.$refs.grantApi.open(item)
      })
    },
    formatDate(time) {
      return moment().valueOf('x') > moment(time).format('x')
    },
    // 设置表格高度
    $_setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = setRemainingHeight(
          this.$store.state.global.subMarginFn,
        )
      })
    },

    navigationDrawerClose() {
      this.drawer = false
      this.ops = 'new'
      this.tokenEt = this.initTokenData()
    },

    async $_getTableData() {
      // 保存查询参数
      this.tableLoading = true
      const res = await findTokenList(this.query)
      this.tableData = res.data.records
      this.tableDataTotal = res.data.total
      this.tableLoading = false
    },

    changePage(e) {
      this.$_getTableData()

      // this.searchUsers()
    },
    changeSize(e) {
      this.query.pageNum = 1
      this.$_getTableData()
    },

    add() {
      this.drawer = true
      // 重置校验和表单
      this.$refs.form.resetValidation()
      this.tokenEt = this.initTokenData()
      this.$forceUpdate()
    },

    edit(data) {
      this.treeSelect = []
      const record = {
        ...data,
      }

      this.tokenEt = { ...record }

      this.drawer = true

      this.ops = 'edit'
      this.$nextTick(() => {
        if (record.departmentList.length) {
          this.$refs['elTree1'].$refs.tree.setCheckedKeys([
            record.departmentList[0].id,
          ])
          this.$refs['elTree1'].filterText =
            record.departmentList[0].departmentName
        } else {
          this.$refs['elTree1'].$refs.tree.setCheckedKeys([])
          this.$refs['elTree1'].filterText = ''
        }
      })
    },
    async revoke(data) {
      this.$swal({
        title: this.$t('token.swal.revoke.title'),
        text: this.$t('token.swal.revoke.text'),
        icon: 'warning',
        reverseButtons: true,
        showCancelButton: true,
        confirmButtonText: this.$t('action.confirm'),
        cancelButtonText: this.$t('action.cancel'),
        customClass: {
          confirmButton: 'sweet-btn-primary',
        },
      }).then(async result => {
        if (result.isConfirmed) {
          let res = await revokeAccessToken({
            id: data.id,
          })
          if (res.code === 200) {
            this.$notify.info('success', this.$t('global.hint.operate'))
            this.query.pageNum = 1
            this.$_getTableData()
          }
        }
      })
    },

    // 初始化token表单信息
    initTokenData() {
      return {
        accessName: '', //token名称
        description: '', // 描述
        accessKey: '', // accessKey
        accessSecret: '', // accessSecret
        expirationDate: '', // 失效时间，若无失效时间，则表示Token永久生效
        accessKeyType: 'Internal', // 内部传Internal，外部传External
        role: '1',
      }
    },

    // 新增token信息
    async onAdd(form, fn) {
      try {
        await addToken(form)
        this.$notify.info(
          'success',
          this.$t('global.hint.add', [this.$t('token.currentTitle')]),
        )
        this.$refs.form.resetValidation()

        // 新增token信息成功，重新加载数据
        this.query.pageNum = 1
        this.$_getTableData()

        // 关闭抽屉
        fn()
      } catch (err) {
        this.$notify.info('error', err)
        fn(false, true)
      }
    },

    // 修改token信息
    async onEdit(form, fn) {
      try {
        const res = await updateToken(form)
        this.$notify.info(
          'success',
          this.$t('global.hint.edit', [this.$t('token.currentTitle')]),
        )
        this.$refs.form.resetValidation()

        // 修改token信息成功，重新加载数据
        this.query.pageNum = 1
        this.$_getTableData()

        // 关闭抽屉
        fn()
      } catch (err) {
        this.$notify.info('error', res.msg)
        fn(false, true)
      }
    },

    // 提交token信息
    onConfirm(fn) {
      const bool = this.$refs.form.validate()
      // 深拷贝表单对象
      const form = lodash.cloneDeep(this.tokenEt)
      // 如果存在时间对象则补充时分秒参数，为空则删除该属性不然后台会报错
      if (form.expirationDate) {
        form.expirationDate += ' 00:00:00'
      } else {
        delete form.expirationDate
      }
      if (bool) {
        // 校验通过
        if (this.ops === 'edit') {
          this.onEdit(form, fn)
        } else {
          this.onAdd(form, fn)
        }
      } else {
        fn(false, true)

        // 校验不通过
        // this.$refs.drawer.confirmLoading = true
        // this.$notify.info('error', '存在【必填项】未填')
      }
    },

    // 取消提交token信息
    onCancel() {
      this.$refs.form.resetValidation()
      this.$refs.form.reset()
    },
  },
}
</script>
<style lang="scss" scoped>
.vsoc-drawer__content .row .v-text-field.v-input--dense {
  margin-bottom: 8px !important;
  // font-size: 1rem !important;
}
::v-deep .header {
  align-items: center;
  .v-chip.v-size--default {
    height: auto;
  }
  .v-text-field__details {
    display: none;
  }
  .v-input--dense > .v-input__control > .v-input__slot {
    margin: 0;
  }
}

::v-deep.v-list-item__title {
  font-size: 1.1667rem;
  line-height: 1.8333rem;
  color: var(--v-color-base) !important;
}
.v-list--dense .v-list-item .v-list-item__content {
  padding: 8px 16px !important;
}
</style>
