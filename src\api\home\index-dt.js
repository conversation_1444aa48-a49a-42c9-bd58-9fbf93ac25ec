// 获取首页基础信息
export function getHomeBasicInfoDt() {
  return {
    vehicleNumber: '131',
    vehicleNumberLastWeek: '19',
    vehicleNumberLastWeekPercent: '+85.38%',
    activeNumber: '5',
    activeNumberLastWeek: '2',
    activeNumberLastWeekPercent: '+60.00%',
    abnormalNumber: '83',
    abnormalNumberLastWeek: '5',
    abnormalNumberLastWeekPercent: '+93.98%',
    addedTodayNumber: '0',
    addedTodayNumberLastWeek: '2',
    addedTodayNumberLastWeekPercent: '+0%',
  }
}

// 整个系统的服务态势
export function getServiceSituationDt(params) {
  return {
    systemServiceSituation: 'normal',
    updateDateTime: 1665965930634,
    goodNumber: '73',
    goodPercent: '56%',
    fineNumber: '2',
    finePercent: '2%',
    normalNumber: '2',
    normalPercent: '2%',
    poorNumber: '53',
    poorPercent: '41%',
  }
}

// 查询统计线性图数据
export function getStatisticsDt(params) {
  let data = []
  if (params.searchCode == 'vehicle_info') {
    data = [
      {
        dataType: 'daily_assets_total',
        nodes: [
          { xvalue: '2022-10-12', yvalue: '19' },
          {
            xvalue: '2022-10-13',
            yvalue: '22',
          },
          { xvalue: '2022-10-14', yvalue: '51' },
          {
            xvalue: '2022-10-15',
            yvalue: '51',
          },
          { xvalue: '2022-10-16', yvalue: '51' },
          {
            xvalue: '2022-10-17',
            yvalue: '59',
          },
          { xvalue: '2022-10-18', yvalue: '130' },
        ],
      },
      {
        dataType: 'daily_active_assets_total',
        nodes: [
          { xvalue: '2022-10-12', yvalue: '2' },
          {
            xvalue: '2022-10-13',
            yvalue: '3',
          },
          { xvalue: '2022-10-14', yvalue: '29' },
          {
            xvalue: '2022-10-15',
            yvalue: '0',
          },
          { xvalue: '2022-10-16', yvalue: '0' },
          {
            xvalue: '2022-10-17',
            yvalue: '8',
          },
          { xvalue: '2022-10-18', yvalue: '70' },
        ],
      },
      {
        dataType: 'daily_abnormal_assets_total',
        nodes: [
          { xvalue: '2022-10-12', yvalue: '5' },
          {
            xvalue: '2022-10-13',
            yvalue: '8',
          },
          { xvalue: '2022-10-14', yvalue: '38' },
          {
            xvalue: '2022-10-15',
            yvalue: '38',
          },
          { xvalue: '2022-10-16', yvalue: '38' },
          {
            xvalue: '2022-10-17',
            yvalue: '46',
          },
          { xvalue: '2022-10-18', yvalue: '83' },
        ],
      },
      {
        dataType: 'daily_new_assets_total',
        nodes: [
          { xvalue: '2022-10-12', yvalue: '2' },
          {
            xvalue: '2022-10-13',
            yvalue: '3',
          },
          { xvalue: '2022-10-14', yvalue: '29' },
          {
            xvalue: '2022-10-15',
            yvalue: '0',
          },
          { xvalue: '2022-10-16', yvalue: '0' },
          {
            xvalue: '2022-10-17',
            yvalue: '8',
          },
          { xvalue: '2022-10-18', yvalue: '70' },
        ],
      },
    ]
  } else if (params.searchCode == 'alarm_statistics') {
    data = [
      {
        dataType: 'daily_new_waithandle_alarm_total',
        nodes: [
          { xvalue: '2022-10-12', yvalue: '35' },
          {
            xvalue: '2022-10-13',
            yvalue: '85',
          },
          { xvalue: '2022-10-14', yvalue: '286' },
          {
            xvalue: '2022-10-15',
            yvalue: '187',
          },
          { xvalue: '2022-10-16', yvalue: '197' },
          {
            xvalue: '2022-10-17',
            yvalue: '210',
          },
          { xvalue: '2022-10-18', yvalue: '259' },
        ],
      },
      {
        dataType: 'daily_new_waithandle_disaster_alarm_total',
        nodes: [
          { xvalue: '2022-10-12', yvalue: '11' },
          {
            xvalue: '2022-10-13',
            yvalue: '24',
          },
          { xvalue: '2022-10-14', yvalue: '66' },
          {
            xvalue: '2022-10-15',
            yvalue: '63',
          },
          { xvalue: '2022-10-16', yvalue: '74' },
          {
            xvalue: '2022-10-17',
            yvalue: '73',
          },
          { xvalue: '2022-10-18', yvalue: '102' },
        ],
      },
      {
        dataType: 'daily_new_waithandle_major_alarm_total',
        nodes: [
          { xvalue: '2022-10-12', yvalue: '0' },
          {
            xvalue: '2022-10-13',
            yvalue: '0',
          },
          { xvalue: '2022-10-14', yvalue: '0' },
          {
            xvalue: '2022-10-15',
            yvalue: '0',
          },
          { xvalue: '2022-10-16', yvalue: '0' },
          {
            xvalue: '2022-10-17',
            yvalue: '0',
          },
          { xvalue: '2022-10-18', yvalue: '0' },
        ],
      },
      {
        dataType: 'daily_new_waithandle_minor_alarm_total',
        nodes: [
          { xvalue: '2022-10-12', yvalue: '24' },
          {
            xvalue: '2022-10-13',
            yvalue: '1',
          },
          { xvalue: '2022-10-14', yvalue: '4' },
          {
            xvalue: '2022-10-15',
            yvalue: '4',
          },
          { xvalue: '2022-10-16', yvalue: '3' },
          {
            xvalue: '2022-10-17',
            yvalue: '7',
          },
          { xvalue: '2022-10-18', yvalue: '9' },
        ],
      },
      {
        dataType: 'daily_new_waithandle_warning_alarm_total',
        nodes: [
          { xvalue: '2022-10-12', yvalue: '0' },
          {
            xvalue: '2022-10-13',
            yvalue: '0',
          },
          { xvalue: '2022-10-14', yvalue: '0' },
          {
            xvalue: '2022-10-15',
            yvalue: '0',
          },
          { xvalue: '2022-10-16', yvalue: '0' },
          {
            xvalue: '2022-10-17',
            yvalue: '0',
          },
          { xvalue: '2022-10-18', yvalue: '0' },
        ],
      },
      {
        dataType: 'daily_new_waithandle_info_alarm_total',
        nodes: [
          { xvalue: '2022-10-12', yvalue: '0' },
          {
            xvalue: '2022-10-13',
            yvalue: '0',
          },
          { xvalue: '2022-10-14', yvalue: '0' },
          {
            xvalue: '2022-10-15',
            yvalue: '0',
          },
          { xvalue: '2022-10-16', yvalue: '0' },
          {
            xvalue: '2022-10-17',
            yvalue: '0',
          },
          { xvalue: '2022-10-18', yvalue: '0' },
        ],
      },
    ]
  }

  return data
}

// 危险资产位置
export function getRiskAssetsLocationDt(params) {
  return [
    {
      eventCode: null,
      dataType: 'be_attacked',
      number: '8',
      percent: null,
      city: '广州市',
      locationDetail: null,
      lat: null,
      lng: null,
    },
    {
      eventCode: null,
      dataType: 'be_attacked',
      number: '4',
      percent: null,
      city: '惠州市',
      locationDetail: null,
      lat: null,
      lng: null,
    },
    {
      eventCode: null,
      dataType: 'be_attacked',
      number: '12',
      percent: null,
      city: '武汉市',
      locationDetail: null,
      lat: null,
      lng: null,
    },
    {
      eventCode: null,
      dataType: 'be_attacked',
      number: '4',
      percent: null,
      city: '东莞市',
      locationDetail: null,
      lat: null,
      lng: null,
    },
    {
      eventCode: null,
      dataType: 'be_attacked',
      number: '12',
      percent: null,
      city: '北京市',
      locationDetail: null,
      lat: null,
      lng: null,
    },
    {
      eventCode: null,
      dataType: 'be_attacked',
      number: '4',
      percent: null,
      city: '郑州市',
      locationDetail: null,
      lat: null,
      lng: null,
    },
  ]
}

// 车辆危险事件类型top6
export function getRiskEventDt(params) {
  return [
    {
      eventCode: null,
      dataType: '电池因高温而面临风险',
      number: '814',
      percent: '31.95%',
      city: null,
      locationDetail: null,
      lat: null,
      lng: null,
    },
    {
      eventCode: null,
      dataType: '车辆远程启动异常',
      number: '560',
      percent: '21.98%',
      city: null,
      locationDetail: null,
      lat: null,
      lng: null,
    },
    {
      eventCode: null,
      dataType: '车辆超速告警250km/h',
      number: '508',
      percent: '19.94%',
      city: null,
      locationDetail: null,
      lat: null,
      lng: null,
    },
    {
      eventCode: null,
      dataType: '未知的服务器指令',
      number: '385',
      percent: '15.11%',
      city: null,
      locationDetail: null,
      lat: null,
      lng: null,
    },
    {
      eventCode: null,
      dataType: '里程数欺诈',
      number: '168',
      percent: '6.59%',
      city: null,
      locationDetail: null,
      lat: null,
      lng: null,
    },
    {
      eventCode: null,
      dataType: '发动机过热',
      number: '113',
      percent: '4.43%',
      city: null,
      locationDetail: null,
      lat: null,
      lng: null,
    },
  ]
}

// 待处理告警事件
export function getWaitHandleAlarmDt(params) {
  return {
    disasterNumber: 13,
    newDisasterNumber: 62,
    majorNumber: 0,
    newMajorNumber: 0,
    minorNumber: 54,
    newMinorNumber: 2,
    warningNumber: 0,
    newWarningNumber: 0,
    infoNumber: 0,
    newInfoNumber: 0,
  }
}

// 最近告警事件
export function getRecentAlertEventDt(params) {
  return {
    content: [
      {
        createDate: 1665965320703,
        updateDate: 1665965320703,
        createUser: null,
        updateUser: null,
        id: 397299,
        name: '未知的服务器指令',
        alarmLevel: 'Critical',
        alarmDescription: '未知的服务器指令，指令：SQL',
        alarmDate: 1665965320000,
        alarmLocation: '',
        alarmLocationCity: '',
        alarmLocationLng: 113.928359,
        alarmLocationLat: 23.131618,
        status: 'waitHandle',
        tags: ',1553998378264924161,',
        vehicleId: 'acTaaesCsZTRcsaWx',
        vehicleAssetVin: 'N/A',
        vehicleAssetType: null,
        vehicleAssetYear: 2020,
        vehicleAssetEngine: null,
        detectorId: '1555101527948099586',
        detectorName: '未知的服务器指令',
      },
      {
        createDate: 1665965782902,
        updateDate: 1665965782903,
        createUser: null,
        updateUser: null,
        id: 397298,
        name: '远程启动异常',
        alarmLevel: 'Medium',
        alarmDescription: '车辆远程启动异常，当前发动机状态为：启动，消息指令：remote_start_event',
        alarmDate: 1665965782000,
        alarmLocation: '',
        alarmLocationCity: '',
        alarmLocationLng: 113.888888,
        alarmLocationLat: 23.164748,
        status: 'waitHandle',
        tags: null,
        vehicleId: 'acTaaesCsZTRcsaWy',
        vehicleAssetVin: 'N/A',
        vehicleAssetType: null,
        vehicleAssetYear: 2020,
        vehicleAssetEngine: null,
        detectorId: '1555364832725925890',
        detectorName: '远程启动异常',
      },
      {
        createDate: 1665965350808,
        updateDate: 1665965350808,
        createUser: null,
        updateUser: null,
        id: 397297,
        name: '远程启动异常',
        alarmLevel: 'Medium',
        alarmDescription: '车辆远程启动异常，当前发动机状态为：启动，消息指令：remote_start_event',
        alarmDate: 1665965350000,
        alarmLocation: '',
        alarmLocationCity: '',
        alarmLocationLng: 113.019897,
        alarmLocationLat: 23.187909,
        status: 'waitHandle',
        tags: null,
        vehicleId: 'acTaaesCsZTRcsaW1',
        vehicleAssetVin: 'N/A',
        vehicleAssetType: null,
        vehicleAssetYear: 2020,
        vehicleAssetEngine: null,
        detectorId: '1555364832725925890',
        detectorName: '远程启动异常',
      },
      {
        createDate: 1665965711293,
        updateDate: 1665965711293,
        createUser: null,
        updateUser: null,
        id: 397296,
        name: '未知的服务器指令',
        alarmLevel: 'Critical',
        alarmDescription: '未知的服务器指令，指令：SQL',
        alarmDate: 1665965710000,
        alarmLocation: '',
        alarmLocationCity: '',
        alarmLocationLng: 113.884001,
        alarmLocationLat: 23.183599,
        status: 'waitHandle',
        tags: ',1553998378264924161,',
        vehicleId: 'acTaaesCsZTRcsaWx',
        vehicleAssetVin: 'N/A',
        vehicleAssetType: null,
        vehicleAssetYear: 2020,
        vehicleAssetEngine: null,
        detectorId: '1555101527948099586',
        detectorName: '未知的服务器指令',
      },
      {
        createDate: 1665965164694,
        updateDate: 1665965164694,
        createUser: null,
        updateUser: null,
        id: 397295,
        name: '远程启动异常',
        alarmLevel: 'Medium',
        alarmDescription: '车辆远程启动异常，当前发动机状态为：启动，消息指令：remote_start_event',
        alarmDate: 1665965163000,
        alarmLocation: '',
        alarmLocationCity: '',
        alarmLocationLng: 113.063626,
        alarmLocationLat: 23.136821,
        status: 'waitHandle',
        tags: null,
        vehicleId: 'acTaaesCsZTRcsaWy',
        vehicleAssetVin: 'N/A',
        vehicleAssetType: null,
        vehicleAssetYear: 2020,
        vehicleAssetEngine: null,
        detectorId: '1555364832725925890',
        detectorName: '远程启动异常',
      },
      {
        createDate: 1665966098665,
        updateDate: 1665966098665,
        createUser: null,
        updateUser: null,
        id: 397293,
        name: '未知的服务器指令',
        alarmLevel: 'Critical',
        alarmDescription: '未知的服务器指令，指令：SQL',
        alarmDate: 1665966097000,
        alarmLocation: '',
        alarmLocationCity: '',
        alarmLocationLng: 113.069483,
        alarmLocationLat: 23.105534,
        status: 'waitHandle',
        tags: ',1553998378264924161,',
        vehicleId: 'acTaaesCsZTRcsaWx',
        vehicleAssetVin: 'N/A',
        vehicleAssetType: null,
        vehicleAssetYear: 2020,
        vehicleAssetEngine: null,
        detectorId: '1555101527948099586',
        detectorName: '未知的服务器指令',
      },
    ],
    pageable: {
      sort: { empty: false, sorted: true, unsorted: false },
      offset: 0,
      pageNumber: 0,
      pageSize: 10,
      paged: true,
      unpaged: false,
    },
    last: false,
    totalPages: 133,
    totalElements: 1322,
    size: 10,
    number: 0,
    sort: { empty: false, sorted: true, unsorted: false },
    first: true,
    numberOfElements: 10,
    empty: false,
  }
}
