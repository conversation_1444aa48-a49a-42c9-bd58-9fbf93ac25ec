<template>
  <v-card class="h-full">
    <v-card-title class="float-left">{{
      $t('analytics.alertTrend')
    }}</v-card-title>
    <v-card-text v-empty-chart="isEmpty" class="h-full">
      <vsoc-chart
        v-if="!isEmpty"
        echart-id="flow-statistics"
        :option="radarOption"
      ></vsoc-chart>
    </v-card-text>
    <!-- <v-card-title class="float-left">{{ title }}</v-card-title>
    <v-card-text v-empty-chart="isEmpty" class="h-full pt-6">
      <vsoc-chart
        v-if="!isEmpty"
        :echartId="echartId"
        :option="radarOption"
      ></vsoc-chart>
    </v-card-text> -->
  </v-card>
</template>

<script>
import VsocChart from '@/components/VsocChart.vue'
import { addDays, differenceInDays } from 'date-fns'
import { toDate } from '@/util/filters'
import { tooltip } from '@/assets/echarts-theme/constant'
const baseSeries = {
  name: 'Email',
  type: 'bar',
  stack: 'total-profit',
  barWidth: 13,
  barMinHeight: 15,
  chip: false,
  emphasis: {
    focus: 'series',
  },
  itemStyle: {
    borderRadius: 10,
    borderWidth: 5, // 堆叠间隔
    borderColor: 'transparent',
    borderJoin: 'round',
    borderType: 'solid',
  },
  data: [120, 132, 101, 134, 90, 230, 210],
}
export default {
  name: 'RadarChart1',
  props: {
    color: {
      type: Array,
      default: () => {
        return []
      },
    },
    title: {
      type: String,
    },
    echartId: {
      type: String,
      default: 'radar',
    },
    list: {
      type: Array,
      default: () => {
        return []
      },
    },
    startDate: {
      type: String,
      default: '',
    },
    endDate: {
      type: String,
      default: '',
    },
  },
  components: {
    VsocChart,
  },
  computed: {
    isEmpty() {
      return (
        !this.list ||
        this.list.length === 0 ||
        this.list.filter(v => v.arrays.length !== 0).length === 0
      )
    },
    radarOption() {
      let yList = []
      let xList = []
      const colorList = this.color
      let diffDay = differenceInDays(
        new Date(this.endDate),
        new Date(this.startDate),
      )
      let xAxisData = []
      for (let i = 0; i <= diffDay; i++) {
        const cur = toDate(addDays(new Date(this.startDate), i), 'yyyy-MM-dd')
        xAxisData.push(cur)
      }
      this.list.forEach((item, index) => {
        let obj = {
          name: item.alarmLevelName,
          color: colorList[item.level],
          data: item.arrays.map(s => {
            return [xAxisData.findIndex(v => v === s.time), s.number]
          }),
        }
        yList.push(obj)
      })
      xList = xAxisData.map(date => toDate(date, 'MM/dd'))
      return {
        tooltip,
        // 图例设置
        legend: {
          show: false,
          right: 0,
          icon: 'circle',
          itemHeight: 6, // 修改icon图形大小
          itemGap: 24,
          textStyle: {
            fontSize: 12,
            padding: [0, 0, 0, -8], // 修改文字和图标距离
          },
        },
        grid: {
          left: 10,
          right: 5,
          bottom: 10,
          top: 80,
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: xList,
            // 隐藏坐标轴刻度
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
          },
        ],
        yAxis: [
          {
            type: 'value',
            itemStyle: {
              borderRadius: 50,
            },
          },
        ],
        markArea: {
          itemStyle: {
            borderCap: 'round',
          },
        },
        series: this.list.map(item => {
          return {
            ...baseSeries,
            color: colorList[item.level],
            name: this.$store.state.enums.enums.AlarmLevel[item.level]?.text,
            data: item.arrays.map(s => {
              return [xAxisData.findIndex(v => v === s.time), s.number]
            }),
          }
        }),
      }
    },
  },
  created() {},
}
</script>
<style scoped lang="scss"></style>
