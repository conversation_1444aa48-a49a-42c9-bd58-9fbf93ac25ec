const software = {
  currentTitle: 'Software Asset',
  headers: {
    id: 'ID',
    type: 'Classification',
    customName: 'Custom Name',
    productName: 'Product Name',
    parentProductName: 'Parent Name',
    version: 'Version',
    supplier: 'Supplier Name',
    sourceLink: 'Channel Link',
    cpe: 'CPE',
    recommendedVersion: 'Recommended Version',
    hashType: 'Hash Type',
    hashValue: 'Hash Value',
    partName: 'Parts',
    supplierName: 'Supplier',
    vehiclePlatformName: 'Platform',
    status: 'status',
    notes: 'Remark',
  },
  card: {
    platformCount: 'Platforms Count',
    platCount: 'Part Count',
    supplierCount: 'Supplier Count',
    componentCount: 'Software Count',
  },
  swal: {
    del: {
      title: 'Delete Software Asset',
      text: 'Whether to confirm the deletion of software assets:{0}?',
    },
    batchDel: {
      title: 'Bulk Delete Software Asset',
      text: 'Are you sure to delete a total of {0} software assets?',
    },
    export: 'Are you sure to export a total of {0} software assets?',
  },
  hint: {
    del: 'Please select the software assets to be deleted first!',
    batchDel: 'Successfully deleted software assets in batches!',
    allDel: 'All software assets deleted successfully!',
    change: 'Please select the software asset to be changed first!',
    batchChange: 'Successfully changed software assets in batches!',
  },
  allText: 'Software Asset Data',
  title: {
    add: 'New Software Asset',
    edit: 'Edit Software Asset',
    detail: 'Software Asset Details',
  },
  template: {
    hint: 'Download the software asset template',
    text: 'Software Asset Import Template',
  },
  fileHint:
    'The size of a single attachment should not exceed 20M; attachment format requirements: xls,xlsx',
}

export default software
