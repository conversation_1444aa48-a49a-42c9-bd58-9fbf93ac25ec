import { getLocalStorage, setLocalStorage } from '@/util/localStorage'
import themeConfig from '@themeConfig'
export default {
  namespaced: true,
  state: {
    app: {
      contentLayoutNav: themeConfig.app.contentLayoutNav,
      routeTransition: themeConfig.app.routeTransition,
      skinVariant: getLocalStorage('skin-variant')
        ? getLocalStorage('skin-variant')
        : themeConfig.app.skinVariant,
      contentWidth: themeConfig.app.contentWidth,
      map: getLocalStorage('APP_MAP') || themeConfig.app.map,
    },
    menu: {
      isMenuHidden: themeConfig.menu.isMenuHidden,
      isVerticalNavMini: themeConfig.menu.isVerticalNavMini,
    },
    appBar: {
      type: themeConfig.appBar.type,
      isBlurred: themeConfig.appBar.isBlurred,
    },
    footer: {
      type: themeConfig.footer.type,
    },
    themes: themeConfig.themes,
    isThemeAndTimeZone: themeConfig.app.isThemeAndTimeZone,
    globalThemeMode: getLocalStorage('GLOBAL_THEME_MODE') || 'auto',
    appPrimary: getLocalStorage('UPDATE_APP_PRIMARY') || 'default',
    themeFlag: 'false',
  },
  mutations: {
    UPDATE_APP_PRIMARY(state, value) {
      setLocalStorage('UPDATE_APP_PRIMARY', value)
      // localStorage.setItem('UPDATE_APP_PRIMARY', value)
      state.appPrimary = value
    },
    UPDATE_GLOBAL_THEME_MODE(state, themeMode) {
      setLocalStorage('GLOBAL_THEME_MODE', themeMode)
      // localStorage.setItem('GLOBAL_THEME_MODE', themeMode)
      state.globalThemeMode = themeMode
    },
    UPDATE_ISTHEMEANDTIMEZONE(state, value) {
      state.isThemeAndTimeZone = value
    },
    UPDATE_APP_MAP(state, value) {
      state.app.map = value
    },
    UPDATE_APP_ROUTE_TRANSITION(state, value) {
      state.app.routeTransition = value
    },
    UPDATE_CONTENT_LAYOUT_NAV(state, value) {
      state.app.contentLayoutNav = value
    },
    UPDATE_APP_SKIN_VARIANT(state, value) {
      state.app.skinVariant = value
    },
    UPDATE_APP_CONTENT_WIDTH(state, value) {
      state.app.contentWidth = value
    },
    TOGGLE_MENU_MENU_HIDDEN(state, value) {
      state.menu.isMenuHidden = value
    },
    TOGGLE_MENU_VERTICAL_NAV_MINI(state, value) {
      state.menu.isVerticalNavMini = value
    },
    UPDATE_APP_BAR_TYPE(state, value) {
      state.appBar.type = value
    },
    UPDATE_APP_BAR_IS_BLURRED(state, value) {
      state.appBar.isBlurred = value
    },
    UPDATE_FOOTER_TYPE(state, value) {
      state.footer.type = value
    },
    UPDATE_THEMES(state, value) {
      state.themes = value
    },
    UPDATE_THEMES_FLAG(state, value) {
      state.themeFlag = value
    },
    // setThemeFlag(state, value) {
    //   state.themeFlag = value
    // },
  },
  actions: {
    // async getThemeFlag({ commit }) {
    //   try {
    //     const e = await Promise.all([
    //       getPropertiesList({
    //         propertyKey: 'Theme_Configuration',
    //       }),
    //       getPropertiesList({
    //         propertyKey: 'Theme_Color',
    //       }),
    //     ])
    //     let data = e[0].data.records.length
    //       ? e[0].data.records[0].propertyValue
    //       : 'false'
    //     commit('setThemeFlag', data)
    //     if (e[1].data.records.length) {
    //       let themeColor = JSON.parse(e[1].data.records[0].propertyValue)
    //       if (themeColor) {
    //         if (!localStorage.getItem('GLOBAL_THEME_MODE')) {
    //           commit('UPDATE_GLOBAL_THEME_MODE', themeColor.Theming)
    //         }
    //         if (!localStorage.getItem('UPDATE_APP_PRIMARY')) {
    //           commit('UPDATE_APP_PRIMARY', themeColor.PrimaryColor)
    //         }
    //       }
    //     }
    //   } catch (err) {
    //     console.log('获取主题错误', err)
    //   }
    // },
  },
}
