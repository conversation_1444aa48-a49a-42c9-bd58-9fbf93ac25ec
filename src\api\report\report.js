import { request, vsocPath } from '../../util/request'

// 获取报告配置列表
export const getReportConfig = function (data) {
  return request({
    url: `${vsocPath}/reportConfig/reportConfig`,
    method: 'post',
    data,
  })
}

// 新增报告配置
export const addReportConfig = function (data) {
  return request({
    url: `${vsocPath}/reportConfig/addReportConfig`,
    method: 'post',
    data,
  })
}

// 编辑报告配置
export const editReportConfig = function (data) {
  return request({
    url: `${vsocPath}/reportConfig/updateReportConfig`,
    method: 'post',
    data,
  })
}

// 删除报告配置
export const deleteReportConfig = function (id) {
  return request({
    url: `${vsocPath}/reportConfig/delReportConfig?id=${id}`,
    method: 'post',
  })
}

// 获取数据报告列表
export const getReport = function (data) {
  return request({
    url: `${vsocPath}/report/report`,
    method: 'post',
    data,
  })
}

//运营报告下载
// export const reportDownload = function (data) {
//   return request({
//     url: `${vsocPath}/report/download`,
//     method: 'post',
//     data,
//     loading: true,
//   })
// }

// 获取数据报告列表
export const getDownload = function (data) {
  return {
    //车辆态势分析
    situationAnalysis: {
      title: '车辆态势分析',
      data: [
        {
          healthStatusName: 'B(较好)',
          sum: 6,
          alarmPercentage: '16.7%',
        },
        {
          healthStatusName: 'D(较差)',
          sum: 1,
          alarmPercentage: '1%',
        },
        {
          healthStatusName: 'D(很差)',
          sum: 1,
          alarmPercentage: '1%',
        },
      ],
      allCount: 8,
    },
    //影响车型分析
    influenceModel: {
      title: '影响车型分析',
      data: [
        {
          count: 2,
          vehicle_model: '宝来',
          format: '5.26%',
        },
      ],
      allCount: 2,
    },
    //安全事件类型分析
    securityEventTypeAnalysis: {
      title: '安全事件类型分析',
      data: [
        {
          name: '未知的服务器指令',
          sum: 14,
          alarmPercentage: '36.84%',
        },
      ],
      allCount: 14,
    },
    //危险车辆所在城市分析
    analysisOfTheCityWhereDangerousVehiclesAreLocated: {
      title: '危险车辆所在城市分析',
      data: [
        {
          locationCity: '湖南',
          sum: 14,
          alarmPercentage: '36.84%',
        },
      ],
      allCount: 14,
    },
    //危险资产明细单
    securityAssetList: {
      title: '危险资产明细单',
      data: [
        {
          id: 'Demo00015',
          assetTypeName: 'app',
          model: 'byd',
          healthStatusName: 'A',
          groupNames: '车队5,车队2',
          sum: 0,
          asunm: 1, //严重级别告警数量
          bsunm: 1, //高级别告警数量
          csunm: 1, //中级别告警数量
          dsunm: 1, //低级别告警数量
          regisDate: '2023-01-13 14:49:17', //首次登记时间
          lastActiveDate: '2023-01-13 14:49:17', //最后接收时间
          locationCity: '湖南', //最后位置
        },
      ],
      allCount: 1,
    },
  }
}

//危险资产分析报告
export const getMonthThreatReport = function (data) {
  return request({
    url: `${vsocPath}/report/assets`,
    method: 'post',
    data,
    loading: true,
    responseType: 'blob',
  })
}

//威胁事件分析报告月报
export const getMonthAlarmReport = function (data) {
  return request({
    url: `${vsocPath}/report/alarmReport`,
    method: 'post',
    data,
    loading: true,
    responseType: 'blob',
  })
}

//威胁事件分析报告周报
export const getWeekAlarmReport = function (data) {
  return request({
    url: `${vsocPath}/report/alarm`,
    method: 'post',
    data,
    loading: true,
    responseType: 'blob',
  })
}
//响应工单明细下载
export const getExcelExport = function (data) {
  return request({
    url: `${vsocPath}/report/export`,
    method: 'post',
    data,
    loading: true,
    responseType: 'blob',
  })
}

//响应工单明细下载
export const getWeekTicketReport = function (data) {
  return request({
    url: `${vsocPath}/report/export`,
    method: 'post',
    data,
    loading: true,
    responseType: 'blob',
  })
}

//周报运营报告
export const reportDownload = function (data) {
  return request({
    url: `${vsocPath}/report/downloadReport`,
    method: 'post',
    data,
    loading: true,
  })
}
