import { request, vsocPath } from '../../util/request'

// 响应列表
export const getLinkages = function (data) {
  return request({
    url: `${vsocPath}/linkage/linkages`,
    method: 'post',
    data,
  })
}

// 新增响应
export const addResponse = function (data) {
  return request({
    url: `${vsocPath}/linkage/addLinkage`,
    method: 'post',
    data,
  })
}

// 编辑响应
export const editResponse = function (data) {
  return request({
    url: `${vsocPath}/linkage/updateLinkage`,
    method: 'post',
    data,
  })
}

// 响应详情
export const linkageDetail = function (data) {
  return request({
    url: `${vsocPath}/linkage/linkageDetail`,
    method: 'post',
    data,
  })
}

// 删除响应
export const delResponse = function (data) {
  return request({
    url: `${vsocPath}/linkage/deleteLinkage`,
    method: 'post',
    data,
  })
}

// 启用/停用响应
export const updateLinkageStatus = function (data) {
  return request({
    url: `${vsocPath}/linkage/updateLinkageStatus`,
    method: 'post',
    data,
  })
}

// 排序响应
export const linkageSort = function (data) {
  return request({
    url: `${vsocPath}/linkage/linkageSort`,
    method: 'post',
    data,
  })
}
