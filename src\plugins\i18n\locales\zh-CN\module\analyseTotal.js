const analyseTotal = {
  ac: '分析结果',
  jd: '理由与详情',
  yes: '是',
  no: '否',
  general1: '基础信息',
  cwe1: '选择CWE',
  vo: '漏洞概览',
  allComponet: '全部组件',
  vulnerableComponet: '风险组件',
  404: {
    action: '回去',
    heading: '哎呀！这太尴尬了',
    message: '找不到您要查找的页面',
  },
  admin: {
    access_management: '访问管理',
    alert_created: '已创建警报',
    alert_deleted: '已删除警报',
    alert_log_successful_publish: '记录发布成功',
    alert_log_successful_publish_help:
      '成功将警报发布到目标后发出日志消息，而不是仅在遇到问题时发出日志。帮助调试丢失的警报，或使警报发布可审核。',
    alerts: '警报',
    analyzer_internal_desc:
      '内部分析器根据来自国家漏洞数据库、GitHub 公告（如果已启用）和 VulnDB（如果已启用）的内部漏洞数据库评估组件。此分析器使用组件中定义的通用平台枚举 (CPE)。具有有效 CPE 的组件将使用此分析器进行评估。',
    analyzer_internal_enable: '启用内部分析器',
    analyzer_internal_fuzzy_enable:
      '启用模糊 CPE 匹配。有助于解决 NVD 数据不一致的问题，突出缺失的风险，但也会增加误报',
    analyzer_internal_fuzzy_exclude_internal: '在内部组件上启用模糊 CPE 匹配',
    analyzer_internal_fuzzy_exclude_purl:
      '对已定义包 URL (PURL) 的组件启用模糊 CPE 匹配',
    analyzer_ossindex_desc:
      'OSS Index 是 Sonatype 提供的一项服务，可识别第三方组件中的漏洞。Dependency-Track 与 OSS Index 服务原生集成，可提供高度准确的结果。使用此分析器需要被分析组件的有效 PackageURL。',
    analyzer_ossindex_enable: '启用 OSS 索引分析器',
    analyzer_snyk_alias_sync_warning:
      'Snyk 不区分相关漏洞和相同漏洞。请谨慎操作。',
    analyzer_snyk_api_version: 'API 版本',
    analyzer_snyk_api_version_warning: '更改默认版本可能会破坏集成！',
    analyzer_snyk_desc:
      'Snyk 的安全情报数据库（也称为 Snyk Intel 漏洞数据库）由专门的研究团队维护，该团队结合了公共资源、开发者社区的贡献、专有研究和机器学习，以不断适应不断变化和扩大的安全威胁。Dependency-Track 与 Snyk 的 REST API 本地集成，以提供高度准确的结果。使用此分析器需要被分析组件的有效 PackageURL。',
    analyzer_snyk_enable: '启用 Snyk 分析器',
    analyzer_snyk_how_to_api_token_help: '如何获取 API 令牌？',
    analyzer_snyk_how_to_api_version_help: '在哪里可以找到可用的版本？',
    analyzer_snyk_how_to_org_id_help: '如何找到我的组织 ID？',
    analyzer_snyk_multiple_tokens_info:
      '可以提供多个标记，用分号分隔它们，例如',
    analyzer_snyk_org_id: '组织 ID',
    analyzer_snyk_why_multiple_cvss: '为什么同一漏洞有多个 CVSS 评分？',
    analyzer_trivy_enable: '启用 Trivy 分析器',
    analyzer_trivy_ignore_unfixed: '忽略未修复的漏洞',
    analyzer_vulndb_desc:
      'VulnDB 是 Risk Based Security 提供的一项商业服务，可识别第三方组件中的漏洞。Dependency-Track 与 VulnDB 服务原生集成，可提供高度准确的结果。使用此分析器需要被分析组件的有效 CPE。',
    analyzer_vulndb_enable: '启用 VulnDB 分析器',
    analyzers: '分析器',
    api_key_comment: 'api_key_comment',
    api_key_comment_updated: 'api_key_comment_updated',
    api_key_created_tooltip: 'api_key_created_tooltip',
    api_key_last_used_tooltip: 'api_key_last_used_tooltip',
    api_keys: 'API 密钥',
    api_token: 'API 令牌',
    api_token_header: 'api_token_header',
    base_url: '基本 URL',
    bearer_token_auth_enable: '使用个人访问令牌进行身份验证',
    bom_formats: 'BOM 格式',
    bom_formats_desc:
      '启用对处理各种格式的 BOM 的支持。只有启用的 BOM 格式才会被处理。',
    bom_validation: '物料清单验证',
    bom_validation_info:
      '从历史上看，Dependency-Track 不会根据 CycloneDX 架构验证上传的 BOM 和 VEX。\n虽然这允许处理不严格遵守架构的 BOM，但当上传的文件被接受但在异步处理期间无法提取时，可能会导致混乱。\n从此版本开始，如果上传的文件未通过架构验证，将被拒绝。\n请注意，这可能会揭示 BOM 生成器中当前生成无效 CycloneDX 文档的问题',
    cargo: '货物',
    change_password: '更改密码',
    change_password_next_login: '用户必须在下次登录时更改密码',
    clone_template: '克隆模板',
    composer: '作曲家',
    configuration: '配置',
    configuration_saved: '配置已保存',
    configuration_test: '配置测试',
    consumer_key: '消费者密钥',
    consumer_secret: '消费秘密',
    cpan: '亚太网络',
    create_alert: '创建警报',
    create_ldap_user: '创建 LDAP 用户',
    create_managed_user: '创建管理用户',
    create_oidc_group: '创建组',
    create_oidc_user: '创建 OpenID Connect 用户',
    create_repository: '创建存储库',
    create_team: '创建团队',
    create_template: '创建模板',
    create_user: '创建用户',
    default: '默认',
    default_template_restored: '恢复默认模板',
    defectdojo: '缺陷Dojo',
    delete_alert: '删除警报',
    delete_oidc_group: '删除组',
    delete_repository: '删除存储库',
    delete_team: '删除团队',
    delete_template: '删除模板',
    delete_user: '删除用户',
    destination: '目的地',
    distinguished_name: '专有名称',
    edit_api_key_comment: 'edit_api_key_comment',
    email: '电子邮件',
    email_address: '电子邮件地址',
    email_enable_ssltls: '启用 SSL/TLS 加密',
    email_from_address: '发件人电子邮件地址',
    email_prefix: '主题前缀',
    email_smtp_password: 'SMTP 密码',
    email_smtp_port: 'SMTP 服务器端口',
    email_smtp_server: 'SMTP 服务器',
    email_smtp_username: 'SMTP 用户名',
    email_trust_cert: '信任 SMTP 服务器提供的证书',
    enable_acl: '启用投资组合访问控制（测试版）',
    enable_bom_cyclonedx: '启用 CycloneDX',
    enable_default_template_override: '启用默认模板覆盖',
    enable_email: '启用电子邮件',
    enable_index_consistency_check: '启用定期一致性检查',
    enable_svg_badge: '启用 SVG 徽章支持（未经身份验证）',
    enabled: '已启用',
    experimental: '实验性的',
    experimental_bom_upload_v2: 'BOM 处理 V2',
    experimental_bom_upload_v2_info:
      '处理上传的 BOM 并将其引入 Dependency-Track 的逻辑已经过彻底修改，变得更加可靠和高效。\n此外，BOM 处理现在是一个原子操作，因此中途发生的错误不会导致部分状态被遗漏。\n组件和服务的重复数据删除更加可预测，并且在处理过程中发出的日志消息包含额外的上下文，使它们更容易关联。\n由于新的实现会对 Dependency-Track 在 BOM 上传方面的行为产生重大影响，因此此版本默认禁用它。',
    experimental_info: '以下选项是实验性选项，应谨慎使用。',
    fortify_ssc: '强化SSC',
    gem: '宝石',
    general: '一般的',
    general_template_configuration: '常规模板配置',
    github: 'GitHub',
    github_advisories: 'GitHub 公告',
    go_modules: 'Go 模块',
    group: '团体',
    hackage: 'Hackage',
    hex: '十六进制',
    identifier: '标识符',
    include_active_children: '包括项目的活跃子项',
    include_children: '包括项目的子项',
    index_consistency_check_cadence: '节奏（分钟）',
    index_consistency_check_description:
      '您可以启用定期后台任务，检查所有索引是否存在、是否损坏以及它们与 Dependency Track 数据库的差异是否低于定义的阈值百分比。任何检查失败都将触发相应索引的重建。需要重新启动才能将节奏修改考虑在内。',
    index_consistency_check_threshold: '增量阈值（百分比）',
    index_general_description:
      'Dependency Track 使用 Apache Lucene 对项目或漏洞等各种实体进行全文搜索。',
    index_issues_description:
      '随着时间的推移，lucene 索引可能会退化或偏离 Dependency Track 数据库。尽管 DT 会尽最大努力将偏离最小化，但仍提供以下管理功能以在必要时检查或恢复索引。必须谨慎使用。',
    index_rebuild_description:
      '您可以选择性地触发部分或全部索引的立即重建。索引重建将由异步任务执行。您可以使用 Dependency Track 日志检查进度。',
    index_use_cases:
      '全文搜索功能主要用于搜索API（即所有索引）和CPE内部分析器模糊匹配（即漏洞软件索引）。',
    integration_defectdojo_enable: '启用 DefectDojo 集成',
    integration_defectdojo_reimport_enable: '启用重新导入',
    integration_fortify_ssc_enable: '启用 Fortify SSC 集成',
    integration_kenna_connector_id: '连接器 ID',
    integration_kenna_enable: '启用 Kenna Security 集成',
    integrations: '集成',
    internal: '内部的',
    internal_analyzer: '内部的',
    internal_component_desc:
      '内部组件将被排除在依赖外部系统的漏洞扫描和版本检查之外。\n请注意，以下正则表达式必须符合 Java 的正则表达式语法。\n正则表达式必须匹配整个字符串。要匹配所有包含“example”的命名空间，请写入“.*example.*”。',
    internal_components: '内部组件',
    internal_identification_error:
      '排队内部组件识别时发生错误。请查看服务器日志了解详细信息',
    internal_identification_queued: '内部组件识别排队',
    jira: '吉拉',
    jira_auth_with_token: 'Jira 个人访问令牌',
    jira_desc: 'Jira 服务身份验证',
    jira_password: 'Jira 密码',
    jira_ticket_type: 'Jira 票证类型',
    jira_url: 'Jira 基本 URL',
    jira_username:
      'Jira 用户（服务帐户）。必须将用户配置到适当的 jira 实例并允许其创建问题。',
    kenna_security: '肯纳安全',
    ldap_users: 'LDAP 用户',
    limit_to: '限制于',
    limit_to_projects: '限于项目',
    limit_to_tags: '限制标签',
    managed_users: '托管用户',
    mapped_ldap_groups: '映射 LDAP 组',
    mapped_oidc_groups: '映射的 OpenID 连接组',
    mapped_teams: '映射团队',
    maven: 'Maven',
    members: '成员',
    mime_type: 'Mime 类型',
    name_regex: '组件名称正则表达式',
    name_regex_desc: '指定一个正则表达式，通过组件名称来识别内部组件。',
    namespace_regex: '组件命名空间正则表达式',
    namespace_regex_desc:
      '指定按命名空间标识内部组件的正则表达式。命名空间在各种生态系统中通常称为“组”、“组织”或“供应商”。',
    national_vulnerability_database: '国家漏洞数据库',
    nixpkgs: 'Nixpkgs',
    notification_level: '通知级别',
    notifications: '通知',
    npm: '新平台',
    nuget: 'NuGet',
    nvd: '尼日尔',
    nvd_additionally_download_feeds: '另外下载提要',
    nvd_additionally_download_feeds_help:
      '提要不会被解析，但会提供给其他客户端',
    nvd_api_last_modification: '最后修改时间 (UTC)',
    nvd_api_last_modification_help:
      '在对 NVD 数据库进行一次完整的镜像后，所有后续的镜像操作将仅请求自上次成功执行以来修改的数据。',
    nvd_api_last_modification_warning:
      '通常不建议手动更改上次修改日期时间，但可用于强制重新提取 NVD 数据。请注意，由于 NVD 的 REST API 存在限制，配置了上次修改日期时间后，只能请求连续 120 天的数据。重置上次修改日期时间将导致整个 NVD 数据库重新镜像。',
    nvd_enable_mirroring_via_api: '通过 API 启用镜像',
    nvd_request_api_key_help: '如何获取 API 密钥？',
    nvd_why_enable_api_help: '为什么要启用 API 镜像？',
    oidc_group_created: 'OpenID Connect 组已创建',
    oidc_group_deleted: 'oidc_group_deleted',
    oidc_group_name: '团队名字',
    oidc_groups: 'OpenID 连接群组',
    oidc_users: 'OpenID Connect 用户',
    oss_index: 'Sonatype OSS 索引',
    osv_advisories: 'Google OSV 公告（测试版）',
    password: '密码（或访问令牌）',
    password_confirm: '确认密码',
    password_never_expires: '密码永不过期',
    password_updated: '密码已更新',
    perform_identification: '进行识别',
    perform_test: '进行测试',
    permissions: '权限',
    personal_access_token: '个人访问令牌',
    portfolio_access_control: '投资组合访问控制',
    project_access: '项目访问',
    publisher: '发行商',
    publisher_class: '出版者阶级',
    python: 'Python',
    registered_email_address: '注册电子邮件地址',
    reindex_components: '组件',
    reindex_cpes: '通用平台枚举（CPE）',
    reindex_error: '提交重新索引请求时出错',
    reindex_licenses: '许可证',
    reindex_projects: '项目',
    reindex_service_components: '服务组件',
    reindex_submitted: '重新索引请求已提交',
    reindex_vulnerabilities: '漏洞',
    reindex_vulnerable_software: '易受攻击的软件',
    remove_api_key: 'remove_api_key',
    repositories: '存储库',
    repository_authentication: '需要身份验证',
    repository_created: '已创建存储库',
    repository_deleted: '存储库已删除',
    repository_type: '存储库类型',
    required_confirmPassword: '需要确认密码，且密码必须匹配',
    required_email: '需要电子邮件地址',
    required_fullname: '需要全名',
    required_oidc_group_name: '名称为必填项',
    required_password: '密码是必需的',
    required_team_name: '团队名称为必填项',
    required_username: '用户名是必填项',
    restore_default_template: '恢复默认模板',
    scope: '范围',
    select_ecosystem: '选择生态系统',
    select_ldap_group: '选择 LDAP 组',
    select_oidc_group: '选择 OpenID Connect 组',
    select_permission: '选择权限',
    select_project: '选择项目',
    select_team: '选择团队',
    select_team_as_recipient: '选择团队作为收件人',
    snyk: 'Snyk（测试版）',
    subject_identifier: '主题标识符',
    submit: '提交',
    suspended: '暂停',
    synchronization_cadence_minutes: '同步节奏（分钟）',
    synchronization_cadence_restart_required:
      '需要重新启动 Dependency-Track 才能使节奏更改生效',
    task_scheduler: '任务计划程序',
    task_scheduler_component_analysis_cache_clear: '清除组件分析缓存',
    task_scheduler_description:
      'Dependency Track 任务调度程序以固定间隔执行各种后台任务。您可以使用下面的表单修改每个任务的固定间隔。每个间隔以小时为单位表示。需要重新启动 Dependency Track 才能以更新的节奏重新安排任务。',
    task_scheduler_internal_component_identification: '内部组件识别',
    task_scheduler_ldap_sync: 'LDAP 同步',
    task_scheduler_portfolio_metrics_update: '投资组合指标',
    task_scheduler_portfolio_vulnerability_analysis: '投资组合脆弱性分析',
    task_scheduler_repository_metadata_fetch: '存储库元数据获取',
    task_scheduler_vulnerability_metrics_update: '漏洞指标',
    team_created: 'team_created',
    team_deleted: '团队已删除',
    team_membership: '团队成员',
    team_name: '队名',
    teams: '团队',
    template: '模板',
    template_basedir: '模板基目录',
    template_basedir_tooltip: '此属性用作通知模板搜索的基目录',
    template_created: '模板已创建',
    template_deleted: '模板已删除',
    template_override_description:
      '打开模板覆盖控制并提供模板基目录允许您覆盖依赖跟踪默认通知发布者模板。',
    template_override_file_hierarchy:
      '模板基目录中具有适当目录层次结构和命名方案的任何 Pebble 模板（例如 ${base directory}/templates/notification/publisher/email.peb）都将覆盖 Dependency Track 默认模板。',
    template_override_restart_needed: '需要重新启动依赖轨道才能使修改生效。',
    template_override_security_warning:
      '您必须为模板基目录设置适当的权限，以防止不受信任的第三方提供可能导致潜在远程代码执行的欺诈性 Pebble 模板。',
    templates: '模板',
    test_notification_queued: '测试通知已排队',
    token: '代币',
    trivy: '琐事',
    url: '网址',
    user_created: '用户创建',
    user_deleted: '用户已删除',
    username: '用户名',
    vuln_sources: '漏洞来源',
    vulndb: '漏洞数据库',
    vulnsource_alias_sync_enable: '启用漏洞别名同步',
    vulnsource_alias_sync_enable_tooltip:
      '别名数据有助于识别多个数据库中的相同漏洞。如果源提供了此数据，请将其与 Dependency-Track 的数据库同步。',
    vulnsource_github_advisories_desc:
      'GitHub 公告 (GHSA) 是一个 CVE 数据库，其中包含影响开源世界的 GitHub 安全公告。Dependency-Track 通过 GitHub 的公共 GraphQL API 镜像公告，与 GHSA 集成。镜像每天刷新一次，或在重新启动 Dependency-Track 实例时刷新。需要个人访问令牌 (PAT) 才能通过 GitHub 进行身份验证，但无需为其分配范围。',
    vulnsource_github_advisories_enable: '启用 GitHub Advisory 镜像',
    vulnsource_nvd_desc:
      '国家漏洞数据库 (NVD) 是最大的公开漏洞情报来源。它由美国国家标准与技术研究院 (NIST) 的一个小组维护，并以 MITRE 和其他机构的工作为基础。NVD 中的漏洞称为常见漏洞和暴露 (CVE)。从 1990 年代到现在，NVD 中记录了超过 100,000 个 CVE。',
    vulnsource_nvd_enable: '启用国家漏洞数据库镜像',
    vulnsource_nvd_feeds_url: 'NVD 源 URL',
    vulnsource_nvd_notice:
      '该产品使用来自 NVD API 的数据，但未获得 NVD 的认可或认证。',
    vulnsource_osv_advisories_desc:
      'Google OSV 是开源项目的分布式漏洞和分类基础设施，旨在帮助开源维护者和开源消费者。它充当采用 OpenSSF 漏洞格式的漏洞数据库的聚合器。',
    vulnsource_osv_advisories_enable: '选择生态系统以启用 Google OSV 咨询镜像',
    vulnsource_osv_alias_sync_warning:
      'OSV 可能会将不相同的漏洞报告为别名。请谨慎操作。',
    vulnsource_osv_base_url: 'OSV 基本 URL',
  },
  condition: {
    forbidden: '禁止 (403)',
    http_request_error: 'HTTP 请求错误',
    server_error: '服务器错误',
    successful: '成功',
    unsuccessful_action: '无法执行请求的操作',
  },
  hashes: {
    blake3: 'BLAKE3',
    blake_256: 'BLAKE2b-256',
    blake_384: 'BLAKE2b-384',
    blake_512: 'BLAKE2b-512',
    md5: 'MD5',
    sha3_256: 'SHA3-256',
    sha3_384: 'SHA3-384',
    sha3_512: 'SHA3-512',
    sha_1: 'SHA-1',
    sha_256: 'SHA-256',
    sha_384: 'SHA-384',
    sha_512: 'SHA-512',
  },
  language: {
    de: '德语',
    en: '英语',
    es: '西班牙语',
    fr: '法语',
    hi: '印地语',
    it: '意大利语',
    ja: '日本人',
    pl: '抛光',
    pt: '葡萄牙语（葡萄牙）',
    'pt-BR': '葡萄牙语（巴西）',
    ru: '俄语',
    zh: '中国人',
  },
  message: {
    about: '关于',
    active: '有效',
    active1: '状态',
    add: '添加',
    add_affected_component: '添加受影响组件',
    add_comment: '添加评论',
    add_component: '添加组件',
    add_license: '添加许可证',
    add_tag: '添加标签',
    add_version: '添加版本',
    administration: '行政',
    affected_components: '受影响组件',
    affected_projects: '受影响项目',
    age: '年龄',
    age_tooltip:
      '采用 ISO-8601 时期格式的年龄（例如 P1Y = 1 岁；P2Y3M = 2 岁，3 个月）',
    aliases: '别名',
    analysis: '分析结果',
    analysis_details_tooltip:
      '详细信息（解释、解决方法详细信息和其他影响信息）',
    analysis_state: '分析状态',
    analysis_status: '分析状态',
    analysis_tooltip: '漏洞发生的当前状态',
    analyzer: '分析器',
    apply_vex: '应用VEX',
    apply_vex_tooltip: '将漏洞可利用性交换 (VEX) 文档的分析应用于该项目。',
    approved: '得到正式认可的',
    attributed_on: '归因于',
    audit_trail: '审计轨迹',
    audit_vulnerabilities: '审计漏洞',
    auditing_progress: '审计进度',
    authenticated: '已认证',
    authors: '作者',
    bom: '物料清单',
    bom_format: 'BOM 格式',
    bom_uploaded: 'BOM已上传',
    browse: '浏览',
    can_not_fix: '无法修复',
    cancel: '取消',
    change_password: '更改密码',
    classification: '分类',
    classifier: '分类器',
    classifier1: '类型',
    clear_all: '清除全部',
    close: '关闭',
    code_not_present: '代码不存在',
    code_not_reachable: '代码无法访问',
    comment: '评论',
    comments: '评论',
    component: '组件',
    component_application: '应用程序',
    component_author: '作者',
    component_author_desc: '组件的作者',
    component_classification: 'component_classification',
    component_classification_desc: 'component_classification_desc',
    component_classifier_desc:
      '指定组件的类型：资产（应用程序、操作系统和硬件）和非资产（库、框架和文件）',
    component_container: '容器',
    component_cpe_desc:
      'MITRE 或 NIST 提供的 CPE v2.2 或 v2.3 URI。所有资产（应用程序、操作系统和硬件）都应指定 CPE',
    component_created: '组件已创建',
    component_deleted: '组件已删除',
    component_details: '组件详细信息',
    component_device: '设备',
    component_file: '文件',
    component_filename_desc: '指定观察到的组件的文件名',
    component_firmware: '硬件',
    component_framework: '框架',
    component_group_desc: '供应商的更高级别命名空间、组或供应商标识符',
    component_hash: '组件哈希',
    component_hash_desc: '指定使用上述算法观察到的组件哈希值',
    component_identifier_desc:
      '通过软件包 URL (PURL) 或 CPE 标识组件。如果版本类型为“精确”，则 PURL 或 CPE 必须包含版本。如果指定了范围，则标识符中的版本信息将被忽略。',
    component_library: '库',
    component_license_expression_desc:
      '以 SPDX 表达式的形式指定组件的许可证信息',
    component_license_url_desc: '指定组件许可证的 URL',
    component_name: '组件名称',
    component_name_desc: '供应商提供的组件名称',
    component_namespace_group_vendor: '命名空间/组/供应商',
    component_operating_system: '操作系统',
    component_package_url_desc:
      '库和框架需要有效的包 URL。PURL 语法：pkg:type/namespace/name@version?qualifiers#subpath',
    component_properties: '组件属性',
    component_search: '组件搜索',
    component_spdx_license_desc: '指定组件的 SPDX 许可证 ID',
    component_supplier_name_desc: '供应组件的组织',
    component_swid_tagid_desc:
      '软件供应商提供的 ISO/IEC 19770-2:2015 (SWID) 标签 ID',
    component_updated: '组件已更新',
    component_version_desc: '供应商提供的组件版本',
    component_vulnerabilities: '组件漏洞',
    components: '组件',
    components1: '组件风险',
    components2: '组件总数',
    condition: '（健康）状况',
    condition_deleted: '条件已删除',
    conditions: '状况',
    connected_as: '连接为',
    contacts: '联系方式',
    coordinates: '组合查询',
    coordinates_version_tooltip:
      '您可以使用比较运算符 >、<、>=、<=、== 和 != 来匹配特定版本或版本范围',
    copyright: '版权',
    cpe: 'CPE',
    cpe_full: '通用平台枚举 (CPE)',
    create: '创造',
    create_component_property: '创建组件属性',
    create_license_group: '创建许可证组',
    create_policy: '创建策略',
    create_project: '创建项目',
    create_project_property: '创建项目属性',
    create_property: '创建属性',
    create_vulnerability: '创造漏洞',
    created: '创建',
    credits: '致谢',
    custom_license: '自定义许可证',
    custom_license_deleted: '自定义许可证已删除',
    cvss: 'CVSS',
    cvss_access_complexity: '访问复杂性',
    cvss_adjacent: '相邻',
    cvss_attack_complexity: '攻击复杂性',
    cvss_attack_vector: '攻击向量',
    cvss_authentication: '认证要求',
    cvss_availability_impact: '可用性影响',
    cvss_base_score: 'CVSS 基础得分',
    cvss_base_score_short: '基础得分',
    cvss_changed: '改变',
    cvss_complete: '完全',
    cvss_confidentiality_impact: '机密性影响',
    cvss_exploitability: '可利用性',
    cvss_exploitability_subscore: 'CVSS 可利用性子分数',
    cvss_high: '高',
    cvss_impact: '影响',
    cvss_impact_subscore: 'CVSS 影响子分数',
    cvss_integrity_impact: '完整性影响',
    cvss_local: '本地',
    cvss_low: '低',
    cvss_medium: '中',
    cvss_multiple: '多重',
    cvss_network: '网络',
    cvss_none: '无',
    cvss_none1: '无需认证',
    cvss_partial: '部分',
    cvss_physical: '物理',
    cvss_privileges_required: '所需权限',
    cvss_required: '需要',
    cvss_scope: '影响范围',
    cvss_single: '单一',
    cvss_source: '选择 CVSS 的源优先级',
    cvss_unchanged: '不变',
    cvss_user_interaction: '用户交互',
    cvss_v2: 'CVSSv2',
    cvss_v2_vector: 'CVSSv2 向量',
    cvss_v3: 'CVSSv3',
    cvss_v3_vector: 'CVSSv3 向量',
    cwe: 'CWE',
    cwe_full: '常见弱点列举 (CWE)',
    cwe_id: 'CWE ID',
    dashboard: '仪表板',
    data: '数据',
    dates: '日期',
    delete: '删除',
    delete_license_group: '删除许可证组',
    delete_policy: '删除策略',
    dependency_graph: '依赖图',
    deprecated: '已弃用',
    description: '描述',
    details: '详情',
    direct_only: '仅限直接',
    direction: '方向',
    download_bom: '下载 BOM',
    email: '电子邮件',
    endpoints: '端点',
    epss: 'EPSS',
    epss_percentile: 'EPSS 百分位数',
    epss_score: 'EPSS 评分',
    exact: '精确',
    exploit_predictions: '利用预测',
    exploitable: '可被利用',
    export_vdr: '导出 VDR',
    export_vdr_tooltip: '导出漏洞披露报告 (VDR)，如 NIST SP 800-161 中所定义。',
    export_vex: '导出 VEX',
    export_vex_tooltip: '导出漏洞可利用性交换 (VEX) 文档。',
    extended: '扩展',
    external_references: '外部引用',
    false_positive: '误报',
    filename: '文件名',
    filters: '筛选器',
    findings: '发现',
    findings_audited: '审计发现',
    findings_unaudited: '未经审计的调查结果',
    first_seen: '首次出现于',
    from: '从',
    fsf_libre: 'FSF 自由',
    fullname: '全名',
    general: '一般的',
    global_audit: '全球审计',
    group: '供应商',
    group_name: '供应商名称',
    grouped_vulnerabilities: '分组漏洞',
    hashes: '哈希',
    hashes_short_desc: '哈希（MD5、SHA、SHA3、Blake2b、Blake3）',
    home: '家',
    identifier: '标识符',
    identifier_type: '标识符类型',
    identity: '身份',
    in_triage: '待评估',
    inactive: '无效',
    inactive_active_children:
      '如果项目有活跃子项目，则不能将其设置为非活跃项目',
    inactive_versions: '非活动版本',
    include_acl: '包括访问控制列表',
    include_audit_history: '包括审计历史',
    include_components: '包括组件',
    include_policy_violations: '包括违反政策的行为',
    include_properties: '包括属性',
    include_services: '包括服务',
    include_tags: '包含标签',
    inherited_risk_score: '继承风险评分',
    internal: '内部的',
    inventory: '存货',
    inventory_with_vulnerabilities: '存在漏洞的清单',
    justification: '理由',
    justification_tooltip: '影响分析状态被断言为“不受影响”的理由',
    language: '语言',
    last_bom_import: '上次 BOM 导入',
    last_measurement: '最后一次检测',
    last_seen: '最后更新时间',
    latest_version: '最新版本',
    legal: '合法的',
    license: '执照',
    license_comments: '许可证评论',
    license_expression: 'SPDX 表达式',
    license_group: '许可证组',
    license_group_created: '许可证组已创建',
    license_group_deleted: '许可证组已删除',
    license_groups: '许可证组',
    license_id: '许可证 ID',
    license_id_desc:
      'SPDX 定义的许可证 ID。该 ID 只能包含字母、数字和特定符号：_ - . +',
    license_name: '许可证名称',
    license_name_desc: '供应商提供的许可证名称',
    license_risk: '牌照风险',
    license_text: '许可证文本',
    license_url: '许可证网址',
    licenses: '许可证',
    login: '登录',
    login_desc: '登录到您的帐户',
    login_forbidden: '此帐户处于非活动状态或已被暂停',
    login_more_options: '更多的选择',
    login_unauthorized: '用户名或密码无效',
    logout: '登出',
    manufacturer: '制造商',
    manufacturer_name: '生产商名称',
    manufacturer_name_desc: '制造项目所述组件的组织',
    matrix: '矩阵',
    metric_refresh_requested: '已请求刷新。刷新任务完成后，指标将会更新。',
    name: '名称',
    no_file_chosen: '没有选中任何文件',
    non_vulnerable: '非脆弱性',
    not_affected: '未受影响',
    not_found_in_dependency_graph: '在依赖关系图中找不到依赖关系',
    not_set: '未设置',
    notes: '笔记',
    object_identifier: '对象标识符',
    object_identifier_desc:
      'Dependency-Track 自动为每个对象分配的唯一标识符 (UUID)',
    occurred_on: '发生于',
    occurrences_in_projects: '项目中发生的事件',
    oidc_availability_check_failed: '无法确定 OpenID Connect 是否可用',
    oidc_redirect_failed: '重定向至 OpenID Connect 身份提供商时发生错误',
    only_direct_tooltip: '仅显示直接依赖关系，隐藏传递依赖关系',
    only_outdated_tooltip: '仅显示具有较新的稳定版本的依赖项',
    operational_risk: '操作风险',
    operator: '操作员',
    osi_approved: 'OSI 批准',
    outdated_only: '仅限过时',
    overview: '概述',
    owasp_rr: 'OWASP 风险评级',
    owasp_rr_awareness: '意识程度',
    owasp_rr_awareness_hidden: '隐藏',
    owasp_rr_awareness_obvious: '明显',
    owasp_rr_awareness_public_knowledge: '公共',
    owasp_rr_awareness_unknown: '未知',
    owasp_rr_business_impact_factor: '业务影响因素',
    owasp_rr_business_impact_score: 'OWASP RR 业务影响评分',
    owasp_rr_business_impact_score_short: '业务影响',
    owasp_rr_ease_of_discovery: '发现难易程度',
    owasp_rr_ease_of_discovery_automated_tools_available: '自动化',
    owasp_rr_ease_of_discovery_difficult: '困难',
    owasp_rr_ease_of_discovery_easy: '容易',
    owasp_rr_ease_of_discovery_impossible: '不可能',
    owasp_rr_ease_of_exploit: '利用难易程度',
    owasp_rr_ease_of_exploit_automated_tools_available: '自动化',
    owasp_rr_ease_of_exploit_difficult: '困难',
    owasp_rr_ease_of_exploit_easy: '容易',
    owasp_rr_ease_of_exploit_theoritical: '理论',
    owasp_rr_financial_damage: '经济损失',
    owasp_rr_financial_damage_bankruptcy: '破产',
    owasp_rr_financial_damage_less_than_cost_to_fix: '小于修复成本',
    owasp_rr_financial_damage_minor: '轻微',
    owasp_rr_financial_damage_significant: '重大',
    owasp_rr_intrusion_detection: '入侵检测',
    owasp_rr_intrusion_detection_active: '实时监控',
    owasp_rr_intrusion_detection_logged_reviewed: '记录并审查',
    owasp_rr_intrusion_detection_logged_unreviewed: '记录但未审查',
    owasp_rr_intrusion_detection_not_logged: '未记录',
    owasp_rr_likelihood_score: 'OWASP RR 似然分数',
    owasp_rr_likelihood_score_short: '可能性',
    owasp_rr_loss_of_accountability: '责任丧失',
    owasp_rr_loss_of_accountability_completely_anonymous: '完全匿名/不可追溯',
    owasp_rr_loss_of_accountability_fully_traceable: '完全可追溯',
    owasp_rr_loss_of_accountability_possibly_traceable: '可能可追溯',
    owasp_rr_loss_of_availability: '服务可用性丧失',
    owasp_rr_loss_of_availability_all: '所有系统不可用',
    owasp_rr_loss_of_availability_extensive_primary_services:
      '主要系统严重影响',
    owasp_rr_loss_of_availability_extensive_secondary_services:
      '次要系统广泛影响',
    owasp_rr_loss_of_availability_minimal_primary_services: '主要系统轻微',
    owasp_rr_loss_of_availability_minimal_secondary_services:
      '次要系统轻微影响',
    owasp_rr_loss_of_confidentiality: '失去机密性（数据）',
    owasp_rr_loss_of_confidentiality_all: '全部数据（严重）',
    owasp_rr_loss_of_confidentiality_extensive_critical:
      '大量关键敏感数据（重大）',
    owasp_rr_loss_of_confidentiality_extensive_non_sensitive:
      '大量非敏感数据（中等）',
    owasp_rr_loss_of_confidentiality_minimal_critical: '少量敏感数据',
    owasp_rr_loss_of_confidentiality_minimal_non_sensitive:
      '少量非敏感数据（轻微）',
    owasp_rr_loss_of_integrity: '完整性丧失（损害）',
    owasp_rr_loss_of_integrity_all: '完全丧失（严重）',
    owasp_rr_loss_of_integrity_extensive_seriously_corrupt:
      '严重广泛影响（重大）',
    owasp_rr_loss_of_integrity_extensive_slightly_corrupt:
      '广泛轻微影响（中等）',
    owasp_rr_loss_of_integrity_minimal_seriously_corrupt:
      '显著轻微影响（轻微）',
    owasp_rr_loss_of_integrity_minimal_slightly_corrupt: '轻微影响（极低）',
    owasp_rr_motivation: '动机',
    owasp_rr_motivation_high_reward: '高回报',
    owasp_rr_motivation_low: '低',
    owasp_rr_motivation_possible_reward: '可能的回报',
    owasp_rr_non_compliance: '不合规',
    owasp_rr_non_compliance_clear: '明显',
    owasp_rr_non_compliance_high_profile: '高关注度',
    owasp_rr_non_compliance_minor: '轻微',
    owasp_rr_opportunity: '机会/访问',
    owasp_rr_opportunity_full: '完全',
    owasp_rr_opportunity_none: '无',
    owasp_rr_opportunity_some: '部分',
    owasp_rr_opportunity_special: '特殊',
    owasp_rr_privacy_violation: '侵犯隐私',
    owasp_rr_privacy_violation_hundreds: '数百人',
    owasp_rr_privacy_violation_millions: '数百万人',
    owasp_rr_privacy_violation_one_individual: '一人',
    owasp_rr_privacy_violation_thousands: '数千人',
    owasp_rr_reputation_damage: '声誉损失',
    owasp_rr_reputation_damage_brand: '品牌',
    owasp_rr_reputation_damage_goodwill: '商誉',
    owasp_rr_reputation_damage_major_accounts: '主要客户',
    owasp_rr_reputation_damage_minimal: '最小',
    owasp_rr_skill_level: '技能水平',
    owasp_rr_skill_level_advanced: '精通',
    owasp_rr_skill_level_network_and_programming: '专家',
    owasp_rr_skill_level_none: '外行',
    owasp_rr_skill_level_security_penetration_testing: '多领域专家',
    owasp_rr_skill_level_some: '基础',
    owasp_rr_technical_impact_factor: '技术影响因子',
    owasp_rr_technical_impact_score: 'OWASP RR 技术影响分数',
    owasp_rr_technical_impact_score_short: '技术影响',
    owasp_rr_threat_agent_factor: '威胁代理因素',
    owasp_rr_threat_size: '威胁规模',
    owasp_rr_threat_size_anonymous_internet_users: '互联网用户',
    owasp_rr_threat_size_auth_users: '已认证用户',
    owasp_rr_threat_size_dev_sa: '开发/系统管理员',
    owasp_rr_threat_size_intranet: '内网用户',
    owasp_rr_threat_size_partners: '合作伙伴',
    owasp_rr_vulnerability_factor: '漏洞因素',
    package_url: '包裹网址 (PURL)',
    package_url_full: '包裹网址 (PURL)',
    password: '密码',
    password_change: '更改密码',
    password_change_success: '密码修改成功',
    password_confirm: '确认新密码',
    password_current: '当前密码',
    password_force_change: '更新密码',
    password_force_change_desc: '您需要更改密码',
    password_new: '新密码',
    password_not_acceptable:
      '检查新密码和确认密码是否匹配。您不能重复使用旧密码',
    password_unauthorized: '检查当前登录凭据并重试',
    phone: '电话',
    policies: '政策',
    policy_created: '已创建政策',
    policy_deleted: '政策已删除',
    policy_management: '策略管理',
    policy_name: '策略名称',
    policy_violations: '违反政策',
    policy_violations_by_classification: '违反政策的分类',
    policy_violations_by_state: '各州违反政策的情况',
    portfolio: '文件夹',
    portfolio_statistics: '项目组合统计',
    portfolio_vulnerabilities: '项目组合漏洞',
    profile_update: '更新个人信息',
    profile_updated: '个人资料已更新',
    project_cloning_in_progress: '正在使用指定的克隆选项创建项目',
    project_created: '项目已创建',
    project_deleted: '项目已删除',
    project_details: '项目细节',
    project_metadata_supplier_name_desc: '提供 BOM 的组织',
    project_name: '项目名称',
    project: '项目',
    project_name_desc: '供应商提供的项目或组件名称',
    project_properties: '项目属性',
    project_reanalyze: '重新分析',
    project_reanalyze_requested:
      '已请求进行项目脆弱性分析。重新分析任务完成后，项目脆弱性数据将会更新。',
    project_reanalyze_tooltip:
      '运行配置的分析器来检测此项目组件中的漏洞。将使用尚未过期的任何缓存结果',
    project_supplier_name_desc: '提供项目所述组件的组织',
    project_updated: '项目已更新',
    project_vulnerabilities: '漏洞趋势',
    projects: '项目',
    projects2: '项目总数',
    projects_at_risk: '风险项目',
    properties: '特性',
    property_created: '创建的财产',
    property_deleted: '属性已删除',
    property_name: '属性名称',
    property_type: '财产种类',
    property_value: '适当的价值',
    protected_at_perimeter: '边界防护',
    protected_at_runtime: '运行时保护',
    protected_by_compiler: '由编译器保护',
    protected_by_mitigating_control: '通过缓解措施进行保护',
    provider: '提供者',
    provider_name: '提供商名称',
    published: '发布',
    published1: '发布时间',
    purl: 'purl',
    range: '范围',
    recommendation: '推荐',
    references: '参考',
    reindex: '重建索引',
    rejected: '拒绝',
    remove_component: '移除组件',
    reported_by: '报告者',
    required_component_identifier: '需要组件标识符',
    required_component_name: '组件名称为必填项',
    required_component_version: '组件版本为必填项',
    required_license_id: '需要许可证 ID',
    required_license_name: '许可证名称为必填项',
    required_project_name: '项目名称为必填项',
    required_service_name: '服务名称为必填项',
    required_vulnerability_vuln_id: '需要唯一的漏洞标识符',
    requires_configuration: '需要依赖项 ',
    requires_dependency: '需要依赖',
    requires_environment: '需要特定环境',
    reset: '重置',
    resolved: '已解决',
    response: '供应商响应（项目）',
    response_tooltip:
      '受影响组件或服务的制造商、供应商或负责该项目的人员对漏洞做出的响应',
    risk_score: '风险评分',
    risk_type: '风险类型',
    rollback: '回滚',
    search: '搜索',
    search_parent: '键入以搜索父项',
    security_risk: '安全风险',
    see_also: '也可以看看',
    select: '选择',
    select_cwe: '选择 CWE',
    select_license: '选择许可证',
    select_project: '选择项目',
    select_tag: '选择标签',
    service_deleted: '服务已删除',
    service_details: '服务详情',
    service_name: '服务名称',
    service_name_desc: '提供者描述的服务名称',
    service_provider_name_desc: '提供商的名称',
    service_updated: '服务已更新',
    service_version_desc: '提供商描述的服务版本',
    service_vulnerabilities: '服务漏洞',
    services: '服务',
    severity: '严重级别',
    show_complete_graph: '显示完整图表',
    show_flat_view: '展示单位项目视图',
    show_in_dependency_graph: '在依赖图中显示',
    show_inactive_projects: '显示不活动的项目',
    show_suppressed_findings: '显示隐藏',
    show_suppressed_violations: '显示已抑制的违规行为',
    show_update_information: '突出显示过时的组件',
    snapshot_notification: '快照通知',
    source_header: '源标头',
    spdx_license_id: 'SPDX 许可证 ID',
    state: '状态',
    subtitle: '字幕',
    supplier: '供应商',
    supplier_name: '供应商名称',
    suppress: '压制',
    suppressed: '被抑制',
    suppressed2: '抑制总数',
    swid: 'swid',
    swid_tagid: 'SWID标签编号',
    switch_view: '搜索时无法切换视图',
    tag_name: '标签名',
    tags: '标签',
    template: '模板',
    text_search: '文本搜索',
    title: '标题',
    to: '到',
    total: '全部的',
    total_findings: '所有审计记录',
    total_findings_excluding_aliases: '总结果（不包括别名）',
    total_findings_including_aliases: '总发现数（包括别名）',
    type: '类型',
    update: '更新',
    update_details: '更新详情',
    updated: '更新',
    upload: '上传',
    upload_bom: '上传BOM',
    upload_bom_tooltip: '上传 BOM，所有组件都将进行漏洞分析',
    upload_vex: '上传 VEX',
    url: '网址',
    urls: '网址',
    username: '用户名',
    value: '价值',
    vendor_response: '供应商回应',
    version: '版本',
    version_distance: '版本距离',
    version_distance_epoch: '时代',
    version_distance_major: '主要的',
    version_distance_minor: '次要的',
    version_distance_patch: '修补',
    version_distance_tooltip: '指定版本号之间的差异，或为空以忽略',
    version_type: '版本类型',
    vex_uploaded: 'VEX 已上传',
    view_details: '查看详情',
    violation_state: '违规状态',
    violations_audited: '违规行为已审计',
    violations_unaudited: '违规行为未经审计',
    vulnerabilities: '漏洞',
    vulnerabilities_by_occurrence: '漏洞发生情况',
    vulnerability: '漏洞',
    vulnerability2: '漏洞总数',
    vulnerability_audit: '漏洞审计',
    vulnerability_created: '造成漏洞',
    vulnerability_created_desc: '漏洞记录最初创建的日期',
    vulnerability_deleted: '漏洞已删除',
    vulnerability_details: '漏洞详细信息',
    vulnerability_published_desc: '漏洞记录最初发布的日期',
    vulnerability_title: '标题',
    vulnerability_title_desc: '漏洞的可选标题',
    vulnerability_updated: '漏洞更新',
    vulnerability_updated_desc: '漏洞记录上次更新的日期',
    vulnerability_vuln_id: '漏洞编号',
    vulnerability_vuln_id_desc: '在同一来源内唯一标识此漏洞的标识符',
    vulnerable: '易受伤害的',
    vulnerable_components: '易受攻击的组件',
    vulnerable_projects: '易受攻击的项目',
    weakness: '弱点',
    will_not_fix: '不会修复',
    workaround_available: '临时解决方案',
    x_trust_boundary: '跨越信任边界',
  },
  operator: {
    contains_all: '包含全部',
    contains_any: '包含任何',
    is: '是',
    is_not: '不是',
    matches: '火柴',
    no_match: '不匹配',
  },
  policy_violation: {
    fails: '违规失败',
    infos: '信息违规',
    license: '违反许可证',
    operational: '操作违规',
    security: '安全违规',
    total: '违规总数',
    warns: '违规警告',
  },
  severity: {
    critical: '严重',
    critical_severity: '严重级别',
    cvss_severity: 'CVSS 严重级别',
    derive_from_cvss_or_owasp_rr: '源自 CVSS 或 OWASP RR',
    high: '高',
    high_severity: '高严重级别',
    info: '信息',
    info_severity: '信息',
    low: '低',
    low_severity: '低严重级别',
    medium: '中',
    medium_severity: '中等严重级别',
    owasp_rr_severity: 'OWASP 风险评级严重性',
    unassigned: '未分配',
    unassigned_severity: '未分配严重性',
  },
  validation: {
    confirmed: '{_field_} 不匹配',
    max_value: '{_field_} 值应低于 {max}',
    min_value: '{_field_} 值应高于 {min}',
    required: '{_field_} 为必填',
  },
  violation: {
    fail: '失败',
    info: '通知',
    warn: '警告',
  },
  vulnerability: {
    critical: '严重级别极高的漏洞',
    high: '高严重性漏洞',
    low: '低严重级别漏洞',
    medium: '中等严重级别漏洞',
    unassigned: '未指派的漏洞',
  },
}
export default analyseTotal
