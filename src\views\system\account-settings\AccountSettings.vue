<template>
  <div class="d-flex flex-column">
    <!-- tabs -->
    <v-card tile elevation="0" class="border-bottom">
      <v-tabs v-model="tab" centered>
        <v-tab v-for="tab in tabs" :key="tab.icon" class="px-10">
          <!-- <v-icon size="20" class="me-3">
          {{ tab.icon }}
        </v-icon> -->
          <span>{{ tab.title }}</span>
        </v-tab>
      </v-tabs>
    </v-card>

    <div class="pa-3 font-weight-normal" :class="{ 'pb-0': tab === 1 }">
      <span v-show="tab === 0">{{ $t('account.hint') }}</span>
    </div>

    <!-- <div class="flex-1 bg-white">
      <account-settings-account v-show="tab === 0"></account-settings-account>

      <account-settings-security v-show="tab === 1"></account-settings-security>
    </div> -->
    <!-- tabs item -->
    <v-tabs-items v-model="tab">
      <v-tab-item>
        <account-settings-account v-show="tab === 0"></account-settings-account>
      </v-tab-item>

      <v-tab-item>
        <account-settings-security
          v-show="tab === 1"
        ></account-settings-security>
      </v-tab-item>
    </v-tabs-items>
  </div>
</template>

<script>
import {
  mdiAccountOutline,
  mdiBellOutline,
  mdiBookmarkOutline,
  mdiInformationOutline,
  mdiLockOpenOutline,
} from '@mdi/js'
import { computed, getCurrentInstance, ref } from '@vue/composition-api'
// demos
import AccountSettingsAccount from './AccountSettingsAccount.vue'
import AccountSettingsSecurity from './AccountSettingsSecurity.vue'

export default {
  components: {
    AccountSettingsAccount,
    AccountSettingsSecurity,
  },
  setup() {
    const tab = ref('')

    const vm = getCurrentInstance().proxy

    // tabs
    const tabs = computed(() => [
      { title: vm.$t('account.accountInfo'), icon: mdiAccountOutline },
      {
        title: vm.$t('account.changePassword'),
        icon: mdiLockOpenOutline,
      },

      // { title: 'Info', icon: mdiInformationOutline },
      // { title: 'Billing', icon: mdiBookmarkOutline },
      // { title: 'Notifications', icon: mdiBellOutline },
    ])

    // account settings data
    const accountSettingData = {
      account: {
        avatarImg: require('@/assets/images/avatars/1.png'),
        username: 'johnDoe',
        name: 'john Doe',
        email: '<EMAIL>',
        role: 'Admin',
        status: 'Active',
        company: 'Google.inc',
      },
      information: {
        bio: 'The name’s John Deo. I am a tireless seeker of knowledge, occasional purveyor of wisdom and also, coincidentally, a graphic designer. Algolia helps businesses across industries quickly create relevant 😎, scaLabel 😀, and lightning 😍 fast search and discovery experiences.',
        birthday: 'February 22, 1995',
        phone: '************',
        website: 'https://ACT.com/',
        country: 'USA',
        languages: ['English', 'Spanish'],
        gender: 'male',
      },
      notification: {
        commentOnArticle: true,
        answerOnForm: true,
        followMe: false,
        newsAnnouncements: false,
        productUpdates: true,
        blogDigest: false,
      },
    }

    const onChange = event => {
      console.log('event', event)
    }

    return {
      onChange,
      tab,
      tabs,
      accountSettingData,
      icons: {
        mdiAccountOutline,
        mdiLockOpenOutline,
        mdiInformationOutline,
        mdiBookmarkOutline,
        mdiBellOutline,
      },
    }
  },
}
</script>
