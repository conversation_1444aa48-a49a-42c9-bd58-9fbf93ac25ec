<template>
  <v-card class="h-full d-flex flex-column">
    <v-card-title>
      <span>{{ $t('analytics.alertTrend') }}</span>
      <!-- <v-spacer></v-spacer> -->
      <!-- <div class="font-weight-semibold">
              <v-icon size="1.25rem" color="error"> mdi-circle-medium </v-icon
              ><span class="text-sm mr-1">严重</span>

              <v-icon size="1.25rem" color="danger"> mdi-circle-medium </v-icon
              ><span class="text-sm mr-1">高</span>

              <v-icon size="1.25rem" color="warning"> mdi-circle-medium </v-icon
              ><span class="text-sm mr-1">中</span>

              <v-icon size="1.25rem" color="low"> mdi-circle-medium </v-icon
              ><span class="text-sm mr-1">低</span>
            </div> -->
    </v-card-title>
    <!-- <vue-apex-charts
            id="total-profit-chart"
            height="280"
            :options="chartOptions"
            :series="chartData"
          ></vue-apex-charts> -->
    <v-card-text class="flex-1">
      <vsoc-chart
        ref="echart"
        class="echart"
        :echart-id="echartId"
        :option="chartOption"
        :isYDashed="true"
      ></vsoc-chart>
    </v-card-text>
  </v-card>
</template>

<script>
import { tooltip } from '@/assets/echarts-theme/constant'
import { toDate } from '@/util/filters'
import { kFormatter } from '@core/utils/filter'
import { addDays, differenceInDays } from 'date-fns'
import VueApexCharts from 'vue-apexcharts'
// eslint-disable-next-line object-curly-newline
import VsocChart from '@/components/VsocChart.vue'
import store from '@/store'
import { getVuetify } from '@core/utils'
import {
  mdiChartBar,
  mdiCurrencyUsd,
  mdiDotsVertical,
  mdiTrendingUp,
} from '@mdi/js'
import { computed, ref, watch } from 'vue-demi'
export default {
  components: {
    VueApexCharts,
    VsocChart,
  },
  props: {
    list: {
      type: [Array],
      default: [],
    },
    echartId: {
      type: String,
      default: () => 'echart',
    },
    advanceQuery: {
      type: Object,
      default: {},
    },
  },
  setup(props) {
    const $vuetify = getVuetify()

    const chartOptions = {
      colors: [
        $vuetify.theme.currentTheme.low,
        $vuetify.theme.currentTheme.danger,
        $vuetify.theme.currentTheme.warning,
        $vuetify.theme.currentTheme.error,
      ],
      chart: {
        type: 'bar',
        stacked: true,
        toolbar: {
          show: false,
        },
      },
      plotOptions: {
        bar: {
          borderRadius: 10,
          columnWidth: '35%',
          startingShape: 'rounded',
          endingShape: 'rounded',
        },
      },
      xaxis: {
        categories: [
          '12/13',
          '12/14',
          '12/15',
          '12/16',
          '12/17',
          '12/18',
          '12/19',
          '12/20',
          '12/21',
          '12/22',
          '12/23',
          '12/24',
          '12/25',
          '12/26',
          '12/27',
          '12/28',
        ],
        axisBorder: {
          show: false,
        },
        axisTicks: {
          show: false,
        },
      },
      yaxis: {
        labels: {
          formatter: value => kFormatter(value, 0),
        },
      },
      grid: {
        strokeDashArray: 7,
      },
      dataLabels: {
        enabled: false,
      },
      legend: {
        show: false,
      },
      stroke: {
        curve: 'smooth',
        width: 6,
        lineCap: 'round',
        colors: ['#fff'],
      },
      responsive: [
        {
          breakpoint: 1650,
          options: {
            plotOptions: {
              bar: {
                borderRadius: 10,
                columnWidth: '45%',
              },
            },
          },
        },
      ],
    }

    const chartData = [
      {
        name: '严重',
        data: [
          60, 220, 250, 190, 300, 210, 350, 250, 190, 30, 210, 190, 30, 210,
        ],
      },
      {
        name: '高',
        data: [40, 160, 110, 150, 100, 120, 90, 150, 0, 120, 90, 0, 120, 90],
      },
      {
        name: '中',
        data: [10, 50, 0, 140, 90, 110, 120, 140, 40, 110, 120, 40, 110, 120],
      },
      {
        name: '低',
        data: [10, 20, 90, 100, 10, 100, 20, 400, 50, 110, 200, 50, 110, 200],
      },
    ]
    const chartOption = ref({
      tooltip,
      // 图例设置
      legend: {
        show: false,
        right: 0,
        icon: 'circle',
        itemHeight: 6, // 修改icon图形大小
        itemGap: 24,
        textStyle: {
          fontSize: 12,
          padding: [0, 0, 0, -8], // 修改文字和图标距离
        },
      },
      grid: {
        left: 10,
        right: 5,
        bottom: 10,
        top: 20,
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          data: ['12/13', '12/14', '12/15', '12/16', '12/17', '12/18', '12/19'],
          // 隐藏坐标轴刻度
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          itemStyle: {
            borderRadius: 50,
          },
        },
      ],
      markArea: {
        itemStyle: {
          borderCap: 'round',
        },
      },
      series: [
        {
          name: 'Email',
          type: 'bar',
          stack: 'Ad',
          barWidth: 10,
          barMinAngle: 50,

          emphasis: {
            focus: 'series',
          },
          itemStyle: {
            borderRadius: 50,
            borderWidth: 30,
            borderJoin: 'round',
          },
          data: [120, 0, 101, 134, 90, 230, 210],
        },
        {
          name: 'Union Ads',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          itemStyle: {
            borderRadius: 50,
          },
          data: [220, 182, 191, 234, 290, 330, 310],
        },
        {
          name: 'Video Ads',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          itemStyle: {
            borderRadius: 50,
          },
          data: [150, 232, 201, 154, 190, 330, 410],
        },
      ],
    })
    const baseSeries = {
      name: 'Email',
      type: 'bar',
      stack: 'total-profit',
      barWidth: 15,
      barMinHeight: 15,
      chip: false,
      emphasis: {
        focus: 'series',
      },
      itemStyle: {
        borderRadius: 10,
        borderWidth: 5, // 堆叠间隔
        borderColor: 'transparent',
        borderJoin: 'round',
        borderType: 'solid',
      },
      data: [120, 132, 101, 134, 90, 230, 210],
    }

    const alarmLevel = computed(() => store.state.enums.enums.AlarmLevel)
    const colorList = Object.assign([], alarmLevel).value

    watch(
      () => props.list,
      newArr => {
        // chartOption.value.color = Object.assign([], alarmLevel).map(
        //   v => v.color,
        // )
        let diffDay = differenceInDays(
          new Date(props.advanceQuery.endDate),
          new Date(props.advanceQuery.startDate),
        )
        let xAxisData = []
        for (let i = 0; i <= diffDay; i++) {
          const cur = toDate(
            addDays(new Date(props.advanceQuery.startDate), i),
            'yyyy-MM-dd',
          )
          xAxisData.push(cur)
        }

        chartOption.value.series = newArr.map(item => {
          // xAxisData = [...xAxisData, ...item.arrays.map(x => x.time)]
          return {
            ...baseSeries,
            color: colorList[item.level]?.color || '',
            name: alarmLevel.value[item.level]?.text,
            data: item.arrays.map(s => {
              return [xAxisData.findIndex(v => v === s.time), s.number]
            }),
          }
        })
        // chartOption.value.xAxis[0].data = [
        //   ...new Set(xAxisData.map(date => toDate(date, 'MM/dd'))),
        // ]
        chartOption.value.xAxis[0].data = xAxisData.map(date =>
          toDate(date, 'MM/dd'),
        )
        chartOption.value.color = chartOption.value.series.map(c => c.color)
        // chartOption.value.series = newVal.map(item => {
        //   return Object.assign(baseSeries, item)
        // })
        // chartOption.value.xAxis = newVal.xAxisData
      },
      { immediate: true, deep: true },
    )
    return {
      chartOptions,
      chartOption,
      chartData,

      icons: {
        mdiDotsVertical,
        mdiTrendingUp,
        mdiCurrencyUsd,
        mdiChartBar,
      },
    }
  },
}
</script>

<style lang="scss">
@import '~@core/preset/preset/mixins.scss';
@import '~vuetify/src/styles/styles.sass';
@media #{map-get($display-breakpoints, 'sm-and-up')} {
  .total-profit-chart-col {
    @include ltr() {
      border-right: thin solid;
    }
    @include rtl() {
      border-left: thin solid;
    }
  }
}
@include theme--child(total-profit-chart-col) using ($material) {
  @media #{map-get($display-breakpoints, 'sm-and-up')} {
    border-color: map-get($material, 'dividers');
  }
}
</style>
