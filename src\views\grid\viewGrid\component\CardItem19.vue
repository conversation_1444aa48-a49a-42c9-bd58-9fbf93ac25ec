<template>
  <!-- 横向柱状图 -->
  <div class="box">
    <div class="box-header">{{ title }}</div>
    <template v-if="list.length !== 0">
      <vsoc-chart
        :echartId="echartId"
        class="box-chart d-flex align-center"
        :option="option"
      ></vsoc-chart>
    </template>
    <template v-else>
      <div
        class="box-chart d-flex align-center color--primary justify-center fs-16"
      >
        None
      </div>
    </template>
  </div>
</template>

<script>
import VsocChart from '@/components/VsocChart.vue'
import { num, numberToFormat } from '@/util/filters'
import {
  axisLabel,
  getRoundSize,
  grid,
  legend,
  splitLine,
  stackSeriesFn,
  tooltip,
} from './chart'
export default {
  name: 'CardItem19',
  props: {
    title: {
      type: String,
      default: '',
    },
    list: {
      type: Array,
      default: () => {
        return []
      },
    },
    echartId: {
      type: String,
      default: '',
    },
  },
  components: {
    VsocChart,
  },
  data() {
    return {}
  },
  computed: {
    option() {
      let xList = this.list[0].arrays.map(v => v.xName)
      let yList1 = this.list[0].arrays.map(v => v.number)
      let yList2 = this.list[1].arrays.map(v => v.number)

      let _this = this
      let colors = []
      if (_this.echartId === 'C057') {
        colors = ['#01eedc', '#0773ff']
      } else {
        colors = ['#2ee56a', '#EE822F']
      }
      return {
        backgroundColor: 'transparent',
        tooltip: {
          ...tooltip(),
          trigger: 'axis',
          axisPointer: { type: 'shadow' },
          borderWidth: 0,
          formatter: function (value) {
            if (_this.echartId === 'C056') {
              const percent1 = _this.list[0].arrays.find(
                v => v.xName === value[0].name,
              )?.percent
              const percent2 = _this.list[1].arrays.find(
                v => v.xName === value[1].name,
              )?.percent
              return `${value[0].marker}  ${value[0].seriesName}：${num(
                value[0].value,
              )} (${percent1}%) <br/> ${value[1].marker}  ${
                value[1].seriesName
              }：${num(value[1].value)} (${percent2}%)`
            }
          },
        },
        legend: {
          ...legend(),
          // ...{
          //   formatter: name => {
          //     return name.slice(0, 1)
          //   },
          // },
        },
        grid: {
          ...grid,
          right: '5%',
          top: '17%',
        },
        xAxis: {
          type: 'category',
          // boundaryGap: true,
          data: xList,
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: {
            ...axisLabel(),
            color: '#fff',
            opacity: 0.7,
          },
          axisPointer: {
            show: true,
            label: {
              formatter: value => {
                // return `${value.value}`
              },
            },
          },
        },
        yAxis: {
          type: 'value',
          splitLine: splitLine,
          axisLabel: {
            ...axisLabel(),
            color: '#fff',
            opacity: 0.7,
            formatter: function (value) {
              return numberToFormat(value)
            },
          },
        },
        series: [
          {
            ...stackSeriesFn(colors[0], yList1, _this.list[0].carName),
            barGap: '180%',
            stack: _this.echartId === 'C057' ? 'total-profit' : '',
            // barWidth: getRoundSize(16),
            label: {
              show: _this.echartId === 'C057' ? false : true,
              ...axisLabel(),
              fontSize: getRoundSize(10),
              color: '#fff',
              opacity: 0.7,
              position: 'top',
              formatter: function (params) {
                return `${numberToFormat(params.value)}`
                // return `${(
                //   (params.value / yList1.reduce((sum, num) => sum + num, 0)) *
                //   100
                // ).toFixed(2)}%`
              },
            },
          },
          {
            ...stackSeriesFn(colors[1], yList2, _this.list[1].carName),
            stack: _this.echartId === 'C057' ? 'total-profit' : '',
            // barWidth: getRoundSize(16),
            label: {
              show: _this.echartId === 'C057' ? false : true,
              ...axisLabel(),
              fontSize: getRoundSize(10),
              color: '#fff',
              opacity: 0.7,
              position: 'top',
              formatter: function (params) {
                return `${numberToFormat(params.value)}`
                // return `${(
                //   (params.value / yList2.reduce((sum, num) => sum + num, 0)) *
                //   100
                // ).toFixed(2)}%`
              },
            },
          },
        ],
      }
    },
  },
}
</script>
