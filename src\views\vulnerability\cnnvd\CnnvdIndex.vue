<template>
  <v-card tile class="main-content">
    <v-card-text class="pa-0">
      <div class="d-flex justify-space-between align-center flex-row-reverse">
        <div class="d-flex align-end">
          <table-search
            :searchList="searchList"
            :searchQuery="query"
            @search="$_search"
          ></table-search>
          <!-- <v-select
            v-model="queryKey"
            small
            color="primary"
            :menu-props="{ auto: true, offsetY: true }"
            append-icon="mdi-chevron-down"
            hide-details
            dense
            outlined
            class="mr-3 select-width"
            :items="searchConditions"
            :label="$t('action.queryConditions')"
          ></v-select>
          <div class="text-width">
            <v-text-field
              v-if="queryKey !== 'vulnerabilityLevelList'"
              v-model="query[queryKey]"
              class="flex-6"
              color="primary"
              outlined
              dense
              hide-details
              :label="$t('action.queryContent')"
            ></v-text-field>
            <v-select
              v-else
              v-model="query[queryKey]"
              clearable
              small
              color="primary"
              :menu-props="{ auto: true, offsetY: true }"
              append-icon="mdi-chevron-down"
              outlined
              dense
              hide-details
              multiple
              :items="cnnvdVulnerabilityLevelEnum"
              :label="$t('action.queryContent')"
            ></v-select>
          </div>
          <v-btn
            color="primary--text bg-btn ml-3"
            elevation="0"
            @click="$_search"
          >
            <span>{{ $t('action.search') }}</span>
          </v-btn> -->
        </div>
      </div>

      <v-data-table
        fixed-header
        :items-per-page="query.pageSize"
        item-key="id"
        :height="tableHeight"
        hide-default-footer
        :headers="headers"
        :items="tableData"
        class="table border-radius-xl mt-3 thead-light"
        :loading="tableLoading"
        @click:row="$_viewDetails"
      >
        <template v-slot:item.id="{ item }">
          <div class="py-2">
            <div>
              <v-chip label x-small class="mb-1" color="secondary">CNNVD</v-chip
              >{{ item.cnnvdId | dataFilter }}
            </div>
            <div>
              <v-chip label x-small text-color="accent">CVE</v-chip
              ><span class="accent--text">{{ item.cveId | dataFilter }}</span>
            </div>
          </div>
        </template>
        <template v-slot:item.cnnvdName="{ item }">
          <div>
            <div
              class="mb-1 text-overflow-hide"
              style="max-width: 25rem"
              v-show-tips
            >
              {{ item.cnnvdName | dataFilter }}
            </div>
            <div>
              <div
                v-if="cnnvdVulnerabilityLevelEnum[item.cnnvdLevel]"
                class="d-flex"
                :style="`color:${
                  cnnvdVulnerabilityLevelEnum[item.cnnvdLevel].color
                }`"
              >
                <vsoc-icon
                  class="mr-1"
                  type="fill"
                  size="middle"
                  icon="icon-loudongdengjibiaozhi"
                ></vsoc-icon>
                <span class="font-weight-medium">{{
                  cnnvdVulnerabilityLevelEnum[item.cnnvdLevel].text
                }}</span>
              </div>
              <span v-else>N/A</span>
            </div>
          </div>
        </template>

        <template v-slot:item.cnnvdType="{ item }">
          <div
            style="max-width: 12rem"
            class="text-overflow-hide"
            v-show-tips
            v-if="vulnerabilityTypeEnum[item.cnnvdType]"
          >
            {{ vulnerabilityTypeEnum[item.cnnvdType].text }}
          </div>
        </template>

        <template v-slot:item.publishedTime="{ item }">
          <div>
            <div class="mb-1">{{ item.publishedTime | dataFilter }}</div>
            <div class="accent--text">
              {{ item.updateTime | dataFilter }}
            </div>
          </div>
        </template>

        <template v-slot:item.createUser="{ item }">
          <div>
            <div class="mb-1">{{ item.createUser | dataFilter }}</div>
            <div class="accent--text">{{ item.createTime }}</div>
          </div>
        </template>

        <template v-slot:item.updateTime="{ item }">
          <div>
            <div class="mb-1">{{ item.updateTime | dataFilter }}</div>
            <div class="accent--text">
              {{ item.dataSourceName | dataFilter }}
            </div>
          </div>
        </template>
      </v-data-table>
      <!-- 分页器 -->
      <vsoc-pagination
        :page.sync="query.pageNum"
        :size.sync="query.pageSize"
        :total="tableDataTotal"
        @change-size="$_search"
        @change-page="getTableData"
      />
    </v-card-text>
  </v-card>
</template>

<script>
import { getCnnvdList } from '@/api/vulnerability'
import TableSearch from '@/components/TableSearch/index.vue'
import VsocPagination from '@/components/VsocPagination.vue'
import VsocDateRange from '@/components/vsoc-date-range/VsocDateRange.vue'

export default {
  name: 'CnnvdIndex',
  props: {
    tableHeight: [String, Number],
  },
  components: {
    VsocPagination,
    VsocDateRange,
    TableSearch,
  },
  data() {
    return {
      range: {
        start: '',
        end: '',
      },

      // 查询内容
      queryKey: 'cnnvdId',
      tableLoading: false,

      // 查询条件下拉选择
      query: {
        cnnvdId: '',
        cnnvdName: '',
        cveId: '',
        publishedTimeStart: '',
        publishedTimeEnd: '',
        cnnvdLevelList: [],
        updateTimeStart: '',
        updateTimeEnd: '',
        createTimeStart: '',
        createTimeEnd: '',
        pageSize: 200,
        pageNum: 1,
        vulnerabilityName: '',
        vulnerabilityLevelList: '',
      },

      tableData: [],
      tableDataTotal: 0,
    }
  },
  computed: {
    searchList() {
      return [
        {
          type: 'multiSearch',
          value: 'vulnerabilityLevelList',
          conditions: [
            {
              type: 'autocomplete',
              value: 'vulnerabilityLevelList',
              text: this.$t('vulnerability.headers.alarmLevel'),
              itemList: this.$store.getters['enums/getCnnvdVulnerabilityLevel'],
            },
            {
              type: 'input',
              value: 'vulnerabilityName',
              text: this.$t('vulnerability.headers.vulnerabilityName'),
            },
            {
              type: 'input',
              value: 'cveId',
              text: this.$t('vulnerability.headers.cveId'),
            },
            {
              type: 'input',
              value: 'cnnvdId',
              text: this.$t('vulnerability.headers.cnnvdId'),
            },
          ],
        },
      ]
    },
    // 查询条件列表
    searchConditions() {
      return [
        {
          text: this.$t('vulnerability.headers.alarmLevel'),
          value: 'vulnerabilityLevelList',
        },
        {
          text: this.$t('vulnerability.headers.vulnerabilityName'),
          value: 'vulnerabilityName',
        },
        {
          text: this.$t('vulnerability.headers.cveId'),
          value: 'cveId',
        },
        {
          text: this.$t('vulnerability.headers.cnnvdId'),
          value: 'cnnvdId',
        },
      ]
    },
    headers() {
      return [
        {
          text: this.$t('vulnerability.headers.idInfo'),
          value: 'id',
          width: 220,
          sortable: false,
        },

        {
          text: this.$t('vulnerability.headers.vulnerInofo'),
          value: 'cnnvdName',
          width: 200,
          sortable: false,
        },

        {
          text: this.$t('vulnerability.cnnvd.updatePublicTime'),
          value: 'publishedTime',
          width: 160,
          sortable: false,
        },
        {
          text: this.$t('vulnerability.headers.entryInfo'),
          value: 'createUser',
          width: 260,
          sortable: false,
        },

        {
          text: this.$t('vulnerability.headers.loopholeType'),
          value: 'cnnvdType',
          width: 140,
          sortable: false,
        },
      ]
    },
    cnnvdVulnerabilityLevelEnum() {
      return this.$store.getters['enums/getCnnvdVulnerabilityLevel']
    },
    vulnerabilityTypeEnum() {
      return Object.assign([], this.$store.state.enums.enums.VulnerabilityType)
    },
  },
  // mounted() {
  //   this.$_search()
  // },
  methods: {
    onReset() {
      Object.assign(this.$data.query, this.$options.data.call(this).query)
      this.$_search()
    },
    $_viewDetails(record) {
      if (this.$route.meta.buttons.includes('cnnvd-detail')) {
        this.$router.push(
          `/bugManagement/cnnvdDetail?cnnvdId=${record.cnnvdId}`,
        )
      }
    },

    $_search() {
      this.query.pageNum = 1
      this.getTableData()
      this.$emit('filter', this.query)
    },

    // 获取表格
    async getTableData() {
      try {
        this.tableLoading = true
        const { data } = await getCnnvdList(this.query)
        this.tableData = data.records
        this.tableDataTotal = data.total
      } catch (e) {
        console.error(`获取漏洞管理：${e}`)
      }
      this.tableLoading = false
    },
  },
}
</script>
<style scoped lang="scss">
.v-chip.v-size--x-small {
  display: inline-block;
  width: 5rem;
  height: 1.8333rem;
  padding: 0;
  margin-right: 4px;
  text-align: center;
}
</style>
