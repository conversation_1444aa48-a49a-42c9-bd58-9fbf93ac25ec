<template>
  <div>
    <bread-crumb></bread-crumb>
    <v-card tile class="main-content">
      <v-card-text class="pa-0">
        <div class="d-flex justify-end align-center mb-3">
          <div class="d-flex align-center">
            <table-search
              :searchList="searchList"
              :searchQuery="query"
              @search="$_search"
            ></table-search>
            <!-- <v-text-field
              v-model="query.reportName"
              color="primary"
              hide-details="auto"
              :label="$t('report.headers.reportName')"
              dense
              outlined
              clearable
              @click:clear="onClear"
              @keyup.enter.native="$_search"
              class="me-3 text-width"
            ></v-text-field>
            <div>
              <v-btn
                class="primary--text bg-btn"
                elevation="0"
                @click="$_search"
              >
                <span>
                  {{ $t('action.search') }}
                </span>
              </v-btn>
            </div> -->
          </div>
        </div>
        <v-data-table
          fixed-header
          :items-per-page="query.pageSize"
          item-key="id"
          :height="tableHeight"
          hide-default-footer
          :headers="headers"
          :items="tableData"
          class="flex-1 thead-light"
          :loading="tableLoading"
        >
          <template v-slot:item.reportType="{ item }">
            <v-icon
              v-if="item.reportType == '2' || item.reportType == '5'"
              size="1.5rem"
            >
              mdi-file-word
            </v-icon>
            <v-icon v-else size="1.5rem"> mdi-file-excel-outline </v-icon>
          </template>

          <template v-slot:item.reportName="{ item }">
            <div v-show-tips style="width: 260px">{{ item.reportName }}</div>
          </template>
          <template v-slot:item.reportTypeName="{ item }">
            <span>{{ item.reportTypeName || 'N/A' }}</span>
          </template>
          <!-- <template v-slot:item.description="{item}">
          <span v-show-tips>{{ item.description }}</span>
        </template>

        <template v-slot:item.state="{item}">
          <v-icon
            v-if="item.state == 1"
            size="1.5rem"
            :color="$activeColor"
          >
            mdi-check-circle
          </v-icon>
          <v-icon
            v-else
            size="1.5rem"
            :color="$inactiveColor"
          >
            mdi-close-circle
          </v-icon>
        </template> -->
          <template v-slot:item.updateDate="{ item }">
            <span v-show-tips>{{ item.updateDate | toDate }}</span>
          </template>
          <template v-slot:item.startDate="{ item }">
            <span v-show-tips>{{ item.startDate | dataFilter }}</span>
          </template>
          <template v-slot:item.endDate="{ item }">
            <span v-show-tips>{{ item.endDate | dataFilter }}</span>
          </template>
          <template v-slot:item.actions="{ item }">
            <!-- <v-btn
              v-show=""
              v-has:reportInstance-download
              icon
              @click.stop="downLoad(item)"
            >
              <v-icon size="1.25rem"> mdi-download </v-icon>
            </v-btn> -->
            <v-btn
              v-has:reportInstance-download
              icon
              @click.stop="downLoad(item)"
            >
              <vsoc-icon
                v-show-tips="$t('action.download')"
                type="fill"
                icon="icon-xiazai"
                class="action-btn"
                size="x-large"
              ></vsoc-icon>
            </v-btn>
            <!-- <v-btn
              v-if="item.reportType == '2' || item.reportType == '5'"
              v-has:reportInstance-download
              icon
              @click.stop="downLoad(item)"
            >
              <vsoc-icon
                v-show-tips="$t('action.export1')"
                type="fill"
                icon="icon-daochu"
                class="action-btn"
                size="x-large"
              ></vsoc-icon>
            </v-btn> -->
            <!-- <v-btn
            :loading="item.btnLoading"
            icon
            @click.stop="del(item)"
          >
            <v-icon
              size="1.25rem"
            >
              mdi-delete
            </v-icon>
          </v-btn> -->
          </template>
        </v-data-table>

        <vsoc-pagination
          :page.sync="query.pageNum"
          :size.sync="query.pageSize"
          :total="tableDataTotal"
          @change-page="getTableData"
          @change-size="$_search"
        >
        </vsoc-pagination>
      </v-card-text>
      <div style="display: none">
        <!-- 本周新增告警事件类型分布占比 -->
        <div id="addAlarmType" style="width: 600px; height: 400px"></div>
        <!-- 告警事件状态分析占比 -->
        <div id="assetStatus" style="width: 600px; height: 400px"></div>
        <!-- 告警严重级别-->
        <div id="alarmLevelTrend" style="width: 600px; height: 400px"></div>
        <!-- 本周新增工单类型分布占比 -->
        <div id="ticketType" style="width: 600px; height: 400px"></div>
        <!-- 工单状态分析占比 -->
        <div id="selectTicketStatus" style="width: 600px; height: 400px"></div>
        <!-- 工单严重级别-->
        <div id="ticketTrend" style="width: 600px; height: 400px"></div>
        <!-- 数据采集情况-->
        <div id="dataProcessing" style="width: 600px; height: 400px"></div>
        <div id="assetRiskTrends" style="width: 600px; height: 400px"></div>
        <!-- 月报 -->
        <!-- 车辆资产态势占比 -->
        <div id="assetSituation" style="width: 600px; height: 400px"></div>
        <!-- 影响车型分析占比 -->
        <div id="influenceModel" style="width: 600px; height: 400px"></div>
        <!-- 告警严重级别占比 -->
        <div id="assetLevelAnalysis" style="width: 600px; height: 400px"></div>
        <!-- 安全告警事件状态分析 -->
        <!-- <div id="assetStatus" style="width: 600px; height: 400px"></div> -->
        <!-- 告警类型占比 -->
        <div
          id="securityEventTypeAnalysis"
          style="width: 600px; height: 400px"
        ></div>
        <!-- 告警事件发生城市分析 -->
        <div
          id="cityWhereDangerousVehiclesAreLocated"
          style="width: 600px; height: 400px"
        ></div>
        <!-- 告警事件趋势 -->
        <div id="tendency" style="width: 600px; height: 400px"></div>
        <!-- 告警严重级别-->
        <!-- <div id="alarmLevelTrend" style="width: 600px; height: 400px"></div> -->
        <!-- 车辆活跃与受攻击情况-->
        <div id="attackAndActivity" style="width: 600px; height: 400px"></div>
        <!-- 新增纳入管控车辆情况-->
        <div id="controlVehicles" style="width: 600px; height: 400px"></div>
      </div>
    </v-card>
  </div>
</template>

<script>
import { getExcelExport, getReport, reportDownload } from '@/api/report/report'
import TableSearch from '@/components/TableSearch/index.vue'
import VsocChart from '@/components/VsocChart'
import VsocPagination from '@/components/VsocPagination.vue'
import BreadCrumb from '@/components/bread-crumb/index.vue'
import {
  alarmStatusColor1,
  alertColor,
  ticketStatusColor,
} from '@/plugins/systemColor'
import { Month_En, PAGESIZE } from '@/util/constant'
import { deepClone, setRemainingHeight } from '@/util/utils'
import docxtemplater from 'docxtemplater'
import { saveAs } from 'file-saver'
import JSZipUtils from 'jszip-utils'
import moment from 'moment'
import PizZip from 'pizzip'
import { empty } from './empty.js'
export default {
  name: 'ReportInstance',
  components: {
    // VsocDrawer,
    VsocPagination,
    VsocChart,
    BreadCrumb,
    TableSearch,
  },
  data() {
    return {
      alarmStatusColor1,
      alertColor,
      ticketStatusColor,
      showEditGroup: false,
      editType: 'add',
      // 查询条件列表
      // 查询条件下拉选择
      search: '',

      tableData: [],
      tableHeight: '34.5rem',
      tableLoading: false,
      btnLoading: false,
      query: {
        pageNum: 1,
        pageSize: PAGESIZE,
        reportName: '',
      },
      filterList: [],
      tableDataTotal: 0,
      valid: false,
      baseUrl: '',
    }
  },
  computed: {
    searchList() {
      return [
        {
          type: 'input',
          value: 'reportName',
          text: this.$t('report.headers.reportName'),
        },
      ]
    },
    headers() {
      return [
        {
          text: '',
          value: 'reportType',
          width: '50px',
        },
        {
          text: this.$t('report.headers.reportName'),
          value: 'reportName',
          width: '260px',
        },
        {
          text: this.$t('report.headers.reportTypeName'),
          value: 'reportTypeName',
          width: '160px',
        },

        {
          text: this.$t('report.headers.startDate'),
          value: 'startDate',
          width: '160px',
        },
        {
          text: this.$t('report.headers.endDate'),
          value: 'endDate',
          width: '160px',
        },
        {
          text: this.$t('report.headers.createDate'),
          value: 'createDate',
          width: '160px',
        },

        {
          text: '',
          value: 'actions',
          width: '60',
          sortable: false,
        },
      ]
    },
    vehicleStatus() {
      return this.$store.state.enums.enums.HealthStatus
    },
    // 获取告警等级颜色
    alertLevel() {
      return this.$store.state.enums.enums.AlarmLevel
    },
  },
  created() {
    this.getLogo()
    this.$_setTableHeight()
    this.$bus.$on('resize', this.$_setTableHeight)
    this.$_search()
  },
  beforeDestroy() {
    this.$bus.$off('resize', this.$_setTableHeight)
  },
  mounted() {},
  methods: {
    onClear() {
      this.query.reportName = ''
      this.$_search()
    },
    async getLogo() {
      let imgsrc = require('@/assets/images/reportLogo.png')
      this.baseUrl = await this.getBase64Sync(imgsrc)
    },
    async getTableData() {
      try {
        this.tableLoading = true
        this.tableData = []
        const res = await getReport(this.query)
        this.tableData = res.data.records
        this.tableDataTotal = res.data.total
      } catch (e) {
        console.error(`获取报告实例：${e}`)
      }
      this.tableLoading = false
    },

    $_search() {
      this.query.pageNum = 1
      this.getTableData()
    },

    $_setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = setRemainingHeight() - 12
      })
    },

    downLoad(item) {
      //月报：0危险资产分析报告1威胁事件分析报告2运营报告
      //周报：3威胁事件分析4响应工单分析5运营报告
      if (['0', '1', '3', '4'].indexOf(item.reportType) !== -1) {
        this.fetchExcel(item)
      } else if (item.reportType === '2' || item.reportType === '5') {
        this.fetchWord(item)
      }
    },
    //excel下载
    fetchExcel(item, typeFunction) {
      const params = { id: item.id }
      getExcelExport(params).then(res => {
        let url = window.URL.createObjectURL(new Blob([res]))
        let link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', item.reportName + '.csv')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      })
    },
    //word下载
    async fetchWord(item) {
      const params = { id: item.id }
      const e = await reportDownload(params)
      if (!(e.data.length && e.data[0].reportData.length)) return
      let data = e.data[0].reportData[0]
      this.wordReport(data, item)
    },
    wordReport(data, item) {
      if (this.$i18n.locale === 'en') {
        data.monthEn = Month_En[data.month]
        data.postureUpdateTime = moment(data.postureUpdateTime).format('LLL')

        data.weekCompletedTickHours =
          data.weekCompletedTickTime.split('小时')[0]
        data.weekCompletedTickMin = data.weekCompletedTickTime
          .split('小时')[1]
          .split('分钟')[0]

        data.twentyFourCompletedHours =
          data.twentyFourCompletedTime.split('小时')[0]
        data.twentyFourCompletedMin = data.twentyFourCompletedTime
          .split('小时')[1]
          .split('分钟')[0]

        data.fortyEightCompletedHours =
          data.fortyEightCompleted.split('小时')[0]
        data.fortyEightCompletedMin = data.fortyEightCompleted
          .split('小时')[1]
          .split('分钟')[0]

        data.seventyTwoCompletedHours =
          data.seventyTwoCompleted.split('小时')[0]
        data.seventyTwoCompletedMin = data.seventyTwoCompleted
          .split('小时')[1]
          .split('分钟')[0]
      }
      //车辆资产态势
      data.assetSituation = data.assetSituation.map(v => {
        return {
          ...v,
          name: v.healthStatusName,
          value: v.count,
          format: v.alarmPercentage,
          itemStyle: { color: this.vehicleStatus[v.healthStatus].color },
        }
      })
      //影响车型分析
      // data.influenceModel = data.influenceModel.map(v => {
      //   return {
      //     ...v,
      //     name: v.vehicleModel,
      //     value: v.sum,
      //     format: v.format,
      //   }
      // })
      data.influenceModel = data.assetActiveStatus?.list.map(v => {
        return {
          ...v,
          format: v.percentage,
          itemStyle: {
            color:
              v.type === '0'
                ? '#2EE56A'
                : v.type === '1'
                ? '#FF910F'
                : '#b4bbcc',
          },
        }
      })
      //告警严重级别
      data.alarmProportion = data.alarmProportion
        .filter(v => v.alarmLevelName)
        .map(v => {
          return {
            ...v,
            name: v.alarmLevelName,
            value: v.count,
            format: v.format,
            itemStyle: { color: this.alertLevel[v.alarmLevel].color },
          }
        })
      //告警类型占比
      data.addAlarmType = data.addAlarmType
        .map(v => {
          return {
            ...v,
            name: v.alarmName,
            value: v.number,
            itemStyle: { color: this.alertColor[v.alarmLevel] },
            format: v.percentage,
          }
        })
        .sort((a, b) => a.value - b.value)
      //告警事件状态分析
      data.addAlarmStatus = data.addAlarmStatus
        .filter(v => v.statusName)
        .map(v => {
          return {
            ...v,
            name: v.statusName,
            value: v.count,
            format: v.format,
            itemStyle: { color: this.alarmStatusColor1[v.status] },
          }
        })
      //告警事件趋势
      let alarmNum = 0
      data.alarmTrend.forEach(v => {
        v.arrays.forEach(item => {
          alarmNum += Number(item.number)
        })
      })
      if (alarmNum === 0) {
        data.alarmTrend = []
      }
      //工单类型
      data.ticketType = data.ticketType
        .map(v => {
          return {
            ...v,
            name: v.ticketType,
            value: v.count,
            itemStyle: { color: '#FF910F' },
          }
        })
        .sort((a, b) => a.value - b.value)
      //工单状态
      data.selectTicketStatus = data.selectTicketStatus.map(v => {
        return {
          ...v,
          name: v.statusName,
          value: v.count,
          format: v.format,
          itemStyle: { color: this.ticketStatusColor[v.status] },
        }
      })
      //工单趋势
      let ticketTrendNum = 0
      data.ticketTrend.forEach(v => {
        v.arrays.forEach(item => {
          ticketTrendNum += Number(item.number)
        })
      })
      if (ticketTrendNum === 0) {
        data.ticketTrend = []
      }
      //占比资产管理数
      data.vehiclePercent =
        (
          (Number(data.onlineCount || 0) / Number(data.vehicleCount || 0)) *
          100
        ).toFixed(2) + '%'
      let parmas = {
        logoImg: this.baseUrl,
        reportType: item.reportType,
        reportName: item.reportName,
        //资产风险趋势
        assetRiskTrendsImg:
          this.initLineChat1(
            '资产风险趋势',
            'assetRiskTrends',
            data.assetRiskTrends,
          ) || empty,
        assetSituationImg:
          this.initCircleChat(
            '车辆资产态势占比',
            'assetSituation',
            data.assetSituation,
          ) || empty,
        influenceModelImg:
          (data.influenceModel &&
            this.initCircleChat(
              '影响车型分析占比',
              'influenceModel',
              data.influenceModel,
            )) ||
          empty,
        assetLevelAnalysisImg:
          this.initCircleChat(
            '告警严重级别占比',
            'assetLevelAnalysis',
            data.alarmProportion,
          ) || empty,
        securityEventTypeAnalysisImg:
          this.initLineType(
            '告警类型占比',
            'securityEventTypeAnalysis',
            data.addAlarmType,
          ) || empty,
        assetStatusImg:
          this.initCircleChat(
            '告警事件状态分析',
            'assetStatus',
            data.addAlarmStatus,
          ) || empty,

        //工单类型
        ticketTypeImg:
          this.initLineChat('', 'ticketType', data.ticketType, '') || empty,
        //工单状态
        selectTicketStatusImg:
          this.initCircleChat(
            '',
            'selectTicketStatus',
            data.selectTicketStatus,
          ) || empty,
        //新增响应工单
        ticketTrendImg:
          this.initBrokenLine('', 'ticketTrend', data.ticketTrend, {
            type: 'bar',
          }) || empty,
        tendencyImg:
          this.initBrokenLine('', 'tendency', data.alarmTrend, {
            type: 'bar',
          }) || empty,
        //数据采集
        dataProcessingImg:
          this.initPeak(
            '',
            'dataProcessing',
            data.dataProcessing,
            item.reportType === '2' ? 'month' : '',
          ) || empty,
        controlVehiclesImg:
          this.initPeak(
            '新增纳入管控车辆',
            'controlVehicles',
            data.controlVehicles,
            item.reportType === '2' ? 'month' : '',
          ) || empty,
      }
      this.$nextTick(() => {
        this.operateReport(Object.assign(data, parmas))
      })
    },
    //环形下载
    initCircleChat(name, id, data) {
      if (data && data.length === 0) return
      var myChart = this.$echarts.init(document.getElementById(id))
      var option = {
        // title: {
        //   text: name,
        //   left: 'center',
        // },
        tooltip: {
          trigger: 'item',
        },
        legend: {
          bottom: '0',
          left: 'center',
          icon: 'circle',
        },
        label: {
          alignTo: 'edge',
          formatter(params) {
            return (
              params.data.name +
              '\n' +
              params.data.value +
              '\n' +
              params.data.format
            )
          },
          minMargin: 10,
          edgeDistance: 10,
          lineHeight: 15,
          rich: {
            time: {
              fontSize: 12,
              color: '#333',
            },
          },
        },
        series: [
          {
            name: name,
            type: 'pie',
            animation: false,
            radius: ['40%', '55%'],
            center: ['50%', '50%'],
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2,
            },
            data: data || [],
          },
        ],
      }
      myChart.setOption(option)
      let img = myChart.getDataURL({
        pixelRatio: 2, // 导出的图片分辨率比例，默认为 1。
        backgroundColor: '#fff', // 导出的图片背景色，默认使用 option 里的 backgroundColor
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
      return img || ''
    },
    //告警类型
    initLineType(name, id, data) {
      if (data.length === 0) return
      var myChart = this.$echarts.init(document.getElementById(id))
      var option = {
        legend: {
          // type: 'plain',
          icon: 'circle',
          // icon: 'none',
          top: '5%',
          right: '5%',
          tooltip: {
            show: true,
          },
        },
        grid: {
          left: 20,
          // right: 10,
          bottom: '5%',
          top: '10%',
          containLabel: true,
        },
        xAxis: {
          type: 'value',
          name: '',
          min: 0,
          data: '',
          axisLabel: {
            formatter: '{value} ',
          },
          splitLine: {
            show: false,
          },
          axisTick: {
            // 轴刻度
            show: true,
          },
          axisLine: {
            show: true,
            //这是y轴文字颜色
            lineStyle: {
              color: '#000',
            },
          },
        },

        yAxis: [
          {
            type: 'category',
            offset: 0,
            axisLabel: {
              width: 180, //将内容的宽度固定
              overflow: 'break', //超出的部分换行
              color: '#000',
              fontSize: 12,
            },
            data: data.map(v => v.name),
            axisPointer: {
              type: 'shadow',
            },
            axisTick: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: '严重',
            data: data,
            type: 'bar',
            animation: false,
            barWidth: 22,
            barMinHeight: 2,
            label: {
              normal: {
                show: true,
                fontSize: 12,
                position: 'right',
                offset: [0, 0, 50, 0],
                formatter(params) {
                  return params.data
                    ? params.data.value + '(' + params.data.format + ')'
                    : ''
                },
                color: '#333',
              },
            },
            itemStyle: {
              normal: {
                color: this.alertColor[0],
              },
            },
          },
          {
            name: '高',
            type: 'bar',
            animation: false,
            itemStyle: {
              normal: {
                color: this.alertColor[1],
              },
            },
          },
          {
            name: '中',
            type: 'bar',
            animation: false,
            itemStyle: {
              normal: {
                color: this.alertColor[2],
              },
            },
          },
          {
            name: '低',
            type: 'bar',
            animation: false,
            itemStyle: {
              normal: {
                color: this.alertColor[3],
              },
            },
          },
        ],
      }
      myChart.setOption(option)
      let img = myChart.getDataURL({
        pixelRatio: 2, // 导出的图片分辨率比例，默认为 1。
        backgroundColor: '#fff', // 导出的图片背景色，默认使用 option 里的 backgroundColor
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
      return img || ''
    },
    //柱状图横向下载
    initLineChat(name, id, data, seriesName) {
      if (data.length === 0) return
      var myChart = this.$echarts.init(document.getElementById(id))
      var option = {
        legend: {
          // type: 'plain',
          // icon: 'circle',
          icon: 'none',
          top: '5%',
          right: '5%',
          tooltip: {
            show: true,
          },
        },
        grid: {
          left: 20,
          // right: 10,
          bottom: '5%',
          top: '10%',
          containLabel: true,
        },
        xAxis: {
          type:
            id === 'cityWhereDangerousVehiclesAreLocated'
              ? 'category'
              : 'value',
          name: '',
          min: 0,
          data:
            id === 'cityWhereDangerousVehiclesAreLocated'
              ? data.map(v => v.name)
              : '',
          axisLabel: {
            formatter: '{value} ',
          },
          splitLine: {
            show: false,
          },
          axisTick: {
            // 轴刻度
            show: true,
          },
          axisLine: {
            show: true,
            //这是y轴文字颜色
            lineStyle: {
              color: '#000',
            },
          },
        },

        yAxis: [
          {
            type:
              id === 'cityWhereDangerousVehiclesAreLocated'
                ? 'value'
                : 'category',
            offset: 0,
            axisLabel: {
              width: 180, //将内容的宽度固定
              overflow: 'break', //超出的部分换行
              color: '#000',
              fontSize: 12,
            },
            data:
              id === 'cityWhereDangerousVehiclesAreLocated'
                ? ''
                : data.map(v => v.name),
            axisPointer: {
              type: 'shadow',
            },
            axisTick: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: seriesName,
            data: data,
            type: 'bar',
            animation: false,
            barWidth: 22,
            barMinHeight: 2,
            label: {
              normal: {
                show: true,
                fontSize: 12,
                position: 'right',
                offset: [0, 0, 50, 0],
                formatter(params) {
                  return params.data
                    ? id === 'securityEventTypeAnalysis'
                      ? params.data.value + '(' + params.data.format + ')'
                      : params.data.value
                    : ''
                },
                color: '#333',
              },
            },
            itemStyle: {
              normal: {
                color: id === 'ticketType' ? '#ee1f1f' : '#ff910f',
              },
            },
          },
        ],
      }
      myChart.setOption(option)
      let img = myChart.getDataURL({
        pixelRatio: 2, // 导出的图片分辨率比例，默认为 1。
        backgroundColor: '#fff', // 导出的图片背景色，默认使用 option 里的 backgroundColor
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
      return img || ''
    },
    //多柱状图
    initLineChat1(name, id, data, seriesName) {
      if (!(data && data.list.length)) return
      const list = deepClone(data.list)
      var myChart = this.$echarts.init(document.getElementById(id))
      var option = {
        legend: {
          right: 10,
          icon: 'circle',
          data: ['活跃资产', '风险资产'],
        },
        grid: {
          left: 10,
          right: 10,
          bottom: '5%',
          top: '10%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          boundaryGap: true,
          axisLabel: {
            interval: 0, //代表显示所有x轴标签显示
          },
          data: list[0].arrays.map(v => v.x),
        },
        yAxis: {
          type: 'value',
        },
        series: [
          {
            data: list.find(v => v.dictId === '0').arrays.map(v => v.y),
            name: '活跃资产',
            label: {
              show: true,
              fontSize: 12,
              position: 'top',
              formatter: '{c}',
              color: '#333',
            },
            animation: false,
            showSymbol: false,
            barWidth: 12,
            barMinHeight: 2,
            type: 'bar',
            itemStyle: {
              color: '#2EE56A',
            },
          },
          {
            data: list.find(v => v.dictId === '1').arrays.map(v => v.y),
            name: '风险资产',
            label: {
              show: true,
              fontSize: 12,
              position: 'top',
              formatter: '{c}',
              color: '#333',
            },
            animation: false,
            showSymbol: false,
            barWidth: 12,
            barMinHeight: 2,
            type: 'bar',
            itemStyle: {
              color: '#FF910F',
            },
          },
        ],
      }
      myChart.setOption(option)
      window.addEventListener('resize', () => {
        myChart.resize()
      })
      let img = myChart.getDataURL({
        pixelRatio: 2, // 导出的图片分辨率比例，默认为 1。
        backgroundColor: '#fff', // 导出的图片背景色，默认使用 option 里的 backgroundColor
      })
      return img || ''
    },
    //多折线图 多柱状图
    initBrokenLine(name, id, data, parmas, reportType) {
      if (data.length === 0) return
      // let allData = []
      // data.forEach(item => {
      //   item.arrays.map(v => {
      //     allData.push({ ...v })
      //   })
      // })
      // let item = {}
      // for (let i = 0; i < allData.length; i++) {
      //   item[allData[i].time] = ~~item[allData[i].time] + allData[i].number
      // }
      var myChart = this.$echarts.init(document.getElementById(id))
      var option = {
        tooltip: {
          trigger: 'axis',
        },
        // '合计'
        legend: {
          right: 10,
          icon: 'circle',
          data: ['严重', '高', '中', '低'],
        },
        grid: {
          left: 10,
          right: 10,
          bottom: '5%',
          top: '10%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          boundaryGap: true,
          axisLabel: {
            interval: 0, //代表显示所有x轴标签显示
          },
          data:
            reportType === 'month'
              ? data[0].arrays.length
                ? data[0].arrays.map(v => v.time.split('-')[2])
                : []
              : data[0].arrays.map(v => v.time),
        },
        yAxis: {
          type: 'value',
        },
        series: [
          {
            name: '严重',
            label: {
              show: parmas.type === 'line' ? false : true,
              fontSize: 12,
              color: '#333',
              formatter: '{c}',
              position: 'top',
            },
            animation: false,
            barWidth: 12,
            barMinHeight: 2,
            type: parmas.type,
            showSymbol: false,
            itemStyle: {
              normal: {
                color: this.alertColor[0],
              },
            },
            data: data.find(v => v.level === '0').arrays.map(v => v.number),
          },
          {
            name: '高',
            label: {
              show: parmas.type === 'line' ? false : true,
              fontSize: 12,
              color: '#333',
              formatter: '{c}',
              position: 'top',
            },
            animation: false,
            showSymbol: false,
            barWidth: 12,
            barMinHeight: 2,
            type: parmas.type,
            itemStyle: {
              normal: {
                color: this.alertColor[1],
              },
            },
            data: data.find(v => v.level === '1').arrays.map(v => v.number),
          },
          {
            name: '中',
            label: {
              show: parmas.type === 'line' ? false : true,
              fontSize: 12,
              color: '#333',
              formatter: '{c}',
              position: 'top',
            },
            animation: false,
            barWidth: 12,
            barMinHeight: 2,
            type: parmas.type,
            showSymbol: false,
            itemStyle: {
              normal: {
                color: this.alertColor[2],
              },
            },
            data: data.find(v => v.level === '2').arrays.map(v => v.number),
          },
          {
            name: '低',
            label: {
              show: parmas.type === 'line' ? false : true,
              fontSize: 12,
              color: '#333',
              formatter: '{c}',
              position: 'top',
            },
            animation: false,
            barWidth: 12,
            barMinHeight: 2,
            type: parmas.type,
            showSymbol: false,
            itemStyle: {
              normal: {
                color: this.alertColor[3],
              },
            },
            data: data.find(v => v.level === '3').arrays.map(v => v.number),
          },
        ],
      }
      myChart.setOption(option)
      window.addEventListener('resize', () => {
        myChart.resize()
      })
      let img = myChart.getDataURL({
        pixelRatio: 2, // 导出的图片分辨率比例，默认为 1。
        backgroundColor: '#fff', // 导出的图片背景色，默认使用 option 里的 backgroundColor
      })
      return img || ''
    },
    //堆叠面积图
    // initAreaChart(name, id, data, parmas) {
    //   if (data.length === 0) return
    //   var myChart = this.$echarts.init(document.getElementById(id))
    //   var option = {
    //     // title: {
    //     //   text: name,
    //     //   left: 'center',
    //     // },
    //     // tooltip: {
    //     //   trigger: 'axis',
    //     // },
    //     legend: {
    //       right: '10%',
    //       icon: 'circle',
    //       data: ['活跃量', '攻击量'],
    //     },
    //     grid: {
    //       left: '5%',
    //       right: '10%',
    //       bottom: '5%',
    //       top: '10%',
    //       containLabel: true,
    //     },
    //     xAxis: {
    //       type: 'category',
    //       boundaryGap: false,
    //       axisLabel: {
    //         interval: 0, //代表显示所有x轴标签显示
    //       },
    //       data: data.map(v => v.time.split('-')[2]),
    //     },
    //     yAxis: {
    //       type: 'value',
    //     },
    //     series: [
    //       {
    //         name: '活跃量',
    //         animation: false,
    //         type: parmas.type,
    //         // stack: parmas.stack,
    //         showSymbol: false,
    //         areaStyle: {
    //           color: '#73be76',
    //           opacity: 0.5,
    //         },
    //         itemStyle: {
    //           normal: {
    //             color: '#73be76',
    //           },
    //         },
    //         data: data.map(v => v.activity),
    //       },
    //       {
    //         name: '攻击量',
    //         animation: false,
    //         type: parmas.type,
    //         // stack: parmas.stack,
    //         showSymbol: false,
    //         areaStyle: {
    //           color: '#bf8127',
    //           opacity: 0.5,
    //         },
    //         itemStyle: {
    //           normal: {
    //             color: '#bf8127',
    //           },
    //         },
    //         data: data.map(v => v.attack),
    //       },
    //     ],
    //   }
    //   myChart.setOption(option)
    //   window.addEventListener('resize', () => {
    //     myChart.resize()
    //   })
    //   let img = myChart.getDataURL({
    //     pixelRatio: 2, // 导出的图片分辨率比例，默认为 1。
    //     backgroundColor: '#fff', // 导出的图片背景色，默认使用 option 里的 backgroundColor
    //   })
    //   return img || ''
    // },
    //尖峰图
    initPeak(name, id, data, reportType) {
      if (data.length === 0) return
      var myChart = this.$echarts.init(document.getElementById(id))
      var option = {
        // title: {
        //   text: name,
        //   left: 'center',
        // },
        grid: {
          left: 10,
          right: 10,
          bottom: '5%',
          top: '10%',
          containLabel: true,
        },
        xAxis: {
          data:
            reportType === 'month'
              ? data.map(v => v.time.split('-')[2])
              : data.map(v => v.time),
          triggerEvent: true,
          axisTick: {
            // 刻度线
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: true,
            rotate: 0,
            interval: 0,
            // textStyle: {
            //   color: '#000',
            //   fontSize: 15,
            // },
          },
        },
        yAxis: {},
        series: [
          {
            name: '',
            animation: false,
            type: 'pictorialBar',
            barCategoryGap: '-80%' /*多个并排柱子设置柱子之间的间距*/,
            barMinHeight: 2,
            symbol:
              'path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z',
            itemStyle: {
              borderColor: '#5f0bd0',
              borderWidth: 1,
              // 渐变色
              color: this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(95, 11, 208, 0.3)',
                },
                {
                  offset: 1,
                  color: 'rgba(95, 11, 208,0.3)',
                },
              ]),
            },
            label: {
              // 数据上方显示数值
              show: true,
              position: 'top',
              textStyle: {
                color: '#333',
                fontSize: 12,
              },
              formatter: '{c}',
            },
            data: data.map(v => v.number),
          },
        ],
      }
      myChart.setOption(option)
      window.addEventListener('resize', () => {
        myChart.resize()
      })
      let img = myChart.getDataURL({
        pixelRatio: 2, // 导出的图片分辨率比例，默认为 1。
        backgroundColor: '#fff', // 导出的图片背景色，默认使用 option 里的 backgroundColor
      })
      return img || ''
    },

    // 点击导出word
    operateReport(parmas) {
      //这里要引入处理图片的插件，下载docxtemplater后，引入的就在其中了
      var ImageModule = require('docxtemplater-image-module-free')
      var fs = require('fs')

      let _this = this
      let reportWord = ''
      let reportName = ''
      // 2为月报
      if (this.$i18n.locale === 'en') {
        if (parmas.reportType === '2') {
          reportWord = 'enMonth1.docx'
          reportName = `Operation_Monthly_Report_${parmas.year}${parmas.month}`
        } else {
          reportWord = 'enWeek1.docx'
          reportName = `Operation_Weekly_Report_W${parmas.week}_${parmas.year}`
        }
      } else {
        reportName = parmas.reportName
        reportWord = parmas.reportType === '2' ? 'month1.docx' : 'week1.docx'
      }
      // 读取并获得模板文件的二进制内容，放在项目中即可
      JSZipUtils.getBinaryContent(
        // parmas.reportType === '2' ? 'month1.docx' : 'week.docx',
        reportWord,
        function (error, content) {
          if (error) {
            throw error
          }

          // 图片处理
          let opts = {}
          opts = { centered: false }
          opts.getImage = chartId => {
            return _this.base64DataURLToArrayBuffer(chartId)
          }
          opts.getSize = function (img, tagValue, tagName) {
            if (empty.includes(tagValue)) {
              return [220, 160]
            } else {
              return [500, 328]
            }
            //自定义指定图像大小，此处可动态调试各别图片的大小
            // if (tagName === 'logoImg') return [358, 57]
            // return [220, 160]
          }

          // 创建一个PizZip实例，内容为模板的内容
          let zip = new PizZip(content)
          // 创建并加载docxtemplater实例对象
          let doc = new docxtemplater()
          doc.attachModule(new ImageModule(opts))
          doc.loadZip(zip)

          // 设置模板变量的值
          doc.setData({
            ...parmas,
          })

          try {
            // 用模板变量的值替换所有模板变量
            doc.render()
          } catch (error) {
            // 抛出异常
            let e = {
              message: error.message,
              name: error.name,
              stack: error.stack,
              properties: error.properties,
            }
            console.log(JSON.stringify({ error: e }))
            throw error
          }

          // 生成一个代表docxtemplater对象的zip文件（不是一个真实的文件，而是在内存中的表示）
          let out = doc.getZip().generate({
            type: 'blob',
            mimeType:
              'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          })
          // 将目标文件对象保存为目标类型的文件，并命名
          saveAs(out, `${reportName}.docx`)
        },
      )
    },

    // 导出echarts图片，格式转换，官方自带，不需要修改
    base64DataURLToArrayBuffer(dataURL) {
      const base64Regex = /^data:image\/(png|jpg|svg|svg\+xml);base64,/
      if (!base64Regex.test(dataURL)) {
        return false
      }
      const stringBase64 = dataURL.replace(base64Regex, '')
      let binaryString
      if (typeof window !== 'undefined') {
        binaryString = window.atob(stringBase64)
      } else {
        binaryString = new Buffer(stringBase64, 'base64').toString('binary')
      }
      const len = binaryString.length
      const bytes = new Uint8Array(len)
      for (let i = 0; i < len; i++) {
        const ascii = binaryString.charCodeAt(i)
        bytes[i] = ascii
      }
      return bytes.buffer
    },
    getBase64Sync(imgUrl) {
      return new Promise(resolve => {
        var xhr = new XMLHttpRequest()
        xhr.open('get', imgUrl, true)
        // 至关重要
        xhr.responseType = 'blob'
        xhr.onload = function () {
          if (this.status == 200) {
            //得到一个blob对象
            var blob = this.response
            // 至关重要
            let oFileReader = new FileReader()
            oFileReader.onloadend = function (e) {
              // 此处拿到的已经是 base64的图片了
              let base64 = e.target.result
              let image = new Image()
              image.src = base64
              let canvas = document.createElement('canvas')
              canvas.width = image.width
              canvas.height = image.height
              let context = canvas.getContext('2d')
              context.drawImage(image, 0, 0, image.width, image.height)
              //返回
              resolve(base64)
            }
            oFileReader.readAsDataURL(blob)
          } else {
            console.log(imgUrl)
          }
        }
        xhr.send()
      })
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map(v => {
        return filterVal.map(j => {
          return v[j]
        })
      })
    },
  },
}
</script>
