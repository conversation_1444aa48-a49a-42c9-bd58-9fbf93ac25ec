<template>
  <v-card class="h-full">
    <v-card-text class="h-full d-flex align-center">
      <div class="w-25 h-full d-flex flex-column">
        <div class="text-xxl font-weight-semibold text--primary">
          {{ $t('posture.serviceAssets') }}
        </div>
        <!-- <div class="text-overflow-hide text-base">共四类</div> -->
      </div>

      <v-row class="w-75">
        <v-col
          v-for="data in statisticsData"
          :key="data.title"
          cols="12"
          md="4"
          class="d-flex flex-column cursor-pointer"
          style="z-index: 9"
          @click="goAsset(data)"
        >
          <div class="d-flex align-center">
            <v-icon
              color="primary"
              class="iconfont"
              :size="20 | getRoundSize"
              >{{ data.icon }}</v-icon
            >
            <span class="ms-2 reverse--text text-ml text-xl-h5">
              {{ data.title }}
            </span>
          </div>
          <div class="color-base mt-1 mt-xl-2 d-flex">
            <count-to
              class="text-h4 text-xl-h3 font-weight-medium"
              :startVal="0"
              :endVal="data.total"
            ></count-to>
          </div>
        </v-col>
      </v-row>
    </v-card-text>
  </v-card>
</template>

<script>
import CountTo from 'vue-count-to'
// eslint-disable-next-line object-curly-newline
import router from '@/router'
import { toDate } from '@/util/filters'
import {
  mdiAccountOutline,
  mdiCurrencyUsd,
  mdiDotsVertical,
  mdiLabelOutline,
  mdiTrendingUp,
} from '@mdi/js'
import { computed, ref } from '@vue/composition-api'
import { endOfDay, startOfDay, subDays } from 'date-fns'

export default {
  components: { CountTo },
  props: {
    list: Array,
  },
  setup(props) {
    const growthEnum = ref({
      up: {
        color: '#DA1F1F',
        icon: 'iconfont icon-shangsheng',
        value: 'up',
      },
      down: {
        color: '#21D95D',
        icon: 'iconfont icon-xiajiang',
        value: 'down',
      },
      zero: {
        color: '#A1A6B1',
        icon: '--',
        value: 'zero',
      },
    })
    const statisticsData = computed(() => {
      if (props.list.length === 0) {
        return []
      }

      // 检查是否是新的车企统计数据格式
      if (
        props.list[0] &&
        props.list[0].assetTypeName &&
        (props.list[0].assetTypeName === 'AutomakerTotal' ||
          props.list[0].assetTypeName === 'SupervisedAutomaker' ||
          props.list[0].assetTypeName === 'RiskAutomaker')
      ) {
        // 新的车企统计数据格式，直接返回
        return props.list.map(item => ({
          title: item.title,
          icon: item.icon,
          total: item.total,
          percent: item.percent,
          status:
            item.percent && Number(item.percent.replace('%', '')) > 0
              ? 'up'
              : 'zero',
        }))
      }

      // 原有的资产统计数据格式（保持兼容性）
      let vehicles = props.list.find(
        v => v.assetTypeName && v.assetTypeName.includes('Vehicle'),
      )
      let vehiclesItem = {
        title: 'Vehicles',
        icon: 'icon-Vehicles',
        total: vehicles?.count,
      }

      let online = props.list.find(
        v => v.assetTypeName && v.assetTypeName.includes('Online'),
      )
      let onlineItem = {
        title: 'Online',
        icon: 'icon-zaixiancheliang',
        total: online?.count,
        percent:
          isNaN(online?.percent) || Number(online?.percent).toFixed(0) + '%',
        status:
          (Number(online?.percent) == 0 && 'zero') ||
          (Number(online?.percent) > 0 ? 'up' : 'down'),
      }

      let critical = props.list.find(
        v => v.assetTypeName && v.assetTypeName.includes('Critical'),
      )
      let criticalItem = {
        title: 'Critical',
        icon: 'icon-weixiancheliang',
        total: critical?.count,
        percent:
          isNaN(critical?.percent) ||
          Number(critical?.percent).toFixed(0) + '%',
        status:
          (Number(critical?.percent) == 0 && 'zero') ||
          (Number(critical?.percent) > 0 ? 'up' : 'down'),
      }

      return [vehiclesItem, onlineItem, criticalItem]
    })
    const goAsset = data => {
      // 处理新的车企统计数据跳转
      if (data.title === '车企总数') {
        router.push({
          path: '/automaker',
          query: { isQuery: 1 },
        })
      } else if (data.title === '已监管车企') {
        router.push({
          path: '/automaker',
          query: {
            is_supervise: '0',
            isQuery: 1,
          },
        })
      } else if (data.title === '风险车企') {
        router.push({
          path: '/automaker',
          query: {
            has_risk: '1',
            isQuery: 1,
          },
        })
      }
      // 处理原有的资产统计数据跳转（保持兼容性）
      else if (data.title === 'Critical') {
        router.push({
          path: '/automaker',
        })
      } else if (data.title === 'Online') {
        router.push({
          path: '/automaker',
        })
      } else {
        router.push({
          path: '/automaker',
        })
      }
    }
    return {
      statisticsData,
      growthEnum,
      goAsset,
      // icons
      icons: {
        mdiDotsVertical,
        mdiTrendingUp,
        mdiAccountOutline,
        mdiLabelOutline,
        mdiCurrencyUsd,
      },
    }
  },
}
</script>

<style lang="scss" scoped>
@import '../../../styles/mixins/_background.scss';
.v-card {
  @include bg-opacity('../../../assets/images/pages/card-bg2.png', 0.08);
}
</style>
