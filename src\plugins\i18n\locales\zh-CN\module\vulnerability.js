const vulnerability = {
  currentTitle: '漏洞',
  headers: {
    cnnvdId: 'CNNVD编号',
    cnvdId: 'CNVD编号',
    cveId: 'CVE编号',
    cvss: 'CVSS评分',
    vulnerabilityName: '漏洞名称',
    alarmLevel: '漏洞等级',
    loopholeType: '漏洞类型',
    recordTime: '报送时间',
    publicTime: '公开时间',
    updateTime: '更新日期',
    createTime: '同步时间',
    dataSource: '漏洞来源',
    entryInfo: '同步信息',
    idInfo: '编号信息',
    vulnerInofo: '漏洞信息',
    collectionTime: '报送公开时间',
    updateInfo: '更新信息',
    findUser: '发现者',
    findDate: '发现日期',
    vulnerId: '漏洞编号',
    affectManufacturers: '影响厂商',
  },
  vulnerabilityDate: '漏洞时间',
  tab2: '实验室漏洞',
  template: {
    hint: '下载漏洞导入模板',
    text: '漏洞导入模板',
  },
  edit: {
    title2: '影响产品',
    title3: '漏洞详情',
    title4: '新增漏洞',
    title5: '编辑漏洞',
    title6: '实验室漏洞详情',
    hint: 'eg: xxxx网站/xxxx系统/xxxx平台/xxxx',
    url: '漏洞URL',
    desc: '漏洞描述',
    solution: '解决方案',
    file: '漏洞附件',
  },
  tip: '漏洞影响的组件及版本号',
  repair: '修复方案',
  cvssInfo: 'CVSS信息',
  cvssHint: '请参考CVE的评分',
  link: '参考链接',
  patch: '补丁信息',
  cveLink: 'CVE链接',
  swal: {
    del: {
      title: '删除实验室漏洞',
      text: '是否确认删除实验室漏洞：{0}？',
    },
  },

  cve: {
    headers: {
      desc: '描述',
      v3: 'V3.x评分',
      v2: 'V2.x评分',
      publishedDate: 'NVD发布日期',
    },
    drawer: {
      cweId: 'CWE编号',
      isLink: '包含超链接',
      version: '评分版本',
      range: '评分范围',
      lastUpdateDate: 'NVD最后修订日期',
    },
    detail: {
      state: '漏洞状态',
      metric: '严重性评分',
      affectedRange: '影响范围',
      product: '产品',
      vendor: '厂商',
      version: '版本',
      type: '脆弱性类型',
      hint: '影响厂商/产品以及版本',
    },
  },

  cnnvd: {
    updateDate: '修改时间',
    publicDate: '公开时间',
    updatePublicTime: '修改公开时间',
  },

  cavd: {
    publicTime: '公开时间',
    info: 'CAVD信息',
    id: 'CAVD编号',
    publicDate: '公开日期',
    repair: '修复建议',
    verify: '验证信息',
    synchronizer: '同步者',
  },
}

export default vulnerability
