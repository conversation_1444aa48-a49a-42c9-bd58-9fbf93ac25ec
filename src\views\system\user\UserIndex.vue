<!-- 用户管理 -->
<template>
  <div>
    <bread-crumb></bread-crumb>
    <v-card tile class="main-content">
      <v-card-text class="pa-0">
        <div class="d-flex justify-space-between align-center">
          <div class="d-flex justify-end align-center">
            <!-- <v-btn
            elevation="0"
            height="2.3rem"
            class=" bg-gradient-primary  flex-1  px-6   shadow-0 font-weight-semibold "
            color="primary"
            @click="add()"
          >
            新增用户
          </v-btn> -->

            <v-btn v-has:user-add elevation="0" color="primary" @click="add">
              <!-- <v-icon>mdi-plus</v-icon> -->
              <span>
                {{ $generateMenuTitle($route.meta.buttonInfo['user-add']) }}
              </span>
            </v-btn>
          </div>
          <div class="d-flex align-center">
            <table-search
              :searchList="searchList"
              :searchQuery="query"
              @search="changeSize"
            ></table-search>
            <!-- <v-row dense class="header pa-0 ma-0">
              <v-col class="pa-0 ma-0">
                <v-text-field
                  v-model="query.userId"
                  color="primary"
                  hide-details="auto"
                  :label="$t('user.headers.fullName')"
                  dense
                  outlined
                  class="me-3 text-width"
                  @keyup.enter.native="$_getTableData"
                ></v-text-field>
              </v-col>
              <v-col class="pa-0 ma-0">
                <el-tree
                  class="me-3"
                  :labelText="$t('user.headers.organize')"
                  ref="elTree"
                  :organizationFlag="true"
                  :showCheckbox="true"
                  :outlinedFlag="true"
                  :expandFlag="true"
                  :treeData="menuList"
                  :default-props="defaultProps"
                  @change="changeTree"
                  :bottom="true"
                  width="280"
                />
              </v-col>
              <v-col class="pa-0 ma-0">
                <v-select
                  v-model="query.roleId"
                  :items="roleList"
                  :multiple="false"
                  outlined
                  dense
                  :label="$t('user.headers.role')"
                  clearable
                  item-value="roleId"
                  item-text="roleName"
                  class="me-3 text-width"
                  @change="onChangeRole"
                  :menu-props="{ offsetY: true }"
                >
                </v-select>
              </v-col>
              <div>
                <v-btn
                  class="primary--text bg-btn"
                  @click="$_getTableData"
                  elevation="0"
                >
                  <span>{{ $t('action.search') }}</span>
                </v-btn>
              </div>
            </v-row> -->
          </div>
        </div>
        <!-- :search="searchKey" -->
        <!-- loading-text="查询中... 请等待" -->
        <v-data-table
          fixed-header
          :items-per-page="query.pageSize"
          item-key="code"
          :height="tableHeight"
          hide-default-footer
          :headers="headers"
          :items="tableData"
          :loader-height="6"
          class="table border-radius-xl mt-3 thead-light"
          :loading="tableLoading"
        >
          <template v-slot:item.username="{ item }">
            <v-icon size="1.75rem" color="black"> mdi-account-circle </v-icon>
            <span class="ml-2">{{ item.username }}</span>
          </template>

          <template v-slot:item.departmentName="{ item }">
            {{ item.departmentName | dataFilter }}
            <!-- <v-chip-group
              v-if="item.departments && item.departments.length"
              column
              active-class="text-primary"
            >
              <v-chip v-for="depart in item.departments" :key="depart.id" small>
                {{ depart.departmentName }}
              </v-chip>
            </v-chip-group>
            <span v-else>N/A</span> -->
          </template>

          <template v-slot:item.roleName="{ item }">
            <div class="text-no-wrap">{{ item.roleName | dataFilter }}</div>
            <!-- <v-chip-group
              v-if="item.roles && item.roles.length"
              column
              active-class="text-primary"
            >
              <v-chip v-for="role in item.roles" :key="role.roleId" small>
                {{ role.roleName }}
              </v-chip>
            </v-chip-group>
            <span v-else>N/A</span> -->
          </template>

          <template v-slot:item.email="{ item }">
            <span v-show-tips>{{ item.email }}</span>
          </template>

          <template v-slot:item.lastLoginDate="{ item }">
            <span v-if="item.lastLoginDate" v-show-tips>{{
              item.lastLoginDate | toDate
            }}</span>
            <span v-else>N/A</span>
          </template>

          <template v-slot:item.status="{ item }">
            <!-- <v-icon
              v-if="item.status === userStatus[item.status].key"
              v-show-tips="userStatus[item.status].value"
              size="1.5rem"
              :color="$activeColor"
            >
              mdi-checkbox-marked-circle
            </v-icon>
            <v-icon
              v-else
              v-show-tips="userStatus[item.status].value"
              size="1.5rem"
              :color="$inactiveColor"
            >
              mdi-close-circle
            </v-icon> -->
            <div v-if="userStatus[item.status].key == item.status">
              <!-- <v-badge
                dot
                inline
                offset-x="10"
                :offset-y="-18"
                :color="userStatus[item.status].color"
                class="mr-1"
              ></v-badge> -->
              <v-icon class="ml-n1 mr-1" :color="userStatus[item.status].color"
                >mdi-circle-medium</v-icon
              >
              <span>{{ userStatus[item.status].value }}</span>
            </div>
            <div v-else>N/A</div>
          </template>

          <template v-slot:item.level="{ item }">
            <span v-if="userLevelEnum[item.level]">
              {{ userLevelEnum[item.level].text | dataFilter }}
            </span>
          </template>

          <template v-slot:item.actions="{ item }">
            <v-btn v-has:user-unlock icon v-if="item.status == 3">
              <vsoc-icon
                v-show-tips="
                  $generateMenuTitle($route.meta.buttonInfo['user-unlock'])
                "
                icon="mdi-lock-open-outline"
                class="action-btn"
                size="x-large"
                @click="onLock(item)"
              >
              </vsoc-icon>
            </v-btn>
            <v-btn v-has:user-edit icon @click="edit(item)">
              <vsoc-icon
                v-show-tips="
                  $generateMenuTitle($route.meta.buttonInfo['user-edit'])
                "
                type="fill"
                icon="icon-bianji"
                class="action-btn"
                size="x-large"
              ></vsoc-icon>
            </v-btn>
            <v-btn
              v-has:user-del
              icon
              v-if="item.status != 0"
              @click="del(item)"
            >
              <vsoc-icon
                v-show-tips="
                  $generateMenuTitle($route.meta.buttonInfo['user-del'])
                "
                type="fill"
                class="action-btn"
                icon="icon-shanchu"
                size="x-large"
              ></vsoc-icon>
            </v-btn>
          </template>
        </v-data-table>

        <!-- 分页器 -->
        <vsoc-pagination
          :page.sync="query.pageNum"
          :size.sync="query.pageSize"
          :total="tableDataTotal"
          @change-page="changePage"
          @change-size="changeSize"
        >
        </vsoc-pagination>
      </v-card-text>

      <vsoc-drawer
        ref="drawer"
        v-model="drawer"
        temporary
        :title="ops === 'edit' ? $t('user.btn2') : $t('user.btn1')"
        @click:confirm="onConfirm"
        @click:cancel="onCancel"
        @click:close="onCancel"
      >
        <!-- @click:confirm="save" -->
        <v-form ref="form" v-model="valid" dark lazy-validation>
          <!-- v-row会有负边距，这里加个上边距抵消 -->
          <div class="mt-4">
            <v-row class="mb-2 pl-4">
              <span class="text-base font-weight-medium mb-4 color-base">
                {{ $t('global.drawer.baseInfo') }}
              </span>
            </v-row>
            <v-row class="px-4">
              <v-text-field
                v-model="userEt.userId"
                :rules="ops !== 'edit' ? nameRules : []"
                :disabled="ops === 'edit'"
                :label="$t('user.headers.name')"
                placeholder="eg. zhangsan"
                required
                class="is-required"
              >
                <!-- prepend-inner-icon="mdi-account" -->
              </v-text-field>
            </v-row>
            <v-row class="px-4">
              <v-text-field
                v-model="userEt.userName"
                :rules="fullnameRules"
                :label="$t('user.headers.fullName')"
                :placeholder="$t('user.hint.fullName')"
                required
                class="is-required"
              >
                <!-- prepend-inner-icon="mdi-account" -->
              </v-text-field>
            </v-row>
            <v-row
              class="px-4"
              v-if="$store.state.appConfig.isThemeAndTimeZone"
            >
              <v-autocomplete
                v-model="userEt.timezoneId"
                :items="timeZoneList"
                :label="$t('user.timezone')"
                :multiple="false"
                class="mt-2"
                :menu-props="{ offsetY: true }"
              >
              </v-autocomplete>
            </v-row>
            <v-row
              class="px-4"
              v-if="
                $store.state.appConfig.isThemeAndTimeZone && userFlag === 'true'
              "
            >
              <!-- <v-btn-toggle class="toggle-btn" v-model="userEt.level">
                <v-btn
                  v-for="(item, index) in userLevelEnum"
                  :value="item.value"
                  :key="index"
                  elevation="0"
                >
                  {{ item.text }}
                </v-btn>
              </v-btn-toggle> -->
              <v-select
                v-if="userEt.level"
                v-model="userEt.level"
                :items="userLevelEnum"
                :label="$t('user.headers.level')"
                :multiple="false"
                item-value="value"
                item-text="text"
                class="mt-2"
                :menu-props="{ offsetY: true }"
              >
                <!-- <template v-slot:message="{ index }">
                  <span>{{ userLevelEnum[userEt.level].text }}{{ index }}</span>
                </template> -->
              </v-select>
            </v-row>
            <v-row class="px-4">
              <v-text-field
                v-model="userEt.email"
                :rules="emailRules"
                :label="$t('user.headers.email')"
                placeholder="eg. <EMAIL>"
                required
                class="is-required"
              >
                <!-- prepend-inner-icon="mdi-email" -->
              </v-text-field>
            </v-row>

            <v-row class="px-4">
              <v-text-field
                v-model="userEt.phone"
                :label="$t('user.drawer.phone')"
                placeholder="eg. 13887020292"
              >
                <!-- prepend-inner-icon="mdi-phone" -->
              </v-text-field>
            </v-row>

            <!-- v-if="!ops" -->
            <template>
              <!-- <v-row class="mb-2 px-4">
                <v-divider></v-divider>
              </v-row> -->
              <v-row class="my-4 pl-4">
                <span class="text-base font-weight-medium mb-0 color-base">
                  {{ $t('user.drawer.setPassword') }}
                </span>
              </v-row>
              <v-row class="px-4">
                <v-text-field
                  v-model="userEt.password"
                  :rules="
                    ops === 'edit'
                      ? []
                      : [passwordRules.required, passwordRules.min]
                  "
                  :append-icon="showPassword ? 'mdi-eye' : 'mdi-eye-off'"
                  :type="passText"
                  :label="$t('user.drawer.password')"
                  required
                  class="is-required"
                  @click:append="changePassword"
                >
                </v-text-field>
                <!-- prepend-inner-icon="mdi-lock" -->
              </v-row>
              <v-row class="px-4">
                <v-text-field
                  v-model="userEt.rePassword"
                  :label="$t('user.drawer.confirmPassword')"
                  :rules="
                    ops === 'edit'
                      ? []
                      : [
                          passwordRules.required,
                          passwordRules.min,
                          passwordRules.confirmedValidator,
                        ]
                  "
                  :append-icon="showPassword ? 'mdi-eye' : 'mdi-eye-off'"
                  :type="passText"
                  required
                  class="is-required"
                  @click:append="changePassword"
                >
                  <!-- prepend-inner-icon="mdi-lock" -->
                </v-text-field>
              </v-row>
            </template>
            <!-- 20221122:已确定需求，角色单选，用户组多选 -->
            <!-- <v-row class="mb-2 px-4">
              <v-divider></v-divider>
            </v-row> -->
            <!-- <v-row class="mb-2 pl-6">
              <span class="text-lg font-weight-semibold mb-0"> 选择组织 </span>
            </v-row> -->
            <v-row class="px-4">
              <el-tree
                ref="elTree1"
                class="mt-4"
                :outlinedFlag="false"
                :labelText="$t('user.headers.organize')"
                :organizationFlag="true"
                :showCheckbox="true"
                :expandFlag="true"
                :treeData="menuList"
                :default-props="defaultProps"
                @change="changeTree1"
                :top="true"
              />
              <!-- @input="setMenu" -->
              <!-- <v-menu
                bottom
                nudge-top="20"
                :close-on-content-click="false"
                content-class="bg-white mx-2"
                transition="slide-y-transition"
                offset-y
                offset-x
                auto
              >
                <template v-slot:activator="{ on }">
                  <v-text-field
                    v-model="userEt.departmentName"
                    label="组织架构"

                    required
                    color="primary"
                    v-on="on"
                  >
                  </v-text-field>
                </template>

                <v-treeview
                  v-model="treeSelect"
                  color="primary"
                  item-key="id"
                  item-text="name"
                  item-disabled="disabled"
                  selectable
                  return-object
                  :items="menuList"
                  selection-type="independent"
                  @input="treeInput"
                  open-all
                >
                  <template v-slot:label="{ item }">
                    <v-icon
                      size="1rem"
                      v-text="
                        item.type == '0'
                          ? 'mdi-home-city-outline'
                          : 'mdi-graph-outline'
                      "
                      :color="item.type == '0' ? '#06309B' : '#ec9eaa'"
                    />
                    <span v-if="item.state === '0'">
                      {{ item.name }}
                    </span>
                    <span v-else style="color: #cccccc">
                      {{ item.name }}
                    </span>
                  </template>
                </v-treeview>
              </v-menu> -->
            </v-row>
            <!-- <v-row class="mb-2 pl-6">
              <span class="text-lg font-weight-semibold mb-0"> 选择角色 </span>
            </v-row> -->

            <v-row class="px-4">
              <!-- v-model="userEt.roleId" -->
              <v-select
                v-model="userEt.roleId"
                :items="roleList"
                :label="$t('user.headers.role')"
                :multiple="false"
                item-value="roleId"
                item-text="roleName"
                item-disabled="disabled"
                @change="onChangeRole"
                class="mt-2"
                :menu-props="{ offsetY: true }"
              >
                <!-- <template v-slot:selection="{ item }">
                  <v-chip label color="primary" class="py-1 px-2 my-0">
                    <span class="text-caption ls-0">{{ item.roleName }}</span>
                  </v-chip>
                </template> -->
              </v-select>
            </v-row>

            <!-- <v-row class="mb-2 pl-6">
              <span class="text-lg font-weight-semibold mb-0">
                选择用户组
              </span>
            </v-row> -->
            <!-- <v-row class="px-4">
              <v-select
                v-model="userEt.groupId"
                :items="userGroupList"
                label="用户组"
                multiple
                item-value="groupId"
                item-text="groupName"
                class="text-base"
              >
                <template v-slot:selection="{ item }">
                  <v-chip label color="primary" class="py-1 px-2 my-0">
                    <span class="text-caption ls-0">{{ item.groupName }}</span>
                  </v-chip>
                </template>
              </v-select>
            </v-row> -->
          </div>
        </v-form>
      </vsoc-drawer>
    </v-card>
  </div>
</template>

<script>
import {
  confirmedValidator,
  emailValidator,
  min,
  required,
} from '@/@core/utils/validation'
import { querySysGroupList } from '@/api/system/organization'
import {
  addUser,
  delUser,
  findUsers,
  unlockUser,
  updateUser,
  userRole,
} from '@/api/system/user'
import TableSearch from '@/components/TableSearch/index.vue'
import VsocDrawer from '@/components/VsocDrawer.vue'
import VsocPagination from '@/components/VsocPagination.vue'
import breadCrumb from '@/components/bread-crumb/index'
import elTree from '@/components/search-tree/index'
import { PAGESIZE, PAGESIZE_MAX } from '@/util/constant'
import { userStatus } from '@/util/enum'
import { deepClone } from '@/util/throttle'
import { getTimeZoneList } from '@/util/timezone'
import { formatDate, setRemainingHeight } from '@/util/utils'
import lodash from 'lodash'
import md5 from 'md5'

export default {
  name: 'UserIndex',
  components: {
    VsocPagination,
    VsocDrawer,
    breadCrumb,
    elTree,
    TableSearch,
  },
  data() {
    return {
      defaultProps: {
        children: 'children',
        label: 'name',
      },
      passText: 'text',

      menuList: [],
      // 查询内容
      searchKey: '',
      filterList: [],

      // 分页参数
      query: {
        pageNum: 1,
        pageSize: PAGESIZE,
        roleId: '',
        userId: '',
        id: '',
      },
      tableDataTotal: 10,
      tableData: [],
      tableHeight: '34.5rem',
      tableLoading: false,
      selectedArray: [],

      showPassword: false,
      ops: 'new',
      drawer: false,
      userEt: this.initUserData(),
      checkNode: {},
      // 角色列表
      roleList: [],

      // // 用户组列表
      // userGroupList: [],

      valid: true,
      nameRules: [
        v => required(v, this.$t('user.headers.name')),
        v =>
          /[a-zA-Z0-9]{1,50}/g.test(v) ||
          this.$t('validation.wrongFormat', [this.$t('user.headers.name')]),
        v => min(v, this.$t('user.headers.name'), 6),
      ],
      fullnameRules: [v => required(v, this.$t('user.headers.fullName'))],
      emailRules: [
        v => required(v, this.$t('user.headers.email')),
        v => emailValidator(v),
      ],
      passwordRules: {
        required: v => required(v, this.$t('user.drawer.password')),
        min: v => min(v, [this.$t('user.drawer.password')]),
        confirmedValidator: v => confirmedValidator(v, this.userEt.password),
      },
    }
  },
  computed: {
    userFlag() {
      return (
        this.$store.state.enums.configs?.['User_Level']?.['User_Level']
          ?.propertyValue || 'false'
      )
    },
    searchList() {
      return [
        {
          type: 'input',
          value: 'userId',
          text: this.$t('user.headers.fullName'),
        },
        {
          type: 'tree',
          text: this.$t('user.headers.organize'),
          itemList: this.menuList,
          defaultProps: {
            children: 'children',
            label: 'name',
          },
        },
        {
          type: 'select',
          value: 'roleId',
          text: this.$t('user.headers.role'),
          itemList: this.roleList,
        },
      ]
    },
    // 用户时区列表
    timeZoneList() {
      return getTimeZoneList()
    },
    // 用户级别
    userLevelEnum() {
      return Object.assign([], this.$store.state.enums.enums.UserLevel)
    },
    // 用户状态枚举
    userStatus() {
      return this.$store.getters['enums/getUserStatus']
    },
    headers() {
      let defaultHeaders = [
        {
          text: this.$t('user.headers.name'),
          value: 'userId',
          width: '100px',
        },
        {
          text: this.$t('user.headers.fullName'),
          value: 'userName',
          width: '120px',
        },
        {
          text: this.$t('user.headers.level'),
          value: 'level',
          width: '120px',
        },
        {
          text: this.$t('user.headers.organize'),
          value: 'departmentName',
          width: '120px',
        },
        {
          text: this.$t('user.headers.role'),
          value: 'roleName',
          width: '120px',
        },
        {
          text: this.$t('user.headers.email'),
          value: 'email',
          width: '180px',
        },
        {
          text: this.$t('user.drawer.phone'),
          value: 'phone',
          width: 160,
        },
        {
          text: this.$t('user.headers.state'),
          value: 'status',
          width: '80px',
        },
        {
          text: this.$t('user.headers.lastLoginDate'),
          value: 'lastLoginDate',
          width: '150px',
        },
        {
          text: '',
          value: 'actions',
          sortable: false,
          width: 150,
        },
      ]
      if (
        !this.$store.state.appConfig.isThemeAndTimeZone ||
        this.userFlag !== 'true'
      ) {
        let index = defaultHeaders.findIndex(item => item.value === 'level')
        defaultHeaders.splice(index, 1)
      }

      return defaultHeaders
    },
  },
  watch: {
    drawer(val) {
      if (!val) {
        this.navigationDrawerClose()
      }
    },
  },
  created() {
    this.$_setTableHeight()
    this.$bus.$on('resize', this.$_setTableHeight)
  },
  async mounted() {
    await Promise.all([
      this.$_getTableData(),
      this.getRoleList(),
      // this.getGroupUserList(),
      this.getTree(),
      // this.getUserLevel(),
    ])
  },
  beforeDestroy() {
    this.$bus.$off('resize', this.$_setTableHeight)
  },
  methods: {
    changeTree(data, e) {
      if (e && e.checkedKeys.length) {
        this.$refs['elTree'].$refs.tree.setCheckedKeys([data.id])
        this.$refs['elTree'].filterText = data.name
        this.query.id = data.id
      } else {
        this.$refs['elTree'].$refs.tree.setCheckedKeys([])
        this.$refs['elTree'].filterText = ''
        this.query.id = ''
      }
      this.changeSize()
    },
    changeTree1(data, e) {
      if (e && e.checkedKeys.length) {
        this.$refs['elTree1'].$refs.tree.setCheckedKeys([data.id])
        this.$refs['elTree1'].filterText = data.name || data.departmentName
        this.userEt.departmentId = data.id
      } else {
        this.$refs['elTree1'].$refs.tree.setCheckedKeys([])
        this.$refs['elTree1'].filterText = ''
        this.userEt.departmentId = ''
      }
    },
    async onLock(record) {
      this.$swal({
        title: this.$t('user.swal.lock.title'),
        // text: `是否确认解锁用户：${record.userId}？`,
        text: this.$t('user.swal.lock.title', [record.userId]),
        icon: 'warning',
        reverseButtons: true,
        showCancelButton: true,
        confirmButtonText: this.$t('action.confirm'),
        cancelButtonText: this.$t('action.cancel'),
        customClass: {
          confirmButton: 'sweet-btn-primary',
        },
      }).then(async result => {
        if (result.isConfirmed) {
          await unlockUser({ userId: record.userId })
          this.$notify.info('success', this.$t('user.hint.lock'))
          this.$_getTableData()
        }
      })
    },
    async getTree() {
      const e = await querySysGroupList({ state: '0' })
      this.menuList = e.data || []
    },

    changePassword() {
      this.showPassword = !this.showPassword
      this.passText = this.showPassword ? 'text' : 'password'
    },
    // 重置选择条件
    $_reset() {
      this.query = Object.assign(
        this.$data.query,
        this.$options.data.call(this).query,
      )
      this.$nextTick(() => {
        this.$refs['elTree'].$refs.tree.setCheckedKeys([])
        this.$refs['elTree'].filterText = ''
        this.$_getTableData()
      })
    },

    // 选择角色
    onChangeRole(obj) {
      console.log('obj', obj)
    },

    // 设置表格高度
    $_setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = setRemainingHeight(
          this.$store.state.global.subMarginFn,
        )
      })
    },

    // 表格选择事件
    $_tableSelected({ item, value, items }) {
      if (items) {
        this.selecedArray = value ? deepClone(items) : []
      } else if (value) {
        this.selecedArray.push(item)
      } else {
        const index = this.selecedArray.findIndex(v => v.code === item.code)
        this.selecedArray.splice(index, 1)
      }
    },

    navigationDrawerClose() {
      this.drawer = false
      this.ops = 'new'
      this.userEt = this.initUserData()
    },

    /*        `async` searchUsers() {
                  this.tableLoading = true
                  qeuryUserLike({}, this.query.pageNum - 1, this.query.pageSize).then(resp => {
                      if (resp.status === 200) {
                          this.tableDataTotal = resp.data.totalElements
                          this.tableData = resp.data.content.map(
                              e => {
                                  e.user.roles = e.roles.map(r => r.name)
                                  e.user.groups = e.groups.map(r => r.name)
                                  return e.user
                              }
                          )

                      }
                  }).catch(e => {
                      // this.$notify.info('error', '查询异常！')
                  }).finally(() => {
                      this.tableLoading = false
                  })
              }, */

    // 加载用户管理信息
    // async loadData() {
    //   // this.$store.state.user.userInfo.userName
    //   const params = {
    //     userName: this.searchKey,
    //     pageSize: this.query.pageSize,
    //     pageNum: this.query.pageNum,
    //   }
    //   this.tableLoading = true
    //   findUsers(params)
    //     .then(async res => {
    //       console.log('res', res)
    //       if (res.code === 200) {
    //         this.tableData = await res.data.records
    //         this.tableDataTotal = await res.data.total

    //         // this.$notify.info('success', res.msg)
    //       } else {
    //         this.$notify.info('error', res.msg)
    //       }
    //     })
    //     .finally(() => {
    //       this.tableLoading = false
    //     })
    // },

    async $_getTableData() {
      // 保存查询参数
      this.tableLoading = true
      this.tableData = []
      const res = await findUsers(this.query)
      this.tableDataTotal = res.data.total

      this.tableData = res.data.records.map(item => {
        item.departmentName = item.departments
          .map(v => v.departmentName)
          .join('，')

        item.roleName = item.roles.map(v => v.roleName).join('，')
        return item
      })

      this.tableLoading = false
    },

    // async searchUsers() {
    //   // this.tableLoading = true
    //   // this.query.pageNum = 1
    //   // this.loadData(this.searchKey)

    //   // const resp = await queryUserLikeDt({}, this.query.pageNum - 1, this.query.pageSize)
    //   // this.tableDataTotal = resp.totalElements
    //   // this.tableData = resp.content.map(e => {
    //   //   e.user.roles = e.roles.map(r => r.name)

    //   //   return e.user
    //   // })
    //   // this.tableLoading = false
    // },

    changePage(e) {
      this.$_getTableData()

      // this.searchUsers()
    },
    changeSize(e) {
      this.query.pageNum = 1
      this.$_getTableData()
    },

    add() {
      this.drawer = true
      // 重置校验和表单
      this.$refs.form.resetValidation()
      // this.$refs.form.reset()
      this.$nextTick(() => {
        this.$refs['elTree1'].filterText = ''
        this.$refs['elTree1'].$refs.tree.setCheckedKeys([])
      })
      this.userEt = this.initUserData()
      this.$forceUpdate()
    },
    edit(data) {
      this.treeSelect = []
      const record = {
        userId: data.userId,
        userName: data.userName,
        level: data.level,
        timezoneId: data.timezoneId,

        // 角色单选，string
        roleId: data.roles.map(v => v.roleId).join(','),
        departmentId: data.departments.map(v => v.id).join(','),

        roleList: data.roles,
        departmentList: data.departments,

        password: '',
        rePassword: '',
        phone: data.phone,
        email: data.email,
      }
      // record.roleId = data.roleId.split(',') || data.roleId
      // record.roleName = data.roleName.split(',') || data.roleName

      // record.groupName = data.groupName.split(',') || data.groupName
      this.userEt = { ...record }

      this.drawer = true

      this.ops = 'edit'
      this.$nextTick(() => {
        if (record.departmentList.length) {
          this.$refs['elTree1'].$refs.tree.setCheckedKeys([
            record.departmentList[0].id,
          ])
          this.$refs['elTree1'].filterText =
            record.departmentList[0].departmentName
        } else {
          this.$refs['elTree1'].$refs.tree.setCheckedKeys([])
          this.$refs['elTree1'].filterText = ''
        }
      })
      // getUserDetail(data.id).then(resp => {
      //   if (resp.status === 200) {
      //     const { user } = resp.data
      //     user.roleId = resp.data.roles.map(r => r.id)

      //     this.userEt = user
      //     this.drawer = true
      //     this.ops = true
      //   }
      // })
    },
    del(data) {
      // this.$confirm(`是否确认删除用户：${data.userName}？`, '确认信息', {
      //   distinguishCancelAndClose: true,
      //   confirmButtonText: '保存',
      //   cancelButtonText: '取消',
      //   confirmButtonClass: 'color-white',
      // })
      //   .then(async () => {
      //     const params = {
      //       id: data.userId,
      //     }
      //     const res = await delUser(params)
      //     if (res.code === 200) {
      //       this.$notify.info('success', res.msg)
      //       this.query.pageNum = 1
      //       this.$_getTableData()
      //     }
      //   })
      //   .catch(() => {
      //     this.$notify.info('warning', `已取消删除用户：${data.userName}`)
      //   })

      this.$swal({
        title: this.$t('user.swal.del.title'),
        // text: `是否确认删除用户：${data.userName}(${data.userId})？`,
        text: this.$t('user.swal.del.text', [data.userName, data.userId]),
        icon: 'warning',
        reverseButtons: true,
        showCancelButton: true,
        confirmButtonText: this.$t('action.confirm'),
        cancelButtonText: this.$t('action.cancel'),
        // cancelButtonColor: '#f00',
        customClass: {
          confirmButton: 'sweet-btn-primary',
          // cancelButton: 'btn-cancel color-base',
        },
      }).then(async result => {
        if (result.isConfirmed) {
          const params = {
            id: data.userId,
          }
          const res = await delUser(params)
          this.$notify.info(
            'success',
            this.$t('global.hint.del', [this.$t('user.currentTitle')]),
          )
          this.query.pageNum = 1
          this.$_getTableData()
        }
      })
    },

    // save(callback) {
    //   const bool = this.$refs.form.validate()
    //   if (!bool) return callback(false, true)
    //   if (this.userEt.password !== this.userEt.rePassword) {
    //     this.$notify.info('warning', '两次密码不一致！')
    //     callback(false, true)

    //     return
    //   }

    //   if (this.userEt.id) {
    //     updateUser(this.userEt.id, this.userEt)
    //       .then(resp => {
    //         if (resp.status === 200) {
    //           this.$notify.info('success', '修改成功')
    //           this.searchUsers()
    //           this.navigationDrawerClose()
    //           callback()
    //         }
    //       })
    //       .catch(e => {
    //         console.log(e)
    //         callback(false, true)
    //       })
    //   } else {
    //     createUser(this.userEt)
    //       .then(resp => {
    //         if (resp.status === 200) {
    //           this.$notify.info('success', '创建成功')
    //           this.searchUsers()
    //           this.navigationDrawerClose()
    //           callback()
    //         }
    //       })
    //       .catch(e => {
    //         console.log(e)
    //         callback(false, true)
    //       })
    //   }
    // },
    convertDateTime(time) {
      if (time) {
        return formatDate(new Date(time))
      }

      return ''
    },

    /*      getRoleList() {
              qeuryRoleLike({}, 0, PAGESIZE_MAX).then(resp => {
                this.roleList = resp.data.content;
              });
            }, */

    async getRoleList() {
      // const resp = qeuryRoleLikeDt({}, 0, PAGESIZE_MAX)
      // this.roleList = resp.content
      const params = {
        pageNum: 1,
        pageSize: PAGESIZE_MAX,
      }
      const res = await userRole(params)
      this.roleList = res.data.records.map(item => {
        return {
          ...item,
          value: item.roleId,
          text: item.roleName,
          disabled: item.roleId === 'admin',
        }
      })
      // .filter(item => item.roleId !== 'admin')
      // this.roleList = res.data.records
    },

    // async getGroupUserList() {
    //   const params = {
    //     pageNum: 1,
    //     pageSize: PAGESIZE_MAX,
    //   }
    //   const res = await userGroup(params)
    //   this.userGroupList = res.data.records

    // },

    // 初始化用户表单信息
    initUserData() {
      return {
        userId: '', // 登录名
        userName: '', // 登录名
        timezoneId: Intl.DateTimeFormat().resolvedOptions().timeZone,
        email: '', // 邮箱
        phone: '', // 手机号
        fullname: '', // 目前未用到
        password: '', // 密码
        rePassword: '', // 确认密码
        roleList: [],
        groupList: [],
        departmentList: [],
        roleId: '', // 角色
        departmentId: '', //组织架构id
        level: '0', // 用户级别
      }
    },

    onConfirm(fn) {
      // 提交表单前需要注意的问题：
      //   1、密码和确认密码是否一致
      //   2、密码需要md5方式加密，slice(0,10)+vsoc

      // fn(loading = false, dialog = false)
      // loading：控制加载效果
      // dialog：控制抽屉关闭
      const bool = this.$refs.form.validate()

      // 深拷贝
      const form = lodash.cloneDeep(this.userEt)
      if (form.password && form.rePassword) {
        // if (this.userEt.password !== this.userEt.rePassword) {
        //   this.$notify.info('error', '两次密码不一致')

        //   // this.onCancel()
        //   fn(false, true)

        //   return
        // }

        // 密码加密，注意不能共用同一对象，不然密码那一栏会变成加密后的密码
        form.password = `${md5(this.userEt.password).slice(0, 10)}vsoc`
        form.rePassword = `${md5(this.userEt.rePassword).slice(0, 10)}vsoc`
      }

      // 用户组和角色的信息拼接
      if (this.userEt.roleId) {
        form.roleList = [{ roleId: this.userEt.roleId }]
      }

      if (this.userEt.departmentId) {
        form.departmentList = [{ departmentId: this.userEt.departmentId }]
      } else {
        form.departmentList = []
      }
      delete form.roleId
      delete form.departmentId
      // form.roleId = this.userEt.roleId.join(',')
      if (bool) {
        // 校验通过
        if (this.ops === 'edit') {
          this.onEdit(form, fn)
        } else {
          this.onAdd(form, fn)
        }
      } else {
        fn(false, true)

        // 校验不通过
        // this.$refs.drawer.confirmLoading = true
        this.$notify.info('error', '存在【必填项】未填')
      }
    },
    onCancel() {
      this.$refs.form.resetValidation()
      this.$refs.form.reset()
    },

    // 新增用户信息
    async onAdd(form, fn) {
      try {
        await addUser(form)
        this.$notify.info(
          'success',
          this.$t('global.hint.add', [this.$t('user.currentTitle')]),
        )
        this.$refs.form.resetValidation()

        // 新增用户信息成功，重新加载数据
        this.query.pageNum = 1
        this.$_getTableData()

        // 关闭抽屉
        fn()
      } catch (err) {
        this.$notify.info('error', res.msg)
        fn(false, true)
      }
    },

    // 修改用户信息
    async onEdit(form, fn) {
      // form.roleName = this.userEt.roleName.join(',')
      // form.groupName = this.userEt.groupName.join(',')
      try {
        const res = await updateUser(form)
        this.$notify.info(
          'success',
          this.$t('global.hint.edit', [this.$t('user.currentTitle')]),
        )
        this.$refs.form.resetValidation()

        // 新增用户信息成功，重新加载数据
        this.query.pageNum = 1
        this.$_getTableData()

        // 关闭抽屉
        fn()
      } catch (err) {
        this.$notify.info('error', res.msg)
        fn(false, true)
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.vsoc-drawer__content .row .v-text-field.v-input--dense {
  margin-bottom: 8px !important;
  // font-size: 1rem !important;
}
::v-deep .header {
  align-items: center;
  .v-chip.v-size--default {
    height: auto;
  }
  .v-text-field__details {
    display: none;
  }
  .v-input--dense > .v-input__control > .v-input__slot {
    margin: 0;
  }
}

::v-deep.v-list-item__title {
  font-size: 1.1667rem;
  line-height: 1.8333rem;
  color: var(--v-color-base) !important;
}
.v-list--dense .v-list-item .v-list-item__content {
  padding: 8px 16px !important;
}
</style>
