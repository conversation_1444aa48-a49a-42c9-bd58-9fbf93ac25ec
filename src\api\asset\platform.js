import { request, vsocPath } from '../../util/request'

export const getModelList = function (data) {
  return request({
    url: `${vsocPath}/vehicleType/vehicleHealth`,
    method: 'post',
    loading: true,
    data,
  })
}

export const uploadVehicleFile = function (data) {
  return request({
    url: `${vsocPath}/file/vehicleTypeUpload`,
    method: 'post',
    data,
  })
}

export const getVehiclePlatformList = function (data) {
  return request({
    url: `${vsocPath}/vehicleType/vehicleTypes`,
    method: 'post',
    loading: true,
    data,
  })
}

export const getVehicleModelmList = function (data) {
  return request({
    url: `${vsocPath}/vehicleType/vehicleTypes`,
    method: 'post',
    data,
  })
}
export const addVehiclePlatform = function (data) {
  return request({
    url: `${vsocPath}/vehicleType/addVehicleType`,
    method: 'post',
    data,
  })
}

export const editVehiclePlatform = function (data) {
  return request({
    url: `${vsocPath}/vehicleType/updateVehicleType`,
    method: 'post',
    data,
  })
}

export const vehicleTypeDetail = function (data) {
  return request({
    url: `${vsocPath}/vehicleType/vehicleTypeDetail`,
    method: 'post',
    data,
  })
}

export const delVehiclePlatform = function (data) {
  return request({
    url: `${vsocPath}/vehicleType/deleteVehicleType`,
    method: 'post',
    data,
  })
}

export const getAllPlatFormList = function (data) {
  return request({
    url: `${vsocPath}/platform/platformList`,
    method: 'post',
    data,
  })
}
