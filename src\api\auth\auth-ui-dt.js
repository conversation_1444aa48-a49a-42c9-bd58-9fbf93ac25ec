import { deleteRequest, getRequest, postRequest, putRequest, vsocPath } from '../../util/request'

const MODULE_PATH = vsocPath

export function getUiLikeDt(authUi, page, size) {
  const param = { page, size }
  Object.assign(param, authUi)

  return getRequest(`${MODULE_PATH}/auth-ui/like`, param)
}

export function addUi(authUi) {
  return postRequest(`${MODULE_PATH}/auth-ui`, authUi)
}

export function updateUi(id, authUi) {
  return putRequest(`${MODULE_PATH}/auth-ui`, authUi)
}

export function deleteUis(ids) {
  return deleteRequest(`${MODULE_PATH}/auth-ui`, ids)
}

export function getSimpleUiTree() {
  return getRequest(`${MODULE_PATH}/auth-ui/tree/simple`)
}

export function getUiTreeDt() {
  return [
    {
      id: 57,
      code: '首页',
      name: '首页',
      uiType: 'Menu',
      url: '/home',
      description: '',
      component: 'Home/index',
      iconCls: 'mdi-view-dashboard',
      isShow: true,
      parentId: 0,
      orderNum: 1,
      children: null,
    },
    {
      id: 12,
      code: '资产中心',
      name: '资产中心',
      uiType: 'Catalogue',
      url: '',
      description: '资产中心',
      component: '',

      // iconCls: 'mdi-diamond-stone',
      iconCls: 'mdi-car',
      isShow: true,
      parentId: 0,
      orderNum: 2,
      children: [
        {
          id: 60,
          code: '资产管理',
          name: '资产管理',
          uiType: 'Menu',
          url: '/assets',
          description: '',
          component: 'Assets/Assets.vue',
          iconCls: 'O',
          isShow: true,
          parentId: 12,
          orderNum: 0,
          children: [
            {
              id: 15,
              code: '车辆详情',
              name: '车辆详情',
              uiType: 'Menu',
              url: 'details',
              description: '资产详情',
              component: 'Assets/AssetsDetails.vue',
              iconCls: '',
              isShow: false,
              parentId: 60,
              orderNum: 0,
              children: null,
            },
          ],
        },

        //   {
        //   "id": 75,
        //   "code": "零部件管理",
        //   "name": "零部件管理",
        //   "uiType": "Menu",
        //   "url": "/parts",
        //   "description": "",
        //   "component": "Parts/index.vue",
        //   "iconCls": "P",
        //   "isShow": true,
        //   "parentId": 12,
        //   "orderNum": 2,
        //   "children": null
        // }, {
        //   "id": 76,
        //   "code": "服务器管理",
        //   "name": "服务器管理",
        //   "uiType": "Menu",
        //   "url": "/servers",
        //   "description": "",
        //   "component": "Servers/index.vue",
        //   "iconCls": "S",
        //   "isShow": true,
        //   "parentId": 12,
        //   "orderNum": 3,
        //   "children": null
        // },
        {
          id: 59,
          code: '资产组',
          name: '资产组',
          uiType: 'Menu',
          url: '/assetGroup',
          description: '',
          component: 'Assets/Group/index.vue',
          iconCls: 'O',
          isShow: true,
          parentId: 12,
          orderNum: 4,
          children: null,
        },
      ],
    },
    {
      id: 16,
      code: '告警管理',
      name: '告警管理',
      uiType: 'Menu',
      url: '/alertManagement',
      description: '告警管理',
      component: 'Alerts/index.vue',
      iconCls: 'mdi-flash',
      isShow: true,
      parentId: 0,
      orderNum: 3,
      children: [
        {
          id: 50,
          code: 'system:alertDetails',
          name: '告警详情',
          uiType: 'Menu',
          url: '/alertManagement/details',
          description: '',
          component: 'Alerts/AlertsDetails.vue',
          iconCls: '',
          isShow: false,
          parentId: 16,
          orderNum: 0,
          children: null,
        },
      ],
    },
    {
      id: 17,
      code: '检测管理',
      name: '检测管理',
      uiType: 'Menu',
      url: '/detectManagement',
      description: '检测管理',
      component: 'Detectors/index.vue',
      iconCls: 'mdi-shield-search',
      isShow: true,
      parentId: 0,
      orderNum: 4,
      children: [
        {
          id: 19,
          code: '检测编辑',
          name: '检测编辑',
          uiType: 'Menu',
          url: '/detectManagement/edit',
          description: '检测编辑',
          component: 'Detectors/edit.vue',
          iconCls: 'O',
          isShow: false,
          parentId: 17,
          orderNum: 5,
          children: null,
        },
      ],
    },
    {
      id: 62,
      code: '处置管理',
      name: '处置管理',
      uiType: 'Catalogue',
      url: '',
      description: '',
      component: '',
      iconCls: 'mdi-shield-check',
      isShow: true,
      parentId: 0,
      orderNum: 7,
      children: [
        {
          id: 22,
          code: '处置配置',
          name: '处置配置',
          uiType: 'Menu',
          url: '/actionManagement',
          description: '处置配置',
          component: 'Actions/index.vue',
          iconCls: 'O',
          isShow: true,
          parentId: 62,
          orderNum: 1,
          children: [
            {
              id: 23,
              code: '处置编辑',
              name: '处置编辑',
              uiType: 'Menu',
              url: '/actionManagement/edit',
              description: '处置编辑',
              component: 'Actions/edit.vue',
              iconCls: 'actions',
              isShow: false,
              parentId: 22,
              orderNum: 7,
              children: null,
            },
          ],
        },
        {
          id: 63,
          code: '处置结果',
          name: '处置结果',
          uiType: 'Menu',
          url: '/action-log',
          description: '',
          component: 'Actions/ActionsList.vue',
          iconCls: 'O',
          isShow: true,
          parentId: 62,
          orderNum: 2,
          children: null,
        },
      ],
    },
    {
      id: 24,
      code: '数字孪生',
      name: '数字孪生',
      uiType: 'Catalogue',
      url: '',
      description: '数字孪生',
      component: '',
      iconCls: 'mdi-car-shift-pattern',
      isShow: true,
      parentId: 0,
      orderNum: 8,
      children: [
        {
          id: 51,
          code: '信号管理',
          name: '信号管理',
          uiType: 'Menu',
          url: '/digital-twin/signals',
          description: '',
          component: 'Signals/index.vue',
          iconCls: 'O',
          isShow: true,
          parentId: 24,
          orderNum: 0,
          children: [
            {
              id: 58,
              code: '信号编辑',
              name: '信号编辑',
              uiType: 'Menu',
              url: '/digital-twin/signals/edit',
              description: '',
              component: 'Signals/edit.vue',
              iconCls: 'O',
              isShow: false,
              parentId: 51,
              orderNum: 0,
              children: null,
            },
          ],
        },
        {
          id: 53,
          code: '推断管理',
          name: '推断管理',
          uiType: 'Menu',
          url: '/digital-twin/sensors',
          description: '',
          component: 'Sensors/index.vue',
          iconCls: 'O',
          isShow: true,
          parentId: 24,
          orderNum: 2,
          children: [
            {
              id: 56,
              code: '推断编辑',
              name: '推断编辑',
              uiType: 'Menu',
              url: '/digital-twin/sensors/edit',
              description: '',
              component: 'Sensors/edit.vue',
              iconCls: 'O',
              isShow: false,
              parentId: 53,
              orderNum: 0,
              children: null,
            },
          ],
        },
      ],
    },

    /*    {
    "id": 25,
    "code": "属性管理",
    "name": "属性管理",
    "uiType": "Menu",
    "url": "/profiles",
    "description": "属性管理",
    "component": "Profiles/index.vue",
    "iconCls": "mdi-shield-link-variant",
    "isShow": true,
    "parentId": 0,
    "orderNum": 9,
    "children": null
  }, */
    {
      id: 8,
      code: '系统管理',
      name: '系统管理',
      uiType: 'Catalogue',
      url: '/system',
      description: '系统管理',
      component: '',
      iconCls: 'mdi-apps',
      isShow: true,
      parentId: 0,
      orderNum: 10,
      children: [
        {
          id: 9,
          code: '用户管理',
          name: '用户管理',
          uiType: 'Menu',
          url: '/userManagement',
          description: '用户管理',
          component: 'System/User/index.vue',
          iconCls: 'O',
          isShow: true,
          parentId: 8,
          orderNum: 0,
          children: null,
        },
        {
          id: 61,
          code: '用户组',
          name: '用户组',
          uiType: 'Menu',
          url: '/groupUser',
          description: '',
          component: 'System/Group/index.vue',
          iconCls: 'O',
          isShow: true,
          parentId: 8,
          orderNum: 1,
          children: null,
        },
        {
          id: 11,
          code: '角色管理',
          name: '角色管理',
          uiType: 'Menu',
          url: '/roleManagement',
          description: '角色管理',
          component: 'System/Role/index.vue',
          iconCls: 'O',
          isShow: true,
          parentId: 8,
          orderNum: 2,
          children: null,
        },

        // {
        //   id: 10,
        //   code: 'system:menu',
        //   name: '菜单管理',
        //   uiType: 'Menu',
        //   url: '/menuManagement',
        //   description: '菜单管理',
        //   component: 'System/Menu/index.vue',
        //   iconCls: 'O',
        //   isShow: true,
        //   parentId: 8,
        //   orderNum: 3,
        //   children: [
        //     {
        //       id: 33,
        //       code: 'system:menu:del',
        //       name: '菜单删除按钮',
        //       uiType: 'Button',
        //       url: '',
        //       description: '菜单管理修改按钮',
        //       component: '',
        //       iconCls: '',
        //       isShow: true,
        //       parentId: 10,
        //       orderNum: 0,
        //       children: null,
        //     },
        //   ],
        // },
        // {
        //   id: 40,
        //   code: '接口管理',
        //   name: '接口管理',
        //   uiType: 'Menu',
        //   url: '/interfaceManagement',
        //   description: '',
        //   component: 'System/Interface/index.vue',
        //   iconCls: 'O',
        //   isShow: true,
        //   parentId: 8,
        //   orderNum: 4,
        //   children: null,
        // },
        // {
        //   id: 42,
        //   code: '邮件管理',
        //   name: '邮件管理',
        //   uiType: 'Menu',
        //   url: '',
        //   description: '',
        //   component: '',
        //   iconCls: 'O',
        //   isShow: true,
        //   parentId: 8,
        //   orderNum: 5,
        //   children: [
        //     {
        //       id: 43,
        //       code: '邮件清单',
        //       name: '邮件清单',
        //       uiType: 'Menu',
        //       url: '/email/list',
        //       description: '',
        //       component: 'System/Email/index.vue',
        //       iconCls: 'O',
        //       isShow: true,
        //       parentId: 42,
        //       orderNum: 0,
        //       children: null,
        //     },
        //     {
        //       id: 46,
        //       code: '模板管理',
        //       name: '模板管理',
        //       uiType: 'Menu',
        //       url: '/email/template',
        //       description: '',
        //       component: 'System/Email/EmailTemplate.vue',
        //       iconCls: 'O',
        //       isShow: true,
        //       parentId: 42,
        //       orderNum: 1,
        //       children: null,
        //     },
        //   ],
        // },
      ],
    },

    //   {
    //   "id": 64,
    //   "code": "调度管理",
    //   "name": "调度管理",
    //   "uiType": "Menu",
    //   "url": "",
    //   "description": "",
    //   "component": "",
    //   "iconCls": "mdi-cog-sync-outline",
    //   "isShow": true,
    //   "parentId": 0,
    //   "orderNum": 11,
    //   "children": [{
    //     "id": 67,
    //     "code": "调度管理",
    //     "name": "调度管理",
    //     "uiType": "Menu",
    //     "url": "/scheduler/manage",
    //     "description": "",
    //     "component": "Scheduler/SchedulerManage.vue",
    //     "iconCls": "M",
    //     "isShow": true,
    //     "parentId": 64,
    //     "orderNum": 0,
    //     "children": [{
    //       "id": 68,
    //       "code": "编辑调度",
    //       "name": "编辑调度",
    //       "uiType": "Menu",
    //       "url": "/scheduler/manage/edit",
    //       "description": "",
    //       "component": "Scheduler/SchedulerEdit.vue",
    //       "iconCls": "E",
    //       "isShow": false,
    //       "parentId": 67,
    //       "orderNum": 0,
    //       "children": null
    //     }]
    //   }, {
    //     "id": 66,
    //     "code": "调度结果",
    //     "name": "调度结果",
    //     "uiType": "Menu",
    //     "url": "/scheduler/result",
    //     "description": "",
    //     "component": "Scheduler/SchedulerResult.vue",
    //     "iconCls": "R",
    //     "isShow": true,
    //     "parentId": 64,
    //     "orderNum": 1,
    //     "children": null
    //   }, {
    //     "id": 65,
    //     "code": "引擎管理",
    //     "name": "引擎管理",
    //     "uiType": "Menu",
    //     "url": "/scheduler/app",
    //     "description": "",
    //     "component": "Scheduler/SchedulerApp.vue",
    //     "iconCls": "A",
    //     "isShow": true,
    //     "parentId": 64,
    //     "orderNum": 3,
    //     "children": null
    //   }]
    // },
    {
      id: 73,
      code: '漏洞管理',
      name: '漏洞管理',
      uiType: 'Catalogue',
      url: '/bug',
      description: '',
      component: '',
      iconCls: 'mdi-bug',
      isShow: true,
      parentId: 0,
      orderNum: 12,
      children: [
        {
          id: 26,
          code: '漏洞列表',
          name: '漏洞列表',
          uiType: 'Menu',
          url: '/bug/management',
          description: '漏洞管理',
          component: 'Bugs/index.vue',
          iconCls: 'O',
          isShow: true,
          parentId: 73,
          orderNum: 12,
          children: [
            {
              id: 74,
              code: '编辑漏洞',
              name: '编辑漏洞',
              uiType: 'Menu',
              url: '/bug/management/edit',
              description: '',
              component: 'Bugs/edit.vue',
              iconCls: 'O',
              isShow: false,
              parentId: 26,
              orderNum: 0,
              children: null,
            },
            {
              id: 32,
              code: '漏洞详情',
              name: '漏洞详情',
              uiType: 'Menu',
              url: '/bug/management/details',
              description: '漏洞详情',
              component: 'Bugs/details.vue',
              iconCls: '',
              isShow: false,
              parentId: 26,
              orderNum: 0,
              children: null,
            },
          ],
        },
        {
          id: 71,
          code: '同步管理',
          name: '同步管理',
          uiType: 'Menu',
          url: '/bug/synchronous',
          description: '',
          component: 'Scheduler/SchedulerManage.vue',
          iconCls: 'O',
          isShow: true,
          parentId: 73,
          orderNum: 19,
          children: [
            {
              id: 72,
              code: '编辑同步',
              name: '编辑同步',
              uiType: 'Menu',
              url: '/bug/synchronous/edit',
              description: '',
              component: 'Scheduler/SchedulerEdit.vue',
              iconCls: 'O',
              isShow: false,
              parentId: 71,
              orderNum: 0,
              children: null,
            },
          ],
        },
      ],
    },
    {
      id: 27,
      code: '情报管理',
      name: '情报管理',
      uiType: 'Menu',
      url: '/intelligenceManagement',
      description: '情报管理',
      component: 'Intelligence/index.vue',
      iconCls: 'mdi-shield-bug',
      isShow: true,
      parentId: 0,
      orderNum: 13,
      children: null,
    },
    {
      id: 28,
      code: '采集管理',
      name: '采集管理',
      uiType: 'Menu',
      url: '/collectManagement',
      description: '采集策略',
      component: 'Collection/index.vue',
      iconCls: 'mdi-server-network',
      isShow: true,
      parentId: 0,
      orderNum: 14,
      children: null,
    },

    /*    {
    "id": 29,
    "code": "消息管理",
    "name": "消息管理",
    "uiType": "Menu",
    "url": "/messageManagement",
    "description": "消息管理",
    "component": "Messages/index.vue",
    "iconCls": "mdi-bell",
    "isShow": true,
    "parentId": 0,
    "orderNum": 15,
    "children": null
  }, */
    {
      id: 48,
      code: '仿真模拟',
      name: '仿真模拟',
      uiType: 'Menu',
      url: '/simulator',
      description: '',
      component: 'Simulator/index.vue',
      iconCls: 'mdi-brightness-auto',
      isShow: true,
      parentId: 0,
      orderNum: 16,
      children: null,
    },

    //   {
    //   "id": 69,
    //   "code": "映射规则管理",
    //   "name": "映射规则管理",
    //   "uiType": "Menu",
    //   "url": "/mapping",
    //   "description": "",
    //   "component": "Mapping/index.vue",
    //   "iconCls": "mdi-repeat",
    //   "isShow": true,
    //   "parentId": 0,
    //   "orderNum": 17,
    //   "children": null
    // }, {
    //   "id": 70,
    //   "code": "函数管理",
    //   "name": "函数管理",
    //   "uiType": "Menu",
    //   "url": "/functions",
    //   "description": "",
    //   "component": "Functions/index.vue",
    //   "iconCls": "mdi-function-variant",
    //   "isShow": true,
    //   "parentId": 0,
    //   "orderNum": 18,
    //   "children": null
    // }
  ]
}
