<template>
  <!-- <vsoc-dialog
    v-model="isShow"
    :title="title"
    dense
    @input="$emit('input', $event)"
    @click:confirm="confirm"
    width="800"
  >
    <v-form ref="form" v-model="valid" class="batch-edit-box px-6 py-4">
      <v-row
        v-if="editForm.actionType === '1'"
        class="pl-0 pr-0 mb-3 d-flex align-center"
      >
        <v-col class="mr-3">
          <el-tree
            labelText="处理组"
            ref="elTree"
            :organizationFlag="true"
            :outlinedFlag="false"
            :showCheckbox="true"
            :expandFlag="true"
            :treeData="groupList"
            :default-props="defaultProps"
            @change="changeTree"
            :bottom="true"
            :ruleFlag="true"
          />
        </v-col>
        <v-col>
          <v-autocomplete
            v-model="editForm.assignedTo"
            :menu-props="{ offsetY: true, maxHeight: 300 }"
            append-icon="mdi-chevron-down"
            :items="allUserList"
            label="处理人"
            :disabled="!show"
            dense
            :rules="[v => !!v || '处理人是必填的']"
          >
          </v-autocomplete>
        </v-col>
      </v-row>
      <quill-editor
        ref="myQuillEditor"
        v-model="editForm.remark"
        :options="editorOption"
        class="editor"
      >
      </quill-editor>
    </v-form>
  </vsoc-dialog> -->
  <vsoc-drawer
    v-model="isShow"
    :title="title + ' ' + $t('ticket.currentTitle')"
    :confirm-btn-text="$t('action.confirm')"
    @click:confirm="confirm"
  >
    <v-form ref="form" v-model="valid" class="batch-edit-box pt-2">
      <v-row class="pl-6 pr-6 d-flex align-center tip-drawer-box">
        <v-icon style="color: #ff910f" size="14"> mdi-alert-circle </v-icon>
        <span class="tip-drawer-box ml-2 text-root">
          <!-- 确定要{{ title }}「{{ num }}」条工单吗？ -->
          {{ $t('ticket.hint.opInfo', [title, num]) }}
        </span>
      </v-row>
      <div v-if="editForm.actionType === '1'">
        <v-row class="pl-6 pr-6">
          <el-tree
            class="mt-4"
            :labelText="$t('ticket.headers.assignedGroupName')"
            ref="elTree"
            :organizationFlag="true"
            :outlinedFlag="false"
            :showCheckbox="true"
            :expandFlag="true"
            :treeData="groupList"
            :default-props="defaultProps"
            @change="changeTree"
            :bottom="true"
            :ruleFlag="true"
            :isDense="false"
          />
        </v-row>

        <v-row class="pl-6 pr-6">
          <v-autocomplete
            v-model="editForm.assignedTo"
            :menu-props="{ offsetY: true, maxHeight: 300 }"
            append-icon="mdi-chevron-down"
            :items="allUserList"
            :label="$t('ticket.headers.assignedToName')"
            :disabled="!show"
            :rules="[
              v =>
                !!v ||
                this.$t('validation.required', [
                  this.$t('ticket.headers.assignedToName'),
                ]),
            ]"
          >
          </v-autocomplete>
        </v-row>
      </div>

      <v-row class="pl-6 pr-6">
        <v-textarea
          v-model="editForm.remark"
          :label="$t('ticket.headers.reason')"
          color="primary"
          :rules="[
            v =>
              !!v ||
              this.$t('validation.required', [
                this.$t('ticket.headers.reason'),
              ]),
          ]"
          :rows="$AREA_ROWS"
        ></v-textarea>
      </v-row>
    </v-form>
  </vsoc-drawer>
</template>

<script>
import VsocDrawer from '@/components/VsocDrawer.vue'

import VsocDialog from '@/components/VsocDialog.vue'
import elTree from '@/components/search-tree/index'
import 'quill/dist/quill.bubble.css'
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import { quillEditor } from 'vue-quill-editor'
export default {
  name: 'BatchEdit',
  components: {
    VsocDialog,
    quillEditor,
    elTree,
    VsocDrawer,
  },
  props: {
    groupList: Array,
    userList: Array,
  },
  data() {
    return {
      valid: true,
      title: '',
      defaultProps: {
        children: 'children',
        label: 'name',
      },
      editorOption: {
        placeholder: '对工单进行评论或备注',
        modules: {
          toolbar: false,
        },
      },
      isShow: false,
      show: false,
      allUserList: [],
      editForm: {
        actionType: '',
        remark: '',
        assignedGroup: '',
        assignedTo: '',
      },
      num: 0,
    }
  },
  computed: {},
  mounted() {},
  methods: {
    open(params, callback) {
      this.isShow = true
      this.fn = callback
      this.$nextTick(() => {
        this.$refs.form.resetValidation()
        this.editForm.actionType = params.item.type
        this.title = this.$t(params.item.title)
        this.num = params.num
        this.editForm.remark = ''
        this.changeTree()
      })
    },
    changeTree(data, e) {
      if (!this.$refs['elTree']) return
      if (e && e.checkedKeys.length) {
        this.$refs['elTree'].$refs.tree.setCheckedKeys([data.id])
        this.$refs['elTree'].filterText = data.name
        this.editForm.assignedGroup = data.id
        this.editForm.assignedTo = ''
        this.allUserList = this.userList.filter(
          v => (v.departments.length && v.departments[0].id) === data.id,
        )
        this.show = true
      } else {
        this.$refs['elTree'].$refs.tree.setCheckedKeys([])
        this.$refs['elTree'].filterText = ''
        this.editForm.assignedGroup = ''
        this.editForm.assignedTo = ''
        this.show = false
      }
    },
    confirm(cb) {
      if (!this.$refs.form.validate()) return cb(false, true)
      // if (!this.editForm.remark) {
      //   this.$notify.info('error', '请填写原因！')
      //   return cb(false, true)
      // }
      this.fn(this.editForm)
      cb()
    },
  },
}
</script>
<style lang="scss" scoped>
.batch-edit-box {
  .tip-drawer-box {
    color: var(--v-color-base) !important;
  }
  // ::v-deep .v-textarea textarea {
  //   height: 200px !important;
  // }
  .editor {
    width: 100% !important;
    height: 200px !important;
    ::v-deep .ql-editor {
      padding: 8px 12px !important;
      color: rgba(94, 86, 105, 0.87) !important;
      font-size: 14px !important;
    }
    ::v-deep .ql-container.ql-snow {
      border-color: #e0dede !important;
      border-radius: 5px !important;
    }

    ::v-deep .ql-editor.ql-blank::before {
      font-style: normal !important;
      color: rgba(94, 86, 105, 0.68) !important;
      font-size: 0.9rem !important;
    }
    ::v-deep .ql-container.ql-snow.ql-disabled {
      .ql-editor {
        color: rgba(94, 86, 105, 0.38) !important;
      }
    }
  }
}
</style>
