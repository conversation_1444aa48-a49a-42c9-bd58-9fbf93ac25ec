<template>
  <div>
    <bread-crumb>
      <template v-slot:left>
        <v-chip
          small
          label
          style="font-size: 12px"
          class="color-base ml-3 bg-btn"
        >
          {{ $t('global.pagination.total') }} {{ tableData.length }}
        </v-chip>
      </template>
    </bread-crumb>
    <!-- <div class="d-flex align-center justify-space-between pt-0 px-4 mb-3">
      <v-btn
        small
        v-has:canvas-add
        elevation="0"
        color="primary"
        @click="addCard"
      >
        <span>
          {{ $t('action.add') }}
        </span>
      </v-btn>
      <div class="d-flex align-center">
        <table-search
          :searchList="searchList"
          :searchQuery="query"
          @search="$_search"
        ></table-search>
      </div>
    </div> -->
    <div
      v-if="tableData.length > 0"
      :style="{ height: `calc(100vh - ${topHeaderHeight + 52}px)` }"
      class="overflow-auto scroll-bar-bg"
    >
      <div class="py-0 px-2 mt-n1 cards-container">
        <div class="pa-2" v-for="item in tableData" :key="item.id">
          <v-card class="w-100 pa-0" ripple>
            <v-card-text class="w-100 pa-0">
              <div
                class="w-100 cursor-pointer"
                style="height: 175px"
                @click="goShare(item)"
              >
                <v-img
                  height="100%"
                  :src="require(`@/assets/images/bg5.png`)"
                ></v-img>
              </div>
              <div class="pa-1">
                <div class="d-flex align-center">
                  <div
                    class="text-content text-overflow-hide text--primary font-weight-semibold-light"
                    style="max-width: 90%"
                  >
                    {{ item.name }}
                  </div>

                  <div
                    v-has:canvas-edit
                    class="ml-2 cursor-pointer"
                    @click.stop="onEdit(item)"
                  >
                    <vsoc-icon
                      v-show-tips="$t('grid.edit1')"
                      type="fill"
                      icon="icon-bianjimingcheng"
                      class="accent--text primary--hover"
                      size="1.166667rem"
                    ></vsoc-icon>
                  </div>

                  <div
                    v-has:canvas-share
                    class="ml-3 cursor-pointer"
                    @click.stop="shareScreen(item)"
                  >
                    <vsoc-icon
                      v-show-tips="$t('grid.share.btn')"
                      type="fill"
                      icon="icon-fenxiang"
                      class="accent--text primary--hover"
                      size="1.166667rem"
                    ></vsoc-icon>
                  </div>
                </div>
                <div style="line-height: 20px">{{ item.updateDate }}</div>
              </div>
              <!-- <div class="d-flex btn-box">
                <div
                  class="del-btn cursor-pointer mr-2"
                  v-has:layout-edit
                  @click.stop="goChart(item)"
                >
                  <vsoc-icon
                    v-show-tips="$t('grid.edit2')"
                    type="fill"
                    class="action-btn1"
                    size="x-large"
                    icon="icon-bianji"
                  ></vsoc-icon>
                </div>
                <div
                  class="del-btn cursor-pointer"
                  v-has:canvas-del
                  @click.stop="onDelete(item)"
                >
                  <vsoc-icon
                    v-show-tips="$t('grid.delete.title')"
                    type="fill"
                    class="action-btn1"
                    size="x-large"
                    icon="icon-shanchu"
                  ></vsoc-icon>
                </div>
              </div> -->
            </v-card-text>
          </v-card>
        </div>
      </div>
    </div>

    <div
      v-if="tableLoading === false && tableData.length === 0"
      style="height: 70vh"
      class="d-flex flex-column justify-center align-center"
    >
      <v-img
        max-width="22rem"
        max-height="22rem"
        contain
        :src="require('@/assets/images/tree-empty.png')"
      ></v-img>
      <div v-if="query.name">
        <div class="text--primary text-xxl">
          {{ $t('model.empty', { name: query.name }) }}
        </div>
        <div class="text-ml mt-1">{{ $t('model.hint') }}</div>
      </div>
      <div v-else class="text-ml mt-1">{{ $t('global.noData') }}</div>
    </div>
    <add-dialog
      ref="addDialog"
      :item="editForm"
      :mode="ops"
      @refresh="$_search"
    ></add-dialog>

    <vsoc-drawer v-model="showShare" title="分享" hideFooter>
      <div class="mt-4">
        <v-row class="pl-4 pr-4 d-flex align-center">
          <v-text-field
            outlined
            v-model="shareToken"
            label="Token"
            color="primary"
            dense
            hide-details
          >
          </v-text-field>
          <v-btn
            class="ml-3"
            small
            elevation="0"
            color="primary"
            @click.stop="changeShareToken"
          >
            生成链接
          </v-btn>
        </v-row>
        <v-row class="pl-4 pr-4">
          <div class="mt-6 item-color item-text text--primary">
            {{ $t('grid.headers.url') }}
          </div>
          <div class="mt-2 item-color item-text1">
            {{ $t('grid.share.tip') }}
          </div>
          <v-text-field
            class="mt-6"
            outlined
            v-model="shareUrl"
            dense
            hide-details
            :disabled="!shareUrl"
          >
          </v-text-field>
          <v-btn
            small
            class="mt-6 w-100"
            elevation="0"
            color="primary"
            v-copy="shareUrl"
            :disabled="!shareUrl"
          >
            {{ $t('grid.share.copyBtn') }}
          </v-btn>
        </v-row>
      </div>
    </vsoc-drawer>
  </div>
</template>

<script>
import { delCanvas, getCanvas } from '@/api/grid/index'
import breadCrumb from '@/components/bread-crumb/index'
import TableSearch from '@/components/TableSearch/index.vue'
import VsocDrawer from '@/components/VsocDrawer.vue'
import AddDialog from './AddDialog.vue'
export default {
  name: 'CanvasIndex',
  components: {
    breadCrumb,
    AddDialog,
    TableSearch,
    VsocDrawer,
  },
  data() {
    return {
      query: {
        name: '',
      },
      tableData: [],
      tableLoading: false,
      editForm: {},
      ops: '',
      showShare: false,
      shareUrl: '',
      shareToken: '',
      shareId: '',
    }
  },
  computed: {
    searchList() {
      return [
        {
          type: 'input',
          value: 'name',
          text: this.$t('grid.headers.name'),
        },
      ]
    },
    topHeaderHeight() {
      return this.$store.getters['global/getTopHeaderHeight']
    },
  },

  mounted() {
    this.$_getTableData()
  },
  methods: {
    shareScreen(item) {
      this.showShare = true
      this.shareToken = ''
      this.shareUrl = ''
      this.shareId = item.id
    },
    changeShareToken() {
      if (!this.shareToken) {
        return this.$notify.info('error', this.$t('grid.hint'))
      }
      // http://172.20.1.141:8090/vsocwebdev15/grid/shareCanvas?id=1997103293075030016&token=bearer 12110e85-1d55-4646-87be-d32461e519c2
      this.shareUrl =
        window.location.href?.split('grid')[0] +
        `grid/shareCanvas?id=${this.shareId}&token=${this.shareToken}`
      this.$notify.info('success', this.$t('grid.hint1'))
    },
    onDelete(item) {
      this.$swal({
        title: this.$t('grid.delete.title'),
        text: this.$t('grid.delete.text', [item.name]),
        icon: 'warning',
        reverseButtons: true,
        showCancelButton: true,
        confirmButtonText: this.$t('action.confirm'),
        cancelButtonText: this.$t('action.cancel'),
        customClass: {
          confirmButton: 'sweet-btn-primary',
        },
      }).then(async result => {
        if (result.isConfirmed) {
          try {
            const res = await delCanvas({ id: item.id })
            if (res.code === 200) {
              this.$notify.info(
                'success',
                this.$t('global.hint.del', [this.$t('grid.currentTitle')]),
              )
            }
            this.$_search()
          } catch (e) {
            console.error(`删除画布错误：${e}`)
          }
        }
      })
    },
    onEdit(record) {
      this.ops = 'edit'
      this.editForm = { ...record }
      this.$refs.addDialog.isDrawerShow = true
    },
    addCard() {
      this.ops = 'new'
      this.$refs.addDialog.isDrawerShow = true
    },
    goChart(item) {
      this.$router.push(`/grid?id=${item.id}`)
    },
    goShare(item) {
      if (this.$route.meta.buttonInfo['canvas-detail']) {
        this.$router.push(`/grid/shareCanvas?id=${item.id}`)
      }
    },
    onClear() {
      this.query.name = ''
      this.$_search()
    },
    $_search() {
      this.query.pageNum = 1
      this.$_getTableData()
    },
    async $_getTableData() {
      try {
        this.tableLoading = true
        this.tableData = []
        const res = await getCanvas(this.query)
        setTimeout(() => {
          this.tableData = res.data
          this.tableDataTotal = res.data.length
          this.tableLoading = false
        }, 300)
      } catch (e) {
        this.tableLoading = false
        console.error(`获取画布管理管理：${e}`)
      }
    },
  },
}
</script>
<style scoped lang="scss">
.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(326px, 1fr));
  width: auto;
}

@media (min-width: 1600px) {
  .cards-container {
    grid-template-columns: repeat(auto-fill, minmax(390px, 1fr));
  }
}
// .v-card:hover {
//   box-shadow: 0 -3px 0 0 $primary inset, 3px 3px 10px 0px rgba(0, 0, 0, 10%) !important;
// }
.primary--hover:hover {
  color: $primary !important;
}
.v-card {
  position: relative;
  .btn-box {
    position: absolute;
    top: 8px;
    right: 8px;
    opacity: 0;
  }
  .del-btn {
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 4px;
    line-height: 32px;
    cursor: pointer;
    color: #ffffff;
    .action-btn1 {
      justify-content: center;
    }
  }
  .del-btn:hover {
    background: $primary !important;
    color: #ffffff !important;
  }
}
.v-card:hover .btn-box {
  opacity: 1;
}
</style>
