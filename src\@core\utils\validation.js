import { i18n } from '@/plugins/i18n'
import { isEmpty } from './index'
//判断是否为外链
export function isExternal(path) {
  return /^((https|http)?:|mailto:|tel:)/.test(path)
}
// This field is required
export const required = (value, name = i18n.t('validation.defaultName')) =>
  !!value?.toString().trim() || i18n.t('validation.required', [name])

export const checkChinese = (
  value = '',
  name = i18n.t('validation.defaultName'),
) => {
  let reg = new RegExp('[\\u4E00-\\u9FFF]+', 'g')
  if (reg.test(value)) {
    return i18n.t('validation.chineseValidate', [name])
  }
  return true
}

export const min = (
  value,
  name = i18n.t('validation.defaultName'),
  minLen = 8,
) =>
  (value && value.length >= minLen) ||
  i18n.t('validation.minLen', { minLen, name })

export const max = (value, length) => {
  if (!value) return true
  return value.length <= length || i18n.t('validation.maxLen', [length])
}
export const emailValidator = value => {
  if (isEmpty(value)) {
    return true
  }

  // eslint-disable-next-line
  const re =
    /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/

  if (Array.isArray(value)) {
    return value.every(val => re.test(String(val)))
  }
  // The Email field must be a valid email
  return re.test(String(value)) || i18n.t('validation.email')
}

export const passwordValidator = password => {
  /* eslint-disable no-useless-escape */
  const regExp = /(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%&*()]).{8,}/
  /* eslint-enable no-useless-escape */
  const validPassword = regExp.test(password)
  // Field must contain at least one uppercase, lowercase, special character and digit with min 8 chars
  return (
    // eslint-disable-next-line operator-linebreak
    validPassword ||
    '字段必须至少包含一个大写字母、小写字母、特殊字符和至少 8 个字符的数字'
  )
}

export const confirmedValidator = (value, target) =>
  // The Confirm Password field confirmation does not match
  // eslint-disable-next-line implicit-arrow-linebreak
  value === target || i18n.t('validation.matchPassword')

export const between = (value, min, max) => () => {
  const valueAsNumber = Number(value)

  return (
    (Number(min) <= valueAsNumber && Number(max) >= valueAsNumber) ||
    `Enter number between ${min} and ${max}`
  )
}

export const integerValidator = (
  value,
  name = i18n.t('validation.defaultName'),
) => {
  if (isEmpty(value)) {
    return true
  }

  if (Array.isArray(value)) {
    return value.every(val => /^-?[0-9]+$/.test(String(val)))
  }
  // This field must be an integer
  // `${name}必须是整数`
  return /^-?[0-9]+$/.test(String(value)) || i18n.t('validation.int', { name })
}

export const regexValidator = (value, regex) => {
  if (isEmpty(value)) {
    return true
  }

  let regeX = regex
  if (typeof regeX === 'string') {
    regeX = new RegExp(regeX)
  }

  if (Array.isArray(value)) {
    return value.every(val => regexValidator(val, { regeX }))
  }

  return regeX.test(String(value)) || 'The Regex field format is invalid'
}

export const alphaValidator = value => {
  if (isEmpty(value)) {
    return true
  }

  // const valueAsString = String(value)

  return (
    /^[A-Z]*$/i.test(String(value)) ||
    'The Alpha field may only contain alphabetic characters'
  )
}

// 多个url，用逗号拼接
export const urlsValidator = value => {
  if (value === undefined || value === null || value.length === 0) {
    return true
  }
  /* eslint-disable no-useless-escape */
  const re =
    /^(http[s]?:\/\/){0,1}(www\.){0,1}[a-zA-Z0-9\.\-]+\.[a-zA-Z]{2,5}[\.]{0,1}(,(http[s]?:\/\/){0,1}(www\.){0,1}[a-zA-Z0-9\.\-]+\.[a-zA-Z]{2,5}[\.]{0,1})*$/

  return re.test(value) || i18n.t('validation.url')
}

export const urlValidator = value => {
  if (value === undefined || value === null || value.length === 0) {
    return true
  }
  /* eslint-disable no-useless-escape */
  const re =
    /^(http[s]?:\/\/){0,1}(www\.){0,1}[a-zA-Z0-9\.\-]+\.[a-zA-Z]{2,5}[\.]{0,1}/

  return re.test(value) || 'URL is invalid'
}

export const lengthValidator = (value, length) => {
  if (isEmpty(value)) {
    return true
  }

  return (
    value.length === length ||
    `The Min Character field must be at least ${length} characters`
  )
}
export const alphaDashValidator = value => {
  if (isEmpty(value)) {
    return true
  }

  const valueAsString = String(value)

  return /^[0-9A-Z_-]*$/i.test(valueAsString) || 'All Character is not valid'
}
// 去掉html字符串中的所有标签元素
export const delHtmlTag = str => {
  return str.replace(/<[^>]+>/g, '')
}
