@media (min-width: 3560px) {
  html {
    font-size: 30px;
  }
}
@media (max-width: 2560px) {
  html {
    font-size: 14px !important;
  }
}
@media (max-width: 2200px) {
  html {
    font-size: 14px !important;
  }
}
@media (max-width: 1920px) {
  html {
    font-size: 14px !important;
  }
}
@media (max-width: 1600px) {
  html {
    font-size: 12px !important;
  }
}
@media (max-width: 1440px) {
  html {
    font-size: 12px !important;
  }
}
@media (max-width: 1240px) {
  html {
    font-size: 12px !important;
  }
}
@media (max-width: 768px) {
  html {
    font-size: 12px;
  }
}

/* chrome & safari 浏览器 IE不支持修改*/
/*滚动条整体部分,必须要设置*/
/*滚动条的轨道*/
// ::-webkit-scrollbar {
//   width: 8px; /*no*/
//   height: 8px; /*no*/
// }
// ::-webkit-scrollbar-track {
//   background: transparent;
//   border-radius: 2px;
// }
// /*滚动条的滑块按钮*/
// ::-webkit-scrollbar-thumb {
//   background: rgba(191, 191, 191, 0.5);
//   border-radius: 10px;
// }

// ::-webkit-scrollbar-thumb:hover {
//   background: rgba(191, 191, 191, 0.9);
// }

.btn-group-tab {
  .v-btn--active {
    position: relative;
    &::after {
      display: block !important;
      box-shadow: none;
      content: '';
      position: absolute;
      left: 0;
      bottom: 0;
      height: 2px;
      width: 100%;
      background-color: currentColor;
    }
  }
}
