import { request, vsocPath } from '../../util/request'

export const getRiskAssetAnalyse = function (data) {
  return request({
    url: `${vsocPath}/assetAlarm/findRiskAssetAnalyse`,
    method: 'post',
    loading: false,
    data,
  })
}

//获取影响资产
export const getFindAffectAsset = function (data) {
  return request({
    url: `${vsocPath}/assetAlarm/findAffectAsset`,
    method: 'post',
    loading: false,
    data,
  })
}

export const getAlarmEventAnalyse = function (data) {
  return request({
    url: `${vsocPath}/assetAlarm/findAlarmEventAnalyse`,
    method: 'post',
    loading: false,
    data,
  })
}

const controller = new AbortController()
export const getAlarmLocation = function (data) {
  controller.abort()
  return request({
    url: `${vsocPath}/assetAlarm/findAlarmLocation`,
    method: 'post',
    loading: false,
    signal: controller.signal,
    data,
  })
}

// 资产实例-idps统计
export const getIdpsStatistics = function (data) {
  return request({
    url: `${vsocPath}/vehicleAsset/idpsStatistics`,
    method: 'post',
    loading: false,
    data,
  })
}
