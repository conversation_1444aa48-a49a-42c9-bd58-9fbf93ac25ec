const profile = {
  currentTitle: '特征',
  headers: {
    signalName: '特征名称',
    dataType: '数据类型',
    featureType: '特征类型',
    assertCount: '资产总数',
    differentNum: '不同值',
    cronExpression: '执行策略',
    lastStudy: '上次学习阶段',
    active: '状态',
    dataRangeUnit: '数据范围',
    rangeValue: '范围值',
    day: '天',
    totalSample: '样本总量',
    vehicleModels: '车型范围',
    timeRange: '时间范围',
    unlimited: '无限制',
  },
  lastDay: '最近1天',
  lastDay1: '最近{0}天',
  historyHeaders: {
    id: '编号Id',
    signalName: '信号名称',
    analyseStatus: '分析状态',
    startDate: '开始时间',
    endDate: '结束时间',
    remark: '备注',
    proportion: '占比',
    count: '数量',
    uniqueVin: '唯一车辆总数',
    categorical: '分类',
    numeric: '数值',
    model: '车型',
    emptyData: '过滤空数据',
  },
  btn: {
    add: '新增特征',
    edit: '编辑特征',
    detail: '特征详情',
    history: '查看历史',
  },
  hint: {
    tip: '在本节中，您可以观察数字孪生中每个选定信号的值分布。对于每个配置文件，您可以定义将在给定资产中触发异常的意外值。',
    tip1: '请指定总资产中资产的最小百分位数',
    tip2: '此最小百分位数将被视为配置文件的预期',
    tip3: '任何低于此阈值的信号值都将触发“非预期值”异常',
    tip4: '最大100',
  },
  detail: {
    signal: '选择信号',
    thresholdSetting: '异常阈值',
    cronExpression: '定时表达式',
    profile: '特征值列表',
    unexpectedvalue: '非预期值',
    expectedvalue: '预期值',
    automatic: '自动',
    thresholdbaseline: '推荐值归类',
    value: '值',
    classified: '归类为',
    Assets: '资产数量',
    tip1: '本节介绍队列中信号的值',
    tip2: '通过确定的基线对潜在异常值的分析，将“意外值”显示为异常',
    tip3: '然后，团队可以决定是执行处置还是允许活动',
    tip4: '并选择将来应如何管理此检测到的值',
  },
  table: {
    tip1: '在车辆通信内中观察到的值',
    tip2: '将确定收集的信号数据值是“非预期值”还是“预期值”',
    tip3: '注意：您可以手动定义分类值以确定值是“预期”/“异常”',
    tip4: '或者：让定义的阈值保持平台的建议（“自动”）',
    tip5: '枚举值：1.非预期值/2.预期值/3.非预期值(Auto)/4.预期值(Auto)',
    tip6: '推荐值归类：由后台通过定义的阈值或者机器学习对值进行分类',
    tip7: '在给定分类或分组下报告值的观察到的数量',
  },
  notity: {
    tip1: '请先选择信号',
    tip2: '特征值已存在',
  },
  delete: {
    title: '删除值（暂存操作）',
    text: '确认删除特征值:{0}?',
    tip: '已从暂存表中删除',
  },
  detele1: {
    title: '删除特征',
    text: '确认删除特征:{0}?',
  },
}
export default profile
