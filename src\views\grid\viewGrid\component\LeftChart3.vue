<template>
  <div class="box">
    <!-- <dv-charts :option="option1" class="box-chart" /> -->
    <div class="box-header box-position-top">
      {{ $t('screen.alertTrend') }}
      <span class="box-header-num">{{ total | numberToFormat }}</span>
    </div>
    <template v-if="alarmTrend.length !== 0">
      <vsoc-chart
        echartId="alert-line"
        class="box-chart d-flex align-center"
        :option="option"
      ></vsoc-chart>
    </template>
    <template v-else>
      <div
        class="box-chart d-flex align-center color--primary justify-center fs-16"
      >
        None
      </div>
    </template>
  </div>
</template>

<script>
import VsocChart from '@/components/VsocChart.vue'
import { lineOptionFn } from './chart'
const xList = [
  '04/11',
  '04/12',
  '04/13',
  '04/14',
  '04/15',
  '04/16',
  '04/17',
  '04/18',
]

export default {
  name: 'LeftChart3',
  props: {
    alarmTrend: {
      tyep: Array,
      default: () => {
        return []
      },
    },
    xList: Array,
    total: {
      type: [Number, String],
      default: 0,
    },
  },
  components: {
    VsocChart,
  },
  data() {
    return {
      // option: {},
      myChart: undefined,
    }
  },
  // mounted() {
  //   this.onDraw()
  // },
  computed: {
    yList() {
      let alertLevel = this.$store.state.enums.enums.AlarmLevel
      if (!alertLevel || alertLevel.length === 0) {
        return []
      }
      let yData = []
      this.alarmTrend.forEach(item => {
        let obj = {
          name: alertLevel[item.level].text,
          color: alertLevel[item.level].color,
          data: item.arrays.map(x => x.number),
        }
        yData.push(obj)
      })
      return yData
      // return [
      //   {
      //     name: alertLevel[0].text,
      //     // color: '#FF4C51',
      //     color: '#FF385D',
      //     data: [139, 99, 10, 17, 0, 2, 13, 29],
      //   },
      //   {
      //     name: alertLevel[1].text,
      //     // color: '#fb8c00',
      //     color: '#FF6B00',
      //     data: [61, 52, 0, 5, 0, 0, 15, 7],
      //   },
      //   {
      //     name: alertLevel[2].text,
      //     // color: '#EEC900',
      //     color: '#F0DA4C',
      //     data: [4, 5, 0, 0, 0, 0, 0, 5],
      //   },
      //   {
      //     name: alertLevel[3].text,
      //     // color: '#3caea3',
      //     color: '#32FDB8',
      //     data: [2, 34, 0, 0, 0, 0, 7, 1],
      //   },
      // ]
    },
    option() {
      return lineOptionFn(this.xList, this.yList)
    },
  },
  // methods: {
  //   onResize() {
  //     this.$nextTick(() => {
  //       this.option = lineOptionFn(xList, this.yList)
  //       this.myChart.setOption(this.option)
  //       this.myChart.resize()
  //     })
  //   },
  //   onDraw() {
  //     const ele = document.getElementById('alert-line')
  //     this.myChart = this.$echarts.init(ele, {
  //       height: 'auto',
  //       width: 'auto',
  //     })
  //     this.option = lineOptionFn(xList, this.yList)
  //     this.myChart.setOption(this.option)
  //   },
  // },
}
</script>
