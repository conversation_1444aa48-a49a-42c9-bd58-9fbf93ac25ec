<template>
  <v-card style="height: 20rem" class="d-flex flex-column">
    <v-card-title>
      {{ title }}
      <slot
        v-if="echartId === 'alert-count-chart'"
        name="title"
        :allCount="allCount"
      >
        <span class="font-weight-normal me-1 ml-2">
          <!-- {{
          allCount | numberToFormat
        }} -->
        </span>
      </slot>
      <slot v-else name="title" :sum="sum">
        <span class="font-weight-normal me-1 ml-2">
          <!-- {{
          sum | numberToFormat
        }} -->
        </span>
      </slot>
    </v-card-title>
    <div
      v-empty-chart="list.length === 0"
      class="flex-1 d-flex justify-center align-center w-100"
    >
      <slot name="text" :selected="selected" :list="list" :sum="sum">
        <!-- 插槽默认内容 -->
        <v-card-text
          class="text-no-wrap d-flex justify-center flex-column pr-0"
        >
          <div
            v-for="(item, index) in list"
            :key="index"
            class="text--primary text-ml d-flex ml-2 mb-4"
          >
            <vsoc-icon size="x-large" :color="item.color" :icon="item.icon" />
            <div
              class="font-weight-normal ml-1 ml-xl-3 text-capitalize text--secondary"
              style="width: 5rem"
            >
              {{ item.name }}
            </div>
            <div style="width: 4.8rem" class="text-no-wrap mx-xl-4">
              {{ item.value | numberToFormat }}
            </div>
            <div style="width: 4.5rem" class="text--secondary text-no-wrap">
              {{ item.percent }}
            </div>
          </div>
        </v-card-text>
      </slot>
      <v-card-text class="h-100 pl-0">
        <vsoc-chart
          ref="echart"
          class="echart"
          :echart-id="echartId"
          :option="chartOption"
          @highlight="onHighlight"
          @finished="onFinished"
          @click="onClick"
        ></vsoc-chart>
      </v-card-text>
    </div>
  </v-card>
</template>

<script>
import VsocChart from '@/components/VsocChart'
import { numberToFormat } from '@/util/filters'
import themeConfig from '@themeConfig'
import lodash from 'lodash'

export default {
  components: {
    VsocChart,
  },
  props: {
    title: {
      type: String,
      default: () => '标题',
    },

    list: {
      type: Array,
      default: () => [],
    },
    echartId: {
      type: String,
      default: () => 'echart',
    },
    padding: {
      type: [Number, Array],
      default: () => [0, 0],
    },
    formatter: {
      type: Function,
      default(params) {
        return `{value|${numberToFormat(params.value)}}`
      },
    },
  },
  data() {
    return {
      myChart: '',
      sum: 0,
      allCount: 0,
      percent: 0,
      dataIndex: 0,
      selected: {
        name: '暂无数据',
        value: 0,
        color: '#ffffff',
      },

      backgroundColor: '#b087f8',
      textColor: '#fff',
      rich: {
        name: {
          fontSize: 12,
          fontWeight: 400,
          padding: this.padding,
          color: '#aaa5c0',
          backgroundColor: '#312d4b',
        },
        value: {
          lineHeight: 32,
          fontSize: 24,
          padding: this.padding,
          verticalAlign: 'bottom',
          fontWeight: 'normal',
          color: '#cfcce5',
          backgroundColor: '#312d4b',
        },
      },
      emphasis: {
        show: true,
        scale: true,
        scaleSize: 0,
        label: {
          formatter: params => this.formatter(params),

          // // 切换非默认选项配置数据展示
          // if (params.dataIndex != 0) {
          //   return `\n{value|${params.value}k}` + `\n{name|${params.name}}`
          // }
        },
      },
      label: {
        show: true,
        position: 'center',

        lineHeight: 16,

        formatter: params => '',

        // if (params.dataIndex === 0) {
        //   return `\n{value|${params.value}k}\n{name|${params.name}}`
        // }
      },
      itemStyle: {
        borderWidth: 8,
        borderColor: 'transparent',
      },

      chartOption: {
        // color: ['#9155fd', '#d4d5d7', '#ffb400', '#ff4c51'],
        color: this.colorList,

        legend: {
          top: '5%',
          left: 'center',
          show: false,
        },

        series: [
          {
            name: '告警级别',
            type: 'pie',
            radius: ['67%', '89%'],
            avoidLabelOverlap: true,
            percentPrecision: 0,
            minAngle: 6,
            stillShowZeroSum: true,

            label: {},
            labelLine: {
              show: false,
            },

            // 数据倒叙排序
            // data: [
            //   { value: 85, name: 'series-1' },
            //   { value: 20, name: 'series-2' },
            //   { value: 30, name: 'series-3' },
            //   { value: 50, name: 'series-4' },
            // ],
            data: [],
          },
        ],
      },
    }
  },
  watch: {
    '$vuetify.theme.dark': {
      handler(value) {
        if (value) {
          this.backgroundColor = themeConfig.themes.dark.backgroundColor
          this.textColor = themeConfig.themes.dark.color
        } else {
          this.backgroundColor = themeConfig.themes.light.backgroundColor
          this.textColor = themeConfig.themes.light.color
        }
        this.resetColor()
      },
      deep: true,
      immediate: true,
    },
    list: {
      handler(newArr) {
        if (this.echartId === 'alert-count-chart' && newArr.length) {
          this.allCount = newArr[0].allCount
        }
        const sortArr = lodash.orderBy(newArr, ['value'], ['desc'])
        this.chartOption.series[0].data = sortArr.filter(v => v.value !== 0)
        this.chartOption.color = sortArr.map(v => v.color)

        // this.selected = Object.assign(sortArr[0], { color: this.colorList[0] })
        this.selected = sortArr[0]
        const valueArr = sortArr.map(item => item.value)
        this.sum = valueArr.length > 0 && valueArr.reduce((x, y) => x + y)
        if (this.sum > 0) {
          sortArr.forEach(v => {
            v.percent = Math.round((v.value / this.sum) * 100) + '%'
          })
        }
        this.percent =
          sortArr[0]?.percent ||
          `${
            this.sum > 0 && Math.round((this.selected.value / this.sum) * 100)
          }%`
        if (newArr.length === 0) {
          this.percent = '0%'
          this.selected = {
            name: '暂无数据',
            value: 0,
            color: '#ffffff',
          }
        }
        if (this.myChart) {
          // 取消前一条高亮
          this.myChart.dispatchAction({
            type: 'downplay',
            seriesIndex: 0,
            dataIndex: this.dataIndex,
          })
          this.dataIndex = 0
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    onClick(event, myChart) {
      this.$emit('click', event, myChart)
    },
    onHighlight(obj) {
      this.selected = Object.assign(obj.data, { color: obj.color })
      this.percent =
        this.selected?.percent ||
        `${this.sum > 0 && Math.round((this.selected.value / this.sum) * 100)}%`
      if (this.dataIndex !== obj.dataIndex) {
        // 取消前一条高亮
        this.myChart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: this.dataIndex,
        })
        this.dataIndex = obj.dataIndex
      }
    },
    onFinished(myChart) {
      this.myChart = myChart

      // 默认第一条数据高亮
      myChart.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: this.dataIndex,
      })
    },
    resetColor() {
      this.rich.name.color = this.textColor
      this.rich.name.backgroundColor = ''
      this.rich.value.color = this.textColor
      this.rich.value.backgroundColor = ''

      this.itemStyle.borderColor = this.backgroundColor
      Object.assign(this.label, { rich: this.rich })
      Object.assign(this.emphasis, { rich: this.rich })
      Object.assign(
        this.chartOption.series[0],
        { label: this.label },
        { itemStyle: this.itemStyle },
        { emphasis: this.emphasis },
      )
    },
  },
}
</script>
