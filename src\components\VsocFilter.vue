<template>
  <div
    ref="filterList"
    class="filterList-box rounded-0 d-flex align-center justify-lg-space-between"
    :style="{ padding: filterList.length === 0 ? '6px 0' : '6px 16px' }"
  >
    <v-divider
      v-show="filterList.length !== 0"
      vertical
      class="primary my-2"
    ></v-divider>
    <span
      v-show="filterList.length !== 0"
      class="text-primary text-root font-weight-semibold con-title pl-2"
    >
      {{ $t('action.queryConditions') }}：</span
    >
    <v-chip-group
      v-if="filterList.length"
      class="pa-0 ma-0 flex-1 overflow-hidden"
    >
      <v-chip
        v-for="(item, index) in filterList"
        :key="index"
        small
        pill
        light
        dense
        class="pa-0 filter-chip active-chip bg-white px-4"
        :close="item.close === false ? false : true"
        close-icon="mdi-close"
        @click="openDraw(item)"
        @click:close="$_clearFilter(item, index)"
      >
        <slot name="text" :item="item"
          >{{ $t(item.label) }}：{{ deal(item) }}</slot
        >
      </v-chip>
    </v-chip-group>
    <v-btn
      v-if="filterList.length && isShowReset"
      color="primary"
      x-small
      rounded
      class="px-4 py-1 text-base"
      @click="onReset"
    >
      <v-icon left> mdi-eraser </v-icon>
      {{ $t('action.clear') }}
    </v-btn>
  </div>
</template>

<script>
import { clearFilterItem } from '@/util/utils'
let _this = ''
export default {
  name: 'VsocFilter',
  props: {
    filterList: {
      type: Array,
      default: () => [],
    },
    mode: {
      type: String,
      default: () => 'default',
    },
    clearType: {
      type: String,
      default: () => '',
    },
    isShowReset: {
      type: Boolean,
      default: () => true,
    },
  },
  computed: {
    offsetHeight() {
      return this.filterList && this.$refs.filterList
        ? this.$refs.filterList.offsetHeight
        : 0
    },
  },
  mounted() {
    if (this.mode === 'default') {
      _this = this.$parent.$parent
    } else {
      _this = this.$parent
    }
  },
  methods: {
    //如果有mapkey并且value是string类型
    deal(item) {
      let text = ''
      if (
        item.mapKey &&
        typeof item.value === 'string' &&
        _this[item.mapKey] &&
        _this[item.mapKey][item.value]
      ) {
        text = _this[item.mapKey][item.value].text
      } else {
        text = item.text
      }
      return text
    },
    openDraw(item) {
      if (
        item.type === 'Array' &&
        Array.isArray(item.value) &&
        item.value.length > 1
      ) {
        _this.showDoQuery && _this.showDoQuery()
      }
    },
    // 清除某个查询条件
    $_clearFilter(item) {
      if (this.mode !== 'default') {
        this.$emit('clear', item)
        return
      }
      const bool = clearFilterItem.call(_this, item)
      if (!bool) {
        _this.$_search()
      }
    },
    onReset() {
      if (this.mode !== 'default') {
        this.$emit('reset')
        return
      }
      if (this.clearType === 'initQuery') {
        const assetType = _this.$data.query?.assetType
        Object.assign(_this.$data.query, _this.$options.data.call(_this).query)
        _this.initQuery({ assetType: assetType })
      } else {
        Object.assign(_this.$data.query, _this.$options.data.call(_this).query)
        _this.$_search()
      }
    },
  },
}
</script>
<style scoped lang="scss">
::v-deep .v-chip {
  color: #1f2533 !important;
}
.v-divider--vertical {
  border-left-width: 0.18rem !important;
}
// .con-title {
//   border-left: 2px solid red;

//   position: relative;
//   &::before {
//     content: '';
//     width: 2px;
//     height: 12px;
//     background: $primary;
//     position: absolute;
//     left: 0;
//     top: 0;
//     bottom: 0;
//     margin: auto;
//   }
// }
// .filterList-box {
//   ::v-deep .v-chip-group .v-chip {
//     margin: 4px 0px 4px 16px !important;
//     height: 26px !important;
//     line-height: 26px !important;
//   }
// }
</style>
