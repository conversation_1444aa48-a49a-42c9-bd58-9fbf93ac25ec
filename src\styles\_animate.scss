$r: 0.25em;
$offset: 8 * $r;
.loader-circle,
.loader-circle:before,
.loader-circle:after {
  border-radius: 50%;
  display: inline-block;
  width: $r * 2;
  height: $r * 2;
  //   background: $foreground;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation: loading 1.8s infinite ease-in-out;
  animation: loading 1.8s infinite ease-in-out;
}

.loader-circle {
  color: $white;
  //   font-size: 10px;
  //   margin: 80px auto;
  margin: 0 6em;
  position: relative;
  top: -6 * $r;
  text-indent: -9999em;
  -webkit-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;

  &:before,
  &:after {
    content: '';
    position: absolute;
    top: 0;
  }

  &:before {
    left: -$offset;
    -webkit-animation-delay: -0.32s;
    animation-delay: -0.32s;
  }

  &:after {
    left: $offset;
  }
}

@mixin loading-frames {
  0%,
  80%,
  100% {
    box-shadow: -3em $offset 0 -1.3em;
  }

  40% {
    box-shadow: -3em $offset 0 0;
  }
}

@-webkit-keyframes loading {
  @include loading-frames;
}
@keyframes loading {
  @include loading-frames;
}
