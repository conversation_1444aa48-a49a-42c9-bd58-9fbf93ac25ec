import { deleteRequest, getRequest, postRequest, putRequest, vsocPath } from '../../util/request'

const MODULE_PATH = vsocPath

export function qeuryUserLike(user, page, size) {
  const param = { page, size }
  Object.assign(param, user)

  return getRequest(`${MODULE_PATH}/user/like/detail`, param)
}

export function createUser(user) {
  return postRequest(`${MODULE_PATH}/user`, user)
}

export function updateUser(id, user) {
  return putRequest(`${MODULE_PATH}/user/${id}`, user)
}

export function getUserDetail(id) {
  return getRequest(`${MODULE_PATH}/user/detail/${id}`)
}

export function deleteUser(ids) {
  return deleteRequest(`${MODULE_PATH}/user`, ids)
}
