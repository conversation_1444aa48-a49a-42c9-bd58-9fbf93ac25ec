const grid = {
  currentTitle: 'Canvas',
  headers: {
    name: 'Canvas Name',
    title: 'Canvas Title',
    titleEnName: 'Canvas English Title',
    time: 'Valid To',
    url: 'Share Link',
  },
  share: {
    copyBtn: 'Copy Sharing Link',
    btn: 'Enable Sharing',
    tip: 'Share the canvas with people without VSOC accounts through the following link',
    delTitle: 'Disable the sharing link?',
    delTip:
      'Existing links will be cancelled. It can be reactivated, but the link will change. Are you sure you want to perform this operation?',
  },

  screen: {
    edit: 'You are editing the dashboard',
    view: 'You are previewing the dashboard',
    btn: 'Edit Dashboard',
    btn1: 'Select Template',
    select: 'Active Charts',
    grid: 'Select default template',
  },
  tip: 'Please select the canvas background',

  bg1: 'Background 1',
  bg2: 'Background 2',
  bg3: 'Background 3',
  edit1: 'Edit Canvas',
  edit2: 'Design Canvas',
  add: 'New Canvas',
  delete: {
    title: 'Delete Canvas',
    text: 'Are you sure to delete the Canvas:{0}?',
  },
  hint: 'Token is required',
  link: 'Generate Link',
  hint1: 'Share link generated successfully',
}

export default grid
