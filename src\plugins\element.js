// import MySelect from '@/components/ElSelect/index.js'
import {
  Col,
  DatePicker,

  // Input,
  Option,
  Popover,
  Row,
  // Scrollbar,
  Select,
  Table,
  TableColumn,
  Tree,
  Upload,
} from 'element-ui'

import '@/styles/vendors/_el-date-picker.scss'
import '@/styles/vendors/_el-select.scss'
import '@/styles/vendors/_el-table.scss'
import '@/styles/vendors/_el-upload.scss'
import '@/styles/vendors/element-variables.scss'
import Vue from 'vue'

// import MySelect from '@/components/ElSelect/index.js'

Vue.use(Row)
Vue.use(Col)
Vue.use(DatePicker)

// Vue.use(Button)
Vue.use(Table)
Vue.use(TableColumn)

// Vue.use(Skeleton)
// Vue.use(SkeletonItem)
Vue.use(Upload)
Vue.use(Option)

// Vue.use(Tooltip)

Vue.use(Popover)

// Vue.use(Scrollbar)
Vue.use(Select)
Vue.use(Tree)
// Vue.use(Tag)
// Vue.use(Input)
// Vue.use(Tabs)
// Vue.use(TabPane)

// Vue.use(MySelect)

import eleLocale from 'element-ui/lib/locale'
import { i18n } from './i18n'
eleLocale.i18n((key, value) => i18n.t(key, value))
