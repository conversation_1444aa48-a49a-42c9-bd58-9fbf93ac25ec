<template>
  <div class="d-flex align-center">
    <div>{{ $t('infer.expression.everyDay') }}:</div>
    <v-icon size="1.25rem">mdi-clock-outline</v-icon>
    <div style="width: 360px">
      <v-select
        v-model="hours"
        class="mx-2"
        color="primary"
        dense
        outlined
        solo
        multiple
        chips
        :items="hourOptions"
        hide-details
        item-text="label"
        item-value="value"
        @change="emitChange"
        :menu-props="{ offsetY: true }"
      >
        <template v-slot:selection="{ item, index }">
          <v-chip
            v-if="index <= 3"
            color="primary"
            class="chip-box"
            close
            label
            size="x-small"
            @click:close="removeTags(index)"
          >
            <span class="text-caption">{{ item.label }}</span>
          </v-chip>
          <span v-if="index === 4" class="grey--text text-caption">
            (+{{ hours.length - 4 }})
          </span>
        </template>
      </v-select>
    </div>
    <!-- <el-select
      v-model="hours"
      class="mx-2"
      multiple
      filterable
      style="width: 30rem"
      @change="emitChange"
    >
      <el-option
        v-for="item in hourOptions"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      ></el-option>
    </el-select> -->
    <div>{{ $t('infer.expression.hour') }}</div>
    <div style="width: 100px">
      <v-select
        v-model="minute"
        class="mx-2"
        color="primary"
        dense
        disabled
        outlined
        solo
        :items="minuteOptions"
        item-text="label"
        item-value="value"
        hide-details
        @change="emitChange"
        :menu-props="{ offsetY: true }"
      >
      </v-select>
    </div>
    <div>{{ $t('infer.expression.minute') }}</div>
    <!-- <el-select
      v-model="minute"
      class="mx-2"
      filterable
      style="width: 6rem"
      @change="emitChange"
    >
      <el-option
        v-for="item in minuteOptions"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      ></el-option>
    </el-select> -->
  </div>
</template>

<script>
export default {
  name: 'CronDay',
  data() {
    return {
      hours: [0],
      minute: 0,
    }
  },
  computed: {
    hourOptions() {
      return Array.from(Array(24), (_, i) => {
        if (i < 10) {
          return {
            value: i,
            label: `0${i}`,
          }
        }

        return {
          value: i,
          label: i,
        }
      })
    },
    minuteOptions() {
      return Array.from(Array(60), (_, i) => {
        if (i < 10) {
          return {
            value: i,
            label: `0${i}`,
          }
        }

        return {
          value: i,
          label: i,
        }
      })
    },
    cronExp() {
      if (this.hours.length === 0) {
        return `0 ${this.minute} * * * ?`
      }

      return `0 ${this.minute} ${this.hours.join(',')} * * ?`
    },
  },
  methods: {
    init(value) {
      const tempArr = value.split(' ')
      this.minute = Number(tempArr[1])
      if (tempArr[2] === '*') {
        this.hours = []
      } else {
        const hourArr = tempArr[2].split(',')
        this.hours = hourArr.filter(v => v !== '').map(v => Number(v))
      }
    },
    emitChange() {
      this.$emit('change', this.cronExp)
    },
    removeTags(index) {
      this.hours.splice(index, 1)
      this.$emit('change', this.cronExp)
    },
  },
}
</script>
<style lang="scss" scoped></style>
