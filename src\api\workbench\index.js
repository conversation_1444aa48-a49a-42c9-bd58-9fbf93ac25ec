import { request, vsocPath } from '../../util/request'
export const getAlarmAndTick = function (data) {
  return request({
    url: `${vsocPath}/workTable/selectAlarmAndTick`,
    method: 'post',
    data,
  })
}

export const getDataProcessing = function (data) {
  return request({
    url: `${vsocPath}/workTable/dataProcessing`,
    method: 'post',
    data,
  })
  // return Promise.resolve({
  //   cause: null,
  //   data: [
  //     {
  //       number: 3,
  //       time: '2023-08-16',
  //     },
  //     {
  //       number: 1,
  //       time: '2023-08-17',
  //     },
  //     {
  //       number: 1,
  //       time: '2023-08-18',
  //     },
  //     {
  //       number: '0',
  //       time: '2023-08-19',
  //     },
  //     {
  //       number: '0',
  //       time: '2023-08-20',
  //     },
  //     {
  //       number: 2,
  //       time: '2023-08-21',
  //     },
  //     {
  //       number: 2,
  //       time: '2023-08-22',
  //     },
  //   ],
  //   code: 200,
  //   msg: '操作成功',
  // })
}

export const getAlarmStatus = function (data) {
  return request({
    url: `${vsocPath}/workTable/selectAlarmStatus`,
    method: 'post',
    data,
  })
}

export const getTicketStatus = function (data) {
  return request({
    url: `${vsocPath}/workTable/selectTicketStatus`,
    method: 'post',
    data,
  })
}

export const getAlarmTrend = function (data) {
  return request({
    url: `${vsocPath}/workTable/alarmTrend`,
    method: 'post',
    data,
  })
}

export const getTicketTrend = function (data) {
  return request({
    url: `${vsocPath}/workTable/ticketTrend`,
    method: 'post',
    data,
  })
}
