<template>
  <div>
    <bread-crumb></bread-crumb>

    <div no-gutters class="organization-box d-flex">
      <div ref="refCard" class="organization-box-left">
        <div class="px-6 pb-6 pt-0">
          <v-card-text
            class="px-0 py-0 mt-0 mb-0"
            :style="{ height: height + 'px' }"
          >
            <el-tree
              v-if="items.length"
              :labelText="$t('organization.hint.tip')"
              ref="elTree"
              :organizationFlag="true"
              :showCheckbox="false"
              :expandFlag="true"
              :treeData="items"
              :default-props="defaultProps"
              :menuItems="menuItems"
              @click="updateTree"
              @addItem="addItem"
            >
            </el-tree>
            <div
              v-else
              class="h-100 d-flex flex-column align-center justify-center"
            >
              <v-btn icon @click="addItem('', 1)" class="add-btn">
                <v-img
                  width="80px"
                  height="80px"
                  :src="require('@/assets/images/addOrg.png')"
                >
                </v-img>
                <!-- <v-icon size="80" color="$primary"> mdi-plus-box </v-icon> -->
              </v-btn>
              <div class="mt-10">{{ $t('organization.hint.addTip') }}</div>
              <div class="mt-2 secondary--text">
                {{ $t('organization.hint.noTip') }}
              </div>
            </div>
          </v-card-text>
        </div>

        <v-checkbox
          class="px-6 organization-checkbox mt-0 py-0"
          v-model="stateCheck"
          dense
          :disabled="items.length > 0 ? false : true"
          color="primary"
          :false-value="0"
          :true-value="1"
          hide-details
          :label="$t('organization.hint.showTip')"
          @change="getTree"
        ></v-checkbox>
      </div>

      <div class="organization-box-right flex-1 pt-5 pl-6 pr-4">
        <div v-if="treeSelect.length">
          <div class="d-flex justify-space-between align-center right-tile">
            <div class="text-xl font-weight-semibold">
              {{ treeSelect[0].name }}
            </div>
            <div>
              <v-menu
                v-if="query.state === '0'"
                offset-y
                left
                nudge-bottom="4"
                transition="scale-transition"
                content-class="menu-list"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-btn
                    class="px-2 py-0 d-flex align-center"
                    color="primary"
                    small
                    dark
                    text
                    v-bind="attrs"
                    v-on="on"
                    v-has:org-add
                  >
                    <vsoc-icon
                      icon="icon-xinzeng"
                      class="primary--text"
                      type="fill"
                      size="16px"
                    ></vsoc-icon>
                    <!-- <v-icon size="12px">mdi-plus-circle</v-icon> -->
                    <span class="ml-2 mr-3 text-content primary--text">{{
                      $t('action.add')
                    }}</span>
                    <v-icon size="16px">mdi-chevron-down</v-icon>
                  </v-btn>
                </template>

                <v-list>
                  <v-list-item
                    v-for="(item, i) in menuItems"
                    :key="i"
                    class="list-hover"
                    @click="addItem(item)"
                  >
                    <v-list-item-title>
                      {{ $t(item.title) }}
                    </v-list-item-title>
                  </v-list-item>
                </v-list>
              </v-menu>
            </div>
          </div>
          <v-form ref="form" v-model="valid" class="px-0 py-0">
            <v-row
              class="mx-0 my-0 px-0 py-0 mt-8 d-flex justify-space-between"
            >
              <v-col class="px-0 py-0 mr-6">
                <!-- <div class="mt-3 text-sm w-20">
                  {{ dealName(query.type) + '编码' }}
                </div> -->
                <v-text-field
                  v-model="query.departmentId"
                  :label="
                    dealName(query.type) +
                    '  ' +
                    $t('organization.headers.code')
                  "
                  color="primary"
                  dense
                  :rules="rules.departmentId"
                  hide-details="auto"
                  :disabled="query.state === '0' ? false : true"
                ></v-text-field>
              </v-col>
              <v-col class="px-0 py-0">
                <!-- <div class="mt-3 text-sm w-20">
                  {{ dealName(query.type) + '名称' }}
                </div> -->
                <v-text-field
                  v-model="query.departmentName"
                  :label="
                    dealName(query.type) +
                    '  ' +
                    $t('organization.headers.name')
                  "
                  color="primary"
                  dense
                  :rules="rules.departmentName"
                  hide-details="auto"
                  :disabled="query.state === '0' ? false : true"
                ></v-text-field>
              </v-col>
            </v-row>
            <v-row
              class="mx-0 my-0 px-0 py-0 mt-8 d-flex justify-space-between"
            >
              <v-col class="px-0 pt-0 mr-6">
                <v-autocomplete
                  v-model="query.isParent"
                  dense
                  :menu-props="{ offsetY: true }"
                  append-icon="mdi-chevron-down"
                  hide-details="auto"
                  :items="parentData"
                  :label="$t('organization.headers.parent')"
                  item-text="totalName"
                  item-value="id"
                  clearable
                  :disabled="query.state === '0' ? false : true"
                />

                <!-- <template v-slot:selection="{ item, index }"> -->
                <!-- <v-chip
                      close
                      @click:close="ticketInfo.dealUser = ''"
                      label
                      color="primary"
                    >
                      <span class="text-caption">{{ item.text }}</span>
                    </v-chip> -->
                <!-- </template> -->
                <!-- <el-select
                  v-model="query.isParent"
                  filterable
                  placeholder="上级"
                  :clearable="query.type == '1' ? false : true"
                  :disabled="query.state === '0' ? false : true"
                >
                  <el-option
                    v-for="item in parentData"
                    :key="item.id"
                    :label="item.totalName"
                    :value="item.id"
                  >
                  </el-option>
                </el-select> -->
              </v-col>
              <v-col class="px-0 pt-0">
                <v-autocomplete
                  v-if="query.type == '1'"
                  v-model="query.directorId"
                  dense
                  :menu-props="{ offsetY: true, maxHeight: 300 }"
                  append-icon="mdi-chevron-down"
                  hide-details="auto"
                  :items="userData"
                  :label="$t('organization.headers.manager')"
                  item-text="userName"
                  item-value="userId"
                  clearable
                  :disabled="query.state === '0' ? false : true"
                  :loading="isLoading"
                  :search-input.sync="search"
                  no-filter
                />
                <v-text-field
                  v-else
                  v-model="query.dingTalk"
                  :label="$t('organization.headers.DingTalkId')"
                  color="primary"
                  dense
                  hide-details="auto"
                ></v-text-field>
                <!-- <el-select
                  v-model="query.directorId"
                  filterable
                  remote
                  reserve-keyword
                  placeholder="部门负责人"
                  :remote-method="getUsers"
                  :loading="searchLoading"
                  clearable
                  @clear="clear"
                  :disabled="query.state === '0' ? false : true"
                >
                  <el-option
                    v-for="item in userData"
                    :key="item.userId"
                    :label="item.userName"
                    :value="item.userId"
                  >
                  </el-option>
                </el-select> -->
              </v-col>
            </v-row>
            <v-row
              v-if="query.type == '1'"
              class="mx-0 my-0 px-0 py-0 mt-8 d-flex justify-space-between"
            >
              <v-col class="px-0 pt-0 mr-6">
                <v-text-field
                  v-model="query.dingTalk"
                  :label="$t('organization.headers.DingTalkId')"
                  color="primary"
                  dense
                  hide-details="auto"
                ></v-text-field>
              </v-col>
              <v-col class="px-0 pt-0"></v-col>
            </v-row>
            <v-row class="mx-0 my-0 px-0 py-0 mt-8">
              <v-col cols="12" class="px-0 py-0 d-flex justify-end">
                <!-- <v-btn v-has:org-delete elevation="0" @click="exportTicket">
                  {{ $t('action.export') }}
                </v-btn> -->
                <v-btn
                  v-if="query.state === '0'"
                  depressed
                  outlined
                  :loading="btnLoading"
                  @click="delItem"
                  v-has:org-delete
                  class="px-6 py-2 ml-4 font-weight-normal btn-secondary btn-outline-secondary"
                >
                  <span class="action-btn"> {{ $t('action.del') }}</span>
                </v-btn>
                <v-btn
                  v-if="query.state === '0'"
                  :loading="btnLoading"
                  depressed
                  outlined
                  @click="changeState"
                  v-has:org-disable
                  class="px-6 py-2 ml-4 font-weight-normal btn-secondary btn-outline-secondary"
                >
                  <span class="action-btn">{{ $t('action.disable') }}</span>
                </v-btn>
                <v-btn
                  v-if="query.state === '1'"
                  :loading="btnLoading"
                  depressed
                  outlined
                  v-has:org-enable
                  @click="changeState"
                  class="px-6 py-2 ml-4 font-weight-normal btn-secondary btn-outline-secondary"
                >
                  <span class="action-btn">{{ $t('action.enable') }}</span>
                </v-btn>
                <v-btn
                  v-if="query.state === '0'"
                  :loading="btnLoading"
                  class="ml-4 px-6 py-2 btn-primary"
                  color="primary"
                  @click="update"
                  v-has:org-edit
                >
                  <span>
                    {{ $t('action.save') }}
                  </span>
                </v-btn>
              </v-col>
            </v-row>
          </v-form>
        </div>
        <div
          v-else
          class="h-100 d-flex flex-column align-center justify-center"
        >
          <v-img
            :style="{ opacity: items.length ? '1' : '0.3' }"
            width="160px"
            height="147px"
            :src="require('@/assets/images/tree-empty.png')"
            style="flex: inherit"
          ></v-img>
          <div v-if="items.length" class="mt-15">
            {{ $t('organization.hint.selectTip') }}
          </div>
          <div v-if="items.length" class="mt-2 secondary--text">
            {{ $t('organization.hint.selectTip1') }}
          </div>
        </div>
      </div>

      <add-dialog ref="refAddDialog"></add-dialog>
    </div>
  </div>
</template>
<script>
import {
  addDepartment,
  delDepartment,
  querySysGroupList,
  updateDepartment,
} from '@/api/system/organization'
import { findUsers } from '@/api/system/user'
import breadCrumb from '@/components/bread-crumb/index'
import elTree from '@/components/el-tree/index'
import { deepClone, setRemainingHeight } from '@/util/utils'
import AddDialog from './AddDialog.vue'

export default {
  components: {
    AddDialog,
    breadCrumb,
    elTree,
  },
  data() {
    return {
      defaultProps: {
        children: 'children',
        label: 'totalName',
      },
      height: 400,
      stateCheck: 0, //0启用 1禁用
      open: [],
      active: [],
      btnLoading: false,
      searchLoading: false,
      items: [],
      treeSelect: [],
      menuItems: [],
      userData: [],
      treeData: [],
      parentData: [],
      valid: true,
      isLoading: false,
      search: null,
      query: {
        departmentId: '',
        departmentName: '',
        directorId: '',
        isParent: '',
        type: '',
        id: '',
        state: '',
        dingTalk: '',
      },
      text: '',
      rules: {
        departmentId: [
          v =>
            !!v ||
            this.$t('validation.required', [
              this.$t('organization.headers.code'),
            ]),
        ],
        departmentName: [
          v =>
            !!v ||
            this.$t('validation.required', [
              this.$t('organization.headers.name'),
            ]),
        ],
      },
    }
  },

  computed: {
    filter() {
      return (item, search, textKey) =>
        item['name'].indexOf(search) > -1 ||
        item['departmentId'].indexOf(search) > -1
    },
  },
  watch: {
    search(val) {
      if (this.isLoading) return
      this.getUsers(val)
    },
  },
  created() {
    this.getTree()
    this.getUsers()
    this.$_setTableHeight()
    this.$bus.$on('resize', this.$_setTableHeight)
  },
  beforeDestroy() {
    this.$bus.$off('resize', this.$_setTableHeight)
  },
  methods: {
    dealTree(treeList) {
      if (treeList && treeList.length) {
        treeList.forEach(v => {
          v.isShow = false
          v.totalName = v.departmentId + ' - ' + v.name
          if (v.children) {
            this.dealTree(v.children)
          }
        })
      }
      return treeList
    },
    // 设置表格高度
    $_setTableHeight() {
      this.$nextTick(() => {
        this.height = setRemainingHeight() + 42
      })
    },
    async getTree(isReset) {
      const e = await querySysGroupList({
        state: this.stateCheck === 1 ? '' : '0',
      })

      this.items = this.dealTree(e.data)
      this.treeData = this.flat(deepClone(e.data))
      this.treeData = this.treeData.map(v => {
        return {
          ...v,
          totalName: v.departmentId + ' - ' + v.name,
        }
      })
      if (isReset === 1) {
        const tree = this.treeData.filter(v => v.id === this.query.id)
        this.updateTree(tree)
      }
    },
    dealName(type) {
      return type == '0'
        ? this.$t('organization.headers.company')
        : this.$t('organization.headers.department')
    },

    // 获取用户信息
    async getUsers(val) {
      const params = {
        pageNum: 1,
        pageSize: 20,
        userName: val || '',
      }
      try {
        this.isLoading = true
        const res = await findUsers(params)
        this.userData = res.data.records || []
      } catch (e) {
        console.error(`获取所有用户信息：${e}`)
      } finally {
        this.isLoading = false
      }
    },
    //循环树
    flat(arr) {
      let newArr = []
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].children) {
          newArr.push(...this.flat(arr[i].children))
          // delete arr[i].children
        }
        newArr.push({ ...arr[i] })
      }
      return newArr
    },
    updateTree(e) {
      if (e.length === 0) return

      this.dealTree(this.items)
      e[0].isShow = true

      // this.items = [...this.items]
      this.treeSelect = e
      this.text = this.treeSelect[0].typeName || ''
      this.query.isParent =
        this.treeSelect[0].parentId == '0' ? '' : this.treeSelect[0].parentId
      this.query.type = this.treeSelect[0].type
      this.query.departmentId = this.treeSelect[0].departmentId
      this.query.id = this.treeSelect[0].id
      this.query.departmentName = this.treeSelect[0].name
      this.query.directorId = this.treeSelect[0].directorId
      this.query.state = this.treeSelect[0].state
      this.query.dingTalk = this.treeSelect[0].dingTalk
      //0为公司 1为部门
      if (this.query.type == '0') {
        this.menuItems = [
          {
            type: 0,
            title: 'organization.btn.add',
            isParent: false,
          },
          {
            type: 0,
            title: 'organization.btn.add1',
            isParent: true,
          },
          {
            type: 1,
            title: 'organization.btn.add2',
            isParent: true,
          },
        ]
        this.parentData = this.treeData.filter(
          item =>
            item.id !== this.query.id &&
            item.parentId !== this.query.id &&
            item.type === '0',
        )
      } else {
        this.menuItems = [
          {
            type: 1,
            title: 'organization.btn.add3',
            isParent: false,
          },
          {
            type: 1,
            title: 'organization.btn.add2',
            isParent: true,
          },
        ]
        this.parentData = this.treeData.filter(
          item => item.id !== this.query.id && item.parentId !== this.query.id,
        )
      }
    },
    //新建
    addItem(item, tip) {
      let params
      if (tip === 1) {
        params = {
          title: this.$t('organization.currentTitle'),
          isParent: '0',
          type: '0',
          totalName: '',
          dingTalk: '',
        }
      } else {
        let findName = this.parentData.find(
          item => item.id === this.query.isParent,
        )
        params = {
          title: this.$t(item.title),
          isParent: item.isParent ? this.query.id : this.query.isParent,
          type: item.type,
          totalName: item.isParent
            ? this.query.departmentId + ' - ' + this.query.departmentName
            : this.query.isParent && findName
            ? findName.totalName
            : '',
          dingTalk: '',
        }
      }
      this.$refs['refAddDialog'].open(params, res => {
        const data = deepClone(res)
        if (!data.isParent) {
          data.isParent = '0'
        }
        const text =
          Number(data.type) === 1
            ? this.$t('organization.headers.department')
            : this.$t('organization.headers.company')
        addDepartment(data)
          .then(r => {
            if (r.code === 200) {
              this.$notify.info('success', this.$t('global.hint.add', [text]))
            }
            this.getTree()
            this.$refs['refAddDialog'].close()
          })
          .catch(() => {
            this.$refs['refAddDialog'].confirmLoading = false
          })
      })
    },
    //修改
    async update() {
      const bool = this.$refs.form.validate()
      if (!bool) return
      this.btnLoading = true

      const params = deepClone(this.query)
      params.isParent = this.query.isParent || '0'
      params.ids = [params.id]
      const text =
        Number(params.type) === 1
          ? this.$t('organization.headers.department')
          : this.$t('organization.headers.company')
      try {
        const res = await updateDepartment(params)
        if (res.code === 200) {
          this.$notify.info('success', this.$t('global.hint.edit', [text]))
          this.getTree(1)
        }
      } catch (e) {
        console.error(`修改失败：${e}`)
      } finally {
        this.btnLoading = false
      }
    },
    //改变状态
    async changeState() {
      const bool = this.$refs.form.validate()
      if (!bool) return
      const text =
        this.query.state === '0'
          ? this.$t('action.disable')
          : this.$t('action.enable')
      const ids = this.flat(this.treeSelect).map(v => {
        return v.id
      })
      const params = {
        ids: ids || [],
        state: this.query.state === '0' ? 1 : 0,
      }
      this.btnLoading = true
      try {
        const res = await updateDepartment(params)
        if (res.code === 200) {
          this.$notify.info(
            'success',
            this.text + ' ' + text + ' ' + this.$t('global.hint.successfully'),
          )
          this.getTree(1)
        }
      } catch (e) {
        console.error(text + `失败：${e}`)
      } finally {
        this.btnLoading = false
      }
    },
    //删除
    async delItem() {
      if (
        this.treeSelect.length &&
        this.treeSelect[0].children &&
        this.treeSelect[0].children.length
      ) {
        const text =
          this.treeSelect[0].type === '1'
            ? this.$t('organization.hint.delTip')
            : this.$t('organization.hint.delTip1')
        return this.$notify.info('warning', text)
      }
      this.$swal({
        title: this.$t('organization.swal.del.title'),
        text: this.$t('organization.swal.del.text'),
        icon: 'warning',
        reverseButtons: true,
        showCancelButton: true,
        confirmButtonText: this.$t('action.confirm'),
        cancelButtonText: this.$t('action.cancel'),
        customClass: {
          confirmButton: 'sweet-btn-primary',
        },
      }).then(async result => {
        if (result.isConfirmed) {
          this.btnLoading = true
          try {
            const res = await delDepartment({
              id: this.query.id,
            })
            if (res.code === 200) {
              const text =
                this.treeSelect[0].type === '1'
                  ? this.$t('organization.hint.delTip2')
                  : this.$t('global.hint.del', [
                      this.$t('organization.headers.company'),
                    ])
              this.$notify.info('success', text)
              this.treeSelect = []
              this.getTree()
            }
          } catch (e) {
            console.error(`删除失败：${e}`)
          } finally {
            this.btnLoading = false
          }
        }
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.v-menu__content.menu-list {
  margin-left: 12px !important;
  .v-list {
    padding: 0 !important;
  }
  .v-list-item {
    min-width: 124px !important;
    height: 34px !important;
    color: var(--v-color-base) !important;
    text-align: left;
  }
  .list-hover:hover {
    color: $primary !important;
    background-color: rgba(1, 76, 241, 0.1) !important;
  }
}
.organization-box {
  color: var(--v-color-base);

  font-size: $font-size-content;

  ::v-deep .organization-checkbox .v-label {
    color: var(--v-color-base) !important;
  }
  ::v-deep .organization-checkbox .v-label.v-label--is-disabled {
    color: $placeholder !important;
  }
  // height: calc(100vh - 119px);
  ::v-deep .v-treeview-node__root {
    padding-left: 0px !important;
    padding-right: 0px !important;
    min-height: 22px !important;
    margin-top: 12px !important;
    margin-bottom: 12px !important;
  }
  .organization-box-left {
    width: 400px !important;
    background: var(--v-backgroundColor-base) !important;
  }
  .organization-box-right {
    background: var(--v-backgroundColor-base) !important;
    border-left: 1px solid var(--v-bgColor-base) !important;
  }
  // ::v-deep .v-icon.v-icon {
  //   font-size: 18px !important;
  // }
  ::v-deep .v-sheet.v-card {
    border-radius: 0px !important;
  }
  ::v-deep .v-treeview-node__toggle {
    color: #626262 !important;
  }
  .right-tile {
    font-size: $font-size-root;
    color: var(--v-color-base) !important;

    height: 22px;
  }
  .edit-text {
    cursor: pointer;
    color: $primary;
  }
  .organization-checkbox {
    display: flex;
    align-items: center;
    height: 38px;
    // background: #f5f6fa !important;
    background: var(--v-bgColor-base) !important;
    ::v-deep .v-label {
      font-size: $font-size-root !important;
    }
    ::v-deep .v-input--selection-controls__input {
      margin-right: 4px !important;
    }
  }
}
</style>
