import { request, vsocPath } from '../../util/request'

// 根据groupid查询关联用户个数
export const selectUserGroupCount = function (params) {
  return request({
    url: `${vsocPath}/sysGroup/selectUserGroupCount`,
    method: 'post',
    params,
  })
}

// 查询用户组信息
export const userGroup = function (params) {
  return request({
    url: `${vsocPath}/sysGroup/userGroup`,
    method: 'post',
    data: params,
  })
}

// 用户组【添加】接口
export const addGroup = function (params) {
  return request({
    url: `${vsocPath}/sysGroup/addGroup`,
    method: 'post',
    data: params,

    // {
    // 	"groupId": "yyy",
    // 	"groupName": "运营",
    // 	"description": "不容易"
    // }
  })
}

// 用户组【修改】接口
export const updateGroup = function (params) {
  return request({
    url: `${vsocPath}/sysGroup/updateGroup`,
    method: 'post',
    data: params,

    //   {
    //   "groupId": "www",
    //   "groupName":"老板"
    // }
  })
}

// 用户组【删除】接口
export const delGroup = function (params) {
  return request({
    url: `${vsocPath}/sysGroup/delGroup`,
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    params,
  })
}
