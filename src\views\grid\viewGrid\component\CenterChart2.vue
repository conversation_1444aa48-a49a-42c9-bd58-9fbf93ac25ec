<template>
  <div class="box" style="padding: 3.125% 4%">
    <div class="box-relative">
      <div class="box-header box-position-top" style="padding-bottom: 0.8%">
        {{ $t('screen.dataHandle') }}
        <span class="box-header-num">{{ total | numberToFormat }}</span>
      </div>
    </div>

    <template v-if="dataProcessing.length !== 0">
      <vsoc-chart
        echartId="data-area"
        class="box-chart d-flex align-center"
        :option="option"
      ></vsoc-chart>
    </template>
    <template v-else>
      <div
        class="box-chart d-flex align-center color--primary justify-center fs-16"
      >
        None
      </div>
    </template>
  </div>
</template>

<script>
import VsocChart from '@/components/VsocChart.vue'
import { linearOptionFn } from './chart'

let xList = ['4/11', '4/12', '4/13', '4/14', '4/15', '4/16', '4/17']
let yList = [
  {
    name: 'IEP',
    data: [140, 232, 101, 264, 90, 340, 250],
    color: '#F0DA4C',
  },
  {
    name: 'CDI',
    data: [250, 342, 211, 374, 200, 450, 360],
    color: '#32FDB8',
  },
  {
    name: 'ISMS',
    data: [350, 442, 311, 474, 300, 550, 460],
    // color: '#37A2FF',
    color: '#21CBFF',
  },
]

export default {
  name: 'CenterChart3',
  props: {
    xList: Array,
    dataProcessing: {
      type: Array,
      default: () => {
        return []
      },
    },
    total: {
      type: [Number, String],
      default: 0,
    },
  },
  components: {
    VsocChart,
  },
  data() {
    return {
      // option: {},
      myChart: undefined,
    }
  },
  computed: {
    option() {
      // this.dataProcessing.forEach((item, index) => {
      //   yList[index].name = item.levelName
      //   yList[index].data = item.arrays.map(x => x.number)
      // })
      yList = [
        {
          name: '内容总量',
          data: this.dataProcessing && this.dataProcessing.map(v => v.number),
          color: '#F0DA4C',
        },
      ]
      return linearOptionFn(this.xList, yList)
    },
  },
  // mounted() {
  //   this.onDraw()
  // },
  // methods: {
  //   onResize() {
  //     this.$nextTick(() => {
  //       this.option = linearOptionFn(xList, yList)
  //       this.myChart.setOption(this.option)
  //       this.myChart.resize()
  //     })
  //   },
  //   onDraw() {
  //     const ele = document.getElementById('data-area')
  //     this.myChart = this.$echarts.init(ele, {
  //       height: 'auto',
  //       width: 'auto',
  //     })

  //     this.option = linearOptionFn(xList, yList)
  //     this.myChart.setOption(this.option)
  //   },
  // },
}
</script>
