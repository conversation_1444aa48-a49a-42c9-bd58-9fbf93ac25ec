<template>
  <div>
    <bread-crumb></bread-crumb>

    <v-card tile class="main-content">
      <v-card-text class="pa-0">
        <div class="mb-3 d-flex justify-space-between">
          <div></div>
          <div class="d-flex align-center">
            <table-search
              :searchList="searchList"
              :searchQuery="query"
              @search="$_search"
            ></table-search>
            <!-- <v-text-field
              v-model="query.itemName"
              color="primary"
              hide-details="auto"
              :label="$t('cloud.headers.itemName')"
              dense
              outlined
              @keyup.enter.native="$_search"
              class="me-3 text-width"
            ></v-text-field>
            <v-btn color="primary--text bg-btn" elevation="0" @click="$_search">
              <span>{{ $t('action.search') }}</span>
            </v-btn> -->
          </div>
        </div>

        <v-data-table
          fixed-header
          :items-per-page="tableDataTotal"
          item-key="id"
          :height="tableHeight"
          hide-default-footer
          :headers="headers"
          :items="tableData"
          class="flex-1 thead-light"
          :loading="tableDataLoading"
        >
          <template v-slot:item.itemName="{ item }">
            <div
              v-show-tips
              style="max-width: 200px"
              class="text-overflow-hide"
            >
              {{ item.itemName }}
              <!-- {{ $generateName(item.itemName, item.itemEnName) }} -->
            </div>
          </template>

          <template v-slot:item.itemEnName="{ item }">
            <div
              v-show-tips
              style="max-width: 200px"
              class="text-overflow-hide"
            >
              {{ item.itemEnName }}
            </div>
          </template>
          <template v-slot:item.chartValue="{ item }">
            <div class="d-flex" v-if="chartEnum[item.chartValue]">
              <vsoc-icon
                :icon="chartEnum[item.chartValue].icon"
                class="action-btn"
              ></vsoc-icon>
              <span class="ml-2">{{ chartEnum[item.chartValue].text }}</span>
            </div>
            <div v-else>N/A</div>
          </template>
          <template v-slot:item.contentSourceType="{ item }">
            <v-chip
              small
              label
              class="text--secondary"
              v-if="$toItem(cloudMethodEnum, item.contentSourceType)"
              >{{
                $toItem(cloudMethodEnum, item.contentSourceType).text
              }}</v-chip
            >
          </template>

          <template v-slot:item.actions="{ item }">
            <v-btn v-has:grid-edit icon @click="onEdit(item)">
              <vsoc-icon
                v-show-tips="$t('action.edit')"
                type="fill"
                icon="icon-bianji"
                class="action-btn"
                size="x-large"
              ></vsoc-icon>
            </v-btn>
          </template>
        </v-data-table>
        <!-- 分页器 -->
        <vsoc-pagination
          :page.sync="query.pageNum"
          :size.sync="query.pageSize"
          :total="tableDataTotal"
          @change-page="getTableData"
          @change-size="$_search"
        >
        </vsoc-pagination>
      </v-card-text>
    </v-card>
    <cloud-edit
      ref="cloudEdit"
      :item="editForm"
      :mode="ops"
      @refresh="$_search"
    ></cloud-edit>
  </div>
</template>

<script>
import { max } from '@/@core/utils/validation'
import { getlayoutItems } from '@/api/grid/index'
import BreadCrumb from '@/components/bread-crumb/index.vue'
import TableSearch from '@/components/TableSearch/index.vue'
import VsocPagination from '@/components/VsocPagination.vue'
import { dataFilter } from '@/util/filters'
import { setRemainingHeight } from '@/util/utils'
import CloudEdit from './CloudEdit.vue'

export default {
  name: 'SetGridIndex',
  components: {
    BreadCrumb,
    CloudEdit,
    VsocPagination,
    TableSearch,
  },
  filters: {
    dataFilter,
  },
  data() {
    return {
      max,
      tableDataLoading: false,
      // 分页参数
      query: {
        itemName: '', //布局指标项名称
        isActive: '', //是否有效 0有效 1无效
        pageNum: 1, //当前页
        pageSize: 10, //每页多少条
      },
      tableDataTotal: 0,
      tableHeight: '34.5rem',
      tableData: [],
      ops: '',
      editForm: {
        platformName: '',
      },
    }
  },
  computed: {
    searchList() {
      return [
        {
          type: 'input',
          value: 'itemName',
          text: this.$t('cloud.headers.itemName'),
        },
      ]
    },
    chartEnum() {
      return this.$store.state.enums.enums['Layout Item Chart Type']
    },
    cloudMethodEnum() {
      return this.$store.getters['enums/getCloudMethod']
    },
    headers() {
      return [
        {
          text: this.$t('cloud.headers.itemKey'),
          value: 'itemKey',
          width: 100,
        },
        {
          text: this.$t('cloud.headers.itemName'),
          value: 'itemName',
          width: 200,
        },
        // {
        //   text: this.$t('cloud.headers.itemEnName'),
        //   value: 'itemEnName',
        //   width: 200,
        // },
        // {
        //   text: this.$t('cloud.headers.chart'),
        //   value: 'chartValue',
        //   width: 180,
        // },
        {
          text: this.$t('cloud.headers.readMethod'),
          value: 'contentSourceType',
          width: 160,
        },
        {
          text: this.$t('global.updateUser'),
          value: 'updateUser',
          width: 160,
        },
        {
          text: this.$t('global.updateDate'),
          value: 'updateDate',
          width: 160,
        },
        {
          text: '',
          value: 'actions',
          sortable: false,
          width: 80,
        },
      ]
    },
  },

  created() {
    this.$_setTableHeight()
    this.$bus.$on('resize', this.$_setTableHeight)
    this.$_search()
  },
  beforeDestroy() {
    this.$bus.$off('resize', this.$_setTableHeight)
  },
  methods: {
    onEdit(record) {
      this.ops = 'edit'
      this.editForm = { ...record }
      this.$refs.cloudEdit.isDrawerShow = true
    },

    $_search() {
      this.query.pageNum = 1
      this.getTableData()
    },

    $_setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = setRemainingHeight() - 12
      })
    },

    // 获取平台
    async getTableData() {
      // 保存查询参数
      this.tableDataLoading = true
      try {
        const { data } = await getlayoutItems(this.query)
        this.tableData = data.records
        this.tableDataTotal = data.total
      } catch (e) {
        console.error(`获取自定化配置数据管理错误：${e}`)
      } finally {
        this.tableDataLoading = false
      }
    },
  },
}
</script>
