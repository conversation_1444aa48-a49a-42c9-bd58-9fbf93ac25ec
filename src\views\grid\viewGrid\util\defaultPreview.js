// 横排 29  7 8 7
// 竖排 29  7 9 8

// 竖排  8(31%) 13(38%) 8(31%)
// 横排  6 8 8 7
export const defaultPreviewData = []
// export const defaultPreviewData = [
//   {
//     type: 1,
//     defaultList: [
//       { x: 0, y: 0, w: 8, h: 6, i: 'C001', itemKey: 'C001' },
//       { x: 8, y: 0, w: 13, h: 22, i: 'C009', itemKey: 'C009' },
//       { x: 21, y: 0, w: 8, h: 6, i: 'C005', itemKey: 'C005' },
//       { x: 0, y: 6, w: 8, h: 8, i: 'C002', itemKey: 'C002' },
//       { x: 21, y: 6, w: 8, h: 8, i: 'C006', itemKey: 'C006' },
//       { x: 0, y: 14, w: 8, h: 8, i: 'C003', itemKey: 'C003' },
//       { x: 21, y: 14, w: 8, h: 8, i: 'C007', itemKey: 'C007' },
//       { x: 0, y: 22, w: 8, h: 7, i: 'C004', itemKey: 'C004' },
//       { x: 8, y: 22, w: 13, h: 7, i: 'C012', itemKey: 'C012' },
//       { x: 21, y: 22, w: 8, h: 7, i: 'C008', itemKey: 'C008' },
//     ],
//   },
//   {
//     type: 2,
//     defaultList: [
//       { x: 0, y: 0, w: 8, h: 6, i: 'C001', itemKey: 'C001' },
//       { x: 8, y: 0, w: 13, h: 22, i: 'C010', itemKey: 'C010' },
//       { x: 21, y: 0, w: 8, h: 6, i: 'C005', itemKey: 'C005' },
//       { x: 0, y: 6, w: 8, h: 8, i: 'C002', itemKey: 'C002' },
//       { x: 21, y: 6, w: 8, h: 8, i: 'C006', itemKey: 'C006' },
//       { x: 0, y: 14, w: 8, h: 8, i: 'C003', itemKey: 'C003' },
//       { x: 21, y: 14, w: 8, h: 8, i: 'C007', itemKey: 'C007' },
//       { x: 0, y: 22, w: 8, h: 7, i: 'C004', itemKey: 'C004' },
//       { x: 8, y: 22, w: 13, h: 7, i: 'C011', itemKey: 'C011' },
//       { x: 21, y: 22, w: 8, h: 7, i: 'C008', itemKey: 'C008' },
//     ],
//   },
//   {
//     // chartValue 3 竖排7 12 10 横排8 13 8
//     type: 3,
//     defaultList: [
//       {
//         x: 0,
//         y: 0,
//         w: 8,
//         h: 7,
//         i: 'C040',
//         itemKey: 'C040',
//         id: 40,
//         isBorder: '0',
//         startWidth: 8,
//         startHeight: 7,
//         updateDate: '2024-12-04 15:29:20',
//         itemKeyList: [],
//         minH: 2,
//         moved: false,
//         isActive: '0',
//         isBorder: '0',
//         minHeight: 2,
//         itemName: '车企风险概览',
//         chartValue: '2',
//         value: [
//           {
//             name: '车企总数',
//             value: 62,
//           },
//           {
//             name: '风险车企总数',
//             value: 12,
//           },
//           // {
//           //   name: '车辆总数',
//           //   value: 5111894,
//           // },
//         ],
//         createDate: '2024-12-04 15:29:20',
//         updateUser: 'lyg',
//         minWidth: 2,
//         itemEnName: 'Asset Statistics',
//         minW: 2,
//         createUser: 'lyg',
//         canvasIdList: [],
//         contentSourceType: '0',
//       },
//       {
//         type: 1,
//         x: 8,
//         y: 0,
//         w: 13,
//         h: 19,
//         i: 'C041',
//         itemKey: 'C041',
//         id: 41,
//         isBorder: '0',
//         startWidth: 13,
//         startHeight: 20,
//         updateDate: '2024-12-04 15:29:20',
//         itemKeyList: [],
//         minH: 2,
//         moved: false,
//         isActive: '0',
//         isBorder: '1',
//         minHeight: 2,
//         itemName: '企业安全风险评估TOP',
//         chartValue: '14',
//         value: [
//           {
//             city: '340100',
//             cityName: '合肥市',
//             count: 5579,
//             latitude: 31.86119,
//             longitude: 117.283042,
//             carModel: '比亚迪汽车工业有限公司',
//           },
//           {
//             city: '340200',
//             cityName: '芜湖市',
//             count: 2344,
//             latitude: 31.326319,
//             longitude: 118.376451,
//             carModel: '奇瑞汽车股份有限公司',
//           },
//           {
//             city: '340500',
//             cityName: '马鞍山市',
//             count: 789,
//             latitude: 31.689362,
//             longitude: 118.507906,
//             carModel: '安徽华菱汽车有限公司',
//           },
//           {
//             city: '341100',
//             cityName: '滁州市',
//             count: 413,
//             latitude: 32.303627,
//             longitude: 118.316264,
//             carModel: '安徽吉事达专用汽车有限公司',
//           },
//           {
//             city: '340400',
//             cityName: '淮南市',
//             count: 38,
//             latitude: 32.647574,
//             longitude: 117.018329,
//             carModel: '奇瑞新能源汽车股份有限公司',
//           },
//           {
//             city: '340300',
//             cityName: '蚌埠市',
//             count: 24,
//             latitude: 32.939667,
//             longitude: 117.363228,
//             carModel: '奇瑞汽车股份有限公司',
//           },
//           {
//             city: '340800',
//             cityName: '安庆市',
//             count: 31,
//             latitude: 30.50883,
//             longitude: 117.043551,
//             carModel: '雷萨股份有限公司',
//           },
//         ],
//         createDate: '2024-12-04 15:29:20',
//         updateUser: 'lyg',
//         minWidth: 2,
//         itemEnName: 'Asset Statistics',
//         minW: 2,
//         createUser: 'lyg',
//         canvasIdList: [],
//         contentSourceType: '0',
//         headers1: [
//           {
//             text: '排名',
//             value: 'riskNum',
//           },
//           {
//             text: '企业名称',
//             value: 'riskName',
//           },
//           {
//             text: '风险等级',
//             value: 'riskLevel',
//           },
//           {
//             text: '安全指数',
//             value: 'riskSafetyIndex',
//           },
//         ],
//       },
//       {
//         x: 21,
//         y: 0,
//         w: 8,
//         h: 7,
//         i: 'C042',
//         itemKey: 'C042',
//         id: 42,
//         isBorder: '0',
//         startWidth: 8,
//         startHeight: 7,
//         updateDate: '2024-12-04 15:29:20',
//         itemKeyList: [],
//         minH: 2,
//         moved: false,
//         isActive: '0',
//         isBorder: '0',
//         minHeight: 2,
//         itemName: '风险概览',
//         chartValue: '2',
//         value: [
//           {
//             name: '风险总数',
//             value: '9218',
//             riskType: '0',
//           },
//           {
//             name: '待处理风险',
//             value: '297',
//             riskType: '1',
//           },
//           {
//             name: '已处理风险',
//             value: '8921',
//             riskType: '',
//           },
//         ],
//         createDate: '2024-12-04 15:29:20',
//         updateUser: 'lyg',
//         minWidth: 2,
//         itemEnName: 'Asset Statistics',
//         minW: 2,
//         createUser: 'lyg',
//         canvasIdList: [],
//         contentSourceType: '0',
//       },
//       //境内TOP
//       {
//         x: 21,
//         y: 0,
//         w: 8,
//         h: 19,
//         i: 'C100',
//         itemKey: 'C100',
//         id: 100,
//         isBorder: '0',
//         startWidth: 8,
//         startHeight: 19,
//         updateDate: '2024-12-04 15:29:20',
//         itemKeyList: [],
//         minH: 2,
//         moved: false,
//         isActive: '0',
//         isBorder: '0',
//         minHeight: 2,
//         itemName: '境内攻击排名TOP',
//         chartValue: '3',
//         value: [
//           { riskNum: '1', riskName: '北京', riskValue: '275(40%)' },
//           { riskNum: '2', riskName: '上海', riskValue: '264(30%)' },
//           { riskNum: '3', riskName: '广州', riskValue: '251(20%)' },
//           { riskNum: '4', riskName: '大连', riskValue: '239(10%)' },
//           { riskNum: '5', riskName: '南宁', riskValue: '227(5%)' },
//           { riskNum: '1', riskName: '北京', riskValue: '275(40%)' },
//           { riskNum: '2', riskName: '上海', riskValue: '264(30%)' },
//           { riskNum: '3', riskName: '广州', riskValue: '251(20%)' },
//           { riskNum: '4', riskName: '大连', riskValue: '239(10%)' },
//           { riskNum: '5', riskName: '南宁', riskValue: '227(5%)' },
//         ],
//         headers1: [
//           {
//             text: '排名',
//             value: 'riskNum',
//           },
//           {
//             text: '省份',
//             value: 'riskName',
//           },
//           {
//             text: '攻击次数',
//             value: 'riskValue',
//             align: 'center',
//           },
//         ],
//         createDate: '2024-12-04 15:29:20',
//         updateUser: 'lyg',
//         minWidth: 2,
//         itemEnName: 'Asset Statistics',
//         minW: 2,
//         createUser: 'lyg',
//         canvasIdList: [],
//         contentSourceType: '0',
//       },
//       //境外TOP
//       {
//         x: 21,
//         y: 0,
//         w: 8,
//         h: 19,
//         i: 'C101',
//         itemKey: 'C101',
//         id: 101,
//         isBorder: '0',
//         startWidth: 8,
//         startHeight: 19,
//         updateDate: '2024-12-04 15:29:20',
//         itemKeyList: [],
//         minH: 2,
//         moved: false,
//         isActive: '0',
//         isBorder: '0',
//         minHeight: 2,
//         itemName: '境外攻击排名TOP',
//         chartValue: '3',
//         value: [
//           { riskNum: '1', riskName: '美国', riskValue: '275(40%)' },
//           { riskNum: '2', riskName: '澳大利亚', riskValue: '264(30%)' },
//           { riskNum: '3', riskName: '韩国', riskValue: '251(20%)' },
//           { riskNum: '4', riskName: '英国', riskValue: '239(10%)' },
//           { riskNum: '5', riskName: '日本', riskValue: '227(5%)' },
//           { riskNum: '1', riskName: '美国', riskValue: '275(40%)' },
//           { riskNum: '2', riskName: '澳大利亚', riskValue: '264(30%)' },
//           { riskNum: '3', riskName: '韩国', riskValue: '251(20%)' },
//           { riskNum: '4', riskName: '英国', riskValue: '239(10%)' },
//           { riskNum: '5', riskName: '日本', riskValue: '227(5%)' },
//         ],
//         headers1: [
//           {
//             text: '排名',
//             value: 'riskNum',
//           },
//           {
//             text: '国家名称',
//             value: 'riskName',
//           },
//           {
//             text: '攻击次数',
//             value: 'riskValue',
//             align: 'center',
//           },
//         ],
//         createDate: '2024-12-04 15:29:20',
//         updateUser: 'lyg',
//         minWidth: 2,
//         itemEnName: 'Asset Statistics',
//         minW: 2,
//         createUser: 'lyg',
//         canvasIdList: [],
//         contentSourceType: '0',
//       },
//       {
//         x: 0,
//         y: 7,
//         w: 8,
//         h: 12,
//         i: 'C043',
//         itemKey: 'C043',
//         id: 43,
//         isBorder: '0',
//         startWidth: 8,
//         startHeight: 12,
//         updateDate: '2024-12-04 15:29:20',
//         itemKeyList: [],
//         minH: 2,
//         moved: false,
//         isActive: '0',
//         isBorder: '0',
//         minHeight: 2,
//         itemName: '风险企业TOP',
//         chartValue: '15',
//         value: [
//           {
//             id: 1,
//             riskNum: 2200,
//             riskName: '奇瑞汽车股份有限公司',
//             riskLevel: '0',
//             riskLevelName: '高',
//             riskSafetyIndex: 23.91,
//             riskSafetyCount: 1629356,
//           },
//           {
//             id: 2,
//             riskNum: 2100,
//             riskName: '比亚迪汽车工业有限公司',
//             riskLevel: '0',
//             riskLevelName: '高',
//             riskSafetyIndex: 22.83,
//             riskSafetyCount: 909170,
//           },
//           {
//             id: 3,
//             riskNum: 2000,
//             riskName: '合肥长安汽车有限公司',
//             riskLevel: '0',
//             riskLevelName: '高',
//             riskSafetyIndex: 21.74,
//             riskSafetyCount: 860536,
//           },
//           {
//             id: 4,
//             riskNum: 950,
//             riskName: '安徽江淮汽车集团股份有限公司',
//             riskLevel: '1',
//             riskLevelName: '中等',
//             riskSafetyIndex: 10.33,
//             riskSafetyCount: 827893,
//           },
//           {
//             id: 5,
//             riskNum: 820,
//             riskName: '奇瑞新能源汽车股份有限公司',
//             riskLevel: '1',
//             riskLevelName: '中等',
//             riskSafetyIndex: 8.91,
//             riskSafetyCount: 436350,
//           },
//           {
//             id: 6,
//             riskNum: 650,
//             riskName: '奇瑞商用车(安徽)有限公司',
//             riskLevel: '1',
//             riskLevelName: '中等',
//             riskSafetyIndex: 7.07,
//             riskSafetyCount: 384039,
//           },
//           {
//             id: 7,
//             riskNum: 430,
//             riskName: '蔚来汽车科技(安徽)有限公司',
//             riskLevel: '1',
//             riskLevelName: '中等',
//             riskSafetyIndex: 4.67,
//             riskSafetyCount: 39263,
//           },
//           {
//             id: 8,
//             riskNum: 95,
//             riskName: '安徽华菱汽车有限公司',
//             riskLevel: '1',
//             riskLevelName: '中等',
//             riskSafetyIndex: 1.03,
//             riskSafetyCount: 5722,
//           },
//           {
//             id: 9,
//             riskNum: 85,
//             riskName: '大众汽车(安徽)有限公司',
//             riskLevel: '1',
//             riskLevelName: '中等',
//             riskSafetyIndex: 0.92,
//             riskSafetyCount: 4488,
//           },
//           {
//             id: 10,
//             riskNum: 12,
//             riskName: '安徽安凯汽车股份有限公司',
//             riskLevel: '1',
//             riskLevelName: '中等',
//             riskSafetyIndex: 0.18,
//             riskSafetyCount: 4173,
//           },
//         ],
//         createDate: '2024-12-04 15:29:20',
//         updateUser: 'lyg',
//         minWidth: 2,
//         itemEnName: 'Asset Statistics',
//         minW: 2,
//         createUser: 'lyg',
//         canvasIdList: [],
//         contentSourceType: '0',
//       },
//       {
//         x: 21,
//         y: 0,
//         w: 8,
//         h: 12,
//         i: 'C044',
//         itemKey: 'C044',
//         id: 44,
//         isBorder: '0',
//         startWidth: 8,
//         startHeight: 12,
//         updateDate: '2024-12-04 15:29:20',
//         itemKeyList: [],
//         minH: 2,
//         moved: false,
//         isActive: '0',
//         isBorder: '0',
//         minHeight: 2,
//         itemName: '通信数据流量统计',
//         chartValue: '13',
//         value: [
//           {
//             total: '1000',
//             title: '数据总量',
//             data: [
//               {
//                 name: '密文数据',
//                 value: 750,
//               },
//               // {
//               //   name: '明文数据',
//               //   value: 250,
//               // },
//             ],
//           },
//           {
//             total: '1.2',
//             title: '当天数据量',
//             data: [
//               {
//                 name: '密文数据',
//                 value: 0.9,
//               },
//               // {
//               //   name: '明文数据',
//               //   value: 0.3,
//               // },
//             ],
//           },
//         ],
//         createDate: '2024-12-04 15:29:20',
//         updateUser: 'lyg',
//         minWidth: 2,
//         itemEnName: 'Asset Statistics',
//         minW: 2,
//         createUser: 'lyg',
//         canvasIdList: [],
//         contentSourceType: '0',
//       },
//       {
//         x: 0,
//         y: 19,
//         w: 8,
//         h: 10,
//         i: 'C045',
//         itemKey: 'C045',
//         id: 45,
//         isBorder: '0',
//         startWidth: 8,
//         startHeight: 10,
//         updateDate: '2024-12-04 15:29:20',
//         itemKeyList: [],
//         minH: 2,
//         moved: false,
//         isActive: '0',
//         isBorder: '0',
//         minHeight: 2,
//         itemName: '风险类型分布',
//         chartValue: '20',
//         value: {
//           total: 9218,
//           list: [
//             {
//               name: '白名单异常访问',
//               value: 2766,
//             },
//             {
//               name: 'APP异常安装',
//               value: 2305,
//             },
//             {
//               name: '数据流量异常增长',
//               value: 2303,
//             },
//             {
//               name: '通信链路安全协议失效',
//               value: 1383,
//             },
//             {
//               name: '区域车辆异常聚集',
//               value: 461,
//             },
//           ],
//         },
//         createDate: '2024-12-04 15:29:20',
//         updateUser: 'lyg',
//         minWidth: 2,
//         itemEnName: 'Asset Statistics',
//         minW: 2,
//         createUser: 'lyg',
//         canvasIdList: [],
//         contentSourceType: '0',
//       },
//       {
//         x: 8,
//         y: 19,
//         w: 13,
//         h: 10,
//         i: 'C046',
//         itemKey: 'C046',
//         id: 46,
//         isBorder: '0',
//         startWidth: 13,
//         startHeight: 10,
//         updateDate: '2024-12-04 15:29:20',
//         itemKeyList: [],
//         minH: 2,
//         moved: false,
//         isActive: '0',
//         isBorder: '0',
//         minHeight: 2,
//         itemName: '企业实时风险情况',
//         chartValue: '3',
//         value: [
//           {
//             riskName: '奇瑞汽车股份有限公司',
//             riskLevel: '0',
//             riskLevelName: '高',
//             model: '瑞虎8',
//             desc: '白名单异常访问',
//             updateTime: '2025-4-23 12:22:31',
//             riskStatus: '0',
//             statusName: '未整改',
//             riskRemark: '下发整改指令',
//             id: 2,
//           },
//           {
//             riskName: '奇瑞汽车股份有限公司',
//             riskLevel: '0',
//             riskLevelName: '高',
//             model: '星途摇光',
//             desc: 'APP异常安装',
//             updateTime: '2025-4-23 12:22:31',
//             riskStatus: '2',
//             statusName: '已整改',
//             riskRemark: '下发整改指令',
//             id: 3,
//           },
//           {
//             riskName: '奇瑞汽车股份有限公司',
//             riskLevel: '0',
//             riskLevelName: '高',
//             model: '瑞虎8',
//             desc: '数据流量异常增长',
//             updateTime: '2025-4-23 12:22:31',
//             riskStatus: '1',
//             statusName: '整改中',
//             riskRemark: '下发整改指令',
//             id: 4,
//           },
//           {
//             riskName: '蔚来汽车有限公司',
//             riskLevel: '1',
//             riskLevelName: '中等',
//             model: 'ES6',
//             desc: '通信链路安全协议失效',
//             updateTime: '2025-4-23 12:22:31',
//             riskStatus: '1',
//             statusName: '评估中',
//             riskRemark: '下发整改指令',
//             id: 5,
//           },
//           {
//             riskName: '蔚来汽车有限公司',
//             riskLevel: '2',
//             riskLevelName: '低',
//             model: 'ES6',
//             desc: '区域车辆异常聚集',
//             updateTime: '2025-4-23 12:22:31',
//             riskStatus: '',
//             statusName: '',
//             riskRemark: '下发整改指令',
//             id: 6,
//           },
//         ],
//         createDate: '2024-12-04 15:29:20',
//         updateUser: 'lyg',
//         minWidth: 2,
//         itemEnName: 'Asset Statistics',
//         minW: 2,
//         createUser: 'lyg',
//         canvasIdList: [],
//         contentSourceType: '0',
//         headers1: [
//           {
//             text: '企业名称',
//             value: 'riskName',
//           },
//           {
//             text: '车型',
//             value: 'model',
//             align: 'center',
//           },
//           {
//             text: '风险类型',
//             value: 'desc',
//             align: 'center',
//           },
//           // {
//           //   text: '风险等级',
//           //   value: 'riskLevel',
//           //   align: 'center',
//           // },
//           {
//             text: '上报时间',
//             value: 'updateTime',
//           },
//           {
//             text: '状态',
//             value: 'riskStatus',
//             align: 'center',
//           },
//           {
//             text: '操作',
//             value: 'riskRemark',
//             align: 'center',
//           },
//         ],
//       },
//       {
//         x: 21,
//         y: 19,
//         w: 8,
//         h: 10,
//         i: 'C047',
//         itemKey: 'C047',
//         id: 47,
//         isBorder: '0',
//         startWidth: 8,
//         startHeight: 10,
//         updateDate: '2024-12-04 15:29:20',
//         itemKeyList: [],
//         minH: 2,
//         moved: false,
//         isActive: '0',
//         isBorder: '0',
//         minHeight: 2,
//         itemName: '安全风险趋势',
//         chartValue: '7',
//         value: {
//           total: 19,
//           list: [
//             {
//               name: '高危事件',
//               dictId: '0',
//               riskEvent: true,
//               arrays: [
//                 {
//                   x: '2024-12',
//                   y: 89,
//                 },
//                 {
//                   x: '2025-01',
//                   y: 85,
//                 },
//                 {
//                   x: '2025-02',
//                   y: 92,
//                 },
//                 {
//                   x: '2025-03',
//                   y: 97,
//                 },
//                 {
//                   x: '2025-04',
//                   y: 87,
//                 },
//                 {
//                   x: '2025-05',
//                   y: 76,
//                 },
//               ],
//             },
//             {
//               name: '预警事件',
//               dictId: '1',
//               riskEvent: true,
//               arrays: [
//                 {
//                   x: '2024-12',
//                   y: 2021,
//                 },
//                 {
//                   x: '2025-01',
//                   y: 1267,
//                 },
//                 {
//                   x: '2025-02',
//                   y: 1395,
//                 },
//                 {
//                   x: '2025-03',
//                   y: 1526,
//                 },
//                 {
//                   x: '2025-04',
//                   y: 1806,
//                 },
//                 {
//                   x: '2025-05',
//                   y: 1659,
//                 },
//               ],
//             },
//           ],
//         },
//         createDate: '2024-12-04 15:29:20',
//         updateUser: 'lyg',
//         minWidth: 2,
//         itemEnName: 'Asset Statistics',
//         minW: 2,
//         createUser: 'lyg',
//         canvasIdList: [],
//         contentSourceType: '0',
//         headers1: [
//           {
//             text: '企业名称',
//             value: 'riskName',
//             width: '30%',
//           },
//           {
//             text: '事件描述',
//             value: 'desc',
//             width: '55%',
//           },
//           {
//             text: '审核',
//             value: 'riskRemark',
//             // align: 'center',
//             width: '15%',
//           },
//         ],
//       },
//     ],
//   },
//   {
//     // chartValue 3 竖排7 12 10 横排8 13 8
//     type: 4,
//     defaultList: [
//       {
//         x: 0,
//         y: 0,
//         w: 8,
//         h: 5,
//         i: 'C050',
//         itemKey: 'C050',
//         id: 40,
//         isBorder: '0',
//         startWidth: 8,
//         startHeight: 5,
//         updateDate: '2024-12-04 15:29:20',
//         itemKeyList: [],
//         minH: 2,
//         moved: false,
//         isActive: '0',
//         isBorder: '0',
//         minHeight: 2,
//         itemName: '生产车企概况',
//         chartValue: '18',
//         value: [
//           {
//             name: '车企总数',
//             value: '62',
//           },
//           {
//             name: '品牌总数',
//             value: '64',
//           },
//           // {
//           //   name: '车辆类型占比',
//           //   value: [
//           //     {
//           //       type: '商用车',
//           //       color: '#F2BA02',
//           //       value: '87',
//           //     },
//           //     {
//           //       type: '乘用车',
//           //       color: '#66C8FF',
//           //       value: '13',
//           //     },
//           //   ],
//           // },
//           // {
//           //   name: '能源类型占比',
//           //   value: [
//           //     {
//           //       type: '燃油车',
//           //       color: '#EE822F',
//           //       value: '69',
//           //     },
//           //     {
//           //       type: '新能源',
//           //       color: '#2ee56a',
//           //       value: '31',
//           //     },
//           //   ],
//           // },
//         ],
//         createDate: '2024-12-04 15:29:20',
//         updateUser: 'lyg',
//         minWidth: 2,
//         itemEnName: 'Asset Statistics',
//         minW: 2,
//         createUser: 'lyg',
//         canvasIdList: [],
//         contentSourceType: '0',
//       },
//       {
//         x: 8,
//         y: 0,
//         w: 13,
//         h: 19,
//         i: 'C051',
//         itemKey: 'C051',
//         id: 41,
//         isBorder: '0',
//         startWidth: 13,
//         startHeight: 20,
//         updateDate: '2024-12-04 15:29:20',
//         itemKeyList: [],
//         minH: 2,
//         moved: false,
//         isActive: '0',
//         isBorder: '1',
//         minHeight: 2,
//         itemName: '',
//         chartValue: '14',
//         value: [
//           {
//             city: '340100',
//             cityName: '合肥市',
//             count: 2646369,
//             latitude: 31.86119,
//             longitude: 117.283042,
//             carModel: '奇瑞汽车股份有限公司',
//           },
//           {
//             city: '340200',
//             cityName: '芜湖市',
//             count: 2452676,
//             latitude: 31.326319,
//             longitude: 118.376451,
//             carModel: '奇瑞汽车股份有限公司',
//           },
//           {
//             city: '340500',
//             cityName: '马鞍山市',
//             count: 5900,
//             latitude: 31.689362,
//             longitude: 118.507906,
//             carModel: '蔚来汽车有限公司',
//           },
//           {
//             city: '341100',
//             cityName: '滁州市',
//             count: 572,
//             latitude: 32.303627,
//             longitude: 118.316264,
//             carModel: '奇瑞汽车股份有限公司',
//           },
//           {
//             city: '340400',
//             cityName: '淮南市',
//             count: 343,
//             latitude: 32.647574,
//             longitude: 117.018329,
//             carModel: '奇瑞汽车股份有限公司',
//           },
//           {
//             city: '340300',
//             cityName: '蚌埠市',
//             count: 187,
//             latitude: 32.939667,
//             longitude: 117.363228,
//             carModel: '奇瑞汽车股份有限公司',
//           },
//           {
//             city: '340800',
//             cityName: '安庆市',
//             count: 46,
//             latitude: 30.50883,
//             longitude: 117.043551,
//             carModel: '奇瑞汽车股份有限公司',
//           },
//           {
//             city: '341800',
//             cityName: '宣城市',
//             count: 6,
//             latitude: 30.945667,
//             longitude: 118.757995,
//             carModel: '奇瑞汽车股份有限公司',
//           },
//           {
//             city: '341200',
//             cityName: '阜阳市',
//             count: 16,
//             latitude: 32.896969,
//             longitude: 115.819729,
//             carModel: '奇瑞汽车股份有限公司',
//           },
//           {
//             city: '341600',
//             cityName: '亳州市',
//             count: 3,
//             latitude: 33.869338,
//             longitude: 115.782939,
//             carModel: '奇瑞汽车股份有限公司',
//           },
//         ],
//         createDate: '2024-12-04 15:29:20',
//         updateUser: 'lyg',
//         minWidth: 2,
//         itemEnName: 'Asset Statistics',
//         minW: 2,
//         createUser: 'lyg',
//         canvasIdList: [],
//         contentSourceType: '0',
//       },
//       {
//         x: 21,
//         y: 0,
//         w: 8,
//         h: 5,
//         i: 'C052',
//         itemKey: 'C052',
//         id: 42,
//         isBorder: '0',
//         startWidth: 8,
//         startHeight: 5,
//         updateDate: '2024-12-04 15:29:20',
//         itemKeyList: [],
//         minH: 2,
//         moved: false,
//         isActive: '0',
//         isBorder: '0',
//         minHeight: 2,
//         itemName: '汽车重要数据概况',
//         chartValue: '18',
//         value: [
//           {
//             name: '备案车企总数',
//             value: '16',
//             // value: [
//             //   {
//             //     color: '#F2BA02',
//             //     value: '25.7',
//             //   },
//             // ],
//           },
//           {
//             name: '重要数据类型',
//             value: '51',
//             // value: [
//             //   {
//             //     color: '#66C8FF',
//             //     value: '66.7',
//             //   },
//             // ],
//           },
//         ],
//         createDate: '2024-12-04 15:29:20',
//         updateUser: 'lyg',
//         minWidth: 2,
//         itemEnName: 'Asset Statistics',
//         minW: 2,
//         createUser: 'lyg',
//         canvasIdList: [],
//         contentSourceType: '0',
//       },
//       {
//         x: 0,
//         y: 7,
//         w: 8,
//         h: 14,
//         i: 'C053',
//         itemKey: 'C053',
//         id: 43,
//         isBorder: '0',
//         startWidth: 8,
//         startHeight: 14,
//         updateDate: '2024-12-04 15:29:20',
//         itemKeyList: [],
//         minH: 2,
//         moved: false,
//         isActive: '0',
//         isBorder: '0',
//         minHeight: 2,
//         itemName: '企业车联网排名TOP',
//         chartValue: '15',
//         value: [
//           {
//             id: 1,
//             riskNum: 1,
//             riskName: '奇瑞汽车股份有限公司',
//             riskLevel: '0',
//             riskLevelName: '高',
//             riskSafetyIndex: 31.88,
//             riskSafetyCount: 1629356,
//           },
//           {
//             id: 2,
//             riskNum: 2,
//             riskName: '比亚迪汽车工业有限公司',
//             riskLevel: '0',
//             riskLevelName: '高',
//             riskSafetyIndex: 17.79,
//             riskSafetyCount: 909170,
//           },
//           {
//             id: 3,
//             riskNum: 3,
//             riskName: '合肥长安汽车有限公司',
//             riskLevel: '0',
//             riskLevelName: '高',
//             riskSafetyIndex: 16.84,
//             riskSafetyCount: 860536,
//           },
//           {
//             id: 4,
//             riskNum: 4,
//             riskName: '安徽江淮汽车集团股份有限公司',
//             riskLevel: '1',
//             riskLevelName: '中等',
//             riskSafetyIndex: 16.2,
//             riskSafetyCount: 827893,
//           },
//           {
//             id: 5,
//             riskNum: 5,
//             riskName: '奇瑞新能源汽车股份有限公司',
//             riskLevel: '1',
//             riskLevelName: '中等',
//             riskSafetyIndex: 8.54,
//             riskSafetyCount: 436350,
//           },
//           {
//             id: 6,
//             riskNum: 6,
//             riskName: '奇瑞商用车(安徽)有限公司',
//             riskLevel: '1',
//             riskLevelName: '中等',
//             riskSafetyIndex: 7.51,
//             riskSafetyCount: 384039,
//           },
//           {
//             id: 7,
//             riskNum: 7,
//             riskName: '蔚来汽车科技(安徽)有限公司',
//             riskLevel: '1',
//             riskLevelName: '中等',
//             riskSafetyIndex: 0.77,
//             riskSafetyCount: 39263,
//           },
//           {
//             id: 8,
//             riskNum: 8,
//             riskName: '安徽华菱汽车有限公司',
//             riskLevel: '1',
//             riskLevelName: '中等',
//             riskSafetyIndex: 0.11,
//             riskSafetyCount: 5722,
//           },
//           {
//             id: 9,
//             riskNum: 9,
//             riskName: '大众汽车(安徽)有限公司',
//             riskLevel: '1',
//             riskLevelName: '中等',
//             riskSafetyIndex: 0.09,
//             riskSafetyCount: 4488,
//           },
//           {
//             id: 10,
//             riskNum: 10,
//             riskName: '安徽安凯汽车股份有限公司',
//             riskLevel: '1',
//             riskLevelName: '中等',
//             riskSafetyIndex: 0.08,
//             riskSafetyCount: 4173,
//           },
//         ],
//         createDate: '2024-12-04 15:29:20',
//         updateUser: 'lyg',
//         minWidth: 2,
//         itemEnName: 'Asset Statistics',
//         minW: 2,
//         createUser: 'lyg',
//         canvasIdList: [],
//         contentSourceType: '0',
//       },
//       {
//         x: 21,
//         y: 0,
//         w: 8,
//         h: 14,
//         i: 'C054',
//         itemKey: 'C054',
//         id: 44,
//         isBorder: '0',
//         startWidth: 8,
//         startHeight: 14,
//         updateDate: '2024-12-04 15:29:20',
//         itemKeyList: [],
//         minH: 2,
//         moved: false,
//         isActive: '0',
//         isBorder: '0',
//         minHeight: 2,
//         itemName: '车企备案平台信息',
//         chartValue: '15',
//         value: [
//           {
//             id: 1,
//             // riskNum: 5,
//             riskName: '新能源电动汽车远程服务系统',
//             // riskLevel: '0',
//             // riskLevelName: '高',
//             riskSafetyIndex: 100,
//             riskSafetyCount: 2022051400254,
//             riskColor: '#66C8FF',
//           },
//           {
//             id: 2,
//             // riskNum: 2,
//             riskName: '卡嘉车联网平台',
//             // riskLevel: '0',
//             // riskLevelName: '高',
//             riskSafetyIndex: 100,
//             riskSafetyCount: 2022051400255,
//             riskColor: '#4874CB',
//           },
//           {
//             id: 3,
//             // riskNum: 5,
//             riskName: '奇瑞股份车联网系统',
//             // riskLevel: '0',
//             // riskLevelName: '高',
//             riskSafetyIndex: 100,
//             riskSafetyCount: 2022051400190,
//             riskColor: '#66C8FF',
//           },
//           {
//             id: 4,
//             // riskNum: 5,
//             riskName: '汉马科技远程监控平台',
//             // riskLevel: '0',
//             // riskLevelName: '高',
//             riskSafetyIndex: 100,
//             riskSafetyCount: 2022051400187,
//             riskColor: '#66C8FF',
//           },
//           {
//             id: 5,
//             // riskNum: 5,
//             riskName: '安凯客车e控平台',
//             // riskLevel: '0',
//             // riskLevelName: '高',
//             riskSafetyIndex: 100,
//             riskSafetyCount: 2022051400188,
//             riskColor: '#66C8FF',
//           },
//           {
//             id: 6,
//             // riskNum: 5,
//             riskName: '奇瑞新能源车联网系统',
//             // riskLevel: '0',
//             // riskLevelName: '高',
//             riskSafetyIndex: 100,
//             riskSafetyCount: 2022051400191,
//             riskColor: '#66C8FF',
//           },
//           {
//             id: 7,
//             // riskNum: 5,
//             riskName: '奇瑞商用车车联网系统',
//             // riskLevel: '0',
//             // riskLevelName: '高',
//             riskSafetyIndex: 100,
//             riskSafetyCount: 2022051400192,
//             riskColor: '#66C8FF',
//           },
//           {
//             id: 8,
//             // riskNum: 5,
//             riskName: '奇瑞商用车车联网系统',
//             // riskLevel: '0',
//             // riskLevelName: '高',
//             riskSafetyIndex: 100,
//             riskSafetyCount: 2022051400193,
//             riskColor: '#66C8FF',
//           },
//           {
//             id: 9,
//             // riskNum: 5,
//             riskName: '车联网系统',
//             // riskLevel: '0',
//             // riskLevelName: '高',
//             riskSafetyIndex: 100,
//             riskSafetyCount: 2022051400189,
//             riskColor: '#66C8FF',
//           },
//         ],
//         createDate: '2024-12-04 15:29:20',
//         updateUser: 'lyg',
//         minWidth: 2,
//         itemEnName: 'Asset Statistics',
//         minW: 2,
//         createUser: 'lyg',
//         canvasIdList: [],
//         contentSourceType: '0',
//       },
//       {
//         x: 0,
//         y: 19,
//         w: 8,
//         h: 10,
//         i: 'C055',
//         itemKey: 'C055',
//         id: 45,
//         isBorder: '0',
//         startWidth: 8,
//         startHeight: 10,
//         updateDate: '2024-12-04 15:29:20',
//         itemKeyList: [],
//         minH: 2,
//         moved: false,
//         isActive: '0',
//         isBorder: '0',
//         minHeight: 2,
//         itemName: '监管车辆类型占比',
//         chartValue: '16',
//         value: {
//           out: {
//             list: [
//               {
//                 name: '乘用车',
//                 value: 4961352,
//                 percent: '97.08%',
//               },
//               {
//                 name: '商用车',
//                 value: 149142,
//                 percent: '2.92%',
//               },
//             ],
//           },
//           in: {
//             list: [
//               {
//                 name: '新能源',
//                 value: 2667605,
//                 percent: '52.20%',
//               },
//               {
//                 name: '燃油车',
//                 value: 2442889,
//                 percent: '47.80%',
//               },
//             ],
//           },
//         },
//         createDate: '2024-12-04 15:29:20',
//         updateUser: 'lyg',
//         minWidth: 2,
//         itemEnName: 'Asset Statistics',
//         minW: 2,
//         createUser: 'lyg',
//         canvasIdList: [],
//         contentSourceType: '0',
//       },
//       {
//         x: 8,
//         y: 19,
//         w: 13,
//         h: 10,
//         i: 'C056',
//         itemKey: 'C056',
//         id: 46,
//         isBorder: '0',
//         startWidth: 13,
//         startHeight: 10,
//         updateDate: '2024-12-04 15:29:20',
//         itemKeyList: [],
//         minH: 2,
//         moved: false,
//         isActive: '0',
//         isBorder: '0',
//         minHeight: 2,
//         itemName: '车联网年度数据趋势',
//         chartValue: '19',
//         value: [
//           {
//             carValue: '0',
//             carName: '乘用车',
//             arrays: [
//               {
//                 number: 369054,
//                 xName: '2022',
//                 percent: '13.83',
//               },
//               {
//                 number: 439693,
//                 xName: '2023',
//                 percent: '16.48',
//               },
//               {
//                 number: 1462513,
//                 xName: '2024',
//                 percent: '54.82',
//               },
//               {
//                 number: 396345,
//                 xName: '2025',
//                 percent: '14.85',
//               },
//             ],
//           },
//           {
//             carValue: '1',
//             carName: '商用车',
//             arrays: [
//               {
//                 number: 572697,
//                 xName: '2022',
//                 percent: '23.44',
//               },
//               {
//                 number: 694366,
//                 xName: '2023',
//                 percent: '28.42',
//               },
//               {
//                 number: 948318,
//                 xName: '2024',
//                 percent: '38.82',
//               },
//               {
//                 number: 227508,
//                 xName: '2025',
//                 percent: '9.31',
//               },
//             ],
//           },
//         ],
//         createDate: '2024-12-04 15:29:20',
//         updateUser: 'lyg',
//         minWidth: 2,
//         itemEnName: 'Asset Statistics',
//         minW: 2,
//         createUser: 'lyg',
//         canvasIdList: [],
//         contentSourceType: '0',
//       },
//       {
//         x: 21,
//         y: 19,
//         w: 8,
//         h: 10,
//         i: 'C057',
//         itemKey: 'C057',
//         id: 47,
//         isBorder: '0',
//         startWidth: 8,
//         startHeight: 10,
//         updateDate: '2024-12-04 15:29:20',
//         itemKeyList: [],
//         minH: 2,
//         moved: false,
//         isActive: '0',
//         isBorder: '0',
//         minHeight: 2,
//         itemName: '车企备案定级分布',
//         chartValue: '9',
//         value: {
//           total: 16,
//           list: [
//             {
//               name: '3级',
//               value: 14,
//               type: '0',
//             },
//             {
//               name: '2级',
//               value: 2,
//               type: '1',
//             },
//           ],
//         },
//         createDate: '2024-12-04 15:29:20',
//         updateUser: 'lyg',
//         minWidth: 2,
//         itemEnName: 'Asset Statistics',
//         minW: 2,
//         createUser: 'lyg',
//         canvasIdList: [],
//         contentSourceType: '0',
//       },
//     ],
//   },
// ]

export const riskNumColor = [
  '#ca3d3e',
  '#df7746',
  '#e1a822',
  '#79b64c',
  '#00aeee',
  '#ffc000',
  '#ed9595',
  '#F2BA02',
  '#2ee56a',
  '#4874CB',
]

export const riskColor = ['#ff0000', '#ffc000', '#39aa71']

export const colors = ['#fff', '#333']

export const anhuiDefaultPreviewData = [
  {
    // chartValue 3 竖排7 12 10 横排8 13 8
    type: '1',
    defaultList: [
      {
        x: 0,
        y: 0,
        w: 8,
        h: 5,
        i: 'C001',
        itemKey: 'C001',
      },

      {
        x: 8,
        y: 0,
        w: 13,
        h: 19,
        i: 'C004',
        itemKey: 'C004',
      },

      {
        x: 21,
        y: 0,
        w: 8,
        h: 5,
        i: 'C006',
        itemKey: 'C006',
      },

      {
        x: 0,
        y: 7,
        w: 8,
        h: 14,

        i: 'C002',
        itemKey: 'C002',
      },

      {
        x: 21,
        y: 0,
        w: 8,
        h: 14,
        i: 'C007',
        itemKey: 'C007',
      },

      {
        x: 0,
        y: 19,
        w: 8,
        h: 10,
        i: 'C003',
        itemKey: 'C003',
      },

      {
        x: 8,
        y: 19,
        w: 13,
        h: 10,
        i: 'C005',
        itemKey: 'C005',
      },

      {
        x: 21,
        y: 19,
        w: 8,
        h: 10,
        i: 'C008',
        itemKey: 'C008',
      },
    ],
  },
  {
    // chartValue 3 竖排7 12 10 横排8 13 8
    type: '2',
    defaultList: [
      {
        x: 0,
        y: 0,
        w: 8,
        h: 7,
        i: 'C009',
        itemKey: 'C009',
      },

      {
        x: 8,
        y: 0,
        w: 13,
        h: 19,
        i: 'C012',
        itemKey: 'C012',
        mapType: 1,
      },

      {
        x: 8,
        y: 0,
        w: 13,
        h: 19,
        i: 'C019',
        itemKey: 'C019',
        mapType: 2,
      },

      {
        x: 8,
        y: 0,
        w: 13,
        h: 19,
        i: 'C020',
        itemKey: 'C020',
        mapType: 3,
      },

      {
        x: 21,
        y: 0,
        w: 8,
        h: 7,
        i: 'C014',
        itemKey: 'C014',
      },

      //境内TOP
      {
        x: 21,
        y: 0,
        w: 8,
        h: 19,
        i: 'C017',
        itemKey: 'C017',
        headers1: [
          {
            text: '排名',
            value: 'riskNum',
          },
          {
            text: '省份',
            value: 'riskName',
          },
          {
            text: '攻击次数',
            value: 'riskValue',
            align: 'center',
          },
        ],
      },

      //境外TOP
      {
        x: 21,
        y: 0,
        w: 8,
        h: 19,
        i: 'C018',
        itemKey: 'C018',
        headers1: [
          {
            text: '排名',
            value: 'riskNum',
          },
          {
            text: '国家',
            value: 'riskName',
          },
          {
            text: '攻击次数',
            value: 'riskValue',
            align: 'center',
          },
        ],
      },

      {
        x: 0,
        y: 7,
        w: 8,
        h: 12,
        i: 'C010',
        itemKey: 'C010',
      },

      {
        x: 21,
        y: 0,
        w: 8,
        h: 12,
        i: 'C015',
        itemKey: 'C015',
      },

      {
        x: 0,
        y: 19,
        w: 8,
        h: 10,
        i: 'C011',
        itemKey: 'C011',
      },

      {
        x: 8,
        y: 19,
        w: 13,
        h: 10,
        i: 'C013',
        itemKey: 'C013',
        headers1: [
          {
            text: '企业名称',
            value: 'riskName',
          },
          {
            text: '风险等级',
            value: 'riskLevel',
            align: 'center',
          },
          {
            text: '风险类型',
            value: 'desc',
            align: 'center',
          },

          {
            text: '上报时间',
            value: 'updateTime',
          },
          {
            text: '状态',
            value: 'riskStatus',
            align: 'center',
          },
          {
            text: '操作',
            value: 'riskRemark',
            align: 'center',
          },
        ],
      },

      {
        x: 21,
        y: 19,
        w: 8,
        h: 10,
        i: 'C016',
        itemKey: 'C016',
      },
    ],
  },
  {
    type: '3',
    defaultList: [
      // {
      //   x: 0,
      //   y: 0,
      //   w: 8,
      //   h: 10,
      //   i: 'C031',
      //   itemKey: 'C031',
      //   itemName: '已运营',
      // },
      {
        // itemName: '风险总数',
        itemKey: 'C021',
        i: 'C021',
        w: 8,
        h: 6,
        x: 0,
        y: 0,
      },

      {
        // itemName: '风险平台TOP5',
        w: 8,
        x: 0,
        y: 6,
        h: 8,
        i: 'C022',
        itemKey: 'C022',
      },

      {
        // itemName: '风险资产TOP5',
        h: 8,
        i: 'C023',
        itemKey: 'C023',
        w: 8,
        x: 0,
        y: 14,
      },

      {
        // itemName: '风险类型占比',
        h: 7,
        i: 'C024',
        itemKey: 'C024',
        w: 8,
        x: 0,
        y: 22,
      },

      {
        // itemName: '车企安全态势评级',
        h: 22,
        i: 'C025',
        itemKey: 'C025',
        w: 13,
        x: 8,
        y: 0,
      },

      {
        // itemName: '数据采集情况',
        h: 7,
        i: 'C026',
        itemKey: 'C026',
        w: 13,
        x: 8,
        y: 22,
      },

      {
        // itemName: '风险统计',
        h: 6,
        i: 'C027',
        itemKey: 'C027',
        w: 8,
        x: 21,
        y: 0,
      },

      {
        // itemName: '告警事件严重性分布',
        h: 8,
        i: 'C028',
        itemKey: 'C028',
        w: 8,
        x: 21,
        y: 6,
      },

      {
        // itemName: '告警事件类型TOP5',
        h: 8,
        i: 'C029',
        itemKey: 'C029',
        w: 8,
        x: 21,
        y: 14,
      },

      {
        // itemName: '告警事件趋势',
        h: 7,
        i: 'C030',
        itemKey: 'C030',
        w: 8,
        x: 21,
        y: 22,
      },
    ],
  },
]
