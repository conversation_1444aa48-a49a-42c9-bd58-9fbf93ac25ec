// 浅色样式
.theme--light {
  .el-select {
    width: 100%;
    &:hover .el-input__inner {
      border-color: rgba(0, 0, 0, 0.86);
    }
    .el-input__inner {
      border-color: rgba(0, 0, 0, 0.38);
      background-color: transparent;
      padding: 1px 30px 1px 15px;

      &:hover {
        border-color: rgba(0, 0, 0, 0.86);
      }

      &:focus {
        border-color: $primary;
        border-width: 2px;
        padding: 0 29px 0 14px;
      }
    }

    .el-input.is-focus .el-input__inner {
      border-color: $primary;
      border-width: 2px;
      padding: 0 29px 0 14px;
    }

    .el-input .el-select__caret {
      color: rgba(0, 0, 0, 0.54);
      font-size: $font-size-base;
      font-weight: bold;

      transform: scale(0.7) rotateZ(180deg);
    }

    .el-input .el-select__caret.is-reverse {
      transform: scale(0.7) rotateZ(0);
    }

    .el-select__tags {
      margin-left: 4px;
      flex-wrap: nowrap;
      overflow-y: auto;
      ::-webkit-scrollbar {
        width: 8px; /*no*/
        height: 4px; /*no*/
      }

      .el-tag.el-tag--info {
        background-color: $default;
        color: #fff;
        .el-tag__close {
          background-color: #fff;
          color: $default;
          border-radius: 0.375rem;
        }
      }
    }
  }

  .el-input {
    .el-input__inner {
      border-color: rgba(0, 0, 0, 0.2);
      background-color: transparent;
      padding: 1px 30px 1px 15px;

      &:hover {
        border-color: rgba(0, 0, 0, 0.2);
      }

      &:focus {
        border-color: $primary;
        border-width: 2px;
        padding: 0 29px 0 14px;
      }
    }
  }
}
// 下拉框样式
.el-select-dropdown.el-popper {
  margin-top: 0;
  .popper__arrow {
    display: none !important;
  }
}

.el-popper {
  border-radius: 0.375rem;
  box-shadow: 0 3.125rem 6.25rem rgb(50 50 93 / 10%),
    0 0.9375rem 2.1875rem rgb(50 50 93 / 15%),
    0 0.3125rem 0.9375rem rgb(0 0 0 / 10%);
}

.el-select-dropdown__item {
  color: #7b809a;
  &.selected {
    color: #7b809a;
    font-weight: initial;
    background-color: rgba($color: #fff, $alpha: 0.16);
  }
}
.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
  background-color: rgba($color: #adb5bd, $alpha: 0.125);
}

.v-autocomplete__content.v-menu__content.v-menu__content {
  border-radius: 0.375rem;
}

.el-input__inner {
  border-radius: 0.375rem;
}

.el-select-dropdown.is-multiple .el-select-dropdown__item.selected {
  font-weight: bold;
  // color: rgba($color: $primary, $alpha: 1.0);
  color: #7b809a;
  background-color: rgba($color: #adb5bd, $alpha: 0.12);

  &::after {
    top: 0;
  }
}
.el-select__multiple__slot {
  padding: 0.0625rem 0 0.0625rem 0.9375rem;
  color: #606266;
  font-size: 14px;

  & + input {
    margin-left: 0;
  }
}

.el-select-dropdown {
  z-index: 999 !important;
}

// 暗黑模式
.theme--dark {
  $dark-color: rgba(231, 227, 252, 0.87);
  $dark-background-color: #312d4b; // rgb(49, 45, 75)
  .el-select {
    width: 100%;
    &:hover .el-input__inner {
      border-color: rgba(231, 227, 252, 0.86);
    }
    .el-input__inner {
      border-color: rgba(231, 227, 252, 0.38);
      background-color: transparent;
      padding: 1px 30px 1px 15px;
      color: $dark-color;

      &:hover {
        border-color: rgba(231, 227, 252, 0.86);
      }

      &:focus {
        border-color: $primary;
        border-width: 2px;
        padding: 0 29px 0 14px;
      }
    }

    .el-input.is-focus .el-input__inner {
      border-color: $primary;
      border-width: 2px;
      padding: 0 29px 0 14px;
    }

    .el-input .el-select__caret {
      color: rgba(231, 227, 252, 0.54);
      font-size: 12px;
      font-weight: bold;

      transform: scale(0.7) rotateZ(180deg);
    }

    .el-input .el-select__caret.is-reverse {
      transform: scale(0.7) rotateZ(0);
    }

    .el-select__tags {
      margin-left: 4px;
      flex-wrap: nowrap;
      overflow-y: auto;
      ::-webkit-scrollbar {
        width: 8px; /*no*/
        height: 4px; /*no*/
      }

      .el-tag.el-tag--info {
        background-color: $default;
        color: #fff;
        .el-tag__close {
          background-color: #fff;
          color: $default;
          border-radius: 0.375rem;
        }
      }
    }
  }

  .el-input {
    color: $dark-color;
    .el-input__inner {
      border-color: rgba(231, 227, 252, 0.38);
      background-color: transparent;
      padding: 1px 30px 1px 15px;

      &:hover {
        border-color: rgba(231, 227, 252, 0.86);
      }

      &:focus {
        border-color: $primary;
        border-width: 2px;
        padding: 0 29px 0 14px;
      }
    }
  }
  // 暗黑模式：下拉框样式

  .el-select-dropdown {
    border-color: $dark-background-color;
    background-color: $dark-background-color;
    color: rgba(231, 227, 252, 0.87);
  }
  .el-select-dropdown.el-popper {
    margin-top: 0;
    .popper__arrow {
      display: none !important;
    }
  }

  .el-popper {
    border-radius: 0.375rem;
    box-shadow: 0 3.125rem 6.25rem rgb(50 50 93 / 10%),
      0 0.9375rem 2.1875rem rgb(50 50 93 / 15%),
      0 0.3125rem 0.9375rem rgb(0 0 0 / 10%);
  }

  .el-select-dropdown__item {
    color: $dark-color;
    position: relative;
    &.selected {
      position: relative;
      color: $dark-color;
      font-weight: initial;
      background-color: rgba($color: $dark-background-color, $alpha: 0.16);
    }
  }
  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    background-color: $dark-background-color;
    // opacity: 0.08;
  }
  .el-select-dropdown__item:hover::before,
  .el-select-dropdown__item.selected::before {
    opacity: 0.08;
    background-color: currentColor;
    bottom: 0;
    content: '';
    left: 0;
    pointer-events: none;
    position: absolute;
    right: 0;
    top: 0;
    transition: 0.3s cubic-bezier(0.25, 0.8, 0.5, 1);
  }

  .v-autocomplete__content.v-menu__content.v-menu__content {
    border-radius: 0.375rem;
  }

  .el-input__inner {
    border-radius: 0.375rem;
  }

  .el-select-dropdown.is-multiple .el-select-dropdown__item.selected {
    font-weight: bold;
    // color: rgba($color: $primary, $alpha: 1.0);
    color: $dark-color;
    background-color: rgba($color: $dark-background-color, $alpha: 0.12);

    &::after {
      top: 0;
    }
  }
  .el-select__multiple__slot {
    padding: 0.0625rem 0 0.0625rem 0.9375rem;
    color: $dark-color;
    font-size: 14px;

    & + input {
      margin-left: 0;
    }
  }

  .el-select-dropdown {
    z-index: 999 !important;
  }
}
