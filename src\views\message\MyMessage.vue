<template>
  <div class="my-messgae-box">
    <!-- <v-tabs v-model="titleValue" height="54" centered>
      <div v-for="item in titleList" :key="item.value">
        <v-tab class="mr-14" @click="changeTitle(item)">
          {{ $t(item.text) }}（{{ item.num }}）
        </v-tab>
      </div>
    </v-tabs> -->

    <bread-crumb ref="topHeader">
      <template slot="center">
        <div ref="tab">
          <v-tabs v-model="titleValue" height="54" centered>
            <template v-for="item in titleList">
              <v-tab class="mr-14" :key="item.value" @click="changeTitle(item)">
                {{ $t(item.text) }}（{{ item.num }}）
              </v-tab>
            </template>
            <!-- <v-tab
              class="mr-14"
              v-for="item in titleList"
              :key="item.value"
              :disabled="tableLoading"
              @click="changeTitle(item)"
            >
              <span class="ml-2">{{ $t(item.text) }}（{{ item.num }}）</span>
            </v-tab> -->
          </v-tabs>
        </div>
      </template>
    </bread-crumb>
    <v-card tile class="main-content">
      <v-card-text class="pa-0">
        <div class="d-flex align-center justify-space-between flex-row-reverse">
          <div class="d-flex align-center">
            <table-search
              :searchList="searchList"
              :searchQuery="query"
              @search="fetchNote"
            ></table-search>
            <!-- <div class="d-flex align-end">
              <v-select
                outlined
                dense
                v-model="queryKey"
                small
                color="primary"
                :menu-props="{ offsetY: true }"
                append-icon="mdi-chevron-down"
                hide-details
                class="me-3 select-width"
                :items="searchConditions"
                :label="$t('action.queryConditions')"
                @change="changeCondition"
              ></v-select>
              <v-text-field
                v-if="queryKey === 'title'"
                v-model="query[queryKey]"
                color="primary"
                hide-details="auto"
                :label="$t('message.headers.title')"
                dense
                outlined
                class="me-3 text-width"
                clearable
                @click:clear="onClear"
                @keyup.enter.native="$_search"
              ></v-text-field>
              <v-select
                v-else
                v-model="query[queryKey]"
                :items="readStatusList"
                dense
                :label="$t('message.headers.status')"
                hide-details="auto"
                class="me-3 text-width"
                clearable
                outlined
                :menu-props="{ offsetY: true }"
                @change="$_search"
              >
              </v-select>
            </div>
            <vsoc-date-range
              v-model="dateRange.range"
              no-title
              :menu-props="dateRange.menuProps"
              @input="onChangeDate"
              @search="$_search"
            >
              <template v-slot:text="{ on, attrs }">
                <v-text-field
                  type="button"
                  clearable
                  outlined
                  dense
                  class="append-icon-max me-3 date-width"
                  readonly
                  hide-details
                  color="primary"
                  large
                  :label="$t('message.headers.createDate')"
                  prepend-inner-icon="mdi-calendar-range-outline"
                  :value="RANGE_STR(query.startDate, query.endDate)"
                  @click:clear="onChangeDate({ start: '', end: '' })"
                ></v-text-field>
              </template>
            </vsoc-date-range>
            <div>
              <v-btn
                class="primary--text bg-btn"
                elevation="0"
                @click="$_search"
              >
                <span>
                  {{ $t('action.search') }}
                </span>
              </v-btn>
            </div> -->
          </div>

          <div class="d-flex justify-end align-center">
            <v-btn
              elevation="0"
              color="primary"
              @click.stop="setRead"
              v-has:all-read
            >
              <span>
                {{ $t('message.btn.read') }}
              </span>
            </v-btn>
            <v-btn
              elevation="0"
              color="primary"
              class="ml-3"
              v-has:set-note
              @click.stop="setNote"
            >
              <span>
                {{ $t('message.btn.note') }}
              </span>
            </v-btn>
          </div>
        </div>

        <v-data-table
          ref="table"
          fixed-header
          :items-per-page="query.pageSize"
          item-key="id"
          :height="tableHeight"
          hide-default-footer
          :headers="headers"
          :items="tableData"
          class="table border-radius-xl mt-3 thead-light"
          :loading="tableLoading"
          @click:row="setReadItem"
        >
          <template v-slot:item.messageType="{ item }">
            <div class="d-flex">
              <vsoc-icon
                type="fill"
                class="primary--text"
                :icon="
                  item.messageType === '0'
                    ? 'icon-gonggaobiaozhi'
                    : item.messageType === '1'
                    ? 'icon-gongdanbiaozhi'
                    : item.messageType === '2'
                    ? 'icon-gaojingjibiebiaozhi'
                    : ''
                "
              ></vsoc-icon>
              <span class="ml-2">{{ item.messageTypeName }}</span>
              <!-- <v-avatar color="primary" size="2.3rem">
                <span class="white--text text-xxs">{{
                  item.messageTypeName
                }}</span>
              </v-avatar> -->
            </div>
          </template>
          <template v-slot:item.title="{ item }">
            <div v-show-tips style="width: 160px">
              {{ item.title }}
            </div>
          </template>
          <template v-slot:item.content="{ item }">
            <div class="d-flex align-center" style="width: 360px">
              <div v-show-tips>{{ item.content }}</div>
              <div @click.stop>
                <v-btn
                  v-copy="item.content"
                  v-show-tips="$t('action.copy')"
                  icon
                >
                  <!-- <v-icon size="1rem"> mdi-content-copy </v-icon> -->
                  <vsoc-icon
                    size="1.2rem"
                    type="fill"
                    icon="icon-fuzhi"
                  ></vsoc-icon>
                </v-btn>
              </div>
            </div>
          </template>
          <template v-slot:item.createDate="{ item }">
            <span v-show-tips>{{ item.createDate | toDate }}</span>
          </template>
          <template v-slot:item.status="{ item }">
            <v-icon
              v-if="item.status === '1'"
              size="1.1667rem"
              :color="$activeColor"
            >
              mdi-checkbox-marked-circle
            </v-icon>
            <v-icon v-else size="1.5rem" :color="$inactiveColor">
              mdi-close-circle
            </v-icon>
          </template>
          <!-- <template v-slot:item.action="{ item }">
            <v-btn
              small
              color="primary"
              v-if="item.status !== '1'"
              @click.stop="setRead(item)"
            >
              已读
            </v-btn>
          </template> -->
        </v-data-table>

        <!-- 分页器 -->
        <vsoc-pagination
          :page.sync="query.pageNum"
          :size.sync="query.pageSize"
          :total="tableDataTotal"
          @change-page="getMessageList"
          @change-size="getMessageList"
        >
        </vsoc-pagination>
      </v-card-text>
    </v-card>
    <!-- 通知设置 -->
    <note-box ref="refNoteBox" />
  </div>
</template>

<script>
import {
  getMyMessagesList,
  getUnreadStatistics,
  handleRead,
} from '@/api/message/index'
import TableSearch from '@/components/TableSearch/index.vue'
import VsocPagination from '@/components/VsocPagination.vue'
import BreadCrumb from '@/components/bread-crumb/index'
import VsocDateRange from '@/components/vsoc-date-range/VsocDateRange.vue'
import { RANGE_STR } from '@/components/vsoc-date-range/constants'
import { setRemainingHeight } from '@/util/utils'
import { endOfDay, format, startOfDay, subDays } from 'date-fns'
import NoteBox from './Note.vue'
export default {
  name: 'MessageIndex',
  components: {
    VsocPagination,
    VsocDateRange,
    BreadCrumb,
    TableSearch,
    NoteBox,
  },
  data() {
    return {
      queryKey: 'title',
      titleValue: '',
      titleList: [
        { value: '', type: 'total', text: 'message.titleList.all', num: 0 },
        {
          value: '0',
          type: 'noticeCount',
          text: 'message.titleList.notice',
          num: 0,
        },
        {
          value: '1',
          type: 'ticketCount',
          text: 'message.titleList.ticket',
          num: 0,
        },
        {
          value: '2',
          type: 'alarmCount',
          text: 'message.titleList.alert',
          num: 0,
        },
      ],

      // 分页参数
      query: {
        title: '',
        messageType: '',
        startDate: format(
          startOfDay(subDays(new Date(), 7)),
          'yyyy-MM-dd HH:mm:ss',
        ),
        endDate: format(endOfDay(new Date()), 'yyyy-MM-dd HH:mm:ss'),
        status: '0',
        pageNum: 1,
        pageSize: 200,
      },
      // dateRange: {
      //   range: {
      //     start: format(
      //       startOfDay(subDays(new Date(), 7)),
      //       'yyyy-MM-dd HH:mm:ss',
      //     ),
      //     end: format(endOfDay(new Date()), 'yyyy-MM-dd HH:mm:ss'),
      //   },
      //   menuProps: { offsetY: true, closeOnContentClick: false },
      // },

      tableLoading: false,
      tableDataTotal: 0,
      tableHeight: '34.5rem',
      tableData: [],
    }
  },
  computed: {
    searchList() {
      return [
        {
          type: 'multiSearch',
          value: 'status',
          conditions: [
            {
              type: 'input',
              value: 'title',
              text: this.$t('message.headers.title'),
            },
            {
              type: 'select',
              value: 'status',
              text: this.$t('message.headers.status'),
              itemList: this.$store.getters['enums/getReadStatus'],
            },
          ],
        },
        {
          type: 'date',
          value: ['startDate', 'endDate'],
          text: this.$t('message.headers.createDate'),
          dateRange: {
            range: {
              start: format(
                startOfDay(subDays(new Date(), 7)),
                'yyyy-MM-dd HH:mm:ss',
              ),
              end: format(endOfDay(new Date()), 'yyyy-MM-dd HH:mm:ss'),
            },
            menuProps: { offsetY: true, closeOnContentClick: false },
          },
        },
      ]
    },
    headers() {
      return [
        {
          text: this.$t('message.headers.messageType'),
          value: 'messageType',
          width: '100px',
          sortable: true,
        },
        {
          text: this.$t('message.headers.title'),
          value: 'title',
          width: '160px',
          sortable: false,
        },
        {
          text: this.$t('message.headers.content'),
          value: 'content',
          width: '360px',
          sortable: false,
        },
        {
          text: this.$t('global.createDate'),
          value: 'createDate',
          width: '160px',
          sortable: true,
        },
        {
          text: this.$t('message.headers.status'),
          value: 'status', //0未读 1已读
          width: '100px',
          sortable: true,
        },
      ]
    },
    // 查询条件列表
    searchConditions() {
      return [
        {
          text: this.$t('message.headers.title'),
          value: 'title',
        },
        {
          text: this.$t('message.headers.status'),
          value: 'status',
        },
      ]
    },
    readStatusList() {
      return this.$store.getters['enums/getReadStatus']
    },
    MessageTypeList() {
      return this.$store.state.enums.enums.MessageType
    },
  },
  watch: {
    '$route.query.type'(val) {
      if (val) {
        this.init()
      }
    },
  },
  created() {
    this.init()
    this.$_setTableHeight()
    this.$bus.$on('resize', this.$_setTableHeight)
  },

  beforeDestroy() {
    this.$bus.$off('resize', this.$_setTableHeight)
  },

  methods: {
    setNote() {
      this.$refs['refNoteBox'].open()
    },
    onClear() {
      this.query[this.queryKey] = ''
      this.$_search()
    },
    changeCondition(item) {
      this.searchConditions.forEach(v => {
        if (v.value !== item) {
          this.query[v.value] = ''
        }
      })
    },
    init() {
      if (this.$route.query.type) {
        this.titleValue = Number(this.$route.query.type) + 1
        this.query.messageType = this.$route.query.type
      }
      this.fetchNote()
    },
    $_reset() {
      this.query = Object.assign(
        this.$data.query,
        this.$options.data.call(this).query,
      )
      this.$_search()
    },
    fetchNote() {
      this.getAllNum()
      this.$_search()
    },
    changeTitle(item) {
      this.query.messageType = item.value
      this.$_search()
    },
    RANGE_STR,
    // 消息发送时间改变
    onChangeDate(range) {
      this.dateRange.range = range
      this.query.startDate = range.start
      this.query.endDate = range.end
      this.$_search()
    },

    $_setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = setRemainingHeight() - 12
      })
    },

    $_search() {
      this.query.pageNum = 1
      this.getMessageList()
    },
    async setReadItem(item) {
      if (item.status === '1') return
      try {
        const res = await handleRead({
          ids: [item.id],
        })
        if (res.code === 200) {
          this.$notify.info('success', this.$t('message.hint.read1'))
          this.$store.dispatch('global/getMessge')
          this.fetchNote()
        }
      } catch (error) {
        console.error(`已读错误：${error}`)
      }
    },
    async setRead() {
      let unReadIds = this.tableData
        .filter(v => v.status === '0')
        .map(v => v.id)
      if (unReadIds.length === 0) {
        return this.$notify.info('warning', this.$t('message.hint.read2'))
      }
      try {
        const res = await handleRead({
          ids: unReadIds,
        })
        if (res.code === 200) {
          this.$notify.info('success', this.$t('message.hint.read2'))
          this.$store.dispatch('global/getMessge')
          this.fetchNote()
        }
      } catch (error) {
        console.error(`已读错误：${error}`)
      }
    },

    async getAllNum() {
      const res = await getUnreadStatistics()
      const allRecords = res.data || {}
      this.titleList.forEach(item => {
        item.num = allRecords[item.type]
      })
    },

    async getMessageList() {
      this.tableLoading = true
      try {
        const res = await getMyMessagesList(this.query)
        this.tableData = res.data.records || []
        this.tableDataTotal = res.data.total
      } catch (e) {
        console.log(`获取消息列表数据错误：${e}`)
      }
      this.tableLoading = false
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .header {
  align-items: center;
  .v-chip.v-size--default {
    height: auto;
  }
  .v-text-field__details {
    display: none;
  }
  .v-input--dense > .v-input__control > .v-input__slot {
    margin: 0;
  }
}
.navigation-drawer-max-width {
  max-width: 35rem;
  z-index: 10;
}
.my-messgae-box {
  .row {
    padding: 0;
    margin: 0;
  }
  .col {
    padding: 0;
    margin: 0;
  }
}
</style>
