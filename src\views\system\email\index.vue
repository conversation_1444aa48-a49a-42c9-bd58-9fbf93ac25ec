<template>
  <div>
    <bread-crumb>
      <!-- 更新 -->
      <!-- 配置测试 -->
      <template>
        <v-btn v-has:email-test color="primary" elevation="0" @click="onTest">
          {{ $t('email.btn.test') }}
        </v-btn>
        <v-btn
          v-has:email-update
          color="primary"
          elevation="0"
          class="ml-3"
          :loading="isLoading"
          @click="onUpdate"
        >
          {{ $t('action.update') }}
        </v-btn>
      </template>
    </bread-crumb>
    <v-card
      tile
      class="main-content"
      style="height: calc(100vh - 114px); padding: 24px 96px 0 96px !important"
    >
      <v-form ref="form" v-model="valid" lazy-validation>
        <div>
          <v-switch
            class="mt-0"
            style="width: 120px"
            v-model="form.enableEmail"
            inset
            dense
            hide-details
            :true-value="'0'"
            :false-value="'1'"
            :label="$t('email.headers.enableEmail')"
          ></v-switch>
        </div>
        <div class="mt-5">
          <v-text-field
            v-model="form.fromEmailAddress"
            color="primary"
            class="is-required"
            :rules="[
              required(
                form.fromEmailAddress,
                $t('email.headers.fromEmailAddress'),
              ),
              emailValidator,
            ]"
            :label="$t('email.headers.fromEmailAddress')"
          ></v-text-field>
        </div>
        <div class="mt-1">
          <v-text-field
            v-model="form.smtpServer"
            color="primary"
            class="is-required"
            :rules="[required(form.smtpServer, $t('email.headers.smtpServer'))]"
            :label="$t('email.headers.smtpServer')"
          ></v-text-field>
        </div>
        <div class="mt-1">
          <v-text-field
            v-model="form.port"
            color="primary"
            class="is-required"
            :rules="[required(form.port, $t('email.headers.port'))]"
            :label="$t('email.headers.port')"
          ></v-text-field>
        </div>
        <div class="mt-1">
          <v-text-field
            v-model="form.username"
            color="primary"
            class="is-required"
            :rules="[required(form.username, $t('email.headers.username'))]"
            :label="$t('email.headers.username')"
          ></v-text-field>
        </div>
        <div class="mt-1">
          <v-text-field
            v-model="form.password"
            :type="isPasswordVisible ? 'text' : 'password'"
            :append-icon="
              isPasswordVisible ? icons.mdiEyeOffOutline : icons.mdiEyeOutline
            "
            @click:append="isPasswordVisible = !isPasswordVisible"
            color="primary"
            class="is-required"
            :rules="[required(form.password, $t('email.headers.password'))]"
            :label="$t('email.headers.password')"
          ></v-text-field>
        </div>
        <div class="mt-1">
          <v-switch
            class="mt-0"
            style="width: 180px"
            v-model="form.enableSSL"
            inset
            dense
            :true-value="'true'"
            :false-value="'false'"
            hide-details
            :label="$t('email.headers.enableSSL')"
          ></v-switch>
        </div>
      </v-form>
    </v-card>
    <!-- 配置测试 -->
    <vsoc-drawer
      v-model="testEmailShow"
      :title="$t('email.btn.test')"
      @click:confirm="confirmTestEmail"
    >
      <v-form ref="testForm" v-model="testValid" lazy-validation>
        <v-text-field
          v-model="emailAddress"
          color="primary"
          class="is-required"
          :rules="[
            required(emailAddress, $t('email.headers.emailAddress')),
            emailValidator,
          ]"
          :label="$t('email.headers.emailAddress')"
        ></v-text-field>
      </v-form>
    </vsoc-drawer>
  </div>
</template>

<script>
import {
  getData,
  updateEmailServerConfig,
  testSendEmail,
} from '@/api/email/index'
import breadCrumb from '@/components/bread-crumb/index'
import VsocDrawer from '@/components/VsocDrawer.vue'
import { emailValidator, required } from '@core/utils/validation'
import { deepClone } from '@/util/utils'
import { mdiChevronLeft, mdiEyeOffOutline, mdiEyeOutline } from '@mdi/js'

export default {
  name: 'emailConfig',
  components: { breadCrumb, VsocDrawer },
  data() {
    return {
      isPasswordVisible: false,
      icons: {
        mdiChevronLeft,
        mdiEyeOffOutline,
        mdiEyeOutline,
      },
      emailAddress: '',
      testEmailShow: false,
      testValid: true,
      isLoading: false,
      emailValidator,
      required,
      valid: true,
      form: {
        enableEmail: '1', //是否启用邮箱 0开启 1关闭 （默认不启用）
        fromEmailAddress: '',
        smtpServer: '', //服务器地址
        port: '', //服务器端口
        username: '', //username
        password: '', //授权密码
        enableSSL: '', //启用SSL加密 true启用 false关闭
      },
    }
  },
  computed: {},
  created() {
    this.loadData()
  },
  methods: {
    async onUpdate() {
      try {
        this.isLoading = true
        const bool = await this.$refs.form.validate()
        if (!bool) return
        const data = deepClone(this.form)
        const res = await updateEmailServerConfig(data)
        if (res.code === 200) {
          this.$notify.info('success', this.$t('email.hint'))
          this.loadData()
        }
      } catch (e) {
        console.error(`更新失败:${e}`)
      } finally {
        this.isLoading = false
      }
    },

    onTest() {
      this.emailAddress = ''
      this.testEmailShow = true
      this.$refs.testForm.resetValidation()
    },

    async confirmTestEmail(callBack) {
      try {
        const bool = await this.$refs.testForm.validate()
        if (!bool) return callBack(false, true)
        const params = {
          toEmailAddress: this.emailAddress,
          // emailSubject: 'Notification Test',
          // emailContent: 'SMTP configuration test',
        }
        const res = await testSendEmail(params)
        if (res.code === 200) {
          callBack()
          this.$notify.info('success', this.$t('email.hint1'))
        }
      } catch (e) {
        callBack(false, true)
        console.error(`测试失败：${e}`)
      }
    },
    async loadData() {
      try {
        const { data } = await getData()
        this.form = data
      } catch (err) {
        console.log('邮箱查询报错', err)
      } finally {
      }
    },
  },
}
</script>
