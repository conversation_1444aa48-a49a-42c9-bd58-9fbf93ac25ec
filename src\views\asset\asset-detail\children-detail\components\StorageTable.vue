<template>
  <v-card class="h-full">
    <v-card-text>
      <v-tabs
        height="3.5rem"
        class="rounded-xxl bg-body mb-4 asset-children"
        active-class="rounded-xxl ma-1 white--text primary"
        centered
        grow
        hide-slider
        v-model="currentTab"
      >
        <v-tab>{{ $t('asset.idpsTab2.equipmentApplication') }}</v-tab>
        <v-tab>{{ $t('asset.idpsTab2.processDetails.title') }}</v-tab>
        <v-tab>{{ $t('asset.idpsTab2.listeningPort') }}</v-tab>
      </v-tabs>
      <v-tabs-items v-model="currentTab">
        <v-tab-item>
          <v-row dense style="height: 19.5rem" class="d-flex ma-0 px-2">
            <v-col cols="6">
              <v-card
                outlined
                class="h-100 pa-3 text--primary d-flex flex-column justify-space-between"
              >
                <div class="text-xxl">
                  {{ $t('asset.idpsTab2.processDetails.userTotal') }}
                </div>
                <div>
                  <span class="text-6xl mr-3">{{
                    equipmentApplicationData.userObj.count
                  }}</span>
                  <span>
                    <span
                      :class="`${equipmentApplicationData.userObj.color}--text`"
                      class="opacity-b1 rounded-sm"
                    >
                      <i class="iconfont icon-xiajiang"></i>
                      <!-- <v-icon :color="equipmentApplicationData.userObj.color"
                        >mdi-arrow-down-thin</v-icon
                      > -->
                    </span>
                    <span
                      :class="`${equipmentApplicationData.userObj.color}--text`"
                      class="text-xxl ml-2"
                      >{{ equipmentApplicationData.userObj.percent }}</span
                    >
                  </span>
                </div>
                <!-- <v-sparkline
                  :value="[52, 73, 59, 52, 44, 80, 59, 44, 59]"
                  type="bar"
                  :gradient="['#533df126', '#533DF1', '#533df126', '#533df126']"
                  gradient-direction="right"
                  :line-width="6"
                  :smooth="4"
                  padding="10"
                  stroke-linecap="round"
                ></v-sparkline> -->
                <v-img
                  src="../../../../../assets/images/pages/<EMAIL>"
                  contain
                  max-height="6.6rem"
                  min-width="90%"
                  class="mx-3"
                />
              </v-card>
            </v-col>
            <v-col cols="6">
              <v-card
                outlined
                class="h-100 pa-3 text--primary d-flex flex-column justify-space-between"
              >
                <div class="text-xxl">
                  {{ $t('asset.idpsTab2.processDetails.processTotal') }}
                </div>
                <div>
                  <span class="text-6xl mr-3">{{
                    equipmentApplicationData.processObj.count
                  }}</span>
                  <span>
                    <span
                      :class="`${equipmentApplicationData.processObj.color}--text`"
                      class="opacity-b1 rounded-sm font-weight-medium"
                    >
                      <i class="iconfont icon-shangsheng"></i>
                      <!-- <v-icon :color="equipmentApplicationData.processObj.color"
                        >mdi-arrow-up-thin</v-icon
                      > -->
                    </span>
                    <span
                      :class="`${equipmentApplicationData.processObj.color}--text`"
                      class="text-xxl ml-2"
                      >{{ equipmentApplicationData.processObj.percent }}</span
                    >
                  </span>
                </div>

                <v-img
                  src="../../../../../assets/images/pages/<EMAIL>"
                  contain
                  max-height="6.6rem"
                  min-width="90%"
                  class="mx-3"
                />
              </v-card>
            </v-col>
          </v-row>
        </v-tab-item>
        <v-tab-item>
          <v-data-table
            :headers="headers"
            :items="tableData"
            :item-class="record => rowClass(record, tableData)"
            height="19.5rem"
            class="asset-children"
            item-key="id"
            :items-per-page="tableData.length"
            hide-default-footer
          ></v-data-table>
        </v-tab-item>
        <v-tab-item>
          <v-data-table
            :headers="headers2"
            :items="tableData2"
            :item-class="record => rowClass(record, tableData2)"
            height="19.5rem"
            class="asset-children"
            item-key="id"
            :items-per-page="tableData2.length"
            hide-default-footer
          ></v-data-table>
        </v-tab-item>
      </v-tabs-items>
    </v-card-text>
  </v-card>
</template>

<script>
export default {
  data() {
    return {
      currentTab: 0,

      tableData: [
        {
          id: 1,
          userId: 'admin',
          process: '34',
          shell: 'Null',
          cpu: '58%',
          ram: '66%',
          rom: '89%',
        },
        {
          id: 2,
          userId: 'admin',
          process: '34',
          shell: 'Null',
          cpu: '58%',
          ram: '66%',
          rom: '89%',
        },
        {
          id: 3,
          userId: 'admin',
          process: '34',
          shell: 'Null',
          cpu: '58%',
          ram: '66%',
          rom: '89%',
        },
        {
          id: 4,
          userId: 'admin',
          process: '34',
          shell: 'Null',
          cpu: '58%',
          ram: '66%',
          rom: '89%',
        },
        {
          id: 5,
          userId: 'admin',
          process: '34',
          shell: 'Null',
          cpu: '58%',
          ram: '66%',
          rom: '89%',
        },
      ],
      equipmentApplicationData: {
        userObj: {
          count: 7,
          status: 'down',
          color: 'warning',
          percent: '20%',
        },
        processObj: {
          count: 28,
          status: 'up',
          color: 'error',
          percent: '13%',
        },
      },
      headers2: [
        {
          text: 'Proto',
          value: 'proto',
          width: 80,
          align: 'center',
          sortable: false,
        },
        {
          text: 'Recy-Q',
          value: 'recyQ',
          width: 80,
          align: 'center',
          sortable: false,
        },
        {
          text: 'Send-Q',
          value: 'sendQ',
          width: 80,
          align: 'center',
          sortable: false,
        },
        {
          text: 'Local Address',
          value: 'localAddress',
          width: 160,
          align: 'center',
          sortable: false,
        },
        {
          text: 'Foreign Address',
          value: 'foreignAddress',
          width: 160,
          align: 'center',
          sortable: false,
        },
        {
          text: 'State',
          value: 'state',
          width: 160,
          align: 'center',
          sortable: false,
        },
        {
          text: 'User',
          value: 'user',
          width: 80,
          align: 'center',
          sortable: false,
        },
      ],
      tableData2: [
        {
          id: '0',
          proto: 'tcp6',
          recyQ: '0',
          sendQ: '0',
          localAddress: '::ffff:192.168.49.:7912',
          foreignAddress: '::ffff:192.168.49:34018',
          state: 'ESTABLISHED',
          user: 'shell',
        },
        {
          id: '1',
          proto: 'tcp6',
          recyQ: '0',
          sendQ: '0',
          localAddress: 'localhost:55644',
          foreignAddress: 'localhost:7912',
          state: 'ESTABLISHED',
          user: 'u0_a113',
        },
        {
          id: '2',
          proto: 'tcp6',
          recyQ: '0',
          sendQ: '0',
          localAddress: 'localhost:55640',
          foreignAddress: 'localhost:7912',
          state: 'ESTABLISHED',
          user: 'u0_a113',
        },
        {
          id: '3',
          proto: 'tcp6',
          recyQ: '0',
          sendQ: '0',
          localAddress: 'localhost:7912',
          foreignAddress: 'localhost:55650',
          state: 'ESTABLISHED',
          user: 'shell',
        },
        {
          id: '4',
          proto: 'tcp6',
          recyQ: '0',
          sendQ: '0',
          localAddress: 'localhost:55642',
          foreignAddress: 'localhost:7912',
          state: 'ESTABLISHED',
          user: 'u0_a113',
        },
        {
          id: '5',
          proto: 'tcp6',
          recyQ: '0',
          sendQ: '0',
          localAddress: '::ffff:192.168.49.:7912',
          foreignAddress: '::ffff:192.168.49:33486',
          state: 'ESTABLISHED',
          user: 'shell',
        },
        {
          id: '6',
          proto: 'tcp6',
          recyQ: '0',
          sendQ: '0',
          localAddress: 'localhost:7912',
          foreignAddress: 'localhost:55644',
          state: 'ESTABLISHED',
          user: 'shell',
        },
        {
          id: '7',
          proto: 'tcp6',
          recyQ: '0',
          sendQ: '0',
          localAddress: 'localhost:7912',
          foreignAddress: 'localhost:55640',
          state: 'ESTABLISHED',
          user: 'shell',
        },
        {
          id: '8',
          proto: 'tcp6',
          recyQ: '0',
          sendQ: '0',
          localAddress: 'localhost:55650',
          foreignAddress: 'localhost:7912',
          state: 'ESTABLISHED',
          user: 'u0_a113',
        },
        {
          id: '9',
          proto: 'tcp6',
          recyQ: '0',
          sendQ: '0',
          localAddress: 'localhost:7912',
          foreignAddress: 'localhost:55642',
          state: 'ESTABLISHED',
          user: 'shell',
        },
      ],
    }
  },
  computed: {
    headers() {
      return [
        {
          text: this.$t('asset.idpsTab2.processDetails.headers.user'),
          value: 'userId',
          width: 80,
          align: 'center',
          sortable: false,
        },
        {
          text: this.$t('asset.idpsTab2.processDetails.headers.process'),
          value: 'process',
          width: 100,
          align: 'center',
          sortable: false,
        },
        {
          text: this.$t('asset.idpsTab2.processDetails.headers.startCommand'),
          value: 'shell',
          width: 100,
          align: 'center',
          sortable: false,
        },
        {
          text: this.$t('asset.idpsTab2.processDetails.headers.cpu'),
          value: 'cpu',
          width: 100,
          align: 'center',
          sortable: false,
        },
        {
          text: this.$t('asset.idpsTab2.processDetails.headers.ram'),
          value: 'ram',
          width: 100,
          align: 'center',
          sortable: false,
        },
        {
          text: this.$t('asset.idpsTab2.processDetails.headers.rom'),
          value: 'rom',
          width: 100,
          align: 'center',
          sortable: false,
        },
      ]
    },
  },
  methods: {
    rowClass(record, tableData) {
      let index = tableData.findIndex(item => item.id === record.id)
      if (index % 2 === 0) {
        return 'bg-body'
      }
      return
    },
  },
}
</script>
