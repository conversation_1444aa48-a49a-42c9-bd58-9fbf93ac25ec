<template>
  <div class="box py-0 px-1 align-center">
    <div class="fs-16 color--primary w-100 text-center">
      <i class="iconfont icon-kapianshuaxin fs-18"></i>
      <span class="mx-2"
        >{{ $t('global.updateDiff', []) }}{{ refreshDate | getDateDiff }}</span
      >
      {{ refreshDate | toDate }}
    </div>
    <div class="animate shield d-flex flex-column justify-center align-center">
      <div
        class="fs-64 c1-posture"
        v-if="vehicleStatus[posture].color"
        :style="{ color: vehicleStatus[posture].color }"
      >
        {{ vehicleStatus[posture].text1 }}
      </div>
      <div class="c1-text">{{ $t('posture.serviceSituation') }}</div>
    </div>

    <!-- <v-img
      class="animate"
      contain
      :max-height="$getCeilSize(400)"
      :max-width="$getCeilSize(275)"
      src="../images/<EMAIL>"
    ></v-img> -->
    <!-- <div class="box-header position-relative ml-3 mt-4 box-header2">
      {{ $t('screen.assetTotal') }}
      <dv-digital-flop
        v-resize="onResize"
        ref="digitalFlop"
        :config="itemConfig"
        class="digital"
      />
    </div>

    <div class="d-flex justify-lg-space-between">
      <div class="d-flex flex-row">
        <div class="box-left">
          <dv-border-box-14 class="border-box2">
            <span class="box-center-left-text">{{
              $t('screen.alertTotal')
            }}</span>
            <dv-digital-flop
              ref="alertDigital"
              :config="alertConfig"
              class="box-center-left-num"
            />
          </dv-border-box-14>

          <dv-border-box-14 class="border-box2">
            <span class="box-center-left-text">{{
              $t('screen.ticketTotal')
            }}</span>
            <dv-digital-flop
              ref="orderDigital"
              :config="orderConfig"
              class="box-center-left-num"
            />
          </dv-border-box-14>
        </div>
        <v-img
          class="img-left"
          :height="$getCeilSize(133)"
          :min-width="$getCeilSize(79)"
          :aspect-ratio="133 / 79"
          contain
          src="../images/line1.png"
        ></v-img>
      </div>
      <div class="d-flex flex-row">
        <div>
          <div class="box-h"></div>
          <v-img
            class="img-right"
            :height="$getCeilSize(144)"
            :min-width="$getCeilSize(58)"
            :aspect-ratio="144 / 58"
            contain
            src="../images/line2.png"
          ></v-img>
        </div>
        <div class="box-right box-h">
          <div class="h-100">
            <div class="box-right-text">
              {{ $t('screen.vulnerabilityTotal') }}
            </div>
            <div class="position-relative" style="height: 50%">
              <dv-digital-flop
                ref="carDigital"
                :config="carConfig"
                class="box-center-left-num"
                style="left: -3%; top: 45%; width: 100%"
              />
            </div>
          </div>
          <v-img></v-img>
        </div>
      </div>
    </div> -->
  </div>
</template>

<script>
import { numberToFormat } from '@/util/filters'
import { getRoundSize } from './chart'
import DvBorderBox14 from './dv-border-box14/Index.vue'
const baseStyle = {
  fill: '#44E2FE',
  fontWeight: 600,
  textAlign: 'center',
  fontStyle: 'normal',
  fontFamily:
    '"PingFang SC", "Source Han Sans CN", "STHeiti", "Microsoft YaHei"',
}
export default {
  name: 'CenterChart1',
  props: {
    refreshDate: [Date, String],
    posture: [String, Number],
    // assetCount: [Number, String],
    // ticketCount: [Number, String],
    // alarmCount: [Number, String],
    // vulneraCount: [Number, String],
  },
  components: {
    DvBorderBox14,
  },
  data() {
    return {
      timer: null,
      itemTotal: 1451,
      carTotal: 141,
      orderTotal: 561,
      alertTotal: 78,
      // itemConfig: 1451,
    }
  },
  computed: {
    vehicleStatus() {
      return this.$store.state.enums.enums.HealthStatus
    },
    alertConfig() {
      return {
        number: [this.alertTotal],
        content: '{nt}',
        formatter: numberToFormat,
        textAlign: 'right',
        style: {
          ...baseStyle,
          fontSize: getRoundSize(24),
          lineHeight: getRoundSize(34),
        },
      }
    },
    itemConfig() {
      return {
        number: [this.itemTotal],
        content: '{nt}',
        formatter: numberToFormat,
        style: {
          ...baseStyle,
          fontSize: getRoundSize(33),
          lineHeight: getRoundSize(46),
        },
      }
    },
    orderConfig() {
      return {
        number: [this.orderTotal],
        content: '{nt}',
        textAlign: 'right',
        formatter: numberToFormat,
        style: {
          ...baseStyle,
          fontSize: getRoundSize(24),
          lineHeight: getRoundSize(34),
        },
      }
    },
    carConfig() {
      return {
        number: [this.carTotal],
        content: '{nt}',
        textAlign: 'left',
        formatter: numberToFormat,
        style: {
          ...baseStyle,
          fill: '#ff385d',
          fontSize: getRoundSize(40),
          lineHeight: getRoundSize(60),
        },
      }
    },
  },
  // watch: {
  //   assetCount() {
  //     this.toggleNum()
  //   },
  // },
  // mounted() {
  //   this.timer = setInterval(() => this.toggleNum(), 5000)
  // },
  // beforeDestroy() {
  //   clearInterval(this.timer)
  // },
  methods: {
    onResize() {
      this.toggleResize('digitalFlop')
      this.toggleResize('carDigital')
      this.toggleResize('orderDigital')
      this.toggleResize('alertDigital')
      this.toggleNum()
    },
    toggleResize(refName) {
      const ref = this.$refs[refName]
      if (ref) {
        ref.renderer = null
        ref.init()
      }
    },
    async toggleNum() {
      this.itemTotal = 0
      this.carTotal = 0
      this.orderTotal = 0
      this.alertTotal = 0
      this.itemTotal = await Promise.resolve(this.assetCount)
      this.carTotal = await Promise.resolve(this.vulneraCount)
      this.orderTotal = await Promise.resolve(this.ticketCount)
      this.alertTotal = await Promise.resolve(this.alarmCount)
    },
  },
}
</script>
