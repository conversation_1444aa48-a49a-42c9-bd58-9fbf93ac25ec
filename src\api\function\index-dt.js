// import { cmdbPath, request } from '../../util/request'

// 获取函数列表
export function getFunctionListDt(params) {
  return {
    content: [
      {
        createDate: null,
        updateDate: 1665960715680,
        createUser: null,
        updateUser: 'currentUser',
        id: 1,
        function_name: 'timeDiff',
        grammar: 'timeDiff(StartTime, EndTime, TimeUnit)',
        parameters: ` StartTime - Timestamp ${'\n'} EndTime - Timestamp ${'\n'} TimeUnit -'Text' - ['seconds', 'minutes', 'hours', 'days']`,
        description:
          '返回根据提供的TimeUnit计算StartTime到EndTime之间的时间间隔',
        state: 1,
      },
      {
        createDate: null,
        updateDate: 1665960700070,
        createUser: null,
        updateUser: 'currentUser',
        id: 2,
        function_name: 'size',
        grammar: 'size(variable)',
        parameters: 'variable - Array or String',
        description: '返回参数的长度',
        state: 1,
      },
      {
        createDate: null,
        updateDate: 1665960703456,
        createUser: null,
        updateUser: 'currentUser',
        id: 3,
        function_name: 'lowercase',
        grammar: 'lowercase(variable)',
        parameters: 'variable - Text',
        description: '返回参数的小写字符串',
        state: 1,
      },
      {
        createDate: null,
        updateDate: 1665960706570,
        createUser: null,
        updateUser: 'currentUser',
        id: 4,
        function_name: 'abs',
        grammar: 'abs(variable)',
        parameters: 'variable - Numeric',
        description:
          '返回数字的绝对值; 一个数字的绝对值是该数字不带其符号的形式',
        state: 1,
      },

      // {
      //   createDate: null,
      //   updateDate: 1665960709711,
      //   createUser: null,
      //   updateUser: 'currentUser',
      //   id: 7,
      //   function_name: 'tripJourneyDistance',
      //   grammar: 'tripJourneyDistance(currentOdometer)',
      //   parameters: 'currentOdometer - Numeric',
      //   description: '返回行程距离',
      //   state: 1,
      // },
      // {
      //   createDate: null,
      //   updateDate: 1665960712637,
      //   createUser: null,
      //   updateUser: 'currentUser',
      //   id: 9,
      //   function_name: 'inclusionTime',
      //   grammar: 'inclusionTime(currentMessageSendTime)',
      //   parameters: 'currentMessageSendTime - Timestamp',
      //   description: null,
      //   state: 1,
      // },
      {
        createDate: null,
        updateDate: 1665960712637,
        createUser: null,
        updateUser: 'currentUser',
        id: 9,
        function_name: 'splitToNumber',
        grammar: 'splitToNumber(variable,delimiter, index)',
        parameters: ` variable - Text ${'\n'} delimiter - Text - 按给定分隔符拆分输入 ${'\n'} index - Numeric - 索引从0开始`,
        description:
          'Splits input by qiven delimiter, returns item at index as number',
        state: 1,
      },
      {
        createDate: null,
        updateDate: 1665960718879,
        createUser: null,
        updateUser: 'currentUser',
        id: 5,
        function_name: 'substring',
        grammar: 'substring(variable, startIndex, endIndex)',
        parameters: ` variable - Text - 入参 ${'\n'} startIndex - Numeric -起始索引（包括）, 索引从 0 开始 ${'\n'} endIndex - Numeric - 结束索引（不包括）`,
        description: '返回字符串的子字符串',
        state: 1,
      },
      {
        createDate: null,
        updateDate: 1665960722244,
        createUser: null,
        updateUser: 'currentUser',
        id: 6,
        function_name: 'versionCompare',
        grammar: 'versionCompare(firstVersion, secondVersion)',
        parameters: ` firstVersion - 'Text' ${'\n'} secondVersion - 'Text'`,
        description:
          'Compares signals representing versions and returns one of the following numeric values: 1 (versionA > versionB), 0 (versionA == versionB), -1 (versionA < versionB), null (error)',
        state: 1,
      },
      {
        createDate: null,
        updateDate: 1665960715680,
        createUser: null,
        updateUser: 'currentUser',
        id: 7,
        function_name: 'FenceCheck',
        grammar: 'FenceCheck(Longitude,Latitude,Range)',
        parameters: ` Longitude - Numeric  ${'\n'} Latitude - Numeric ${'\n'} Range - List(电子围栏区域)`,
        description:
          '根据提供的经/纬度计算是否在电子围栏区域，如果在范围内则返回True,不再范围内则返回False; 返回值类型：Boolean；',
        state: 1,
      },
      {
        createDate: null,
        updateDate: 1665960715680,
        createUser: null,
        updateUser: 'currentUser',
        id: 8,
        function_name: 'LocationCheck',
        grammar: 'LocationCheck(Longitude,Latitude)',
        parameters: `Longitude - Numeric  ${'\n'}Latitude - Numeric ${'\n'}`,
        description:
          '根据提供的经/纬度计算该经纬度所在的中国境内省级行政区(包括自治区/直辖市)，返回省级行政区的中文名称(String类型)如果经纬度不在中国境内，则返回"境外"',
        state: 1,
      },
      {
        createDate: null,
        updateDate: 1665960715680,
        createUser: null,
        updateUser: 'currentUser',
        id: 10,
        function_name: 'CIDSCheck',
        grammar:
          'CIDSCheck(Vehicle_Type,Ruleset_Version,Alert_Type,Algorithm_index)',
        parameters: `Vehicle_Type - Text(车端上报的车型代码) ${'\n'}Ruleset_Version - Text(车端上报的Ruleset主版本号) ${'\n'}Alert_type - Text(车端上报的告警类型) ${'\n'}Algorithm_index - Text(车端上报的CIDS的算法ID)`,
        description:
          '根据车端上报的车型以及主版本号与云端管理的Ruleset集合进行对比，如果符合需求约定的规则，则返回1 (通过), 如果找到对应版本但不符合约定规则，则返回0(不通过); 如果找不到对应版本或者异常场景，则返回-1(异常); 返回值类型:Number',
        state: 1,
      },
      {
        createDate: null,
        updateDate: 1665960715680,
        createUser: null,
        updateUser: 'currentUser',
        id: 11,
        function_name: 'EIDSCheck',
        grammar: 'EIDSCheck(Vehicle_Type,Ruleset_Version,Alert_Type,Rule_List)',
        parameters: `Vehicle_Type - Text(车端上报的车型代码) ${'\n'}Ruleset_Version - Text(车端上报的Ruleset主版本号) ${'\n'}Alert_type - Text(车端上报的告警类型) ${'\n'}Rule_List - ObjectArray(车端上报的EIDS的RuleId集合)`,
        description:
          '根据车端上报的车型以及主版本号与云端管理的Ruleset集合进行对比，如果符合需求约定的规则，则返回1 (通过), 如果找到对应版本但不符合约定规则，则返回0(不通过); 如果找不到对应版本或者异常场景，则返回-1(异常); 返回值类型:Number',
        state: 1,
      },
      {
        createDate: null,
        updateDate: 1665960715680,
        createUser: null,
        updateUser: 'currentUser',
        id: 12,
        function_name: 'getJsonValue',
        grammar: 'getJsonValue(variable,key)',
        parameters: `variable - String(待处理的JSON字符串) ${'\n'}key - String(要获取的键名)`,
        description:
          '从JSON字符串中提取指定key的value值,如果JSON格式化失败，返回"-99999",如果获取不到值，返回"-99990"',
        state: 1,
      },
      {
        createDate: null,
        updateDate: 1665960715680,
        createUser: null,
        updateUser: 'currentUser',
        id: 13,
        function_name: 'split',
        grammar: 'split(variable, delimiter, index)',
        parameters: `variable - Text(需要分割的原始字符串) ${'\n'}delimiter - Text(分隔符（支持单字符或多字符）) ${'\n'}index - Numeric(目标子串的索引（从0开始）)`,
        description:
          '返回分割后的第index个字符串元素（保留原始类型）; 返回值类型:Text',
        state: 1,
      },
      // {
      //   createDate: null,
      //   updateDate: 1665960725874,
      //   createUser: null,
      //   updateUser: 'currentUser',
      //   id: 8,
      //   function_name: 'countryCodeAlpha',
      //   grammar: 'countryCodeAlpha(lat,lon,countryCode)',
      //   parameters: 'lat - Numeric \\n lon - Numeric \\n countryCode - Text',
      //   description: null,
      //   state: 1,
      // },
    ],
    pageable: {
      sort: { empty: true, sorted: false, unsorted: true },
      offset: 0,
      pageNumber: 0,
      pageSize: 2000,
      paged: true,
      unpaged: false,
    },
    last: true,
    totalPages: 1,
    totalElements: 9,
    size: 2000,
    number: 0,
    sort: { empty: true, sorted: false, unsorted: true },
    first: true,
    numberOfElements: 9,
    empty: false,
  }
}

// 新增函数
export function addFunctionDt(data) {
  return request({
    url: `${cmdbPath}/vehicleFunction`,
    method: 'post',
    data,
  })
}

// 更新函数
export function updatetFunctionDt(data) {
  return request({
    url: `${cmdbPath}/vehicleFunction/${data.id}`,
    method: 'put',
    data,
  })
}

// 删除函数
export function deleteFunctionDt(id) {
  return request({
    url: `${cmdbPath}/vehicleFunction/${id}`,
    method: 'delete',
  })
}
