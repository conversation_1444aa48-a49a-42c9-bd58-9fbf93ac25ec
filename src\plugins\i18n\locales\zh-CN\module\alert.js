const alert = {
  currentTitle: '告警',
  headers: {
    severity: '级别',
    id: '告警编号',
    name: '告警名称',
    type: '告警类型',
    description: '描述',
    asset: '影响资产',
    vin: 'VIN',
    deviceType: '设备类型',
    model: '所属车企',
    status: '状态',
    tag: '告警分类',
    triggered: '触发时间',
    update: '最后更新时间',
    location: '告警位置',
    year: '车辆年份',
    engine: '引擎类型',
    unhandledAlertStatus: '未处理告警状态',
    handledAlertStatus: '已处理告警状态',
    extent: '告警范围',
    assertId: '资产编号',
    occurrenceTime: '发生时间',
    suppressionFrequency: '抑制次数',
  },
  btn: {
    status: '告警变更',
    status1: '告警变更',
    more: '更多操作',
    advanced: '调查取证',
    ticket: '转工单',
    dispose: '查看处置',
    export1: '精选导出',
    export2: '时段导出',
    changeStatus: '状态变更',
    detection: '查看检测',
  },
  hint: {
    tip: '时间范围不能超过60天！',
    update: '更新成功！',
    updateTip: '{0}条告警信息将更新',
    updateTip1: '告警编号为【{0}】的告警信息将更新',
    detailTip: '基于平台接收的消息得出的时间',
    detailTip1: '更新于{0}',
    tip: '点击创建查询值',
  },
  detail: {
    last6Months: '最近6个月',
    last24Hours: '最近24小时',
    occurrences: '发生次数',
    assets: '影响资产数',
    info: '资产基本信息',
    posture: '当前态势',
    alerts: '未处理告警',
    firstTime: '首次登记时间',
    lastTime: '最后接收时间',
    lastLocation: '最后位置',
    startTime: '开始时间',
    endTime: '结束时间',
    timeLine: '资产事件轴',
    table: '资产列表',
    mapBox: {
      tips1: '未搜索到“{0}”相关的内容',
      tips2: '请尝试其他搜索',
      tips3: 'N/A--地址未解析完成',
    },
  },
}

export default alert
