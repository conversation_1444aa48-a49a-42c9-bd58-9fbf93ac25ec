import { request, vsocPath } from '../../util/request'
//获取工单列表
export function getTicketList(data) {
  return request({
    url: `${vsocPath}/ticket/queryPage`,
    method: 'post',
    data,
  })
}

//获取详情
export function getTicketById(data) {
  return request({
    url: `${vsocPath}/ticket/ticketDetails`,
    method: 'post',
    data,
  })
}

//获取已有的工单分类
export function getTicketClassify(data) {
  return request({
    url: `${vsocPath}/classifyManage/queryPage`,
    method: 'post',
    data,
  })
}

//创建工单分类
export function addTicketClassify(data) {
  return request({
    url: `${vsocPath}/ticketClassify/addClassify`,
    method: 'post',
    data,
  })
}

//新增工单
export function addTicket(data) {
  return request({
    url: `${vsocPath}/ticket/addTicket`,
    method: 'post',
    data,
  })
}

//修改工单
export function editTicket(data) {
  return request({
    url: `${vsocPath}/ticket/updateTicket`,
    method: 'post',
    data,
  })
}
//获取所有操作类型
export function ticketOperationType() {
  return request({
    url: `${vsocPath}/ticket/ticketOperationType`,
    method: 'post',
  })
}
//批量导出工单
export function exportBatch(data) {
  return request({
    url: `${vsocPath}/ticket/exportBatch`,
    method: 'post',
    data,
    responseType: 'blob',
  })
}

//更新工单备注
export function updateRemark(data) {
  return request({
    url: `${vsocPath}/ticket/updateRemark`,
    method: 'post',
    data,
  })
}

//获取工单权限按钮和状态
export function getTicketBtn() {
  return request({
    url: `${vsocPath}/ticket/allowOperation`,
    method: 'post',
  })
}

//获取工单状态
export function getTicketStatus() {
  return request({
    url: `${vsocPath}/ticket/ticketStatus`,
    method: 'post',
  })
}

//获取工单优先级
export function getTicketPriority() {
  return request({
    url: `${vsocPath}/ticket/ticketPriority`,
    method: 'post',
  })
}

//附件预览
export function previewAttachment(name) {
  return request({
    url: `${vsocPath}/file/previewAttachment?fileId=${name}`,
    method: 'post',
  })
}

//批量更改
export function updateStatusBatch(data) {
  return request({
    url: `${vsocPath}/ticket/updateStatusBatch`,
    method: 'post',
    data,
  })
}

//获取数据来源
export function ticketDataSource() {
  return request({
    url: `${vsocPath}/ticket/ticketDataSource`,
    method: 'post',
  })
}

//文件下载
export function ticketDownload(filePath) {
  return request({
    url: `${vsocPath}/file/ticketDownload?filePath=${filePath}`,
    method: 'post',
    responseType: 'blob',
    isNotReturn: true,
  })
}

export const downloadAssetTemplate = function (data) {
  return request({
    url: `${vsocPath}/ticket/downloadTemplate`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-download',
    },
    responseType: 'blob',
  })
}
export function makdwnImg(data) {
  return request({
    url: `${vsocPath}/file/ticketUpload`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-download',
    },
  })
}
