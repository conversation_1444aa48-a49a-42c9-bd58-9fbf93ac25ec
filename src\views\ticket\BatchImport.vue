<template>
  <vsoc-drawer
    v-model="isShow"
    :title="$t('ticket.btn.import')"
    dense
    :hideFooter="true"
  >
    <!-- @click:confirm="onConfirm" -->
    <v-form ref="form" class="py-4">
      <v-chip
        pill
        label
        class="mb-4 py-4 w-100 text-center primary-hover-chip d-block download"
      >
        <vsoc-icon
          class="mr-2"
          size="x-large"
          type="fill"
          icon="icon-xiazai"
        ></vsoc-icon>
        <span
          class="text-decoration-underline cursor-pointer"
          @click="onDownload"
        >
          {{ $t('ticket.template.hint') }}
        </span>
      </v-chip>
      <el-upload
        ref="upload"
        :headers="headers"
        :action="vsocPath + '/ticket/importBatchTicket'"
        :on-change="handleChange"
        :on-success="handleSuccess"
        :on-error="handleError"
        :on-exceed="handleExceed"
        :before-upload="handleBefore"
        :file-list="fileList"
        :show-file-list="false"
        accept=".xls,.xlsx"
        multiple
        width="100%"
      >
        <div class="el-upload__box d-flex flex-column justify-center" v-ripple>
          <v-btn
            outlined
            class="btn-none"
            elevation="0"
            :loading="isUploading"
            :ripple="false"
          >
            <v-icon dense class="text-ml"> mdi-paperclip </v-icon>
            <span class="text-decoration-underline ml-1">{{
              $t('action.upload')
            }}</span>
          </v-btn>
          <div class="mt-2 text-wrap">
            {{ $t('ticket.fileHint') }}
          </div>
        </div>
      </el-upload>
      <v-alert
        v-if="fileList && fileList.length > 0"
        dense
        text
        :type="
          fileList[fileList.length - 1].response &&
          fileList[fileList.length - 1].response.code !== 200
            ? 'error'
            : 'success'
        "
        icon="mdi-close-circle-outline"
        class="mt-4 mb-0 py-2 px-3 text-ml"
      >
        {{
          fileList[fileList.length - 1].response &&
          fileList[fileList.length - 1].response.msg
        }}
      </v-alert>
      <v-chip
        class="mt-4 text-ml primary-hover-chip w-100 py-2 file-list"
        label
        tag="div"
        v-for="(item, index) in fileList"
        :key="index"
        @click.stop="onDownloadFile(item.raw)"
      >
        <div class="w-100 d-flex flex-row align-end">
          <v-icon
            v-if="item.response && item.response.code !== 200"
            color="error"
            >mdi-close-circle</v-icon
          >
          <vsoc-icon
            class="primary--text"
            v-else
            type="fill"
            icon="icon-fujianicon"
          ></vsoc-icon>
          <span class="ml-2 mr-3 text-overflow-hide">{{ item.name }}</span>
          <v-spacer></v-spacer>
          <span class="action-btn">{{ item.size | formatFileSize }}</span>
          <vsoc-icon class="ml-3" type="fill" icon="icon-xiazai"></vsoc-icon>
        </div>
      </v-chip>
    </v-form>
  </vsoc-drawer>
</template>

<script>
import { downloadAssetTemplate } from '@/api/ticket'
import VsocDrawer from '@/components/VsocDrawer.vue'
import VsocIcon from '@/components/VsocIcon.vue'
import { vsocPath } from '@/util/request'
export default {
  name: 'BatchImport',
  props: {
    selectedList: Array,
  },
  components: {
    VsocDrawer,
    VsocIcon,
  },
  data() {
    return {
      base64File: '',
      isUploading: false,
      isShow: false,

      vsocPath,
      importFile: null,
      fileList: [],
    }
  },
  computed: {
    headers() {
      return this.$store.getters['global/getFileHeaders']
    },
    statusEnum() {
      return this.$store.getters['enums/getActiveStatus']
    },
  },
  watch: {
    isShow(newVal) {
      if (newVal) {
        this.fileList = []
      }
    },
  },

  methods: {
    onDownloadFile(downfile) {
      let objectUrl = undefined
      if (this.base64File) {
        objectUrl =
          'data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,' +
          this.base64File
      } else {
        objectUrl = URL.createObjectURL(downfile)
      }
      const tmpLink = document.createElement('a')

      tmpLink.href = objectUrl
      tmpLink.download = downfile.name
      document.body.appendChild(tmpLink)
      tmpLink.click()

      document.body.removeChild(tmpLink)
      URL.revokeObjectURL(objectUrl)
    },
    handleBefore(file) {
      if (this.isUploading) {
        return
      }
      let isXls = /\.(xls|xlsx)$/.test(file.name)
      let maxSize = 5 * 1024 * 1024
      const isLtMaxSize = file.size <= maxSize
      if (!isLtMaxSize || !isXls) {
        this.$notify.info('error', this.$t('ticket.fileHint'))
        return isLtMaxSize && isXls
      }
      this.isUploading = true
    },
    handleExceed(files, fileList) {
      console.log('超出文件限制数了')
    },
    handleError(err, file, fileList) {
      this.$notify.info('error', err)
    },
    handleSuccess(res, file, fileList) {
      const { code, msg, data } = res
      if (code === 200) {
        fileList[fileList.length - 1].raw = this.toFile(data, file.name)
        this.$emit('refresh')
      } else {
        this.base64File = ''
      }

      this.fileList = fileList
      this.isUploading = false
    },

    toFile(base64Url, fileName) {
      let mime =
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      let bstr = window.atob(base64Url)
      let n = bstr.length
      let u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      let file = new File([u8arr], fileName, { type: mime })
      return file
    },
    handleChange(file, fileList) {},
    async onDownload() {
      const file = await downloadAssetTemplate()
      const xlsx =
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'
      const blob = new Blob([file], { type: xlsx })
      //转换数据类型
      const a = document.createElement('a') // 转换完成，创建一个a标签用于下载

      a.download = `${this.$t('ticket.template.text')}.xlsx`
      a.href = URL.createObjectURL(blob)
      document.body.appendChild(a)
      a.click()
      a.remove()
      // 直接打开下载文件的链接
      // window.location.href = res.request.responseURL
    },
    async onConfirm(cb) {
      const res = await this.$refs.upload.submit()
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .theme--light.download.v-chip {
  color: inherit;
}
.v-chip.v-size--default {
  height: auto;
}
::v-deep .el-upload {
  width: 100%;
}
.el-upload__box {
  border: 2px dashed currentColor;
  background: $blue-white-color;
  width: 100%;
  height: 10.8333rem;
  border-radius: 2px;
  &:hover {
    color: $primary;
  }
}
::v-deep .btn-none.v-btn--outlined {
  color: inherit !important;
  border-color: inherit !important;
  border-width: 0 !important;
  &:hover::before {
    opacity: 0 !important;
  }
}
::v-deep .file-list.v-chip .v-chip__content {
  width: 100%;
  text-align: center;
}
::v-deep .v-alert__icon.v-icon {
  margin-right: 4px;
  font-size: 1.6667rem;
}
::v-deep .v-alert.success--text {
  color: $primary !important;
  caret-color: $primary !important;
}
::v-deep .v-alert__icon.v-icon.success--text {
  color: $primary !important;
}
</style>
