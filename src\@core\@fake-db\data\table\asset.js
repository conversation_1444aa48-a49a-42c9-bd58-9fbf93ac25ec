import mock from '@core/@fake-db/mock'

const data = {
  content: [
    {
      createDate: 1665969630298,
      updateDate: 1665968152220,
      createUser: null,
      updateUser: null,
      id: 'acTaaesCsZTRcsaW0',
      vin: 'N/A',
      vehicleType: '北京奔驰 GLK',
      vehicleGroups: [],
      registrationDate: 1665969629000,
      lastActiveDate: 1665968151000,
      location: '113.872323.18.236943',
      locationDate: null,
      healthStatus: 'poor',
      healthStatusDate: 1665964265399,
      alarmInfo: {
        disasterNumber: 2,
        majorNumber: 0,
        minorNumber: 1,
        warningNumber: 0,
        infoNumber: 0,
      },
      odometer: null,
      odometerLastWeekPercent: null,
      batteryTemperature: null,
      batteryTemperatureLastWeekPercent: null,
    },
    {
      createDate: 1665969633731,
      updateDate: 1665968148151,
      createUser: null,
      updateUser: null,
      id: 'acTaaesCsZTRcsaW1',
      vin: 'N/A',
      vehicleType: '北京奔驰 GLK',
      vehicleGroups: [],
      registrationDate: 1665969632000,
      lastActiveDate: 1665968147000,
      location: '113.042211,23.190331',
      locationDate: null,
      healthStatus: 'poor',
      healthStatusDate: 1665965976317,
      alarmInfo: {
        disasterNumber: 4,
        majorNumber: 0,
        minorNumber: 1,
        warningNumber: 0,
        infoNumber: 0,
      },
      odometer: null,
      odometerLastWeekPercent: null,
      batteryTemperature: null,
      batteryTemperatureLastWeekPercent: null,
    },
    {
      createDate: 1665969629727,
      updateDate: 1665968146927,
      createUser: null,
      updateUser: null,
      id: 'acTaaesCsZTRcsaWx',
      vin: 'N/A',
      vehicleType: '北京奔驰 GLK',
      vehicleGroups: [],
      registrationDate: 1665969629000,
      lastActiveDate: 1665968146000,
      location: '113.876078,23.135873',
      locationDate: null,
      healthStatus: 'poor',
      healthStatusDate: 1665966945406,
      alarmInfo: {
        disasterNumber: 6,
        majorNumber: 0,
        minorNumber: 0,
        warningNumber: 0,
        infoNumber: 0,
      },
      odometer: null,
      odometerLastWeekPercent: null,
      batteryTemperature: null,
      batteryTemperatureLastWeekPercent: null,
    },
    {
      createDate: 1665969620007,
      updateDate: 1665968145803,
      createUser: null,
      updateUser: null,
      id: 'acTaaesCsZTRcsaWz',
      vin: 'N/A',
      vehicleType: '奥迪 A4L',
      vehicleGroups: [],
      registrationDate: 1665969619000,
      lastActiveDate: 1665968145000,
      location: '113.873716,23.136692',
      locationDate: null,
      healthStatus: 'normal',
      healthStatusDate: 1661328896943,
      alarmInfo: {
        disasterNumber: 0,
        majorNumber: 0,
        minorNumber: 9,
        warningNumber: 0,
        infoNumber: 0,
      },
      odometer: null,
      odometerLastWeekPercent: null,
      batteryTemperature: null,
      batteryTemperatureLastWeekPercent: null,
    },
    {
      createDate: 1665969644131,
      updateDate: 1665968130901,
      createUser: null,
      updateUser: null,
      id: 'acTaaesCsZTRcsaWy',
      vin: 'N/A',
      vehicleType: '奥迪 A4L',
      vehicleGroups: [],
      registrationDate: 1665969643000,
      lastActiveDate: 1665968130000,
      location: '113.854654,23.142637',
      locationDate: null,
      healthStatus: 'poor',
      healthStatusDate: 1665965386937,
      alarmInfo: {
        disasterNumber: 0,
        majorNumber: 0,
        minorNumber: 5,
        warningNumber: 0,
        infoNumber: 0,
      },
      odometer: null,
      odometerLastWeekPercent: null,
      batteryTemperature: null,
      batteryTemperatureLastWeekPercent: null,
    },
    {
      createDate: 1665962952329,
      updateDate: 1665962953617,
      createUser: null,
      updateUser: null,
      id: 'NGI0ZCATktZGQxZjdlZDc5NjVj',
      vin: 'N/A',
      vehicleType: '北京奔驰 GLC',
      vehicleGroups: [],
      registrationDate: 1665965032000,
      lastActiveDate: 1665962952000,
      location: '113.986264,23.197469',
      locationDate: null,
      healthStatus: 'good',
      healthStatusDate: 1665962952326,
      alarmInfo: {
        disasterNumber: 0,
        majorNumber: 0,
        minorNumber: 0,
        warningNumber: 0,
        infoNumber: 0,
      },
      odometer: null,
      odometerLastWeekPercent: null,
      batteryTemperature: null,
      batteryTemperatureLastWeekPercent: null,
    },
    {
      createDate: 1665962292615,
      updateDate: 1665962293006,
      createUser: null,
      updateUser: null,
      id: 'I5LTg0NmUtY2U1ZGUwMzFjMzA5',
      vin: 'N/A',
      vehicleType: '北京奔驰 GLC',
      vehicleGroups: [],
      registrationDate: 1665964251000,
      lastActiveDate: 1665962291000,
      location: '113.986264,23.197469',
      locationDate: null,
      healthStatus: 'good',
      healthStatusDate: 1665962292614,
      alarmInfo: {
        disasterNumber: 0,
        majorNumber: 0,
        minorNumber: 0,
        warningNumber: 0,
        infoNumber: 0,
      },
      odometer: null,
      odometerLastWeekPercent: null,
      batteryTemperature: null,
      batteryTemperatureLastWeekPercent: null,
    },
    {
      createDate: 1665962292133,
      updateDate: 1665962292425,
      createUser: null,
      updateUser: null,
      id: 'MmEyLWFmZmUtMjcwZmE3OGUwNDBl',
      vin: 'N/A',
      vehicleType: '北京奔驰 GLC',
      vehicleGroups: [],
      registrationDate: 1665964251000,
      lastActiveDate: 1665962291000,
      location: '113.986264,23.197469',
      locationDate: null,
      healthStatus: 'good',
      healthStatusDate: 1665962292132,
      alarmInfo: {
        disasterNumber: 0,
        majorNumber: 0,
        minorNumber: 0,
        warningNumber: 0,
        infoNumber: 0,
      },
      odometer: null,
      odometerLastWeekPercent: null,
      batteryTemperature: null,
      batteryTemperatureLastWeekPercent: null,
    },
    {
      createDate: 1665962291623,
      updateDate: 1665962292027,
      createUser: null,
      updateUser: null,
      id: 'ase00YWE2LTg3DIwMDkz',
      vin: 'N/A',
      vehicleType: '北京奔驰 GLC',
      vehicleGroups: [],
      registrationDate: 1665964250000,
      lastActiveDate: 1665962290000,
      location: '113.986264,23.197469',
      locationDate: null,
      healthStatus: 'good',
      healthStatusDate: 1665962291622,
      alarmInfo: {
        disasterNumber: 0,
        majorNumber: 0,
        minorNumber: 0,
        warningNumber: 0,
        infoNumber: 0,
      },
      odometer: null,
      odometerLastWeekPercent: null,
      batteryTemperature: null,
      batteryTemperatureLastWeekPercent: null,
    },
    {
      createDate: 1665962291152,
      updateDate: 1665962291440,
      createUser: null,
      updateUser: null,
      id: 'aseGEz3LWJlNjIzMDYz',
      vin: 'N/A',
      vehicleType: '北京奔驰 GLC',
      vehicleGroups: [],
      registrationDate: 1665964250000,
      lastActiveDate: 1665962290000,
      location: '113.986264,23.197469',
      locationDate: null,
      healthStatus: 'good',
      healthStatusDate: 1665962291151,
      alarmInfo: {
        disasterNumber: 0,
        majorNumber: 0,
        minorNumber: 0,
        warningNumber: 0,
        infoNumber: 0,
      },
      odometer: null,
      odometerLastWeekPercent: null,
      batteryTemperature: null,
      batteryTemperatureLastWeekPercent: null,
    },
  ],
  pageable: {
    sort: { empty: false, sorted: true, unsorted: false },
    offset: 0,
    pageNumber: 0,
    pageSize: 10,
    paged: true,
    unpaged: false,
  },
  last: false,
  totalPages: 13,
  totalElements: 130,
  size: 10,
  number: 0,
  sort: { empty: false, sorted: true, unsorted: false },
  first: true,
  numberOfElements: 10,
  empty: false,
}

mock.onGet('/data-table/assert').reply(() => [200, data])
