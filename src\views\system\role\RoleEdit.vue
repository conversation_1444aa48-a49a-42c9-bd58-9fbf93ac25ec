<template>
  <v-form ref="form" v-model="valid" lazy-validation>
    <div class="mt-4">
      <v-row class="px-2 mt-6">
        <span class="form-section-title">
          {{ $t('global.drawer.baseInfo') }}
        </span>
      </v-row>
      <v-row class="px-2">
        <v-text-field
          v-model="roleEt.roleId"
          :rules="idRules"
          :label="$t('role.headers.code')"
          placeholder="eg. admin"
          required
          autocomplete="off"
          :disabled="mode === 'edit'"
          color="primary"
          class="is-required"
        >
        </v-text-field>
      </v-row>
      <v-row class="px-2">
        <v-text-field
          v-model="roleEt.roleName"
          :rules="nameRules"
          :label="$t('role.headers.name')"
          placeholder="eg. 管理员"
          required
          autocomplete="off"
          color="primary"
          class="is-required"
        >
        </v-text-field>
      </v-row>
      <v-row class="px-2">
        <!-- <v-textarea
          v-model="roleEt.description"
          label="描述"
          placeholder="eg. 系统管理员权限"
          dense
          required
          rows="3"
          row-height="25"
          color="primary"
        >
        </v-textarea> -->
        <v-text-field
          v-model="roleEt.description"
          :label="$t('role.headers.description')"
          required
          color="primary"
          placeholder="eg. 系统管理员权限"
        >
        </v-text-field>
      </v-row>
    </div>
  </v-form>
</template>

<script>
import { required } from '@/@core/utils/validation'
import { addRole, updateRole } from '@/api/system/role'
export default {
  name: 'Edit',
  props: {
    item: {
      type: Object,
      required: false,
    },
    mode: {
      type: String,
      required: false,
      default: () => 'new',
    },
  },
  data() {
    return {
      roleEt: this.item,

      valid: true,

      // v => /[a-zA-Z0-9]{1,50}/g.test(v) || '角色名称格式错误'
      nameRules: [
        v => required(v, this.$t('role.headers.name')),

        // 只支持【汉字|字母|数字】的角色名称
        v =>
          /^[\p{Unified_Ideograph}a-zA-Z0-9]{1,50}$/gu.test(v) ||
          this.$t('validation.wrongFormat', [this.$t('role.headers.name')]),
      ],
      idRules: [
        v => required(v, this.$t('role.headers.code')),
        v =>
          /^[a-zA-Z0-9]{1,50}$/gu.test(v) ||
          this.$t('validation.wrongFormat', [this.$t('role.headers.code')]),
      ],
    }
  },
  mounted() {},
  methods: {
    save(callback) {
      const bool = this.$refs.form.validate()
      if (!bool) return callback(false, true)

      if (this.mode === 'edit') {
        // this.roleEt.roleId,
        updateRole(this.roleEt)
          .then(resp => {
            if (resp.code === 200) {
              this.$notify.info(
                'success',
                this.$t('global.hint.edit', [this.$t('role.currentTitle')]),
              )
              this.$emit('save')
              callback()
            } else {
              throw new Error(resp.msg)
            }
          })
          .catch(e => {
            this.$notify.info('error', e)
            callback(false, true)
          })
      } else {
        addRole(this.roleEt)
          .then(resp => {
            if (resp.code === 200) {
              this.$notify.info(
                'success',
                this.$t('global.hint.add', [this.$t('role.currentTitle')]),
              )
              this.$emit('save')
              callback()
            } else {
              throw new Error(resp.msg)
            }
          })
          .catch(e => {
            console.log(e)
            callback(false, true)
          })
      }
    },
  },
}
</script>

<style scoped></style>
