//深色版本
.data-view-5 {
  width: 100%;
  height: 100%;
  $primary: #44e2fe;
  $white: #fff;

  @function font($num: 16) {
    @return 100vw / 1920 * $num;
  }

  .ffRoboto {
    font-family: 'Roboto';
    letter-spacing: 0.08em;
  }

  .ffDef {
    font-family: $body-font-family;
    letter-spacing: 0.08em;
  }
  .cloud-fs-22 {
    @extend .ffRoboto;
    font-size: font(20);
    // line-height: font(26);
  }
  .color--primary {
    color: $primary;
  }
  .fs-16 {
    font-size: font(16);
    line-height: font(22);
    opacity: 0.8;
  }

  .fs-16-1 {
    font-size: font(16);
    line-height: font(22);
    opacity: 1;
  }

  .fs-14 {
    font-size: font(14);
    line-height: font(22);
    opacity: 0.7;
  }
  .fs-14-1 {
    font-size: font(14);
    line-height: font(22);
  }

  .fs-12 {
    font-size: font(12);
    line-height: font(17);
    opacity: 0.6;
  }
  .fs-12-1 {
    font-size: font(12);
    line-height: font(17);
  }

  .fs-21 {
    font-size: font(21);
    line-height: font(21);
  }

  .fs-18 {
    font-size: font(18);
  }

  .fs-22 {
    font-size: font(22);
  }

  .fs-24 {
    font-size: font(24);
  }
  .fs-33 {
    font-size: font(33);
    // line-height: font(46);
    // line-height: 46px;
    // line-height: 0.8;
    color: $primary;
  }

  .fs-64 {
    font-size: font(64);
    line-height: font(89.6);
  }

  $langs: (
    en: (
      size: font(18),
      family: 'Roboto',
      weight: $font-weight-semibold,
    ),
    zh-CN: (
      size: font(20),
      family: 'Fontquan-XinYiGuanHeiTi',
      weight: $font-weight-normal,
    ),
  );
  @mixin mixinLang($class: null) {
    @each $key, $value in $langs {
      [data-lang='#{$key}'] & {
        font-size: map-get($value, 'size');
        font-family: map-get($value, 'family');
        font-weight: map-get($value, 'weight');
      }
      @if ($class) {
        [data-lang='#{$key}'] .#{$class} {
          @content ($value);
        }
      }
    }
  }
  .card-item-18 {
    margin-left: font(30);
  }
  .card-item-18-1 {
    margin-left: font(160);
  }
  .loading-box {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
  }
  .dv-loading {
    background: url('./images/loading-bg.svg');
    background-position: center;
    background-size: cover;
    // background: #010620;
  }

  .data-view-1 {
    color: $white;
    position: relative;
    line-height: 1.5;
    width: 100%;
    height: 100%;
    // width: 100vw;
    // height: 100vh;
    .dv-full-screen-container-1 {
      // resize: both;
      position: fixed;
      top: 0px;
      left: 0px;
      // overflow: hidden;
      transform-origin: left top;
      z-index: 9;
      width: 100%;
      height: 100%;
      // width: 100vw;
      // height: 100vh;
      // background-image: url('./images/<EMAIL>');
      background-position: center;
      background-size: cover;
      display: flex;
      flex-direction: column;
    }

    .cloud-bg {
      background-image: url('./images/loading-bg.svg') !important;
    }
    .main-edit {
      // position: absolute;
      top: 0;
      background: #1e1e1e;
      padding: 0% 1%;
      height: 8%;
      .edit-tip {
        font-size: font(22);
      }
    }
    .main-layout {
      width: 100%;
      // flex: 1;
      position: relative;
      display: flex;
      flex-direction: column;
      height: 100%;
    }
    .main-layout-1 {
      height: 100%;
      top: 0%;
    }
    .main-layout-2 {
      height: 92%;
      top: 8%;
    }
    .main-header {
      position: relative;
      width: 100%;

      color: $primary;
      height: 10%;
      display: flex;
      justify-content: space-between;
      flex-shrink: 0;
      padding: 0 1%;
      margin-top: 0.2%;
      .header-center-decoration {
        width: 45%;
        height: 60%;
        padding-top: 3%;
      }

      .header-left-decoration,
      .header-right-decoration {
        width: 24%;
        height: 55%;
        padding-top: calc(2% + 1px);
      }
      .title-date {
        position: absolute;
        top: 18%;
        left: 4%;
        font-size: font(14);
        line-height: font(22);
        font-weight: $font-weight-semibold-light;
      }
      .title-date1 {
        position: absolute;
        top: 18%;
        // width: 24%;
        left: 15%;
        font-size: font(14);
        line-height: font(22);
        font-weight: $font-weight-semibold-light;
      }
      .title-tab {
        position: absolute;
        top: 30%;
        right: font(246);
        font-size: font(14);
        line-height: font(22);
        font-weight: $font-weight-semibold-light;
      }
      .title-tab-text {
        color: #fff !important;
      }
      .title-tab-text-1 {
        margin-right: font(40);
      }
      .title-margin {
        margin-right: font(36);
      }
      .title-date2 {
        font-size: font(16) !important;
      }
      .title-header-day {
        font-size: font(28);
        font-weight: $font-weight-semibold;
        color: #32fdb8;
      }
      .mh-logo {
        // width: 167px;
        // height: 29px;
        // background: url('./images/yq-logo.png') 70% 30% no-repeat;
        // background-size: auto;
        position: absolute;
        width: 24%;
        left: 4%;
        top: 33%;
      }
      .mh-logo,
      .mh-date {
        transform: translateY(-50%);
      }

      .mh-title {
        position: absolute;
        font-size: font(32);
        font-weight: $font-weight-semibold;
        left: 50%;
        // top: 50%;
        top: 40%;
        transform: translate(-50%, -50%);
      }

      .mh-date {
        position: absolute;
        right: 5%;
        @extend .fs-18;
        width: 40%;
        height: 60%;
        top: 30%;
      }
    }

    .main-container {
      position: relative;
      width: 100%;
      padding: 0.38% 0.5% 0 0.5%;
      flex: 1;
      .top-image {
        right: font(0);
        top: font(0);
        width: font(30);
      }
      // height: 90% !important;
      // overflow-y: auto;
      // display: grid;
      // grid-template-columns: 30% auto 30%;
      // grid-template-rows: repeat(3, 31.66%);
      // gap: 2% 1%;
      // .item8 {
      //   grid-area: 1 / 2 / 3 / 3;
      // }
    }

    .layout-box {
      position: relative;
      overflow-x: hidden;
      width: 100%;
      height: 100% !important;
      // padding-right: 1%;
      // overflow-y: auto;
    }
    .dv-full-screen-container-1 .box {
      width: 100%;
      height: 100%;
      display: flex;
      padding: font(26) font(24);
      // padding: 4% 5%;
      // padding: 40px 20px;
      flex-direction: column;
      position: relative;
      font-style: normal;
      font-family: $body-font-family;
    }
    .dv-full-screen-container-1 .box.box-chart {
      padding: font(26) font(24) font(16) font(24);
    }

    .box-header {
      @include mixinLang;
      line-height: font(26);
      letter-spacing: 0.08em;
      color: $white;

      // padding-left: font(10);
      // padding-bottom: font(8);
      padding-left: 2.5%;
      padding-bottom: 1%;

      // background: url('./images/subtext.svg') no-repeat;
      background: url('./images/subtext1.svg') no-repeat;
      background-position: left bottom;
      // background-size: 100%;
      // max-width: font(403);
      background-size: contain;
      min-width: font(287);
      max-width: font(360);
    }

    .box-header-num {
      @extend .ffDef;
      @extend .fs-21;
      color: $primary;
      font-weight: 500 !important;
      margin-left: font(12);
      vertical-align: bottom;
    }

    .box-chart {
      width: 100%;
      flex: 1;
    }

    .dv9 {
      color: $primary;
      font-weight: $font-weight-semibold;
      font-size: font(24);
      line-height: font(34);
    }

    .digital {
      @extend .fs-33;
      position: absolute;
      top: 5%;
      left: 56%;
      transform: translateX(-50%);
      width: 80%;
      height: 100%;
    }
    .box-relative {
      position: relative;
      width: 100%;
      // min-height: font(12);
    }
    .box-position-top {
      position: absolute;
      width: 100%;
    }

    $box-left-top: 16%;
    .box-left {
      margin-top: $box-left-top !important;
      margin-left: 2%;
      // padding: 18px 27px;
      // padding-left: 30px;
      // width: 190px;
      // height: 106px;
      // filter: drop-shadow(0px 4px 4px rgba(0, 0, 0, 0.25));

      // background: url('./images/box1.png') no-repeat;
      // background-size: contain;
      // background-position: left top;
      // height: font(106);
      // min-width: font(190);

      // display: flex;
      // flex-direction: column;
      // justify-content: center;
    }

    .img-left {
      margin-top: $box-left-top + 22%;
      // margin-top: $box-left-top + 47px;
      margin-left: -2px;
    }

    .justify-space-evenly {
      justify-content: space-evenly;
    }

    .font-fx-16 {
      font-family: 'Fontquan-XinYiGuanHeiTi';
      font-style: normal;
      font-size: font(16);
      line-height: font(19);
      opacity: 0.8;
      @include mixinLang;
    }

    .box-center-right-text {
      @extend .font-fx-16;
      position: absolute;
      width: 50%;
      left: 35%;
      bottom: 0%;
    }

    .box-center-left-text {
      @extend .font-fx-16;

      color: $white;
      padding-left: 2%;
      padding-bottom: 1.5%;

      // position: absolute;
      // top: 50%;
      // transform: translateY(-50%);
      // width: 46%;
      // text-align: right;
    }

    .box-center-left-num {
      font-style: normal;
      font-weight: $font-weight-semibold;
      font-size: font(24);
      line-height: font(34);
      color: $primary;

      width: 40%;
      position: absolute;
      height: 100%;
      top: 4%;
      right: 10%;
    }
    .big-content-box {
      width: 100%;
      height: 100%;
      display: flex;
      // padding: 4% 5%;
      // padding: 40px 20px;
      flex-direction: column;
      position: relative;
      font-style: normal;
      font-family: $body-font-family;
    }

    $box-right-top: 0%;
    .img-right {
      // margin-top: 112px;
      // margin-top: $box-right-top + 14%;
      margin-top: -32%;
    }

    .box-h {
      height: font(112);
    }

    .box-right {
      margin-right: 3%;
      margin-top: $box-right-top;
      padding-left: 10%;
      padding-top: 9.5%;

      // width: 180px;
      // height: 112px;
      background: url('./images/box2.svg') no-repeat;
      background-size: contain;
      background-position: left bottom;
      min-width: font(210);
      min-height: font(132);
    }

    .box-right-text {
      @extend .font-fx-16;
      margin-left: font(24);
    }

    .box-right-num {
      font-style: normal;
      font-weight: $font-weight-semibold;
      font-size: font(40);
      line-height: font(60);
      color: #ff385d;
    }

    .progress-linear {
      // border: 1px solid #ffffff4d !important;
      border-radius: 10px;
      box-shadow: inset 0px 0px 4px #ffffff4d, 0px 0px 4px #ffffff4d;
      // filter: blur(2px);
      height: font(10) !important;
      .v-progress-linear__determinate {
        border-radius: 10px;
        height: 100%;
        // margin: 1px;
        // filter: contrast(200%);
        // background: linear-gradient(270deg, #fe9837 0%, rgba(255, 164, 78, 0) 100%);
      }
    }
    .progress-linear.progress-linear-15 {
      height: font(2) !important;
    }
    .progress-linear1 {
      box-shadow: inset 0px 0px 4px #ffffff4d, 0px 0px 4px #ffffff4d;
      // filter: blur(2px);
      height: font(10) !important;
      .v-progress-linear__determinate {
        height: 100%;
      }
    }

    .progress-gradient {
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
    }

    $progress-color: (
      '0': #32fdb8,
      '1': #21cbff,
      '2': #fe9837,
      '3': #44e2fe,
      '4': #ffe436,
    );

    @each $name, $val in $progress-color {
      .progress-#{$name} {
        .v-progress-linear__determinate {
          // border-color: transparent !important;
          background: $val !important;
          // background: linear-gradient(
          //   270deg,
          //   $val 0%,
          //   rgba(255, 164, 78, 0) 100%
          // ) !important;
        }
      }
    }
    // .progress-0 {
    //   .v-progress-linear__determinate {
    //     background: linear-gradient(270deg, #fe9837 0%, rgba(255, 164, 78, 0) 100%);
    //   }
    // }

    .right-chart1-value {
      @extend .fs-14;
      display: inline-block;
      min-width: font(98);
      max-width: font(100);
      padding-left: 3%;
      // min-width: 8.3333rem;
      white-space: nowrap;
      color: #fff;
    }

    .right-chart1-value.right-chart2-value {
      @extend .fs-14-1;
      color: #fff !important;
      opacity: 1;
    }

    .right-chart1-text {
      @extend .fs-14;
      width: 40%;
      min-width: 6rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      padding-right: font(5);
      color: #fff;
    }

    .right-chart1-text1 {
      @extend .fs-14;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      color: #fff;
    }
    .date-combox {
      background: rgba($primary, 0.15) !important;
      border-radius: 5px !important;
      height: auto !important;
      margin-top: 0;
      max-width: font(124) !important;
      // padding-left: font(8) !important;
      // line-height: font(26) !important;
    }
    .check-text {
      // font-size: font(18) !important;
      color: $primary !important;
      padding: font(6) font(0) font(6) font(16) !important;
      // line-height: font(35) !important;
    }
    .date-combox .v-icon {
      color: $primary !important;
      margin-right: font(16);
      // height: 100% !important;
      position: absolute;
      bottom: 0;
      top: 0;
      // height: auto !important;
    }
    .date-combox .v-input__icon {
      // margin-top: font(8);
      // height: 100% !important;
    }
    .date-combox .v-select__selections input {
      color: $primary !important;
      font-size: font(14);
      // max-height: 10px !important;
      // padding: 0 !important;
      // line-height: 0 !important;
    }
    .v-btn:not(.v-btn--round).v-size--small.date-option {
      background: rgba($primary, 0.15) !important;
      color: $primary !important;
      font-size: font(18) !important;
      line-height: font(26) !important;
      padding: font(6) font(16) !important;
      height: auto !important;
    }

    .v-menu__content:not(.list-style) .v-list-item {
      height: font(45);
    }
    // .v-btn.btn-icon {
    //   background: url('./images/btn.svg') no-repeat center / 100%;
    //   // height: font(32) !important;
    //   width: font(32) !important;
    //   margin-right: 3%;
    //   background-size: contain;
    // }

    .date-list {
      background: rgba($white, 0.1) !important;
      backdrop-filter: blur(6px);
      .v-list-item:not(.v-list-item--active):not(.v-list-item--disabled) {
        color: $white;
      }
      .v-list-item--active {
        color: $primary !important;
      }
    }

    // dv-border-box-14---start
    .mh-tab {
      // top: font(-55);
      // height: font(60.5);
      position: absolute;
      // transform: translateY(50%);
      top: font(-6);
      height: font(52);
    }
    .tab-content {
      height: 100%;
      margin-top: 4%;
    }
    .tab-content1 {
      height: 100%;
      margin-top: 8%;
    }
    .border-box {
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: font(16);
      margin: 0 font(8);
      border: 1px solid #1b6e9a;
      border-radius: 8px;
      background: #002058;

      color: $primary;
      backdrop-filter: blur(6px);
      @include mixinLang;
    }

    .border-box2 {
      width: font(220);
      height: font(55);
      background: rgba(68, 226, 254, 0.08) !important;
      margin-bottom: font(16) !important;
    }

    .left-top-radius {
      position: absolute;
      top: font(-9.625);
      left: font(-10);
      background: url('./images/radius1.svg') no-repeat top left;
      background-size: contain;
      width: font(24);
      height: font(24);
    }
    .right-top-radius {
      position: absolute;
      top: font(-9.625);
      right: font(-10);
      background: url('./images/radius2.svg') no-repeat top left;
      background-size: contain;
      width: font(24);
      height: font(24);
    }
    .left-bottom-radius {
      position: absolute;
      bottom: font(-8.625);
      left: font(-10);
      background: url('./images/radius4.svg') no-repeat top left;
      background-size: contain;
      width: font(24);
      height: font(24);
    }

    .right-bottom-radius {
      position: absolute;
      bottom: font(-8.625);
      right: font(-10);
      background: url('./images/radius3.svg') no-repeat top left;
      background-size: contain;
      width: font(24);
      height: font(24);
    }
    // dv-border-box-14---end

    .box-header2 {
      font-size: font(22);
    }

    .cloud-container {
      position: relative;
      padding: 1.5% 1.5% 1%;
      display: grid;
      height: calc(100vh - 10%);
      grid-template-columns: repeat(4, 24.2%);
      grid-template-rows: 66% 32%;
      gap: 2% 1%;
      .item7 {
        grid-area: 1 / 2 / 2 / 4;
      }
    }
    .c-box {
      @extend .box;
      padding: 7%;
      .box-chart {
        display: flex !important;
        justify-content: space-between !important;
      }
    }
    .c-box-header {
      font-size: font(20);
      line-height: font(26);
      letter-spacing: -0.04em;
      color: $white;

      // padding-left: 1.8%;
      padding-left: 2.5%;
      padding-bottom: 1%;
      // margin-left: 2%;

      background: url('./images/box-header.svg') no-repeat;
      background-position: left bottom;
      background-size: contain;
      min-width: font(287);
      min-height: font(12);
      max-width: 100%;

      @extend .ffRoboto;
    }
    .c-left1-num {
      font-weight: $font-weight-semibold;
      font-size: font(40);
      line-height: font(65);
      color: $primary;
    }
    .card-item3 {
      font-weight: $font-weight-semibold;
      font-size: font(28);
      // line-height: font(38);
    }
    .card-item-title-text {
      font-weight: $font-weight-semibold;
      font-size: font(18);
    }
    .type-num {
      font-weight: $font-weight-semibold;
      font-size: font(40);
      line-height: font(65);
    }
    .type-num-1 {
      color: #f0da4c;
    }
    .type-num-2 {
      color: #ff385d;
    }
    .type-num-3 {
      color: $primary;
    }
    .c-left1-unit {
      color: $primary;
      margin-left: 2%;
      font-size: font(24);

      opacity: 0.6;
      height: font(112);
      line-height: font(112);
    }
    .c-center2-text {
      font-size: font(18);
      line-height: font(26);
      opacity: 0.8;
      // margin-top: 4%;
      // margin-top: font(16);
      margin-left: font(2);
    }
    .c-center2-num {
      color: #32fdb8;
      font-size: font(40);
      line-height: font(56);
      font-weight: $font-weight-semibold-light;
      // margin: font(16) 0 font(32) font(4);
    }
    .c-center2-num-1 {
      font-size: font(28);
    }
    .c-center2-unit {
      font-size: font(24);
      line-height: font(44);
      opacity: 0.6;
    }
    .shield {
      width: font(300);
      height: font(480);
      background: url('./images/<EMAIL>') center no-repeat;
      background-size: contain;
    }
    .vehicle-title {
      margin-top: font(16);
    }
    .animate {
      animation: shake-y 4s linear infinite;
    }
    @keyframes shake-y {
      0% {
        transform: translate(0px, 0px);
      }
      50% {
        transform: translate(0, font(15));
      }
      100% {
        transform: translate(0px, 0px);
      }
    }
    .c1-posture {
      text-shadow: 0 0 font(10) $primary;
    }
    .c1-text {
      font-size: font(28);
      line-height: font(39.2);
      font-weight: 500;
      color: #cdeeff;
      margin: font(16) 0 font(32);
      opacity: 0.6;
      text-shadow: 0 0 font(3) #cdeeff;
    }
    .vue-grid-layout {
      width: 100% !important;
      height: 100% !important;
    }
    .vue-grid-item {
      left: auto !important;
    }
    .del-btn {
      position: absolute;
      right: font(22);
      top: font(22);
      z-index: 99999;
      opacity: 0;
    }
    .vue-grid-item:hover .del-btn {
      opacity: 1;
    }
    .aside-box {
      position: absolute;
      right: 0;
      top: 0;
      width: 25%;
      height: calc(100vh - 8%);
      z-index: 9999;
      .v-card {
        padding: 5%;
        border-radius: 0px !important;
        background: rgba(30, 30, 30, 0.98) !important;
        .v-input {
          background: rgba(255, 255, 255, 0.15);
        }
        .v-subheader {
          color: #fff !important;
          font-size: font(18);
          padding: 0;
          height: font(26);
        }
        .v-list {
          padding: 0 !important;
          height: 100%;
          background: rgba(30, 30, 30, 0.98) !important;
        }
        .v-item-group {
          margin: 4% 0 !important;
          height: 85%;
          overflow-y: auto;
        }

        .v-list-item {
          background: rgba(255, 255, 255, 0.05);
          margin-bottom: 4%;
          border-radius: 8px;
          padding: 0 !important;
          .list-item {
            padding: 0 4% !important;
          }
        }
        .v-list-item:last-child {
          margin-bottom: 0%;
        }
        .v-item--active {
          border: 1px solid rgba(68, 226, 254, 1);
          color: #44e2fe;
        }
        .v-list-item--link:before {
          background: rgba(30, 30, 30, 0.98) !important;
          opacity: 0;
        }
        .v-list-item__content {
          font-size: font(16);
          font-weight: 500;
        }
      }
    }
    .aside-box.grid-box {
      .v-card {
        .v-item-group {
          height: 95% !important;
        }
        .v-list-item {
          margin-bottom: 2% !important;
        }
      }
    }
    .share-box {
      .item-text {
        font-size: font(16);
        font-weight: 500;
      }
      .item-text1 {
        font-size: font(14);
        font-weight: 400;
        color: rgba(255, 255, 255, 0.5) !important;
      }
      .item-color {
        color: #fff;
      }
      .switch-box {
        .v-input {
          background: none !important;
        }
        .v-input__slot {
          background: rgba(255, 255, 255, 0.15) !important;
        }
      }
      .v-input {
        background: rgba(255, 255, 255, 0.15) !important;
      }
      .v-input input {
        color: rgba(255, 255, 255, 1) !important;
      }
      .v-input--is-disabled input {
        opacity: 0.3;
      }
      .v-btn.v-btn--has-bg {
        background: #44e2fe !important;
        color: #fff !important;
      }
      .v-btn.v-btn--disabled.v-btn--has-bg {
        opacity: 0.6;
      }
    }
  }
  .data-view-1 ::-webkit-scrollbar-thumb {
    width: 15px !important; /*no*/
    height: 15px !important; /*no*/
    background: rgba(191, 191, 191, 0.5) !important;
    border-radius: 10px !important;
    border: 5px solid #1e1e1e !important;
  }
  .data-view-1 ::-webkit-scrollbar-track {
    border-radius: 0 5px 5px 0;
    background: transparent !important;
  }
  .percent-num {
    font-size: font(14);
    line-height: font(18);
  }

  .chart-card-3 {
    padding-top: font(24);
    width: 56%;
  }
  .chart-card-3-line {
    margin-top: -3%;
    height: font(26);
    width: 58% !important;
    margin-left: 22%;
    font-size: font(14);
    line-height: font(26);
    text-align: center;
    color: #44e2fe;
    font-weight: $font-weight-semibold;
    background: linear-gradient(
      to right,
      rgba(#44e2fe, 0.001) 0%,
      rgba(#44e2fe, 0.3) 50%,
      rgba(#44e2fe, 0.001) 100%
    );
  }
  .v-window__prev {
    margin: 0 !important;
    margin-right: font(12) !important;
    background: none !important;
  }
  .v-window__next {
    margin: 0 !important;
    margin-left: font(12) !important;
    background: none !important;
  }
  .table-card.table-card-1 {
    // margin-top: font(12);
  }
  .table-card.table-card-1 tbody {
    visibility: collapse !important;
  }
  .table-card.table-card-2 .v-data-table-header {
    visibility: collapse !important;
  }
  .table-card.table-card-1 .v-data-table__wrapper {
    overflow: hidden !important;
  }
  .table-card.v-data-table.v-data-table--fixed-header thead th {
    background: #0d3f87;
    color: #21cbff !important;
    border-bottom: thin solid transparent !important;
    box-shadow: none !important;
    font-size: font(14) !important;
  }
  .table-card.v-data-table .v-data-table__wrapper > table > thead > tr > th {
    height: font(26) !important;
    padding: 0 font(0) !important;
  }
  .table-card.v-data-table > .v-data-table__wrapper > table > tbody > tr > td {
    color: #fff !important;
    border-bottom: thin solid transparent !important;
    font-size: font(14) !important;
    height: font(38) !important;
    padding: 0 font(0) !important;
  }

  .card-item5-text {
    width: font(45);
  }
  .card-item5-text1 {
    width: font(105);
    margin-left: font(12);
  }
  .v-autocomplete__content .v-list-item {
    color: var(--v-color-base) !important;
  }
  .model-list .v-list-item__title {
    color: var(--v-color-base) !important;
  }
  // .v-window__prev .v-icon__svg {
  //   width: font(24) !important;
  //   height: font(24) !important;
  //   color: #fff !important;
  // }
  // .v-window__next .v-icon__svg {
  //   width: font(24) !important;
  //   height: font(24) !important;
  //   color: #fff !important;
  // }
  .box-line-3 {
    .line-content {
      padding: font(10) 0 font(20) 0;
    }
    .dot {
      border-radius: 50%;
      width: font(10);
      height: font(10);
      // margin-top: font(8);
    }
    .dot-1 {
      border-radius: 50%;
      width: font(20);
      height: font(20);
      line-height: font(20);
      // margin-top: font(4);
    }
    .risk-name {
      color: '#fff';
      margin-bottom: font(4);
    }
    .list-item {
      margin-bottom: font(16);
    }
    .tab-content-1 {
      padding: font(12) 0 font(24) 0 !important;
    }
  }

  .map-image {
    width: font(26);
    height: font(28);
    display: inline-block;
    cursor: pointer;
    margin-left: font(30);
  }
  .map-image-1 {
    margin: 0 font(10);
  }
  .risk-num {
    width: font(28);
    height: font(18);
  }
  .risk-num-sort {
    width: font(24);
    height: font(24);
    border-radius: 50%;
    text-align: center;
    line-height: font(24);
  }
  .tianzhuan-icon .icon-tiaozhuan {
    font-size: font(26) !important;
  }
}
