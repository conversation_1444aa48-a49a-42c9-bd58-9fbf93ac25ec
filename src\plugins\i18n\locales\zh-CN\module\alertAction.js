const alertAction = {
  currentTitle: '告警响应',
  headers: {
    name: '处置名称',
    disposeTypeName: '处置类型',
    version: '版本号',
    severityList: '严重等级适配',
    severity: '严重等级',
    active: '状态',
    warnClassifyTypeName: '适用告警分类',
    receiveUser: '通知对象',
    noticeContent: '通知内容',
  },
  detail: {
    link: '告警详情链接',
  },
  btn: {
    add: '新增告警响应',
    edit: '修改告警响应',
    detail: '告警响应详情',
    alert: '告警参数',
    app: '车辆参数',
  },
  swal: {
    del: {
      title: '删除告警响应',
      text: '删除后将终止基于此处置流程的所有外部集成',
      tip: '已启用的响应任务不允许删除!',
    },
  },
  hint: {
    require: '{0}不能为空!',
  },
}
export default alertAction
