<template>
  <v-menu
    offset-y
    absolute
    left
    nudge-bottom="14"
    :elevation="$vuetify.theme.dark ? 9 : 8"
    content-class="list-style notification-menu-content"
  >
    <template v-slot:activator="{ on, attrs }">
      <v-badge
        v-show="messageNum"
        color="#f5222d"
        class="badge-box"
        offset-x="10"
        offset-y="8"
        dot
      >
        <!-- <v-icon @click="handleMenu" v-bind="attrs" v-on="on">
          {{ icons.mdiBellOutline }}
        </v-icon> -->
        <v-btn
          icon
          small
          color="white"
          @click="handleMenu"
          v-bind="attrs"
          v-on="on"
        >
          <vsoc-icon
            type="fill"
            size="x-large"
            icon="icon-xitongtongzhi"
          ></vsoc-icon>
        </v-btn>
      </v-badge>

      <v-btn
        v-show="Number(messageNum) === 0"
        icon
        small
        color="white"
        @click="handleMenu"
        v-bind="attrs"
        v-on="on"
      >
        <vsoc-icon
          type="fill"
          size="x-large"
          icon="icon-xitongtongzhi"
        ></vsoc-icon>
      </v-btn>
      <!-- <v-icon
        v-show="Number(messageNum) === 0"
        @click="handleMenu"
        v-bind="attrs"
        v-on="on"
        size="18"
      >
        {{ icons.mdiBellOutline }}
      </v-icon> -->
    </template>

    <v-card class="app-bar-notification-content-container">
      <v-tabs
        v-model="type"
        background-color="#F5F6FA"
        color="primary"
        grow
        height="56px"
        class="message-tab-box"
      >
        <v-tab
          v-for="item in titleList"
          :key="item.value"
          @click.stop="changeType(item.value)"
        >
          <div
            class="d-flex align-center"
            v-if="messageTypeList && messageTypeList[item.value]"
          >
            <span>{{ messageTypeList[item.value].text }}</span>
            <v-badge
              v-show="item.num"
              class="badge-box-1"
              color="#f5222d"
              :content="Number(item.num) > 99 ? '99+' : item.num"
            >
            </v-badge>
          </div>
        </v-tab>
      </v-tabs>
      <v-list class="py-0">
        <div class="message-box overflow-y">
          <div v-if="messageList.length">
            <template v-for="(notification, index) in messageList">
              <v-list-item
                :key="notification.id"
                link
                @click.stop="
                  notification.status === '0' ? setRead(notification, 1) : ''
                "
                :class="notification.status === '1' ? 'read-box' : ''"
              >
                <v-list-item-avatar size="38" color="#F5F6FA">
                  <vsoc-icon
                    type="fill"
                    :icon="
                      notification.messageType === '0'
                        ? 'icon-gonggaobiaozhi'
                        : notification.messageType === '1'
                        ? 'icon-gongdanbiaozhi'
                        : notification.messageType === '2'
                        ? 'icon-gaojingjibiebiaozhi'
                        : ''
                    "
                    class="primary--text"
                    size="x-large"
                  ></vsoc-icon>
                  <!-- <span class="text-lg">{{ notification.typeName }}</span> -->
                </v-list-item-avatar>

                <v-list-item-content class="d-block">
                  <div class="d-flex align-center">
                    <v-list-item-title v-show-tips class="text-content">
                      {{ notification.title }}
                    </v-list-item-title>
                    <v-list-item-action>
                      {{ dealTime(notification.createDate) }}
                    </v-list-item-action>
                  </div>
                  <v-list-item-subtitle>
                    {{ notification.content }}
                  </v-list-item-subtitle>
                </v-list-item-content>
              </v-list-item>
            </template>
          </div>
          <div
            v-else
            class="d-flex flex-column justify-center align-center h-100"
          >
            <v-img
              width="100px"
              height="100px"
              :src="require('@/assets/images/tree-empty.png')"
              style="flex: inherit"
            ></v-img>
            <div class="mt-4 no-text">{{ $t('notices.hint.nodata') }}</div>
          </div>
        </div>
        <v-divider style="border-color: #e6eaf2"></v-divider>
        <div class="w-100 read-all-btn">
          <div
            v-if="messageList.length"
            class="d-flex align-center justify-space-between"
          >
            <div class="btn mr-12" @click.stop="setRead">
              {{ $t('message.btn.read') }}
            </div>
            <div
              class="btn"
              @click="$router.push(`/message/myMessage?type=${type}`)"
            >
              {{ $t('notices.btn.more') }}
              <!-- 查看更多消息 -->
            </div>
          </div>
          <div
            v-else
            class="btn"
            @click="$router.push(`/message/myMessage?type=${type}`)"
          >
            {{ $t('notices.btn.previous') }}
            <!-- 查看历史消息 -->
          </div>
        </div>
      </v-list>
    </v-card>
  </v-menu>
</template>

<script>
import { getMyMessagesList, handleRead } from '@/api/message/index'
import { NUMBER_TURN } from '@/util/constant'
import { getLocalStorage, setLocalStorage } from '@/util/localStorage'
import { deepClone } from '@/util/utils'
import { mdiBellOutline } from '@mdi/js'
import { format, getDay } from 'date-fns'
export default {
  data() {
    return {
      timer: null,
      tableData: [],
      tableDataTotal: 0,
      tableLoading: false,
      icons: {
        mdiBellOutline,
      },
      type: '0',
      typeList: [
        { value: '0', type: 'noticeCount', text: '公告', num: 0 },
        { value: '1', type: 'ticketCount', text: '工单', num: 0 },
        { value: '2', type: 'alarmCount', text: '告警', num: 0 },
      ],
      isback: false,
      isflag: false,
      stop: false,
      readList: [],
      messageList: [],
    }
  },
  computed: {
    messageTypeList() {
      return this.$store.state.enums.enums.MessageType
    },
    messageNum() {
      return this.$store.getters['global/getMessageNum']
    },
    // messageList() {
    //   return this.$store.getters['global/getMessageList'](this.type)
    // },
    titleList() {
      return this.typeList.map(v => {
        return {
          ...v,
          num: this.$store.getters['global/getUnreadMessage'][v.type],
        }
      })
    },
  },
  watch: {
    '$store.state.global.messageList': {
      deep: true,
      handler(newVal, oldVal) {
        const noteSetting = getLocalStorage('noteSetting')
        if (noteSetting && noteSetting.flag === '1') return
        let findItem = this.messageList.find(v => v.status === '1')
        if (findItem) {
          this.fetchList(findItem.messageType)
        }
        let newList = deepClone(newVal)
        let setMessageList = JSON.parse(getLocalStorage('messageList')) || []
        if (setMessageList.length) {
          newList = newList.filter(
            v => setMessageList.map(v => v.id).indexOf(v.id) === -1,
          )
        }
        let messageList = newList.filter(
          v =>
            v.status === '0' &&
            new Date().getTime() >= new Date(v.createDate).getTime(),
        )
        let allMessageList = messageList.reverse() || []
        for (let i = 0; i < allMessageList.length; i++) {
          allMessageList[i].timer = setTimeout(() => {
            if (this.$store.state.global.messageList.length === 0) {
              allMessageList.forEach(v => {
                if (v.timer) clearTimeout(v.timer)
              })
              return
            }
            if (
              getLocalStorage('noteSetting') &&
              JSON.parse(getLocalStorage('noteSetting')).flag === '1'
            ) {
              allMessageList.forEach(v => {
                if (v.timer) clearTimeout(v.timer)
              })
              return
            }
            let messages = allMessageList.filter(
              x => this.readList.indexOf(x.id) === -1,
            )
            if (messages.length) {
              i = 0
              allMessageList = messages.filter(v => !v.isShow)
            } else {
              allMessageList.forEach(v => {
                if (v.timer) clearTimeout(v.timer)
              })
              return
            }
            if (this.isback) {
              allMessageList.forEach(v => {
                if (v.timer) clearTimeout(v.timer)
              })
              return
            }
            if (allMessageList[i]) {
              allMessageList[i].isShow = true
              this.$notify.info(
                'info',
                allMessageList[i].messageTypeName +
                  '：' +
                  allMessageList[i].title,
              )
            }
          }, 3000 * i)
        }
        setMessageList = messageList.concat(setMessageList)
        setLocalStorage('messageList', JSON.stringify(setMessageList))
        // localStorage.setItem('messageList', JSON.stringify(setMessageList))
      },
    },
  },
  created() {
    this.polling()
  },
  beforeDestroy() {
    this.$store.commit('global/clearPollingST')
    this.isback = true
  },
  methods: {
    handleMenu() {
      this.type = '0'
      this.fetchList(this.type)
    },
    changeType(val) {
      this.fetchList(val)
      // this.messageList = this.$store.getters['global/getMessageList'](val)
    },
    //获取消息
    async fetchList(val) {
      const res = await getMyMessagesList({
        pageNum: 1,
        status: '0',
        pageSize: 50,
        messageType: val,
      })
      this.messageList = res.data.records || []
    },
    // 轮询
    polling() {
      this.$store.dispatch('global/getMessge')
      this.$store.dispatch('global/poll')
    },
    dealTime(createDate) {
      let time = ''
      if (this.isCurrentWeek(createDate)) {
        time =
          '周' +
          NUMBER_TURN[getDay(new Date(createDate))] +
          ' ' +
          format(new Date(createDate), 'HH:mm')
      } else {
        time = format(new Date(createDate), 'yyyy-MM-dd')
      }
      return time
    },
    isCurrentWeek(past) {
      const pastTime = new Date(past).getTime()
      const today = new Date(new Date().toLocaleDateString())
      let day = today.getDay()
      day = day == 0 ? 7 : day
      const oneDayTime = 60 * 60 * 24 * 1000
      const monday = new Date(today.getTime() - oneDayTime * (day - 1))
      const nextMonday = new Date(today.getTime() + oneDayTime * (8 - day))
      if (monday.getTime() <= pastTime && nextMonday.getTime() > pastTime) {
        return true
      } else {
        return false
      }
    },

    async setRead(item, type) {
      const unRead = this.messageList.filter(v => v.status === '0')
      if (unRead.length === 0) return
      const params = {
        ids: item && item.id ? [item.id] : this.messageList.map(v => v.id),
      }
      try {
        const res = await handleRead(params)
        if (res.code === 200) {
          if (type === 1) {
            this.titleList.forEach(v => {
              if (Number(v.value) === Number(item.messageType)) {
                v.num = Number(v.num) - 1
              }
            })
            item.status = '1'
            this.readList.push(item.id)
          } else {
            this.titleList.forEach(v => {
              if (Number(v.value) === this.type) {
                v.num = Number(v.num) - this.messageList.length
              }
            })
            this.messageList.forEach(v => {
              v.status = '1'
              this.readList.push(v.id)
            })
          }
        }
      } catch (error) {
        console.error(`已读错误：${error}`)
      }
    },
  },
}
</script>

<style lang="scss">
@import '~vuetify/src/styles/styles.sass';
.badge-box {
  .v-badge__badge {
    font-size: 6px !important;
    padding: 0 !important;
    line-height: 6px !important;
  }
}

.badge-box-1 {
  .v-badge__badge {
    min-width: 15px;
    color: #ffffff;
    line-height: 18px;
    font-size: 12px;
    height: 18px;
    background: #da1f1f !important;
    left: 5px !important;
    bottom: -8px !important;
    border-radius: 4px;
    padding: 0 4px;
  }
}

.app-bar-notification-content-container {
  .message-box {
    width: 340px;
    height: 326px;
    background: #fff;
    .v-list-item {
      height: 66px;
      padding: 0 14px;
    }
    .v-list-item--link:before {
      background-color: rgba(1, 76, 241, 0.1);
    }
    .v-list-item:hover::before {
      opacity: 1;
    }
    .v-list-item__content {
      font-size: 12px;
      color: #1f2533;
    }

    // .v-list-item:hover .v-list-item__content {
    //   color: #fff !important;
    //   z-index: 1;
    // }
    .v-list-item__action {
      min-width: auto !important;
      white-space: nowrap !important;
      margin: 0 !important;
      margin-left: 0 !important;
    }
    .v-list-item__subtitle {
      color: #a1a6b1 !important;
      margin-top: 2px;
    }
  }
  .read-box {
    .iconfont {
      color: #b4bbcc !important;
    }
    .v-list-item__title,
    .v-list-item__action,
    .v-list-item__subtitle {
      color: #a1a6b1 !important;
    }
  }
  .read-all-btn {
    height: 42px;
    background: #f5f6fa;
    display: flex;
    align-items: center;
    justify-content: center;
    .btn {
      color: #1f2533;
      font-size: 12px;
      cursor: pointer;
      border-bottom: 1px solid #1f2533;
      padding-bottom: 1px;
    }
    .btn:hover {
      color: $primary;
      border-bottom: 1px solid $primary;
    }
  }

  .no-text {
    color: #1f2533;
    font-size: 14px;
  }
}

.ps-user-notifications {
  max-height: calc(var(--vh, 1vh) * 80);
}

.notification-menu-content {
  z-index: 999 !important;
  @media #{map-get($display-breakpoints, 'xs-only')} {
    min-width: calc(100vw - (1.5rem * 2)) !important;
    left: 50% !important;
    transform: translateX(-50%);
  }
}
.v-application .v-tabs:not(.v-tabs--vertical).message-tab-box {
  box-shadow: none !important;
}
.message-tab-box .v-tab {
  font-size: 16px !important;
}
.message-tab-box.v-tabs > .v-tabs-bar .v-tab:not(.v-tab--active) {
  color: #686e7c !important;
}
.message-tab-box .v-tabs-slider {
  width: 48px !important;
  margin: auto !important;
}
</style>
