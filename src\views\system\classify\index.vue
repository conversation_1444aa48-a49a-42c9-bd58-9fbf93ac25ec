<!-- 分类管理 -->
<template>
  <div>
    <bread-crumb></bread-crumb>

    <v-card tile class="main-content">
      <v-card-text class="pa-0">
        <div class="d-flex justify-space-between align-center flex-row-reverse">
          <div class="d-flex align-center">
            <table-search
              :searchList="searchList"
              :searchQuery="query"
              @search="$_search"
            ></table-search>
            <!-- <v-text-field
              v-model="query.name"
              color="primary"
              hide-details="auto"
              :label="$t('classify.headers.name')"
              dense
              outlined
              @keyup.enter.native="$_search"
              class="me-3 text-width"
            ></v-text-field>
            <v-select
              hide-details="auto"
              v-model="query.type"
              :items="classifyTypeList"
              :multiple="false"
              outlined
              dense
              :label="$t('classify.headers.typeName')"
              clearable
              @change="$_search"
              :menu-props="{ offsetY: true }"
              class="me-3 text-width"
            >
            </v-select>
            <v-select
              hide-details="auto"
              v-model="query.status"
              :items="classifyStatusList"
              :multiple="false"
              outlined
              dense
              :label="$t('classify.headers.status')"
              clearable
              @change="$_search"
              :menu-props="{ offsetY: true }"
              class="me-3 text-width"
            >
            </v-select>
            <div>
              <v-btn
                class="primary--text bg-btn"
                elevation="0"
                @click="$_search"
              >
                <span>
                  {{ $t('action.search') }}
                </span>
              </v-btn>
            </div> -->
          </div>
          <div class="d-flex justify-end align-center">
            <v-btn
              elevation="0"
              color="primary"
              v-has:classify-add
              @click="add"
            >
              <!-- <v-icon>mdi-plus</v-icon> -->
              <span>
                {{ $t('action.add') }}
              </span>
            </v-btn>
          </div>
        </div>
        <v-data-table
          fixed-header
          :items-per-page="query.pageSize"
          item-key="code"
          :height="tableHeight"
          hide-default-footer
          :headers="headers"
          :items="tableData"
          class="table border-radius-xl mt-3 thead-light"
          :loading="tableLoading"
        >
          <!-- <template
            v-for="item in headers"
            v-slot:[`header.${item.value}`]="{ header }"
          >
            <div :key="item.value">{{ $t(header.text) }}</div>
          </template> -->
          <template v-slot:item.type="{ item }">
            <span>
              {{
                classifyEum[item.type] ? classifyEum[item.type].dictName : 'N/A'
              }}
            </span>
          </template>
          <template v-slot:item.name="{ item }">
            <div v-show-tips style="width: 160px" class="text-overflow-hide">
              {{ item.name | dataFilter }}
            </div>
          </template>
          <template v-slot:item.status="{ item }">
            <div v-if="classifyStatusEum[item.status]">
              <v-badge
                dot
                inline
                offset-x="10"
                :offset-y="-18"
                :color="
                  classifyStatusEum[item.status]
                    ? classifyStatusEum[item.status].color
                    : ''
                "
                class="mr-1"
              ></v-badge>
              <span>{{ classifyStatusEum[item.status].text }}</span>
            </div>
            <span v-else>N/A</span>
          </template>
          <template v-slot:item.description="{ item }">
            <div v-show-tips style="width: 160px" class="text-overflow-hide">
              {{ item.description | dataFilter }}
            </div>
          </template>
          <template v-slot:item.createUser="{ item }">
            <vsoc-icon
              v-if="
                $store.state.global.userIdByAllList.includes(item.createUser)
              "
              class="primary--text"
              icon="icon-yongyouzhe"
              type="fill"
            ></vsoc-icon>
            <span v-else>{{ item.createUser }}</span>
          </template>
          <template v-slot:item.actions="{ item }">
            <v-btn
              v-if="item.status === '0'"
              icon
              @click.stop="handleStatus(item)"
              v-has:classify-disable="item"
            >
              <vsoc-icon
                v-show-tips="classifyStatusEum['1'].text"
                type="fill"
                :icon="'icon-tingyong'"
                class="action-btn"
                size="x-large"
              ></vsoc-icon>
            </v-btn>

            <v-btn
              v-if="item.status === '1'"
              icon
              @click.stop="handleStatus(item)"
              v-has:classify-enable="item"
            >
              <vsoc-icon
                v-show-tips="classifyStatusEum['0'].text"
                type="fill"
                :icon="'icon-qiyong'"
                class="action-btn"
                size="x-large"
              ></vsoc-icon>
            </v-btn>

            <v-btn v-has:classify-edit="item" icon @click="edit(item)">
              <vsoc-icon
                v-show-tips="$t('action.edit')"
                type="fill"
                icon="icon-bianji"
                class="action-btn"
                size="x-large"
              ></vsoc-icon>
            </v-btn>
            <!-- <v-btn v-has:classify-del="item" icon @click="del(item)">
              <vsoc-icon
                v-show-tips="$t('action.del')"
                type="fill"
                class="action-btn"
                icon="icon-shanchu"
                size="x-large"
              ></vsoc-icon>
            </v-btn> -->
          </template>
        </v-data-table>

        <!-- 分页器 -->
        <vsoc-pagination
          :page.sync="query.pageNum"
          :size.sync="query.pageSize"
          :total="tableDataTotal"
          @change-page="getTableData"
          @change-size="$_search"
        >
        </vsoc-pagination>
      </v-card-text>

      <vsoc-drawer
        v-model="drawer"
        :title="
          ops === 'new' ? $t('classify.btn.add') : $t('classify.btn.edit')
        "
        :confirm-btn-text="$t('action.confirm')"
        @click:confirm="save"
      >
        <div v-if="drawer">
          <classify-edit
            v-if="ops === 'new' || ops === 'edit'"
            ref="refClassify"
            :item="classifyEt"
            :mode="ops"
            :classifyTypes="classAllList"
            @save="navigationDrawerCloseAndRefresh"
          ></classify-edit>
        </div>
      </vsoc-drawer>
    </v-card>
  </div>
</template>

<script>
import {
  deleteAlarmType,
  getAlarmTypes,
  updateActive,
} from '@/api/classify/index'
import VsocDrawer from '@/components/VsocDrawer.vue'
import VsocPagination from '@/components/VsocPagination.vue'

import breadCrumb from '@/components/bread-crumb/index'
import TableSearch from '@/components/TableSearch/index.vue'
import { setRemainingHeight } from '@/util/utils'
import ClassifyEdit from './classifyEdit.vue'

export default {
  name: 'ClassifyIndex',
  components: {
    VsocPagination,
    ClassifyEdit,
    VsocDrawer,
    breadCrumb,
    TableSearch,
  },
  data() {
    return {
      // 分页参数
      query: {
        type: '',
        status: '',
        name: '',
        pageNum: 1,
        pageSize: 10,
      },
      tableLoading: true,
      tableDataTotal: 0,
      tableData: [],
      tableHeight: '34.5rem',

      ops: '',
      drawer: false,
      classifyEt: this.initRoleData(),
      valid: true,
    }
  },
  watch: {
    drawer(val) {
      if (!val) {
        this.navigationDrawerClose()
      }
    },
  },
  created() {
    this.$_setTableHeight()
    this.$bus.$on('resize', this.$_setTableHeight)
    this.$_search()
  },

  computed: {
    searchList() {
      return [
        {
          type: 'input',
          value: 'name',
          text: this.$t('classify.headers.name'),
        },
        {
          type: 'select',
          value: 'type',
          itemList: this.$store.getters['enums/getClassifyTypeList'],
          text: this.$t('classify.headers.typeName'),
          multiple: false,
        },
        {
          type: 'select',
          value: 'status',
          itemList: this.$store.getters['enums/getClassifyStatusList'],
          text: this.$t('classify.headers.status'),
          multiple: false,
        },
      ]
    },
    headers() {
      return [
        // {
        //   text: this.$t('classify.headers.id'),
        //   value: 'id',
        //   width: '120px',
        //   sortable: false,
        // },
        {
          text: this.$t('classify.headers.typeName'),
          value: 'type',
          width: '140px',
        },
        {
          text: this.$t('classify.headers.name'),
          value: 'name',
          width: '160px',
        },

        {
          text: this.$t('classify.headers.description'),
          value: 'description',
          width: '160px',
        },
        {
          text: this.$t('classify.headers.status'),
          value: 'status',
          width: '120px',
        },
        {
          text: this.$t('classify.headers.createDate'),
          value: 'createDate',
          width: '160px',
        },
        {
          text: this.$t('classify.headers.updateDate'),
          value: 'updateDate',
          width: '160px',
        },
        {
          text: this.$t('classify.headers.updateUser'),
          value: 'updateUser',
          width: '120px',
        },
        {
          text: this.$t('global.owner'),
          value: 'createUser',
          width: '120px',
        },
        {
          text: '',
          value: 'actions',
          sortable: false,
          width: '120',
        },
      ]
    },
    classifyEum() {
      return this.$store.state.enums.enums.ClassifyType
    },
    classifyTypeList() {
      return this.$store.getters['enums/getClassifyTypeList']
    },
    classAllList() {
      let list = this.$store.getters['enums/getClassifyTypeList'].filter(
        v => !['2', '3'].includes(v.value),
      )
      return list
      // return this.$store.getters['enums/getClassifyTypeList'].map(v => {
      //   return {
      //     ...v,
      //     disabled: v.value === '2' || v.value === '3' ? true : false,
      //   }
      // })
    },
    classifyStatusEum() {
      return this.$store.state.enums.enums.ClassifyStatus
    },
    classifyStatusList() {
      return this.$store.getters['enums/getClassifyStatusList']
    },
  },
  beforeDestroy() {
    this.$bus.$off('resize', this.$_setTableHeight)
  },
  methods: {
    handleStatus(item) {
      const text =
        item.status === '0'
          ? this.classifyStatusEum['1'].text
          : this.classifyStatusEum['0'].text
      this.$swal({
        title:
          item.status === '0'
            ? this.$t('response.swal.stop')
            : this.$t('response.swal.start'),
        text: this.$t('response.swal.sure', [text, item.name]),
        icon: item.status === '0' ? 'error' : 'success',
        reverseButtons: true,
        showCancelButton: true,
        confirmButtonText: this.$t('action.confirm'),
        cancelButtonText: this.$t('action.cancel'),
        customClass: {
          confirmButton: 'sweet-btn-primary',
        },
      }).then(async result => {
        if (result.isConfirmed) {
          try {
            const res = await updateActive({
              id: item.id,
              status: item.status === '0' ? '1' : '0',
            })
            if (res.code === 200) {
              this.$notify.info('success', this.$t('response.swal.tip', [text]))
            }
            this.$_search()
          } catch (e) {
            console.error(text + `错误：${e}`)
          }
        }
      })
    },
    $_setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = setRemainingHeight(
          this.$store.state.global.subMarginFn,
        )
      })
    },

    navigationDrawerClose() {
      this.drawer = false
      this.classifyEt = this.initRoleData()
    },
    navigationDrawerCloseAndRefresh() {
      this.$_search()
      this.drawer = false
      this.classifyEt = this.initRoleData()
    },
    $_search() {
      this.query.pageNum = 1
      this.getTableData()
    },
    $_reset() {
      this.query = Object.assign(
        this.$data.query,
        this.$options.data.call(this).query,
      )
      this.$_search()
    },
    // 加载表格数据
    async getTableData() {
      this.tableLoading = true
      try {
        const res = await getAlarmTypes(this.query)
        this.tableDataTotal = res.data.total
        this.tableData = res.data.records
      } catch (e) {
        console.error(`获取分类错误：${e}`)
      } finally {
        this.tableLoading = false
      }
    },

    add() {
      this.ops = 'new'
      this.drawer = true
    },
    edit(data) {
      this.ops = 'edit'

      Object.keys(data).forEach(key => {
        this.classifyEt[key] = data[key]
      })
      this.drawer = true
    },
    grantUi(data) {
      this.ops = 'grant'
      this.classifyEt = {
        id: data.id,
        name: data.name,
        description: data.description,
        type: data.type,
        classifySuperiorList: data.classifySuperiorList,
      }
      this.drawer = true
    },

    initRoleData() {
      return {
        id: '',
        name: '',
        description: '',
        type: '',
        classifySuperiorList: [],
      }
    },
    save(callback) {
      this.$refs.refClassify.save(callback)
    },
    //删除
    del(data) {
      if (data.status === '0') {
        return this.$notify.info('info', '已启用的分类不允许删除!')
      }
      this.$swal({
        title: this.$t('classify.swal.del.title'),
        text: this.$t('classify.swal.del.text', [data.name]),
        icon: 'warning',
        reverseButtons: true,
        showCancelButton: true,
        confirmButtonText: this.$t('action.confirm'),
        cancelButtonText: this.$t('action.cancel'),
        customClass: {
          confirmButton: 'sweet-btn-primary',
        },
      }).then(async result => {
        if (result.isConfirmed) {
          const params = {
            id: data.id,
          }
          const res = await deleteAlarmType(params)
          if (res.code === 200) {
            this.$notify.info(
              'success',
              this.$t('global.hint.del', [data.name]),
            )
            this.$_search()
          }
        } else {
          // this.$notify.info('warning', `已取消删除分类：${data.name}`)
        }
      })
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .header {
  align-items: center;
  .v-chip.v-size--default {
    height: auto;
  }
  .v-text-field__details {
    display: none;
  }
  .v-input--dense > .v-input__control > .v-input__slot {
    margin: 0;
  }
}
</style>
