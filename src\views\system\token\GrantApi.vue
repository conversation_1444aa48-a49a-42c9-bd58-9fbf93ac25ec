<template>
  <vsoc-drawer
    v-model="grantDrawer"
    :title="
      $generateMenuTitle($route.meta.buttonInfo['bind-api']) +
      '（' +
      bindNum +
      '）'
    "
    :width="700"
    @click:confirm="onSave"
  >
    <v-form ref="form" v-model="valid" lazy-validation>
      <v-row class="pl-2 pr-2 mx-0 my-0">
        <v-col cols="12" class="px-0 py-0">
          <el-tree
            v-if="grantDrawer"
            ref="elTree"
            labelText="API"
            class="mt-n2"
            :nodeKey="'url'"
            :organizationFlag="false"
            :showCheckbox="true"
            :treeData="permissionList"
            :default-props="defaultProps"
            :expandFlag="false"
            :isApiIcon="true"
          >
          </el-tree>
        </v-col>
      </v-row>
    </v-form>
  </vsoc-drawer>
</template>

<script>
import { bindApis, getApiList, getSelectApis } from '@/api/system/token'
import elTree from '@/components/el-tree/index'
import VsocDrawer from '@/components/VsocDrawer.vue'
import { deepClone } from '@/util/utils'
export default {
  name: 'GrantApi',
  components: {
    elTree,
    VsocDrawer,
  },
  data() {
    return {
      bindNum: 0,
      grantDrawer: false,
      valid: true,
      tokenId: '',
      queryField: '',

      permissionList: [],
      defaultProps: {
        children: 'apiInfoVoList',
        label: 'name',
      },
    }
  },

  mounted() {
    const _this = this
    Promise.all([this.loadApiList()]).then(values => {
      _this.permissionList = _this.deepFilter(deepClone(values[0]))
    })
  },
  methods: {
    open(item) {
      this.tokenId = item.id
      this.grantDrawer = true
      this.$nextTick(() => {
        // this.$refs['elTree'].filterText = ''
        // this.$refs['elTree'].$refs['tree'].setCheckedKeys([])
        Promise.all([this.getApiIds()]).then(values => {
          this.bindNum = values[0].length
          this.$refs['elTree'].$refs['tree'].setCheckedKeys(values[0])
        })
      })
    },
    deepFilter(list) {
      return list.filter(item => {
        if (item.description) {
          item.name = `${item.description}（${item.url}）`
        } else {
          item.name = item.apiGroup
        }
        if (item.apiInfoVoList) {
          item.apiInfoVoList = this.deepFilter(item.apiInfoVoList)
        }
        return true
      })
    },
    loadApiList() {
      return new Promise((resolve, reject) =>
        getApiList({ queryField: '' })
          .then(resp => {
            if (resp.code === 200 && resp.data) {
              resolve(resp.data)
            }
          })
          .catch(err => {
            reject(err)
          }),
      )
    },

    // 获取选中的菜单id数组
    getApiIds() {
      const _this = this

      return new Promise((resolve, reject) => {
        getSelectApis({ accessTokenId: _this.tokenId })
          .then(resp => {
            if (resp.code === 200 && resp.data) {
              resolve(resp.data)
            }
          })
          .catch(e => {
            reject(e)
          })
      })
    },

    // 重新授权
    onSave(callback) {
      const bool = this.$refs.form.validate()
      if (!bool) return callback(false, true)
      let urlList =
        this.$refs['elTree'].$refs['tree'].getCheckedKeys(true) || []
      bindApis({ accessTokenId: this.tokenId, urlList: urlList })
        .then(resp => {
          if (resp.code === 200) {
            this.$notify.info('success', this.$t('role.grant'))
            callback()
          }
        })
        .catch(e => {
          console.log(e)
          callback(false, true)
        })
    },
  },
}
</script>

<style lang="scss" scoped></style>
