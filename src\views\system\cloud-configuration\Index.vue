<template>
  <div>
    <bread-crumb class="mb-3"></bread-crumb>

    <v-card tile class="main-content">
      <v-card-text class="pa-0">
        <div class="d-flex justify-space-between align-center flex-row-reverse">
          <div class="d-flex align-end">
            <v-text-field
              v-model="query.itemName"
              color="primary"
              hide-details
              :label="$t('cloud.headers.itemName')"
              dense
              outlined
              class="text-width"
              @keyup.enter.native="$_search"
            ></v-text-field>
            <v-btn
              color="primary--text bg-btn ml-3"
              elevation="0"
              @click="$_search"
            >
              <!-- <v-icon class="me-1"> mdi-magnify </v-icon> -->
              <span>{{ $t('action.search') }}</span>
            </v-btn>
          </div>
        </div>

        <v-data-table
          ref="collectTable"
          fixed-header
          :items-per-page="tableDataTotal"
          item-key="id"
          :height="tableHeight"
          hide-default-footer
          :headers="headers"
          :items="tableData"
          class="table border-radius-xl mt-3 thead-light pb-4"
          :loading="tableDataLoading"
        >
          <template v-slot:item.itemName="{ item }">
            <div
              v-show-tips
              style="max-width: 20rem"
              class="text-overflow-hide"
            >
              {{ item.itemName }}
              <!-- {{ $generateName(item.itemName, item.itemEnName) }} -->
            </div>
          </template>

          <template v-slot:item.itemEnName="{ item }">
            <div
              v-show-tips
              style="max-width: 20rem"
              class="text-overflow-hide"
            >
              {{ item.itemEnName }}
            </div>
          </template>

          <template v-slot:item.staticValue="{ item }">
            <!-- <div>
              {{ item.value }}
            </div> -->
          </template>

          <template v-slot:item.contentSourceType="{ item }">
            <v-chip
              small
              label
              class="text--secondary"
              v-if="$toItem(cloudMethodEnum, item.contentSourceType)"
              >{{
                $toItem(cloudMethodEnum, item.contentSourceType).text
              }}</v-chip
            >
          </template>

          <template v-slot:item.actions="{ item }">
            <v-btn icon @click="onEdit(item)" v-has:cloud-edit>
              <vsoc-icon
                v-show-tips="$t('action.edit')"
                type="fill"
                icon="icon-bianji"
                class="action-btn"
                size="x-large"
              ></vsoc-icon>
            </v-btn>
          </template>
        </v-data-table>
      </v-card-text>
    </v-card>
    <cloud-edit
      ref="cloudEdit"
      :item="editForm"
      :mode="ops"
      @refresh="getTableData"
    ></cloud-edit>
  </div>
</template>

<script>
import { max } from '@/@core/utils/validation'
import { getCloudList } from '@/api/system/cloud'
import breadCrumb from '@/components/bread-crumb/index'
import { dataFilter } from '@/util/filters'
import { setRemainingHeight } from '@/util/utils'
import CloudEdit from '@/views/system/cloud-configuration/CloudEdit'
export default {
  name: 'CloudConfigurationIndex',
  components: {
    breadCrumb,
    CloudEdit,
  },
  filters: {
    dataFilter,
  },
  data() {
    return {
      max,
      tableDataLoading: false,

      // 分页参数
      query: {
        itemName: '', //域名
      },
      tableDataTotal: 0,
      tableHeight: '34.5rem',
      tableData: [],
      ops: '',
      editForm: this.initRoleData(),
    }
  },
  computed: {
    changeEnum() {
      return this.$store.getters['enums/getChangeType']
    },
    activeEnum() {
      return this.$store.getters['enums/getActiveStatus']
    },
    cloudMethodEnum() {
      return this.$store.getters['enums/getCloudMethod']
    },
    headers() {
      return [
        {
          text: this.$t('cloud.headers.itemKey'),
          value: 'itemKey',
          width: 100,
        },
        {
          text: this.$t('cloud.headers.itemName'),
          value: 'itemName',
          width: 200,
        },
        {
          text: this.$t('cloud.headers.itemEnName'),
          value: 'itemEnName',
          width: 160,
        },
        {
          text: this.$t('cloud.headers.readMethod'),
          value: 'contentSourceType',
          width: 160,
        },
        // {
        //   text: this.$t('global.createUser'),
        //   value: 'createUser',
        //   width: 160,
        // },
        // {
        //   text: this.$t('global.createDate'),
        //   value: 'createDate',
        //   width: 160,
        // },
        {
          text: this.$t('global.updateUser'),
          value: 'updateUser',
          width: 160,
        },
        {
          text: this.$t('global.updateDate'),
          value: 'updateDate',
          width: 160,
        },
        {
          text: '',
          value: 'actions',
          sortable: false,
          width: 80,
        },
      ]
    },
  },

  created() {
    this.$_setTableHeight()
    this.$bus.$on('resize', this.$_setTableHeight)
    this.$_search()
  },
  beforeDestroy() {
    this.$bus.$off('resize', this.$_setTableHeight)
  },
  methods: {
    add() {
      this.ops = 'new'
      this.$refs.cloudEdit.isDrawerShow = true
    },
    onEdit(record) {
      this.ops = 'edit'

      this.editForm = { ...record }
      this.$refs.cloudEdit.isDrawerShow = true
    },

    initRoleData() {
      return {
        platformName: '',
        description: '',
      }
    },

    $_search() {
      this.query.pageNum = 1
      this.getTableData()
    },

    $_setTableHeight() {
      this.$nextTick(() => {
        const filterFn = () => {
          return 50 - 28
        }
        this.tableHeight = setRemainingHeight(filterFn)
      })
    },

    getRandomDate() {
      const start = new Date(2020, 0, 1)
      const end = new Date(2023, 3, 26)
      const randomDate = new Date(
        start.getTime() + Math.random() * (end.getTime() - start.getTime()),
      )
      return randomDate.toLocaleString()
    },
    // 获取平台
    async getTableData() {
      // 保存查询参数
      this.tableDataLoading = true
      try {
        const { data } = await getCloudList(this.query)
        this.tableData = data
        this.tableDataTotal = data.length
      } catch (e) {
        console.error(`获取平台管理错误：${e}`)
      } finally {
        this.tableDataLoading = false
      }
    },
  },
}
</script>
