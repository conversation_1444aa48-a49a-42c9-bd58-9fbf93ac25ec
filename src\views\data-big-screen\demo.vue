<template>
  <div id="data-view" v-resize="getScreenSize">
    <dv-loading v-if="isLoading">Loading...</dv-loading>
    <div id="dv-full-screen-container" class="cloud-bg" v-else>
      <div class="main-header main-header-1">
        <!-- <v-img
          @click="goIndex"
          contain
          :height="$getCeilSize(36)"
          :min-width="$getCeilSize(360)"
          :aspect-ratio="360 / 36"
          src="./images/logo.svg"
          alt="logo"
          class="mh-logo cursor-pointer"
        /> -->
        <div class="title-date1 d-flex align-center">
          已运营<span class="title-header-day mx-2">15</span
          ><span>{{ $t('global.time.day1') }}</span>
        </div>
        <dv-decoration-8
          @click="goIndex"
          class="header-left-decoration mh-high"
          :color="[primary, primary]"
        />

        <dv-decoration-5
          class="header-center-decoration"
          :color="[primary, primary]"
        />
        <dv-decoration-8
          class="header-right-decoration"
          :reverse="true"
          :color="[primary, primary]"
        />
        <div class="mh-title" @click="goIndex">
          安徽智能网联汽车安全监测平台
        </div>
        <div class="mh-date d-flex justify-lg-space-around align-center">
          <span class="text-center"
            >{{ dateYear }} {{ dateDay }} {{ dateWeek }}</span
          >

          <v-btn icon @click="toggleFullScreen">
            <v-icon v-if="isFull" :size="$getCeilSize(30)" :color="primary"
              >mdi-power</v-icon
            >
            <vsoc-icon
              :size="$getCeilSize(30)"
              v-else
              :icon="'icon-daxiaopingqiehuan-1'"
            ></vsoc-icon>
          </v-btn>
        </div>
      </div>

      <div class="cloud-container">
        <div class="item1">
          <dv-border-box-12>
            <left-demo-cloud-1 :list="securityRisks"></left-demo-cloud-1>
          </dv-border-box-12>
        </div>
        <div class="item2">
          <dv-border-box-12>
            <RightDemoCloud1 :isMapChina="isMapChina"></RightDemoCloud1>
            <!-- <right-cloud-1
              :list="overviewData.list"
              :total="overviewData.total"
            ></right-cloud-1> -->
          </dv-border-box-12>
        </div>
        <div class="item3">
          <dv-border-box-12>
            <left-cloud-2
              :list="distributionData.list"
              :total="distributionData.total"
              title="安全事件危害等级占比"
            ></left-cloud-2>
          </dv-border-box-12>
        </div>
        <div class="item4">
          <dv-border-box-12>
            <center-cloud-2 :total="intrusions"></center-cloud-2>
          </dv-border-box-12>
        </div>
        <div class="item5">
          <dv-border-box-12>
            <center-cloud-3
              :list="attacksTop.list"
              :total="attacksTop.total"
              title="近30天告警类型分布"
            ></center-cloud-3>
          </dv-border-box-12>
        </div>
        <div class="item6">
          <dv-border-box-12>
            <RightDemoCloud2></RightDemoCloud2>
            <!-- <right-cloud-2
              :list="riskTop.list"
              :total="riskTop.total"
            ></right-cloud-2> -->
          </dv-border-box-12>
        </div>
        <div class="item7">
          <center-demo-cloud-1
            :toData="flyList"
            title="攻击地图测绘"
            @changeMap="isChina => (isMapChina = isChina)"
          ></center-demo-cloud-1>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getCeilSize as $getCeilSize } from './component/chart.js'

import LeftCloud2 from './component/LeftCloud2.vue'
import LeftDemoCloud1 from './component/LeftDemoCloud1.vue'
import RightDemoCloud1 from './component/rightDemoCloud1.vue'
import RightDemoCloud2 from './component/rightDemoCloud2.vue'

import RightCloud1 from './component/RightCloud1.vue'
import RightCloud2 from './component/RightCloud2.vue'

import CenterCloud1 from './component/CenterCloud1.vue'
import CenterCloud2 from './component/CenterCloud2.vue'
import CenterCloud3 from './component/CenterCloud3.vue'
import CenterDemoCloud1 from './component/CenterDemoCloud1.vue'

import { toDate } from '@/util/filters'
import { exitFullscreen, isFullScreen, requestFullScreen } from '@/util/utils'
import DvDecoration8 from './component/dv-decoration8/Index.vue'

export const ISO_FORMAT = 'yyyy-MM-dd HH:mm:ss'

//判断是否是全屏状态
let isFull =
  Math.abs(
    window.screen.height - window.document.documentElement.clientHeight,
  ) <= 17

// 阻止F11键默认事件，用HTML5全屏API代替
window.addEventListener('keydown', function (e) {
  e = e || window.event
  if (e.keyCode === 122 && !isFull) {
    e.preventDefault()
    // enterFullScreen()
    requestFullScreen(document.documentElement)
  }
})

export default {
  name: 'data-big-screen',
  components: {
    LeftDemoCloud1,
    LeftCloud2,
    CenterCloud2,
    RightCloud1,
    RightCloud2,
    CenterCloud3,
    CenterCloud1,
    DvDecoration8,
    RightDemoCloud1,
    RightDemoCloud2,
    CenterDemoCloud1,
  },
  data() {
    return {
      isMapChina: false,
      $getCeilSize,
      isFull: isFull,
      currentDate: 7,
      primary: '#44e2fe',
      dateDay: null,
      dateYear: null,
      dateWeek: null,

      timer: null,
      timer2: null,

      isLoading: false,
      // 获取浏览器可视区域高度（包含滚动条）、
      // 获取浏览器可视区域高度（不包含工具栏高度）、
      // 获取body的实际高度  (三个都是相同，兼容性不同的浏览器而设置的)
      screenHeight:
        window.innerHeight ||
        document.documentElement.clientHeight ||
        document.body.clientHeight,
      screenWidth:
        window.innerWidth ||
        document.documentElement.clientWidth ||
        document.body.clientWidth,

      securityRisks: {
        attacksToday: 3.748 * Math.pow(10, 3), //今日告警条数
        attacksInSevenDays: 1.23 * Math.pow(10, 6), //近7天告警条数
        leakedInternetDomain: 1.56 * Math.pow(10, 6), //互联网暴漏的有效域名条数
        securityBaselineItems: 0, //安全基线配置项总数
        continuousDaysOfSafeOperations: 256, //持续安全运营天数
      },
      flyList: [],
      overviewData: {
        list: [],
        total: 0,
      },
      distributionData: {
        list: [],
        total: 0,
      },
      intrusions: 0,
      riskTop: {
        list: [],
        total: 0,
      },
      attacksTop: {
        list: [],
        total: 0,
      },
    }
  },
  // async beforeRouteEnter(to, from, next) {
  //   debugger
  //   if (getToken() && this.$store.state.global.cloudConfigList.length === 0) {
  //     let list = await this.$store.dispatch('global/loadCloudConfigList')
  //     loadCloudMenuEn(list)
  //     loadCloudMenuZh(list)
  //   }
  //   next()
  // },
  computed: {
    weekday() {
      return [
        this.$t('screen.week.sun'),
        this.$t('screen.week.mon'),
        this.$t('screen.week.tue'),
        this.$t('screen.week.wed'),
        this.$t('screen.week.thu'),
        this.$t('screen.week.fri'),
        this.$t('screen.week.sat'),
      ]
    },
  },
  created() {
    this.loadData(true)
  },
  mounted() {
    this.$nextTick(() => {
      if (!isFull || !isFullScreen()) {
        // enterFullScreen()
        requestFullScreen(document.documentElement)
        // document.documentElement.requestFullscreen({ navigationUI: 'hide' })
      }
    })
    this.timeInterval()
    let time = 60
    this.timer2 = setInterval(this.loadData, time * 1000)
  },
  beforeDestroy() {
    clearInterval(this.timer)
    clearInterval(this.timer2)
  },
  async beforeRouteEnter(to, from, next) {
    // if (getToken() && store.state.global.cloudConfigList.length === 0) {
    //   let list = await store.dispatch('global/loadCloudConfigList')
    //   loadCloudMenuEn(list)
    //   loadCloudMenuZh(list)
    // }
    next(vm => {
      // 通过 `vm` 访问组件实例
      if (!isFull || !isFullScreen()) {
        // enterFullScreen()
        requestFullScreen(document.documentElement)
        // document.documentElement.requestFullscreen({ navigationUI: 'hide' })
      }
    })
  },
  beforeRouteLeave(to, from, next) {
    if (isFull || isFullScreen()) {
      exitFullscreen()
      // exitFullScreen()
    }
    next()
  },
  methods: {
    async loadData(mode = false) {
      // console.log('开始了', mode, format(Date.now(), ISO_FORMAT))
      this.isLoading = mode
      Promise.all([
        this.loadSecurityRisks(),
        this.loadFlyMap(),
        this.loadAssetsOverview(),
        this.loadVulnerabilityStatisticsBySeverity(),
        this.loadSuccessfulIntrusions(),
        this.loadSecurityBaselineRiskTop(),
        this.loadTopAttackMethods(),
      ])
        .then(res => {
          if (!this.isFull && mode) {
            // this.$notify.info('success', this.$t('screen.fullHint'))
          }
          // this.continueDay = differenceInDays(
          //   new Date(),
          //   new Date(this.continueObj.value),
          // )
        })
        .finally(() => {
          this.isLoading = false
        })
    },
    async loadTopAttackMethods() {
      // const { data } = await getTopAttackMethods()
      // let list = Object.entries(data.packetData).map(item => {
      //   return {
      //     name: item[0],
      //     value: item[1],
      //   }
      // })
      this.attacksTop.list = [
        {
          name: '白名单异常访问',
          value: 332,
        },
        {
          name: 'APP异常安装',
          value: 1055,
        },
        {
          name: '数据流量异常增长',
          value: 798,
        },
        {
          name: '通信链路安全协议失效',
          value: 1145,
        },
        {
          name: '区域车辆异常聚集',
          value: 678,
        },
      ]
      this.attacksTop.total = 4008
    },
    async loadSecurityBaselineRiskTop() {
      // const { data } = await getSecurityBaselineRiskTop()
      let data = {
        total: 5662,
        packetData: {
          'SSH listening on default port': 1310,
          'Policy for the maximum number of days an account is valid after a Linux password expires': 1271,
          'Limit remote login of users with root privileges': 1281,
          'Linux account password lifetime policy': 1058,
          'sudo password-less user detection': 742,
        },
      }
      let list = Object.entries(data.packetData).map(item => {
        return {
          name: item[0],
          value: item[1],
        }
      })
      this.riskTop.list = list
      this.riskTop.total = data.total
    },
    async loadSuccessfulIntrusions() {
      // const { data } = await getSuccessfulIntrusions()
      this.intrusions = 0
      this.intrusions = 11273
    },
    async loadVulnerabilityStatisticsBySeverity() {
      // const { data } = await getVulnerabilityStatisticsBySeverity()
      let data = {
        total: 400,
        packetData: {
          High: 100,
          Low: 100,
          Medium: 100,
          Critical: 100,
        },
      }
      let list = Object.entries(data.packetData).map(item => {
        return {
          name: item[0],
          value: item[1],
        }
      })
      this.distributionData.list = list
      this.distributionData.total = data.total
    },
    async loadAssetsOverview() {
      // const { data } = await getAssetsOverview()
      let data = {
        total: 2215,
        packetData: {
          cdb: 72,
          cbs: 1280,
          clb: 94,
          cvm: 663,
          mongodb: 21,
          postgres: 5,
          redis: 80,
        },
      }
      let list = Object.entries(data.packetData).map(item => {
        return {
          name: item[0],
          value: item[1],
        }
      })
      this.overviewData.list = list
      this.overviewData.total = data.total
    },
    async loadSecurityRisks() {
      // const { data } = await getSecurityRisks()
      this.securityRisks = {
        attacksToday: 5668, //今日告警条数
        attacksInSevenDays: 826, //近7天告警条数
        leakedInternetDomain: 566, //互联网暴漏的有效域名条数
        securityBaselineItems: 6, //安全基线配置项总数
        continuousDaysOfSafeOperations: 682, //持续安全运营天数
      }
    },
    async loadFlyMap() {
      // const { data } = await getFlyMap()
      this.flyList = [
        [
          { name: 'China,anhui', location: [112.936271, 28.235399] },
          { name: 'China,anhui', location: [112.936271, 28.235399] },
        ],
        [{ name: 'Albania' }, { name: 'China,anhui' }],
        [{ name: 'Bhutan' }, { name: 'China,anhui' }],
        [{ name: 'United States, Washington' }, { name: 'China,anhui' }],
        [{ name: 'Norway' }, { name: 'China,anhui' }],
        [
          {
            name: 'Australia',
            location: [133.565867, -25.303147],
          },
          { name: 'China,anhui' },
        ],
        [
          {
            name: 'Brazil',
            location: [-54.013916, -8.270681],
          },
          { name: 'China,anhui' },
        ],
      ]
    },
    toggleFullScreen() {
      if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen()
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen()
        }
        if (document.referrer) {
          this.$router.go(-1)
        } else {
          this.$router.push('/')
        }
      }
      // document.documentElement.onfullscreenchange = () => {
      //   console.log('进来了')
      //   location.reload()
      // }
    },
    timeInterval() {
      this.timer = setInterval(() => {
        this.dateDay = toDate(new Date(), 'hh:mm:ss')
        this.dateYear = toDate(new Date(), 'yyyy/MM/dd')
        this.dateWeek = this.weekday[new Date().getDay()]
      }, 1000)
    },
    goIndex() {
      this.$router.push('/')
    },
    getScreenSize() {
      this.getScreenHeight()
      this.getScreenWidth()
      isFull =
        Math.abs(
          window.screen.height - window.document.documentElement.clientHeight,
        ) <= 17
      this.isFull = isFull
    },
    // 获取浏览器高度进行自适应
    getScreenHeight() {
      this.screenHeight =
        window.innerHeight ||
        document.documentElement.innerHeight ||
        document.body.clientHeight
    },
    // 字体大小根据宽度自适应
    getScreenWidth() {
      this.screenWidth =
        window.innerWidth ||
        document.documentElement.clientWidth ||
        document.body.clientWidth
      this.$store.commit('global/setClientWidth', this.screenWidth)
    },
  },
}
</script>

<style lang="scss">
@import './index.scss';
</style>
