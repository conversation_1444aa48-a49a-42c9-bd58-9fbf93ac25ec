// 安全态势接口
import { request, vsocPath } from '../../util/request'

// 安全态势汇总接口
export const getSecurityPosture = function (data) {
  return request({
    url: `${vsocPath}/assetAlarm/findSecurityPosture`,
    method: 'post',
    data,
  })
}

// 数据处理统计接口
export const getDataProcessing = function (data) {
  return request({
    url: `${vsocPath}/flow/dataProcessing`,
    method: 'post',
    data,
  })
}

// 告警事件类型top5
export const getAlarmsSorting = function (data) {
  return request({
    url: `${vsocPath}/assetAlarm/findAlarmsSorting`,
    method: 'post',
    data,
  })
}

// 告警事件趋势接口
export const getAlarmtRend = function (data) {
  return request({
    url: `${vsocPath}/assetAlarm/alarmtRend`,
    method: 'post',
    data,
  })
}
