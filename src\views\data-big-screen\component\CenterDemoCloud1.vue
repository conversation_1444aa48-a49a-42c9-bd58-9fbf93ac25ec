<template>
  <div class="box px-0 pb-0" style="padding-top: 3.5% !important">
    <div class="d-flex align-center justify-space-between">
      <div class="c-box-header" style="width: 50%">近7日攻击地图测绘</div>
      <v-btn v-show-tips="'切换地图'" icon @click.stop="changeMap">
        <vsoc-icon
          class="color--primary"
          type="fill"
          icon="icon-qiehuan"
          size="x-large"
        ></vsoc-icon>
      </v-btn>
    </div>

    <!-- <div class="box-chart">
      <dv-flyline-chart-enhanced
        :config="config"
        style="width: 100%; height: 100%"
      />
    </div> -->
    <vsoc-chart
      echartId="fly"
      :option="isChina ? chinaOption : option"
    ></vsoc-chart>
  </div>
</template>

<script>
import VsocChart from '@/components/VsocChart.vue'
import { getRoundSize, primary } from './chart'
import * as geoChinaMap from './map/geoChinaMapEn.json'
// import * as geoCoordMap from './map/geoCoordMapEn.json'
import chinaMap from '@/views/data-big-screen/component/map/china-center.js'
import worldChinaCenter from '@/views/data-big-screen/component/map/world-china-center'
import { geoCoordMap } from './map/geoCoordMapEn.js'

// 飞行线样式
var planePath =
  'path://M1705.06,1318.313v-89.254l-319.9-221.799l0.073-208.063c0.521-84.662-26.629-121.796-63.961-121.491c-37.332-0.305-64.482,36.829-63.961,121.491l0.073,208.063l-319.9,221.799v89.254l330.343-157.288l12.238,241.308l-134.449,92.931l0.531,42.034l175.125-42.917l175.125,42.917l0.531-42.034l-134.449-92.931l12.238-241.308L1705.06,1318.313z'

const convertData = data => {
  var res = []

  for (var i = 0; i < data.length; i++) {
    var dataItem = data[i]

    if (!geoCoordMap[dataItem[0].name]) {
      geoCoordMap[dataItem[0].name] = dataItem[0].location
    }
    if (!geoCoordMap[dataItem[1].name]) {
      geoCoordMap[dataItem[1].name] = dataItem[1].location
    }
    var fromCoord = geoCoordMap[dataItem[0].name]
    var toCoord = geoCoordMap[dataItem[1].name]
    if (fromCoord && toCoord) {
      let xFrom = fromCoord[0]
      if (xFrom <= -30) {
        fromCoord[0] = 360 + xFrom
      }
      let xTo = toCoord[0]
      if (xTo <= -30) {
        toCoord[0] = 360 + xTo
      }
      res.push({
        fromName: dataItem[0].name,
        toName: dataItem[1].name,
        coords: [fromCoord, toCoord],
        value: dataItem[1].value,
      })
    }
  }
  return res
}

var convertChinaData = function (data) {
  var res = []
  for (var i = 0; i < data.length; i++) {
    var dataItem = data[i]
    var fromCoord = geoChinaMap.default[dataItem[0].name]
    var toCoord = geoChinaMap.default[dataItem[1].name]
    if (fromCoord && toCoord) {
      res.push({
        fromName: dataItem[0].name,
        toName: dataItem[1].name,
        coords: [fromCoord, toCoord],
      })
    }
  }
  return res
}

export default {
  name: 'CenterDemoCloud2',
  props: {
    toData: {
      type: Array,
      default: () => {
        return [
          [
            { name: 'China,ChangSha', location: [112.936271, 28.235399] },
            { name: 'China,ChangSha', location: [112.936271, 28.235399] },
          ],
          [{ name: 'China,ChangSha' }, { name: 'Albania' }],
          [{ name: 'China,ChangSha' }, { name: 'Burundi' }],
          [{ name: 'China,ChangSha' }, { name: 'Belarus' }],
          [{ name: 'China,ChangSha' }, { name: 'Bhutan' }],
          [{ name: 'China,ChangSha' }, { name: 'United States, Washington' }],
          [{ name: 'China,ChangSha' }, { name: 'Canada, Winnipeg' }],
          [{ name: 'China,ChangSha' }, { name: 'Russia, Nizhny Novgorod' }],
          // [{ name: 'China,ChangSha' }, { name: 'Brazil, Porto Alegre' }],
          [{ name: 'China,ChangSha' }, { name: 'Norway' }],
          [
            { name: 'China,ChangSha' },
            {
              name: 'Australia',
              location: [133.565867, -25.303147],
            },
          ],
          [
            { name: 'China,ChangSha' },
            {
              name: 'Brazil',
              location: [-54.013916, -8.270681],
            },
          ],
        ]
      },
    },
  },
  components: {
    VsocChart,
  },
  created() {
    this.$echarts.registerMap('world', worldChinaCenter)
  },
  data() {
    return {
      // geoCoordMap,
      // geoChinaMap,
      isChina: false,
    }
  },
  computed: {
    option() {
      var toData = [
        [
          { name: 'China,anhui', location: [112.936271, 28.235399] },
          { name: 'China,anhui', location: [112.936271, 28.235399] },
        ],
        [{ name: 'Albania' }, { name: 'China,anhui' }],
        // [{ name: 'Burundi' }, { name: 'China,anhui' }],
        // [{ name: 'Belarus' }, { name: 'China,anhui' }],
        [{ name: 'Bhutan' }, { name: 'China,anhui' }],
        [{ name: 'United States, Washington' }, { name: 'China,anhui' }],
        // [{ name: 'Canada, Winnipeg' }, { name: 'China,anhui' }],
        // [{ name: 'Russia, Nizhny Novgorod' }, { name: 'China,anhui' }],
        [{ name: 'Norway' }, { name: 'China,anhui' }],
        [
          {
            name: 'Australia',
            location: [133.565867, -25.303147],
          },
          { name: 'China,anhui' },
        ],
        [
          {
            name: 'Brazil',
            location: [-54.013916, -8.270681],
          },
          { name: 'China,anhui' },
        ],
      ]
      let datas = [['China,anhui', toData]]
      let series = []
      datas.forEach((item, i) => {
        series.push(
          {
            name: item[0],
            type: 'lines',
            zlevel: 1,
            // 飞行线特效
            effect: {
              show: true, // 是否显示
              period: 2, // 特效动画时间
              trailLength: 0.4, // 特效尾迹长度。取从 0 到 1 的值，数值越大尾迹越长
              symbol: 'circle', // 特效图形标记
              // symbol: planePath,
              color: primary,
              symbolSize: 4, // 特效图标大小
              loop: true,
            },
            // 线条样式
            lineStyle: {
              curveness: -0.2, // 飞线弧度
              type: 'solid', // 飞线类型
              color: primary, // 飞线颜色
              width: 2, // 飞线宽度
              opacity: 0,
            },
            data: convertData(item[1]),
          },
          {
            type: 'effectScatter', // 带有涟漪特效动画的散点（气泡）图
            coordinateSystem: 'geo',
            markPoint: {
              symbol:
                'path://M9 20C7.75 20 0 13.9705 0 9C0 4.0295 4.0295 0 9 0C13.9705 0 18 4.0295 18 9C18 13.9705 10.25 20 9 20ZM9 11.75C10.5188 11.75 11.75 10.5187 11.75 9C11.75 7.48125 10.5188 6.25 9 6.25C7.48125 6.25 6.25 7.48125 6.25 9C6.25 10.5187 7.48125 11.75 9 11.75Z',
            },
            zlevel: 2,
            symbol: 'circle',
            // symbol:
            //   'path://M9 20C7.75 20 0 13.9705 0 9C0 4.0295 4.0295 0 9 0C13.9705 0 18 4.0295 18 9C18 13.9705 10.25 20 9 20ZM9 11.75C10.5188 11.75 11.75 10.5187 11.75 9C11.75 7.48125 10.5188 6.25 9 6.25C7.48125 6.25 6.25 7.48125 6.25 9C6.25 10.5187 7.48125 11.75 9 11.75Z',
            symbolSize: 10,
            // 涟漪特效
            rippleEffect: {
              period: 4,
              scale: 4,
              brushType: 'stroke',
            },
            itemStyle: {
              color: '#FF385D',
            },
            label: {
              show: true,
              color: '#fff',
              position: 'bottom',
              fontSize: getRoundSize(16),
              formatter: function (item) {
                return ''
              },
            },
            // 这里用来组装自定义数据，以便在tooltip中取得。
            data: item[1].map(dataItem => {
              return {
                name: dataItem[0].name,
                value: geoCoordMap[dataItem[0].name],
              }
            }),
          },
        )
      })

      return {
        // 底图样式
        geo: {
          map: 'world', // 地图类型
          roam: false, // 如果只想要开启缩放或者平移，可以设置成 'scale' 或者 'move'。设置成 true 为都开启
          // top: getRoundSize(24),
          // left: getRoundSize(48),
          // right: getRoundSize(48),
          // bottom: getRoundSize(30),
          zoom: 1.2, // 初始缩放大小
          // center: [11.3316626, 19.5845024], // 地图中心点
          scaleLimit: {
            // 缩放等级
            min: 1,
            max: 15,
          },
          // nameMap:
          //   this.$i18n.locale === 'en'
          //     ? require('./map/nameMapEn.json')
          //     : require('./map/nameMapZh.json'), // 自定义地区的名称映射
          // 三维地理坐标系样式
          itemStyle: {
            color: 'rgba(0,116,177, .6)',
            borderColor: 'rgb(79,228,255)',
            borderWidth: 0.5,
          },
          // 鼠标悬浮样式
          emphasis: {
            itemStyle: {
              areaColor: 'rgba(0,162,248, .6)',
            },
            label: {
              show: true,
              color: '#90d9ff',
              fontSize: 16,
            },
          },
        },

        series: series,

        legend: {
          show: false,
        },

        tooltip: {
          show: false,
          trigger: 'item',
          triggerOn: 'click', // 提示框触发的条件
          enterable: false, // 鼠标是否可进入提示框浮层中，默认为false，如需详情内交互，如添加链接，按钮，可设置为 true
          backgroundColor: 'rgba(0,0,0,0.8)',
          borderColor: 'rgba(0,0,0,0.2)',
          textStyle: {
            color: '#fff',
          },
          formatter: function (params) {
            if (params.seriesType == 'lines') {
              return params.data.fromName + '>' + params.data.toName
            } else if (params.seriesType === 'effectScatter') {
              return params.name
            } else {
              return ''
            }
          },
        },
      }
    },

    chinaOption() {
      var hefeiData = [
        [{ name: '合肥' }, { name: '合肥' }],
        [{ name: '长春' }, { name: '合肥' }],
        [{ name: '包头' }, { name: '合肥' }],
        [{ name: '拉萨' }, { name: '合肥' }],
        [{ name: '乌鲁木齐' }, { name: '合肥' }],
        [{ name: '北海' }, { name: '合肥' }],
        [{ name: '北京' }, { name: '合肥' }],
      ]

      let datas = [['合肥', hefeiData]]
      let series = []
      datas.forEach((item, i) => {
        series.push(
          {
            name: item[0],
            type: 'lines',
            zlevel: 1,
            // 飞行线特效
            effect: {
              show: true, // 是否显示
              period: 2, // 特效动画时间
              trailLength: 0.4, // 特效尾迹长度。取从 0 到 1 的值，数值越大尾迹越长
              symbol: 'circle', // 特效图形标记
              // symbol: planePath,
              color: primary,
              symbolSize: 4, // 特效图标大小
              loop: true,
            },
            // 线条样式
            lineStyle: {
              curveness: -0.2, // 飞线弧度
              type: 'solid', // 飞线类型
              color: primary, // 飞线颜色
              width: 2, // 飞线宽度
              opacity: 0,
            },
            data: convertChinaData(item[1]),
          },
          {
            type: 'effectScatter', // 带有涟漪特效动画的散点（气泡）图
            coordinateSystem: 'geo',
            markPoint: {
              symbol:
                'path://M9 20C7.75 20 0 13.9705 0 9C0 4.0295 4.0295 0 9 0C13.9705 0 18 4.0295 18 9C18 13.9705 10.25 20 9 20ZM9 11.75C10.5188 11.75 11.75 10.5187 11.75 9C11.75 7.48125 10.5188 6.25 9 6.25C7.48125 6.25 6.25 7.48125 6.25 9C6.25 10.5187 7.48125 11.75 9 11.75Z',
            },
            zlevel: 2,
            symbol: 'circle',
            symbolSize: 10,
            // 涟漪特效
            rippleEffect: {
              period: 4,
              scale: 4,
              brushType: 'stroke',
            },
            itemStyle: {
              color: '#FF385D',
            },
            label: {
              show: true,
              color: '#fff',
              position: 'bottom',
              fontSize: getRoundSize(16),
              formatter: function (item) {
                return ''
              },
            },
            // 这里用来组装自定义数据，以便在tooltip中取得。
            data: item[1].map(dataItem => {
              return {
                name: dataItem[0].name,
                value: geoChinaMap.default[dataItem[0].name],
              }
            }),
          },
        )
      })

      return {
        // 底图样式
        geo: {
          map: 'china', // 地图类型
          roam: false, // 如果只想要开启缩放或者平移，可以设置成 'scale' 或者 'move'。设置成 true 为都开启
          // zoom: 1.2, // 初始缩放大小
          top: getRoundSize(25),
          left: getRoundSize(50),
          right: getRoundSize(50),
          bottom: getRoundSize(30),
          // center: [11.3316626, 19.5845024], // 地图中心点
          // scaleLimit: {
          //   // 缩放等级
          //   min: 1,
          //   max: 15,
          // },
          // nameMap:
          //   this.$i18n.locale === 'en'
          //     ? require('./map/nameMapEn.json')
          //     : require('./map/nameMapZh.json'), // 自定义地区的名称映射
          // 三维地理坐标系样式
          itemStyle: {
            color: 'rgba(0,116,177, .6)',
            borderColor: 'rgb(79,228,255)',
            borderWidth: 0.5,
          },
          // 鼠标悬浮样式
          emphasis: {
            itemStyle: {
              areaColor: 'rgba(0,162,248, .6)',
            },
            label: {
              show: true,
              color: '#90d9ff',
              fontSize: 16,
            },
          },
        },

        series: series,

        legend: {
          show: false,
        },

        tooltip: {
          show: false,
          trigger: 'item',
          triggerOn: 'click', // 提示框触发的条件
          enterable: false, // 鼠标是否可进入提示框浮层中，默认为false，如需详情内交互，如添加链接，按钮，可设置为 true
          backgroundColor: 'rgba(0,0,0,0.8)',
          borderColor: 'rgba(0,0,0,0.2)',
          textStyle: {
            color: '#fff',
          },
          formatter: function (params) {
            if (params.seriesType == 'lines') {
              return params.data.fromName + '>' + params.data.toName
            } else if (params.seriesType === 'effectScatter') {
              return params.name
            } else {
              return ''
            }
          },
        },
      }
    },
  },
  methods: {
    changeMap() {
      this.isChina = !this.isChina
      if (this.isChina) {
        this.$echarts.registerMap('china', chinaMap)
      } else {
        this.$echarts.registerMap('world', worldChinaCenter)
      }
      this.$emit('changeMap', this.isChina)
    },
  },
}
</script>
