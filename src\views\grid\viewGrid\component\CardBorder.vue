<template>
  <dv-border-box-12
    class="w-100"
    :style="{ height: height + 'px' }"
    v-if="isBorder === '0' && $route.query.isLightTheme !== 'true'"
  >
    <v-btn
      v-if="showDel"
      class="del-btn"
      :minHeight="$getCeilSize(40)"
      :maxWidth="$getCeilSize(10)"
      color="#44E2FE"
      @click.stop="deleteCard"
    >
      <vsoc-icon
        class="action-btn1"
        :size="$getCeilSize(24)"
        icon="icon-shanchu"
      ></vsoc-icon>
    </v-btn>
    <slot></slot>
  </dv-border-box-12>
  <div v-else class="w-100" :style="{ height: height + 'px' }">
    <v-btn
      v-if="showDel"
      class="del-btn"
      color="#44E2FE"
      :minHeight="$getCeilSize(40)"
      :maxWidth="$getCeilSize(10)"
      @click.stop="deleteCard"
    >
      <vsoc-icon
        class="action-btn1"
        :size="$getCeilSize(24)"
        icon="icon-shanchu"
      ></vsoc-icon>
    </v-btn>
    <slot></slot>
  </div>
</template>
<script>
export default {
  props: {
    height: {
      type: Number,
      default: 0,
    },
    isBorder: {
      type: String,
      default: '0',
    },
    showDel: {
      type: Boolean,
      default: false,
    },
    isLightTheme: {
      type: String,
      default: 'false',
    },
  },
  methods: {
    deleteCard() {
      this.$emit('deleteCard')
    },
  },
}
</script>
