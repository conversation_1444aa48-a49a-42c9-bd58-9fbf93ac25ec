// 日期范围选择框
<template>
  <div>
    <!-- absolute -->
    <v-menu
      v-model="menu"
      :close-on-content-click="false"
      offset-y
      offset-overflow
      eager
      v-bind="menuProps"
    >
      <template v-slot:activator="{ on, attrs }">
        <div v-bind="attrs" v-on="on">
          <slot name="text" :on="on" :attrs="attrs" :inputValue="inputValue">
            <!-- 默认的input -->
            <v-text-field
              style="max-width: 36rem; min-width: 26rem"
              clearable
              readonly
              color="primary"
              large
              hide-details
              :label="inputLabel"
              append-icon="mdi-calendar-range-outline"
              :value="inputValue"
              v-bind="attrs"
              v-on="on"
              @click:clear="reset"
            />
          </slot>
        </div>

        <!-- <v-text-field
          class="v-date-range__input-field"
          :value="inputValue"
          readonly
          :disabled="disabled"
          :placeholder="placeholder"
          v-bind="attrs"
          v-on="on"
          v-bind="inputProps"
        ></v-text-field> -->
      </template>
      <v-card class="v-date-range__menu-content">
        <v-card-text class="pa-0">
          <div
            :data-days="highlightDates.length"
            :class="{
              'v-date-range__pickers': true,
              'v-date-range--highlighted': highlightDates.length,
            }"
          >
            <!-- 标题区域 -->
            <v-card-title v-if="$slots.title">
              <slot v-if="$slots.title" name="title"></slot>
            </v-card-title>

            <v-card-text>
              <div class="v-date-range__content">
                <!-- 左侧快捷选项 -->
                <!-- <v-list-item-group
                  v-if="!noPresets"
                  color="primary"
                  v-model="isPresetActive"
                >
                  <v-subheader
                    class="text-xs text--disabled text-center mx-4 mt-1 font-weight-semibold"
                  >
                    {{ presetLabel }}
                  </v-subheader>

                  <v-list-item
                    v-for="(preset, index) in presets"
                    :key="index"
                    @click="selectPreset(index)"
                    class="text--primary"
                    exact-active-class="primary--text v-list-item--exact-active"
                  >
                    <v-list-item-content class="font-weight-semibold">
                      {{ preset.label }}
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group> -->
                <!-- 采用tab开发 -->
                <div class="d-flex justify-space-between">
                  <template v-if="!noPresets">
                    <v-subheader
                      class="text-xs text--disabled text-no-wrap text-center font-weight-semibold"
                      >{{ presetLabel || $t('action.preset') }}</v-subheader
                    >
                    <v-tabs
                      v-model="isPresetActive"
                      style="max-width: 400px !important"
                      color="primary"
                    >
                      <v-tab class="pa-0 ma-0" style="min-width: 0px" />
                      <v-tab
                        v-for="(preset, index) in presets"
                        :key="index"
                        style="min-width: 4.5rem !important"
                        @click="selectPreset(index)"
                        class="px-2"
                      >
                        {{ preset.label }}
                      </v-tab>
                    </v-tabs>
                  </template>
                  <div class="d-flex align-center justify-end flex-grow-1">
                    <v-btn
                      v-if="actionLabels && actionLabels.reset"
                      class="mr-2"
                      small
                      depressed
                      @click="reset"
                    >
                      {{ mergedActionLabels.reset }}
                    </v-btn>
                    <v-btn class="mr-2" small depressed @click="menu = false">
                      {{ mergedActionLabels.cancel }}
                    </v-btn>
                    <v-btn
                      small
                      depressed
                      color="primary"
                      :disabled="!bothSelected"
                      @click="applyRange"
                      class="mr-4"
                    >
                      {{ mergedActionLabels.apply }}
                    </v-btn>
                  </div>
                </div>
                <!-- <v-divider
                  v-if="!noPresets"
                  class="ml-0 mr-2"
                  vertical
                ></v-divider> -->
                <div class="my-4 mx-4">
                  <div>
                    <v-row>
                      <v-col class="d-flex position-relative">
                        <v-text-field
                          v-model="picker.startDate"
                          outlined
                          dense
                          hide-details
                          readonly
                          class="w-10 mr-3"
                        ></v-text-field>
                        <v-text-field
                          v-model="date"
                          outlined
                          dense
                          hide-details
                          readonly
                          :class="[
                            'w-10',
                            isOpenDateLeft
                              ? 'v-input--is-focused primary--text'
                              : '',
                          ]"
                          :disabled="timeDisabled"
                        ></v-text-field>
                        <flat-pickr
                          v-model="picker.startTime"
                          :config="isAsset ? assetConfig : config"
                          class="flat-picker-custom-style"
                          @on-open="isOpenDateLeft = true"
                          @on-close="isOpenDateLeft = false"
                        ></flat-pickr>

                        <!-- <v-menu
                          v-if="isDayShow"
                          ref="menu1"
                          v-model="isStartTimeShow"
                          :close-on-content-click="false"
                          :nudge-right="40"
                          :return-value.sync="picker.startTime"
                          transition="scale-transition"
                          offset-y
                        >
                          <template v-slot:activator="{ on, attrs }">
                            <v-text-field
                              v-model="picker.startTime"
                              outlined
                              dense
                              hide-details
                              readonly
                              v-bind="attrs"
                              class="w-10"
                              v-on="on"
                              :disabled="timeDisabled"
                            ></v-text-field>

                          </template>

                          <v-time-picker
                            v-if="isStartTimeShow"
                            v-model="picker.startTime"
                            color="primary"
                            @click:minute="$refs.menu1.save(picker.startTime)"
                          ></v-time-picker>
                        </v-menu> -->
                      </v-col>
                      <vsoc-icon
                        icon="mdi-chevron-right"
                        class="action-btn"
                        size="24"
                      ></vsoc-icon>
                      <v-col class="d-flex position-relative">
                        <v-text-field
                          v-model="picker.endDate"
                          outlined
                          dense
                          hide-details
                          readonly
                          class="w-10 mr-3"
                        ></v-text-field>
                        <v-text-field
                          v-model="date"
                          outlined
                          dense
                          hide-details
                          readonly
                          :class="[
                            'w-10',
                            isOpenDateRight
                              ? 'v-input--is-focused primary--text'
                              : '',
                          ]"
                          :disabled="timeDisabled"
                        ></v-text-field>
                        <flat-pickr
                          v-model="picker.endTime"
                          :config="isAsset ? assetConfig : config"
                          class="flat-picker-custom-style"
                          @on-open="isOpenDateRight = true"
                          @on-close="isOpenDateRight = false"
                        ></flat-pickr>
                        <!-- <v-menu
                          v-if="isDayShow"
                          ref="menu2"
                          v-model="isEndTimeShow"
                          :close-on-content-click="false"
                          :nudge-right="40"
                          :return-value.sync="picker.endTime"
                          transition="scale-transition"
                          offset-y
                        >
                          <template v-slot:activator="{ on, attrs }">
                            <v-text-field
                              v-model="picker.endTime"
                              outlined
                              dense
                              hide-details
                              readonly
                              v-bind="attrs"
                              class="w-10"
                              v-on="on"
                              :disabled="timeDisabled"
                            ></v-text-field>
                          </template>
                          <v-time-picker
                            v-if="isEndTimeShow"
                            v-model="picker.endTime"
                            color="primary"
                            @click:minute="$refs.menu2.save(picker.endTime)"
                          ></v-time-picker>
                        </v-menu> -->
                      </v-col>
                    </v-row>
                    <!-- 日期选择框 -->
                    <v-row no-gutters class="mt-2">
                      <v-col>
                        <v-date-picker
                          v-model="picker.startDate"
                          :class="[
                            'v-date-range__picker--start',
                            'v-date-range__picker',
                            isWidthAuto ? 'width-box-auto' : '',
                          ]"
                          :locale="$i18n.locale"
                          :first-day-of-week="firstDayOfWeek"
                          :min="selectMonth ? '' : limitDate ? limitMin : min"
                          :max="
                            selectMonth
                              ? new Date().toISOString().substr(0, 7)
                              : picker.endDate || max
                          "
                          :no-title="noTitle"
                          :next-icon="nextIcon"
                          :prev-icon="prevIcon"
                          :events="highlightDates"
                          :event-color="highlightClasses"
                          :type="selectMonth ? 'month' : 'date'"
                          @change="onChangeStartDate"
                        ></v-date-picker>
                      </v-col>
                      <v-divider class="mx-1" vertical></v-divider>
                      <v-col>
                        <v-date-picker
                          v-model="picker.endDate"
                          :class="[
                            'v-date-range__picker--end',
                            'v-date-range__picker',
                            isWidthAuto ? 'width-box-auto' : '',
                          ]"
                          :locale="$i18n.locale"
                          :first-day-of-week="firstDayOfWeek"
                          :min="
                            selectMonth
                              ? picker.startDate
                              : picker.startDate || min
                          "
                          :max="
                            selectMonth
                              ? new Date().toISOString().substr(0, 7)
                              : limitDate
                              ? limitMax
                              : max
                          "
                          :no-title="noTitle"
                          :next-icon="nextIcon"
                          :prev-icon="prevIcon"
                          :events="highlightDates"
                          :event-color="highlightClasses"
                          :type="selectMonth ? 'month' : 'date'"
                          @change="onChangeEndDate"
                        ></v-date-picker>
                      </v-col>
                    </v-row>
                  </div>

                  <!-- <div class="text-align-right">
                    <v-spacer class="mt-4"></v-spacer>
                    <v-btn
                      class="mr-4"
                      depressed
                      outlined
                      @click="menu = false"
                    >
                      {{ mergedActionLabels.cancel }}
                    </v-btn>
                    <v-btn
                      depressed
                      color="primary"
                      :disabled="!bothSelected"
                      @click="applyRange"
                    >
                      {{ mergedActionLabels.apply }}
                    </v-btn>
                  </div> -->
                </div>
              </div>
            </v-card-text>
          </div>
        </v-card-text>
      </v-card>
    </v-menu>
  </div>
</template>
<script>
import { addDays, differenceInCalendarDays, format, parse } from 'date-fns'
// import { endOfDay } from 'date-fns/fp'
// import startOfDay from 'date-fns/startOfDay'
import 'flatpickr/dist/flatpickr.css' //引入他的css样式
import flatPickr from 'vue-flatpickr-component' //引入flatpickr组件
import {
  DEFAULT_ACTION_LABELS_FN,
  ENDOFDAY,
  ISO_FORMAT,
  MONTH_FORMAT,
  PRESETSFN,
  RANGE_STR,
  SECONDS_FORMAT,
  STARTOFDAY,
  TIME_FORMAT,
} from './constants'

export default {
  name: 'VsocDateRange',
  components: {
    flatPickr,
  },
  props: {
    timeDisabled: {
      type: Boolean,
      default: false,
    },
    // Take start and end as the input. Passable via v-model.
    value: {
      type: Object,
      default: () => ({
        start: STARTOFDAY,
        end: ENDOFDAY,
      }),
    },
    isWidthAuto: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    // 快捷范围函数
    presetFn: {
      type: Function,
      default: () => PRESETSFN(new Date()),
    },

    // // 快捷范围选项
    // presets: {
    //   type: Array,
    //   default: () => PRESETSFN(new Date()),
    // },
    noPresets: {
      type: Boolean,
      default: false,
    },

    // Denotes the Placeholder string for start date.
    startLabel: {
      type: String,
      default: 'Start Date',
    },

    // Denotes the Placeholder string for start date.
    endLabel: {
      type: String,
      default: 'End Date',
    },

    // The string that gets placed between `startLabel` and `endLabel`
    separatorLabel: {
      type: String,
      default: 'To',
    },
    presetLabel: {
      type: String,
      // default: '快捷选项',
    },
    actionLabels: {
      type: Object,
      // default: () => DEFAULT_ACTION_LABELS_FN,
    },
    inputLabel: {
      type: String,
      default: '日期范围',
    },

    /**
     * Following values are all passable to vuetify's own datepicker
     * component.
     */
    // Min selectable date.
    min: {
      type: String,
      default: undefined,
    },

    // Max selectable date
    max: {
      type: String,
      default: undefined,
    },

    // 无效，locale 随系统变化
    // locale: {
    //   type: String,
    //   default: 'zh-us',
    // },
    firstDayOfWeek: {
      type: [String, Number],
      default: 0,
    },
    noTitle: {
      type: Boolean,
      default: true,
    },
    displayFormat: {
      type: String,
    },
    highlightColor: {
      type: String,
      default: 'blue lighten-5',
    },

    /**
     * Icons
     */
    nextIcon: {
      type: String,
      default: '$vuetify.icons.next',
    },
    prevIcon: {
      type: String,
      default: '$vuetify.icons.prev',
    },
    inputProps: {
      type: Object,
      default: () => ({}),
    },
    menuProps: {
      type: Object,
      default: () => ({}),
    },

    // 是否选择时分秒
    isDayShow: {
      type: Boolean,
      default: true,
    },

    // 只显示年月
    selectMonth: {
      type: Boolean,
      default: false,
    },
    isAsset: {
      type: Boolean,
      default: false,
    },
    //是否限制日期
    limitDate: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      isOpenDateLeft: false,
      isOpenDateRight: false,
      date: null,
      config: {
        enableTime: true,
        noCalendar: true,
        dateFormat: 'H:i',
        wrap: false,
        static: true,
        time_24hr: true,
        minuteIncrement: 1,
      },
      assetConfig: {
        enableTime: true,
        noCalendar: true,
        dateFormat: 'H:i:S',
        wrap: false,
        static: true,
        time_24hr: true,
        minuteIncrement: 1,
      },
      isPresetActive: -1,
      count: 1,
      menu: false,
      menu2: false,
      isStartTimeShow: false,
      isEndTimeShow: false,
      limitMax: '',
      limitMin: '',
      picker: {
        startDate: this.value.start || '',
        endDate: this.value.end || '',
        startTime: '',
        endTime: '',
      },

      // pickerStart: this.value.start,
      // pickerEnd: this.value.end,
      pickerStartTime: '',
      highlightDates: [],
      highlightClasses: {},
    }
  },
  computed: {
    // presets: {
    //   get() {
    //     return this.presetFn(new Date())
    //   },
    //   set(value) {
    //     return value
    //   },
    // },

    presets() {
      return this.count && this.presetFn(new Date())
    },
    inputValue() {
      if (this.isValueEmpty) {
        return ''
      }
      const start = this.displayFormat
        ? this.formatDate(this.value.start, this.displayFormat)
        : this.value.start
      const end = this.displayFormat
        ? this.formatDate(this.value.end, this.displayFormat)
        : this.value.end

      return RANGE_STR(start, end)
    },
    placeholder() {
      return `${this.startLabel}    ${this.separatorLabel}    ${this.endLabel}`
    },

    /**
     * If the value prop doesn't have any start value,
     * its most likely that an empty object was passed.
     */
    isValueEmpty() {
      return !this.value.start || !this.picker.startDate
    },

    /**
     * If the user has selected both the dates or not
     */
    bothSelected() {
      return this.picker.startDate && this.picker.endDate
    },
    // isPresetActive() {
    //   return (
    //     this.presets.map(
    //       preset =>
    //         preset.range[0] === this.picker.startDate &&
    //         preset.range[1] === this.picker.endDate,
    //     ) || []
    //   )
    // },
    mergedActionLabels() {
      return { ...DEFAULT_ACTION_LABELS_FN(), ...this.actionLabels }
    },
  },
  watch: {
    // Watching to see if the menu is closed.
    menu(isOpen) {
      if (!isOpen) {
        this.closeMenu()
      } else {
        this.highlight()
      }
    },
    'picker.startDate': 'highlight',
    'picker.endDate': 'highlight',
    value: {
      handler(val) {
        if (JSON.stringify(val) === '{}') {
          return
        }
        this.formatRange()
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    changeDate() {
      // console.log(1)
      // this.isOpenDate = true
    },
    formatRange() {
      if (this.value.start == '' || this.value.end == '') {
        this.picker.startDate = ''
        this.picker.startTime = ''
        this.picker.endDate = ''
        this.picker.endTime = ''

        return
      }
      const isMonth = this.selectMonth ? MONTH_FORMAT : ISO_FORMAT
      this.picker.startDate = this.value.start
        ? format(new Date(this.value.start), isMonth)
        : ''

      this.picker.startTime = this.value.start
        ? format(
            new Date(this.value.start),
            this.isAsset ? SECONDS_FORMAT : TIME_FORMAT,
          )
        : ''
      this.picker.endDate = this.value.end
        ? format(new Date(this.value.end), isMonth)
        : ''

      this.picker.endTime = this.value.end
        ? format(
            new Date(this.value.end),
            this.isAsset ? SECONDS_FORMAT : TIME_FORMAT,
          )
        : ''
    },
    onChangeStartDate(string) {
      // this.picker.startTime = format(new Date(string), TIME_FORMAT)
      this.picker.startTime = '00:00'
      this.limitMax = addDays(new Date(string), this.limitDate * 1)
        .toISOString()
        .substr(0, 10)
    },
    onChangeEndDate(string) {
      this.picker.endTime = '23:59'
      this.limitMin = addDays(new Date(string), -(this.limitDate * 1))
        .toISOString()
        .substr(0, 10)

      // this.picker.endTime = format(new Date(string), TIME_FORMAT)
      // if (this.picker.startDate === this.picker.endDate && this.picker.startTime === this.picker.endTime) {
      //   // 同一天且同一个时间
      //   this.picker.startTime = format(startOfDay(new Date(string)), TIME_FORMAT)
      //   this.picker.endTime = format(endOfDay(new Date(string)), TIME_FORMAT)
      // }
    },

    /**
     * Emit the input event with the updated range data.
     * This makes v-model work.
     */
    applyRange() {
      this.menu = false
      this.emitRange()
      this.$nextTick(() => {
        this.$emit('search')
      })
    },

    /**
     * Called every time the menu is closed.
     * It also emits an event to tell the parent
     * that the menu has closed.
     *
     * Upon closing the datepicker values are set
     * to the current selected value.
     */
    closeMenu() {
      // Reset the changed values for datepicker models.
      // this.picker.startDate = this.value.start
      // this.picker.endDate = this.value.end
      // this.picker.startTime = this.value.startTime
      // this.picker.endTime = this.value.endTime
      this.formatRange()
      this.highlight()
      this.$emit('menu-closed')
    },
    formatDate(date, fmt) {
      const isMonth = this.selectMonth ? MONTH_FORMAT : ISO_FORMAT

      return format(parse(date, isMonth, new Date()), fmt)
    },
    highlight() {
      if (!this.bothSelected) {
        return
      }
      const dates = []
      const classes = {}
      const isMonth = this.selectMonth ? MONTH_FORMAT : ISO_FORMAT
      const start = parse(this.picker.startDate, isMonth, new Date())
      const end = parse(this.picker.endDate, isMonth, new Date())
      const diff = Math.abs(differenceInCalendarDays(start, end))

      // Loop though all the days in range.
      for (let i = 0; i <= diff; i++) {
        const date = format(addDays(start, i), isMonth)
        dates.push(date)
        const classesArr = []
        classesArr.push('v-date-range__in-range')
        classesArr.push(this.highlightColor)
        i === 0 && classesArr.push('v-date-range__range-start')
        i === diff && classesArr.push('v-date-range__range-end')
        classes[date] = classesArr.join(' ')
      }
      this.highlightDates = dates
      this.highlightClasses = classes
    },
    selectPreset(presetIndex) {
      ++this.count

      // this.picker.startDate = this.presets[presetIndex].range[0]
      // this.picker.endDate = this.presets[presetIndex].range[1]
      const isMonth = this.selectMonth ? MONTH_FORMAT : ISO_FORMAT

      this.picker.startDate = format(
        this.presets[presetIndex].range[0],
        isMonth,
      )
      this.picker.startTime = format(
        this.presets[presetIndex].range[0],
        this.isAsset ? SECONDS_FORMAT : TIME_FORMAT,
      )

      this.picker.endDate = format(this.presets[presetIndex].range[1], isMonth)
      this.picker.endTime = format(
        this.presets[presetIndex].range[1],
        this.isAsset ? SECONDS_FORMAT : TIME_FORMAT,
      )
    },
    reset() {
      // Reset Picker Values
      // this.picker.startDate = ''
      // this.picker.endDate = ''
      this.isPresetActive = -1
      this.$emit('input', { start: '', end: '' })
      this.menu = false
      // this.picker = Object.assign(
      //   this.$data.picker,
      //   this.$options.data.call(this).picker,
      // )

      this.highlightDates = []
      this.highlightClasses = {}
      // this.emitRange()
    },
    emitRange() {
      if (this.isAsset) {
        this.$emit('input', {
          start: [this.picker.startDate, `${this.picker.startTime}`]
            .join(' ')
            .trim(),
          end: [this.picker.endDate, `${this.picker.endTime}`].join(' ').trim(),
        })
      } else if (this.isDayShow) {
        this.$emit('input', {
          start: [this.picker.startDate, `${this.picker.startTime}:00`]
            .join(' ')
            .trim(),
          end: [this.picker.endDate, `${this.picker.endTime}:59`]
            .join(' ')
            .trim(),
        })
      } else {
        this.$emit('input', {
          start: this.picker.startDate,
          end: this.picker.endDate,
        })
      }
    },
  },
}
</script>
<style scoped lang="scss">
.v-card__text {
  padding: 1rem 1rem 1rem 0rem;
}
.w-10 {
  width: 9.5rem !important;
}
.w-10 ::v-deep .v-input__slot {
  min-height: 2.1875rem !important;
  // line-height: 2.1875rem !important;
}
.v-date-range__pickers ::v-deep .v-card__text {
  padding: 0;
}
.v-date-range__pickers ::v-deep .width-box-auto .v-picker__body {
  width: 100% !important;
}
// ::v-deep .v-text-field.v-text-field--enclosed {
//   width: 9.5rem;
// }
::v-deep .v-date-picker-header__value button {
  font-weight: 700;
}
::v-deep .v-date-picker-table {
  padding: 0;
  height: auto;
}
::v-deep .v-card__actions {
  padding-right: 1rem;
  padding-bottom: 1rem;
}
.v-date-range__input-field >>> input {
  text-align: center;
}
::v-deep .v-text-field > .v-input__control > .v-input__slot {
  cursor: pointer !important;
}

/* =============================================
=            Menu Content            =
============================================= */
.v-date-range__content {
  display: flex;
  flex-direction: column;

  >>> .v-date-picker-table {
    height: auto;
    padding: 0;
    .v-btn {
      border-radius: 0;
    }
  }
}

/* =====  End of Menu Content  ====== */
.v-date-range__pickers >>> .v-date-picker-table__events {
  height: 100%;
  width: 100%;
  top: 0;
  z-index: -1;
}

/* =============================================
=            Date buttons            =
============================================= */
.v-date-range__pickers >>> .v-date-picker-table table {
  width: auto;
  margin: auto;
  border-collapse: collapse;

  & th,
  & td {
    height: 32px;
    width: 32px;
  }

  & td {
    .v-btn {
      &.v-btn--outline {
        border: none;
        box-shadow: 0 0 0 1px currentColor inset;
      }

      &.v-btn--active::before {
        background-color: transparent !important;
      }
    }
  }
}

/* =====  End of Date buttons  ====== */
/* =============================================
=            Highlighting the even bubble dot            =
============================================= */
.v-date-range__pickers >>> .v-date-range__in-range {
  height: 100%;
  width: 100%;
  margin: 0;
  border-radius: 0;
}

/* =====  End of Highlighting the even bubble dot  ====== */

.v-list-item-group .v-list-item--active {
  border-left: 0.125rem solid var(--v-primary-base);
  color: var(--v-primary-base) !important;
}
.theme--light.v-list-item--active:hover::before,
.theme--light.v-list-item--active::before {
  opacity: 0;
}

::v-deep .v-tab,
.v-application.theme--light .v-tabs:not(.v-tabs--vertical) {
  box-shadow: none !important;
  letter-spacing: -0.5px;
}
</style>
