import { request, vsocPath } from '../../util/request'

// 获取未读消息条数
export const getUnreadStatistics = function () {
  return request({
    url: `${vsocPath}/message/unreadStatistics`,
    method: 'post',
  })
}

// 获取消息中心列表
export const getList = function (data) {
  return request({
    url: `${vsocPath}/message/messageCenter`,
    method: 'post',
    data,
  })
}

// 获取我的消息中心
export const getMyMessagesList = function (data) {
  return request({
    url: `${vsocPath}/message/myMessages`,
    method: 'post',
    data,
  })
}

// 发布公告或通知
export const sendMessage = function (data) {
  return request({
    url: `${vsocPath}/message/publish`,
    method: 'post',
    data,
  })
}

// 消息已读
export const handleRead = function (data) {
  return request({
    url: `${vsocPath}/message/read`,
    method: 'post',
    data,
  })
}

// 消息阅读详情
export const readDetail = function (data) {
  return request({
    url: `${vsocPath}/message/readDetail`,
    method: 'post',
    data,
  })
}
