import { request, vsocPath } from '../../util/request'

//查询token列表
export const findTokenList = function (params) {
  return request({
    url: `${vsocPath}/access/accessTokens`,
    method: 'post',
    data: params,
  })
}

// 新增token
export const addToken = function (params) {
  return request({
    url: `${vsocPath}/access/addAccessToken`,
    method: 'post',
    data: params,
  })
}

// 获取token信息
export const findTokenDetail = function (params) {
  return request({
    url: `${vsocPath}/access/accessTokenDetail`,
    method: 'post',
    data: params,
  })
}

// token【修改】接口
export const updateToken = function (params) {
  return request({
    url: `${vsocPath}/access/updateAccessToken`,
    method: 'post',
    data: params,
  })
}

// 撤销token
export const revokeAccessToken = function (params) {
  return request({
    url: `${vsocPath}/access/revokeAccessToken`,
    method: 'post',
    data: params,
  })
}

//API列表
export const getApiList = function (params) {
  return request({
    url: `${vsocPath}/apiInfo/apiList`,
    method: 'post',
    data: params,
  })
}

//已授权API
export const getSelectApis = function (params) {
  return request({
    url: `${vsocPath}/apiInfo/findBindApis`,
    method: 'post',
    data: params,
  })
}

//绑定授权API
export const bindApis = function (params) {
  return request({
    url: `${vsocPath}/apiInfo/bindApis`,
    method: 'post',
    data: params,
  })
}
