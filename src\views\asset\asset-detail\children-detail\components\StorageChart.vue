<template>
  <v-card class="h-full d-flex flex-column">
    <v-card-text>
      <v-tabs
        height="3.5rem"
        class="rounded-xxl bg-body asset-children text-uppercase"
        active-class="rounded-xxl ma-1 white--text primary"
        centered
        grow
        hide-slider
        v-model="currentTab"
      >
        <v-tab v-for="tab in tabOption" :key="tab.text">{{ tab.text }}</v-tab>
      </v-tabs>
    </v-card-text>
    <v-card-text class="flex-1">
      <vsoc-chart
        echart-id="cpu"
        :option="gradientChart"
        :is-y-dashed="true"
      ></vsoc-chart>
    </v-card-text>
  </v-card>
</template>

<script>
import VsocChart from '@/components/VsocChart.vue'
import { startOfDay, subHours } from 'date-fns'
import {
  grid,
  legend,
  linearGradient,
  tooltip,
} from '../../../../../assets/echarts-theme/constant'
let xData = []
for (let i = 8; i > 0; i--) {
  // i = i - 1
  let time = subHours(startOfDay(new Date()), i)
  xData.push(time)
}
// 类型待定：百分比/数值
const yData = [
  {
    name: '最小值',
    // 不可为string，如'10%'，否则会出现不了图形
    data: [100, 120, 90, 390, 130, 160, 280, 240, 220, 180, 270, 280, 375],
  },
  {
    name: '平均值',
    data: [60, 80, 360, 110, 80, 100, 90, 180, 160, 140, 200, 220, 275, 100],
  },
  {
    name: '最大值',
    data: [20, 40, 30, 70, 380, 60, 50, 140, 120, 100, 140, 180, 220],
  },
]
export default {
  components: {
    VsocChart,
  },
  data() {
    return {
      currentTab: 0,
    }
  },
  computed: {
    gradientChart() {
      const colorList = ['#298CFF', '#533DF1', '#44E2FE']

      return {
        color: colorList,
        series: yData.map((item, index) => {
          // return { ...seriesCommon, ...item }
          return linearGradient.seriesCommonFn(
            item.name,
            item.data,
            colorList[index],
          )
        }),
        tooltip,
        legend,
        grid: {
          ...grid,
          right: '3%',
        },
        xAxis: linearGradient.xAxisFn(xData),
        yAxis: [
          {
            type: 'value',
          },
        ],
      }
    },
    tabOption() {
      return [
        {
          // 当前时间点的百分比
          text: 'cpu(15%)',
        },
        {
          text: 'ram(33%)',
        },
        {
          text: 'rom(9%)',
        },
      ]
    },
  },
  methods: {},
}
</script>
<style scoped lang="scss"></style>
