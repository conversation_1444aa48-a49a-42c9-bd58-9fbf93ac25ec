.v-data-table {
  .v-data-table__wrapper tbody tr.v-data-table__expanded__content {
    box-shadow: 0 -0.0625rem 0 0 $gray-200 inset !important;
    background-color: transparent !important;

    &:hover {
      box-shadow: 0 -0.0625rem 16px 0 $gray-200 inset !important;
    }
  }

  .v-data-table__wrapper > table > tbody > tr > th,
  .v-data-table__wrapper > table > thead > tr > th,
  .v-data-table__wrapper > table > tfoot > tr > th {
    font-weight: $font-weight-normal;
    // font-size: $font-size-sm !important;
    // box-shadow: inset 0px -1px 0px $color-dividers--light;
    font-size: $font-size-base !important;
    line-height: 1.5rem;
    border-top: 0 !important;
  }

  .v-data-table__wrapper > table > tbody > tr > td,
  .v-data-table__wrapper > table > thead > tr > td,
  .v-data-table__wrapper > table > tfoot > tr > td,
  .v-data-table__wrapper > table > tbody > tr > th,
  .v-data-table__wrapper > table > thead > tr > th,
  .v-data-table__wrapper > table > tfoot > tr > th {
    height: 3.23rem !important;
    font-weight: $font-weight-normal;
    font-size: $font-size-base !important;
    line-height: 1.5rem;
  }

  .v-simple-checkbox {
    .v-input--selection-controls__input {
      height: 16px !important;
      width: 16px !important;
    }
    .v-input--selection-controls__ripple {
      height: 26px !important;
      width: 26px !important;
      top: calc(25% - 16px);
    }
    top: calc(25% - 6px);
  }
}

.main-content {
  padding: 16px !important;
  padding-bottom: 0 !important;
  box-shadow: none !important;
}

th,
td {
  text-wrap: nowrap;
}
