<template>
  <div>
    <bread-crumb
      ref="topHeader"
      :showDoQuery="showDoQuery"
      :filterList="filterList"
      :showAdvance="true"
    >
    </bread-crumb>
    <!-- <div
      ref="filterList"
      class="rounded-0 d-flex align-center justify-lg-space-between py-1 px-2"
      :class="{ 'pb-2': filterList.length === 0 }"
    >
      <v-chip-group v-if="filterList.length" class="w-90 pa-0 ma-0">
        <v-chip
          v-for="(item, index) in filterList"
          :key="index"
          small
          pill
          dense
          class="pa-0 active-chip rounded-pill color-base bg-white px-4"
          close
          close-icon="mdi-close"
          @click:close="$_clearFilter(item, index)"
        >
          {{ item.text }}
        </v-chip>
      </v-chip-group>
      <v-btn
        v-if="filterList.length"
        color="primary"
        x-small
        rounded
        class="px-4 py-1 rounded-pill text-base"
        @click="onReset"
      >
        <v-icon left> mdi-eraser </v-icon>
        {{ $t('action.clear') }}
      </v-btn>
    </div> -->

    <v-card tile class="main-content">
      <v-card-text class="pa-0">
        <div class="d-flex justify-space-between align-center flex-row-reverse">
          <div class="d-flex align-end">
            <table-search
              :searchList="searchList"
              :searchQuery="query"
              @search="$_search"
            ></table-search>
            <!-- <vsoc-date-range
              v-model="dateRange.range"
              no-title
              @input="onChangeDate"
            >
              <template v-slot:text="{ on, attrs }">
                <v-text-field
                  type="button"
                  clearable
                  outlined
                  dense
                  class="append-icon-max me-3 date-width"
                  readonly
                  hide-details
                  color="primary"
                  large
                  :label="$t('operateLog.headers.operDate')"
                  prepend-inner-icon="mdi-calendar-range-outline"
                  :value="RANGE_STR(query.startDate, query.endDate)"
                  @click:clear="onChangeDate({ start: '', end: '' })"
                ></v-text-field>
              </template>
            </vsoc-date-range>

            <v-autocomplete
              v-model="query.moduleNames"
              :items="menuList"
              multiple
              outlined
              dense
              hide-details
              class="me-3 text-width"
              :menu-props="{ offsetY: true }"
              :label="$t('operateLog.headers.module')"
              ><template v-slot:selection="{ index }">
                <span v-if="index === 0">
                  {{ $t('global.pagination.selected') }}：{{
                    query.moduleNames.length
                  }}
                </span>
              </template>
            </v-autocomplete>

            <v-btn color="primary--text bg-btn" elevation="0" @click="$_search">
              <span>{{ $t('action.search') }}</span>
            </v-btn> -->
          </div>
          <div class="d-flex justify-end align-center">
            <v-btn
              elevation="0"
              color="primary"
              @click="showExport = true"
              v-has:oper-log-export
            >
              <span>
                {{
                  $generateMenuTitle($route.meta.buttonInfo['oper-log-export'])
                }}
              </span>
            </v-btn>
          </div>
        </div>
        <v-data-table
          fixed-header
          :items-per-page="query.pageSize"
          item-key="id"
          :height="tableHeight"
          hide-default-footer
          :headers="headers"
          :items="tableData"
          class="table border-radius-xl mt-3 thead-light"
          :loading="tableLoading"
          @click:row="edit"
        >
          <template v-slot:item.moduleName="{ item }">
            <v-chip small label text-color="primary">
              {{ item.moduleName | dataFilter }}
            </v-chip>
          </template>
          <template v-slot:item.operationType="{ item }">
            <v-chip small label v-if="operateTypeEnum[item.operationType]">
              <!-- <v-icon size="1.25rem" class="mr-1">{{
                operateTypeEnum[item.operationType].icon
              }}</v-icon> -->
              <span>{{ operateTypeEnum[item.operationType].text }}</span>
            </v-chip>
          </template>
          <template v-slot:item.operAddress="{ item }">
            <div style="max-width: 16rem" v-show-tips>
              {{ item.operAddress | dataFilter }}
            </div>
          </template>

          <template v-slot:item.operDetail="{ item }">
            <a v-show-tips>{{ $t('action.detail') }}</a>
          </template>

          <template v-slot:item.operDate="{ item }">
            <span v-show-tips>{{ item.operDate | toDate }}</span>
          </template>
          <template v-slot:item.status="{ item }">
            <!-- <v-chip
              v-if="statusEnum[item.status]"
              v-show-tips="item.statusName"
              small
              :color="statusEnum[item.status].color"
              label
            >
              {{ item.statusName }}
            </v-chip> -->
            <v-icon
              v-if="statusEnum[item.status]"
              v-show-tips="item.statusName"
              size="1.5rem"
              :color="statusEnum[item.status].color"
            >
              {{ statusEnum[item.status].icon }}
            </v-icon>
          </template>
          <template v-slot:item.actions="{ item }">
            <v-btn
              v-show-tips="$t('action.detail')"
              icon
              @click.stop="onDetail(item)"
            >
              <!-- <v-icon size="1.25rem"> mdi-file-eye </v-icon> -->
              <vsoc-icon
                type="fill"
                size="x-large"
                class="action-btn"
                icon="icon-xiangqing"
              ></vsoc-icon>
            </v-btn>
            <!-- <v-btn icon @click.stop="edit(item)">
              <v-icon size="1.25rem"> mdi-pencil </v-icon>
            </v-btn>

            <v-btn :loading="item.btnLoading" icon @click.stop="del(item)">
              <v-icon size="1.25rem"> mdi-delete </v-icon>
            </v-btn> -->
          </template>
        </v-data-table>

        <vsoc-pagination
          :page.sync="query.pageNum"
          :size.sync="query.pageSize"
          :total="tableDataTotal"
          @change-page="getTableData"
          @change-size="getTableData"
        >
        </vsoc-pagination>
      </v-card-text>
    </v-card>
    <operate-drawer
      ref="advancedDrawer"
      v-model="showAdvance"
      :menuList="menuList"
      @do-query="doQuery"
    ></operate-drawer>
    <vsoc-drawer
      v-model="showDetail"
      :hideFooter="true"
      width="30%"
      :title="$t('operateLog.detailTitle')"
    >
      <!-- <template v-slot:title>
        <div class="font-weight-semibold text-h5 w-50 pb-0">
          <span>操作日志详情</span>
        </div>
        <v-btn size="1.5rem" icon @click="showDetail = false">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </template> -->

      <v-row class="pt-4 color-base">
        <v-col cols="12">
          <span class="font-weight-bold mr-4"
            >{{ $t('operateLog.headers.module') }}:</span
          >
          <v-chip small color="primary" label>
            {{ currentRecord.moduleName | dataFilter }}
          </v-chip>
        </v-col>

        <v-col cols="12">
          <span class="font-weight-bold mr-4"
            >{{ $t('operateLog.headers.address') }}:</span
          >
          <span>{{ currentRecord.operUrl }}</span>
        </v-col>

        <v-col cols="12">
          <span class="font-weight-bold mr-4"
            >{{ $t('operateLog.loginMessage') }}:</span
          >
          <span>
            {{ currentRecord.operator }} / {{ currentRecord.operIp }} /
            {{ currentRecord.operAddress }}
          </span>
        </v-col>
        <v-col cols="12" class="d-flex align-center">
          <span class="font-weight-bold mr-4 text-no-wrap"
            >{{ $t('operateLog.headers.method') }}:</span
          >

          <span
            v-show-tips="currentRecord.method"
            class="d-inline-block w-75"
            style="vertical-align: text-bottom"
          >
            {{ currentRecord.method }}
          </span>
          <!-- <v-btn icon
            ><vsoc-icon icon="icon-fuzhi" v-copy="currentRecord.method"
          /></v-btn> -->
        </v-col>
        <!-- <v-col cols="12">
          <span class=" font-weight-bold mr-4">请求参数:</span>
          <span>{{ currentRecord.operParam }}</span>
        </v-col> -->
        <v-col cols="12">
          <span class="font-weight-bold mr-4"
            >{{ $t('operateLog.headers.operDate') }}:</span
          >
          <span v-show-tips>{{ currentRecord.operDate | toDate }}</span>
        </v-col>
        <v-col cols="12">
          <span class="font-weight-bold mr-4"
            >{{ $t('operateLog.headers.operType') }}:</span
          >
          <span>{{ currentRecord.requestMethod }}</span>
        </v-col>
        <v-col cols="6">
          <span class="font-weight-bold mr-4"
            >{{ $t('operateLog.headers.status') }}:</span
          >
          <span v-if="statusEnum[currentRecord.status]">
            <v-icon
              size="1.25rem"
              class="mr-2"
              :color="statusEnum[currentRecord.status].color"
            >
              {{ statusEnum[currentRecord.status].icon }}
            </v-icon>
            <span class="text-sm">{{ currentRecord.statusName }}</span>
          </span>
        </v-col>

        <v-col cols="12" class="pa-0 pl-3">
          <v-tabs v-model="currentTabs" height="40">
            <v-tab class="text-base font-weight-bold">{{
              $t('operateLog.response')
            }}</v-tab>
            <v-tab class="text-base font-weight-bold">{{
              $t('operateLog.request')
            }}</v-tab>
          </v-tabs>

          <div
            class="position-relative"
            v-if="
              currentRecord.resultParamParse &&
              currentRecord.operParamParse &&
              showDetail
            "
          >
            <vsoc-code-mirror
              :code="
                currentTabs === 0
                  ? currentRecord.resultParamParse
                  : currentRecord.operParamParse
              "
              :readOnly="true"
            ></vsoc-code-mirror>
            <v-btn
              icon
              v-show-tips="$t('action.copy')"
              class="position-absolute"
              ><vsoc-icon
                type="fill"
                class="action-btn"
                icon="icon-fuzhi"
                v-copy="
                  currentTabs === 0
                    ? currentRecord.resultParam
                    : currentRecord.operParam
                "
            /></v-btn>
          </div>

          <!-- <div>{{ currentRecord.resultParam }}</div> -->
          <!-- <div class="wrapper">
            <input id="exp1" class="exp" type="checkbox" />
            <div class="text">
              <label class="btn" for="exp1"></label>
              <span class=" font-weight-bold mr-4">返回参数:</span
              >{{ currentRecord.resultParam }}
            </div>
          </div> -->
        </v-col>
      </v-row>
    </vsoc-drawer>

    <vsoc-dialog
      v-model="showExport"
      dense
      width="400"
      :title="$generateMenuTitle($route.meta.buttonInfo['oper-log-export'])"
      @click:confirm="onExport"
    >
      <v-form ref="form" v-model="valid">
        <vsoc-date-range
          ref="refRange"
          v-model="dateRange1.range"
          no-title
          :menu-props="dateRange1.menuProps"
          @input="onChangeDate1"
        >
          <template v-slot:text="{ on, attrs }">
            <v-text-field
              clearable
              style="width: 370px"
              outlined
              dense
              class="me-3 pa-0"
              readonly
              color="primary"
              large
              :label="$t('loginLog.dateRange')"
              append-icon="mdi-calendar-range-outline"
              v-bind="attrs"
              v-on="on"
              persistent-hint
              :hint="$t('global.hint.maxExportCount')"
              :value="RANGE_STR(exportQuery.startDate, exportQuery.endDate)"
              @click:clear="onChangeDate1({ start: '', end: '' })"
              :rules="[v => required(v, $t('loginLog.dateRange'))]"
            ></v-text-field>
          </template>
        </vsoc-date-range>
      </v-form>
    </vsoc-dialog>
  </div>
</template>

<script>
import { required } from '@/@core/utils/validation'
import {
  exportOperateLog,
  getOperateLog,
  getOperateLogDetail,
} from '@/api/log/operate'
import VsocCodeMirror from '@/components/VsocCodeMirror.vue'
import VsocDialog from '@/components/VsocDialog.vue'
import VsocPagination from '@/components/VsocPagination.vue'
import breadCrumb from '@/components/bread-crumb/index'
import VsocDateRange from '@/components/vsoc-date-range/VsocDateRange.vue'
import { RANGE_STR } from '@/components/vsoc-date-range/constants'
import { differenceInDays } from 'date-fns'

import VsocDrawer from '@/components/VsocDrawer.vue'
import { PAGESIZE } from '@/util/constant'
import { clearFilterItem, setRemainingHeight } from '@/util/utils'
import OperateDrawer from '@/views/log/operate-log/OperateDrawer'
import { cloneDeep } from 'lodash'
import TableSearch from '@/components/TableSearch/index.vue'

export default {
  name: 'OperateLog',
  components: {
    VsocPagination,
    VsocDateRange,
    OperateDrawer,
    VsocDialog,
    breadCrumb,
    VsocCodeMirror,
    VsocDrawer,
    TableSearch,
  },
  data() {
    return {
      menuList: [],
      required,
      currentTabs: 0,
      exportQuery: {
        startDate: '',
        endDate: '',
      },
      dateRange1: {
        range: {},
        menuProps: { offsetY: true, closeOnContentClick: false },
      },
      showExport: false,
      showDetail: false,
      currentRecord: {},
      filterList: [],
      dateRange: {
        range: {},
      },
      showAdvance: false,
      query: {
        startDate: null,
        endDate: null,
        moduleNames: [],
        operationType: '',
        status: '',
        operator: '',
        pageNum: 1,
        pageSize: PAGESIZE,
      },
      editType: 'add',

      tableData: [],
      tableHeight: '34.5rem',
      tableLoading: false,
      btnLoading: false,
      tableDataTotal: 0,
      valid: false,
      nameIsRepeat: false,
    }
  },
  created() {
    this.$_setTableHeight()
    this.$bus.$on('resize', this.$_setTableHeight)
    this.getTableData()
    this.loadAuditConfigList()
  },
  beforeDestroy() {
    this.$bus.$off('resize', this.$_setTableHeight)
  },
  computed: {
    searchList() {
      return [
        {
          type: 'date',
          value: ['startDate', 'endDate'],
          text: this.$t('operateLog.headers.operDate'),
          dateRange: {
            range: {
              start: '',
              end: '',
            },
            menuProps: { offsetY: true, closeOnContentClick: false },
          },
        },
        {
          type: 'autocomplete',
          value: 'moduleNames',
          text: this.$t('operateLog.headers.module'),
          itemList: this.menuList,
        },
      ]
    },
    headers() {
      return [
        {
          text: this.$t('operateLog.headers.id'),
          value: 'id',
          width: 100,
        },
        {
          text: this.$t('operateLog.headers.module'),
          value: 'moduleName',
          width: 100,
        },
        {
          text: this.$t('operateLog.headers.type'),
          value: 'operationType',
          width: 100,
        },
        {
          text: this.$t('operateLog.headers.operType'),
          value: 'requestMethod',
          width: 100,
        },
        {
          text: this.$t('operateLog.headers.operator'),
          value: 'operator',
          width: 130,
        },
        {
          text: this.$t('operateLog.headers.address'),
          value: 'operIp',
          width: 160,
        },
        {
          text: this.$t('operateLog.headers.location'),
          value: 'operAddress',
          width: 100,
        },
        {
          text: this.$t('operateLog.headers.status'),
          value: 'status',
          width: 100,
        },
        {
          text: this.$t('operateLog.headers.operDate'),
          value: 'operDate',
          width: 160,
        },
        {
          text: '',
          value: 'actions',
          width: 100,
          sortable: false,
        },
      ]
    },
    statusEnum() {
      return this.$store.getters['enums/getOperateStatus']
    },
    operateTypeEnum() {
      return this.$store.state.enums.enums.OperateTypes
    },
    // 资产类型
    assetTypeEnum() {
      return this.$store.state.enums.enums.AssetType
    },
  },
  watch: {
    filterList() {
      this.$_setTableHeight()
    },
    '$store.state.enums.enums': {
      handler() {
        this.$_appendFilterListItem()
      },
      deep: true,
    },
  },
  methods: {
    // 获取系统模块数据
    async loadAuditConfigList() {
      this.menuList = await this.$store.dispatch('global/loadAuditConfigList')
      this.menuList.unshift({ text: 'N/A', value: '-1' })
    },
    //全部导出
    async onExport(callBack) {
      if (!this.$refs.form.validate()) return callBack(false, true)
      const days = differenceInDays(
        new Date(this.exportQuery.endDate),
        new Date(this.exportQuery.startDate),
      )
      if (days > 31) {
        this.$notify.info('error', this.$t('global.hint.monthMore1'))
        return callBack(false, true)
      }
      try {
        const file = await exportOperateLog(this.exportQuery)
        const xlsx = 'application/vnd.ms-excel;charset=utf-8'
        const blob = new Blob([file], { type: xlsx })
        //转换数据类型
        const a = document.createElement('a') // 转换完成，创建一个a标签用于下载
        a.download = `${this.$t('operateLog.currentTitle')}(${
          this.exportQuery.startDate
        }~${this.exportQuery.endDate}).xlsx`
        a.href = window.URL.createObjectURL(blob)
        a.click()
        a.remove()
        callBack()
      } catch (err) {
        this.$notify.info('error', err)
        callBack(false, true)
      }

      // const params = {
      //   startDate: this.exportQuery.startDate,
      //   endDate: this.exportQuery.endDate,
      //   pageShowType: this.query.pageShowType,
      // }
      // exportBatch(params).then(res => {

      // })
    },
    // 导出时间改变
    onChangeDate1(range) {
      this.dateRange1.range = range
      this.exportQuery.startDate = range.start
      this.exportQuery.endDate = range.end
    },
    onReset() {
      Object.assign(this.$data.query, this.$options.data.call(this).query)
      this.$_search()
    },
    async onDetail(record) {
      const { data } = await getOperateLogDetail({ id: record.id })
      this.showDetail = true
      data.resultParamParse = data.resultParam
        ? JSON.parse(data.resultParam)
        : {}
      data.operParamParse = data.operParam ? JSON.parse(data.operParam) : {}
      this.currentRecord = data
    },
    RANGE_STR,
    // 首次登记时间改变
    onChangeDate(range) {
      this.dateRange.range = range
      this.query.startDate = range.start
      this.query.endDate = range.end
    },
    // 展示高级查询
    showDoQuery() {
      this.showAdvance = true

      let advanceQuery = cloneDeep(this.query)

      delete advanceQuery.pageNum
      delete advanceQuery.pageSize
      this.$refs.advancedDrawer.advanceQuery = advanceQuery
    },
    //  高级查询
    doQuery(advanceQuery) {
      this.query = Object.assign(this.query, cloneDeep(advanceQuery))
      this.dateRange.range = {
        start: this.query.startDate,
        end: this.query.endDate,
      }
      this.$_search()
    },
    async getTableData() {
      try {
        this.tableLoading = true
        this.tableData = []
        const { data } = await getOperateLog(this.query)
        this.tableData = data.records
        // this.tableData = await this.getLocaltion(data.records)
        this.tableDataTotal = data.total
      } catch (e) {
        console.error(`获取组关系实例：${e}`)
      }
      this.tableLoading = false
    },

    $_search() {
      this.query.pageNum = 1
      this.getTableData()
      this.$_appendFilterListItem()
    },
    // 添加查询条件
    $_appendFilterListItem() {
      const searchList = cloneDeep(this.query)
      delete searchList.pageNum
      delete searchList.pageSize
      delete searchList.startDate
      delete searchList.endDate
      delete searchList.moduleNames
      this.filterList = this.$refs.advancedDrawer.setFilterList(searchList)
      // const searchKeyList = [
      //   {
      //     key: 'type',
      //     type: 'String',
      //     mapKey: 'operateTypeEnum',
      //   },
      //   {
      //     key: 'moduleNames',
      //     type: 'String',
      //   },
      //   {
      //     key: 'status',
      //     type: 'String',
      //   },
      //   {
      //     key: 'operator',
      //     type: 'String',
      //   },
      // ]
      // this.$_setTableHeight()
      // handleFilterItem.call(this, searchKeyList)
      // // 状态
      // const index = this.filterList.findIndex(v => v.key === 'status')
      // if (index !== -1) {
      //   const value = this.query.status
      //   this.filterList[index].text = this.statusEnum[value]?.text
      // }

      // // 操作类型
      // const typeIndex = this.filterList.findIndex(v => v.key === 'type')
      // if (typeIndex !== -1) {
      //   const value = this.query.type
      //   this.filterList[typeIndex].text = this.operateTypeEnum[value]?.text
      // }
    },
    // 清除某个查询条件
    $_clearFilter(item) {
      // this.filterList.splice(index, 1)
      // console.log('this.filterList', this.filterList)
      // const tempObj = Object.assign(this.$data.query, this.$options.data.call(this).query)
      // this.query = Object.assign(tempObj, Object.fromEntries(this.filterList))
      // this.$_search()

      const bool = clearFilterItem.call(this, item)
      if (!bool) {
        this.$_search()
      }
    },

    // 设置表格高度
    $_setTableHeight() {
      this.$nextTick(() => {
        const fn = () => {
          return -this.$refs.topHeader?.filterHeight - 2
        }
        this.tableHeight = setRemainingHeight(fn)
      })
    },

    add() {
      this.editType = 'add'

      this.$refs.form.resetValidation()
    },

    edit(item) {
      this.editType = 'edit'
    },

    // async del(item) {
    //   this.$set(item, 'btnLoading', true)

    //   const params = {
    //     groupId: item.id,
    //   }

    //   this.$swal({
    //     title: '删除日志',
    //     text: `删除之后不可恢复，确认删除该日志？`,
    //     icon: 'warning',
    //     reverseButtons: true,
    //     showCancelButton: true,
    //     confirmButtonText: '确认',
    //     cancelButtonText: '取消',
    //     customClass: {
    //       confirmButton: 'sweet-btn-primary',
    //     },
    //   }).then(async result => {
    //     if (result.isConfirmed) {
    //       try {
    //         const res = await delAllRelations(params)
    //         if (res.code === 200) {
    //           this.$notify.info('success', '解绑成功！')
    //         }
    //         this.$_search()
    //       } catch (e) {
    //         console.error(`删除操作日志错误：${e}`)
    //       }
    //     }
    //   })

    //   this.$set(item, 'btnLoading', false)
    // },
  },
}
</script>

<style lang="scss" scoped>
.position-absolute {
  position: absolute;
  top: 0;
  right: 0;
}
</style>
