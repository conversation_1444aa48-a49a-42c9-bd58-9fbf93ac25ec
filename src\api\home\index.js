import { cmdbPath, request } from '../../util/request'

// 获取首页基础信息
export const getHomeBasicInfo = function (params) {
  return request({
    url: `${cmdbPath}/home-page/basicInfo`,
    method: 'get',
    params,
  })
}

// 危险资产位置
export const getRiskAssetsLocation = function (params) {
  return request({
    url: `${cmdbPath}/home-page/riskAssetsLocation`,
    method: 'get',
    params,
  })
}

// 车辆危险事件类型top6
export const getRiskEvent = function (params) {
  return request({
    url: `${cmdbPath}/home-page/riskEvent`,
    method: 'get',
    params,
  })
}

// 整个系统的服务态势
export const getServiceSituation = function (params) {
  return request({
    url: `${cmdbPath}/home-page/serviceSituation`,
    method: 'get',
    params,
  })
}

// 查询统计线性图数据
export const getStatistics = function (params) {
  return request({
    url: `${cmdbPath}/home-page/statistics`,
    method: 'get',
    params,
  })
}

// 待处理告警事件
export const getWaitHandleAlarm = function (params) {
  return request({
    url: `${cmdbPath}/home-page/waitHandleAlarm`,
    method: 'get',
    params,
  })
}

// 最近告警事件
export const getRecentAlertEvent = function (params) {
  return request({
    url: `${cmdbPath}/asset-alarm/like`,
    method: 'get',
    params,
  })
}
