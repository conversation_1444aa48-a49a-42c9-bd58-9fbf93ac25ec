<!-- 告警抽屉 width="24rem" -->
<template>
  <vsoc-drawer
    width="400px"
    :title="$t('action.advanced')"
    :value="value"
    @click:confirm="doQuery"
    @input="close"
    @click:close="close"
    @click:cancel="close"
    class="alert-drawer"
  >
    <template #right-title>
      <v-btn icon class="no-hb ml-4" text @click="clearAdvanceQuery">
        <v-icon size="16">mdi-filter-variant-remove</v-icon>
      </v-btn>
    </template>

    <v-form ref="form" v-model="valid">
      <v-text-field
        v-model="advanceQuery.alarmId"
        color="primary"
        :label="$t('alert.headers.id')"
        type="number"
        medium
        class="mt-4"
        hide-spin-buttons
        :rules="idRules"
      ></v-text-field>
      <v-text-field
        v-model="advanceQuery.vehicleId"
        color="primary"
        medium
        :label="$t('alert.headers.assertId')"
      ></v-text-field>

      <v-autocomplete
        v-model="advanceQuery.vehicleCompanyCode"
        medium
        :items="modelItems"
        color="primary"
        :label="$t('alert.headers.model')"
      ></v-autocomplete>

      <v-autocomplete
        v-if="advanceQuery.assetType === '1'"
        v-model="advanceQuery.vehiclePlatformCode"
        medium
        :items="platFormData"
        color="primary"
        label="所属车企平台"
      ></v-autocomplete>

      <v-combobox
        v-model="advanceQuery.alarmTypeList"
        :menu-props="{ offsetY: true, maxHeight: 300 }"
        :items="alertTypes"
        :label="$t('alert.headers.type')"
        multiple
        :search-input.sync="searchTag"
        :return-object="false"
      >
        <template v-slot:selection="{ item, index }">
          <v-chip
            small
            pill
            color="primary"
            close
            @click:close="removeTypes(index)"
          >
            {{ alertTypeMap[item] ? alertTypeMap[item].text : item }}
          </v-chip>
        </template>
        <template v-slot:no-data>
          <p
            class="pa-2 mb-0 text-body list-hover"
            @click="createType(searchTag)"
          >
            {{ $t('alert.hint.tip') }}：<strong>{{ searchTag }}</strong>
          </p>
        </template>
      </v-combobox>

      <v-autocomplete
        v-model="advanceQuery.warnClassifyIds"
        color="primary"
        :items="alertTags"
        :label="$t('alert.headers.tag')"
        multiple
        item-text="name"
        item-value="id"
        :menu-props="{ offsetY: true, maxHeight: 300 }"
        class="drawer-2"
      >
        <template v-slot:selection="{ item, index }">
          <span v-if="index === 0" class="text-caption">
            {{ $t('global.selected') }}{{ advanceQuery.warnClassifyIds.length }}
          </span>
        </template>
      </v-autocomplete>

      <vsoc-date-range
        ref="dateInput"
        v-model="dateRange.range"
        no-title
        :menu-props="dateRange.menuProps"
        @input="onChangeDate"
        class="date-input"
      >
        <template v-slot:text="{ on, attrs }">
          <v-text-field
            clearable
            class="append-icon-max me-3"
            readonly
            hide-details
            color="primary"
            large
            :label="$t('alert.headers.triggered')"
            v-bind="attrs"
            v-on="on"
            :value="RANGE_STR(advanceQuery.startDate, advanceQuery.endDate)"
            @click:clear="onChangeDate({ start: '', end: '' })"
          ></v-text-field>
        </template>
      </vsoc-date-range>

      <div class="text-base color-base font-weight-semibold-light mt-6 mb-2">
        {{ $t('alert.headers.severity') }}
      </div>
      <div class="d-flex flex-wrap justify-space-between">
        <v-checkbox
          v-for="(item, i) in alertLevel"
          :key="i"
          v-model="advanceQuery.alarmLevelList"
          color="primary"
          :value="item.value"
          hide-details
          class="mt-0 mb-4 w-50 pa-0"
        >
          <template v-slot:label>
            <div class="d-flex align-center text-center">
              <!-- <v-icon :style="{ color: item.color }" size="16" class="mr-2">
                mdi-flash
              </v-icon> -->
              <vsoc-icon
                type="fill"
                icon="icon-gaojingjibiebiaozhi"
                size="14px"
                :style="{ color: item.color }"
                class="mr-2"
              ></vsoc-icon>
              <span class="text-base color-base">
                {{ item.text }}
              </span>
            </div>
          </template>
        </v-checkbox>
      </div>
      <div class="text-base color-base font-weight-semibold-light mt-2 mb-2">
        {{ $t('alert.headers.unhandledAlertStatus') }}
      </div>
      <div class="d-flex flex-wrap justify-lg-space-between">
        <v-checkbox
          v-for="(item, i) in unAlertStatusList"
          :key="i"
          v-model="advanceQuery.statusList"
          color="primary"
          :value="item.value"
          column
          hide-details
          class="mt-0 w-50 pa-0"
          :disabled="item.disabled ? true : false"
        >
          <template v-slot:label>
            <div class="d-flex align-center text-center">
              <v-badge
                dot
                inline
                offset-x="10"
                :offset-y="-18"
                :color="
                  alertStatus[item.value] && alertStatus[item.value].color
                "
                class="mr-2"
              ></v-badge>
              <span class="text-base color-base">
                {{ item.text }}
              </span>
            </div>
          </template>
        </v-checkbox>
      </div>
      <div class="text-base color-base font-weight-semibold-light mt-6 mb-2">
        {{ $t('alert.headers.handledAlertStatus') }}
      </div>
      <div class="d-flex flex-wrap justify-lg-space-between">
        <v-checkbox
          v-for="(item, i) in alertStatusList"
          :key="i"
          v-model="advanceQuery.statusList"
          color="primary"
          :value="item.value"
          column
          hide-details
          class="mt-0 mb-4 w-50 pa-0"
          :disabled="item.disabled ? true : false"
        >
          <template v-slot:label>
            <div class="d-flex align-center text-center">
              <v-badge
                dot
                inline
                offset-x="10"
                :offset-y="-18"
                :color="
                  alertStatus[item.value] && alertStatus[item.value].color
                "
                class="mr-2"
              ></v-badge>
              <span class="text-base color-base">
                {{ item.text }}
              </span>
            </div>
          </template>
        </v-checkbox>
      </div>
    </v-form>
  </vsoc-drawer>
</template>

<script>
import { max } from '@/@core/utils/validation'
import VsocDateRange from '@/components/vsoc-date-range/VsocDateRange.vue'
import { RANGE_STR } from '@/components/vsoc-date-range/constants'

import VsocDrawer from '@/components/VsocDrawer.vue'
import { format, subDays } from 'date-fns'

export default {
  components: {
    VsocDrawer,
    VsocDateRange,
  },
  props: {
    value: Boolean,
    alertTags: Array,
    alertTypes: Array,
    modelItems: Array,
    platFormData: Array,
    alertTypeMap: Object,
  },
  data() {
    return {
      createing: false,
      searchTag: null,
      valid: true,
      idRules: [v => max(v, 10)],
      isLoading: false,
      search: null,
      dateRange: {
        range: {
          start: '',
          end: '',
        },
        menuProps: { offsetY: true, closeOnContentClick: false },
      },
      advanceQuery: {
        startDate: '',
        endDate: '',
        alarmLevelList: [],
        statusList: [],
        warnClassifyIds: [],
        vehicleId: '',
        alarmTypeList: [],
        alarmId: '',
        vehicleCompanyCode: '',
        vehiclePlatformCode: '',
      },
      pickerOptions: {
        shortcuts: [
          {
            text: '最近1小时',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 1000 * 60 * 60)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '最近24小时',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 1000 * 60 * 60 * 24)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '最近7天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 1000 * 60 * 60 * 24 * 7)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '最近30天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 1000 * 60 * 60 * 24 * 30)
              picker.$emit('pick', [start, end])
            },
          },
        ],
      },
    }
  },
  watch: {},
  computed: {
    unAlertStatusList() {
      const statusList = this.$store.getters['enums/getAlertStatus']
      return statusList.filter(v => v.value === '0' || v.value === '1')
    },
    alertStatusList() {
      const statusList = this.$store.getters['enums/getAlertStatus']
      return statusList.filter(v => v.value !== '0' && v.value !== '1')
    },
    alertStatus() {
      return this.$store.state.enums.enums.AlarmStatus
    },
    assetTypeEnum() {
      return this.$store.state.enums.enums.AssetType
    },
    // 获取告警等级颜色
    alertLevel() {
      return this.$store.state.enums.enums.AlarmLevel
    },
  },
  created() {},
  methods: {
    removeTypes(index) {
      this.advanceQuery.alarmTypeList.splice(index, 1)
    },
    async createType(tagName) {
      if (this.createing) return
      this.createing = true
      // this.alertTypes.unshift(tagName)
      this.advanceQuery.alarmTypeList.push(tagName)
      this.searchTag = ''
      this.createing = false
    },

    RANGE_STR,

    close(bool) {
      if (!bool) {
        this.$emit('input', false)
      }
    },

    setModel(val) {
      this.dateRange.range = {
        start: val.startDate,
        end: val.endDate,
      }
      this.advanceQuery = val
      // this.getSelectBox()
    },

    // closeChip(index) {
    //   console.log(index)
    // },
    // 时间范围改变
    onChangeDate(range) {
      this.dateRange.range = range
      this.advanceQuery.startDate = range.start
      this.advanceQuery.endDate = range.end
    },

    clearAdvanceQuery() {
      this.advanceQuery = {
        assetType: this.advanceQuery.assetType,
        startDate: '',
        endDate: '',
        alarmLevelList: ['0', '1', '2', '3', '4', '5'],
        statusList: ['0', '1'],
        warnClassifyIds: [],
        vehicleId: '',
        alarmTypeList: [],
        alarmId: '',
        vehicleCompanyCode: '',
        vehiclePlatformCode: '',
        pageNum: 1,
        pageSize: 10,
      }
      const defalutTime = [subDays(new Date(), 7), new Date()]
      const start = format(defalutTime[0], 'yyyy-MM-dd')
      const end = format(defalutTime[1], 'yyyy-MM-dd')
      const range = {
        start: [start, '00:00:00'].join(' ').trim(),
        end: [end, '23:59:59'].join(' ').trim(),
      }
      this.onChangeDate(range)
    },

    //  高级查询
    doQuery(callback) {
      const bool = this.$refs.form.validate()
      if (!bool) {
        return callback(false, true)
      }
      setTimeout(() => {
        const params = {
          advanceQuery: this.advanceQuery,
        }
        this.$emit('do-query', params)
        callback()
      }, 180)
    },
  },
}
</script>

<style lang="scss" scoped>
.date-input {
  ::v-deep .v-text-field.v-input--dense {
    margin-right: 0 !important;
  }
}
.selectBox {
  ::v-deep .v-list-item__title > ::v-deep .v-badge {
    margin-top: 0 !important;
  }

  ::v-deep .v-application--is-ltr .v-list-item__action:first-child {
    margin-right: 0 !important;
  }
  ::v-deep .v-input--selection-controls__ripple:before {
    opacity: 0 !important;
  }

  ::v-deep .v-list-item__title > .v-badge {
    margin-top: 0;
  }

  ::v-deep .v-list .v-list-item--active input,
  ::v-deep .v-input--selection-controls__input input {
    display: none;
  }

  ::v-deep .v-input--selection-controls__input {
    display: flex;
    justify-content: center;
  }
}
</style>
