const token = {
  currentTitle: 'Token',
  headers: {
    accessName: 'Token Name',
    token: 'Token',
    accessKeyType: 'Type',
    tokenLevel: 'Level',
    expirationDate: 'Expiration Date',
    lastUsedDate: 'Last Used Date',
    usedCount: 'Used Count',
    createUser: 'Create User',
    createDate: 'Create Date',
    updateUser: 'Update User',
    updateDate: 'Update Date',
    isActive: 'Is Active',
    describe: 'Describe',
  },
  btn1: 'Create Token',
  btn2: 'Modify Token',
  hint: {
    fullName: 'Within 100 Words',
  },
  swal: {
    revoke: {
      title: 'Revoke Token',
      text: 'You are revoking this token. After revocation, all APIs that depend on this token will be inaccessible！',
    },
  },
}

export default token
