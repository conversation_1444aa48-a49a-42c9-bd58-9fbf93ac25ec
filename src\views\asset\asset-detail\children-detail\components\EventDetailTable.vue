<template>
  <v-card>
    <v-card-title
      >{{ $t('asset.idpsTab2.eventDetailList.title') }}
      <v-spacer></v-spacer>
      <a class="text-base text--secondary text-decoration-underline">
        {{ $t('asset.idpsTab2.viewMore') }}
      </a>
    </v-card-title>
    <v-card-text>
      <v-data-table
        :headers="headers"
        :items="tableData"
        :item-class="rowClass"
        height="19.5rem"
        class="asset-children"
        item-key="id"
        :items-per-page="tableData.length"
        hide-default-footer
      ></v-data-table>
    </v-card-text>
  </v-card>
</template>

<script>
import { toDate } from '@/util/filters'
export default {
  name: 'EventDetailTable',
  data() {
    return {
      tableData: [
        {
          id: 1,
          userId: toDate(new Date()),
          process: '入口流量异常',
          shell: '检测到入口流量异常',
          cpu: '58',
        },
        {
          id: 2,
          userId: toDate(new Date()),
          process: '未知用户',
          shell: '检测到未知用户',
          cpu: '58',
        },
        {
          id: 3,
          userId: toDate(new Date()),
          process: '入口流量异常',
          shell: '检测到入口流量异常',
          cpu: '58',
        },
        {
          id: 4,
          userId: toDate(new Date()),
          process: '未知进程',
          shell: '检测到未知进程',
          cpu: '58',
        },
        {
          id: 5,
          userId: toDate(new Date()),
          process: '入口流量异常',
          shell: '检测到入口流量异常',
          cpu: '58',
        },
      ],
    }
  },
  computed: {
    headers() {
      return [
        {
          text: this.$t('asset.idpsTab2.eventDetailList.headers.time'),
          value: 'userId',
          width: 120,
          align: 'center',
          sortable: false,
        },
        {
          text: this.$t('asset.idpsTab2.eventDetailList.headers.type'),
          value: 'process',
          width: 100,
          align: 'center',
          sortable: false,
        },
        {
          text: this.$t('asset.idpsTab2.eventDetailList.headers.contnet'),
          value: 'shell',
          width: 100,
          align: 'center',
          sortable: false,
        },
        {
          text: this.$t('asset.idpsTab2.eventDetailList.headers.count'),
          value: 'cpu',
          width: 80,
          align: 'center',
          sortable: false,
        },
      ]
    },
  },
  methods: {
    rowClass(record) {
      let index = this.tableData.findIndex(item => item.id === record.id)
      if (index % 2 === 0) {
        return 'bg-body'
      }
      return
    },
  },
}
</script>
<style scoped lang="scss"></style>
