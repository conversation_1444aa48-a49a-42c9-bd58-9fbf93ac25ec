{
    // Editor Preferences
    "editor.formatOnSave": true,
    // JS
    "javascript.updateImportsOnFileMove.enabled": "always",
    // JSON
    "[json]": {
        "editor.defaultFormatter": "vscode.json-language-features"
    },
    "[jsonc]": {
        "editor.defaultFormatter": "vscode.json-language-features"
    },
    // Extensions: Emmet
    "emmet.excludeLanguages": [],
    "emmet.includeLanguages": {
        "markdown": "html"
    },
    // Extension: Prettier
    "prettier.requireConfig": true,
    "[html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[typescript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[markdown]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[scss]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    // Extension: ESLint
    "editor.codeActionsOnSave": {
        "source.organizeImports": "never",
        "source.fixAll": "never"
    },
    "eslint.validate": [
        "vue",
        "html",
        "javascript",
        "typescript",
        "javascriptreact",
        "typescriptreact"
    ],
    "eslint.alwaysShowStatus": true,
    "eslint.format.enable": true,
    // Extension: Vetur
    // "vetur.experimental.templateInterpolationService": true,
    "vetur.validation.template": false,
    "vetur.format.defaultFormatter.html": "none",
    "[vue]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode",
    },
    // Extension: npm
    "npm.packageManager": "yarn",
    "search.useIgnoreFiles": true,
    "search.useParentIgnoreFiles": true,
    "liveServer.settings.CustomBrowser": "chrome",
    "i18n-ally.localesPaths": [
        "src/plugins/i18n/locales",
    ], // 翻译文件路径
    "i18n-ally.enabledParsers": [
        "js"
    ],
    // "i18n-ally.dirStructure": "file",
    "i18n-ally.keystyle": "nested", // 翻译路径格式,
    "i18n-ally.sourceLanguage": "zh-CN", // 翻译源语言
    "i18n-ally.displayLanguage": "en", //显示语言， 这里也可以设置显示英文为en
    "i18n-ally.extract.keygenStyle": "camelCase", // 翻译字段命名样式采用驼峰
    "i18n-ally.namespace": true,
    "i18n-ally.pathMatcher": "{locale}/module/{namespace}.js",
    "vue-i18n.i18nPaths": "src\\plugins\\i18n,src\\@core\\libs\\i18n,src\\plugins\\i18n\\locales"
}