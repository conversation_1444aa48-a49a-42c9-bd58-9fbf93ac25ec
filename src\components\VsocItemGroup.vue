<template>
  <v-item-group style="height: calc(100% - 200px)" @change="itemChange">
    <v-container style="box-sizing: border-box" class="overflow-auto h-100">
      <v-row>
        <v-col
          v-for="item in dataList"
          :key="item.id"
          :cols="colCols"
          :md="colMd"
          :xl="colXl"
          v-if="!item.disabled"
        >
          <v-item
            :style="{ height: model === '1' ? '46px' : '110px' }"
            :value="item.id"
            v-slot="{ active, toggle }"
          >
            <v-hover>
              <template v-slot:default="{ hover }">
                <v-card
                  :color="active ? 'activeBox' : ''"
                  :class="
                    $vuetify.theme.isDark
                      ? 'logistics-card-statistics d-flex align-center modelBoxDark'
                      : 'logistics-card-statistics d-flex align-center modelBox'
                  "
                  @click="toggle"
                >
                  <slot :item="item" :active="active"></slot>
                </v-card>
              </template>
            </v-hover>
          </v-item>
        </v-col>
      </v-row>
    </v-container>
  </v-item-group>
</template>

<script>
export default {
  name: 'VsocItemGroup',
  props: {
    model: {
      type: String,
      default: '',
    },
    dataList: {
      type: Array,
      default: [],
    },
    colCols: {
      type: String,
      default: '12',
    },
    colMd: {
      type: String,
      default: '4',
    },
    colXl: {
      type: String,
      default: '4',
    },
  },
  methods: {
    itemChange(val) {
      let activeEngine = this.dataList.filter(item => {
        return item.id === val
      })
      this.$emit('change', activeEngine[0])
    },
  },
}
</script>

<style scoped lang="scss">
.modelBoxDark {
  border-top: 1px solid #2b3152;
  border-left: 1px solid #2b3152;
  border-right: 1px solid #2b3152;
  border-bottom: 1px solid #2b3152;
  box-sizing: border-box;
  transition: box-shadow 0.3s;
  box-shadow: none !important;
}

.modelBox {
  border-top: 1px solid #e6eaf2;
  border-left: 1px solid #e6eaf2;
  border-right: 1px solid #e6eaf2;
  border-bottom: 1px solid #e6eaf2;
  box-sizing: border-box;
  transition: box-shadow 0.3s;
  box-shadow: none !important;
}

.primary--hover:hover {
  color: $primary !important;
}

.v-card--link:focus:before {
  opacity: 0;
}

//::v-deep .v-sheet:not(.v-sheet--outlined) {
//  box-shadow: none !important;
//}

.v-card:hover {
  //box-shadow: 3px 3px 10px 0px rgba(0, 0, 0, 10%) !important;
  border: 1px solid $primary;
  box-shadow: 0 0 0 1px $primary !important;
}

::v-deep .activeBox {
  //box-shadow: 3px 3px 10px 0px rgba(0, 0, 0, 10%) !important;
  border: 1px solid $primary;
  box-shadow: 0 0 0 1px $primary !important;
}
</style>
