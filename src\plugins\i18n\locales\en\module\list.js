const list = {
  currentTitle: 'List',
  headers: {
    name: 'List name',
    description: 'Description',
    valueTypeName: 'Type',
    itemCount: 'Items',
    isEncrypt: 'Encryption',
    isEncrypt1: 'Encryption',
    Longitude: 'Longitude',
    latitude: 'latitude',
  },
  delete: {
    title: 'Delete List',
    text: 'Are you sure to delete the list:{0}?',
  },
  tip: 'Contains alphanumeric values or {},-_./:&*@]"',
  detail: 'Details',
  btn: {
    new: 'New List',
    edit: 'Edit List',
    detail: 'List Details',
    del: 'Delete Item',
    export: 'Export Item',
    add: 'New Item',
    import: 'Import Item',
    stop: 'Stop drawing',
    begin: 'Start drawing',
    addPath: 'Add coordinate points',
  },
  hint: {
    tip: 'Select this option in case this list is intended to be used against encrypted signals',
    operation: 'Staging operation',
    tip1: 'Incorrect format',
    tip2: 'Item is Required',
    tip3: 'Same Item',
    tip4: 'Error Message',
    tip5: 'The number of items cannot exceed 1000',
    tip6: 'Longitude and latitude are Required',
    tip7: 'Start drawing with the left mouse button and end drawing with the right mouse button',
    tip8: 'Import Notice:',
    tip9: 'File type: Excel',
    tip10: 'Only supports single column data',
    tip11: 'First line title: must be "item"',
  },
  del: {
    title: 'Delete Item (Staging Operation)',
    info: 'The selected item will be temporarily removed from the list',
    tip: 'Removed from staging table',
    text: 'Are you sure to delete the item:{0}?',
  },
}

export default list
