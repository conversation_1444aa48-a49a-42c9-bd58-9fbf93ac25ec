<template>
  <div>
    <bread-crumb></bread-crumb>
    <v-card tile class="main-content">
      <v-card-text class="pa-0">
        <div class="d-flex justify-space-between align-center flex-row-reverse">
          <div class="d-flex align-center">
            <table-search
              :searchList="searchList"
              :searchQuery="query"
              @search="$_search"
            ></table-search>
          </div>
          <div class="d-flex justify-end align-center">
            <v-btn
              v-has:dict-data-add
              elevation="0"
              color="primary"
              @click="$_add"
            >
              <span>
                {{ $t('action.add') }}
              </span>
            </v-btn>
            <v-btn
              v-has:refresh-cache
              class="ml-3"
              elevation="0"
              color="primary"
              :loading="loading"
              @click="$_refreshCache"
            >
              <span>
                {{ $t('action.refreshCache') }}
              </span>
            </v-btn>
          </div>
        </div>
        <v-data-table
          fixed-header
          :items-per-page="query.pageSize"
          item-key="code"
          :height="tableHeight"
          hide-default-footer
          :headers="headers"
          :items="tableData"
          class="table border-radius-xl mt-3 thead-light"
          :loading="tableLoading"
        >
          <template v-slot:item.dictName="{ item }">
            <div v-show-tips style="width: 140px" class="text-overflow-hide">
              {{ item.dictName | dataFilter }}
            </div>
          </template>
          <template v-slot:item.dictEnName="{ item }">
            <div v-show-tips style="width: 140px" class="text-overflow-hide">
              {{ item.dictEnName | dataFilter }}
            </div>
          </template>
          <template v-slot:item.status="{ item }">
            <v-badge
              dot
              inline
              offset-x="10"
              :offset-y="-18"
              :color="item.status === '0' ? activeColor : inactiveColor"
              class="mr-1"
            ></v-badge>
            <span>{{ item.statusName }}</span>
          </template>
          <template v-slot:item.remark="{ item }">
            <div v-show-tips style="width: 140px" class="text-overflow-hide">
              {{ item.remark | dataFilter }}
            </div>
          </template>
          <template v-slot:item.actions="{ item }">
            <v-btn v-has:dict-data-edit icon @click.stop="$_edit(item)">
              <vsoc-icon
                v-show-tips="$t('action.edit')"
                type="fill"
                icon="icon-bianji"
                class="action-btn"
                size="x-large"
              ></vsoc-icon>
            </v-btn>
            <v-btn
              v-has:dict-data-del
              v-if="item.isDefault !== '0'"
              icon
              @click.stop="$_del(item)"
            >
              <vsoc-icon
                v-show-tips="$t('action.del')"
                type="fill"
                icon="icon-shanchu"
                class="action-btn"
                size="x-large"
              ></vsoc-icon>
            </v-btn>
          </template>
        </v-data-table>

        <!-- 分页器 -->
        <vsoc-pagination
          :page.sync="query.pageNum"
          :size.sync="query.pageSize"
          :total="tableDataTotal"
          @change-page="getTableData"
          @change-size="$_search"
        >
        </vsoc-pagination>
      </v-card-text>
    </v-card>
    <data-edit
      ref="refDataEdit"
      :mode="drawMode"
      :allTypeList="allDictType"
      @save="$_search"
    />
  </div>
</template>
<script>
import { delDictType, dictTypeList, getDictDataList } from '@/api/system/dict'

import VsocPagination from '@/components/VsocPagination.vue'

import breadCrumb from '@/components/bread-crumb/index'
import TableSearch from '@/components/TableSearch/index.vue'
import { activeColor, inactiveColor } from '@/plugins/systemColor'
import { setRemainingHeight } from '@/util/utils'
import { loading } from '@jiaminghi/data-view'
import DataEdit from './dataEdit.vue'
export default {
  name: 'DictIndex',
  components: {
    VsocPagination,
    breadCrumb,
    TableSearch,
    DataEdit,
  },
  data() {
    return {
      activeColor,
      inactiveColor,
      // 分页参数
      query: {
        dictType: '', //字典类型id
        dictName: '', //字典标签(中文)
        dictEnName: '', //字典标签(英文)
        status: '', //状态（0正常 1停用）
        pageNum: 1, //当前页
        pageSize: 10, //每页多少条
      },
      tableLoading: true,
      tableDataTotal: 0,
      tableData: [],
      tableHeight: '34.5rem',
      drawMode: '',
      allDictType: [],
      loading: false,
    }
  },
  created() {
    this.$_setTableHeight()
    this.$bus.$on('resize', this.$_setTableHeight)
    this.query.dictType = this.$route.query.dictId
    this.getAllType()
    this.$_search()
  },
  computed: {
    searchList() {
      return [
        {
          type: 'autocomplete',
          value: 'dictType',
          multiple: 'false',
          text: this.$t('dict.headers1.dictType'),
          itemList: this.allDictType,
        },
        {
          type: 'input',
          value: 'dictName',
          text: this.$t('dict.headers1.dictName1'),
        },
        // {
        //   type: 'multiSearch',
        //   value: 'dictName',
        //   conditions: [
        //     {
        //       type: 'input',
        //       value: 'dictName',
        //       text: this.$t('dict.headers1.dictName'),
        //     },
        //     {
        //       type: 'input',
        //       value: 'dictEnName',
        //       text: this.$t('dict.headers1.dictEnName'),
        //     },
        //   ],
        // },
      ]
    },
    headers() {
      return [
        {
          text: this.$t('dict.headers1.dictId'),
          value: 'dictId',
          width: '100px',
        },
        {
          text: this.$t('dict.headers1.dictName'),
          value: 'dictName',
          width: '140px',
        },

        {
          text: this.$t('dict.headers1.dictEnName'),
          value: 'dictEnName',
          width: '140px',
        },
        {
          text: this.$t('dict.headers1.sort'),
          value: 'sort',
          width: '100px',
        },
        {
          text: this.$t('dict.headers.status'),
          value: 'status',
          width: '100px',
        },
        {
          text: this.$t('dict.headers.isDefault'),
          value: 'isDefaultName',
          width: '100px',
        },
        {
          text: this.$t('dict.headers.remark'),
          value: 'remark',
          width: '140px',
        },
        {
          text: this.$t('global.createDate'),
          value: 'createDate',
          width: '160px',
        },
        {
          text: this.$t('global.updateDate'),
          value: 'updateDate',
          width: '160px',
        },
        {
          text: this.$t('global.updateUser'),
          value: 'updateUser',
          width: '120px',
        },
        {
          text: '',
          value: 'actions',
          sortable: false,
          width: '120',
        },
      ]
    },
  },
  beforeDestroy() {
    this.$bus.$off('resize', this.$_setTableHeight)
  },
  methods: {
    //刷新缓存
    $_refreshCache() {
      this.loading = true
      setTimeout(() => {
        this.loading = false
        location.reload()
      }, 100)
    },
    //获取所有字典类型
    async getAllType() {
      const data = await dictTypeList({ dictId: '', dictName: '' })
      this.allDictType =
        data.data.map(v => {
          return {
            value: v.dictId,
            text: v.dictName,
          }
        }) || []
    },
    //新增
    $_add() {
      this.drawMode = 'new'
      let findItem = this.allDictType.find(v => v.value === this.query.dictType)
      if (!findItem) return this.$notify.info('warning', this.$t('dict.tip2'))
      this.$refs['refDataEdit'].open(findItem)
    },
    //编辑
    $_edit(item) {
      this.drawMode = 'edit'
      this.$refs['refDataEdit'].open(item)
    },
    //删除
    $_del(item) {
      let name = this.$i18n.locale === 'en' ? item.dictEnName : item.dictName
      this.$swal({
        title: this.$t('dict.del1.title'),
        text: this.$t('dict.del1.text', [name]),
        icon: 'warning',
        showCancelButton: true,
        reverseButtons: true,
        confirmButtonText: this.$t('action.confirm'),
        cancelButtonText: this.$t('action.cancel'),
        customClass: {
          confirmButton: 'sweet-btn-primary',
        },
      }).then(async result => {
        if (result.isConfirmed) {
          try {
            const res = await delDictType({
              dictType: this.query.dictType,
              dictId: item.dictId,
            })
            if (res.code === 200) {
              this.$notify.info(
                'success',
                this.$t('global.hint.del', [this.$t('dict.currentTitle1')]),
              )
            }
            this.$_search()
          } catch (e) {
            console.error(`删除字典数据错误：${e}`)
          }
        }
      })
    },
    $_setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = setRemainingHeight(
          this.$store.state.global.subMarginFn,
        )
      })
    },
    $_search() {
      this.query.pageNum = 1
      this.getTableData()
    },
    // 加载表格数据
    async getTableData() {
      this.tableLoading = true
      try {
        const res = await getDictDataList(this.query)
        this.tableDataTotal = res.data.total
        this.tableData = res.data.records
      } catch (e) {
        console.error(`获取字典数据错误：${e}`)
      } finally {
        this.tableLoading = false
      }
    },
  },
}
</script>
