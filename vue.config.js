const path = require('path')
const { mergeSassVariables } = require('@vuetify/cli-plugin-utils')

// const CompressionPlugin = require('compression-webpack-plugin')
const version = new Date().getTime()
module.exports = {
  publicPath:
    process.env.NODE_ENV === 'development' ? '/vsocwebcatarcdev/' : '/test/',
  outputDir: 'dist',
  assetsDir: 'static',
  lintOnSave: false,
  productionSourceMap: false,

  configureWebpack: config => {
    if (process.env.NODE_ENV !== 'development') {
      return {
        plugins: [
          new CompressionPlugin({
            test: /\.js$|\.html$|\.css/, // 匹配文件名
            threshold: 10240, // 对超过10K的数据进行压缩
            deleteOriginalAssets: false, // 是否删除原文件
            minRatio: 0.8,
          }),
        ],
      }
    }
  },

  css: {
    // Enable CSS source maps.
    sourceMap: process.env.NODE_ENV !== 'production',
    // 是否使用css分离插件 ExtractTextPlugin
    extract:
      process.env.NODE_ENV !== 'production'
        ? false
        : {
            // 修改打包后css文件名 // css打包文件，添加时间戳
            filename: `css/[name].${version}.css`,
            chunkFilename: `css/[name].${version}.css`,
          },
  },
  devServer: {
    port: 8080, // 端口号
    open: true, // 配置自动启动浏览器

    // 配置多个代理
    proxy: {
      '/vsoccatarctest': {
        // target: 'http://175.6.134.2:9087', // 开发要访问的接口域名
        target: 'http://172.20.0.167:9091', // 测试要访问的接口域名
        // ws: true, // 是否启用websockets
        changeOrigin: true, // 开启代理：在本地会创建一个虚拟服务端，然后发送请求的数据，并同时接收请求的数据，这样服务端和服务端进行数据的交互就不会有跨域问题
        secure: false,
        pathRewrite: {
          '^/vsoccatarctest': '', // 这里理解成用'/api'代替target里面的地址,比如我要调用'http://40.00.100.100:3002/user/add'，直接写'/api/user/add'即可
        },
      },
      '/vsoccatarcdev': {
        // target: 'http://175.6.134.2:9087', // 开发要访问的接口域名
        target: 'http://172.20.0.167:9090', // 测试要访问的接口域名
        // ws: true, // 是否启用websockets
        changeOrigin: true, // 开启代理：在本地会创建一个虚拟服务端，然后发送请求的数据，并同时接收请求的数据，这样服务端和服务端进行数据的交互就不会有跨域问题
        secure: false,
        pathRewrite: {
          '^/vsoccatarcdev': '', // 这里理解成用'/api'代替target里面的地址,比如我要调用'http://40.00.100.100:3002/user/add'，直接写'/api/user/add'即可
        },
      },
      // '/api': {
      //   target: 'https://api.map.baidu.com',
      //   ws: false,
      //   changOrigin: true, //允许跨域
      //   pathRewrite: {
      //     '^/api': '', //请求的时候使用这个/baidu 就可以
      //   },
      // },
    },
  },

  // '@axios': path.resolve(__dirname, 'src/plugins/axios.js'),

  transpileDependencies: ['vuetify', 'json-editor-vue'],
  configureWebpack: {
    externals: [
      {
        './cptable': 'var cptable',
      },
    ],
    resolve: {
      alias: {
        '@themeConfig': path.resolve(__dirname, 'themeConfig.js'),
        '@core': path.resolve(__dirname, 'src/@core'),
        '@user-variables': path.resolve(__dirname, 'src/styles/variables.scss'),
        apexcharts: path.resolve(
          __dirname,
          'node_modules/apexcharts-clevision',
        ),
      },
    },
    output: {
      // 输出重构 打包编译后的 文件名称 【模块名称.版本号.时间戳】
      filename:
        process.env.NODE_ENV !== 'production'
          ? '[name].[hash].js'
          : `js/[name].[chunkhash].${version}.js`,
      chunkFilename: `js/[id].[chunkhash].${version}.js`,
    },
    module: {
      rules: [
        // 让 webpack 识别 `.mjs` 文件
        {
          test: /\.mjs$/,
          include: /node_modules/,
          type: 'javascript/auto',
        },
      ],
    },
  },
  chainWebpack: config => {
    // it can improve the speed of the first screen, it is recommended to turn on preload
    config.plugin('preload').tap(() => [
      {
        rel: 'preload',

        // to ignore runtime.js
        fileBlacklist: [/\.map$/, /hot-update\.js$/, /runtime\..*\.js$/],
        include: 'initial',
      },
    ])

    const modules = ['vue-modules', 'vue', 'normal-modules', 'normal']
    modules.forEach(match => {
      config.module
        .rule('sass')
        .oneOf(match)
        .use('sass-loader')
        .tap(opt => mergeSassVariables(opt, "'@/styles/variables.scss'"))
      config.module
        .rule('scss')
        .oneOf(match)
        .use('sass-loader')
        .tap(opt => mergeSassVariables(opt, "'@/styles/variables.scss';"))
    })

    // web worker 需要重新安装worker-loader
    // config.module
    //   .rule('worker')
    //   .test(/\.worker\.js$/)
    //   .use('worker-loader')
    //   .loader('worker-loader')
    //   .end()

    // // 解决：worker 热更新问题
    // config.module.rule('js').exclude.add(/\.worker\.js$/)

    // 拆分包

    // config.when(process.env.NODE_ENV !== 'development', config => {

    config.optimization.splitChunks({
      chunks: 'all',
      cacheGroups: {
        // vuetifyUI: {
        //   name: 'chunk-vuetifyUI', // split elementUI into a single package
        //   priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
        //   test: /[\\/]node_modules[\\/]_?vuetify(.*)/, // in order to adapt to cnpm
        // },
        echartUI: {
          name: 'chunk-echartUI', // split elementUI into a single package
          priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
          test: /[\\/]node_modules[\\/]_?echart(.*)/, // in order to adapt to cnpm
        },
        elementUI: {
          name: 'chunk-elementUI', // split elementUI into a single package
          priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
          test: /[\\/]node_modules[\\/]_?element-ui(.*)/, // in order to adapt to cnpm
        },
        libs: {
          name: 'chunk-libs',
          test: /[\\/]node_modules[\\/]/,
          priority: 10,
          chunks: 'initial', // only package third parties that are initially dependent
        },
        commons: {
          name: 'chunk-commons',
          test: path.resolve('src/components'), // can customize your rules
          minChunks: 3, //  minimum common number
          priority: 5,
          reuseExistingChunk: true,
        },
      },
    })
    config.optimization.runtimeChunk('single')

    // })
  },
}
