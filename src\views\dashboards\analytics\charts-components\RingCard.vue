<template>
  <v-card class="h-full">
    <v-card-title class="text-xxl pb-2">
      {{ title }}
      <slot name="title" :sum="sum"></slot>
    </v-card-title>
    <v-card-text
      v-empty-chart="list.length === 0"
      style="height: 72%"
      class="d-flex pb-0 align-center"
    >
      <vsoc-chart
        ref="echart"
        class="h-100 w-50"
        :echart-id="echartId"
        :option="chartOption"
        @highlight="onHighlight"
        @finished="onFinished"
        @click="onClick"
      ></vsoc-chart>
      <div class="w-50 text-left">
        <div class="text--primary">
          <span class="text-n4xl font-weight-semibold mr-2">{{
            numberToFormat(selected.value, 'Object').num
          }}</span>
          <span>{{ numberToFormat(selected.value, 'Object').unit }}</span>
        </div>
        <div class="mt-4 text-left">
          <!-- :color="`rgba(${selected.color},0.15)`"
          :style="`color:${selected.color};background:rgba(${selected.color},0.15)`" -->
          <v-avatar
            size="1rem"
            rounded
            class="mr-2"
            :color="selected.color | opactiyColor"
          >
            <vsoc-icon
              size="0.85rem"
              :style="`color:${selected.color}`"
              type="fill"
              icon="icon-gaojingjibiebiaozhi"
            ></vsoc-icon>
          </v-avatar>
          <span :style="`color:${selected.color}`">{{ selected.name }}</span>
        </div>
      </div>
    </v-card-text>
  </v-card>
</template>

<script>
import { hexToRgb } from '@/@core/utils'
import VsocChart from '@/components/VsocChart'
import { numberToFormat } from '@/util/filters'
import themeConfig from '@themeConfig'
import lodash from 'lodash'
export default {
  components: {
    VsocChart,
  },
  filters: {
    opactiyColor(color) {
      if (/^(rgb)/.test(color)) {
        let newColor = color.replace('rgb', 'rgba').replace(')', ',0.2)')
        return newColor
      }
      const obj = hexToRgb(color)
      return `rgba(${obj.r},${obj.g},${obj.b},0.2)`
    },
  },
  props: {
    title: {
      type: String,
      default: () => '标题',
    },
    list: {
      type: Array,
      default: () => [],
    },
    echartId: {
      type: String,
      default: () => 'echart',
    },
    padding: {
      type: [Number, Array],
      default: () => [10, 0],
    },
    formatter: {
      type: Function,
      default(params) {
        return `\n{value|${numberToFormat(params.value)}}\n{name|${
          params.name
        }}`
      },
    },
  },
  data() {
    return {
      numberToFormat,
      myChart: '',
      sum: 0,
      percent: 0,
      dataIndex: 0,
      selected: {
        name: '暂无数据',
        value: 0,
        color: '#ffffff',
      },

      backgroundColor: '#b087f8',
      textColor: '#fff',
      rich: {
        name: {
          fontSize: '0.8571rem',
          padding: this.padding,
        },
        value: {
          lineHeight: 20,
          fontSize: '1.4286rem',
          padding: this.padding,
          fontWeight: 500,
        },
      },
      emphasis: {
        show: true,
        scale: true,
        scaleSize: 0,
        label: {
          formatter: params => this.formatter(params),

          // // 切换非默认选项配置数据展示
          // if (params.dataIndex != 0) {
          //   return `\n{value|${params.value}k}` + `\n{name|${params.name}}`
          // }
        },
      },
      label: {
        show: true,
        position: 'center',

        lineHeight: 16,

        formatter: params => '',

        // if (params.dataIndex === 0) {
        //   return `\n{value|${params.value}k}\n{name|${params.name}}`
        // }
      },
      itemStyle: {
        borderWidth: 4,
        borderRadius: 8,
        borderColor: 'transparent',
      },

      chartOption: {
        legend: {
          top: '5%',
          left: 'center',
          show: false,
        },

        series: [
          {
            name: '告警级别',
            type: 'pie',
            radius: ['75%', '90%'],
            // radius: [55, 68],
            // radius: [60, 70],
            avoidLabelOverlap: true,
            percentPrecision: 2,
            minAngle: 6,
            stillShowZeroSum: true,
            label: {},
            labelLine: {
              show: false,
            },

            // 数据倒叙排序
            // data: [
            //   { value: 85, name: 'series-1' },
            //   { value: 20, name: 'series-2' },
            //   { value: 30, name: 'series-3' },
            //   { value: 50, name: 'series-4' },
            // ],
            data: [],
          },
          // {
          //   name: '',
          //   type: 'pie',
          //   z: 2,
          //   clockwise: false, //顺时加载
          //   radius: ['78%', '85%'],
          //   label: {
          //     show: false,
          //   },
          //   itemStyle: {
          //     label: {
          //       show: false,
          //     },
          //     labelLine: {
          //       show: false,
          //     },
          //   },
          //   data: [
          //     {
          //       value: 1,
          //       itemStyle: {
          //         color: 'rgba(250, 250, 250, 0.1)',
          //       },
          //       tooltip: {
          //         show: false,
          //       },
          //     },
          //   ],
          // },
        ],
      },
    }
  },
  watch: {
    '$vuetify.theme.dark': {
      handler(value) {
        if (value) {
          this.backgroundColor = themeConfig.themes.dark.backgroundColor
          this.textColor = themeConfig.themes.dark.secondary
        } else {
          this.backgroundColor = themeConfig.themes.light.backgroundColor
          this.textColor = themeConfig.themes.light.color
        }
        this.resetColor()
      },
      deep: true,
      immediate: true,
    },
    list: {
      handler(newArr) {
        const sortArr = lodash.orderBy(newArr, ['value'], ['desc'])
        this.chartOption.series[0].data = sortArr.filter(v => v.value !== 0)
        this.chartOption.color = sortArr.map(v => v.color)

        // this.selected = Object.assign(sortArr[0], { color: this.colorList[0] })
        this.selected = sortArr[0]
        const valueArr = sortArr.map(item => item.value)
        this.sum = valueArr.length > 0 && valueArr.reduce((x, y) => x + y)
        if (this.sum > 0) {
          sortArr.forEach(v => {
            v.percent = Math.round((v.value / this.sum) * 100) + '%'
          })
        }
        this.percent =
          sortArr[0]?.percent ||
          `${
            this.sum > 0 && Math.round((this.selected.value / this.sum) * 100)
          }%`
        if (newArr.length === 0) {
          this.percent = '0%'
          this.selected = {
            name: '暂无数据',
            value: 0,
            color: '#ffffff',
          }
        }
        if (this.myChart) {
          // 取消前一条高亮
          this.myChart.dispatchAction({
            type: 'downplay',
            seriesIndex: 0,
            dataIndex: this.dataIndex,
          })
          this.dataIndex = 0
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    onClick(event, myChart) {
      this.$emit('click', event, myChart)
    },
    onHighlight(obj) {
      this.selected = Object.assign(obj.data, { color: obj.color })
      this.percent =
        this.selected?.percent ||
        `${this.sum > 0 && Math.round((this.selected.value / this.sum) * 100)}%`
      if (this.dataIndex !== obj.dataIndex) {
        // 取消前一条高亮
        this.myChart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: this.dataIndex,
        })
        this.dataIndex = obj.dataIndex
      }
    },
    onFinished(myChart) {
      this.myChart = myChart

      // 默认第一条数据高亮
      myChart.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: this.dataIndex,
      })
    },
    resetColor() {
      this.rich.name.backgroundColor = ''
      this.rich.value.backgroundColor = ''
      this.rich.name.color = this.textColor
      this.rich.value.color = this.textColor
      this.itemStyle.borderColor = this.backgroundColor
      Object.assign(this.label, { rich: this.rich })
      Object.assign(this.emphasis, { rich: this.rich })
      Object.assign(
        this.chartOption.series[0],
        { label: this.label },
        { itemStyle: this.itemStyle },
        { emphasis: this.emphasis },
      )
    },
  },
}
</script>
