const validation = {
  required: '{0}是必填的',
  defaultName: '此字段',
  wrongFormat: '{0}格式错误',
  email: '邮箱地址格式无效',
  minLen: '{name}不能少于{minLen}位数',
  matchPassword: '新密码和确认密码不一致',
  maxLen: '最大长度为{0}',
  url: '非法url',
  intRange: '有效值范围：1-100内的正整数',
  underValidate: '只能是小写字母和数字以及下划线',
  intRange2: '有效值范围：{range}的整数',
  select: '{0}是必选的',
  int: '{name}必须是整数',
  valueRange: '有效值范围：{0}~{1}',
  maxValue: '最大数值为{0}',
  minValue: '最小数值为{0}',
  chineseValidate: '{0}不支持输入中文',
  minSelect: '至少选择一项',
}

export default validation
