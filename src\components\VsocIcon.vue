<template>
  <div v-if="icon" class="d-flex align-center">
    <svg
      v-if="iconType === 'icon'"
      class="icon"
      :width="measurement"
      :height="measurement"
    >
      <use :xlink:href="'#' + icon"></use>
    </svg>
    <v-icon
      v-if="iconType === 'mdi'"
      :size="measurement"
      v-bind="$attrs"
      v-on="$listeners"
      >{{ icon }}</v-icon
    >
    <!-- font-class 引用 只能单色 -->
    <i
      v-if="iconType === 'fill'"
      :style="`font-size:${measurement}`"
      class="iconfont"
      :class="icon"
    ></i>
  </div>
</template>

<script>
export default {
  name: 'VsocIcon',
  props: {
    size: {
      type: [String, Number],
      default: 'large',
    },
    icon: {
      type: String,
    },
    type: {
      type: String,
    },
  },
  computed: {
    fontSize() {
      const fontSize = getComputedStyle(window.document.documentElement)[
        'font-size'
      ].replace('px', '')
      return fontSize
    },
    measurement() {
      let temp = ''
      switch (this.size) {
        case 'small':
          temp = '1rem' // 12
          break
        case 'middle':
          temp = '1.16667rem' // 14
          break
        case 'large':
          temp = '1.33333rem' // 16
          break
        case 'x-large':
          temp = '1.5rem' // 18
          break
        case 'xx-large':
          temp = '1.75rem' // 21
          break
        default:
          temp = this.size
          break
      }
      return temp
    },
    iconType() {
      if (this.type && this.type === 'fill') {
        return this.type
      }
      if (this.icon.includes('mdi')) {
        return 'mdi'
      } else {
        return 'icon'
      }
    },
  },
}
</script>
<style scoped lang="scss">
.icon {
  vertical-align: -0.15em;
  // fill: currentColor;
  overflow: hidden;
}
</style>
