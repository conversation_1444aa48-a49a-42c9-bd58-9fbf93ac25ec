const ticketStatistics = {
  statusHeaders: {
    priority: 'Priority',
    opening: 'Open',
    openingExpired: 'Open(breached)',
    handling: 'Handling',
    handlingExpired: 'Handling(breached)',
    resolved: 'Resolved',
    closed: 'Closed',
    cancelled: 'Cancelled',
    total: 'Total',
  },

  peopleHeaders: {
    assigned: 'Assignee',
    received: 'Total Received',
    resolved: 'Total Resovled',
    handling: 'Handling(not overdue)',
    handlingExpired: 'Handling(breached)',
  },
  closeAutoRefresh: 'Auto-refresh is turned off!',
  setRefresh: 'Set refresh frequency',
  addByDay: 'New Tickets',
  backlogByDay: 'Backlog Tickets',
  ticketType: 'Ticket Type',
  title1: 'Recently Added',
  title2: 'Ticket Status By Priority',
  title3: 'Ticket Status By Assignee',
  title4: 'Trend Of New Tickets',
  title5: 'Trend Of Open Tickets',
  recentlyAdd: 'Recently added',
  totalTicket: 'Total Tickets',
}

export default ticketStatistics
