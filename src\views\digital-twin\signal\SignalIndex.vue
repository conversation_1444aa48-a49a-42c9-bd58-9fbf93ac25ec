<!-- 资产模型 -->
<template>
  <div>
    <bread-crumb> </bread-crumb>
    <v-card tile class="main-content">
      <v-card-text class="pa-0">
        <div class="d-flex justify-space-between align-center">
          <div class="d-flex justify-end align-center"></div>
          <div class="d-flex align-end">
            <table-search
              :searchList="searchList"
              :searchQuery="query"
              @search="$_search"
            ></table-search>
          </div>
        </div>

        <v-data-table
          fixed-header
          show-expand
          :items-per-page="query.pageSize"
          item-key="id"
          :height="tableHeight"
          hide-default-footer
          :headers="headers"
          :items="tableData"
          class="thead-light mt-3"
          :loading="tableLoading"
          @click:row="$_clickRow"
        >
          <template v-slot:item.data-table-expand="{ item, isExpanded }">
            <v-icon v-if="isExpanded && item.showExpand" size="1.75rem">
              mdi-chevron-up
            </v-icon>
            <v-icon v-else-if="!isExpanded && item.showExpand" size="1.75rem">
              mdi-chevron-down
            </v-icon>
          </template>
          <template v-slot:header.ttl="{ header }">
            <span>{{ header.text }}</span>
            <v-tooltip bottom close-delay="6" z-index="1">
              <template v-slot:activator="{ on }">
                <v-icon size="1rem" v-on="on"> mdi-information </v-icon>
              </template>
              <div>
                <p>{{ $tc('signal.ttl.maximum', 1) }}</p>
                <p></p>
                <p>{{ $t('signal.ttl.value0') }}</p>
                <p>{{ $t('signal.ttl.value1') }}</p>
              </div>
            </v-tooltip>
          </template>
          <template v-slot:item.name="{ item }">
            <span v-show-tips>{{ item.name }}</span>
          </template>
          <template v-slot:item.assetType="{ item }">
            <!-- <span v-show-tips>{{
              (assetTypeEnum[item.assetType] &&
                assetTypeEnum[item.assetType].text) ||
              ''
            }}</span> -->
            <div
              v-if="assetTypeEnum[Number(item.assetType)]"
              class="d-flex align-center"
            >
              <!-- <v-icon size="1.25rem" left>
                {{ assetTypeEnum[Number(item.assetType)].icon }}
              </v-icon> -->
              <vsoc-icon
                type="fill"
                :icon="assetTypeEnum[Number(item.assetType)].icon"
                class="mr-2 primary--text"
              ></vsoc-icon>
              <span>{{ assetTypeEnum[Number(item.assetType)].text }}</span>
            </div>
            <span v-else>N/A</span>
          </template>
          <template v-slot:item.intervalSize="{ item }">
            {{ item.intervalSize | formatSeconds }}
          </template>
          <template v-slot:item.ttl="{ item }">
            <!-- <div v-if="item.signalType == 0">
              <span v-if="item.ttlType === '2'" class="text-lowercase">{{
                item.ttl | formatSeconds
              }}</span>
              <span v-else>{{ item.ttlTypeName }}</span>
            </div>
            <div v-else>N/A</div> -->

            <div>
              <span class="text-lowercase">{{ item.ttl | formatSeconds }}</span>
            </div>
          </template>
          <template v-slot:item.description="{ item }">
            <div v-if="item.description" v-show-tips style="width: 260px">
              {{ item.description }}
            </div>
            <span v-else>N/A</span>
          </template>
          <template v-slot:item.isEncrypt="{ item }">
            <div
              class="d-flex align-center"
              v-if="item.signalType == 0 && encryptEnum[item.isEncrypt]"
            >
              <v-icon dense>
                {{ encryptEnum[item.isEncrypt].icon }}
                <!-- {{ item.isEncrypt === '1' ? 'mdi-check' : 'mdi-window-close' }} -->
              </v-icon>
            </div>
          </template>
          <template v-slot:item.signalType="{ item }">
            <span v-show-tips>{{
              signalTypeMap[item.signalType]
                ? signalTypeMap[item.signalType].text
                : ''
            }}</span>
          </template>
          <template v-slot:item.signalValueType="{ item }">
            <!-- 行为事件 -->
            <span v-if="item.signalType === '1'" v-show-tips>{{
              signalValueTypeMap[8]
            }}</span>
            <!-- 信号状态 -->
            <span v-else v-show-tips>{{
              signalValueTypeMap[item.signalValueType] || ''
            }}</span>
          </template>
          <template v-slot:item.updateDate="{ item }">
            <span class="text-no-wrap">{{ item.updateDate | toDate }}</span>
          </template>
          <template v-slot:item.active="{ item }">
            <div class="ml-n1" v-if="activeEnum[item.active]">
              <v-icon size="1.5rem" :color="activeEnum[item.active].color">
                {{ activeEnum[item.active].icon }}
              </v-icon>
              {{ activeEnum[item.active].text }}
            </div>
          </template>
          <template v-slot:item.actions="{ item }">
            <v-btn
              v-has:signal-edit.lite="item"
              v-show-tips="$generateBtnTitle('signal-edit')"
              icon
              @click.stop="edit(item)"
            >
              <vsoc-icon
                type="fill"
                icon="icon-bianji"
                class="action-btn"
                size="x-large"
              ></vsoc-icon>
            </v-btn>
          </template>

          <template v-slot:expanded-item="{ headers, item }">
            <td
              v-if="item.showExpand"
              :colspan="headers.length + 1"
              class="py-6"
            >
              <div class="text-sm pl-6 d-flex">
                <div class="w-20">
                  <div class="pb-2 text--primary font-weight-bold">ID</div>

                  <div @click.stop class="w-100 d-flex align-center">
                    <div class="text-overflow-hide">
                      {{ item.id }}
                    </div>
                    <v-btn
                      v-copy="item.id"
                      v-show-tips="$t('action.copy')"
                      icon
                    >
                      <!-- <v-icon size="1rem"> mdi-content-copy </v-icon> -->
                      <vsoc-icon
                        size="1.2rem"
                        type="fill"
                        icon="icon-fuzhi"
                      ></vsoc-icon>
                    </v-btn>
                  </div>
                </div>
                <template v-if="['2', '3'].includes(item.signalValueType)">
                  <!-- <div class="w-10">
                  <div class="pb-2 text--primary font-weight-bold">
                    约束可选值
                  </div>
                  <div v-show-tips>
                    {{
                      item.constrainsObject && item.constrainsObject.enumValues
                        ? item.constrainsObject.enumValues.join(',')
                        : ''
                    }}
                  </div>
                </div> -->
                  <div class="w-10">
                    <div class="pb-4 text--primary font-weight-bold">
                      {{ $t('signal.max') }}
                    </div>
                    <div>
                      {{
                        item.constrainsObject ? item.constrainsObject.max : ''
                      }}
                    </div>
                  </div>
                  <!-- <div class="w-10">
                  <div class="pb-2 text--primary font-weight-bold">
                    最大长度
                  </div>
                  <div>
                    {{
                      item.constrainsObject
                        ? item.constrainsObject.maxLength
                        : ''
                    }}
                  </div>
                </div> -->
                  <div class="w-10">
                    <div class="pb-4 text--primary font-weight-bold">
                      {{ $t('signal.min') }}
                    </div>
                    <div>
                      {{
                        item.constrainsObject ? item.constrainsObject.min : ''
                      }}
                    </div>
                  </div>
                  <div class="w-10">
                    <div class="pb-4 text--primary font-weight-bold">
                      {{ $t('signal.unit') }}
                    </div>
                    <div>
                      {{
                        item.constrainsObject
                          ? item.constrainsObject.symbol
                          : ''
                      }}
                    </div>
                  </div>
                </template>
                <template
                  v-if="
                    item.signalValueType === '0' &&
                    item.constrainsObject &&
                    item.constrainsObject.enumValues
                  "
                >
                  <div class="w-80">
                    <div class="pb-2 text--primary font-weight-bold">
                      {{ $t('signal.enum') }}
                    </div>
                    <v-chip-group>
                      <v-chip
                        v-for="tag in item.constrainsObject.enumValues"
                        :key="tag"
                        small
                      >
                        {{ tag }}
                      </v-chip>
                    </v-chip-group>
                  </div>
                </template>
                <template v-if="item.signalValueType === '1'">
                  <div class="w-80">
                    <div class="pb-2 text--primary font-weight-bold">
                      {{ $t('signal.enumMap') }}
                    </div>
                    <v-chip-group>
                      <v-chip small class="mr-2">
                        true:{{ item.constrainsObject.true | dataFilter }}
                      </v-chip>
                      <v-chip small>
                        false:{{ item.constrainsObject.false | dataFilter }}
                      </v-chip>
                    </v-chip-group>
                  </div>
                </template>
                <template v-if="item.signalValueType === '5'">
                  <div class="w-80">
                    <div class="pb-2 text--primary font-weight-bold">
                      {{ $t('logic.sub') }}
                    </div>
                    <v-chip-group v-if="item.subSignalInfos">
                      <v-chip
                        v-for="subSignal in item.subSignalInfos"
                        :key="subSignal.id"
                        small
                      >
                        {{ subSignal.name }}
                      </v-chip>
                    </v-chip-group>
                  </div>
                </template>
                <template
                  v-if="item.signalValueType === '6' && item.structureListInfo"
                >
                  <div class="w-80">
                    <div class="pb-2 text--primary font-weight-bold">
                      {{ $t('logic.sub') }}
                    </div>

                    <v-chip small>
                      {{
                        item.structureListInfo && item.structureListInfo.name
                      }}
                    </v-chip>
                  </div>
                </template>
              </div>
            </td>
          </template>

          <template v-slot:item.dataType="{ item }">
            <div
              v-if="signalDataTypeEnum[item.dataType] && item.signalType == 0"
            >
              {{ signalDataTypeEnum[item.dataType].text }}
            </div>
            <!-- <div v-else>N/A</div> -->
          </template>
          <template v-slot:item.createUser="{ item }">
            <vsoc-icon
              v-if="
                $store.state.global.userIdByAllList.includes(item.createUser)
              "
              class="primary--text"
              type="fill"
              icon="icon-yongyouzhe"
            ></vsoc-icon>
            <span v-else>{{ item.createUser }}</span>
          </template>
        </v-data-table>

        <!-- 分页器 -->
        <vsoc-pagination
          :page.sync="query.pageNum"
          :size.sync="query.pageSize"
          :total="tableDataTotal"
          @change-size="$_search"
          @change-page="getTableData"
        >
        </vsoc-pagination>
      </v-card-text>
    </v-card>
    <!-- <signals-drawer
      ref="signalsDrawer"
      v-model="showAdvanceSearch"
      @do-query="doQuery"
    ></signals-drawer> -->
  </div>
</template>

<script>
import { deleteDigitalTwin, queryPage, updateDigitalWin } from '@/api/signal'
import VsocPagination from '@/components/VsocPagination.vue'
import breadCrumb from '@/components/bread-crumb/index'
import { PAGESIZE } from '@/util/constant'
import {
  clearFilterItem,
  deepClone,
  handleFilterItem,
  handleQueryParams,
  setRemainingHeight,
} from '@/util/utils'
import { cloneDeep, filter } from 'lodash'

// import { signalValueTypeList } from '@/components/logic-engine/lib/tools'

import TableSearch from '@/components/TableSearch/index.vue'
// import SignalsDrawer from './SignalDrawer.vue'

export default {
  name: 'SignalIndex',
  components: {
    VsocPagination,
    breadCrumb,
    // SignalsDrawer,
    TableSearch,
  },
  data() {
    return {
      // 信号值类型可选
      signalValueTypeList: [
        {
          text: '字符型(String)',
          oldText: 'Text',
          value: 'STRING',
          key: '0',
        },
        {
          text: '布尔型(Boolean)',
          oldText: 'Boolean',
          value: 'BOOLEAN',
          key: '1',
        },
        {
          text: '小数型(Double)',
          oldText: 'Numeric',
          value: 'DOUBLE',
          key: '2',
        },
        {
          text: '整数型(Long)',
          oldText: 'Whole number',
          value: 'LONG',
          key: '3',
        },
        {
          text: '时间戳(Timestamp)',
          oldText: 'Timestamp',
          value: 'TIMESTAMP',
          key: '4',
        },
        {
          text: '字典型(Dictionary)',
          oldText: 'Structured',
          value: 'DICTIONARY',
          key: '5',
        },
        {
          text: '对象数组(Object array)',
          oldText: 'Structured list',
          value: 'OBJECT_ARRAY',
          key: '6',
        },
        {
          text: '数组型(Array)',
          oldText: 'Array',
          value: 'ARRAY',
          key: '7',
        },
        {
          text: '柱状图(Histogram)',
          oldText: 'Histogram',
          value: 'Histogram',
          key: '8',
        },
      ],
      searchOption: {
        searchItem: [
          { text: '名称', value: 'name' },
          { text: 'id', value: 'id' },
        ],
        keyword: '',
        currentSearch: '名称',
      },
      editActive: '',
      searchKey: '',
      showAdvanceSearch: false,

      // 查询条件列表
      // 查询条件下拉选择
      query: {
        // id: '',
        // name: '',
        queryField: '',
        dataType: '',
        signalValueTypes: [], // 值类型
        isEncrypt: '', // 是否加密
        signalType: '', // 信号类型
        active: '', // 活跃状态
        assetType: '',
        // assetType: ['Vehicle'],
        // signalValueType: [],
        // signalType: [],
        // active: [],
        // sort: 'updateDate,description',
        pageNum: 1,
        pageSize: PAGESIZE,
      },

      tableData: [],
      filterList: [],
      tableHeight: '34.5rem',
      tableLoading: false,
      tableDataTotal: 0,
      valid: false,
      signalValueTypeMap: {},
      // signalValueTypeList,
      showAddEnumValues: false,
      enumVal: '',
    }
  },
  computed: {
    encryptionEnum() {
      return this.$store.state.enums?.configs?.['Encryption_Mode_Flag']?.[
        'Encryption_Mode_Flag'
      ].propertyValue
    },
    searchList() {
      return [
        {
          type: 'input',
          value: 'queryField',
          text: `Id | ${this.$t('signal.headers.name')}`,
        },
      ]
    },
    signalDataTypeEnum() {
      return this.$store.state.enums.enums.SignalDataType
    },
    headers() {
      let headers = [
        {
          text: '',
          value: 'data-table-expand',
          width: '50px',
        },

        {
          text: this.$t('signal.headers.name'),
          value: 'name',
          width: '200px',
        },

        {
          text: this.$t('signal.headers.desc'),
          value: 'description',
          width: '350px',
        },
        {
          text: this.$t('signal.headers.valueType'),
          value: 'signalValueType',
          width: '140px',
        },

        {
          text: this.$t('global.status'),
          value: 'active',
          width: '120px',
        },
        {
          text: this.$t('signal.headers.lastUpdate'),
          value: 'updateDate',
          width: '160px',
        },

        {
          text: '',
          value: 'actions',
          width: '150px',
          sortable: false,
        },
      ]
      return this.encryptionEnum === 'true'
        ? headers
        : headers.filter(v => v.value !== 'isEncrypt')
    },
    encryptEnum() {
      return this.$store.state.enums.enums.Encrypt
    },
    activeEnum() {
      return this.$store.state.enums.enums.ActiveStatus
    },
    signalTypeMap() {
      return this.$store.state.enums.enums.SignalType
    },
    assetTypeEnum() {
      return this.$store.state.enums.enums.AssetType
    },
  },
  watch: {
    // 监听筛选框内容，重新设置表格高度
    filterList() {
      this.$_setTableHeight()
    },
  },
  created() {
    this.$_setTableHeight()
    this.handleMap()

    // const query = handleQueryParams({ type: 'get', key: 'signals' })

    // if (query) {
    //   this.query = query
    // }
    // this.$_search()
    this.init()
    this.$bus.$on('resize', this.$_setTableHeight)
  },
  beforeDestroy() {
    this.$bus.$off('resize', this.$_setTableHeight)
  },
  methods: {
    init() {
      const query = handleQueryParams({ type: 'get', key: 'signals' })
      if (query) {
        this.query = query
      }
      this.$_search()
    },
    onClear() {
      this.query.queryField = ''
      this.$_search()
    },
    async onActive(item) {
      try {
        const params = deepClone(item)
        params.active = params.active === '0' ? '1' : '0'
        const { code } = await updateDigitalWin(params)
        this.getTableData()
      } catch (err) {
        console.log('修改状态', err)
      }
    },

    async getTableData() {
      try {
        this.tableLoading = true
        this.tableData = []

        // const params = {
        //   pageNum: this.query.pageNum,
        //   pageSize: this.query.pageSize,
        // }
        handleQueryParams({ type: 'set', query: this.query, key: 'signals' })
        const params = deepClone(this.query)
        params.ids = this.query.id ? new Array(this.query.id) : []
        delete params.id

        // 值类型的数据处理
        if (this.query.signalValueTypes.length > 0) {
          const arr = filter(this.signalValueTypeList, v =>
            this.query.signalValueTypes.includes(v.text),
          )
          params.signalValueTypes = arr.map(v => v.key)
        }
        const res = await queryPage(params)
        this.tableData = res.data.records.map(item => {
          item.showExpand = true

          // // 行为事件
          // if (item.signalType === '1') {
          //   item.showExpand = false
          // } else if (!item.constrainsObject) {
          //   item.showExpand = false
          // } else if (
          //   !item.constrainsObject.max &&
          //   !item.constrainsObject.maxLength &&
          //   !item.constrainsObject.min &&
          //   !item.constrainsObject.unit
          // ) {
          //   if (
          //     !item.constrainsObject.enumValues ||
          //     item.constrainsObject.enumValues.length === 0
          //   ) {
          //     item.showExpand = false
          //   }
          // }

          //对信号状态 显示ID在展开项中
          if (item.signalType === '1' || ['7'].includes(item.signalValueType)) {
            item.showExpand = false
          }

          return item
        })
        this.tableDataTotal = res.data.total
      } catch (err) {
        console.error(`获取数字孪生信号管理：${err}`)
      }

      this.tableLoading = false
    },

    $_search() {
      this.query.pageNum = 1

      // const cur = this.searchOption.searchItem.find(
      //   v => v.text === this.searchOption.currentSearch,
      // )
      // if (cur.value) {
      //   this.query[cur.value] = this.searchOption.keyword
      // }

      this.getTableData()
      this.$_appendFilterListItem()
    },

    doQuery(advanceQuery) {
      this.query = cloneDeep(advanceQuery)

      this.$_search()
    },

    // 设置表格高度
    $_setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = setRemainingHeight(
          this.$store.state.global.subMarginFn,
        )
      })
    },

    handleMap() {
      this.signalValueTypeList.forEach(item => {
        // this.signalValueTypeMap[item.value] = item.text
        this.signalValueTypeMap[item.key] = item.text
      })
    },

    // 单击表格行
    $_clickRow(item, slot) {
      slot.expand(!slot.isExpanded)
    },

    add() {
      this.$router.push('/digit-twin/signal/edit')
    },

    edit(item) {
      this.$router.push(`/digit-twin/signal/edit?id=${item.id}`)
    },

    // 删除信号
    deleteSignal(item) {
      this.$swal({
        title: this.$t('signal.swal.title'),
        text: this.$t('signal.swal.hint', { name: item.name }),
        icon: 'warning',
        reverseButtons: true,
        showCancelButton: true,
        confirmButtonText: this.$t('action.confirm'),
        cancelButtonText: this.$t('action.cancel'),
        customClass: {
          confirmButton: 'sweet-btn-primary',
        },
      }).then(async result => {
        if (result.isConfirmed) {
          // this.$swal.fire('更新成功', '', 'success');
          try {
            const res = await deleteDigitalTwin({
              id: item.id,
            })
            if (res.code === 200) {
              this.$notify.info(
                'success',
                this.$t('global.hint.del', [this.$t('signal.currentTitle')]),
              )
            }
            this.$_search()
          } catch (e) {
            console.error(`删除信号错误：${e}`)
          }
        }
      })
    },

    showDoQuery() {
      this.showAdvanceSearch = true
      const data = deepClone(this.query)
      this.$refs.signalsDrawer.setModel(data)
    },

    // 添加查询条件
    $_appendFilterListItem() {
      const searchKeyList = [
        {
          key: 'id',
          type: 'String',
          label: 'id',
        },
        {
          key: 'name',
          type: 'String',
          label: 'signal.headers.name',
        },
        {
          key: 'assetType',
          type: 'String',
          mapKey: 'assetTypeEnum',
          label: 'global.assetType',
        },
        {
          key: 'dataType',
          type: 'String',
          mapKey: 'signalDataTypeEnum',
          label: 'signal.headers.dataType',
        },
        {
          key: 'signalValueTypes',
          type: 'Array',
          mapKey: 'signalValueTypeMap',
          label: 'signal.headers.valueType',
        },
        {
          key: 'signalType',
          type: 'String',
          mapKey: 'signalTypeMap',
          label: 'signal.headers.modelType',
        },
        {
          key: 'isEncrypt',
          type: 'String',
          mapKey: 'encryptEnum',
          label: 'global.encryption',
        },
        {
          key: 'active',
          type: 'String',
          mapKey: 'activeEnum',
          label: 'global.status',
        },
      ]
      handleFilterItem.call(this, searchKeyList)

      // 是否加密
      // const encrypt = this.filterList.find(v => v.key === 'isEncrypt')
      // if (encrypt) {
      //   encrypt.text = encrypt.value === '1' ? '加密' : '不加密'
      // }
      this.strToEnum('isEncrypt', this.query.isEncrypt, this.encryptEnum)

      // 模型类型
      // const signalType = this.filterList.find(v => v.key === 'signalType')
      // if (signalType) {
      //   signalType.text = signalType.value === '1' ? '行为事件' : '信号状态'
      // }
      this.strToEnum('signalType', this.query.signalType, this.signalTypeMap)

      // 状态
      // const active = this.filterList.find(v => v.key === 'active')
      // if (active) {
      //   active.text = active.value === '1' ? '有效' : '无效'
      // }
      this.strToEnum('active', this.query.active, this.activeEnum)

      // 资产类型
      // const assetType = this.filterList.find(v => v.key === 'assetType')
      // if (assetType) {
      //   assetType.text = assetType.text === '0' ? 'Vehicle' : 'App'
      // }
      this.strToEnum('assetType', this.query.assetType, this.assetTypeEnum)

      this.strToEnum('dataType', this.query.dataType, this.signalDataTypeEnum)
    },

    strToEnum(key, value, enums) {
      const index = this.filterList.findIndex(v => v.key == key)
      if (index !== -1) {
        this.filterList[index].text = enums[value]?.text
      }
    },
    onReset() {
      this.searchOption.keyword = ''
      Object.assign(this.$data.query, this.$options.data.call(this).query)
      this.$_search()
    },

    $_clearFilter(item) {
      if (item.label === this.searchOption.currentSearch) {
        this.searchOption.keyword = ''
      }

      const bool = clearFilterItem.call(this, item)
      if (!bool) {
        this.getTableData()
      }
    },
  },
}
</script>
