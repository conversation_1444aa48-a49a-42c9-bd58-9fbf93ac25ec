.el-picker-panel .popper__arrow {
  display: none;
}
.elDatePicker .el-picker-panel__footer .el-button--text {
  display: none;
}
.el-picker-panel {
  z-index: 999 !important;
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.14),
    0 7px 10px -5px rgba(64, 64, 64, 0.4);
  border-radius: 0.75rem !important;
  border: none;
  color: $body-color;
  margin-top: 0.75rem !important;
  // transform: translateX(-16px);

  .el-picker-panel__content {
    width: 100%;
    padding: 0 1rem 1rem 1rem;
    margin: 0;
    margin-top: 1rem;
  }

  .el-date-picker__header {
    margin: 1rem 1rem 0 1rem;
  }

  .el-picker-panel__sidebar {
    border-radius: 0.75rem 0 0 0.75rem !important;
  }
}
.el-picker-panel.elDatePicker1 {
  z-index: 99999 !important;
}
.el-date-range-picker {
  .el-date-range-picker__content {
    width: 50%;

    .el-date-table {
      margin-top: 1rem;
    }
  }

  // 隐藏清空按钮
  .el-picker-panel__footer .el-button:first-child {
    display: none;
  }
}

.el-date-range-picker__content.is-left {
  border-color: rgba(0, 0, 0, 0.05);
}

.el-date-table {
  th {
    border: none;
    color: $body-color;
    font-weight: 600;
    padding: 4px 0;
  }

  td {
    width: 2rem;
    height: 2rem;
    color: $headings-color;

    div {
      padding: 0;
      span {
        height: 1.95rem;
        width: 1.95rem;
        line-height: 1.95rem;
      }
    }
  }
}

.el-date-table td.available:hover {
  color: $headings-color;
  span {
    background-color: rgba($color: #000000, $alpha: 0.06);
  }
}

.el-date-table td.in-range div,
.el-date-table td.in-range div:hover,
.el-date-table.is-week-mode .el-date-table__row.current div,
.el-date-table.is-week-mode .el-date-table__row:hover div {
  background-color: rgba($color: $primary, $alpha: 0.08);
}

.el-date-table td.end-date span,
.el-date-table td.start-date span,
.el-date-table td.current:not(.disabled) span {
  background-color: $primary !important;
}

.el-date-table td.today span {
  color: $primary;
  border: 1px solid $primary;
  &:hover {
    background-color: transparent;
  }
}

.el-date-range-picker__header div,
.el-date-range-picker__header .el-picker-panel__icon-btn,
.el-date-picker__header .el-date-picker__header-label,
.el-date-picker__header .el-picker-panel__icon-btn {
  color: var(--v-color-base);
  font-weight: 600;
}

.el-date-table td.next-month,
.el-date-table td.prev-month {
  color: rgba($color: $body-color, $alpha: 0.6);
}

.el-date-picker__time-header,
.el-date-range-picker__time-header {
  padding: 1rem;
  padding-bottom: 0;
  border: none;
  .el-input__inner {
    border-color: rgba(0, 0, 0, 0.23);
    border-radius: 4px;

    &:hover {
      border-color: rgba(0, 0, 0, 0.43);
    }

    &:focus {
      border-color: $primary;
    }
  }

  .el-icon-arrow-right {
    opacity: 0;
  }
}

.el-picker-panel__footer {
  border: none;
  padding: 0 1rem 1rem 1rem;
  border-radius: 0.5rem;
  font-size: 1rem;

  .el-button--text {
    color: $primary;
    padding: 0.4375rem 0.9375rem;
    //padding: 0.75rem 1.5rem;
    transition: 0.2s;
    border-radius: 0.5rem;
    //border-radius: 1rem;
    &:hover {
      background-color: rgba($color: $primary, $alpha: 0.08);
    }
  }

  .el-button.is-plain {
    background-image: linear-gradient(
      195deg,
      $primary-gradient,
      $primary-gradient-state
    ) !important;
    color: #fff;

    border-radius: 0.5rem;
    box-shadow: 0 0.1875rem 0.0625rem -0.125rem rgba(0, 0, 0, 0.2),
      0 0.125rem 0.125rem 0 rgba(0, 0, 0, 0.14),
      0 0.0625rem 0.3125rem 0 rgba(0, 0, 0, 0.12) !important;
    border: none;
    transition: 0.2s;
    &:hover {
      transform: scale(1.02);
    }

    &.is-disabled {
      opacity: 0.6;
      &:hover {
        transform: scale(1);
        color: #fff;
      }
    }
    // .span {
    //   color: #fff !important;
    // }
  }

  .el-button + .el-button {
    margin-left: 8px;
    padding: 0.3rem 1rem;
    font-weight: 600;
    width: 5rem;
  }
}

.el-time-panel__footer {
  border-top: none;
  .el-time-panel__btn.cancel {
    color: $body-color;
    transition: 0.2s;
    &:hover {
      transform: scale(1.1);
    }
  }

  .el-time-panel__btn.confirm {
    color: $primary;
    transition: 0.2s;
    &:hover {
      transform: scale(1.05);
    }
  }
}

.el-time-spinner__item.active:not(.disabled) {
  color: $headings-color;
}

.el-picker-panel__shortcut {
  line-height: 1;
  margin-bottom: 1rem;
  &:hover {
    color: $primary;
  }
}

.el-picker-panel__sidebar {
  padding-top: 1rem;
  padding-left: 0.25rem;
  z-index: 1;
  text-align: left;
}
