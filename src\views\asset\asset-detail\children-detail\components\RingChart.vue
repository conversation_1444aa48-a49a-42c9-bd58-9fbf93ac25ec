<template>
  <v-card class="h-full d-flex flex-column">
    <v-card-title>{{ title }}</v-card-title>
    <v-card-text class="flex-1 pb-0" v-empty-chart="isEmpty">
      <v-row class="h-100">
        <v-col cols="6" class="h-100 px-0">
          <vsoc-chart
            class="ml-2"
            :echartId="echartId"
            :option="chartOption"
            @highlight="onHighlight"
          ></vsoc-chart>
        </v-col>
        <v-col
          cols="6"
          class="d-flex flex-column justify-center h-100 pl-4 mt-n4 pb-0 pl-0"
        >
          <v-simple-table class="scroll-bar-card" height="100%">
            <template v-slot:default>
              <tbody>
                <tr v-for="item in list" :key="item.dessert">
                  <td class="d-flex align-center px-0">
                    <v-icon size="16" :color="item.color" class="iconfont mr-2">
                      {{ item.icon ? item.icon : 'icon-gaojingjibiebiaozhi' }}
                    </v-icon>

                    <span
                      class="w-60 text-overflow-hide inline-block align-middle text--primary"
                      v-show-tips="item.name"
                    >
                      {{ item.name }}
                    </span>
                  </td>
                  <td class="text-center text--primary px-0">
                    {{ item.value | numberToFormat }}
                  </td>
                  <td class="text-center text--secondary px-0">
                    {{ item.percent }}
                  </td>
                </tr>
              </tbody>
            </template>
          </v-simple-table>
        </v-col>
      </v-row>
    </v-card-text>
  </v-card>
</template>

<script>
import VsocChart from '@/components/VsocChart.vue'
import { getRoundSize, numberToFormat } from '@/util/filters'
import themeConfig from '@themeConfig'
import { sumBy } from 'lodash'
export default {
  name: 'RegularDistribution',
  props: {
    color: {
      type: Array,
      default: () => [
        '#313CA6',
        '#0082D6',
        '#55D1FD',
        '#3D4A66',
        '#8F9AB2',
        '#B4BBCC',
      ],
    },
    list: {
      type: Array,
      default: () => [],
    },
    echartId: {
      type: String,
      default: 'chart',
    },
    title: {
      type: String,
    },
    subtext: {
      type: String,
      default: '总数',
    },
    icon: {
      type: String,
    },
  },
  components: {
    VsocChart,
  },
  computed: {
    isEmpty() {
      return this.list.filter(v => v.value != 0 && v.value != null).length === 0
    },
    chartOption() {
      let list = this.list.map(v => {
        return {
          ...v,
          itemStyle: { color: v.color },
        }
      })
      const sum = sumBy(list, v => v.value)
      return {
        // color: this.color,
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c}\t({d}%)',
          backgroundColor: '',
          textStyle: { color: '#fff' },
        },
        legend: { top: '5%', left: 'center', show: false },
        series: [
          {
            type: 'pie',
            radius: ['65%', '87%'],
            avoidLabelOverlap: true,
            percentPrecision: 1,
            minAngle: 20,
            stillShowZeroSum: true,
            label: {
              show: false,
            },
            itemStyle: {
              borderWidth: 10,
              borderRadius: 16,
              borderColor: this.$vuetify.theme.isDark
                ? themeConfig.themes.dark.backgroundColor
                : themeConfig.themes.light.backgroundColor,
            },
            emphasis: { label: { show: false } },
            labelLine: { show: false },
            data: list,
          },
        ],
        title: {
          subtext: `{text|${this.subtext}}`,
          text: `{fs32|${numberToFormat(sum, 'Object').num}}\t{fs14|${
            numberToFormat(sum, 'Object').unit
          }}`,
          top: '35%',
          left: '48.2%',
          textAlign: 'center',
          padding: [5, 5],
          textStyle: {
            rich: {
              fs32: {
                fontSize: '2.666666rem',
                fontWeight: 600,
                color: 'inherit',
              },
              fs14: {
                fontSize: 14,
                fontWeight: 600,
                color: 'inherit',
                verticalAlign: 'bottom',
                padding: [6, getRoundSize(2)],
              },
            },
          },

          subtextStyle: {
            rich: {
              text: {
                fontSize: '1rem',
                color: '#7A8599',
                verticalAlign: 'bottom',
              },
            },
          },
        },
      }
    },
  },
  mounted() {},
  methods: {
    onHighlight(event, myChart) {
      this.chartOption.tooltip.backgroundColor = event.color
      myChart.setOption(this.chartOption)
    },
  },
}
</script>
<style scoped lang="scss">
::v-deep
  .v-data-table
  > .v-data-table__wrapper
  > table
  > tbody
  > tr:not(:last-child)
  > td:not(.v-data-table__mobile-row) {
  border: none !important;
}
</style>
