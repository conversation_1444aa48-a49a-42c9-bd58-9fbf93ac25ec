<template>
  <v-overlay v-model="isShow" z-index="999" opacity="0.15" class="zoom-box">
    <div class="load-container load1">
      <div class="loader">Loading...</div>
    </div>
    <!-- <v-card
      color="primary"
      :style="{ background: isDark ? '#06309B' : '#038CD6' }"
      width="220"
    >
      <v-card-text class="pt-3 d-flex align-center">
        <v-img
          :src="require(`@/assets/images/loding-logo.png`)"
          height="30px"
          alt="logo"
          contain
          eager
          class="app-logo over-img-box"
        ></v-img>
        <v-progress-linear indeterminate color="#fff" query></v-progress-linear>
      </v-card-text>
    </v-card> -->
  </v-overlay>
</template>
<script>
export default {
  // props: {
  //   color: {
  //     type: String,
  //     default: '#312d4b',
  //   },
  //   scale: {
  //     type: Number,
  //     default: 1,
  //   },
  // },
  data() {
    return {
      isShow: false,
      isDark: false,
    }
  },
  // watch: {
  //   isShow() {
  //     let classNames = document.getElementById('app').className
  //     this.isDark = classNames.indexOf('theme--light') === -1 ? false : true
  //   },
  // },
  // computed: {
  //   cssVars() {
  //     return {
  //       '--color': this.color,
  //       '--scale': this.scale,
  //     }
  //   },
  // },
}
</script>
<style lang="scss" scoped>
.zoom-box {
  position: absolute !important;
  .load1 .loader,
  .load1 .loader:before,
  .load1 .loader:after {
    background: $primary;
    -webkit-animation: load1 1s infinite ease-in-out;
    animation: load1 1s infinite ease-in-out;
    width: 1em;
    height: 4em;
  }
  .load1 .loader:before,
  .load1 .loader:after {
    position: absolute;
    top: 0;
    content: '';
  }
  .load1 .loader:before {
    left: -1.5em;
    -webkit-animation-delay: -0.32s;
    animation-delay: -0.32s;
  }
  .load1 .loader {
    text-indent: -9999em;
    margin: 8em auto;
    position: relative;
    font-size: 11px;
    -webkit-transform: translateZ(0);
    -ms-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-animation-delay: -0.16s;
    animation-delay: -0.16s;
  }
  .load1 .loader:after {
    left: 1.5em;
  }
  @-webkit-keyframes load1 {
    0%,
    80%,
    100% {
      box-shadow: 0 0 $primary;
      height: 4em;
    }
    40% {
      box-shadow: 0 -2em $primary;
      height: 5em;
    }
  }
  @keyframes load1 {
    0%,
    80%,
    100% {
      box-shadow: 0 0 $primary;
      height: 4em;
    }
    40% {
      box-shadow: 0 -2em $primary;
      height: 5em;
    }
  }
}
// .over-img-box {
//   margin-bottom: 14px;
// }
// .preloader {
//   position: relative;
//   width: 90px;
//   height: 90px;
//   margin: auto;
//   transform: scale(var(--scale));
// }
// .preloader > span {
//   position: absolute;
//   background-color: transparent;
//   height: 16px;
//   width: 16px;
//   border-radius: 8px;
//   animation-name: f_fadeG;
//   animation-duration: 1.2s;
//   animation-iteration-count: infinite;
//   animation-direction: normal;
// }
// .rot-1 {
//   left: 0;
//   top: 40px;
//   animation-delay: 0.45s;
// }
// .rot-2 {
//   left: 10px;
//   top: 10px;
//   animation-delay: 0.6s;
// }
// .rot-3 {
//   left: 40px;
//   top: 0;
//   animation-delay: 0.75s;
// }
// .rot-4 {
//   right: 10px;
//   top: 10px;
//   animation-delay: 0.9s;
// }
// .rot-5 {
//   right: 0;
//   top: 40px;
//   animation-delay: 1.05s;
// }
// .rot-6 {
//   right: 10px;
//   bottom: 10px;
//   animation-delay: 1.2s;
// }
// .rot-7 {
//   left: 40px;
//   bottom: 0;
//   animation-delay: 1.35s;
// }
// .rot-8 {
//   left: 10px;
//   bottom: 10px;
//   animation-delay: 1.5s;
// }
// @keyframes f_fadeG {
//   0% {
//     background-color: var(--color);
//   }
//   100% {
//     background-color: transparent;
//   }
// }
</style>
