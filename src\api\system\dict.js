import { request, vsocPath } from '../../util/request'

// 获取字典管理列表
export const getDictTypeList = function (params) {
  return request({
    url: `${vsocPath}/dict/dictTypes`,
    method: 'post',
    data: params,
  })
}

// 新增字典管理
export const addDictType = function (params) {
  return request({
    url: `${vsocPath}/dict/addDictType`,
    method: 'post',
    data: params,
  })
}

// 修改字典管理
export const editDictType = function (params) {
  return request({
    url: `${vsocPath}/dict/updateDictType`,
    method: 'post',
    data: params,
  })
}

// 删除字典管理
export const delDictType = function (params) {
  return request({
    url: `${vsocPath}/dict/deleteDict`,
    method: 'post',
    data: params,
  })
}
// 所有字典类型列表
export const dictTypeList = function (params) {
  return request({
    url: `${vsocPath}/dict/dictTypeList`,
    method: 'post',
    data: params,
  })
}

// 新增字典数据
export const addDictData = function (params) {
  return request({
    url: `${vsocPath}/dict/addDictData`,
    method: 'post',
    data: params,
  })
}

// 修改字典数据
export const updateDictData = function (params) {
  return request({
    url: `${vsocPath}/dict/updateDictData`,
    method: 'post',
    data: params,
  })
}

// 获取字典数据列表
export const getDictDataList = function (params) {
  return request({
    url: `${vsocPath}/dict/dictData`,
    method: 'post',
    data: params,
  })
}
