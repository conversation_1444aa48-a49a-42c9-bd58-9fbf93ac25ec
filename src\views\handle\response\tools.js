import store from '@/store'

export const flatTree = function (arr) {
  let newArr = []
  for (let i = 0; i < arr.length; i++) {
    if (arr[i].children) {
      newArr.push(...flatTree(arr[i].children))
    }
    newArr.push({ ...arr[i] })
  }
  return newArr
}

export const tagList = {
  0: [
    { type: '工单', value: '${ticket.id}', name: '工单编号' },
    { type: '工单', value: '${ticket.title}', name: '工单标题' },
    { type: '工单', value: '${ticket.url} ', name: '工单详情链接' },
    { type: '工单', value: '${ticket.content}', name: '工单描述' },
    { type: '工单', value: '${ticket.creator}', name: '工单创建人' },
    { type: '工单', value: '${ticket.assigner}', name: '工单处理人' },
    { type: '工单', value: '${ticket.assignGroup}', name: '工单处理组' },
    { type: '工单', value: '${ticket.status}', name: '工单状态' },
    { type: '工单', value: '${ticket.priority}', name: '工单优先级' },
    { type: '工单', value: '${ticket.type}', name: '工单分类' },
    { type: '工单', value: '${ticket.createDate}', name: '工单创建时间' },
    { type: '工单', value: '${ticket.expectDate}', name: '工单预期完成时间' },
    { type: '工单', value: '${ticket.impact}', name: '工单影响等级' },
    { type: '工单', value: '${ticket.channel}', name: '工单来源' },
  ],
  1: [
    { type: '告警', value: '${alert.id}', name: '告警编号' },
    { type: '告警', value: '${alert.title}', name: '告警标题' },
    { type: '告警', value: '${alert.url} ', name: '告警详情链接' },
    { type: '告警', value: '${alert.content}', name: '告警描述' },
    { type: '告警', value: '${alert.creator}', name: '告警创建人' },
    { type: '告警', value: '${alert.updater}', name: '告警处理人' },
    { type: '告警', value: '${alert.status}', name: '告警状态' },
    { type: '告警', value: '${alert.level}', name: '告警严重等级' },
    { type: '告警', value: '${alert.type}', name: '告警分类' },
    { type: '告警', value: '${alert.createDate}', name: '告警创建时间' },
    {
      type: '告警',
      value: '${alert.updateDate}',
      name: '告警最后更新时间',
    },
    { type: '告警', value: '${alert.channel}', name: '告警来源' },
  ],
}
// console.log(store.getters['enums/getResponseConditionField'])
// let allField = store.getters['enums/getResponseConditionField']
// export const responseConditions = {
//   0: allField.filter(
//     v => ['0', '1', '2', '3', '4', '5', '6', '7'].indexOf(v.dictId) !== -1,
//   ),
//   1: allField.filter(
//     v =>
//       [
//         '0',
//         '1',
//         '2',
//         '3',
//         '4',
//         '5',
//         '6',
//         '7',
//         '8',
//         '9',
//         '10',
//         '11',
//         '12',
//         '13',
//         '14',
//         '15',
//         '16',
//         '17',
//       ].indexOf(v.dictId) !== -1,
//   ),
//   2: allField.filter(
//     v => ['18', '19', '20', '21', '22', '23'].indexOf(v.dictId) !== -1,
//   ),
//   3: allField.filter(
//     v => ['18', '19', '20', '21', '22', '23'].indexOf(v.dictId) !== -1,
//   ),
// }

// export const operatorList = [
//   { text: '是', value: '0' },
//   { text: '不是', value: '1' },
//   { text: '包含', value: '2' },
//   { text: '不包含', value: '3' },
//   { text: '已更改', value: '4' },
//   { text: '更改为', value: '5' },
//   { text: '更改自', value: '6' },
//   { text: '未更改为', value: '7' },
//   { text: '不是更改自', value: '8' },
//   { text: '有新回复', value: '9' },
//   { text: '有新指派', value: '10' },
//   { text: '有新取消', value: '11' },
//   { text: '有新完结', value: '12' },
//   { text: '有新驳回', value: '13' },
//   { text: '有新重开', value: '14' },
//   { text: '有新更新', value: '15' },
//   { text: '有新总结', value: '16' },
//   { text: '有新关单', value: '17' },
// ]

export const operatorList = store.getters['enums/getResponseConditionOperator']
// 返回一条逻辑数据模型
export const getLogicItem = function () {
  return {
    event: '',
    operator: '',
    value: [],
    // 当前这条逻辑是否已结束
    settingOver: false,
    eventList: [],
    operatorList: [],
    valueList: [],
    // 当前逻辑进度
    progree: 1,
    multiple: false,
    error: '',
    isEdit: false,
  }
}

// 重置逻辑值
export const resetLoginItem = function (item, filterList = [], isInit) {
  if (isInit) return
  const logicItem = getLogicItem()
  for (const key in logicItem) {
    if (!filterList.length || !filterList.includes(key)) {
      item[key] = logicItem[key]
    }
  }
}

export const handleChange = {
  eventChange({ item, isInit = false }) {
    item.operator = item.isEdit ? item.operator : ''
    item.operatorList = []
    item.progree = 1
    //更新
    if (item.responseEvent === '1' || item.responseEvent === '3') {
      if (
        ['0', '1', '2', '3', '4', '5', '18', '19', '20', '21', '22'].indexOf(
          item.event,
        ) !== -1
      ) {
        item.operatorList = operatorList.filter(
          v => ['4', '5', '6', '7', '8'].indexOf(v.value) !== -1,
        )
      } else if (item.event === '7') {
        item.operatorList = operatorList.filter(
          v => ['4'].indexOf(v.value) !== -1,
        )
      } else if (item.event === '8') {
        item.operatorList = operatorList.filter(
          v => ['9'].indexOf(v.value) !== -1,
        )
      } else if (item.event === '9') {
        //取消
        item.operatorList = operatorList.filter(
          v => ['10'].indexOf(v.value) !== -1,
        )
      } else if (item.event === '10') {
        item.operatorList = operatorList.filter(
          v => ['11'].indexOf(v.value) !== -1,
        )
      } else if (item.event === '11') {
        item.operatorList = operatorList.filter(
          v => ['12'].indexOf(v.value) !== -1,
        )
      } else if (item.event === '12') {
        item.operatorList = operatorList.filter(
          v => ['13'].indexOf(v.value) !== -1,
        )
      } else if (item.event === '13') {
        item.operatorList = operatorList.filter(
          v => ['14'].indexOf(v.value) !== -1,
        )
      } else if (item.event === '14') {
        item.operatorList = operatorList.filter(
          v => ['15'].indexOf(v.value) !== -1,
        )
      } else if (item.event === '15') {
        item.operatorList = operatorList.filter(
          v => ['16'].indexOf(v.value) !== -1,
        )
      } else if (item.event === '16') {
        item.operatorList = operatorList.filter(
          v => ['17'].indexOf(v.value) !== -1,
        )
      } else if (item.event === '17') {
        item.operatorList = operatorList.filter(
          v => ['18'].indexOf(v.value) !== -1,
        )
      } else if (item.event === '6' || item.event === '23') {
        item.operatorList = operatorList.filter(
          v => ['2', '4', '5', '6', '7', '8'].indexOf(v.value) !== -1,
        )
      }
    } else {
      if (
        ['0', '1', '3', '4', '5', '6', '18', '19', '21', '22', '23'].indexOf(
          item.event,
        ) !== -1
      ) {
        item.operatorList = operatorList.filter(
          v => ['0', '1', '2', '3'].indexOf(v.value) !== -1,
        )
      } else if (['2', '20'].indexOf(item.event) !== -1) {
        item.operatorList = operatorList.filter(
          v => ['2', '3'].indexOf(v.value) !== -1,
        )
      }
    }
    setTimeout(() => {
      item.progree = 2
      resetLoginItem(
        item,
        ['event', 'progree', 'operator', 'operatorList'],
        isInit,
      )
    }, 10)
  },

  operatorChange({ item, isInit = false }) {
    item.value = item.isEdit ? item.value : ''
    item.valueList = []
    item.progree = 2
    if (item.operator === '2' || item.operator === '3') {
      item.multiple = true
    } else {
      item.multiple = false
    }

    if (
      ['4', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18'].indexOf(
        item.operator,
      ) !== -1
    ) {
      item.settingOver = true
      return
    }

    setTimeout(() => {
      item.progree = 3
      resetLoginItem(
        item,
        [
          'event',
          'progree',
          'operatorList',
          'value',
          'valueList',
          'operator',
          'multiple',
        ],
        isInit,
      )
    }, 10)
  },

  valueChange({ item, val }) {
    item.value = val
    if (item.progree === 2) {
      item.settingOver = true
    } else {
      if (item.multiple) {
        item.settingOver = item.value.length ? true : false
      } else {
        item.settingOver = item.value ? true : false
      }
    }
  },
}
